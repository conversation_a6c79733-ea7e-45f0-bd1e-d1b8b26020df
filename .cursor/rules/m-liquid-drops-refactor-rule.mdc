---
description: 
globs: 
alwaysApply: false
---
# CloudCart Liquid Drops Refactor Rule

## Description
This rule provides comprehensive guidelines for automatically updating CloudCart's PHP "Drop" classes to ensure they implement all methods defined by Shopify's Liquid object specification while preserving existing functionality. It includes mandatory progress tracking and reporting requirements.

## 0. Refactoring Progress Tracking

### 0.1 Report Document Management
**MANDATORY**: All refactoring activities must be tracked in `app/LiquidEngine/Documentation/DropsRefactoringReport.md`. This report serves as the central tracking document for the refactoring project.

### 0.2 Report Structure
The report document must maintain the following structure:

```markdown
# CloudCart Liquid Drops Refactoring Progress Report

## Project Overview
- **Start Date**: {date}
- **Current Phase**: {phase_name}
- **Overall Progress**: {percentage}%
- **Last Updated**: {timestamp}

## Refactoring Statistics
- **Total Drop Classes**: {total_count}
- **Completed**: {completed_count}
- **In Progress**: {in_progress_count}
- **Not Started**: {not_started_count}

## Phase Status

### Phase 1: Core Objects (High Priority)
| Drop Class | Status | Methods Added | Methods Updated | Completed Date | Notes |
|------------|--------|---------------|-----------------|----------------|-------|
| Product.php | ✅ Completed | 15 | 8 | 2024-01-15 | All Shopify methods implemented |
| Page.php | 🔄 In Progress | 5 | 3 | - | Missing excerpt(), tags() methods |
| Shop.php | ❌ Not Started | 0 | 0 | - | - |

### Phase 2: E-commerce Objects (Medium Priority)
| Drop Class | Status | Methods Added | Methods Updated | Completed Date | Notes |
|------------|--------|---------------|-----------------|----------------|-------|

### Phase 3: Content Objects (Lower Priority)
| Drop Class | Status | Methods Added | Methods Updated | Completed Date | Notes |
|------------|--------|---------------|-----------------|----------------|-------|

## Detailed Change Log

### {DropClassName}.php - {Date}
**Status**: {Completed/In Progress/Not Started}
**Shopify Object**: {object_name}
**File**: [DropClassName.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/DropClassName.php)
**Model**: [ModelName.php](mdc:app/Models/Category/ModelName.php)

#### Methods Updated (Logic Refactored)
- `title()`: Updated to use `name` field for Shopify compatibility
- `price()`: Updated to use `price_from` field instead of `price`
- `url()`: Enhanced URL generation logic

#### Methods Added (New Shopify Compatibility)
- `excerpt()`: Content summary implementation
- `created_at()`: Date wrapper for creation timestamp
- `updated_at()`: Date wrapper for modification timestamp
- `available()`: Computed availability based on stock/active status
- `tags()`: Tag collection implementation

#### Database Schema Verified
- ✅ All required fields exist in database
- ✅ Model relationships properly defined
- ✅ Accessors/mutators analyzed

#### Testing Results
- ✅ All existing templates work
- ✅ New methods return correct data types
- ✅ No performance regression
- ✅ Backward compatibility maintained

#### Performance Impact
- No N+1 queries introduced
- Proper eager loading implemented
- Computed values cached appropriately

#### Documentation Status
- ✅ Individual class documentation created
- ✅ Usage examples provided
- ✅ Migration notes documented

---

## Issues and Blockers

### Current Issues
1. **Issue Title**: Description of any blocking issues
   - **Impact**: High/Medium/Low
   - **Status**: Open/In Progress/Resolved
   - **Resolution**: Description of solution or workaround

### Resolved Issues
1. **Issue Title**: Description of resolved issue
   - **Resolution Date**: {date}
   - **Solution**: Description of how it was resolved

## Next Steps
1. Complete refactoring of {next_class_name}
2. Implement missing methods in {class_name}
3. Create comprehensive test suite
4. Update liquid template documentation

## Quality Metrics
- **Test Coverage**: {percentage}%
- **Documentation Coverage**: {percentage}%
- **Shopify Compatibility**: {percentage}%
- **Performance Regression**: None detected

## Lessons Learned
- Key insights gained during refactoring
- Best practices discovered
- Common patterns identified
```

### 0.3 Report Update Requirements
**CRITICAL**: Every time a Drop class is refactored, the report MUST be updated with:

1. **Status Change**: Update the class status in the phase tables
2. **Method Counts**: Record exact numbers of methods added/updated
3. **Detailed Log Entry**: Add comprehensive change log section
4. **Testing Results**: Document all testing outcomes
5. **Performance Impact**: Note any performance implications
6. **Issue Tracking**: Record any problems encountered
7. **Timestamp**: Update the "Last Updated" field

### 0.4 Report Update Process
When refactoring any Drop class, follow this process:

1. **Before Starting**: Update status to "🔄 In Progress"
2. **During Refactoring**: Note methods being added/updated
3. **After Completion**: 
   - Update status to "✅ Completed"
   - Add detailed change log entry
   - Record all testing results
   - Note performance impact
   - Update statistics
4. **Final Review**: Ensure report accuracy and completeness

### 0.5 Automated Report Validation
The report should be validated for:
- **Accuracy**: All counts and statistics are correct
- **Completeness**: No refactored classes are missing from tracking
- **Consistency**: Status indicators match actual completion state
- **Timeliness**: Report reflects current refactoring state

## 1. Drop Class Architecture Overview

CloudCart uses Liquid Drop classes to expose data objects to the Liquid templating engine. These classes act as adapters between Eloquent models and Shopify-compatible Liquid objects.

### 1.1 Current Structure
```
app/LiquidEngine/LiquidHelpers/Drop/
├── AbstractDrop.php              # Base drop class
├── AbstractTextContent.php       # Base for content objects (pages, articles)
├── Product.php                   # Product drop
├── Page.php                      # Page drop
├── Shop.php                      # Shop/store drop
├── Customer.php                  # Customer drop
├── Order.php                     # Order drop
└── Category/                     # Category-related drops
```

### 1.2 Naming Convention
- **Drop Class**: `Drop\{ObjectName}` (e.g., `Drop\Product`)
- **Eloquent Model**: `Models\{Category}\{ObjectName}` (e.g., `Models\Product\Product`)
- **Relationship**: Drop class name maps to model: `DropFoo` → `Foo` model

## 2. Shopify Liquid Object Specification Mapping

### 2.1 Core Object Types
Based on Shopify's documentation, the following objects should be implemented:

| Shopify Object | CloudCart Drop | Eloquent Model | Priority |
|----------------|----------------|----------------|----------|
| `product` | [Product.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Product.php) | [Product.php](mdc:app/Models/Product/Product.php) | High |
| `page` | [Page.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Page.php) | [Page.php](mdc:app/Models/Page/Page.php) | High |
| `shop` | [Shop.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Shop.php) | Site/Shop model | High |
| `customer` | [Customer.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Customer.php) | Customer model | High |
| `order` | [Order.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Order.php) | Order model | High |
| `variant` | Variant classes | [Variant.php](mdc:app/Models/Product/Variant.php) | High |
| `collection` | [Collection.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Collection.php) | Category model | Medium |
| `article` | Blog/Article drops | Blog models | Medium |
| `cart` | [Cart.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Cart.php) | Cart model | Medium |

### 2.2 Required Method Patterns
Each Drop class must implement methods following Shopify's specification. Common patterns include:

#### 2.2.1 Identification Methods
```php
public function id(): int
public function handle(): string
public function url(): string
```

#### 2.2.2 Content Methods
```php
public function title(): string           // Maps to 'name' in CloudCart
public function content(): string         // Raw content
public function excerpt(): string         // Short description
public function body(): string           // Formatted content
public function body_html(): string      // HTML formatted content
```

#### 2.2.3 Meta Information
```php
public function created_at(): Date
public function updated_at(): Date
public function published_at(): Date
public function author(): string
public function tags(): array
public function metafields(): array
```

## 3. Implementation Guidelines

### 3.1 Update Existing Method Logic
**CRITICAL**: Preserve existing method signatures but update their internal logic to ensure Shopify compatibility:

1. **Method Signature Preservation**: Keep method names, parameters, and return types unchanged
2. **Logic Enhancement**: Update internal implementation to match Shopify specifications
3. **Data Mapping**: Ensure returned data structure matches Shopify object properties
4. **Backward Compatibility**: All existing template calls must continue to work

### 3.2 Database Schema Verification
Before updating method logic, verify the corresponding Eloquent model attributes:

1. **Check Model Properties**: Examine `fillable`, `guarded`, and `casts` arrays
2. **Review Relationships**: Check `hasMany`, `belongsTo`, `belongsToMany` methods
3. **Validate Database Schema**: Ensure database columns exist for mapped properties
4. **Check Accessors/Mutators**: Look for `getAttribute` and `setAttribute` methods
5. **Verify Current Logic**: Analyze existing method implementation for accuracy

### 3.3 Method Logic Update Strategy

#### 3.3.1 Existing Method Enhancement
When updating existing methods to match Shopify specifications:

```php
// Example: Page Drop - Update existing title() method
public function title(): string
{
    // OLD: return $this->_page->title ?? '';
    // NEW: Ensure it matches Shopify's page.title specification
    return $this->_page->name ?? $this->_page->title ?? '';
}
```

#### 3.3.2 Data Structure Validation
When verifying method returns correct data structure:

```php
// Example: Product Drop - Update existing price() method
public function price(): float
{
    // OLD: return $this->_product->price;
    // NEW: Ensure it returns price in currency's subunit (like Shopify)
    return (float) $this->_product->price_from;
}
```

#### 3.3.3 Relationship Method Updates
When updating relationship methods to match Shopify patterns:

```php
// Example: Product Drop - Update existing vendor() method
public function vendor(): ?Vendor
{
    // OLD: return $this->_product->vendor;
    // NEW: Return Drop object like Shopify
    return $this->_product->relationLoaded('vendor') && $this->_product->vendor
        ? new Vendor($this->_product->vendor) 
        : null;
}
```

### 3.4 Missing Method Implementation
When adding new methods for Shopify compatibility:

```php
/**
 * Shopify compatibility: {method_name}
 * Added to match Shopify's {object}.{method_name} specification
 * 
 * @return mixed
 */
public function {method_name}()
{
    // Implementation based on CloudCart data structure
    // Reference: https://shopify.dev/docs/api/liquid/objects/{object}#{method_name}
    return $this->existing_equivalent_method();
}
```

## 4. Specific Object Implementation Guidelines

### 4.1 Product Object
Priority methods to implement based on Shopify Product Object documentation:

**Required Aliases:**
```php
public function title(): string { return $this->name(); }
public function handle(): string { return $this->url_handle(); }
public function available(): bool { /* compute from stock/active */ }
public function compare_at_price(): ?float { /* implement from pricing logic */ }
public function price(): float { return $this->price_from(); }
public function price_max(): float { return $this->price_to(); }
public function price_min(): float { return $this->price_from(); }
public function published_at(): Date { return $this->date_added(); }
public function created_at(): Date { return $this->date_added(); }
public function updated_at(): Date { return $this->date_modified(); }
public function vendor(): ?string { /* implement vendor mapping */ }
public function type(): string { /* implement product type */ }
public function tags(): array { /* implement tags mapping */ }
```

### 4.2 Page Object
Priority method updates for [Page.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Page.php) based on Shopify Page Object documentation:

**Current Analysis:** The Page Drop has existing methods that need logic verification:
- `title()` exists but may need logic update to match Shopify specification
- `body()` exists but should verify it returns proper content
- `author()` exists but needs validation
- `published_at()` exists but needs Date wrapper verification

**Required Updates:**
```php
// Update existing title() method logic
public function title(): string 
{ 
    // Ensure it properly maps to page name (Shopify compatibility)
    return $this->_page->name ?? ''; 
}

// Update existing body() method to match Shopify's page.content
public function content(): string 
{ 
    return $this->body(); // Ensure body() is properly implemented
}

// Add missing methods for Shopify compatibility
public function excerpt(): string 
{ 
    return Str::limit(strip_tags($this->body()), 150); 
}

public function created_at(): Date 
{ 
    return new Date($this->_page->created_at); 
}

public function updated_at(): Date 
{ 
    return new Date($this->_page->updated_at); 
}
```

### 4.3 Shop Object
Priority methods for [Shop.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Shop.php) based on Shopify Shop Object documentation:

**Required Methods:**
```php
public function address(): ?Address { /* implement shop address */ }
public function checkout_url(): string { /* implement checkout URL */ }
public function money_format(): string { /* implement currency format */ }
public function money_with_currency_format(): string { /* implement with currency */ }
public function password_message(): ?string { /* implement if password protected */ }
public function permanent_domain(): string { /* implement primary domain */ }
```

## 5. Implementation Process

### 5.1 Analysis Phase
For each Drop class:

1. **Load Shopify Specification**: Reference the object documentation
2. **Analyze Current Methods**: Map existing methods to Shopify equivalents
3. **Check Model Attributes**: Verify data availability in Eloquent model
4. **Identify Logic Issues**: Find methods with incorrect or incomplete logic
5. **Document Current State**: Record existing method implementations

### 5.2 Refactoring Phase
For each method requiring updates:

1. **Update Report Status**: Set status to "🔄 In Progress" in [ReportDrops.md](mdc:app/LiquidEngine/DocumentationDrops/ReportDrops.md)
2. **Preserve Method Signature**: Keep name, parameters, and return type
3. **Update Internal Logic**: Modify implementation to match Shopify behavior
4. **Verify Data Sources**: Ensure correct model attributes are accessed
5. **Add Missing Methods**: Implement new methods for Shopify compatibility
6. **Update toArray()**: Include all methods in array representation
7. **Track Method Changes**: Document each method addition/update in progress notes

### 5.3 Documentation Phase
1. **Update Report Progress**: Record methods added/updated counts in [ReportDrops.md](mdc:app/LiquidEngine/DocumentationDrops/ReportDrops.md)
2. **Create Documentation Files**: Generate documentation in `app/LiquidEngine/Documentation/`
3. **Document Method Changes**: Record all logic updates made
4. **Provide Usage Examples**: Include Liquid template examples
5. **Create Migration Guide**: Help developers understand changes
6. **Add Detailed Change Log**: Create comprehensive entry in report document

### 5.4 Validation Phase
1. **Test Existing Functionality**: Ensure no regression in templates
2. **Validate Updated Methods**: Test return values and types
3. **Check Performance**: Ensure no N+1 queries introduced
4. **Verify Documentation**: Ensure examples work correctly
5. **Complete Report Entry**: Finalize detailed change log in [ReportDrops.md](mdc:app/LiquidEngine/DocumentationDrops/ReportDrops.md)
6. **Update Final Status**: Change status to "✅ Completed" with completion date
7. **Validate Report Accuracy**: Ensure all statistics and counts are correct

## 6. Code Examples

### 6.1 Method Logic Validation and Update
```php
// BEFORE: Page Drop title() method (example of logic that may need update)
public function title(): string
{
    return $this->_page->title ?? $this->_page->name ?? '';
}

// AFTER: Updated to ensure Shopify compatibility
public function title(): string
{
    // Shopify page.title should always return the page name
    return $this->_page->name ?? '';
}
```

### 6.2 Database Field Mapping Verification
```php
// BEFORE: Product Drop price() method (check if correct field is used)
public function price(): float
{
    return $this->_product->price; // May be wrong field
}

// AFTER: Updated to use correct price field matching Shopify behavior
public function price(): float
{
    // Shopify product.price is the base price
    return (float) $this->_product->price_from;
}
```

### 6.3 Computed Property Implementation
```php
/**
 * Shopify compatibility: available
 * Computes product availability based on active status and stock
 * 
 * @return bool
 */
public function available(): bool
{
    if (!$this->_product->active) {
        return false;
    }
    
    if ($this->_product->tracking && $this->_product->quantity <= 0) {
        return $this->_product->continue_selling;
    }
    
    return true;
}
```

### 6.4 Relationship Mapping Implementation
```php
/**
 * Shopify compatibility: vendor
 * Maps to vendor relationship
 * 
 * @return Vendor|null
 */
public function vendor(): ?Vendor
{
    if (!$this->_product->relationLoaded('vendor') || !$this->_product->vendor) {
        return null;
    }
    
    return new Vendor($this->_product->vendor);
}
```

### 6.5 Date Handling Implementation
```php
/**
 * Shopify compatibility: published_at
 * Maps to date_added with proper Date wrapper
 * 
 * @return Date
 */
public function published_at(): Date
{
    return new Date($this->_product->date_added ?? $this->_product->created_at);
}
```

## 7. Testing Strategy

### 7.1 Regression Testing
- Ensure all existing Liquid templates continue to work
- Test all existing Drop method calls
- Validate toArray() output for API compatibility
- **Report Requirement**: Document all regression test results in [ReportDrops.md](mdc:app/LiquidEngine/DocumentationDrops/ReportDrops.md)

### 7.2 Shopify Compatibility Testing
- Test new methods return expected data types
- Validate Shopify-style template syntax works
- Check performance impact of new methods
- **Report Requirement**: Record compatibility test outcomes in report

### 7.3 Integration Testing
- Test with real CloudCart data
- Validate relationship loading
- Check caching behavior
- **Report Requirement**: Document integration test results in detailed change log

## 8. Migration Considerations

### 8.1 Backward Compatibility
- All existing method signatures must remain unchanged
- Existing template code must continue to work
- API responses must maintain current structure
- **Report Requirement**: Verify and document backward compatibility in report

### 8.2 Performance Impact
- New methods should not introduce N+1 queries
- Consider lazy loading for expensive operations
- Cache computed values when appropriate
- **Report Requirement**: Record performance impact analysis in report

### 8.3 Documentation Updates
- Create comprehensive Drop class documentation in `app/LiquidEngine/Documentation/`
- Update Liquid template documentation
- Add Shopify compatibility notes
- Provide migration examples for developers
- Document method changes and additions
- **Report Requirement**: Track documentation completion status in report

## 9. Priority Implementation Order

### Phase 1: Core Objects (High Priority)
1. [Product.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Product.php) - Most complex, highest impact
2. [Page.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Page.php) - Content management critical
3. [Shop.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Shop.php) - Global store information

### Phase 2: E-commerce Objects (Medium Priority)
1. [Order.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Order.php) - Order management
2. [Customer.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Customer.php) - Customer data
3. [Cart.php](mdc:app/LiquidEngine/LiquidHelpers/Drop/Cart.php) - Shopping cart

### Phase 3: Content Objects (Lower Priority)
1. Blog/Article drops - Content marketing
2. Collection drops - Category management
3. Variant drops - Product variations

**Report Requirement**: Each phase completion must be tracked and validated in [ReportDrops.md](mdc:app/LiquidEngine/DocumentationDrops/ReportDrops.md)

## 10. Quality Assurance

### 10.1 Code Review Checklist
- [ ] All existing methods preserved
- [ ] New methods follow Shopify specification
- [ ] Proper type hints and return types
- [ ] Documentation includes Shopify reference
- [ ] toArray() method updated
- [ ] No performance regressions
- [ ] Backward compatibility maintained
- [ ] **Report entry completed in [ReportDrops.md](mdc:app/LiquidEngine/DocumentationDrops/ReportDrops.md)**

### 10.2 Validation Rules
- Method names must match Shopify specification exactly
- Return types must be compatible with Shopify expectations
- All methods must handle null/empty data gracefully
- Relationships must be properly loaded before access
- Computed properties must be efficient and cached when needed
- **Report tracking must be maintained throughout process**

## 11. Report Management and Documentation Requirements

### 11.1 Report Management Integration
**CRITICAL**: The [ReportDrops.md](mdc:app/LiquidEngine/DocumentationDrops/ReportDrops.md) document is the authoritative source for refactoring progress. All activities must be tracked there.

#### 11.1.1 Before Starting Any Drop Class Refactoring
1. **Initialize/Update Report**: Ensure the report document exists and is current
2. **Add Drop Class Entry**: Add the class to appropriate phase table if not already present
3. **Set Initial Status**: Mark as "❌ Not Started" initially
4. **Verify Prerequisites**: Ensure model analysis is documented

#### 11.1.2 During Refactoring Process
1. **Update Status**: Change to "🔄 In Progress" when work begins
2. **Track Method Changes**: Keep running tally of methods added/updated
3. **Note Issues**: Document any blockers or issues encountered
4. **Performance Notes**: Record any performance considerations discovered

#### 11.1.3 After Completing Refactoring
1. **Finalize Status**: Update to "✅ Completed" with completion date
2. **Record Final Counts**: Enter exact numbers of methods added/updated
3. **Complete Change Log**: Add comprehensive detailed change log entry
4. **Update Statistics**: Recalculate overall progress percentages
5. **Document Testing**: Record all testing results and validation outcomes

#### 11.1.4 Report Validation Checklist
Before considering a Drop class refactoring complete, verify the report contains:
- [ ] Updated status in phase table
- [ ] Accurate method counts (added/updated)
- [ ] Completion date recorded
- [ ] Detailed change log entry created
- [ ] Testing results documented
- [ ] Performance impact noted
- [ ] Any issues/resolutions recorded
- [ ] Overall statistics updated

### 11.2 Documentation Structure
Create comprehensive documentation for each refactored Drop class in `app/LiquidEngine/DocumentationDrops/`:

```
app/LiquidEngine/Documentation/
├── ReportDrops.md                         # MANDATORY progress tracking report
├── README.md                         # Overview of CloudCart Liquid Drops
├── Product.md                        # Product Drop documentation
├── Page.md                           # Page Drop documentation
├── Shop.md                           # Shop Drop documentation
├── Customer.md                       # Customer Drop documentation
├── Order.md                          # Order Drop documentation
├── ShopifyCompatibility.md           # Shopify compatibility mapping
└── MigrationGuide.md                # Migration guide for developers
```

### 11.3 Report Template Structure
The `app/LiquidEngine/Documentation/DropsRefactoringReport.md` must follow the template structure defined in section 0.2, including:
- Project Overview with current status
- Refactoring Statistics with accurate counts
- Phase Status tables with detailed tracking
- Detailed Change Log entries for each refactored class
- Issues and Blockers documentation
- Quality Metrics tracking
- Lessons Learned section

### 11.4 Documentation Update Process
When refactoring a Drop class:

1. **Before Refactoring**: 
   - Update report status to "🔄 In Progress"
   - Document current method implementations
2. **During Refactoring**: 
   - Note all changes made to method logic
   - Track methods added/updated counts
3. **After Refactoring**: 
   - Update documentation with new method behaviors
   - Complete detailed change log in report
   - Update status to "✅ Completed"
4. **Testing**: 
   - Validate documentation examples work correctly
   - Record test results in report
5. **Review**: 
   - Ensure documentation is clear and comprehensive
   - Verify report accuracy and completeness

### 11.5 Report Maintenance
The report document must be:
- **Updated after every Drop class modification**
- **Validated for accuracy of counts and statistics**
- **Reviewed for consistency and completeness**
- **Used as the primary source of truth for project status**
This comprehensive rule ensures CloudCart's Liquid Drop classes maintain full backward compatibility while adding Shopify compatibility, with mandatory progress tracking through the centralized report document.
