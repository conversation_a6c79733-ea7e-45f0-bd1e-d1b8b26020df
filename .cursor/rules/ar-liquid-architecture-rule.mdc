---
description: Use this rule when you need to know about CloudCart Liquid Architecture
globs:
alwaysApply: false
---
# CloudCart Liquid Architecture and Development Rule

## Overview

This rule provides comprehensive guidelines for developing with CloudCart's Liquid templating system, which is based on Shopify's Liquid template language. It covers the system architecture, component responsibilities, development practices, and troubleshooting guidance for maintaining and extending the Liquid engine in the CloudCart platform.

## Architecture

CloudCart's Liquid templating system consists of two main components:

1. **Laravel-Liquid Package** (`packages/cloudcart/laravel-liquid`): The core templating engine providing base Liquid functionality.
2. **LiquidEngine** (`app/LiquidEngine`): CloudCart-specific extensions and customizations to the Liquid templating system.

This architecture allows us to maintain a clean separation between the core templating functionality and CloudCart-specific features.

### Key References

- **Shopify Liquid Documentation**: [https://github.com/Shopify/liquid](mdc:https:/github.com/Shopify/liquid)
- **CloudCart Liquid Architecture**: [app/LiquidEngine/LIQUID_ARCHITECTURE.md](mdc:app/LiquidEngine/LIQUID_ARCHITECTURE.md)
- **Configuration**: [config/liquid.php](mdc:config/liquid.php)
- **Theme Resolution**: [app/Kernel/Bootstrappers/GuessSiteByHostAndBoot.php](mdc:app/Kernel/Bootstrappers/GuessSiteByHostAndBoot.php)

## Component Responsibilities

### Laravel-Liquid Package

The Laravel-Liquid package (`packages/cloudcart/laravel-liquid`) provides:

- Core Liquid syntax parsing and rendering
- Base tag implementations (if, for, assign, etc.)
- Template compilation and caching
- View finder integration with Laravel
- Base classes for extension (AbstractTag, AbstractBlock, etc.)

### LiquidEngine

The LiquidEngine (`app/LiquidEngine`) extends Laravel-Liquid with CloudCart-specific functionality:

- E-commerce specific tags and filters
- Custom drops for CloudCart entities (products, collections, cart, etc.)
- Theme-specific view finders
- Multi-tenant template handling
- Commands for theme management

## Integration Points

The two components are integrated through several mechanisms:

1. **Service Providers**:
   - `Liquid\LiquidServiceProvider`: Registers core Liquid functionality
   - `App\LiquidEngine\Providers\LiquidServiceProvider`: Extends the core provider with CloudCart-specific drivers
   - `App\LiquidEngine\Providers\LiquidEngineServiceProvider`: Registers CloudCart-specific components

2. **Configuration**:
   - `config/liquid.php`: Combines core Liquid configuration with CloudCart extensions

3. **Class Inheritance**:
   - Custom tags extend `Liquid\AbstractTag` or `Liquid\AbstractBlock`
   - Custom filters extend `Liquid\Filters\AbstractFilters`

## Development Guidelines

### General Rules

1. **Avoid Duplication**: Never duplicate functionality between Laravel-Liquid and LiquidEngine.
2. **Configuration Sync**: Keep `config/liquid.php` in sync with current Laravel-Liquid package development.
3. **Documentation**: Document all development in `app/LiquidEngine/LIQUID_ARCHITECTURE.md`.
4. **Shopify Compatibility**: Follow Shopify's Liquid syntax and conventions wherever possible.
5. **Testing**: Always test new features with sample templates before deployment.

### Core vs. Custom

- Use Laravel-Liquid for core templating functionality
- Use LiquidEngine for CloudCart-specific features

### Adding New Features

#### Where to Implement Features

| Feature Type | Implementation Location | Base Class |
|--------------|-------------------------|------------|
| Core Liquid functionality | Laravel-Liquid package | Appropriate base class |
| CloudCart-specific features | LiquidEngine | Extend from Laravel-Liquid classes |
| Generic tags/filters | Laravel-Liquid package | `AbstractTag` or `AbstractFilters` |
| E-commerce tags/filters | LiquidEngine | Extend from Laravel-Liquid classes |

#### Adding a New Tag

1. Determine if the tag is core Liquid functionality or CloudCart-specific:
   - Core: Add to Laravel-Liquid package
   - CloudCart-specific: Add to `app/LiquidEngine/LiquidHelpers/Tags/`

2. Create the tag class:
   - Extend `Liquid\AbstractTag` or `Liquid\AbstractBlock`
   - Implement required methods (`parse` and `render`)

3. Register the tag in `config/liquid.php`:
   ```php
   'tags' => [
       // ...
       'my_tag' => MyTagClass::class,
   ],
   ```

Example:
```php
<?php

namespace App\LiquidEngine\LiquidHelpers\Tags;

use Liquid\AbstractTag;
use Liquid\Context;
use Liquid\LiquidCompiler;

class TagExample extends AbstractTag
{
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        parent::__construct($markup, $tokens, $compiler);
    }

    public function render(Context $context): string
    {
        // Your implementation here
        return "Example output";
    }
}
```

#### Adding a New Filter

1. Determine if the filter is core Liquid functionality or CloudCart-specific:
   - Core: Add to Laravel-Liquid package
   - CloudCart-specific: Add to `app/LiquidEngine/LiquidHelpers/LiquidFilters/`

2. Create the filter class:
   - Extend `Liquid\Filters\AbstractFilters`
   - Implement filter methods

3. Register the filter in `config/liquid.php`:
   ```php
   'filters' => [
       // ...
       MyFilterClass::class,
   ],
   ```

Example:
```php
<?php

namespace App\LiquidEngine\LiquidHelpers\LiquidFilters;

use Liquid\Context;
use Liquid\Filters\AbstractFilters;

class ExampleFilters extends AbstractFilters
{
    protected ?Context $context;

    public function __construct(?Context $context = null)
    {
        $this->context = $context;
    }

    public function example_filter($input)
    {
        // Your implementation here
        return $input . " - filtered";
    }
}
```

#### Adding a New Drop

1. Create a new drop class in `app/LiquidEngine/LiquidHelpers/Drop/`:
   - Extend `Liquid\Drop`
   - Implement necessary methods and properties

2. For model transformations, register in `config/liquid.php`:
   ```php
   'transform_model' => [
       // ...
       MyModel::class => [MyDrop::class, 'make'],
   ],
   ```

Example:
```php
<?php

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Liquid\Drop;

class ExampleDrop extends Drop
{
    protected $source;

    public function __construct($source)
    {
        $this->source = $source;
    }

    public function example_property()
    {
        return $this->source->some_property;
    }
}
```

### Theme Development

#### Theme Structure

```
resources/themes/
├── {theme_name}/
│   ├── templates/
│   ├── sections/
│   ├── snippets/
│   ├── layout/
│   ├── assets/
│   └── config/
└── _global/
    └── global-theme/
        ├── templates/
        ├── sections/
        ├── snippets/
        ├── layout/
        ├── assets/
        └── config/
```

#### Theme Resolution

1. The system first looks for templates in the active theme.
2. If not found, it falls back to the global theme.
3. The theme resolution is configured in `GuessSiteByHostAndBoot.php`.

#### Testing Templates

To test template rendering, use the host request pattern:
```
https://{site-handle}.ccdev.pro/{template-path}
```

Example: `https://ks7sf.ccdev.pro/collections`

Where:
- `ks7sf` is the host for site with ID 5126
- Site has template ID 164
- Template path is `collections`

## Common Development Tasks

### Implementing Shopify-Compatible Endpoints

Follow the Shopify routing conventions when implementing new endpoints:

- Products: `/products/{handle}`
- Collections: `/collections/{handle}`
- Pages: `/pages/{handle}`
- Blog posts: `/blogs/{blog-handle}/{post-handle}`
- Cart: `/cart`
- Search: `/search`

### Section-Based Template Architecture

Follow Shopify's section-based template architecture:

1. **Layout**: `layout/theme.liquid` contains the main HTML structure
2. **Templates**: `templates/*.liquid` define specific page types
3. **Sections**: `sections/*.liquid` are modular components included in templates
4. **Snippets**: `snippets/*.liquid` are reusable code fragments

### Local Testing

For local testing:
1. Create test templates in a development theme
2. Configure your local environment to use the test theme
3. Use `php artisan serve` with appropriate hostname

## Troubleshooting

### Template Not Found

Check the view finder configuration:
1. Verify the theme exists in `resources/themes/`
2. Check template path in the correct location
3. Confirm the template has the correct extension (`.liquid`)
4. Verify the theme is correctly associated with the site

### Tag or Filter Not Working

1. Verify it's registered in `config/liquid.php`
2. Check namespace and class name are correct
3. Ensure proper inheritance from Laravel-Liquid base classes

### Hosting Issues

If experiencing issues with hosting or multi-tenancy:
1. Check site-host mapping in the database
2. Verify theme assignment for the site
3. Confirm `GuessSiteByHostAndBoot.php` is correctly resolving themes

### Performance Issues

1. Check template caching is enabled
2. Look for expensive operations in custom tags/filters
3. Review drop implementations for N+1 query issues

## Performance Considerations

1. **Template Caching**: Use the built-in caching mechanism for compiled templates
2. **Eager Loading**: Use eager loading for data passed to templates
3. **Partial Rendering**: Use section rendering for AJAX updates
4. **Asset Optimization**: Optimize assets and use CDN where appropriate
