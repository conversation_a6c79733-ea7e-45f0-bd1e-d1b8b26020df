{"name": "cloudcart-liquid-documentation", "version": "2.0.0", "description": "Complete documentation for CloudCart Liquid template engine", "main": "README.md", "scripts": {"lint": "markdownlint **/*.md", "validate": "markdown-link-check **/*.md", "serve": "python3 -m http.server 8080"}, "keywords": ["cloudcart", "liquid", "template-engine", "e-commerce", "documentation", "shopify", "laravel"], "author": "CloudCart Development Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cloudcart/liquid-documentation.git"}, "bugs": {"url": "https://github.com/cloudcart/liquid-documentation/issues"}, "homepage": "https://docs.cloudcart.com/liquid", "devDependencies": {"markdownlint-cli": "^0.37.0", "markdown-link-check": "^3.11.2"}, "engines": {"node": ">=14.0.0"}, "files": ["README.md", "tags/**/*.md", "filters/**/*.md", "drops/**/*.md", "examples/**/*.md", "advanced/**/*.md"]}