# CloudCart Liquid Documentation Makefile

.PHONY: help install build serve dev clean lint validate

# Default target
help:
	@echo "CloudCart Liquid Documentation"
	@echo ""
	@echo "Available commands:"
	@echo "  install    Install dependencies"
	@echo "  build      Build documentation"
	@echo "  serve      Serve documentation locally"
	@echo "  dev        Development mode with auto-reload"
	@echo "  clean      Clean build artifacts"
	@echo "  lint       Lint markdown files"
	@echo "  validate   Validate links in documentation"
	@echo "  help       Show this help message"

# Install dependencies
install:
	@echo "Installing dependencies..."
	@if command -v composer >/dev/null 2>&1; then \
		composer install; \
	else \
		echo "Composer not found, skipping PHP dependencies"; \
	fi
	@if command -v npm >/dev/null 2>&1; then \
		npm install; \
	else \
		echo "npm not found, skipping Node.js dependencies"; \
	fi

# Build documentation
build:
	@echo "Building documentation..."
	@echo "Documentation is ready to use with any static site generator"
	@echo "Files are located in the current directory"

# Serve documentation locally
serve:
	@echo "Serving documentation at http://localhost:8080"
	@python3 -m http.server 8080

# Development mode
dev:
	@echo "Starting development server..."
	@if command -v npm >/dev/null 2>&1; then \
		npm run dev; \
	else \
		make serve; \
	fi

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	@rm -rf node_modules/.cache
	@echo "Clean complete"

# Lint markdown files
lint:
	@echo "Linting markdown files..."
	@if command -v markdownlint >/dev/null 2>&1; then \
		markdownlint **/*.md; \
	elif command -v npm >/dev/null 2>&1; then \
		npm run lint; \
	else \
		echo "Markdownlint not found. Install with: npm install -g markdownlint-cli"; \
		exit 1; \
	fi

# Validate links
validate:
	@echo "Validating links..."
	@if command -v markdown-link-check >/dev/null 2>&1; then \
		find . -name "*.md" -exec markdown-link-check {} \; ; \
	elif command -v npm >/dev/null 2>&1; then \
		npm run validate; \
	else \
		echo "markdown-link-check not found. Install with: npm install -g markdown-link-check"; \
		exit 1; \
	fi

# Check if documentation is complete
check:
	@echo "Checking documentation completeness..."
	@echo "Checking for all required files..."
	@for file in \
		"README.md" \
		"tags/control-flow-tags.md" \
		"tags/iteration-tags.md" \
		"tags/variable-tags.md" \
		"tags/template-tags.md" \
		"tags/theme-tags.md" \
		"tags/form-tags.md" \
		"tags/utility-tags.md" \
		"tags/advanced-tags.md" \
		"tags/auth-tags.md" \
		"tags/navigation-tags.md" \
		"tags/function-tags.md" \
		"tags/integration-tags.md" \
		"filters/string-filters.md" \
		"filters/array-filters.md" \
		"filters/math-filters.md" \
		"filters/date-filters.md" \
		"filters/url-filters.md" \
		"filters/asset-filters.md" \
		"filters/color-filters.md" \
		"filters/escape-filters.md" \
		"filters/helper-filters.md" \
		"filters/custom-filters.md"; do \
		if [ -f "$$file" ]; then \
			echo "✅ $$file"; \
		else \
			echo "❌ $$file (missing)"; \
		fi; \
	done

# Deploy to GitHub Pages (if configured)
deploy:
	@echo "Deploying to GitHub Pages..."
	@if [ -d ".git" ]; then \
		git add . && \
		git commit -m "Update documentation" && \
		git push origin main; \
	else \
		echo "Not a git repository"; \
	fi

# Quick setup for new users
setup:
	@echo "Setting up CloudCart Liquid Documentation..."
	@make install
	@echo ""
	@echo "Setup complete! Documentation is ready to use."
