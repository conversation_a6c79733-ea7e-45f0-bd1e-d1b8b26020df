{"private": true, "workspaces": ["resources/themes/*/assets"], "scripts": {"dev:themes": "npm run dev --workspaces", "build:themes": "npm run build --workspaces"}, "devDependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@vitejs/plugin-vue": "^4.0.0", "sass": "^1.70.0", "vite": "^5.0.0", "vue": "^3.2.0"}, "dependencies": {"@fancyapps/fancybox": "^3.5.7", "@openfonts/montserrat_all": "^1.44.2", "@ttskch/select2-bootstrap4-theme": "^1.5.2", "axios": "^0.19.0", "bootstrap": "^4.3.1", "bootstrap-4-grid": "^3.0.0", "chart.js": "^2.8.0", "ekko-lightbox": "^5.3.0", "jquery": "^3.6.0", "jquery-ui": "^1.12.1", "jquery-ui-bundle": "^1.12.1-migrate", "lightgallery": "^1.6.12", "lodash": "^4.17.15", "material-design-icons": "^3.0.1", "moment": "^2.24.0", "nprogress": "^0.2.0", "popper.js": "^1.15.0", "roboto-fontface": "^0.10.0", "select2": "^4.0.13", "sizzle": "^2.3.4", "slick-carousel": "^1.8.1", "sprite-magic-importer": "^1.6.0", "swiper": "^9.0.0", "uglifycss": "0.0.29", "vanilla-lazyload": "^17.5.2", "vue-i18n": "^9.0.0"}}