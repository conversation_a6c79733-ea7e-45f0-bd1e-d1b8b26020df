<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 4/23/2018
 * Time: 4:24 PM
 */

namespace Console\Http\Controllers\Api;

use App\Helper\Plan;
use App\Models\Gate\PlanFeature;
use App\Models\Gate\PlanFeatureGroup;
use App\Models\Oauth\ConsoleActivity;
use Cache;
use Console\Http\Resources\PlanFeatureResource;
use Console\Http\Resources\Resource;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

/**
 * Class PlanFeaturesController
 * @package Console\Http\Controllers\Api
 */
class PlanFeaturesController extends ResourceController
{
    /**
     * Instantiate a new controller instance.
     *
     */
    public function __construct()
    {
        $this->middleware('permission:list plan features')->only('index');
        $this->middleware('permission:create plan features')->only('store');
        $this->middleware('permission:update plan features')->only('update');
    }

    /**
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $collection = PlanFeature::with(['group', 'restrictedPlans', 'hiddenForPlans'])->get();

        return $this->response(PlanFeatureResource::collection($collection));
    }

    /**
     * @return \Illuminate\Http\Response
     */
    public function groups()
    {
        $collection = PlanFeatureGroup::get();

        return $this->response(Resource::collection($collection));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $this->validateRequest($request);

        $feature = PlanFeature::create($request->only([
            'group_id',
            'mapping',
            'name',
            'cast',
            'active',
            'dynamic_pricing',
            'max_value',
            'overwritable',
        ]));

        Plan::clearCache();
        $feature->load(['group', 'restrictedPlans', 'hiddenForPlans']);

        ConsoleActivity::log(auth()->user(), 'create', $feature);

        Cache::tags('gate-response-cache')->flush();

        return $this->response(new Resource($feature));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function attach(Request $request, $id)
    {
        $feature = PlanFeature::findOrFail($id);
        $values = array_filter($request->all(), fn($value): mixed => !empty($value) || $value == 0);
        $feature->restrictedPlans()->sync(array_map(fn($value): array => ['value' => $value], $values));

        $feature->touch();

        Plan::clearCache();
        $feature->load(['group', 'restrictedPlans', 'hiddenForPlans']);

        Cache::tags('gate-response-cache')->flush();

        return $this->response(new Resource($feature));
    }

    /**
     * @param Illuminate\Http\Request $request
     * @param mixed $id
     * @return mixed
     */
    public function hide(Request $request, $id)
    {
        $feature = PlanFeature::findOrFail($id);

        $feature->hiddenForPlans()->sync($request->all());

        $feature->touch();

        Plan::clearCache();
        $feature->load(['group', 'restrictedPlans', 'hiddenForPlans']);

        Cache::tags('gate-response-cache')->flush();

        return $this->response(new Resource($feature));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function update(Request $request, $id)
    {
        /** @var PlanFeature $feature */
        $feature = PlanFeature::findOrFail($id);

        $this->validateRequest($request);

        $feature->fill($request->only([
            'group_id',
            'mapping',
            'name',
            'cast',
            'active',
            'dynamic_pricing',
            'max_value',
            'overwritable',
        ]));

        ConsoleActivity::log(auth()->user(), 'update', $feature);
        $feature->save();
        Plan::clearCache();
        $feature->load(['group', 'restrictedPlans', 'hiddenForPlans']);

        Cache::tags('gate-response-cache')->flush();

        return $this->response(new Resource($feature));
    }

    /**
     * @param Request $request
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateRequest(Request $request)
    {
        $id = (int) $request->input('id');

        $this->validate($request, [
            'name.en' => 'required',
            'name.bg' => 'sometimes|required',
            'mapping' => 'required|unique:gate.cc_gate.plan_features,mapping,' . $id,
            'group_id' => 'required',
            'cast' => 'required',
            'max_value' => 'nullable|int',
        ]);
    }
}
