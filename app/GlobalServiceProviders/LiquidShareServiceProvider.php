<?php

namespace App\GlobalServiceProviders;

use App\Helper\Liquid\Drops\DropAdditionalCheckoutButtons;
use App\Helper\Liquid\Drops\DropAddress;
use App\Helper\Liquid\Drops\DropLocalization;
use App\Helper\Liquid\Drops\DropPage;
use App\Helper\Liquid\Drops\DropPresets;
use App\Helper\Liquid\Drops\DropProduct;
use App\Helper\Liquid\Drops\DropRequest;
use App\Helper\Liquid\Drops\DropRoutes;
use App\Helper\Liquid\Drops\DropSection;
use App\Helper\Liquid\Drops\DropSettings;
use App\Helper\Liquid\Drops\DropShop;
use App\Helper\Liquid\Drops\DropTheme;
use Illuminate\Support\ServiceProvider;
use Liquid\Context;
use Liquid\Factory;

class LiquidShareServiceProvider extends ServiceProvider
{
    /**
     * @return void
     */
    public function register(): void
    {
        Context::setDrops([
            'additional_checkout_buttons' => new DropAdditionalCheckoutButtons(),
            'address' => new DropAddress(),
            'localization' => new DropLocalization(),
            'page' => new DropPage(),
            'presets' => new DropPresets(),
            'product' => new DropProduct(),
            'request' => new DropRequest(),
            'routes' => new DropRoutes(),
            'section' => new DropSection(), // Cannot use object of type App\Helper\Liquid\Drops\DropSection as array
            'settings' => new DropSettings(),
            'shop' => new DropShop(),
            'theme' => new DropTheme(),
        ]);

        $this->callAfterResolving('liquid.factory', function(Factory $factory) {
            $factory->prependLocation(site('template'));
        });
    }
}
