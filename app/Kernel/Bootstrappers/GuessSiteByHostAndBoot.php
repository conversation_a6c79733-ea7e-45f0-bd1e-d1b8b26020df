<?php

declare(strict_types=1);

namespace App\Kernel\Bootstrappers;

use App\Exceptions\Error;
use App\Exceptions\Fault;
use App\Exceptions\Redirect;
use App\Exceptions\Site\Expired;
use App\Exceptions\Site\HttpNotFoundSite;
use App\Exceptions\Site\Suspended;
use App\Exceptions\SiteMaintenance;
use App\Exceptions\SiteProgressInstall;
use App\Models\Router\Host;
use App\Models\Router\Site;
use Illuminate\Cache\TaggedCache;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Ytake\LaravelSmarty\SmartyFactory;

class GuessSiteByHostAndBoot
{
    /**
     * SiteRegister constructor.
     */
    public function __construct(protected Application $app)
    {
    }

    /**
     * Bootstrap the given application.
     *
     * @throws BindingResolutionException
     * @throws Error
     * @throws Fault
     * @throws SiteMaintenance
     * @throws SiteProgressInstall
     * @throws Redirect
     */
    public function bootstrap(): void
    {
        if (request()->is(['.well-known/acme-challenge/*', 'uptime'])) {
            return;
        }

        $site = $this->findSite();

        when($site, function (Site $site): void {
            $this->handleDomainRedirect($site);

            $site->bootDB();
        }, function (): void {
            throw new HttpNotFoundSite();
        });

        if ($this->app->make('namespace') === 'uptime') {
            return;
        }

        when($site->maintenance, function (): void {
            throw new SiteMaintenance();
        });

        when($site->progress != 'completed', function (Site $site): void {
            throw new SiteProgressInstall($site->progress);
        });

        when($site->plan_expired && app_namespace() == 'site', function (): void {
            throw new Expired();
        });

        when($site->suspended && app_namespace() == 'site', function (): void {
            throw new Suspended();
        });

        when($site, function (Site $site): void {
            $isLiquidEngine = $site->theme->engine === 'liquid' && app_namespace() === 'site';
            if ($isLiquidEngine) {
                $this->changeViewFinderPathsForLiquid($site);
                $view = $this->app->make('view');
                $this->shareLiquidContext($view, $site);
            } else {
                $this->changeViewFinderPaths($site);
            }
            $this->registerSessionStorage($site);
        });

        $this->setDateDefaultTimezone($site);
    }

    /**
     * @return Site|null
     *
     * @throws BindingResolutionException
     */
    protected function findSite(): ?Site
    {
        if (ip2long($hostName = $this->getHostName()) !== false) {
            return null;
        }

        /** @var TaggedCache $cache */
        $cache = $this->app->make('cache')->tags([$this->app->make('config')['cache.router.domain-key']]);

        $ttl = $this->app->make('config')['cache.router.domain-ttl'];

        return $cache->remember(Str::lower($hostName), $ttl, function () use ($hostName, $cache, $ttl) {
            $host = Host::whereHostName($hostName)->with(['routerSite' => function (BelongsTo $query): void {
                $query->with(['hosts', 'user', 'gate', 'theme']);
            }])->first();

            if (!($host->routerSite ?? false)) {
                return false;
            }

            $host->routerSite->hosts->each(function (Host $host) use ($cache, $ttl): void {
                $cache->remember($host->getKey(), $ttl, fn(): \App\Models\Router\Host => $host);
            });

            $host->routerSite->setAttribute('host', $host->host);
            $host->routerSite->setAttribute('id', $host->id);
            $host->routerSite->syncOriginal();

            return $host->routerSite;
        }) ?: null;
    }

    /**
     * @param Site $site
     * @return void
     * @throws BindingResolutionException
     * @throws Redirect
     */
    protected function handleDomainRedirect(Site $site): void
    {
        $siteHost = Str::lower(idn_to_ascii($site->host, 0, INTL_IDNA_VARIANT_UTS46));
        $requestHost = $this->getHostName();
        $redirectCode = app_namespace() == 'sitecp' ? 302 : 301;

        if ($siteHost != $requestHost || $site->primary_host_id != $site->id) {
            $primary_host = $site->getPrimaryHost();
            if ($primary_host && $primary_host->isActive()) {
                $protocol = $site->getScheme($primary_host);
                $url = $protocol . $primary_host->host . ($customPort ?? '') . $_SERVER['REQUEST_URI'];
                throw new Redirect($url, $redirectCode);
            }
        }

        $protocol = $site->getScheme();
        $requested_protocol = $this->app->make('request')->getScheme() . '://';
        if ($protocol != $requested_protocol) {
            $url = $protocol . $site->host . ($customPort ?? '') . $_SERVER['REQUEST_URI'];
            throw new Redirect($url, $redirectCode);
        }
    }

    /**
     * @return string
     * @throws BindingResolutionException
     */
    protected function getHostName(): string
    {
        return Str::lower(idn_to_ascii($this->app->make('request')->host(), 0, INTL_IDNA_VARIANT_UTS46));
    }

    /**
     * @param \App\Models\Router\Site|null $site
     * @return void
     */
    protected function setDateDefaultTimezone(?Site $site): void
    {
        $timezone = when($site, fn(Site $site) => $site->timezone ?? config('app.timezone'), config('app.timezone'));

        date_default_timezone_set($timezone);
    }

    /**
     * @param Site $site
     * @return void
     *
     * @throws BindingResolutionException
     */
    protected function changeViewFinderPaths(Site $site): void
    {
        $paths = null;
        if (app_namespace() == 'site') {
            $paths = [
                $site->template => base_path(sprintf('themes/%s/templates/', $site->template)),
                $site->template . '_includes' => base_path(sprintf('themes/%s/includes/', $site->template)),
                '_global_theme' => base_path('themes/_global/global-theme/'),
                'widgets_new' => base_path('themes/'),
            ];
        }

        // site cp theme
        if (app_namespace() == 'sitecp') {
            $paths = [
                $site->template => base_path('protected/templates/sitecp/'),
            ];
        }

        if ($paths) {
            /** @var \Illuminate\View\Factory $view */
            $view = $this->app->make('view');
            $finder = $view->getFinder();
            $finder->addExtension('tpl');

            // Reset paths and add new ones
            $reflection = new \ReflectionClass($finder);
            $pathsProperty = $reflection->getProperty('paths');
            $pathsProperty->setAccessible(true);
            $pathsProperty->setValue($finder, []);

            // Add each path to the finder
            foreach ($paths as $path) {
                if (is_dir($path)) {
                    $finder->addLocation($path);
                }
            }

            $view->setFinder($finder);

            $view->addNamespace('theme_widgets', base_path(sprintf('themes/%s/widgets', $site->template)));

            if ($this->app->bound('smarty.view')) {
                /** @var SmartyFactory $smartyFactory */
                $smartyFactory = $this->app->make('smarty.view');
                $smarty = $smartyFactory->getSmarty();
                $smarty->setTemplateDir($paths);

                if (!is_dir($dir = $this->getCompiledDir('smarty/cache'))) {
                    @mkdir($dir, 0777, true);
                }

                if (!is_dir($dir = $this->getCompiledDir('smarty'))) {
                    @mkdir($dir, 0777, true);
                }

                $smarty->setCacheDir($this->getCompiledDir('smarty/cache'));
                $smarty->setCompileDir($this->getCompiledDir('smarty'));
            }

            if (app_namespace() == 'site') {
                $view->share('widget', $this->app->make('widget'));
            }

            if (app_namespace() == 'sitecp') {
                $view->share('rtl', in_array($site->language_cp, config('languages.rtl', [])) ? '-rtl' : '');
            } else {
                $view->share('rtl', in_array($this->app->getLocale(), config('languages.rtl', [])) ? '-rtl' : '');
            }
        }
    }

    /**
     * @param $engine
     * @return string
     */
    private function getCompiledDir(string $engine): string
    {
        return storage_path('framework/views/compiled/') . $engine . '/' . (app_namespace() == 'site' ? site('template') : app_namespace()) . '/';
    }

    /**
     * @param \App\Models\Router\Site $site
     * @return void
     *
     * @throws BindingResolutionException
     */
    protected function registerSessionStorage(Site $site): void
    {
        if (!$this->app->make('isCrawler')) {
            $this->app->make('config')['session.driver'] = 'mongodb';
            $this->app->make('config')['session.table'] = 'cc_site_sessions_' . $site->site_id;

            $auth = $this->app->make('auth');
            foreach (['admin', 'subscriber', 'customer'] as $guard) {
                $auth->guard($guard)->setSession($this->app->make('session')->driver());
            }
        }
    }

    private function changeViewFinderPathsForLiquid(Site $site): void
    {
        // Set up theme configuration
        config([
            'app.theme_site' => (clone($site))->toArray(),
            'app.theme_handle' => $site->template,
            'app.theme_host' => 'https://' . $site->host,
            'app.asset_base_url' => 'https://' . $site->host,  // e.g. "https://cdn.ccdev.info" // TODO - get the real one
        ]);

        // Load theme settings
        $path = resource_path("themes/{$site->template}/config/settings_data.json");

        if (File::exists($path)) {
            $data = json_decode(File::get($path), true) ?? [];
            config([
                'app.theme_settings_data' => $data,
            ]);
        }

        $engineTemplatePath = config('liquid.templates_path');

        // Set up paths with proper namespacing for Liquid templates
        $paths = [
            $site->template => base_path("{$engineTemplatePath}/{$site->template}/templates"),
            $site->template . '_layout' => base_path("{$engineTemplatePath}/{$site->template}/layout"),
            $site->template . '_sections' => base_path("{$engineTemplatePath}/{$site->template}/sections"),
            $site->template . '_snippets' => base_path("{$engineTemplatePath}/{$site->template}/snippets"),
            $site->template . '_templates' => base_path("{$engineTemplatePath}/{$site->template}/templates"),
            $site->template . '_widgets' => base_path("{$engineTemplatePath}/{$site->template}/widgets"),
            $site->template . '_theme' => base_path("{$engineTemplatePath}/{$site->template}"), // includes from everywhere in the theme folder
            '_global_theme' => base_path("{$engineTemplatePath}/_global/_global_theme"), // Global theme for fallbacks
            'widgets_new' => base_path("{$engineTemplatePath}"),
        ];

        $view = $this->app->make('view');
        $finder = $view->getFinder();

        // Reset the view finder paths to prevent mixing with Smarty paths
        $reflection = new \ReflectionClass($finder);
        $pathsProperty = $reflection->getProperty('paths');
        $pathsProperty->setAccessible(true);
        $pathsProperty->setValue($finder, []);

        // Add paths to the view finder
        foreach ($paths as $namespace => $path) {
            if (is_dir($path)) {
                $view->addLocation($path);
            }
        }

        // Add namespaced paths for Liquid templates
        $view->addNamespace('theme', base_path("{$engineTemplatePath}/{$site->template}"));
        $view->addNamespace('layout', base_path("{$engineTemplatePath}/{$site->template}/layout"));
        $view->addNamespace('sections', base_path("{$engineTemplatePath}/{$site->template}/sections"));
        $view->addNamespace('snippets', base_path("{$engineTemplatePath}/{$site->template}/snippets"));
        $view->addNamespace('templates', base_path("{$engineTemplatePath}/{$site->template}/templates"));
        $view->addNamespace('widgets', base_path("{$engineTemplatePath}/{$site->template}/widgets"));

        // Add global theme namespaces for fallbacks
        $view->addNamespace('global', base_path("{$engineTemplatePath}/_global/_global_theme"));
        $view->addNamespace('global_layout', base_path("{$engineTemplatePath}/_global/_global_theme/layout"));
        $view->addNamespace('global_sections', base_path("{$engineTemplatePath}/_global/_global_theme/sections"));
        $view->addNamespace('global_snippets', base_path("{$engineTemplatePath}/_global/_global_theme/snippets"));
        $view->addNamespace('global_templates', base_path("{$engineTemplatePath}/_global/_global_themetemplates"));

        // Set the view finder with our updated configuration
        $view->setFinder($finder);

        // NEW: Bootstrap Liquid compiled store with CompiledStoreManager
        $this->bootLiquidCompiledStore($site);
    }

    private function shareLiquidContext(\Illuminate\View\Factory $view, Site $site): void
    {
        $view->share([
            'inDevelopment' => inDevelopment(),
            'getGoogleMapKey' => getGoogleMapKey(),
            'img_url' => config('url.img'),
            'theme_config' => new \App\Common\Theme(),
            'widget' => $this->app->make('widget'),
            'rtl' => in_array($this->app->getLocale(), config('languages.rtl', [])) ? '-rtl' : '',
            'request_path' => request()->path(),
            'cart' => session('cart', []),
            'customer' => \Auth::guard('customer')->user(),
            'settings' => config('app.theme_settings_data.current') ?? [],
            'shop' => [
                'name' => $site->gate->name ?? config('app.name'),
                'domain' => $site->host,
                'url' => 'https://' . $site->host,
                'secure_url' => 'https://' . $site->host,
                'locale' => app()->getLocale(),
                'currency' => $site->currency ?? 'USD',
            ],
            'template' => $site->template,
            'request' => request(),
            'page_title' => '',
            'canonical_url' => url()->current(),
            'locales' => collect(config('liquid.locales', [
                [
                    'name' => 'English',
                    'code' => 'en',
                ],
                [
                    'name' => 'Bulgarian',
                    'code' => 'bg-BG',
                ],
            ]))->pluck('name', 'code')->toArray(),
        ]);
    }

    /**
     * Bootstrap Liquid compiled store with the new storage manager
     *
     * This replaces the old hardcoded directory approach with the flexible
     * CompiledStoreManager system that supports multiple storage backends.
     */
    protected function bootLiquidCompiledStore(Site $site): void
    {
        // Check if liquid compiler is bound (may not be in all contexts)
        if (!$this->app->bound('liquid.compiler')) {
            return;
        }

        try {
            // Get the compiled store manager
            /** @var \Liquid\Storage\CompiledStoreManager $storeManager */
            $storeManager = $this->app->make('liquid.compiled.store.manager');

            // Generate namespace for this site/context
            $namespace = $this->generateCompiledStoreNamespace($site);

            // Set namespace for all drivers in the manager
            $storeManager->setNamespace($namespace);

            // Get the configured store driver
            $store = $storeManager->driver();

            // Configure the liquid compiler with the new store
            $this->configureLiquidCompiler($store, $namespace);

            // Also configure JSON compiler if it exists
            $this->configureLiquidJsonCompiler($store, $namespace);

            // Log successful initialization in debug mode
            if (config('app.debug', false)) {
                \Log::info('Liquid compiled store initialized', [
                    'driver' => $storeManager->getDefaultDriver(),
                    'namespace' => $namespace,
                    'site_template' => $site->template,
                    'app_namespace' => app_namespace(),
                ]);
            }

        } catch (\Exception $e) {
            // Log error and fallback to legacy behavior
            \Log::error('Failed to initialize Liquid compiled store, falling back to legacy approach', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'site_template' => $site->template,
                'app_namespace' => app_namespace(),
            ]);

            // Graceful fallback to old directory-based approach
            $this->fallbackToLegacyCompiledDir();
        }
    }

    /**
     * Configure the main Liquid compiler with the compiled store
     */
    protected function configureLiquidCompiler($store, string $namespace): void
    {
        $compiler = $this->app->make('liquid.compiler');

        // Check if compiler supports the new setCompiledStore method
        if (method_exists($compiler, 'setCompiledStore')) {
            $compiler->setCompiledStore($store);
        } else {
            // Fallback: If using old API, create directory for file-based storage
            if ($store instanceof \Liquid\Storage\Stores\FileCompiledStore && method_exists($store, 'getPath')) {
                $compiledDir = $store->getPath() . '/' . $namespace;

                if (!is_dir($compiledDir)) {
                    @mkdir($compiledDir, 0777, true);
                }

                $compiler->setCompiledDir($compiledDir);
            }
        }
    }

    /**
     * Configure the JSON Liquid compiler with the compiled store
     */
    protected function configureLiquidJsonCompiler($store, string $namespace): void
    {
        if (!$this->app->bound('liquid.json_compiler')) {
            return;
        }

        $jsonCompiler = $this->app->make('liquid.json_compiler');

        // Check if JSON compiler supports the new setCompiledStore method
        if (method_exists($jsonCompiler, 'setCompiledStore')) {
            $jsonCompiler->setCompiledStore($store);
        } else {
            // Fallback: If using old API, create directory for file-based storage
            if ($store instanceof \Liquid\Storage\Stores\FileCompiledStore && method_exists($store, 'getPath')) {
                $compiledDir = $store->getPath() . '/' . $namespace;

                if (!is_dir($compiledDir)) {
                    @mkdir($compiledDir, 0777, true);
                }

                $jsonCompiler->setCompiledDir($compiledDir);
            }
        }
    }

    /**
     * Generate namespace for compiled store based on site and context
     *
     * This ensures proper multi-tenant separation of compiled templates.
     */
    protected function generateCompiledStoreNamespace(Site $site): string
    {
        $namespace = app_namespace();

        if ($namespace === 'site') {
            // For site namespace, use template name for separation
            return 'site_' . $site->template;
        }

        // For other namespaces (sitecp, api2, etc.), use the namespace directly
        return $namespace;
    }

    /**
     * Fallback to legacy compiled directory approach
     *
     * This is used when CompiledStoreManager fails to initialize,
     * ensuring the system continues to work even if there are issues.
     */
    protected function fallbackToLegacyCompiledDir(): void
    {
        if ($this->app->bound('liquid.compiler')) {
            if (!is_dir($compiledDir = $this->getCompiledDir('liquid'))) {
                @mkdir($compiledDir, 0777, true);
            }

            $this->app->make('liquid.compiler')->setCompiledDir($compiledDir);
        }

        // Also handle JSON compiler
        if ($this->app->bound('liquid.json_compiler')) {
            if (!is_dir($compiledDir = $this->getCompiledDir('liquid'))) {
                @mkdir($compiledDir, 0777, true);
            }

            $jsonCompiler = $this->app->make('liquid.json_compiler');
            if (method_exists($jsonCompiler, 'setCompiledDir')) {
                $jsonCompiler->setCompiledDir($compiledDir);
            }
        }
    }
}
