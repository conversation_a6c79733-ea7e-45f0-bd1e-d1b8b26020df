<?php

declare(strict_types=1);

use App\Common\DateTimeFormat;
use App\Core\Facades\Module;
use App\Core\Modules;
use App\Helper\Cache\CcCache;
use App\Helper\Format;
use App\Locale\Weight;
use App\Models\Setting\FavIcon;
use App\Models\Setting\Logo;
use Carbon\Carbon;
use Illuminate\Container\Container;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;
use Modules\Core\Core\Models\TranslationHelper;
use Spatie\SchemaOrg\Schema;

if (!function_exists('mix_gate')) {
    /**
     * Get the path to a versioned Mix file.
     *
     * @param string $path
     * @return HtmlString|string
     *
     */
    function mix_gate(string $path): string
    {
//        return 'https://' . config('url.gate') . '/' . $path . '?' . app('last_build');
        return 'https://' . request()->host() . '/admin/gate/' . $path . '?' . app('last_build');
    }
}

if (!function_exists('app_namespace')) {
    /**
     * Get application namespace key.
     *
     * @return string
     */
    function app_namespace()
    {
        return app()->bound('namespace') ? app('namespace') : null;
    }
}

if (!function_exists('setting')) {
    /**
     * Get / set the specified configuration value.
     *
     * If an array is passed as the key, we will assume you want to set an array of values.
     *
     * @param null $key
     * @param mixed $default
     *
     * @return \App\Helper\Setting\DatabaseSettingStore|mixed
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    function setting($key = null, $default = null)
    {
        if (is_null($key)) {
            return app(\App\Helper\Setting\SettingStore::class);
        }

        if ($key == 'site_plan') {
            return site('plan');
        }

        return app(\App\Helper\Setting\SettingStore::class)->get($key, $default);
    }
}

if (!function_exists('configuration')) {
    /**
     * Get / set the specified configuration value.
     *
     * If an array is passed as the key, we will assume you want to set an array of values.
     *
     * @param string $group
     * @param string|null $key
     * @param mixed $default
     * @param string|null $cast
     *
     * @return \Illuminate\Config\Repository|mixed
     */
    function configuration(string $group, ?string $key = null, mixed $default = null, ?string $cast = null)
    {
        $castResult = (fn($value) => match (strtolower((string) $cast)) {
            'array' => (array)($value ?: []),
            'int' => (int)$value,
            'bool', 'boolean' => (bool)$value,
            default => $value,
        });

        if (!site('site_id')) {
            return $key ? $castResult(null) : new \Illuminate\Config\Repository([]);
        }

        $configuration = \App\Models\Setting\Configuration::getByGroup($group);

        $value = is_null($key) ? $configuration : $configuration->get($key, $default);

        return !is_null($key) ? $castResult($value) : $value;
    }
}

if (!function_exists('site')) {
    /**
     * Get currently site.
     *
     * @param null $key
     * @param null $default
     * @return string|int|\App\Models\Router\Site
     */
    function site($key = null, $default = null)
    {
        /** @var \App\Models\Router\Site $site */
        $site = \App\Models\Router\Site::$_is_booted && app()->bound('site') ? app('site') : null;
        if (!$site && $key == 'template') {
            return 'default';
        }

        if (!$site && $key == 'engine') {
            return Theme::getThemeEngine();
        }
        if ($key == 'engine') {
            return Theme::getThemeEngine();
        }
        if ($key == 'theme_key') {
            // return $site->theme->key ?? 'none';
            return $site->theme->mapping ?? 'none';
        }
        if ($key == 'theme_id') {
            return $site->theme->id ?? null;
        }

        if ($key == 'dir') {
            if(app_namespace() == 'sitecp') {
                return base_path('protected/templates/sitecp/');
            } else {
                return base_path('themes/' . $site->template . '/templates/');
            }
        }

        if ($key == 'stat') {
            return !$site || !$site->stat ? collect() : $site->stat;
        }

        if (!$site) {
            return $default;
        }

        if (!$key) {
            return $site;
        }

        if ($key == 'language_cp' && app_namespace() == 'sitecp' && session('language_cp_force')) {
            return session('language_cp_force', $site->language_cp ?: $site->language);
        }

        if (strpos($key, '.')) {
            return $site->dotGet($key, $default);
        }

        return $site->$key;
    }
}

if (!function_exists('lang')) {
    /**
     * Get the translation for the given key.
     *
     * @param string $key
     * @param array $replace
     * @param string|null $locale
     * @param bool $fallback
     *
     * @return string|array|null|\App\Helper\Translation\Translator
     */
    function lang($key = null, array $replace = [], $locale = null, $fallback = true)
    {
        if (is_null($key)) {
            return app('translator');
        }

        return app('translator')->get($key, $replace, $locale, $fallback);
    }
}

if (!function_exists('__')) {
    /**
     * Get the translation for the given key.
     *
     * @param string $key
     * @param array $replace
     * @param string|null $locale
     * @param bool $fallback
     *
     * @return string|array|null
     */
    function __($key, array $replace = [], $locale = null, $fallback = true)
    {
        return lang($key, $replace, $locale, $fallback);
    }
}

if (!function_exists('__en')) {
    /**
     * Get the en translation for the given key.
     *
     * @param string $key
     * @param array $replace
     * @param bool $fallback
     *
     * @return string|array|null
     */
    function __en($key, array $replace = [], $fallback = true)
    {
        return lang($key, $replace, 'en', $fallback);
    }
}

if (!function_exists('site_dir')) {
    /**
     * Translates the given message based on a count.
     *
     * @param string $path
     *
     * @return string
     * @throws Exception
     */
    function site_dir($path = null): string
    {
        $site = site();
        if (!$site) {
            throw new Exception('Site in not initialized!');
        }

        return $site->site_id . '/' . ltrim((string) $path, '/');
    }
}

if (!function_exists('locale')) {
    /**
     * Get language code.
     *
     * @return string
     */
    function locale()
    {
        return app()->getLocale();
    }
}

if (!function_exists('ddob')) {
    /**
     * Dump the passed variables and end the script.
     *
     * @param mixed
     *
     * @return void
     */
    function ddob(...$args): void
    {
        $obLevel = ob_get_level();
        do {
            ob_get_clean();
            $curObLevel = ob_get_level();
        } while ($curObLevel > $obLevel);

        dd(...$args);
    }
}

if (!function_exists('dnd')) {
    /**
     * Dump the passed variables and continue the script.
     *
     * @param mixed
     *
     * @return void
     */
    function dnd(...$args): void
    {
        $cloner = new \Symfony\Component\VarDumper\Cloner\VarCloner();
        if (\in_array(\PHP_SAPI, ['cli', 'phpdbg'])) {
            $dumper = new \Symfony\Component\VarDumper\Dumper\CliDumper();
        } else {
            $dumper = new \Symfony\Component\VarDumper\Dumper\HtmlDumper();
            $dumper->setTheme('light');
        }

        foreach ($args as $v) {
            $dumper->dump($cloner->cloneVar($v));
        }
    }
}

// @todo must be migrate this
if (!function_exists('get')) {
    function get($field, $type = null)
    {
        return request()->query($field);
    }
}

// @todo fix for builder
if (!function_exists('files')) {
    function files($field)
    {
        return $_FILES[$field] ?? null;
    }
}

// @todo fix for builder
if (!function_exists('post')) {
    function post($field, $type = null)
    {

        if ($type == 'bool') {
            return (bool)Request::input($field);
        }

        if (Request::input($field)) {
            $post = Request::input($field);

            if (!in_array($type, ['int', 'float', 'html'])) {
                return $post;
            } elseif ($type === 'int') {
                return intval($post);
            } elseif ($type === 'float') {
                return floatval($post);
            } elseif ($type === 'html') {
                return filter_var($post, FILTER_SANITIZE_STRING, FILTER_FLAG_STRIP_LOW);
            }

        }

        return;

    }
}

// @todo fix for builder
if (!function_exists('utf8_strcasecmp')) {
    function utf8_strcasecmp($str1, $str2): int
    {
        return strcmp(mb_strtoupper((string) $str1, 'UTF-8'), mb_strtoupper((string) $str2, 'UTF-8'));
    }
}

// @todo fix for builder
if (!function_exists('array_iunique')) {
    function array_iunique($array): array
    {
        $lowered = array_map(\Illuminate\Support\Str::class . '::lower', $array);
        return array_intersect_key($array, array_unique($lowered));
    }
}

// @todo fix for builder
if (!function_exists('return_bytes')) {
    function return_bytes($str): ?string
    {
        return preg_replace_callback('/^\s*(?P<number>\d+)\s*(?P<format>([kmgtp]?)b?)?\s*$/i', fn($match) => match (strtoupper((string) $match['format'])) {
            "KB", "K" => $match['number'] * 1024,
            "MB", "M" => $match['number'] * 1024 ** 2,
            "GB", "G" => $match['number'] * 1024 ** 3,
            "TB", "T" => $match['number'] * 1024 ** 4,
            "PB", "P" => $match['number'] * 1024 ** 5,
            default => $match['number'],
        }, trim((string) $str));
    }
}

if (!function_exists('logo')) {
    /**
     * Get store logo by type & size.
     *
     * @param mixed $size
     * @param string $type / main,invoice,bill_of_lading
     *
     * @param bool $return_model
     * @return string|Logo
     * @throws \App\Exceptions\Error
     */
    function logo($size = null, string $type = 'main', $return_model = false)
    {
        /** @var Logo $logo_model */
        $logo_model = CcCache::rememberNoTags('logo.' . $type, config('cache.ttl_1d'), function () use ($type) {
            $logo = Logo::where('type', $type)->first();
            return $logo ?: new Logo();
        });

        return $return_model ? $logo_model : $logo_model->getImage($size);
    }
}

if (!function_exists('noImage')) {
    /**
     * Get no image
     *
     * @param mixed $size
     * @return string
     */
    function noImage($size = null)
    {
        return \App\Helper\ArrayCache::remember('noImage.product.' . $size, fn() => with(new \App\Models\Product\Product())->getImage($size));
    }
}

if (!function_exists('noImages')) {
    /**
     * Get no image
     *
     * @return array
     */
    function noImages()
    {
        return \App\Helper\ArrayCache::remember('noImage.products', fn() => with(new \App\Models\Product\Product())->getImages());
    }
}

if (!function_exists('noImageVendor')) {
    /**
     * Get no image
     *
     * @param mixed $size
     * @return string
     */
    function noImageVendor($size = null)
    {
        return \App\Helper\ArrayCache::remember('noImage.vendor.' . $size, fn() => with(new \App\Models\Product\Vendor())->getImage($size));
    }
}

if (!function_exists('noImageCategory')) {
    /**
     * Get no image
     *
     * @param mixed $size
     * @return string
     */
    function noImageCategory($size = null)
    {
        return \App\Helper\ArrayCache::remember('noImage.category.' . $size, fn() => with(new \App\Models\Product\Category())->getImage($size));
    }
}

if (!function_exists('noImageArticle')) {
    /**
     * Get no image
     *
     * @param mixed $size
     * @return string
     */
    function noImageArticle($size = null)
    {
        return \App\Helper\ArrayCache::remember('noImage.article.' . $size, fn() => with(new \App\Models\Blog\Article())->getImage($size));
    }
}

if (!function_exists('hasLogo')) {
    /**
     * Get store logo by type & size.
     *
     * @param string $type
     *
     * @return string
     * @throws \App\Exceptions\Error
     */
    function hasLogo($type = 'main')
    {
        return logo(null, $type, true)->hasImage();
    }
}

if (!function_exists('hasLogoByType')) {
    /**
     * Get store logo by type & size.
     *
     * @param string $type
     *
     * @return string
     * @throws \App\Exceptions\Error
     */
    function hasLogoByType($type = 'main'): bool
    {
        $logo = logo(null, $type, true);
        return $logo->type == $type && $logo->hasImage();
    }
}

if (!function_exists('favicon')) {
    /**
     * Get store favicon.
     *
     * @return string
     * @throws \App\Exceptions\Error
     */
    function favicon()
    {
        return FavIcon::first()->getImage();
    }
}

if (!function_exists('prefixParserMerge')) {
    /**
     * @param $element
     * @param string $implode
     * @param int|null $remove
     * @return string
     */
    function prefixParserMerge($element, string $implode = '.', ?int $remove = null): string
    {
        $element = str_replace('[]', '@', $element);
        $element = preg_split('~(\[\]|\[|\]|\.|\@)~', $element, 0, PREG_SPLIT_DELIM_CAPTURE);
        $element = array_filter($element, function ($e): bool {
            if ($e === 0 || $e === '0' || $e === '@') {
                return true;
            }

            if ($e == ']' || $e == '[' || $e == '.') {
                return false;
            }

            return !empty($e);
        });

        $element = array_map(fn(string $element): string => str_replace(['.@', '@.'], '.', $element), $element);

        //        $element = array_map(function($element) {
        //            return str_replace('@', '', $element);
        //        }, $element);

        if ($remove) {
            for ($i = 0; $i < $remove; $i++) {
                array_pop($element);
            }
        }

        return implode($implode, $element);
    }
}

if (!function_exists('inputPrefix')) {
    /**
     * @param $input
     * @param null $prefix
     * @param int|null $remove
     * @return string
     */
    function inputPrefix($input, ?string $prefix = null, ?int $remove = null)
    {
        if (empty($prefix)) {
            return formatField(prefixParserMerge($input));
        }

        return formatField(prefixParserMerge($prefix, '.', $remove) . '.' . prefixParserMerge($input));
    }
}

if (!function_exists('inputSuffix')) {
    /**
     * @param $input
     * @param null $suffix
     * @return string
     */
    function inputSuffix($input, $suffix = null)
    {
        if (empty($suffix)) {
            return formatField(prefixParserMerge($input));
        }

        return formatField(prefixParserMerge($input) . '.' . prefixParserMerge($suffix));
    }
}

if (!function_exists('inputIdPrefix')) {
    /**
     * @param $input
     * @param null $prefix
     * @return string
     */
    function inputIdPrefix($input, $prefix = null)
    {
        if (empty($prefix)) {
            return formatField(prefixParserMerge($input, '-'));
        }

        return formatField(prefixParserMerge($prefix, '-') . '-' . prefixParserMerge($input, '-'));
    }
}

if (!function_exists('inputJsError')) {
    /**
     * @param $input
     * @param null $prefix
     * @return string
     */
    function inputJsError($input, $prefix = null): string
    {
        if (empty($prefix)) {
            return str_replace('_', '-', 'js-error-' . prefixParserMerge($input, '-'));
        }

        return str_replace('_', '-', 'js-error-' . prefixParserMerge($prefix, '-') . '-' . prefixParserMerge($input, '-'));
    }
}

if (!function_exists('cssClass')) {
    /**
     * @param $input
     * @param null $prefix
     * @return string
     */
    function cssClass($input, $prefix = null): string
    {
        if (empty($prefix)) {
            return prefixParserMerge($input, '-');
        }

        return prefixParserMerge($prefix, '-') . '-' . prefixParserMerge($input, '-');
    }
}

if (!function_exists('formatField')) {
    /**
     * @param $field
     * @return string
     */
    function formatField($field)
    {
        if (!str_contains((string) $field, '.')) {
            return $field;
        }

        $parts = explode('.', (string) $field);
        $prefix = array_shift($parts);
        return str_replace('[@]', '[]', $prefix . '[' . implode('][', $parts) . ']');
    }
}

if (!function_exists('routeExists')) {
    /**
     * Get the URL to a named route.
     *
     * @param string $name
     * @return boolean
     */
    function routeExists($name)
    {
        return Route::getRoutes()->hasNamedRoute($name);
    }
}

if (!function_exists('forward')) {
    /**
     * Get the URL to a named route.
     *
     * @param string $name
     * @param mixed $parameters
     * @return string
     *
     * @throws Exception
     */
    function forward($name, $parameters = null)
    {
        /** @var \Illuminate\Routing\Route $route */
        if (is_null($route = Route::getRoutes()->getByName($name))) {
            throw new Exception(sprintf('Route name "%s" is not defined!', $name));
        }

        /** @var \Illuminate\Http\Request $request */
        $request = app('request');
        $route->bind($request);
        if ($parameters) {
            foreach ((array)$parameters as $key => $value) {
                $route->setParameter($key, $value);
            }
        }

        return $route->run();
    }
}

if (!function_exists('activeRoute')) {
    /**
     * @param null $name
     * @return null|string|bool
     */
    function activeRoute($name = null)
    {
        $current = Route::getCurrentRoute();
        if (!$current) {
            return;
        }

        if (empty($args = func_get_args()) || $args === []) {
            return $current->getName();
        }

        if (isset($args[0]) && is_array($args[0])) {
            $args = $args[0];
        }

        $args = array_map(fn($arg) => explode(' ', (string) $arg), $args);

        $args = \Illuminate\Support\Arr::collapse($args);

        $name = $current->getName();
        foreach ($args as $i) {
            if (\Illuminate\Support\Str::is($i, $name)) {
                return true;
            }
        }

        return false;
    }
}

if (!function_exists('routeParameter')) {
    /**
     * @param string $parameter
     * @param null|mixed $default
     * @return null|string
     */
    function routeParameter($parameter, $default = null)
    {
        $current = Route::getCurrentRoute();
        if (!$current) {
            return $default;
        }

        return $current->parameter($parameter) ?: $default;
    }
}

if (!function_exists('getReferer')) {
    /**
     * @return null|string
     */
    function getReferer()
    {
        $referrer = Request::server('HTTP_REFERER');
        return $referrer ?: (session() ? session()->previousUrl() : null);
    }
}

if (!function_exists('array_key_exists_multiple')) {

    /**
     * @param array|string $name
     * @param array $array
     * @return bool
     */
    function array_key_exists_multiple($name, array $array): bool
    {
        foreach ((array)$name as $search) {
            if (array_key_exists($search, $array)) {
                return true;
            }
        }

        return false;
    }
}

if (!function_exists('array_column_multiple')) {

    /**
     * @param array $array
     * @param string $column
     * @param bool $unique
     * @return array
     */
    function array_column_multiple(array $array, string $column, bool $unique = true): array
    {
        $tmp = [];
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $tmp = array_merge($tmp, array_column_multiple($value, $column, $unique));
            } elseif ($key == $column) {
                $tmp[] = $value;
            }
        }

        return $unique ? array_unique($tmp) : $tmp;
    }
}

if (!function_exists('in_array_multiple')) {

    /**
     * @param array|string $name
     * @param array $array
     * @return bool
     */
    function in_array_multiple($name, array $array): bool
    {
        foreach ((array)$name as $search) {
            if (in_array($search, $array)) {
                return true;
            }
        }

        return false;
    }
}

if (!function_exists('in_array_all')) {

    /**
     * @param array|string $name
     * @param array $array
     * @return bool
     */
    function in_array_all($name, array $array): bool
    {
        foreach ($array as $search) {
            if (!in_array($search, (array)$name)) {
                return false;
            }
        }

        return true;
    }
}

if (!function_exists('build_url')) {
    /**
     * @param $parsed
     *
     * @return string
     */
    function build_url(array $parsed): string
    {
        $get = (fn($key) => $parsed[$key] ?? null);

        $pass = $get('pass');
        $user = $get('user');
        $userinfo = $pass !== null ? sprintf('%s:%s', $user, $pass) : $user;
        $port = $get('port');
        $scheme = $get('scheme');
        $query = $get('query');
        $fragment = $get('fragment');
        $authority =
            ($userinfo !== null ? $userinfo . '@' : '') .
            $get('host') .
            ($port ? ':' . $port : '');

        return
            (strlen((string) $scheme) ? $scheme . ':' : '') .
            (strlen($authority) ? '//' . $authority : '') .
            $get('path') .
            (strlen((string) $query) ? '?' . $query : '') .
            (strlen((string) $fragment) ? '#' . $fragment : '');
    }
}

if (!function_exists('get_percentage')) {

    /**
     * @param $total
     * @param $number
     * @param int $decimal
     * @return float|int
     */
    function get_percentage($total, $number, $decimal = 2): float|int
    {
        if ($total > 0) {
            return round($number / ($total / 100), $decimal);
        } else {
            return 0;
        }
    }
}

if (!function_exists('percent_of_number')) {

    /**
     * A simple PHP function that calculates the percentage of a given number.
     *
     * @param int|float $number The number you want a percentage of.
     * @param int|float $percent The percentage that you want to calculate.
     * @param bool $int
     * @return int The final result.
     */
    function percent_of_number($number, $percent, $int = true): int|float
    {
        $amount = ($percent / 100) * $number;
        return $int ? intval(round($amount)) : $amount;
    }
}

if (!function_exists('recursive_array_search')) {
    /**
     * @param string $needle
     * @param array $haystack
     * @return false|string|integer
     */
    function recursive_array_search($needle, array $haystack)
    {
        foreach ($haystack as $key => $value) {
            $current_key = $key;
            if ($needle === $value or (is_array($value) && ($new_key = recursive_array_search($needle, $value)) !== false)) {
                return $new_key ?? $current_key;
            }
        }

        return false;
    }
}

if (!function_exists('recursive_in_array')) {
    /**
     * @param string $needle
     * @param array $haystack
     * @return boolean
     */
    function recursive_in_array($needle, array $haystack): bool
    {
        foreach ($haystack as $key => $value) {
            if ($needle === $value or (is_array($value) && recursive_in_array($needle, $value) !== false)) {
                return true;
            }
        }

        return false;
    }
}

if (!function_exists('isCaller')) {

    /**
     * @param array ...$needle
     * @return bool
     */
    function isCaller(...$needle): bool
    {
        if (count($needle) == 1 && is_array($needle[0])) {
            $needle = $needle[0];
        }

        $needle = array_map(\Illuminate\Support\Str::class . '::lower', $needle);
        return !!collect(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS))->first(function ($trace) use ($needle): bool {
            if (empty($trace['class']) || empty($trace['function'])) {
                return false;
            }

            $caller = Str::lower(implode('::', [$trace['class'], $trace['function']]));
            foreach ($needle as $need) {
                if (\Illuminate\Support\Str::is($need, $caller)) {
                    return true;
                }
            }

            return false;
        });
    }
}

if (!function_exists('detectEnvironment')) {

    /**
     * @return bool
     */
    function detectEnvironment()
    {
        return env('APP_ENV', 'production');
        /*static $env;
        if (isset($env)) {
            return $env;
        }

        // local, testing, staging or production
        if (isset($_SERVER['ENVIRONMENT'])) {
            return $env = $_SERVER['ENVIRONMENT'];
        }

        return $env = 'local';*/
    }
}

if (!function_exists('setAutoEnvironment')) {

    /**
     * @return bool
     */
    function setAutoEnvironment(): void
    {
        $value = detectEnvironment();

        if (function_exists('putenv')) {
            putenv('APP_ENV=' . $value);
        }

        $_ENV['APP_ENV'] = $value;
        $_SERVER['APP_ENV'] = $value;
    }
}

if (!function_exists('inDevelopment')) {

    /**
     * @return bool
     */
    function inDevelopment(): string|bool
    {
        if (detectEnvironment() == 'production') {
            return false;
        }

        if (detectEnvironment() == 'staging') {
            return 'staging';
        }

        return true;
    }
}

/**
 * @param $key
 * @return bool
 */
if (!function_exists('isValidGoogleMapKey')) {

    /**
     * @param $key
     * @return bool
     */
    function isValidGoogleMapKey($key = null): bool
    {
        return true;
    }
}


if (!function_exists('getGoogleMapKey')) {

    /**
     * @return null|string
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    function getGoogleMapKey(): ?string
    {
//         return null;
        if (app()->runningInConsole() || (inDevelopment() && Str::contains(request()->getHost(), '.ccdev.pro'))) {
            if (isValidGoogleMapKey(config('google.map'))) {
                return config('google.map');
            }
        }

        if (site('site_id')) {
            if (in_array(site('plan'), ['demo'])) {
                if (isValidGoogleMapKey(config('google.map'))) {
                    return config('google.map');
                }
            } elseif (site('site_id') && site('sand_box')) {
                if (!empty($key = trim((string) setting('google_map_api_key')))) {
                    if (isValidGoogleMapKey($key)) {
                        return $key;
                    }
                }

                $key = Str::endsWith(request()->getHost(), ['.cloudcart.com', '.cloudcart.net', '.ccdevel.com', '.ccdev.pro']) ? config('google.map') : null;
                if (isValidGoogleMapKey($key)) {
                    return $key;
                }
            } else {
                if (isValidGoogleMapKey(setting('google_map_api_key'))) {
                    return trim((string) setting('google_map_api_key'));
                }
            }
        }

        return null;
    }
}

if (!function_exists('hasGoogleMapKey')) {

    /**
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    function hasGoogleMapKey(): bool
    {
        return !!getGoogleMapKey();
    }
}

if (!function_exists('showPriceForUser')) {

    /**
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    function showPriceForUser(): bool
    {
        if (setting('show_prices_only_for_logged_users')) {
            return Auth::customerId() ? true : false;
        }

        return true;
    }
}

if (!function_exists('allowSiteByPlatform')) {

    /**
     * @return bool
     */
    function allowSiteByPlatform(): bool
    {
        // TODO: Fix this when there are workers on both platforms
        //        return true;
        if (platform() == sitePlatform()) {
            return true;
        }

        return false;
    }
}

if (!function_exists('sitePlatform')) {

    /**
     * @return null|string
     */
    function sitePlatform(): ?string
    {
        if (inDevelopment()) {
            return 'local';
        }

        if (!$site = site()) {
            return platform();
        }

        return $site->platform;
    }
}

if (!function_exists('sitePlatformByPlan')) {

    /**
     * @return null|string
     */
    function sitePlatformByPlan(): string
    {
        if (!$site = site()) {
            return 'hetzner-cloud';
        }

        return in_array($site->plan, ['unicorn', 'cc-pro', 'enterprise', 'enterprise-plus']) ? 'google-cloud' : 'hetzner-cloud';
    }
}

if (!function_exists('platformExternalIp')) {

    /**
     * @return string
     */
    function platformExternalIp()
    {
        return match (sitePlatform()) {
            'hetzner' => env('EXTERNAL_IP_HETZNER'),
            'hetzner-cloud' => env('EXTERNAL_IP_HETZNER_CLOUD'),
            default => env('EXTERNAL_IP_GOOGLE'),
        };
    }
}

if (!function_exists('platform')) {

    /**
     * @return null|string
     */
    function platform()
    {
        if (empty($platform = env('APP_PLATFORM')) && inDevelopment() !== false) {
            return 'local';
        }

        return $platform;
    }
}

if (!function_exists('getNamespaceFromRequestUri')) {

    function getNamespaceFromRequestUri(): string
    {
        if (PHP_SAPI == 'cli') {
            return 'builder';
        }

        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? null;
        if ($host) {
            $host = explode(':', (string) $host)[0];
        }

        switch ($host) {
            case 'marketplace.ccdev.info':
            case 'marketplace.cloudcart.com':
                return 'marketplace';
            case 'console.ccdev.info':
            case 'console.cloudcart.com':
            case 'console-hc.cloudcart.com':
                return 'console';
            case 'facebook.ccdev.info':
            case 'facebook.cloudcart.com':
                return 'facebook';
            case 'socialite.ccdev.info':
            case 'socialite.cloudcart.com':
                return 'socialite';
            case 'hooks.ccdev.info':
            case 'hooks.cloudcart.com':
                return 'hooks';
            case 'payments.ccdev.info':
            case 'payments.cloudcart.com':
                return 'payments';
            case 'cca.ccdev.info':
            case 'cca.cloudcart.com':
                return 'analytics';
            default:
                $parts = explode('/', ltrim($_SERVER['REQUEST_URI'] ?? '', '/'));
                return match (array_shift($parts)) {
                    'admin' => 'sitecp',
                    default => 'site',
                };
        }
    }
}

if (!function_exists('getNamespaceKernel')) {

    /**
     * @return string
     * @throws Exception
     */
    function getNamespaceKernel(): string
    {
        return match ($namespace = getNamespaceFromRequestUri()) {
            'console', 'builder' => Console\Http\Kernel::class,
            'marketplace' => MarketPlace\App\Http\Kernel::class,
            'facebook', 'socialite', 'hooks', 'payments' => Socialite\Http\Kernel::class,
            'analytics' => Analytics\Http\Kernel::class,
            'site', 'sitecp' => App\Http\Kernel::class,
            default => throw new Exception('Kernel for "' . $namespace . '" is not defined!'),
        };
    }
}

if (!function_exists('getNamespaceExceptionHandler')) {

    /**
     * @return string
     * @throws Exception
     */
    function getNamespaceExceptionHandler(): string
    {
        return match ($namespace = getNamespaceFromRequestUri()) {
            'console' => Console\Exceptions\Handler::class,
            'analytics' => Analytics\Exceptions\Handler::class,
            'payments', 'facebook', 'socialite', 'hooks' => App\Exceptions\LogHandler::class,
            'site', 'sitecp', 'builder' => App\Exceptions\Handler::class,
            'marketplace' => MarketPlace\App\Exceptions\Handler::class,
            default => throw new Exception('Kernel for "' . $namespace . '" is not defined!'),
        };
    }
}

if (!function_exists('modules')) {

    /**
     * @param null|string $key
     * @return null|App\Core\Modules|App\Core\Modules\Module
     */
    function modules(?string $key = null)
    {
        if (empty($key)) {
            return app(Modules::class);
        }

        return Module::get($key);
    }
}

if (!function_exists('has_module')) {

    /**
     * @param string $key
     * @return boolean
     */
    function has_module(string $key)
    {
        return Module::has($key);
    }
}

if (!function_exists('getMemoryLimit')) {
    /**
     * @param null $memoryLimit
     * @return int|string
     */
    function getMemoryLimit($memoryLimit = null): int
    {
        $memoryLimit = trim($memoryLimit ?: ini_get('memory_limit'));
        if ('-1' === $memoryLimit) {
            return -1;
        }

        $memoryLimit = strtolower($memoryLimit);
        $max = strtolower(ltrim($memoryLimit, '+'));
        if (str_starts_with($max, '0x')) {
            $max = intval($max, 16);
        } elseif (str_starts_with($max, '0')) {
            $max = intval($max, 8);
        } else {
            $max = (int)$max;
        }

        switch (substr($memoryLimit, -1)) {
            case 't':
                $max *= 1024;
                // no break
            case 'g':
                $max *= 1024;
                // no break
            case 'm':
                $max *= 1024;
                // no break
            case 'k':
                $max *= 1024;
        }

        return $max;
    }
}

if (!function_exists('inputSet')) {
    /**
     * @param $spec
     * @param null $value
     * @return array|\Illuminate\Http\Request|string
     */
    function inputSet($spec, $value = null)
    {
        $input = request()->request->all();
        if (is_array($spec)) {
            foreach ($spec as $key => $value) {
                \Illuminate\Support\Arr::set($input, $key, $value);
            }
        } else {
            \Illuminate\Support\Arr::set($input, $spec, $value);
        }

        request()->request->replace($input);
        return request();
    }
}

if (!function_exists('segment')) {

    /**
     * @param $number
     * @param null $default
     * @return string|null
     */
    function segment($number, $default = null)
    {
        return request()->segment($number, $default);
    }
}

if (!function_exists('assetic')) {

    /**
     * @param $bundle
     * @param $output
     * @return string
     */
    function assetic(string $bundle, string $output): string
    {
        $dev = inDevelopment() ? '' : '.min';
        return config('url.img') . 'sitecp/assetic/' . $output . '/' . $bundle . $dev . '.' . $output . '?' . app('last_build');
    }
}

if (!function_exists('themeScript')) {

    /**
     * @return string
     */
    function themeScript(): string
    {
        $dev = inDevelopment() ? '' : '.min';
        return sprintf('%sthemes/%s/js/scripts%s.js?%s', config('url.img'), site('template'), $dev, app('last_build'));
    }
}

if (!function_exists('styleScript')) {

    /**
     * @param bool $rtl
     * @return string
     */
    function styleScript($rtl = false): string
    {
        $dev = inDevelopment() ? '' : '.min';
        $dev .= $rtl ? '-rtl' : '';
        return sprintf('%sthemes/%s/css/styles%s.css?%s', config('url.img'), site('template'), $dev, app('last_build'));
    }
}

if (!function_exists('findMinDiff')) {

    /**
     * @param array|\Illuminate\Support\Collection $array
     * @return int
     */
    function findMinDiff($array): int|float
    {
        if ($array instanceof \Illuminate\Contracts\Support\Arrayable) {
            $array = $array->toArray();
        }

        if (!is_array($array)) {
            $array = [];
        }

        $arr = array_unique(array_map('floatval', $array));
        // Sort array in
        // non-decreasing order
        sort($arr);

        // Initialize difference
        // as infinite
        $diff = PHP_INT_MAX;

        //size of array
        $n = count($arr);

        // Find the min diff by
        // comparing adjacent
        // pairs in sorted array
        for ($i = 0; $i < $n - 1; $i++) {
            if ($arr[$i + 1] - $arr[$i] < $diff) {
                $diff = $arr[$i + 1] - $arr[$i];
            }
        }

        // Return min diff
        return $diff;
    }
}

if (!function_exists('dataCollect')) {

    function dataCollect(string $key, ?array $value = null)
    {
        static $collections = [];

        if ($value) {
            if (!array_key_exists($key, $collections)) {
                $collections[$key] = collect();
            }

            return $collections[$key]->push($value);
        } else {
            $result = $collections[$key] ?? collect();
            $collections[$key] = collect();
            return $result;
        }
    }
}


if (!function_exists('get_string_between')) {
    /**
     * @param $string
     * @param $start
     * @param $end
     * @return bool|string
     */
    function get_string_between($string, $start, $end): string
    {
        $string = ' ' . $string;
        $ini = strpos($string, (string) $start);
        if ($ini == 0) {
            return '';
        }

        $ini += strlen((string) $start);
        $len = strpos($string, (string) $end, $ini) - $ini;
        return substr($string, $ini, $len);
    }
}

if (!function_exists('custom_log')) {
    /**
     * @param null $filename
     * @param null $content
     */
    function custom_log($filename = null, $content = null): void
    {
        $filename = explode('.', basename((string) $filename));
        $filename = $filename[0];
        $filename = realpath(base_path()) . DIRECTORY_SEPARATOR . $filename . '.log';
        if (!$content || !$filename) {
            unlink($filename);
        } else {
            $content = $content . PHP_EOL . PHP_EOL;
            $fp = fopen($filename, 'w');
            fwrite($fp, $content);
            fclose($fp);
        }
    }
}

if (!function_exists('fast_collapse')) {
    /**
     * @param $data
     * @return array
     */
    function fast_collapse($data): array
    {
        if (!is_array($data)) {
            return [];
        }

        $data = array_filter(array_map(function ($values) {
            if ($values instanceof Collection) {
                return $values->all();
            } elseif (is_array($values)) {
                return $values;
            }

            return null;
        }, $data));

        $collapsed = [];
        foreach ($data as $group) {
            if (is_array($group)) {
                $collapsed = array_merge($collapsed, $group);
            }
        }

        return $collapsed;
    }
}

if (!function_exists('supportNewSpinner')) {
    /**
     * @return bool
     */
    function supportNewSpinner()
    {
        if (setting('action_after_add_to_cart') != 'stay_on_page') {
            return false;
        }

        return true;

        $themes = [
            'flair-diel', 'flair', 'braclinic', 'delicious', 'jeans-gameon',
            'virtuoso', 'knowledge', 'summer', 'echappe', 'journey', 'jeans',
            'properties-shadower', 'gameofdrones-living', 'knowledge-tmarket',
            'knowledge-freedom',

            //za lek stil
            'technoarena',
            'freshionista', 'zooland',
            'wonderland', 'one', 'bond', 'motion', 'themex'
        ];

        return in_array(site('template'), $themes);
    }
}

if (!function_exists('disabledUpload')) {
    /**
     * @return bool
     */
    function disabledUpload(): bool
    {
        return config('filesystems.cloud') == 'disabled';
    }
}

if (!function_exists('varExport')) {
    /**
     * PHP var_export() with short array syntax (square brackets) indented 2 spaces.
     *
     * NOTE: The only issue is when a string value has `=>\n[`, it will get converted to `=> [`
     * @link https://www.php.net/manual/en/function.var-export.php
     * @param $expression
     * @param bool $return
     * @return string|string[]|void
     */
    function varExport($expression, $return = false)
    {
        $export = var_export($expression, true);
        $patterns = [
            "/array \(/" => '[',
            "/^([ ]*)\)(,?)$/m" => '$1]$2',
            "/=>[ ]?\n[ ]+\[/" => '=> [',
            "/([ ]*)(\'[^\']+\') => ([\[\'])/" => '$1$2 => $3',
            "/\n\s+/" => "\n    ",
        ];
        $export = preg_replace(array_keys($patterns), array_values($patterns), $export);

        if ((bool)$return) {
            return $export;
        }

        echo $export;
    }
}

if (!function_exists('schemaOrganization')) {

    /**
     * https://raventools.com/blog/10-best-schema-codes-use-every-type-website/
     */
    function schemaOrganization(): string
    {
        $schematics = [];
        try {
            $phone = setting('site_phone');
            try {
                $phoneUtil = PhoneNumberUtil::getInstance();
                $numberProto = $phoneUtil->parse($phone, setting('country'));
                $phone = $phoneUtil->format($numberProto, PhoneNumberFormat::E164);
            } catch (Exception) {
            }

            $schema = Schema::organization();
            $schema->name(setting('site_name'))->url(site()->getSiteUrl())->logo(logo());
            if ($image = setting('og_image_url')) {
                $schema->image($image);
            }

            $schema->contactPoint(
                Schema::contactPoint()->telephone($phone)
            )->address(
                Schema::postalAddress()->streetAddress(setting('site_street'))
                    ->postalCode(setting('postal_code'))->addressCountry(setting('country'))
                    ->addressLocality(setting('site_city'))
            )->description(__('seo.home.description'));

            $schematics[] = $schema->toScript();
        } catch (Exception) {
        }

        //        try {
        //            $schema = Schema::webSite()->name(setting('site_name'))->url(site()->getSiteUrl())
        //                ->potentialAction(
        //                    Schema::searchAction()
        //                        ->target(Schema::entryPoint()->url(route("products.search") . '?query={query}'))
        //                        ->query('required name=query')
        //                );
        //
        //            $schematics[] = $schema->toScript();
        //        } catch (Exception $e) {}

        return implode("\n", $schematics);
    }
}

if (!function_exists('quantityNormalise')) {

    function quantityNormalise($quantity)
    {
        if (str_contains((string) $quantity, '.')) {
            $parts = explode('.', (string) $quantity, 2);
            if ($parts[1] > 0) {
                return $quantity;
            }

            return (int)$quantity;
        }

        return $quantity;
    }
}

if (!function_exists('switchLanguageCallback')) {

    function switchLanguageCallback(Closure $callback, $language)
    {
        if (!$language) {
            return $callback();
        }

        $app = Container::getInstance();

        $original = $app->getLocale();

        try {
            $app->setLocale($language);

            return $callback();
        } finally {
            $app->setLocale($original);
        }

        //        $app = app();
        //        $oldLocale = app()->getLocale();
        //        $translator = lang();
        //        $translator->setLoaded([]);
        //        $app->setLocale($language);
        //        $translator->setLocale($language);
        //
        //        $result = $callback();
        //
        //        $translator->setLoaded([]);
        //        $app->setLocale($oldLocale);
        //        $translator->setLocale($oldLocale);
        //
        //        return $result;
    }
}

if (!function_exists('isZora')) {
    function isZora(): bool
    {
        return in_array(site('site_id'), config('integration.zora.id', []));
    }
}

if (!function_exists('switchSiteCallback')) {
    function switchSiteCallback(Closure $callback, $site_id)
    {
        $site = site();
        if ((is_numeric($site_id) && $site->site_id == $site_id) || ($site_id instanceof \App\Models\Router\Site && $site->site_id == $site_id->site_id)) {
            return $callback();
        }

        if ($site_id instanceof \App\Models\Router\Site) {
            $newSite = $site_id;
        } else {
            $newSite = \App\Helper\Cache\RouterCache::getSiteById($site_id);
        }

        try {
            $newSite->bootDB();
            return $callback();
            //        } catch (Exception $e) {
            //            dd($e->getMessage(), $e->getLine());
        } finally {
            $site->bootDB();
        }
    }
}

if (!function_exists('siteDateTime')) {
    function siteDateTime($date): ?string
    {
        $format = DateTimeFormat::getFormatByTemplate();

        if (!$date) {
            return null;
        }

        if (!($date instanceof Carbon)) {
            $date = Carbon::createFromTimestamp(\Carbon\Carbon::parse((string) $date)->getTimestamp());
        }

        return $date->format($format);
    }
}

if (!function_exists('siteDate')) {
    function siteDate($date): ?string
    {
        $format = DateTimeFormat::getFormatByTemplate('{date}');

        if (!$date) {
            return null;
        }

        if (!($date instanceof Carbon)) {
            $date = Carbon::createFromTimestamp(\Carbon\Carbon::parse((string) $date)->getTimestamp());
        }

        return $date->format($format);
    }
}

if (!function_exists('siteTime')) {
    function siteTime($date): ?string
    {
        $format = DateTimeFormat::getFormatByTemplate('{time}');

        if (!$date) {
            return null;
        }

        if (!($date instanceof Carbon)) {
            $date = Carbon::createFromTimestamp(\Carbon\Carbon::parse((string) $date)->getTimestamp());
        }

        return $date->format($format);
    }
}

if (!function_exists('array_map_key')) {

    function array_map_key(callable $closure, array $array): array
    {
        return array_combine(array_map($closure, array_keys($array)), $array);
    }
}

if (!function_exists('data_get_multiple')) {

    function data_get_multiple($target, $key)
    {
        $key = is_array($key) ? $key : [$key];
        $result = [];

        foreach ($key as $k) {
            $value = data_get($target, $k, new stdClass());
            if (!($value instanceof stdClass)) {
                data_set($result, $k, $value);
            }
        }

        return $result;
    }
}

if (!function_exists('array_back_empty_trim')) {

    /**
     * @return mixed[]
     */
    function array_back_empty_trim(array $value, ?Closure $callback = null): array
    {
        $tmp = [];

        $callback = $callback ?: (fn($v, $k): bool => !empty($v));

        $append = false;
        foreach (array_reverse($value) as $k => $v) {
            if (!$append && $callback($v, $k)) {
                $append = true;
            }

            if ($append) {
                $tmp[$k] = $v;
            }
        }

        $value = array_reverse($tmp);

        return $value;
    }
}

if (!function_exists('array_diff_recursive')) {

    /**
     * @return mixed[]
     * @param mixed $aArray1
     * @param mixed $aArray2
     */
    function array_diff_recursive($aArray1, $aArray2): array
    {
        $aReturn = [];

        foreach ($aArray1 as $mKey => $mValue) {
            if (array_key_exists($mKey, (array)$aArray2)) {
                if (is_array($mValue)) {
                    $aRecursiveDiff = array_diff_recursive($mValue, $aArray2[$mKey]);
                    if ($aRecursiveDiff !== []) {
                        $aReturn[$mKey] = $aRecursiveDiff;
                    }
                } else {
                    if ($mValue != $aArray2[$mKey]) {
                        $aReturn[$mKey] = $mValue;
                    }
                }
            } else {
                $aReturn[$mKey] = $mValue;
            }
        }

        return $aReturn;
    }
}

if (!function_exists('array_find')) {

    function array_find(array $array, string $key)
    {
        foreach ($array as $k => $v) {
            if ($k === $key) {
                return $v;
            } elseif (is_array($v)) {
                return array_find($v, $key);
            }
        }

        return;
    }
}

if (!function_exists('array_extract')) {

    function array_extract(array $array, string $key): array
    {
        $return = [];
        if (array_key_exists($key, $array)) {
            $return[] = $array[$key];
        }

        foreach ($array as $k => $v) {
            if (is_array($v)) {
                $return = array_merge($return, array_extract($v, $key));
            } elseif ($k === $key) {
                $return[] = $v;
            }
        }

        return array_unique($return);
    }
}

if (!function_exists('money')) {

    function money(?float $amount, ?string $currency = null, ?string $language = null, ?int $coinsPad = null): string
    {
        return Format::money(
            is_numeric($amount) ? $amount : 0,
            true,
            $currency,
            $coinsPad,
            $language
        );
    }
}

if (!function_exists('moneyInput')) {

    function moneyInput(string|float|int|null $amount = null, ?string $currency = null, ?string $language = null, ?int $coinsPad = null): string|float|int|null
    {
        return (string) Format::moneyInput(
            is_numeric($amount) ? $amount : 0,
            $currency,
            $coinsPad,
            $language
        );
    }
}

if (!function_exists('moneyFloat')) {

    function moneyFloat(string|float|int|null $amount = null, ?string $currency = null, ?string $language = null, ?int $coinsPad = null): float
    {
        return floatval(moneyInput($amount, $currency, $language, $coinsPad));
    }
}

if (!function_exists('percent')) {

    function percent(?float $percent): string
    {
        return Format::percent(
            is_numeric($percent) ? $percent : 0
        );
    }
}

if (!function_exists('percentInput')) {

    function percentInput(?float $percent): string
    {
        return Format::percent(
            is_numeric($percent) ? $percent : 0,
            true
        );
    }
}

if (!function_exists('percentFloat')) {

    function percentFloat(?float $percent): float
    {
        return floatval(percentInput($percent));
    }
}

if (!function_exists('weight')) {

    function weight(?float $weight): string
    {
        return Weight::format(
            is_numeric($weight) ? $weight : 0
        );
    }
}

if (!function_exists('weightInput')) {

    function weightInput(?float $weight): string
    {
        return Weight::input(
            is_numeric($weight) ? $weight : 0
        );
    }
}

if (!function_exists('weightFloat')) {

    function weightFloat(?float $weight): float
    {
        return floatval(weightInput($weight));
    }
}

/**
 * $unit = K - kilometers, N - Nautical Miles, M - Miles
 */
if (!function_exists('geoDistance')) {
    function geoDistance($lat1, $lon1, $lat2, $lon2, $unit): int|string
    {
        if (($lat1 == $lat2) && ($lon1 == $lon2)) {
            return 0;
        } else {
            $theta = $lon1 - $lon2;
            $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
            $dist = acos($dist);
            $dist = rad2deg($dist);
            $miles = $dist * 60 * 1.1515;
            $unit = strtoupper((string) $unit);

            return match (strtoupper($unit)) {
                "N" => number_format(($miles * 0.8684), 2, '.', ''),
                "M", "IMPERIAL" => number_format($miles, 2, '.', ''),
                default => number_format(($miles * 1.609344), 2, '.', ''),
            };
        }
    }
}

if (!function_exists('array_before')) {

    /**
     * @param string $key
     * @param array $array
     * @return array
     */
    function array_before(string $key, array $array): array
    {
        $position = array_search($key, array_keys($array), true);
        if ($position === false) {
            return [];
        }

        return array_slice($array, 0, $position);
    }
}

if (!function_exists('getAspectRatio')) {

    /**
     * @param int $width
     * @param int $height
     * @return string
     */
    function getAspectRatio(int $width, int $height): string
    {
        // search for greatest common divisor
        $greatestCommonDivisor = static function ($width, $height) use (&$greatestCommonDivisor) {
            return ($width % $height) ? $greatestCommonDivisor($height, $width % $height) : $height;
        };

        $divisor = $greatestCommonDivisor($width, $height);

        return ($width / $divisor) . '/' . ($height / $divisor);
    }
}

if (!function_exists('isLightHouseRequest')) {

    /**
     * @return bool
     */
    function isLightHouseRequest(): bool
    {
        return request()->has('___clh');
    }
}

if (!function_exists('isLightHouseAgent')) {

    /**
     * @return bool
     */
    function isLightHouseAgent(): bool
    {
        return str_contains(strtolower((string) request()->userAgent()), 'lighthouse') ||
            str_contains(strtolower((string) request()->userAgent()), 'gtmetrix') ||
            str_contains(strtolower((string) request()->userAgent()), 'ptst');
    }
}

if (!function_exists('lightHouseResponseReplace')) {

    /**
     * @param $response
     * @return void
     */
    function lightHouseResponseReplace($response): void
    {
        if (!($response instanceof \Illuminate\Http\Response)) {
            return;
        }

        if (!is_string($content = $response->getContent())) {
            return;
        }

        //        $content = preg_replace_callback('#<img([^>]*)src="([^"]*)"([^>]*)>#imUs', function($match) {
        //            if(strpos($match[0], 'loading=') === false) {
        //                $match[0] = str_replace('<img ', '<img loading="lazy" ', $match[0]);
        //            }
        //            return $match[0];
        //        }, $content);

        //        $content = preg_replace_callback('#<link([^>]*)href="([^"]*)\.css([^"]*)"#imUs', function($match) {
        //            return str_replace('<link ', '<link async="async" ', $match[0]);
        //        }, $content);
        //
        $content = preg_replace_callback('#<link([^>]*)href="https://fonts.googleapis.com/css\?family=([^"]*)"#imUs', fn($match): string => str_replace('<link ', '<link async="async" ', $match[0]), (string) $content);

        $allowRemove = \App\Helper\HtmlCache::isBot();
        if (!$allowRemove) {
            $response->setContent($content);
            return;
        }

        $allowedScripts = [];
        $allowedScriptsCheck = ['window.dataLayer', 'var cc_page_data', 'application/ld+json'];
        $content = preg_replace_callback('#<script([^>]*)>(.*)</script>#imUs', function (array $match) use (&$allowedScripts, $allowedScriptsCheck): string {
            if (Str::contains($match[0], $allowedScriptsCheck)) {
                $allowedScripts[] = $match[0];
            }

            return '';
        }, (string) $content);

        $content = str_replace(
            [
                '_slider js-slider',
                '</head>'
            ],
            [
                '_slider js-slider slider-loaded',
                implode("\n", $allowedScripts) . '</head>'
            ],
            (string) $content
        );

        $content = preg_replace_callback('#<button\s+class="_carousel-button([^"]*)"([^>]*)>(.*)</button>#imUs', fn($match): string => '', (string) $content);

        $content = preg_replace_callback('#<div\s+id="back_to_top"([^>]*)>(.*)</div>#imUs', fn($match): string => '', (string) $content);

        $style = '<style>*:not(.fal, .far, .fas, .fa) { font-family: sans-serif !important; }</style>';
        //        $style = '<style>* { font-family: sans-serif !important; }</style>';
        $script = \App\Helper\Html::scriptFile(
            sprintf(
                '%ssite/js/glh/lh%s.js?%s',
                trim((string) config('url.img')),
                inDevelopment() ? '' : '.min',
                app('last_build')
            ),
            [
                'async' => true
            ]
        );

        //        $content = preg_replace_callback('#<link([^>]*)href="([^"]*)(builder/global/fontawesome-pro/css|themes/_global/css/checkout\.)([^"]*)\.css([^"]*)"([^>]*)>#imUs', function($match) {
        //            return '';
        //        }, $content);

        $response->setContent(
            str_replace(['</head>', '</body>'], [$style . '</head>', $script . '</body>'], (string) $content)
        );

    }
}

if (!function_exists('array_dot_keys')) {

    /**
     * @param string $key
     * @param array $array
     * @param mixed $prepend
     * @return array
     */
    function array_dot_keys($array, string $prepend = ''): array
    {
        $results = [];

        foreach ($array as $key => $value) {
            if (is_array($value) && !empty($value)) {
                $results = array_merge($results, array_dot_keys($value, $prepend . $key . '.'));
            } else {
                $results[$prepend . $value] = $prepend . $value;
            }
        }

        return array_unique(array_values($results));
    }
}

if (!function_exists('isArraySequential')) {

    /**
     * @param array $array
     * @return bool
     */
    function isArraySequential($array): bool
    {
        if (!is_array($array)) {
            return false;
        }

        if (count($array) == 0) {
            return true;
        }

        $keys = array_keys($array);

        foreach ($keys as $key) {
            if (!is_numeric($key) || !ctype_digit($key)) {
                return false;
            }
        }

        return true;
    }
}

if (!function_exists('isArrayAssociative')) {

    /**
     * @param array $array
     * @return bool
     */
    function isArrayAssociative($array): bool
    {
        if (!is_array($array)) {
            return false;
        }

        if (count($array) == 0) {
            return true;
        }

        $keys = array_keys($array);

        foreach ($keys as $key) {
            if (!is_numeric($key) || !ctype_digit($key)) {
                return true;
            }
        }

        return false;
    }
}

if (!function_exists('languages')) {

    /**
     * @return Collection
     */
    function languages(): Collection
    {
        $langCodes = config('languages.locales', []);
        $languages = [];
        foreach ($langCodes as $code => $locale) {
            $languages[] = [
                'id' => $code,
                'name' => Str::ucfirst(locale_get_display_language($locale, $locale))
            ];
        }

        return collect($languages);
    }
}

if (!function_exists('mixViteVueSiteCp')) {
    /**
     * @param string $path
     * @return HtmlString
     * @throws Exception
     */
    function mixViteVueSiteCp(string $path): HtmlString
    {
        if (!Str::startsWith($path, '/')) {
            $path = '/' . $path;
        }

        $localPath = '/public/admin/' . (inDevelopment() ? 'vuejs-dev' : 'vuejs') . $path;

        $fileExists = base_path($localPath);

        if (!file_exists($fileExists)) {
            throw new Exception(sprintf('Unable to locate Mix file: %s.', $fileExists));
        }

        return new HtmlString(config('url.siteCpVueJs') . $path . '?' . now()->timestamp);
    }
}


if (!function_exists('mixVueSiteCp')) {
    /**
     * Get the path to a versioned Mix file.
     *
     * @param string $path
     * @return \Illuminate\Support\HtmlString|string
     *
     * @throws \Exception
     */
    function mixVueSiteCp(string $path): \Illuminate\Support\HtmlString
    {
        static $manifests = [];

        if (!Str::startsWith($path, '/')) {
            $path = '/' . $path;
        }

        $manifestPath = base_path('/public/admin/' . (inDevelopment() ? 'vuejs-dev' : 'vuejs') . '/mix-manifest.json');

        if (!isset($manifests[$manifestPath])) {
            if (!file_exists($manifestPath)) {
                throw new Exception('The Mix manifest does not exist.');
            }

            $manifests[$manifestPath] = json_decode(file_get_contents($manifestPath), true);
        }

        $manifest = $manifests[$manifestPath];

        if (!empty($manifest) && !isset($manifest[$path])) {
            throw new Exception(sprintf('Unable to locate Mix file: %s.', $path));
        }

        return new HtmlString(config('url.siteCpVueJs') . $manifest[$path]);
    }
}

if (!function_exists('stringReverse')) {
    /**
     * Get the path to a versioned Mix file.
     *
     * @param null $validate
     * @param int $length
     * @return mixed
     */
    function stringReverse($validate = null, $length = 10): bool|string
    {
        if ($validate) {
            if (!is_string($validate) || !Str::contains($validate, ':')) {
                return false;
            }

            $parts = explode(':', $validate, 2);

            return $parts[0] == strrev($parts[1]);
        }

        $rand = Str::random($length);

        return sprintf('%s:%s', $rand, strrev($rand));
    }
}

if (!function_exists('translationsHash')) {
    /**
     * @param string $key
     * @return string
     */
    function translationsHash(string $key): string
    {
        $language = site('language_cp', site('language'));
        return TranslationHelper::getForGroup($language, $key);
    }
}

if (!function_exists('array_first__')) {
    /**
     * Return the first element in an array passing a given truth test.
     *
     * @param array $array
     * @param callable|null $callback
     * @param mixed|null $default
     * @return mixed
     */
    function array_first__(array $array, callable|null $callback = null, mixed $default = null): mixed
    {
        return Arr::first($array, $callback, $default);
    }
}

if (!function_exists('format_bytes')) {
    function format_bytes($bytes, $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= 1024 ** $pow;

        return round($bytes, $precision) . ' ' . $units[$pow];
    }
}

if (!function_exists('guessRealise')) {
    function guessRealise(): string
    {
        if (inDevelopment()) {
            return 'dev';
        }

        //    if(env('APP_PLATFORM') == 'hetzner-cloud') {
        //        return 'builder-hc@' . (env('BUILD_TAG') ? : ($_SERVER['BUILD_TAG'] ?? 'none'));
        //    }
        //    if(env('APP_PLATFORM') == 'google-cloud') {
        //        return 'builder-gcp@' . (env('BUILD_TAG') ? : ($_SERVER['BUILD_TAG'] ?? 'none'));
        //    }

        return 'builder@' . (env('BUILD_TAG') ?: ($_SERVER['BUILD_TAG'] ?? 'none'));
    }
}

if (!function_exists('str_lower')) {
    function str_lower($string): string
    {
        return Str::lower($string);
    }
}

if (!function_exists('str_upper')) {
    function str_upper($string): string
    {
        return Str::upper($string);
    }
}

if (!function_exists('is_windows_os')) {
    function is_windows_os(): bool
    {
        return (strtoupper(substr(php_uname(), 0, 7)) === 'WINDOWS');
    }
}

if (!function_exists('isAlpin')) {
    function isAlpin(): bool
    {
        static $isAlpin;

        if(isset($isAlpin)) {
            return $isAlpin;
        }

        if(str_contains(strtolower(php_uname()), 'alpine')) {
            return $isAlpin = true;
        }

        $osInfo = @file_get_contents('/etc/os-release');
        return $isAlpin = str_contains((string)$osInfo, 'Alpine');
    }
}

if (!function_exists('currency')) {
    function currency($amount, string $from, string $to): float
    {
        return \App\Models\Gateway\Currency::convert(floatval($amount), $from, $to);
    }
}

if (!function_exists('protected_path')) {

    /**
     * @param string $path
     * @return string
     */
    function protected_path(string $path = ''): string
    {
        return base_path('protected/' . $path);
    }
}

if (!function_exists('engine_one_path')) {

    /**
     * @param string $path
     * @return string
     */
    function engine_one_path(string $path = ''): string
    {
        return protected_path('templates/' . $path);
    }
}

if (!function_exists('engine_two_path')) {

    /**
     * @param string $path
     * @return string
     */
    function engine_two_path(string $path = ''): string
    {
        return base_path('themes/' . $path);
    }
}

if (!function_exists('isLiquidEngine')) {

    /**
     * @return bool
     */
    function isLiquidEngine(): bool
    {
        return site('theme.engine') == 'liquid';
    }
}

if (!function_exists('isSmartyEngine')) {

    /**
     * @return bool
     */
    function isSmartyEngine(): bool
    {
        return site('theme.engine') == 'smarty';
    }
}

if (!function_exists('getLiquidTheme')) {

    /**
     * @return string
     */
    function getLiquidTheme(): string
    {
//        $siteTemplate = 'flair'; // legacy from old project, default for smarty
//        $siteTemplate = 'dawn'; // default shopify theme - https://github.com/Shopify/dawn
          $siteTemplate = 'beauty';
//        $siteTemplate = 'natureface-liquid';
//        $siteTemplate = 'furniture';
//        $siteTemplate = 'nitro';

        return site()->template;
        return $siteTemplate;
    }
}

if (!function_exists('forgetRoute')) {
    /**
     * @param string|array $name
     * @param string $compare
     * @return null|string|bool
     */
    function forgetRoute(string|array $name, string $compare = 'as'): bool|string|null
    {
        if(!is_array($name)) {
            $name = explode(' ', $name);
        }

        $args = array_filter($name);

        $collection = new \Illuminate\Routing\RouteCollection();
        array_map(function(\Illuminate\Routing\Route $route) use($collection, $args, $compare) {
            if(($name = $route->getAction($compare)) && Str::is($args, $name)) {
                return null;
            }

            $collection->add($route);
        }, Route::getRoutes()->getIterator()->getArrayCopy());
        Route::setRoutes($collection);
        URL::setRoutes($collection);

        return true;
    }
}
