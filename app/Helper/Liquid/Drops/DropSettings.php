<?php

declare(strict_types=1);

namespace App\Helper\Liquid\Drops;

use Liquid\Drop;

class DropSettings extends Drop
{
    public function logo(): string
    {
        return $this->_settings(__FUNCTION__, '');
    }

    public function logo_width(): int
    {
        return $this->_settings(__FUNCTION__, 230);
    }

    public function type_header_font(): string
    {
        return $this->_settings(__FUNCTION__, '');
    }

    public function heading_scale(): int
    {
        return $this->_settings(__FUNCTION__, 100);
    }

    public function type_body_font(): string
    {
        return $this->_settings(__FUNCTION__, 'assistant_n4');
    }

    public function body_scale(): int
    {
        return $this->_settings(__FUNCTION__, 100);
    }

    public function page_width(): int
    {
        return intval($this->_settings(__FUNCTION__, 1500));
    }

    public function spacing_sections(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function spacing_grid_horizontal(): int
    {
        return $this->_settings(__FUNCTION__, 8);
    }

    public function spacing_grid_vertical(): int
    {
        return $this->_settings(__FUNCTION__, 8);
    }

    public function animations_reveal_on_scroll(): bool
    {
        return $this->_settings(__FUNCTION__, true);
    }

    public function animations_hover_elements(): string
    {
        return $this->_settings(__FUNCTION__, 'none');
    }

    public function buttons_border_thickness(): int
    {
        return $this->_settings(__FUNCTION__, 1);
    }

    public function buttons_border_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 100);
    }

    public function buttons_radius(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function buttons_shadow_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function buttons_shadow_horizontal_offset(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function buttons_shadow_vertical_offset(): int
    {
        return $this->_settings(__FUNCTION__, 4);
    }

    public function buttons_shadow_blur(): int
    {
        return $this->_settings(__FUNCTION__, 5);
    }

    public function variant_pills_border_thickness(): int
    {
        return $this->_settings(__FUNCTION__, 1);
    }

    public function variant_pills_border_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 55);
    }

    public function variant_pills_radius(): int
    {
        return $this->_settings(__FUNCTION__, 40);
    }

    public function variant_pills_shadow_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function variant_pills_shadow_horizontal_offset(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function variant_pills_shadow_vertical_offset(): int
    {
        return $this->_settings(__FUNCTION__, 4);
    }

    public function variant_pills_shadow_blur(): int
    {
        return $this->_settings(__FUNCTION__, 5);
    }

    public function inputs_border_thickness(): int
    {
        return $this->_settings(__FUNCTION__, 1);
    }

    public function inputs_border_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 55);
    }

    public function inputs_radius(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function inputs_shadow_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function inputs_shadow_horizontal_offset(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function inputs_shadow_vertical_offset(): int
    {
        return $this->_settings(__FUNCTION__, 4);
    }

    public function inputs_shadow_blur(): int
    {
        return $this->_settings(__FUNCTION__, 5);
    }

    public function card_style(): string
    {
        return $this->_settings(__FUNCTION__, 'standard');
    }

    public function card_image_padding(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function card_text_alignment(): string
    {
        return $this->_settings(__FUNCTION__, 'left');
    }

    public function card_color_scheme(): string
    {
        return $this->_settings(__FUNCTION__, 'scheme-1');
    }

    public function card_border_thickness(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function card_border_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 10);
    }

    public function card_corner_radius(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function card_shadow_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function card_shadow_horizontal_offset(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function card_shadow_vertical_offset(): int
    {
        return $this->_settings(__FUNCTION__, 4);
    }

    public function card_shadow_blur(): int
    {
        return $this->_settings(__FUNCTION__, 5);
    }

    public function collection_card_style(): string
    {
        return $this->_settings(__FUNCTION__, 'standard');
    }

    public function collection_card_image_padding(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function collection_card_text_alignment(): string
    {
        return $this->_settings(__FUNCTION__, 'left');
    }

    public function collection_card_color_scheme(): string
    {
        return $this->_settings(__FUNCTION__, 'scheme-2');
    }

    public function collection_card_border_thickness(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function collection_card_border_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 10);
    }

    public function collection_card_corner_radius(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function collection_card_shadow_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function collection_card_shadow_horizontal_offset(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function collection_card_shadow_vertical_offset(): int
    {
        return $this->_settings(__FUNCTION__, 4);
    }

    public function collection_card_shadow_blur(): int
    {
        return $this->_settings(__FUNCTION__, 5);
    }

    public function blog_card_style(): string
    {
        return $this->_settings(__FUNCTION__, 'standard');
    }

    public function blog_card_image_padding(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function blog_card_text_alignment(): string
    {
        return $this->_settings(__FUNCTION__, 'left');
    }

    public function blog_card_color_scheme(): string
    {
        return $this->_settings(__FUNCTION__, 'scheme-2');
    }

    public function blog_card_border_thickness(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function blog_card_border_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 10);
    }

    public function blog_card_corner_radius(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function blog_card_shadow_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function blog_card_shadow_horizontal_offset(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function blog_card_shadow_vertical_offset(): int
    {
        return $this->_settings(__FUNCTION__, 4);
    }

    public function blog_card_shadow_blur(): int
    {
        return $this->_settings(__FUNCTION__, 5);
    }

    public function text_boxes_border_thickness(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function text_boxes_border_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 10);
    }

    public function text_boxes_radius(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function text_boxes_shadow_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function text_boxes_shadow_horizontal_offset(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function text_boxes_shadow_vertical_offset(): int
    {
        return $this->_settings(__FUNCTION__, 4);
    }

    public function text_boxes_shadow_blur(): int
    {
        return $this->_settings(__FUNCTION__, 5);
    }

    public function media_border_thickness(): int
    {
        return $this->_settings(__FUNCTION__, 1);
    }

    public function media_border_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 5);
    }

    public function media_radius(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function media_shadow_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function media_shadow_horizontal_offset(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function media_shadow_vertical_offset(): int
    {
        return $this->_settings(__FUNCTION__, 4);
    }

    public function media_shadow_blur(): int
    {
        return $this->_settings(__FUNCTION__, 5);
    }

    public function popup_border_thickness(): int
    {
        return $this->_settings(__FUNCTION__, 1);
    }

    public function popup_border_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 10);
    }

    public function popup_corner_radius(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function popup_shadow_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 50);
    }

    public function popup_shadow_horizontal_offset(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function popup_shadow_vertical_offset(): int
    {
        return $this->_settings(__FUNCTION__, 4);
    }

    public function popup_shadow_blur(): int
    {
        return $this->_settings(__FUNCTION__, 5);
    }

    public function drawer_border_thickness(): int
    {
        return $this->_settings(__FUNCTION__, 1);
    }

    public function drawer_border_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 10);
    }

    public function drawer_shadow_opacity(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function drawer_shadow_horizontal_offset(): int
    {
        return $this->_settings(__FUNCTION__, 0);
    }

    public function drawer_shadow_vertical_offset(): int
    {
        return $this->_settings(__FUNCTION__, 4);
    }

    public function drawer_shadow_blur(): int
    {
        return $this->_settings(__FUNCTION__, 5);
    }

    public function badge_position(): string
    {
        return $this->_settings(__FUNCTION__, 'top left');
    }

    public function badge_corner_radius(): int
    {
        return $this->_settings(__FUNCTION__, 8);
    }

    public function sale_badge_color_scheme(): string
    {
        return $this->_settings(__FUNCTION__, 'scheme-1f945a2c-5842-41e9-b662-2dcdae755a02');
    }

    public function sold_out_badge_color_scheme(): string
    {
        return $this->_settings(__FUNCTION__, 'scheme-3');
    }

    public function brand_headline(): string
    {
        return $this->_settings(__FUNCTION__, '');
    }

    public function brand_description(): string
    {
        return $this->_settings(__FUNCTION__, '<p></p>');
    }

    public function brand_image_width(): int
    {
        return $this->_settings(__FUNCTION__, 100);
    }

    public function social_facebook_link(): string
    {
        return $this->_settings(__FUNCTION__, '#');
    }

    public function social_instagram_link(): string
    {
        return $this->_settings(__FUNCTION__, 'https://www.instagram.com/?url=https%3A%2F%2Fbeauty-products-theme.myshopify.com');
    }

    public function social_youtube_link(): string
    {
        return $this->_settings(__FUNCTION__, '');
    }

    public function social_tiktok_link(): string
    {
        return $this->_settings(__FUNCTION__, '');
    }

    public function social_twitter_link(): string
    {
        return $this->_settings(__FUNCTION__, '#');
    }

    public function social_snapchat_link(): string
    {
        return $this->_settings(__FUNCTION__, '');
    }

    public function social_pinterest_link(): string
    {
        return $this->_settings(__FUNCTION__, '#');
    }

    public function social_tumblr_link(): string
    {
        return $this->_settings(__FUNCTION__, '');
    }

    public function social_vimeo_link(): string
    {
        return $this->_settings(__FUNCTION__, '');
    }

    public function predictive_search_enabled(): bool
    {
        return $this->_settings(__FUNCTION__, true);
    }

    public function predictive_search_show_vendor(): bool
    {
        return $this->_settings(__FUNCTION__, false);
    }

    public function predictive_search_show_price(): bool
    {
        return $this->_settings(__FUNCTION__, false);
    }

    public function currency_code_enabled(): bool
    {
        return $this->_settings(__FUNCTION__, true);
    }

    public function cart_type(): string
    {
        return $this->_settings(__FUNCTION__, 'notification');
    }

    public function show_vendor(): bool
    {
        return $this->_settings(__FUNCTION__, false);
    }

    public function show_cart_note(): bool
    {
        return $this->_settings(__FUNCTION__, false);
    }

    public function cart_drawer_collection(): string
    {
        return $this->_settings(__FUNCTION__, '');
    }

    public function cart_color_scheme(): string
    {
        return $this->_settings(__FUNCTION__, 'scheme-1');
    }

    public function sections(): array
    {
        return $this->_settings(
            __FUNCTION__,
            [
                'main-password-header' => [
                    'type' => 'main-password-header',
                    'settings' => [
                        'color_scheme' => 'scheme-1',
                    ],
                ],
                'main-password-footer' => [
                    'type' => 'main-password-footer',
                    'settings' => [
                        'color_scheme' => 'scheme-1',
                    ],
                ],
            ]
        );
    }

    public function content_for_index(): array
    {
        return $this->_settings(__FUNCTION__, []);
    }

    public function blocks(): array
    {
        return $this->_settings(
            __FUNCTION__,
            [
                '3782161271425840822' => [
                    'type' => 'shopify://apps/ns-image-swatch-variant-option/blocks/nestscale_product_variant/53eb5ed4-3892-414c-808b-3ea84516b10d',
                    'disabled' => true,
                    'settings' => [
                    ],
                ],
                '4095653568973655092' => [
                    'type' => 'shopify://apps/mag-product-reviews/blocks/app-embed/0cf5caa0-e138-46b8-999b-520d16633e4c',
                    'disabled' => true,
                    'settings' => [
                        'hide_star' => true,
                        'collection_target_class' => '',
                    ],
                ],
                '6933373160360723723' => [
                    'type' => 'shopify://apps/swishlist-simple-wishlist/blocks/wishlist_settings/bfcb80f2-f00d-42ae-909a-9f00c726327b',
                    'disabled' => true,
                    'settings' => [
                    ],
                ],
            ]
        );
    }

    public function color_schemes(): array
    {
        return $this->_settings(
            __FUNCTION__,
            [
                'scheme-1' => [
                    'settings' => [
                        'background' => '#ffffff',
                        'background_gradient' => '',
                        'text' => '#121212',
                        'button' => '#121212',
                        'button_label' => '#ffffff',
                        'secondary_button_label' => '#121212',
                        'shadow' => '#121212',
                    ],
                ],
                'scheme-2' => [
                    'settings' => [
                        'background' => '#f3f3f3',
                        'background_gradient' => '',
                        'text' => '#121212',
                        'button' => '#121212',
                        'button_label' => '#f3f3f3',
                        'secondary_button_label' => '#121212',
                        'shadow' => '#121212',
                    ],
                ],
                'scheme-3' => [
                    'settings' => [
                        'background' => '#242833',
                        'background_gradient' => '',
                        'text' => '#ffffff',
                        'button' => '#ffffff',
                        'button_label' => '#000000',
                        'secondary_button_label' => '#ffffff',
                        'shadow' => '#121212',
                    ],
                ],
                'scheme-4' => [
                    'settings' => [
                        'background' => '#121212',
                        'background_gradient' => '',
                        'text' => '#ffffff',
                        'button' => '#ffffff',
                        'button_label' => '#121212',
                        'secondary_button_label' => '#ffffff',
                        'shadow' => '#121212',
                    ],
                ],
                'scheme-5' => [
                    'settings' => [
                        'background' => '#334fb4',
                        'background_gradient' => '',
                        'text' => '#ffffff',
                        'button' => '#ffffff',
                        'button_label' => '#334fb4',
                        'secondary_button_label' => '#ffffff',
                        'shadow' => '#121212',
                    ],
                ],
                'scheme-a6df4e54-e740-469e-a7d4-a618a3db0bbe' => [
                    'settings' => [
                        'background' => '#21332b',
                        'background_gradient' => '',
                        'text' => '#ffffff',
                        'button' => '#121212',
                        'button_label' => '#ffffff',
                        'secondary_button_label' => '#121212',
                        'shadow' => '#121212',
                    ],
                ],
                'scheme-2090b5fc-ecb7-4d9d-97a5-973ebcb68a72' => [
                    'settings' => [
                        'background' => '#373737',
                        'background_gradient' => '',
                        'text' => '#121212',
                        'button' => '#121212',
                        'button_label' => '#ffffff',
                        'secondary_button_label' => '#121212',
                        'shadow' => '#121212',
                    ],
                ],
                'scheme-1f945a2c-5842-41e9-b662-2dcdae755a02' => [
                    'settings' => [
                        'background' => '#ffc300',
                        'background_gradient' => '',
                        'text' => '#121212',
                        'button' => '#121212',
                        'button_label' => '#ffffff',
                        'secondary_button_label' => '#121212',
                        'shadow' => '#121212',
                    ],
                ],
                'scheme-4539c66b-6e4d-4609-96cb-4475989d82f0' => [
                    'settings' => [
                        'background' => '#f6f6f6',
                        'background_gradient' => '',
                        'text' => '#121212',
                        'button' => '#121212',
                        'button_label' => '#ffffff',
                        'secondary_button_label' => '#121212',
                        'shadow' => '#121212',
                    ],
                ],
            ]
        );
    }

    /**
     * {@inheritDoc}
     */
    public function toArray(): array
    {
        return [
            'logo' => $this->logo(),
            'logo_width' => $this->logo_width(),
            'type_header_font' => $this->type_header_font(),
            'heading_scale' => $this->heading_scale(),
            'type_body_font' => $this->type_body_font(),
            'body_scale' => $this->body_scale(),
            'page_width' => $this->page_width(),
            'spacing_sections' => $this->spacing_sections(),
            'spacing_grid_horizontal' => $this->spacing_grid_horizontal(),
            'spacing_grid_vertical' => $this->spacing_grid_vertical(),
            'animations_reveal_on_scroll' => $this->animations_reveal_on_scroll(),
            'animations_hover_elements' => $this->animations_hover_elements(),
            'buttons_border_thickness' => $this->buttons_border_thickness(),
            'buttons_border_opacity' => $this->buttons_border_opacity(),
            'buttons_radius' => $this->buttons_radius(),
            'buttons_shadow_opacity' => $this->buttons_shadow_opacity(),
            'buttons_shadow_horizontal_offset' => $this->buttons_shadow_horizontal_offset(),
            'buttons_shadow_vertical_offset' => $this->buttons_shadow_vertical_offset(),
            'buttons_shadow_blur' => $this->buttons_shadow_blur(),
            'variant_pills_border_thickness' => $this->variant_pills_border_thickness(),
            'variant_pills_border_opacity' => $this->variant_pills_border_opacity(),
            'variant_pills_radius' => $this->variant_pills_radius(),
            'variant_pills_shadow_opacity' => $this->variant_pills_shadow_opacity(),
            'variant_pills_shadow_horizontal_offset' => $this->variant_pills_shadow_horizontal_offset(),
            'variant_pills_shadow_vertical_offset' => $this->variant_pills_shadow_vertical_offset(),
            'variant_pills_shadow_blur' => $this->variant_pills_shadow_blur(),
            'inputs_border_thickness' => $this->inputs_border_thickness(),
            'inputs_border_opacity' => $this->inputs_border_opacity(),
            'inputs_radius' => $this->inputs_radius(),
            'inputs_shadow_opacity' => $this->inputs_shadow_opacity(),
            'inputs_shadow_horizontal_offset' => $this->inputs_shadow_horizontal_offset(),
            'inputs_shadow_vertical_offset' => $this->inputs_shadow_vertical_offset(),
            'inputs_shadow_blur' => $this->inputs_shadow_blur(),
            'card_style' => $this->card_style(),
            'card_image_padding' => $this->card_image_padding(),
            'card_text_alignment' => $this->card_text_alignment(),
            'card_color_scheme' => $this->card_color_scheme(),
            'card_border_thickness' => $this->card_border_thickness(),
            'card_border_opacity' => $this->card_border_opacity(),
            'card_corner_radius' => $this->card_corner_radius(),
            'card_shadow_opacity' => $this->card_shadow_opacity(),
            'card_shadow_horizontal_offset' => $this->card_shadow_horizontal_offset(),
            'card_shadow_vertical_offset' => $this->card_shadow_vertical_offset(),
            'card_shadow_blur' => $this->card_shadow_blur(),
            'collection_card_style' => $this->collection_card_style(),
            'collection_card_image_padding' => $this->collection_card_image_padding(),
            'collection_card_text_alignment' => $this->collection_card_text_alignment(),
            'collection_card_color_scheme' => $this->collection_card_color_scheme(),
            'collection_card_border_thickness' => $this->collection_card_border_thickness(),
            'collection_card_border_opacity' => $this->collection_card_border_opacity(),
            'collection_card_corner_radius' => $this->collection_card_corner_radius(),
            'collection_card_shadow_opacity' => $this->collection_card_shadow_opacity(),
            'collection_card_shadow_horizontal_offset' => $this->collection_card_shadow_horizontal_offset(),
            'collection_card_shadow_vertical_offset' => $this->collection_card_shadow_vertical_offset(),
            'collection_card_shadow_blur' => $this->collection_card_shadow_blur(),
            'blog_card_style' => $this->blog_card_style(),
            'blog_card_image_padding' => $this->blog_card_image_padding(),
            'blog_card_text_alignment' => $this->blog_card_text_alignment(),
            'blog_card_color_scheme' => $this->blog_card_color_scheme(),
            'blog_card_border_thickness' => $this->blog_card_border_thickness(),
            'blog_card_border_opacity' => $this->blog_card_border_opacity(),
            'blog_card_corner_radius' => $this->blog_card_corner_radius(),
            'blog_card_shadow_opacity' => $this->blog_card_shadow_opacity(),
            'blog_card_shadow_horizontal_offset' => $this->blog_card_shadow_horizontal_offset(),
            'blog_card_shadow_vertical_offset' => $this->blog_card_shadow_vertical_offset(),
            'blog_card_shadow_blur' => $this->blog_card_shadow_blur(),
            'text_boxes_border_thickness' => $this->text_boxes_border_thickness(),
            'text_boxes_border_opacity' => $this->text_boxes_border_opacity(),
            'text_boxes_radius' => $this->text_boxes_radius(),
            'text_boxes_shadow_opacity' => $this->text_boxes_shadow_opacity(),
            'text_boxes_shadow_horizontal_offset' => $this->text_boxes_shadow_horizontal_offset(),
            'text_boxes_shadow_vertical_offset' => $this->text_boxes_shadow_vertical_offset(),
            'text_boxes_shadow_blur' => $this->text_boxes_shadow_blur(),
            'media_border_thickness' => $this->media_border_thickness(),
            'media_border_opacity' => $this->media_border_opacity(),
            'media_radius' => $this->media_radius(),
            'media_shadow_opacity' => $this->media_shadow_opacity(),
            'media_shadow_horizontal_offset' => $this->media_shadow_horizontal_offset(),
            'media_shadow_vertical_offset' => $this->media_shadow_vertical_offset(),
            'media_shadow_blur' => $this->media_shadow_blur(),
            'popup_border_thickness' => $this->popup_border_thickness(),
            'popup_border_opacity' => $this->popup_border_opacity(),
            'popup_corner_radius' => $this->popup_corner_radius(),
            'popup_shadow_opacity' => $this->popup_shadow_opacity(),
            'popup_shadow_horizontal_offset' => $this->popup_shadow_horizontal_offset(),
            'popup_shadow_vertical_offset' => $this->popup_shadow_vertical_offset(),
            'popup_shadow_blur' => $this->popup_shadow_blur(),
            'drawer_border_thickness' => $this->drawer_border_thickness(),
            'drawer_border_opacity' => $this->drawer_border_opacity(),
            'drawer_shadow_opacity' => $this->drawer_shadow_opacity(),
            'drawer_shadow_horizontal_offset' => $this->drawer_shadow_horizontal_offset(),
            'drawer_shadow_vertical_offset' => $this->drawer_shadow_vertical_offset(),
            'drawer_shadow_blur' => $this->drawer_shadow_blur(),
            'badge_position' => $this->badge_position(),
            'badge_corner_radius' => $this->badge_corner_radius(),
            'sale_badge_color_scheme' => $this->sale_badge_color_scheme(),
            'sold_out_badge_color_scheme' => $this->sold_out_badge_color_scheme(),
            'brand_headline' => $this->brand_headline(),
            'brand_description' => $this->brand_description(),
            'brand_image_width' => $this->brand_image_width(),
            'social_facebook_link' => $this->social_facebook_link(),
            'social_instagram_link' => $this->social_instagram_link(),
            'social_youtube_link' => $this->social_youtube_link(),
            'social_tiktok_link' => $this->social_tiktok_link(),
            'social_twitter_link' => $this->social_twitter_link(),
            'social_snapchat_link' => $this->social_snapchat_link(),
            'social_pinterest_link' => $this->social_pinterest_link(),
            'social_tumblr_link' => $this->social_tumblr_link(),
            'social_vimeo_link' => $this->social_vimeo_link(),
            'predictive_search_enabled' => $this->predictive_search_enabled(),
            'predictive_search_show_vendor' => $this->predictive_search_show_vendor(),
            'predictive_search_show_price' => $this->predictive_search_show_price(),
            'currency_code_enabled' => $this->currency_code_enabled(),
            'cart_type' => $this->cart_type(),
            'show_vendor' => $this->show_vendor(),
            'show_cart_note' => $this->show_cart_note(),
            'cart_drawer_collection' => $this->cart_drawer_collection(),
            'cart_color_scheme' => $this->cart_color_scheme(),
            'sections' => $this->sections(),
            'content_for_index' => $this->content_for_index(),
            'blocks' => $this->blocks(),
            'color_schemes' => $this->color_schemes(),
        ];
    }

    private function _settings($property, $value)
    {
        /*dd(
            collect(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS))
                ->map(fn($frame) => sprintf(
                    "%s:%d   %s%s%s()",
                    $frame['file']    ?? '[internal]',
                    $frame['line']    ?? 0,
                    $frame['class']   ?? '',
                    $frame['type']    ?? '',
                    $frame['function'] ?? '[closure]'
                ))
                ->all()
        );*/

        $data = config('app.theme_settings_data', []);

        $settings = [];
        if (isset($data['current']) && is_array($data['current'])) {
            $settings = $data['current'];
        }

        if (empty($settings) && isset($data['presets']['Default'])) {
            $settings = $data['presets']['Default'];
        }

        return $settings[$property] ?? $value;
    }
}
