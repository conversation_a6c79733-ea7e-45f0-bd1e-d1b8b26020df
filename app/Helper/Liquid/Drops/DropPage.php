<?php

declare(strict_types=1);

namespace App\Helper\Liquid\Drops;

use Liquid\Drop;

class DropPage extends Drop
{
    public function id(): int
    {
        if (is_null($this->context)) {
            return 0;
        }

        return (int)($this->context->getAssigns()['page']['id'] ?? 0);
    }

    public function handle(): string
    {
        if (is_null($this->context)) {
            return '';
        }

        return $this->context->getAssigns()['page']['handle'] ?? '';
    }

    public function title(): string
    {
        if (is_null($this->context)) {
            return '';
        }

        return $this->context->getAssigns()['page']['title'] ?? '';
    }

    public function content(): string
    {
        if (is_null($this->context)) {
            return '';
        }

        return $this->context->getAssigns()['page']['content']
            ?? $this->context->getAssigns()['page']['body_html']
            ?? '';
    }

    public function author(): string
    {
        if (is_null($this->context)) {
            return '';
        }

        return $this->context->getAssigns()['page']['author'] ?? '';
    }

    public function created_at(): string
    {
        if (is_null($this->context)) {
            return '';
        }

        return $this->context->getAssigns()['page']['created_at'] ?? '';
    }

    public function updated_at(): string
    {
        if (is_null($this->context)) {
            return '';
        }

        return $this->context->getAssigns()['page']['updated_at'] ?? '';
    }

    public function published_at(): string
    {
        if (is_null($this->context)) {
            return '';
        }

        return $this->context->getAssigns()['page']['published_at'] ?? '';
    }

    public function template_suffix(): string
    {
        if (is_null($this->context)) {
            return '';
        }

        return $this->context->getAssigns()['page']['template_suffix'] ?? '';
    }

    public function url(): string
    {
        if (is_null($this->context)) {
            return '';
        }

        return $this->context->getAssigns()['page']['url'] ?? '';
    }

    public function comment_count(): int
    {
        if (is_null($this->context)) {
            return 0;
        }

        return (int)($this->context->getAssigns()['page']['comment_count'] ?? 0);
    }

    public function metafields(): array
    {
        if (is_null($this->context)) {
            return [];
        }

        return $this->context->getAssigns()['page']['metafields'] ?? [];
    }

    /**
     * {@inheritDoc}
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'handle' => $this->handle(),
            'title' => $this->title(),
            'content' => $this->content(),
            'author' => $this->author(),
            'created_at' => $this->created_at(),
            'updated_at' => $this->updated_at(),
            'published_at' => $this->published_at(),
            'template_suffix' => $this->template_suffix(),
            'url' => $this->url(),
            'comment_count' => $this->comment_count(),
            'metafields' => $this->metafields(),
        ];
    }
}
