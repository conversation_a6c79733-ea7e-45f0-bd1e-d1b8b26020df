<?php

declare(strict_types=1);

namespace App\Helper\Liquid\Drops;

use Liquid\Drop;

class DropAdditionalCheckoutButtons extends Drop
{
    public function content_for_additional_checkout_buttons(): string
    {
        // TODO
        return '';
    }

    /**
     * {@inheritDoc}
     */
    public function toArray(): array
    {
        return [
            'content_for_additional_checkout_buttons' => $this->content_for_additional_checkout_buttons(),
        ];
    }
}
