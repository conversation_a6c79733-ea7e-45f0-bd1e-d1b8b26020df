<?php
/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.2.2019 г.
 * Time: 14:17 ч.
 */

namespace App\Helper;


use App\Exceptions\Error;
use Carbon\Carbon;

class AnalyticsEncrypt
{
    private $password;

    /**
     * @param $password
     */
    public function __construct($password)
    {
        $this->password = $password;
    }

    /**
     * @param array $data
     * @param null $key
     * @return array
     */
    public function encrypt(array $data = [], $key = null)
    {
        if($key && in_array($key, config('uuid.must_encrypt'))) {
            $data = [
                'er' => ($id = ($data[$key . '_id'] ?? null)),
                'erh' => hash_hmac('sha1', $id, $this->password),
                'raw' => $data,
                'key' => $key,
                'time' => Carbon::now()->timezone('UTC')->toDateTimeString()
            ];
        }

        return [$key => base64_encode(gzencode(json_encode($data), 9))];
    }

    /**
     * @param string $data
     * @return array|null
     */
    public function decrypt(string $data)
    {
        try {
            $data = json_decode(gzdecode(base64_decode($data)), true);
        } catch (\Exception $e) {
            return null;
        }

        if(($data['key']??null) && in_array($data['key'], config('uuid.must_encrypt'))) {
            if(hash_hmac('sha1', $data['er'], $this->password) != $data['erh']) {
                return null;
            }
            return Carbon::createFromTimeString($data['time'], 'UTC') > Carbon::now('UTC')->subMinute(inDevelopment() ? 1440 : 10) ? ($data['raw'] ?? null) : null;
        }

        return is_array($data) ? $data : null;
    }
}
