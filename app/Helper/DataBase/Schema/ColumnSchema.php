<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 24.5.2016 г.
 * Time: 15:06 ч.
 */

namespace App\Helper\DataBase\Schema;

use Illuminate\Database\MySqlConnection;

class ColumnSchema
{
    /**
     * @var string name of this column (without quotes).
     */
    public $name;

    /**
     * @var boolean whether this column can be null.
     */
    public $allowNull = false;

    /**
     * @var string the PHP type of this column.
     */
    public $type;

    /**
     * @var mixed default value of this column
     */
    public $defaultValue = '';

    /**
     * @var integer size of the column.
     */
    public $size;

    /**
     * @var integer precision of the column data, if it is numeric.
     */
    public $precision;

    /**
     * @var integer scale of the column data, if it is numeric.
     */
    public $scale;

    /**
     * @var boolean whether this column is a primary key
     */
    public $isPrimaryKey = false;

    /**
     * @var boolean whether this column is auto-incremental
     * @since 1.1.7
     */
    public $autoIncrement = false;

    public $unsigned;

    public $comment;

    public $character;

    public $collate;

    public $value = [];

    public $key = false;

    public $keys = [];

    private array $changed = [];

    /**
     * @param string $table
     * @param mixed $line
     */
    public function __construct($line, /**
     * @var string name of table (without quotes).
     */
        public $table, private readonly MySqlConnection $connection)
    {
        $this->parseColumn($line);
    }

    /**
     * @param mixed $string
     * @return mixed
     */
    protected function parseColumn($string): static
    {
        if (preg_match('~^`([^`]*)`~i', (string) $string, $m)) {
            $this->name = $m[1];
        }

        if (preg_match('~auto_increment~i', (string) $string)) {
            $this->autoIncrement = true;
        }

        if (!preg_match('~not\snull~i', (string) $string)) {
            $this->allowNull = true;
        }

        if (preg_match('~comment\s\'(.*)\',~i', (string) $string, $m)) {
            $this->comment = $m[1];
        }

        if (preg_match('~\sCHARACTER\sSET\s([^\s]*)~i', (string) $string, $m)) {
            $this->character = trim($m[1], ',');
        }

        if (preg_match('~\sCOLLATE\s([^\s]*)~i', (string) $string, $m)) {
            $this->collate = trim($m[1], ',');
        }

        if (preg_match('~default\snull~i', (string) $string)) {
            $this->defaultValue = null;
        } elseif (preg_match('~default\s\'([^\']*)\'~i', (string) $string, $m)) {
            $this->defaultValue = $m[1];
        }

        if (preg_match('/\s((?:var)?char)\((\d+)\)/i', (string) $string, $matches)) {
            $this->type = $matches [1];
            $this->size = $matches [2];
        } elseif (preg_match('/\sdecimal\((\d+),(\d+)\)/i', (string) $string, $matches)) {
            $this->type = 'decimal';
            $this->precision = $matches [1];
            $this->scale = $matches [2];
        } elseif (preg_match('/\sfloat\((\d+),(\d+)\)/i', (string) $string, $matches)) {
            $this->type = 'float';
            $this->precision = $matches [1];
            $this->scale = $matches [2];
        } elseif (preg_match('/\sdouble/i', (string) $string, $matches)) {
            $this->type = 'double';
        } elseif (preg_match('/\s((?:big|medium|small|tiny)?int)\((\d+)\)/i', (string) $string, $matches)) {
            $this->type = $matches [1];
            $this->size = $matches [2];
            $this->unsigned = stripos((string) $string, ' unsigned ') !== false;
        } elseif (preg_match('/\s(enum|set)\((.*)\)/i', (string) $string, $matches)) {
            $this->type = $matches [1];
            $value = explode(',', $matches [2]);
            $this->value = array_map(fn ($value): string => trim((string) $value, "'"), $value);
        } elseif (preg_match('/\s((?:big|medium|small|tiny|long)?text)/i', (string) $string, $matches)) {
            $this->type = $matches [1];
        } elseif (preg_match('/\s((?:big|medium|small|tiny|long)?blob)/i', (string) $string, $matches)) {
            $this->type = $matches [1];
        } elseif (preg_match('/\s(datetime|timestamp|date|time)/i', (string) $string, $matches)) {
            $this->type = $matches [1];
        }

        return $this;
    }

    /**
     * @param array $value
     * @return mixed
     */
    public function value(array $value): static
    {
        $this->changed[__FUNCTION__] = [$this->value, $value];
        $this->value = $value;
        return $this;
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function addValue($value): static
    {
        $this->changed[__FUNCTION__] = [$this->value, array_merge($this->value, (array)$value)];
        $this->value[] = $value;
        return $this;
    }

    public function change()
    {
        if (!$this->changed) {
            return false;
        }

        $sql = 'ALTER TABLE `' . $this->table . '` CHANGE `' . $this->name . '` `' . $this->name . '` ';
        $sql .= $this->type . '(' . $this->_values() . ') CHARACTER SET utf8 COLLATE utf8_unicode_ci';

        if (!$this->allowNull) {
            if ($this->defaultValue) {
                $sql .= ' NOT NULL DEFAULT ' . $this->_escape($this->defaultValue) . '';
            } else {
                $sql .= ' NOT NULL DEFAULT ' . $this->_escape(last($this->value)) . '';
            }
        } else {
            $sql .= ' NULL DEFAULT NULL';
        }

        return $this->connection->statement($sql);
    }

    private function _values(): string
    {
        return implode(',', array_map(fn ($value): string => $this->_escape($value), array_unique($this->value)));
    }

    /**
     * @param mixed $text
     * @return mixed
     */
    private function _escape($text): string
    {
        return $this->connection->getPdo()->quote($text);
        ;
    }

}
