<?php

namespace App\Contracts;

use App\Helper\GeoZone\GeoZoneFilterInformation;
use Modules\Apps\Shippings\Omniship\AbstractManager;
use App\Models\Customer\Customer;
use App\Models\Customer\CustomerBillingAddress;
use App\Models\Customer\CustomerShippingAddress;
use App\Models\Discount\Discount;
use App\Models\Discount\DiscountCode;
use App\Models\Shipping\ShippingProvider;
use App\Models\Store\Cart;
use App\Models\Store\CartItem;
use App\Models\Tax\Tax;
use Illuminate\Support\Collection;
use Omniship\Common\ShippingQuote;
use Throwable;

interface OrderContract
{

    /**
     * @return Customer
     */
    public function getCustomer();

    /**
     * @param $type
     * @return int|string
     */
    public function getSubTotal($type = null);

    /**
     * @param $type
     * @return int|string
     */
    public function getSubTotalWithoutVat($type = null);

    /**
     * @param $type
     * @return int|string
     */
    public function getSubTotalWithVat($type = null);

    /**
     * @param $type
     * @return int|string
     */
    public function getSubTotalVat($type = null);

    /**
     * @param $type
     * @return int|string
     */
    public function getTotalBeforeShipping($type = null);

    /**
     * @return string
     */
    public function getCountryIso2();

    /**
     * @return string|null
     */
    public function getAdminLevel1();

    /**
     * @return GeoZoneFilterInformation|null
     */
    public function getZoneInformationShipping();

    /**
     * @return GeoZoneFilterInformation|null
     */
    public function getZoneInformationBilling();

    /**
     * @return CartItem[]|Collection
     */
    public function getProducts();

    /**
     * @return array
     */
    public function getShippableProductsCategoryIds();

    /**
     * @return Collection
     */
    public function getNotVatTaxes();

    /**
     * @return Discount[]|Collection
     */
    public function getDiscounts();

    /**
     * @return Discount
     */
    public function getDiscountCode();

    /**
     * @return Discount
     */
    public function getPaymentDiscount();

    /**
     * @return Collection
     */
    public function getDiscountShippings();

    /**
     * @return DiscountCode[]|Collection
     */
    public function getDiscountContainerCodes();

    /**
     * @param string|null $type
     * @return integer
     */
    public function getFreeShippingAmountLeft($type = null);

    /**
     * @param $amount
     * @return OrderContract
     */
    public function setFreeShippingAmountLeft($amount);

    /**
     * @return ShippingProvider
     */
    public function getShipping();

    /**
     * @param null $default
     * @return string
     */
    public function getPayerSide($default = null, $with_meta = true);

    /**
     * @return boolean
     */
    public function hasShippable();

    /**
     * @return ShippingQuote|null
     */
    public function getShippingQuote();

    /**
     * @return AbstractManager
     */
    public function getShippingManager();

    /**
     * @return CustomerBillingAddress|null
     */
    public function getBillingAddress();

    /**
     * @return CustomerShippingAddress|null
     */
    public function getShippingAddress();

    public function getTaxAddress();

    /**
     * @param null|array $ignore
     * @return Collection
     */
    public function getTotals($ignore = null);

    /**
     * @return null|Tax
     */
    public function getVatByAddress();

    /**
     * @return Collection
     */
    public function getTotalsSimple();

    /**
     * @param null $type
     * @return float|int|null|string
     */
    public function getTotalWithoutShipping($type = null);

    /**
     * @param $type
     * @return int|string
     */
    public function getTotal($type = null);

    /**
     * @param $type
     * @return int|string
     */
    public function getPaymentTotal($type = null);

    /**
     * @param $type
     * @return int|string
     */
    public function getTotalShipping($type = null);

    /**
     * @return bool
     */
    public function hasVatIncluded();

    public function getShippingAddressForProvider($provider);

    public function getItemsBag();

    public function getPayment();

    /**
     * @return Cart
     */
    public function getCart();

    /**
     * @return null|Discount
     */
    public function getCountdownDiscount();

    /**
     * @return mixed|string
     * @throws Throwable
     */
    public function applyVat();

}
