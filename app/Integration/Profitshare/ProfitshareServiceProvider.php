<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 11.4.2017 г.
 * Time: 00:04 ч.
 */

namespace App\Integration\Profitshare;

use Illuminate\Support\ServiceProvider;

class ProfitshareServiceProvider extends ServiceProvider
{

    public function register()
    {
        $this->app->singleton(ProfitshareManager::APP_KEY, function () {
            return new ProfitshareManager();
        });
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [
            ProfitshareManager::APP_KEY,
        ];
    }
}
