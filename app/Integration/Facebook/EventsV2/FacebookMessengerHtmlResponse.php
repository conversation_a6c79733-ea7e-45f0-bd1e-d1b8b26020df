<?php

declare(strict_types=1);

namespace App\Integration\Facebook\EventsV2;

use App\Exceptions\Error;
use App\Integration\Facebook\Services\Messenger;
use Modules\JsResponse\AbstractHtmlResponse;
use Modules\JsResponse\Formatters\HtmlRow;

class FacebookMessengerHtmlResponse extends AbstractHtmlResponse
{

    /**
     * @return array|null
     * @throws Error
     */
    public function getHtml(): ?array
    {
        if (request()->ajax()) {
            return [];
        }

        /** @var Messenger $messanger */
        $messanger = app('fb:messenger');
        if (!$messanger->getChatManager()->isActive()) {
            return [];
        }

        view()->addNamespace('fbmessenger', $messanger->getViewsDir() . 'storefront/v2/');

        $return = [
            new HtmlRow(view('fbmessenger::init-chat')->render()),
        ];

        return $return;
    }

}
