<?php

declare(strict_types=1);

namespace App\Integration\GoogleTags;

use Apps;
use Modules\JsResponse\AbstractHtmlResponse;
use Modules\JsResponse\Contracts\HtmlRowContract;
use Mo<PERSON>les\JsResponse\Formatters\HtmlRow;
use Throwable;

class GoogleTagsHtmlResponse extends AbstractHtmlResponse
{
    /**
     * @return null|array|HtmlRowContract[]
     * @throws Throwable
     */
    public function getHtml() : ?array
    {
        if(request()->ajax()) {
            return [];
        }

        view()->addNamespace('google_tags', __DIR__ . '/views/');

        $data = [];

        if(Apps::enabled('google_tags') && is_string(Apps::setting('google_tags', 'code'))) {
            $data[] = new HtmlRow(view('google_tags::customer-no-script')->render());
        }

        return $data;
    }

}
