<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 26.1.2017 г.
 * Time: 23:52 ч.
 */

namespace App\Integration\Retargeting;

use App\Helper\Encoders\JavaScript;

class JsEvents
{
    protected $active;

    protected $tracking_api;

    protected $code = [];

    protected $manager;

    protected $triggers = [];

    /**
     * JsEvents constructor.
     * @param RetargetingManager $manager
     * @throws \App\Exceptions\Error
     */
    public function __construct(RetargetingManager $manager)
    {
        $this->active = true;
        $this->manager = $manager;
        $this->tracking_api = $manager->getSetting('tracking_api');
        if (!trim($this->tracking_api)) {
            $this->active = false;
        }
    }

    /**
     * @param array ...$attr
     * @return JsEvents
     */
    public function setEmail(...$attr)
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return JsEvents
     */
    public function sendCategory(...$attr)
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return JsEvents
     */
    public function sendBrand(...$attr)
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return JsEvents
     */
    public function sendProduct(...$attr)
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array $params
     * @return JsEvents
     */
    public function addToCart(array $params)
    {
        if ($this->active) {
            $event = __FUNCTION__;
            $this->code[$event] = "_ra." . $event . "Info = " . JavaScript::encode($params) . ";\n";
            $this->code[$event] .= "
            if(json.product.total_variants > 0) {
                _ra." . $event . "Info.variation = {code: json.product.text_key, stock:true, details:{}};

                for(var i=1; i<=json.product.total_variants;i++) {
                    var vN = json.product['v' + i],
                        pN = json.product['p' + i];

                    _ra." . $event . "Info.variation.details[vN] = {category_name: pN, category: pN, value: vN};
                }
            } else {
                _ra." . $event . "Info.variation = false;
            }";
            $this->code[$event] .= "_ra." . $event . "(_ra." . $event . "Info.product_id, _ra." . $event . "Info.quantity, _ra." . $event . "Info.variation);\n";
        }
        return $this;
    }

    /**
     * @param array $params
     * @return JsEvents
     */
    public function removeFromCart(array $params)
    {
        if ($this->active) {
            $event = __FUNCTION__;
            $this->code[$event] = "_ra." . $event . "Info = " . JavaScript::encode($params) . ";\n";
            $this->code[$event] .= "
            if(json.product.total_variants > 0) {
                _ra." . $event . "Info.variation = {code: json.product.text_key, stock:true, details:{}};

                for(var i=1; i<=json.product.total_variants;i++) {
                    var vN = json.product['v' + i],
                        pN = json.product['p' + i];

                    _ra." . $event . "Info.variation.details[vN] = {category_name: pN, category: pN, value: vN};
                }
            } else {
                _ra." . $event . "Info.variation = false;
            }";
            $this->code[$event] .= "_ra." . $event . "(_ra." . $event . "Info.product_id, _ra." . $event . "Info.quantity, _ra." . $event . "Info.variation);\n";
        }
        return $this;
    }

    /**
     * @param array $params
     * @return JsEvents
     */
    public function setVariation($params)
    {
        if ($this->active) {
            $event = __FUNCTION__;
            $this->code[$event] = "_ra." . $event . "Info = " . JavaScript::encode($params) . ";\n";
            $this->code[$event] .= "
            _ra." . $event . "Info.variation = {code: json.text_key, stock:true, details:{}};

            for(var i=1; i<=3;i++) {
                var vN = json['v' + i],
                    pN = json['p' + i];

                if(vN !== undefined) {
                    _ra." . $event . "Info.variation.details[vN] = {category_name: pN, category: pN, value: vN};
                }
            }";
            $this->code[$event] .= "_ra." . $event . "(_ra." . $event . "Info.product_id, _ra." . $event . "Info.variation);\n";
        }
        return $this;
    }

    /**
     * @param array $params
     * @return JsEvents
     */
    public function addToWishlist($params)
    {
        if ($this->active) {
            $event = __FUNCTION__;
            $this->code[$event] = "_ra." . $event . "Info = " . JavaScript::encode($params) . ";\n";
            $this->code[$event] .= "_ra." . $event . "(_ra." . $event . "Info.product_id);\n";
        }
        return $this;
    }

    /**
     * @param array $params
     * @return JsEvents
     */
    public function clickImage($params)
    {
        if ($this->active) {
            $event = __FUNCTION__;
            $this->code[$event] = "_ra." . $event . "Info = " . JavaScript::encode($params) . ";\n";
            $this->code[$event] .= "_ra." . $event . "(_ra." . $event . "Info.product_id);\n";
        }
        return $this;
    }

    /**
     * @param array ...$attr
     * @return JsEvents
     */
    public function commentOnProduct(...$attr)
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return JsEvents
     */
    public function likeFacebook(...$attr)
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array $a
     * @param array $b
     * @return JsEvents
     */
    public function saveOrder(array $a, array $b)
    {
        return $this->call(__FUNCTION__, $a, $b);
    }

    /**
     * @param array ...$attr
     * @return JsEvents
     */
    public function visitHelpPage(...$attr)
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return JsEvents
     */
    public function checkoutIds(...$attr)
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array $params
     * @return JsEvents
     */
    public function setCartUrl($params)
    {
        if ($this->active) {
            $event = __FUNCTION__;
            $this->code[$event] = "_ra." . $event . "Info = " . JavaScript::encode($params) . ";\n";
            $this->code[$event] .= "_ra." . $event . "(_ra." . $event . "Info.url);\n";
        }
        return $this;
    }

    /**
     * @param array $params
     * @return JsEvents
     */
    public function subscribeEmail($params)
    {
        if ($this->active) {
            $event = __FUNCTION__;
            $this->code[$event] = "_ra." . $event . "Info = " . JavaScript::encode($params) . ";\n";
            $this->code[$event] .= "_ra." . $event . "(_ra." . $event . "Info);\n";
        }
        return $this;
    }

    /**
     * @param $name
     * @param \Closure $callback
     * @param array $args
     * @param string $selector
     * @return $this
     */
    public function trigger($name, \Closure $callback, array $args = [], $selector = 'js:document')
    {
        if ($this->active) {
            $this->triggers[] = [
                'name' => $name,
                'callback' => $callback,
                'args' => $args,
                'selector' => $selector
            ];
        }
        return $this;
    }

    /**
     * @return null|string
     */
    public function render()
    {
        if (!$this->code) {
            return null;
        }
        $code = '<!— Retargeting Code --><script type="text/javascript">';
        $code .= $this->renderClear();
        $code .= '</script><!— End Retargeting Code -->';
        return $code;
    }

    /**
     * @return null|string
     */
    public function renderClear()
    {
        if (!$this->code) {
            return null;
        }
        $code = '$(document).ready(function() {';
        $code .= 'var retargeting_interval = setInterval(function() {';
        $code .= 'if(window[\'_ra\'] != undefined) {';
        $code .= $this->renderCode();
        $code .= 'clearInterval(retargeting_interval); }';
        $code .= '}, 10);';
        $code .= $this->_renderTriggers();
        $code .= '});';
        return $code;
    }

    /**
     * @return string
     */
    public function renderCode()
    {
        return "if('_ra' in window && _ra !== undefined && _ra.ready !== undefined) { \n" . implode("\n", $this->code) . "\n}\n";
    }

    /**
     * @param $event
     * @param array $params
     * @return $this
     */
    private function call($event, array $params = [], array $params2 = [])
    {
        if ($this->active) {
            if (isset($params[0])) {
                $params = $params[0];
            }
            if ($params) {
                if ($params2) {
                    $this->code[$event] = "_ra." . $event . "Info = " . JavaScript::encode($params) . ";\n";
                    $this->code[$event] .= "_ra." . $event . "Products = " . JavaScript::encode($params2) . ";\n";
                    $this->code[$event] .= "_ra." . $event . "(_ra." . $event . "Info, _ra." . $event . "Products);\n";
                } else {
                    $this->code[$event] = "_ra." . $event . "Info = " . JavaScript::encode($params) . ";\n";
                    $this->code[$event] .= "_ra." . $event . "(_ra." . $event . "Info);\n";
                }
            } else {
                if ($event == 'visitHelpPage') {
                    $this->code[$event] = "_ra." . $event . "Info = {\"visit\": true};\n";
                } else {
                    $this->code[$event] = "_ra." . $event . "Info = {};\n";
                }
                $this->code[$event] .= "_ra." . $event . "();\n";
            }
        }
        return $this;
    }

    public function head()
    {
        return '<!— Retargeting Code -->
<script type="text/javascript">' . $this->headClear() . '</script>
<!— End Retargeting Code -->' . "\n";
    }


    public function headClear()
    {
        if(request()->ajax()) {
            return '';
        }

        return '(function(){
	ra_key = "' . $this->tracking_api . '";
	var ra = document.createElement("script"); ra.type ="text/javascript"; ra.async = true;
	ra.src = "https://tracking.retargeting.biz/v3/rajs/" + ra_key + ".js";
	var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(ra,s);})();';
    }

//    public function head() {
//        return '<!— Retargeting Code --><script type="text/javascript">var ra_key = "'.$this->tracking_api.'";</script><script async type="text/javascript" src="//tracking.retargeting.biz/v3/rajs/'.$this->tracking_api.'.js"></script><!— End Retargeting Code -->' . "\n";
//    }

    /**
     * @return string
     * @throws \App\Exceptions\Error
     */
    private function _renderTriggers()
    {
        $return = '';
        foreach ($this->triggers AS $r => $trigger) {
            $instance = new static($this->manager);
            $trigger['callback']($instance);
            $return .= "$(" . JavaScript::encode($trigger['selector']) . ").off('{$trigger['name']}.r{$r}').on('{$trigger['name']}.r{$r}', function(" . implode(', ', array_merge(['e'], $trigger['args'])) . ") {
                " . $instance->renderCode() . "
            });\n";
        }
        return $return;
    }

}
