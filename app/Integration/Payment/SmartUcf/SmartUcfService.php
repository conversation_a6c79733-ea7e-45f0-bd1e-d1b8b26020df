<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 17.2.2017 г.
 * Time: 11:55
 */

namespace App\Integration\Payment\SmartUcf;

use App\Exceptions\Error;
use App\Exceptions\PaymentBadRequest;
use App\Helper\Format;
use App\Integration\Payment\CreditorService;
use App\Models\Gateway\PaymentLogs;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\Payments;
use App\Models\Order\Order;
use App\Models\Order\OrderPayment;
use App\Models\Product\Product;
use App\Services\Creditor;
use Exception;
use Gentor\SmartUcf\Service\SmartUcf;
use Gentor\SmartUcf\Service\SmartUcfException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Modules\Core\Invoicing\Facades\Invoicing;

/**
 * Class SmartUcfService
 *
 * @package App\Integration\Payment\SmartUcf
 * @method  refund(Payments $payment)
 * @method  capturePurchase(Payments $payment, array $data = array())
 */
class SmartUcfService extends CreditorService
{
    /**
     * @const int The minimum price after which payment provider becomes available.
     */
    public const MinPrice = 150;

    /**
     *
     */
    public const Email = '<EMAIL>';

    protected \Gentor\SmartUcf\Service\SmartUcf $ucf;

    /**
     * BnpService constructor.
     *
     */
    public function __construct()
    {
        $this->ucf = new SmartUcf($this->loadConfig());
    }

    /**
     * @param Request $request
     * @return Payments
     */
    public static function getPaymentFromRequest(Request $request): Payments
    {
        $provider_reference_id = $request->get('ref');
        if (empty($provider_reference_id)) {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        return Payments::findByProviderReferenceId('smart_ucf', $provider_reference_id);
    }

    /**
     * @return array
     */
    protected function loadConfig(): array
    {
        $config = array_merge(config('smart-ucf'), [
            'username' => '',
            'password' => '',
            'test_mode' => true,
            'timeout' => 3,
//            'base_uri' => 'http://34.116.206.124'
        ]);

        $provider = PaymentProviderConfiguration::findByProvider('smart_ucf');

        if (!empty($provider->configuration)) {
            $config = array_merge($config, [
                'username' => $provider->configuration['user'] ?? '',
                'password' => $provider->configuration['pass'] ?? '',
                'test_mode' => 'live' != ($provider->configuration['mode'] ?? ''),
            ]);
        }

        return $config;
    }

    /**
     * @return array
     */
    #[\Override]
    public function meta(): array
    {
        return [
            'email' => static::Email,
            'min_price' => static::MinPrice,
        ];
    }

    /**
     * @param float $price
     * @param array $goods
     * @param int $scheme_id
     * @param float|int $down_payment
     * @param float|int $installment
     * @return array
     * @throws \App\Exceptions\Error
     */
    #[\Override]
    public function getPricingVariants($price, array $goods, $scheme_id = 1, $down_payment = 0, $installment = 0): array
    {
        try {
            $variants = $this->ucf->getPricingVariants($price, $goods, $scheme_id, $down_payment, $installment);

            foreach ($variants as $variant) {
                $variant->CorrectDownPaymentAmountFormatted = Format::money(Format::toIntegerPrice($variant->CorrectDownPaymentAmount));
                $variant->InstallmentAmountFormatted = Format::money(Format::toIntegerPrice($variant->InstallmentAmount));
                $variant->TotalRepaymentAmountFormatted = Format::money(Format::toIntegerPrice($variant->TotalRepaymentAmount));
                $variant->NIRFormatted = Format::percent(Format::toIntegerPrice($variant->NIR));
                $variant->APRFormatted = Format::percent(Format::toIntegerPrice($variant->APR));
            }

            return $variants;
        } catch (Exception $exception) {
            throw new Error('Service error: ' . $exception->getMessage());
        }
    }

    /**
     * @return string|null
     */
    #[\Override]
    public static function additionalSettingsUrl(): ?string
    {
        return route('admin.ucf.promo');
    }

    /**
     * @param $productIds
     * @param $price
     * @param int $downPayment
     * @param int $installment
     * @return array
     */
    #[\Override]
    public function getPricingData($productIds, $price, $downPayment = 0, $installment = 0): array
    {
        $products = Product::withHiddenItems()->whereIn('id', $productIds)->with(['category', 'ucfPromotion'])->get();
        $goods = [];
        $filterVariants = [];
        $schemes = [];
        foreach ($products as $product) {
            if ($product->ucfPromotion) {
                $goods[] = $product->ucfPromotion->ucf_cop;
                $filterVariants = array_merge($filterVariants, $product->ucfPromotion->variants);
            } elseif (isset($product->category->ucf_cop)) {
                $goods[] = $product->category->ucf_cop;
            }
        }

        if (empty($goods[0])) {
            $creditor = PaymentProviderConfiguration::findByProvider('smart_ucf');
            $goods = [$creditor->configuration['default_cop'] ?? 'Not found'];
        }

        try {
            $schemes = $this->getPricingSchemes($price, $goods, $downPayment);

            foreach ($schemes as $schemeKey => $scheme) {
                $variants = $this->getPricingVariants($price, $goods, $scheme->PricingSchemeId, $downPayment, $installment);
                $variants = Collection::make($variants)->sortBy('Maturity', SORT_ASC)->all();

                if (!empty($filterVariants)) {
                    foreach ($variants as $variantKey => $variant) {
                        if (!in_array($variant->PricingVariantId, $filterVariants)) {
                            unset($variants[$variantKey]);
                        }
                    }
                }

                if (empty($variants)) {
                    unset($schemes[$schemeKey]);
                    continue;
                }

                $scheme->variants = $variants;
            }

            if (empty($schemes) && !empty($filterVariants)) {
                throw new Error(__('sf.err.cannot_buy_together_free_and_standard_leasing_products'));
            }
        } catch (Exception $exception) {
            $error = $exception->getMessage();
        }

        return [
            'error' => $error ?? null,
            'schemes' => $schemes,
            'downPayment' => (float)$downPayment ?: null,
        ];
    }

    /**
     * @param $price_variant_id
     * @param $productIds
     * @param $price
     * @param int $downPayment
     * @param int $installment
     * @return object|null
     */
    #[\Override]
    public function getPriceVariantId($price_variant_id, $productIds, $price, $downPayment = 0, $installment = 0)
    {
        $data = $this->getPricingData($productIds, $price, $downPayment, $installment);
        foreach (!empty($data['schemes']) ? $data['schemes'] : [] as $scheme) {
            foreach ($scheme->variants as $variant) {
                if ($variant->PricingVariantId == $price_variant_id) {
                    return $variant;
                }
            }
        }

        return;
    }

    /**
     * @param float $price
     * @param array $goods
     * @param float|int $down_payment
     *
     * @return array
     * @throws \App\Exceptions\Error
     */
    #[\Override]
    public function getPricingSchemes($price, array $goods, $down_payment = 0): array
    {
        try {
            return $this->ucf->getPricingSchemes($price, $goods, $down_payment);
        } catch (Exception $exception) {
            throw new Error('Service error: ' . $exception->getMessage());
        }
    }

    /**
     * @param array $data
     * @return array
     * @throws \Throwable
     */
    #[\Override]
    protected function buildRequestEmail(array $data)
    {
        $request = parent::buildRequestEmail($data);
        $request['email'] = static::Email;
        return $request;
    }

    /**
     * @param Payments $payment
     * @return array
     */
    #[\Override]
    public function purchase(Payments $payment): array
    {
        $creditor = (new Creditor())->setProvider($payment->provider);

        $payment->status = 'pending';
        $return = $creditor->addPayment($payment);
        $return['action'] = [
            'type' => 'redirect',
            'url' => route('site.payment.redirect', $payment->id),
        ];

        return $return;
    }

    /**
     * @param Payments $payment
     * @return RedirectResponse|Response
     */
    public function redirect(Payments $payment): \Illuminate\Http\RedirectResponse|\Illuminate\Http\Response
    {
        $data = $payment->provider_data;

        $params = [
            'orderNo' => $payment->orderPayment->order_id,
            'clientFirstName' => $data->customer->firstname,
            'clientLastName' => $data->customer->lastname,
            'clientFullName' => $data->customer->firstname . ' ' . $data->customer->lastname,
//            'clientEGN' => $data->customer->personal_id,
            'clientPhone' => $data->customer->phone,
            'clientEmail' => $data->customer->email,
            'clientDeliveryAddress' => $data->customer->address->address_formatted,
            'onlineProductCode' => $data->PricingSchemeId,
            'totalPrice' => round($payment->amount / 100, 2),
            'initialPayment' => round($data->CorrectDownPaymentAmount, 2),
            'monthlyPayment' => round($data->InstallmentAmount, 2),
            'installmentCount' => $data->PricingVariantId,
            'returnURL' => route('payments.return', ['smart_ucf', 'ref' => $payment->provider_reference_id]),
            'items' => $this->getItems($payment),
        ];

        try {
            $suosId = $this->ucf->sessionStart($params);
        } catch (SmartUcfException $smartUcfException) {
            $response = (object)[
                'reqStatusCode' => $smartUcfException->getCode(),
                'reqStatusText' => $smartUcfException->getMessage(),
            ];
            $payment->provider_data = $this->updateProviderData($payment->provider_data, $response);
            $payment->status = $this->getStatus($response);
            $payment->sync();

            return new RedirectResponse(route('site.payment.return', $payment->id));
        }

        return new Response($this->ucf->redirect($suosId));
    }

    /**
     * @param Payments $payment
     * @param array $data
     * @throws \Exception
     */
    public function completePurchase(Payments $payment, array $data = []): void
    {
        $this->sync($payment);
    }

    /**
     * @param Payments $payment
     * @throws \Exception
     */
    public function sync(Payments $payment): void
    {
        if ('cancelled' == $payment->status) {
            return;
        }

        try {
            $response = $this->ucf->getInfo($payment->orderPayment->order_id);

            PaymentLogs::siteToProvider($payment, [], $response, 'sync');
        } catch (SmartUcfException $e) {
            $response = (object)[
                'reqStatusCode' => $e->getCode(),
                'reqStatusText' => $e->getMessage(),
            ];
        } catch (Exception $e) {
            throw $e;
        }

        $payment->provider_data = $this->updateProviderData($payment->provider_data, $response);
        $payment->status = $this->getStatus($response);
        $payment->sync();
    }

    /**
     * @param OrderPayment $payment
     * @throws Error
     * @throws \Mpdf\MpdfException
     * @throws \SmartyException
     * @throws \Throwable
     */
    public function sendInvoice(OrderPayment $payment): void
    {
        if(Invoicing::getActiveProvider() && $invoice = Invoicing::getInvoice($payment->order)) {
            try {
                $response = $this->ucf->sendInvoice([
                    'orderNo' => $payment->order->id,
                    'invoiceNo' => Invoicing::getNumberInvoice($payment->order),
                    'invoiceFile' => (object)[
                        'fileName' => 'invoice.pdf',
                        'fileContent' => base64_encode($invoice),
                    ],
                ]);
            } catch (SmartUcfException $smartUcfException) {
                $response = (object)[
                    'reqStatusCode' => $smartUcfException->getCode(),
                    'reqStatusText' => $smartUcfException->getMessage(),
                ];
            }

            $payment = $payment->payment;
            $payment->provider_data = $this->updateProviderData($payment->provider_data, $response);
            $payment->sync();
        }
    }

    /**
     * @param Payments $payment
     * @return array
     */
    protected function getItems(Payments $payment): array
    {
        $invoice = $payment->invoice;
        $items = [];
        $noIdItemsTotal = 0;

        foreach ($invoice['items'] as $item) {
            if (empty($item['id'])) {
                $noIdItemsTotal += $item['amount'];
                continue;
            }

            $items[] = (object)[
                'code' => $item['id'],
                'name' => $item['name'],
                'count' => (int)$item['quantity'],
                'singlePrice' => $item['amount'],
            ];
        }

        if ($noIdItemsTotal) {
            $amount = round($noIdItemsTotal / count($items), 2);
            foreach ($items as $item) {
                $item->singlePrice += $amount;
            }
        }

        if ((float)$invoice['shipping']) {
            $items[] = (object)[
                'code' => uniqid(),
                'name' => 'Доставка',
                'count' => 1,
                'singlePrice' => $invoice['shipping'],
            ];
        }

        return $items;
    }

    /**
     * @param $response
     * @return string
     */
    protected function getStatus($response): string
    {
        if ((int)($response->reqStatusCode ?? 0) > 65) {
            return 'cancelled';
        }

        if ((int)($response->reqStatusCode ?? 0) > 50) {
            return 'completed';
        }

        return 'pending';
    }

    /**
     * @param $providerData
     * @param $response
     * @return array|\stdClass
     */
    protected function updateProviderData($providerData, $response)
    {
        if (isset($response->onlineRequestInfo)) {
            return $response;
        }

        $providerData = (array)$providerData;
        if (isset($providerData['providerStatus'])) {
            unset($providerData['providerStatus']);
        }

        if (isset($response->reqStatusCode) && isset($response->reqStatusText)) {
            $providerData = \Illuminate\Support\Arr::prepend($providerData, $response->reqStatusCode . ': ' . $response->reqStatusText, 'providerStatus');
        }

        return $providerData;
    }
}
