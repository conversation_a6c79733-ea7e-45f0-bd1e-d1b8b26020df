<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 17.2.2017 г.
 * Time: 11:55
 */

namespace App\Integration\Payment\Iute;

use App\Exceptions\Error;
use App\Exceptions\Errors;
use App\Helper\Format;
use App\Integration\Payment\CreditCalculator;
use App\Integration\Payment\CreditorService;
use App\Models\Gateway\PaymentLogs;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\Payments;
use App\Services\Creditor;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use League\ISO3166\ISO3166;

/**
 * @method  completePurchase(Payments $payment, array $data = array())
 * @method  refund(Payments $payment)
 */
class IuteService extends CreditorService
{
    /**
     *
     */
    public const KEY = 'iute';

    protected \App\Integration\Payment\Iute\Client $client;

    /**
     *
     */
    public function __construct()
    {
        $this->config = $this->loadConfig();
        $this->client = new Client($this->config);
    }

    /**
     * @return array
     */
    protected function loadConfig(): array
    {
        $provider = PaymentProviderConfiguration::findByProvider(self::KEY);

        return $provider->configuration ?? [
            'mode' => 'test',
            'api_key_test' => '',
            'api_key' => '',
            'admin_api_key_test' => '',
            'admin_api_key' => '',
            'promo_button' => 0,
        ];
    }

    /**
     * @return mixed
     */
    public function getApiKey(): string
    {
        return $this->config['mode'] == 'test' ? $this->config['api_key_test'] : $this->config['api_key'];
    }

    /**
     * @return string
     */
    public function getBaseUrl(): string
    {
        return $this->client->getBaseUrl();
    }

    /**
     * @return string
     */
    public function getLang(): string
    {
        //        return $this->client->getLang();
        return locale();
    }

    /**
     * @return Client
     */
    public function getClient(): Client
    {
        return $this->client;
    }

    /**
     * @param $provider
     * @param $price
     * @param $productIds
     * @param $downPayment
     * @param $installment
     * @return string
     */
    public function renderPriceTable($provider, $price, $productIds, $downPayment = 0, $installment = 0): string
    {
        $path = dirname((new \ReflectionClass(static::class))->getFileName());

        return \Illuminate\Support\Facades\View::addNamespace($provider, $path . '/views')
            ->make(self::KEY . '::creditor_pricing_table', [
                'price' => $price,
                'product_id' => Arr::first($productIds),
            ])->render();
    }

    /**
     * @return array
     */
    public function getLoanProducts(): array
    {
        return $this->client->getLoanProducts();
    }

    /**
     * @param array $params
     * @return array
     */
    public function getProductMappings(array $params): array
    {
        return $this->client->getProductMappings($params);
    }

    /**
     * @param array $params
     * @return array
     */
    public function createProductMappings(array $params): array
    {
        return $this->client->createProductMappings($params);
    }

    /**
     * @param array $params
     * @return array|null
     */
    public function deleteProductMappings(array $params): ?array
    {
        return $this->client->deleteProductMappings($params);
    }

    /**
     * @param Payments $payment
     * @return array|Response
     * @throws Error
     * @throws Errors
     * @throws \Throwable
     */
    #[\Override]
    public function purchase(Payments $payment): \Illuminate\Http\Response|array
    {
        $sessionId = request()->input('iute_checkout_session_id');

        if (empty($sessionId)) {
            return $this->checkoutJs($payment);
        }

        $payment->provider_data = array_merge((array) $payment->provider_data, [
            'iute_checkout_session_id' => $sessionId,
        ]);

        $this->sync($payment);

        return [
            'payment' => [
                'payment_id' => $payment->id,
                'site_reference_id' => $payment->site_reference_id,
            ],
            'status' => $payment->status,
            'action' => false
        ];
    }

    /**
     * @param Payments $payment
     * @param array $data
     * @return Response
     */
    public function capturePurchase(Payments $payment, array $data = []): Response
    {
        $this->client->verifyWebhookSignature();

        $payment->provider_data = array_merge((array) $payment->provider_data, [
            'webhook' => array_merge(['date' => now()->toDateTimeString()], $data),
        ]);

        $this->sync($payment);

        PaymentLogs::providerToSite($payment, $data, ['OK'], 'ipn');

        return new Response('OK');
    }

    /**
     * @param Payments $payment
     * @return void
     */
    public function sync(Payments $payment): void
    {
        $orderId = $payment->getKey();

        try {
            $response = $this->client->getOrderStatus((string) $orderId);
        } catch (ClientException $clientException) {
            $response = json_decode($clientException->getResponse()->getBody()->getContents(), true);
        }

        PaymentLogs::siteToProvider($payment, ['orderId' => $orderId], $response, 'sync');
        $payment->status = $this->getStatusFromResponse($response);
        $payment->provider_data = array_merge((array) $payment->provider_data, [
            'sync' => array_merge(['date' => now()->toDateTimeString()], $response),
        ]);

        $payment->sync();
    }

    /**
     * @param array $response
     * @return string
     */
    protected function getStatusFromResponse(array $response): string
    {
        $status = $response['status'] ?? null;

        return match ($status) {
            'PAID', 'SIGNED' => Payments::STATUS_COMPLETED,
            'PENDING', 'IN PROGRESS' => Payments::STATUS_PENDING,
            default => Payments::STATUS_CANCELED,
        };
    }

    /**
     * @param Payments $payment
     * @return Response
     * @throws Error
     * @throws \App\Exceptions\Errors
     * @throws \Throwable
     */
    protected function checkoutJs(Payments $payment): Response
    {
        request()->offsetSet('creditData', ['provider' => self::KEY]);
        $creditor = (new Creditor())->setProvider($payment->provider);
        $creditor->request = (object) $payment->toArray();

        $payment->provider_data = $creditor->setCreditorDataFromRequest();
        $payment->status = Payments::STATUS_INITIATED;
        $payment->provider_reference_id = $payment->getKey();
        $payment->sync();

        $request = array_merge([
            'merchant' => [
                'userConfirmationUrl' => route('site.payment.webhook', $payment->id),
                //                'userConfirmationUrl' => 'https://webhook.site/24f60b71-032c-4431-bfa2-3e11d90f0237',
//                'userCancelUrl' => 'https://webhook.site/24f60b71-032c-4431-bfa2-3e11d90f0237',
                'userCancelUrl' => route('site.payment.webhook', $payment->id),
                'userConfirmationUrlAction' => 'POST',
            ],
            'shipping' => $this->getCustomerAddress($payment),
            'billing' => $this->getCustomerAddress($payment),
            'items' => $this->getItems($payment),
        ], $this->getOrderTotals($payment));

        $path = dirname((new \ReflectionClass(static::class))->getFileName());

        $js = \Illuminate\Support\Facades\View::addNamespace(self::KEY, $path . '/views')
            ->make(self::KEY . '::checkout', [
                'request' => $request,
            ])->render();

        PaymentLogs::siteToProvider($payment, $request);

        return new Response([
            'status' => 'success',
            'order_id' => $payment->orderPayment->order_id,
            'js' => $js,
        ]);
    }

    /**
     * @param Payments $payment
     * @return array
     * @throws Error
     */
    protected function getItems(Payments $payment): array
    {
        $order = $payment->order_payment->order;

        $items = [];

        foreach ($order->products as $orderProduct) {
            $items[] = [
                'sku' => $orderProduct->product_id,
                'displayName' => $orderProduct->name,
                'qty' => $orderProduct->quantity,
                'unitPrice' => $orderProduct->order_price_input,
                'itemImageUrl' => $orderProduct->getImage(),
                'itemUrl' => $orderProduct->url,
            ];
        }

        return $items;
    }

    /**
     * @param Payments $payment
     * @return array
     */
    protected function getCustomerAddress(Payments $payment): array
    {
        $customer = $payment->provider_data->customer;
        $iso = new ISO3166();
        try {
            $alpha3 = $iso->name($customer->address->country)['alpha3'];
        } catch (\Throwable) {
            $alpha3 = 'BGR';
        }

        return [
            'name' => [
                'first' => $customer->firstname,
                'last' => $customer->lastname,
            ],
            'address' => [
                'line1' => $customer->address->address_formatted,
                'city' => $customer->address->city,
                'zipcode' => $customer->address->postal_code,
                'country' => $alpha3,
            ],
            'phoneNumber' => $customer->phone,
            'email' => $customer->email,
        ];
    }

    /**
     * @param Payments $payment
     * @return array
     */
    protected function getOrderTotals(Payments $payment): array
    {
        $data = $payment->invoice;

        return [
            'orderId' => $payment->getKey(),
            'currency' => $payment->currency,
            'shippingAmount' => $data['shipping'] ?? 0,
            'taxAmount' => $data['tax'] ?? 0,
            'subtotal' => $data['subtotal'],
            'total' => $data['total'],
        ];
    }

    /**
     * @param float $price
     * @param array $goods
     * @param int $scheme_id
     * @param int $down_payment
     * @param int $installment
     * @return array
     * @throws Error
     */
    #[\Override]
    public function getPricingVariants($price, array $goods, $scheme_id, $down_payment = 0, $installment = 0): array
    {
        $configuration = [
            'min' => 3,
            'max' => 3,
            'step' => 1,
        ];

        $percentPerMonth = !empty($configuration['percentPerMonth']) ? $configuration['percentPerMonth'] : static::PercentPerMonth;
        $freeLeasing = !empty($configuration['free_leasing']) ? array_filter(array_map('trim', explode(',', $configuration['free_leasing']))) : [];


        //        $discounted = LeasingDiscount::discounted($goods, static::KEY);

        $variants = [];
        //        foreach ($meta['months'] as $month) {
        for ($month = $configuration['min']; $month <= $configuration['max']; $month = $month + $configuration['step']) {
            //   $calculator = new CreditCalculator(in_array($month, $freeLeasing) ? 0 : $percentPerMonth);
            $calculator = new CreditCalculator($percentPerMonth);
            //dd($calculator);
            $result = $calculator->calc($price, $month, $down_payment);
            $obhstaDuljimaSuma = round($result['obhstaDuljimaSuma'], 2);
            $NIR = $result['glp'] * 100;
            $APR = $result['gpr'] * 100;
            $variants[] = (object) [
                'PricingSchemeId' => '',
                'PricingSchemeName' => $month . ' ' . __('sf.global.months'),
                'PricingVariantId' => $month,
                'Maturity' => $month,
                'InstallmentAmount' => $result['mesechnaVnoska'],
                'InstallmentAmountFormatted' => Format::money(Format::toIntegerPrice($result['mesechnaVnoska'])),
                'CorrectDownPaymentAmount' => $down_payment,
                'CorrectDownPaymentAmountFormatted' => Format::money(Format::toIntegerPrice($down_payment)),
                'NIR' => $NIR,
                'NIRFormatted' => Format::percent(Format::toIntegerPrice($NIR)),
                'APR' => $APR,
                'APRFormatted' => Format::percent(Format::toIntegerPrice($APR)),
                'TotalRepaymentAmount' => $obhstaDuljimaSuma,
                'TotalRepaymentAmountFormatted' => Format::money(Format::toIntegerPrice($obhstaDuljimaSuma)),
            ];
        }

        if (!is_numeric($installment)) {
            $installment = 0;
        }

        // set free leasing if it is available
        foreach ($freeLeasing as $month) {
            if ($configuration['max'] >= $month) {
                $calculatorFreeLeasing = new CreditCalculator(0);
                $resultFreeLeasing = $calculatorFreeLeasing->calc($price, $month, $down_payment);
                $obhstaDuljimaSumaFreeLeasing = round($resultFreeLeasing['obhstaDuljimaSuma'], 2);
                $NIRFreeLeasing = $resultFreeLeasing['glp'] * 100;
                $APRFreeLeasing = $resultFreeLeasing['gpr'] * 100;
                $variants['free_leasing'][] = (object) [
                    'PricingSchemeId' => '',
                    'PricingSchemeName' => $month . ' ' . __('sf.global.months'),
                    'PricingVariantId' => $month,
                    'Maturity' => $month,
                    'InstallmentAmount' => $resultFreeLeasing['mesechnaVnoska'],
                    'InstallmentAmountFormatted' => Format::money(Format::toIntegerPrice($resultFreeLeasing['mesechnaVnoska'])),
                    'CorrectDownPaymentAmount' => $down_payment,
                    'CorrectDownPaymentAmountFormatted' => Format::money(Format::toIntegerPrice($down_payment)),
                    'NIR' => $NIRFreeLeasing,
                    'NIRFormatted' => Format::percent(Format::toIntegerPrice($NIRFreeLeasing)),
                    'APR' => $APRFreeLeasing,
                    'APRFormatted' => Format::percent(Format::toIntegerPrice($APRFreeLeasing)),
                    'TotalRepaymentAmount' => $obhstaDuljimaSumaFreeLeasing,
                    'TotalRepaymentAmountFormatted' => Format::money(Format::toIntegerPrice($obhstaDuljimaSumaFreeLeasing)),
                ];
            }
        }


        if ($installment > 0) {
            $iMax = $installment * (1 + 20 / 100);
            $iMin = $installment * (1 - 20 / 100);
            foreach ($variants as $key => $variant) {
                if (is_array($variant)) {
                    foreach ($variant as $key2 => $variant2) {
                        if ($variant2->InstallmentAmount > $iMax || $variant2->InstallmentAmount < $iMin) {
                            unset($variant[$key][$key2]);
                        }
                    }
                } else {
                    if ($iMax < $variant->InstallmentAmount || $iMin > $variant->InstallmentAmount) {
                        unset($variants[$key]);
                    }
                }
            }

            if (empty($variants)) {
                throw new Error(__('sf.leasing.err.scheme.not_found'));
            }
        }

        return $variants;
    }
}
