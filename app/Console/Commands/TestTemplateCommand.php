<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\LiquidEngine\Services\TemplateLoader;
use App\Models\Router\Site;

class TestTemplateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:template {template? : The template to test} {--all : Test all template variations} {--site_id=5126 : The site ID to use for testing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test template loading';

    /**
     * Execute the console command.
     */
    public function handle(TemplateLoader $loader)
    {
        $this->info('Testing template loading...');
        
        // Set up the site for testing
        $siteId = $this->option('site_id');
        $this->setupSite($siteId);
        
        // Test data
        $data = [
            'title' => 'Test Page',
            'meta_description' => 'Testing template loading',
            'page_title' => 'Test Page',
            'content' => 'This is a test of the template loading system.',
            'page' => [
                'title' => 'Test Page',
                'content' => '<p>This is the page content.</p>'
            ]
        ];
        
        if ($this->option('all')) {
            // Templates to test
            $templates = [
                'page',
                'templates.page',
                'templates/page',
                'default',
                'templates.default',
                'templates/default'
            ];
            
            foreach ($templates as $template) {
                $this->testTemplate($loader, $template, $data);
            }
        } else {
            $template = $this->argument('template') ?: 'page';
            $this->testTemplate($loader, $template, $data);
        }
        
        return Command::SUCCESS;
    }
    
    /**
     * Set up the site for testing
     */
    protected function setupSite(int $siteId): void
    {
        try {
            $site = Site::find($siteId);
            
            if (!$site) {
                $this->error("Site with ID {$siteId} not found.");
                exit(1);
            }
            
            // Manually set the site instance in the application
            app()->instance('site', $site);
            
            // Boot the database connection for the site
            if (method_exists($site, 'bootDB')) {
                $site->bootDB();
            }
            
            $this->info("Using site: {$site->name} (ID: {$site->id})");
            $this->info("Theme key: {$site->theme->key}");
            
        } catch (\Exception $e) {
            $this->error("Error setting up site: " . $e->getMessage());
            $this->line($e->getTraceAsString());
            exit(1);
        }
    }
    
    /**
     * Test a specific template
     */
    protected function testTemplate(TemplateLoader $loader, string $template, array $data): void
    {
        $this->info("Testing template: {$template}");
        
        try {
            $result = $loader->load($template, $data);
            
            if ($result) {
                $this->info("SUCCESS: Template loaded successfully");
                
                // Check if result is a response object or a Liquid Factory
                if (method_exists($result, 'getContent')) {
                    // It's a Response object
                    $this->line("Template content length: " . strlen($result->getContent()));
                    $this->line("Response type: " . get_class($result));
                } else {
                    // It's a Liquid Factory
                    $this->line("Template content length: " . strlen($result->render()));
                    $this->line("Factory type: " . get_class($result));
                }
            } else {
                $this->error("FAILURE: Template not found");
            }
        } catch (\Exception $e) {
            $this->error("ERROR: " . $e->getMessage());
            $this->line($e->getTraceAsString());
        }
        
        $this->newLine();
    }
}
