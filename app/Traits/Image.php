<?php

declare(strict_types=1);

namespace App\Traits;

use App\Exceptions\ConsoleAccessDeniedByPlan;
use App\Helper\Encode;
use App\Helper\UrlImageToFile;
use App\Jobs\ImageProcessing;
use App\Models\Queue\SiteQueue;
use App\Models\Setting\Logo;
use ErrorException;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Arr;
use App\Common\Media;
use App\Exceptions\Error;
use App\Models\System\Storage as StorageSize;
use App\Helper\Plan;
use Carbon\Carbon;
use App\Helper\Image as HelperImage;
use Illuminate\Support\Str;
use ImagickException;
use Illuminate\Support\Facades\Storage;
use App\Models\Product\Image as ImageModel;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Throwable;

/**
 * Trait Image
 *
 * @package App\Traits
 *
 * @property-read null|string $extension
 * @property-read bool $allow_edit
 */
trait Image
{
    use NewImage;

    /**
     * @var array
     */
    protected static $_cache = [];

    /**
     * @var HelperImage
     */
    protected $_helper_image_upload;

    /**
     * @var int
     */
    public $file_size = 0;

    protected static $filename = 'image';

    /**
     * @param mixed $query
     * @param mixed $filename
     * @return mixed
     */
    public function scopeFileName(/** @noinspection PhpUnusedParameterInspection */ $query, $filename): void
    {
        static::$filename = $filename;
    }

    protected function getExtensionAttribute(): ?string
    {
        if (empty($name = $this->getImageFileName())) {
            return null;
        }

        return strtolower(pathinfo((string) $name, PATHINFO_EXTENSION));
    }

    protected function getAllowEditAttribute(): bool
    {
        if (empty($this->extension)) {
            return false;
        }

        return in_array($this->extension, [
            'jpg',
            'jpeg',
            'png',
            'gif'
        ]);
    }

    /**
     * @return HelperImage
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getHelper()
    {
        if (is_null($this->_helper_image_upload)) {
            $this->_helper_image_upload = new HelperImage();
        }

        return $this->_helper_image_upload
            ->setIsSquare(setting(sprintf('%s_image_type', config('image.map.' . get_class($this) . '.type')), 'original') == 'square');
    }

    /**
     *
     */
    public static function bootImage(): void
    {
        static::deleted(function ($item): void {
            /** @var Image $item */
            if ($item->hasImage()) {
                $item->removeImages();
            }
        });


        static::deleting(function ($item): void {
            /** @var Image $item */
            if (!$item->hasStorageUsage()) {
                return;
            }

            $item->storage()->delete();
        });

        static::saved(function ($item): void {
            if (!$item->hasStorageUsage()) {
                if (!$item->getImageFileName() && $item->hasImage()) {
                    $item->removeImages();
                }

                return;
            }

            /** @var Image $item */
            if ($item->file_size) {
                $item->storage()->delete();
                $item->storage()->create([
                    'size' => $item->file_size
                ]);
            }

            if (!$item->getImageFileName() && $item->hasImage()) {
                $item->storage()->delete();
                $item->removeImages();
            }
        });
    }

    /**
     * @return int
     */
    protected function getImageTime()
    {
        if ($this->usesTimestamps() && $this->{$this->getUpdatedAtColumn()}) {
            $timestamp = $this->{$this->getUpdatedAtColumn()};
            return is_string($timestamp) ? $timestamp : $timestamp->getTimestamp();
        }

        return max(app('last_build'), setting('stylesheet_version'));
    }

    /**
     * @return int
     */
    protected function getImageId()
    {
        $image_id = config('image.map.' . get_class($this) . '.image_id_field');
        return $this->{$image_id};
    }

    /**
     * @return bool
     */
    public function expectedProcessing(): bool
    {
        return (bool) config('image.map.' . get_class($this) . '.expected_processing', false);
    }

    /**
     * @return int
     */
    public function getImageProcessed(): int
    {
        $image_processed = $this->getImageProcessedField();
        if (!$image_processed) {
            return 1;
        }

        return (int) $this->{$image_processed};
    }

    /**
     * @return null|string
     */
    public function getImageProcessedField(): ?string
    {
        return config('image.map.' . get_class($this) . '.image_processed', 'image_processed');
    }

    /**
     * @return int
     */
    public function filenameForSave()
    {
        return $this->getImageId();
    }

    /**
     * @return null|array
     */
    public function exifData(): null
    {
        return null;
    }

    /**
     * @return \Illuminate\Config\Repository|mixed
     */
    public function hasStorageUsage()
    {
        return config('image.map.' . get_class(new static()) . '.storage', true);
    }

    /**
     * @return \Illuminate\Config\Repository|mixed
     */
    public function getImageFieldName()
    {
        return config('image.map.' . get_class($this) . '.image_field');
    }

    /**
     * @return string|null
     */
    public function getImageFileName()
    {
        return $this->{$this->getImageFieldName()};
    }

    /**
     * @return string
     * @throws Error
     */
    public function getImagePath()
    {
        if (empty($path = config('image.map.' . get_class($this) . '.image_path'))) {
            throw new Error(sprintf('Image path for "%s" is not defined!', static::class));
        }

        return $path;
    }

    /**
     * @return string
     * @throws Error
     */
    protected function getImageUrlPath(): string
    {
        return config('url.storage') . implode('/', [
            $this->hasStorageUsage() ? site('site_id') : 'all',
            $this->getImagePath(),
            $this->getImageId(),
        ]);
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function getImageDirPath(): string
    {
        if ($this->hasStorageUsage()) {
            return site_dir($this->getImagePath() . '/' . $this->getImageId());
        }

        return implode('/', [
            'all',
            $this->getImagePath(),
            $this->getImageId(),
        ]);
    }

    /**
     * @return bool
     */
    public function hasImage(): bool
    {
        return 0 < (int) $this->max_thumb_size;
    }

    /**
     * @param $file
     *
     * @return null
     */
    public static function getMaxThumbSizeFromFile($file): ?string
    {
        if (preg_match('~(.*)x([\d]{1,})$~', pathinfo((string) $file, PATHINFO_FILENAME), $match)) {
            return $match[2];
        }

        return null;
    }

    /**
     * @return mixed
     */
    public static function getMaxThumbSize(): mixed
    {
        $thumb_sizes = array_values(config('image.thumb_size'));
        return end($thumb_sizes);
    }

    /**
     * @param null $thumb_size
     *
     * @return string
     * @throws \App\Exceptions\Error
     */
    public function getImage($thumb_size = null)
    {
        if (!$thumb_size || strtolower($thumb_size) == 'original') {
            $thumb_size = static::getMaxThumbSize();
        }

        $thumb_sizes = array_flip(config('image.thumb_size'));
        if (!isset($thumb_sizes[$thumb_size])) {
            throw new Error('Unknown thumb size: ' . $thumb_size);
        }

        if (
            !$this->hasImage() ||
            // @todo check and fix this condition
            !in_array($this->max_thumb_size, array_values($thumb_sizes))
        ) {
            $type = config('image.map.' . get_class($this) . '.type');
            /** @todo missing return after this line */
            if (isset(static::$_cache[$type][$thumb_size ?: 'default'])) {
                return static::$_cache[$type][$thumb_size ?: 'default'];
            }

            if (in_array($type, ['admin'])) {
                $letters = implode('', array_filter(array_map(fn($name) => Str::substr($name, 0, 1), [$this->first_name, $this->last_name])));
                if (empty($letters)) {
                    $letters = Str::substr($this->username ?: $this->email, 0, 1);
                }

                //<feOffset dx="6.5" dy="6.5" in="shadow" result="offset-2"></feOffset>
                return sprintf('data:image/svg+xml;base64,%s', base64_encode('<svg width="100%" height="100%" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg" style="background:#432f74"><defs><filter id="hover" ><feFlood flood-color="#052b4a" result="flood"></feFlood><feComposite operator="in" in2="SourceAlpha" in="flood" result="shadow"></feComposite><feOffset dx="-0.5" dy="5.5" in="SourceGraphic" result="offset-1"></feOffset><feMerge><feMergeNode in="offset-2"></feMergeNode><feMergeNode in="offset-1"></feMergeNode></feMerge></filter></defs><text dominant-baseline="middle" text-anchor="middle" x="50%" y="50%" fill="#d8c2dc" filter="url(#hover)" style="font-size: 64px; font-family:Roboto,sans-serif;">' . $letters . '</text></svg>'));
            } elseif (in_array($type, ['customer'])) {
                return Media::gravatar($this->email, $thumb_size);
            } else {
                return static::$_cache[$type][$thumb_size ?: 'default'] = Media::getDefaultImage($thumb_size, $type);
            }
        }

        $url = implode('/', [$this->getImageUrlPath(), $this->getImageFileName()]);

        if ($this->expectedProcessing() && !$this->getImageProcessed()) {
            $ext = pathinfo($url, PATHINFO_EXTENSION);
            return preg_replace(sprintf('~\.%s$~', $ext), '', $url) . sprintf('_original.%s?', $ext) . $this->getImageTime();
        }

        $key = $thumb_sizes[$thumb_size];
        if ($key > $this->max_thumb_size) {
            $thumb_sizes = config('image.thumb_size');
            $thumb_size = $thumb_sizes[$this->max_thumb_size];
        }

        $ext = pathinfo($url, PATHINFO_EXTENSION);
        if (!in_array(strtolower($ext), ['ico', 'svg', 'webp'])) {
            $url = preg_replace(sprintf('~\.%s$~', $ext), '', $url) . sprintf('_%s.%s?', $thumb_size, $ext) . $this->getImageTime();
        } else {
            $url .= "?" . $this->getImageTime();
        }

        return $url;
    }

    /**
     * @param null $thumb_size
     *
     * @return null|\stdClass
     * @throws \App\Exceptions\Error
     */
    public function getDimensions($thumb_size = null)
    {
        if (!$thumb_size) {
            $thumb_size = static::getMaxThumbSize();
        }

        $thumb_sizes = array_flip(config('image.thumb_size'));
        if (!isset($thumb_sizes[$thumb_size])) {
            throw new Error('Unknown thumb size: ' . $thumb_size);
        }

        if (!$this->hasImage() || !in_array($this->max_thumb_size, array_values($thumb_sizes)) || !$this->width || !$this->height) {
            return null;
        }

        [$width, $height] = explode('x', (string) $thumb_size);

        $scale = min($width / $this->width, $height / $this->height);

        return (object) [
            'width' => ceil($this->width * $scale),
            'height' => ceil($this->height * $scale),
        ];
    }

    /**
     * @return string
     */
    public function getOrientation(): string
    {
        if (!$this->hasImage() || !$this->width || !$this->height) {
            return 'square';
        }

        if ($this->width == $this->height) {
            return 'square';
        }

        return $this->width > $this->height ? 'horizontal' : 'vertical';
    }

    /**
     * @return string
     */
    public function getAspectRatioAttribute(): ?string
    {
        if (!$this->hasImage() || $this->width <= 0 || $this->height <= 0) {
            return null;
        }

        return getAspectRatio($this->width, $this->height);
    }

    /**
     * @param null $thumb_size
     *
     * @return null|string
     * @throws \App\Exceptions\Error
     */
    public function getHtmlDimensions($thumb_size = null): ?string
    {
        if (!$sizes = $this->getDimensions($thumb_size)) {
            return null;
        }

        return sprintf('width="%d" height="%d"', $sizes->width, $sizes->height);
    }

    /**
     * @param null $thumb_size
     *
     * @return null|string
     * @throws \App\Exceptions\Error
     */
    public function getCssDimensions($thumb_size = null): ?string
    {
        if (!$sizes = $this->getDimensions($thumb_size)) {
            return null;
        }

        return sprintf('width: %dpx; height: %dpx;', $sizes->width, $sizes->height);
    }

    public function getHtmlSrcSet(): ?string
    {
        if (!$this->hasImage()) {
            return null;
        }

        $images = array_unique($this->getImages());
        if (array_key_exists('original', $images)) {
            unset($images['original']);
        }

        $tmp = [];
        foreach ($images as $key => $image) {
            [$width, $height] = explode('x', $key);
            $tmp[] = sprintf('%s %sw', $image, $width);
        }

        return sprintf('srcset="%s"', implode(', ', $tmp));
    }

    /**
     * @param null $thumb_size
     *
     * @return string
     * @throws \App\Exceptions\Error
     */
    public function emptyImage($thumb_size = null)
    {
        if (!$thumb_size) {
            $thumb_size = static::getMaxThumbSize();
        }

        $thumb_sizes = array_flip(config('image.thumb_size'));
        if (!isset($thumb_sizes[$thumb_size])) {
            throw new Error('Unknown thumb size: ' . $thumb_size);
        }

        $type = config('image.map.' . get_class($this) . '.type');
        /** @todo missing return after this line */
        if (isset(static::$_cache[$type][$thumb_size ?: 'default'])) {
            return static::$_cache[$type][$thumb_size ?: 'default'];
        }

        if (in_array($type, ['admin', 'customer'])) {
            return Media::gravatar($this->email, $thumb_size);
        } else {
            return static::$_cache[$type][$thumb_size ?: 'default'] = Media::getDefaultImage($thumb_size, $type);
        }
    }

    /**
     * @return array
     * @throws \App\Exceptions\Error
     */
    public function getImages(): array
    {
        $thumb_sizes = config('image.thumb_size');
        $images = [];

        foreach ($thumb_sizes as $thumb_size) {
            $images[$thumb_size] = $this->getImage($thumb_size);
        }

        $images['original'] = $this->getImage();

        return $images;
    }

    /**
     * Get image local path for all sizes
     *
     * @param null $size
     *
     * @return array|string
     * @throws Error
     * @throws \Exception
     */
    public function getImagePaths($size = null)
    {
        if (!$this->hasImage()) {
            return [];
        }

        $thumb_sizes = config('image.thumb_size');
        $images = [];

        foreach ($thumb_sizes as $thumb_size) {
            $images[$thumb_size] = $this->getImage($thumb_size);
        }

        $images['original'] = $this->getImage();

        foreach ($images as &$image) {
            $image = str_replace(
                '\\',
                '/',
                explode('?', str_replace($this->getImageUrlPath(), $this->getImageDirPath(), $image))[0]
            );
        }

        return $size ? ($images[$size] ?? null) : $images;
    }

    /**
     * Get image local path for all sizes
     *
     * @return array|string
     * @throws Error
     * @throws \Exception
     */
    public function getImageOriginal(): null|string|false
    {
        if (!$this->hasImage()) {
            return null;
        }

        $images = [$this->getImage()];
        if (preg_match('~^(.+)_(\d+)x(\d+)(\.(.+))$~', (string) $images[0], $match)) {
            $images = \Illuminate\Support\Arr::prepend($images, implode('', [$match[1], '_original', $match[4]]));
        }

        $image = null;
        foreach ($images as $url) {
            if ($content = @file_get_contents($url)) {
                return $content;
            }
        }

        return @file_get_contents($this->getImage());
    }

    /**
     * @return bool
     * @throws Error
     */
    public function hasImageOnStore()
    {
        if (!$this->exists || empty($paths = $this->getImagePaths())) {
            return false;
        }

        return Storage::exists(Arr::last($paths));
    }

    /**
     * @throws Error
     * @throws Throwable
     */
    public function removeImages(): void
    {
        if ($this::class == ImageModel::class) {
            $paths = array_unique($this->getImagePaths());
            foreach ($paths as $path) {
                SiteQueue::executeQueueTask('delete_storage_file', ['file' => $path]);
            }
        } elseif ($this instanceof Logo) {
            foreach ($this->getImagePaths() as $image) {
                try {
                    Storage::delete($image);
                } catch (ErrorException) {
                }
            }
        } else {
            try {
                Storage::deleteDirectory($this->getImageDirPath());
            } catch (ErrorException) {
                // file doesn't exist in google cloud storage
            }
        }
    }

    /**
     * @return null
     */
    protected function uploadGetFile()
    {
        return files(static::$filename);
    }

    /**
     * @param $key
     * @param string $in
     * @return array|null
     */
    protected function uploadGetFileByKeyIn($key = null, string $in = 'options'): ?array
    {
        $files = files($in);
        if (isset($files['name'][$key][static::$filename])) {
            return [
                'name' => $files['name'][$key][static::$filename],
                'type' => $files['type'][$key][static::$filename],
                'tmp_name' => $files['tmp_name'][$key][static::$filename],
                'error' => $files['error'][$key][static::$filename],
                'size' => $files['size'][$key][static::$filename],
            ];
        }

        return null;
    }

    /**
     * @param $file
     *
     * @throws ConsoleAccessDeniedByPlan
     * @throws Error
     */
    protected function uploadCheckStorageSize($file)
    {
        if (empty($file)) {
            return;
        }

        $storage = Plan::storageRemaining();
        $storage['remaining'] -= $file['size'] * 2;
        if ($storage['remaining'] <= 0) {
            if (app()->runningInConsole()) {
                $e = new ConsoleAccessDeniedByPlan('storage');
                $e->withLocale(site('language_cp'), function () use ($e): void {
                    $e->setMessage(__('global.not_enough_space_in_storage'));
                });
                throw $e;
            }

            throw new Error(__('global.not_enough_space_in_storage'));
        }
    }

    /**
     * @param $file
     * @throws Error
     * @throws ImagickException
     */
    public function uploadPrepare($file): void
    {
        //remove old images
        $this->removeImages();

        //create necessary directories
        //        Storage::makeDirectory($this->getImageDirPath(), 0755, true, true);

        $this->getHelper()->setImage($file);
    }

    /**
     * @return $this
     * @throws Error
     * @throws \Exception
     */
    public function uploadThumbs(?string $filename = null)
    {
        $this->getHelper()->setThumbnailSettings(array_reverse(config('image.thumb_size'), true));
        $this->getHelper()->setExifData($this->exifData());

        $filename = pathinfo($filename ?: (string) $this->filenameForSave(), PATHINFO_FILENAME);

        $this->info_if('Begin upload thumbs!');
        [$max_thumb_size, $total_file_size] = $this->getHelper()->saveImage($this->getImageDirPath() . '/' . $filename);
        $this->info_if('End upload thumbs!');
        $this->file_size = $total_file_size;

        if (in_array('width', $this->fillable)) {
            $this->fill([
                'width' => $this->getHelper()->getWidth()
            ]);
        }

        if (in_array('height', $this->fillable)) {
            $this->fill([
                'height' => $this->getHelper()->getHeight()
            ]);
        }

        if ($this->getHelper()->getIsSquare() && in_array('width', $this->fillable) && in_array('height', $this->fillable)) {
            $this->fill([
                'width' => ($max = max($this->getHelper()->getWidth(), $this->getHelper()->getHeight())),
                'height' => $max
            ]);
        }

        $this->forceFill([
            $this->getImageFieldName() => $filename . '.' . $this->getHelper()->getExtension(),
            'max_thumb_size' => $max_thumb_size
        ]);
        $this->info_if('End fill dimensions and other data!');

        if ($this->timestamps) {
            $this->setUpdatedAt(Carbon::now());
        }

        $this->save();
        $this->info_if('Save!');

        //        if ($this instanceof ImageModel) {
        //            SiteQueue::executeQueueTask('product_image_color', ['image_id' => $this->id], null, 15);
        //        }

        return $this;
    }

    /**
     * @return Image
     * @throws \Exception
     * @throws \App\Exceptions\Error
     */
    public function uploadImage(array $attributes = [])
    {
        if (!$file = $this->uploadGetFile()) {
            return $this;
        }

        $filename = $attributes['name'] ?? $file['name'] ?? null;

        return $this->uploadImageFromFile($file, $filename);
    }

    /**
     * @param null|mixed $key
     * @return Image
     * @throws \App\Exceptions\Error
     * @throws \Exception
     */
    public function uploadImageByKeyIn($key = null, string $in = 'options')
    {
        if (!$file = $this->uploadGetFileByKeyIn($key, $in)) {
            return $this;
        }

        return $this->uploadImageFromFile($file);
    }

    /**
     * @param $file
     * @param string|null $filename
     * @return Image
     * @throws ConsoleAccessDeniedByPlan
     * @throws Error
     * @throws Throwable
     * @throws ImagickException
     */
    public function uploadImageFromFile($file, ?string $filename = null)
    {
        $paths = [];
        $this->info_if('Begin check storage size!');
        $this->uploadCheckStorageSize($file);
        $this->info_if('End check storage size!');
        if ($this instanceof ImageModel) {
            $this->getHelper()->setImage($file);
            $paths = $this->getImagePaths();
        } else {
            $this->uploadPrepare($file);
        }

        $this->info_if('End prepare!');

        $this->uploadThumbs($filename);

        foreach ($paths as $path) {
            SiteQueue::executeQueueTask('delete_storage_file', ['file' => $path]);
        }

        return $this;
    }

    /**
     * @param $url
     * @param string $name
     *
     * @param array $fields
     * @return Image|null
     */

    public function uploadImageFromUrl($url, $name = 'image', array $fields = [])
    {
        //        $downloader = new UrlImageToFile($url, $name);
        //        if ($downloader->download()) {
        //            return $this->uploadImage(null, null);
        //        }
        //        return null;
        return SiteQueue::executeQueueTask('image_from_url', [
            'record_id' => $this->id,
            'record_type' => $this::class,
            'image_url' => Encode::urlEncode($url),
            'field_name' => $name,
            'fields' => $fields
        ]);
    }

    /**
     * @param $url
     * @param string $name
     * @param array $fields
     * @throws Error
     */
    public function uploadImageFromUrlNow($url, $name = 'image', array $fields = []): void
    {
        $downloader = new UrlImageToFile($url, $name);
        if ($downloader->download()) {
            $this->uploadImage();
        }
    }

    /**
     * @return MorphOne|StorageSize
     */
    public function storage()
    {
        return $this->morphOne(StorageSize::class, 'item');
    }

    /**
     * @param string $image
     * @return array
     */
    public function getImageDimensions($image): array
    {
        $imageSize = getimagesize($image);
        return [
            'width' => $imageSize[0] ?? null,
            'height' => $imageSize[1] ?? null,
        ];
    }

    /**
     * @param mixed $message
     * @return mixed
     */
    private function info_if($message): void
    {
        if (!app()->runningInConsole()) {
            return;
        }

        $processing = new ImageProcessing(0, '');
        $processing->info($message);
    }
}
