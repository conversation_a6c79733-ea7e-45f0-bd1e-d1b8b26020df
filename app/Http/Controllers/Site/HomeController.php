<?php

declare(strict_types=1);

namespace App\Http\Controllers\Site;

use App\Integration\Payment\Paysera\PayseraService;
use App\LiquidEngine\Assetics\Subscribers\HomePage;
use App\Helper\CustomHomePage;
use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Str;
use App\LiquidEngine\Traits\ThemeBreadcrumbs;
use PaymentGateway;

class HomeController extends Controller
{
    use ThemeBreadcrumbs;

    /**
     * @param Request $request
     * @return ResponseFactory|Response
     */
    public function index(Request $request)
    {
        if ($request->userAgent() == 'Paysera Bot') {
            /** @var PayseraService $gateway */
            $gateway = PaymentGateway::provider('paysera');
            return $gateway->verifyWebsite();
        }

        return !isLiquidEngine() ? $this->indexSmartyEngine($request) : $this->indexLiquidEngine($request);
    }

    public function ccPhpInfo(): Response
    {
        ob_start();
        phpinfo();
        $content = ob_get_clean();

        $content = preg_replace_callback(
            '#<tr><td class="e">(.*?)</td><td class="v">(.*?)</td></tr>#',
            function ($match) {
                $key = trim($match[1]);
                $value = $match[2];
                if (Str::contains($key, ['PASS', 'HOST', 'GOOGLE', 'DATABASE', 'USERNAME', 'FACEBOOK_APP', 'FTP', 'GA4', 'ELASTIC_', 'PBX_', 'CLOUDFLARE_', 'ALGOLIA_', 'NAMECOM_', 'INFOBIP_', 'SMSNTH_', 'VAPID_', 'USERS_WEBHOOK_KEY', 'SENTRY_', 'MAXMIND_', 'INTEGRATIONS_EBAY'])) {
                    $value = '*********';
                }
                return '<tr><td class="e">' . $key . '</td><td class="v">' . $value . '</td></tr>';
            },
            $content
        );

        return response($content);
    }

    public function opCache()
    {
        return response()->json(opcache_get_status(true));
    }

    private function indexSmartyEngine(Request $request)
    {
        if (CustomHomePage::showCustomHomePage($request) && ($page = CustomHomePage::getSystemHomePage())) {
            $pageController = new PageController();
            $pageController->setPage($page);

            $response = response($pageController->get());
        }

        if (!isset($response)) {
            if (isLiquidEngine()) {
                return new PageController()->testLiquidTheme('index', $wildcart = '', $data = []);
            }
            $response = \Illuminate\Support\Facades\View::mainResponse('home.home');
        }

        return $response;
    }

    private function indexLiquidEngine(Request $request)
    {
        if (CustomHomePage::showCustomHomePage($request) && ($page = CustomHomePage::getSystemHomePage())) {
            $pageController = new PageController();
            $pageController->setPage($page);

            return response($pageController->get());
        }

        $data = [
            'template' => new Template('index'),
        ];

        $this->breadcrumbs()->add(
            __('pages.home'),
            route('site.home')
        );

        event('controller:homePage', new HomePage());

        return response(liquid('templates/index', $data));
    }
}
