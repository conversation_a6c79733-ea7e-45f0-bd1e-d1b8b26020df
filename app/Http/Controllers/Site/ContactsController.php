<?php

declare(strict_types=1);

namespace App\Http\Controllers\Site;

use App\Events\ContactsFormRequest;
use App\Events\ProductFormRequest;
use App\Exceptions\HttpNotFound;

//use App\Helper\Mail\CustomerNotification;
use App\Helper\Mail\QueueNotifyAdmin;
use App\Http\Request\Site\ContactsRequest;
use App\LiquidEngine\Assetics\Subscribers\Contacts;
use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use Exception;
use Throwable;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Auth;
use Illuminate\Routing\Controller;
use App\LiquidEngine\Traits\ThemeBreadcrumbs;

class ContactsController extends Controller
{
    use ThemeBreadcrumbs;

    /**
     * @param Request $request
     * @param string|null $product_url_handle
     * @return Response
     */
    public function index(Request $request, ?string $product_url_handle = null)
    {
        $subject = null;
        $message = null;
        $product = null;
        $variant = null;

        if ($product_url_handle) {
            $variant_id = $request->query('variant_id');
            if ($variant_id) {
                $product = Product::urlHandle($product_url_handle)->with(['variants' => function ($q) use ($variant_id): void {
                    /** @var Variant $q */
                    $q->where('id', $variant_id);
                }])->active()->first();
                if (!$product || !$product->variants->count()) {
                    throw new HttpNotFound(__('sf.store.err.product_no_longer_exists'));
                }

                /** @var Variant $variant */
                $variant = $product->variants->first();
                if ($product->p1) {
                    $product->name .= ' (' . $product->p1 . ':' . $variant->v1;
                    if ($product->p2) {
                        $product->name .= '; ' . $product->p2 . ':' . $variant->v2;
                        if ($product->p3) {
                            $product->name .= '; ' . $product->p3 . ':' . $variant->v3;
                        }
                    }

                    $product->name .= ')';
                }
            } else {
                $product = Product::urlHandle($product_url_handle)->active()->first();
                if (!$product) {
                    throw new HttpNotFound(__('sf.store.err.product_no_longer_exists'));
                }

                $variant = $product->variant;
            }

            $subject = __('sf.contacts.product_request.subject', ['product' => $product->name]);
            $message = __('sf.contacts.product_request.message', [
                    'product' => $product->name,
                    'url' => $product->url(),
                ]) . "\n\n";
        }

        return !isLiquidEngine() ? $this->indexSmartyEngine($request, $subject, $message, $product, $variant) : $this->indexLiquidEngine($request, $subject, $message, $product, $variant);
    }

    /**
     * @param ContactsRequest $request
     * @return ResponseFactory|Response
     */
    public function send(ContactsRequest $request)
    {
        //send email notification start
        $vars = [
            '{$shop_url}' => site()->getSiteUrl('primary'),
            '{$contact_first_name}' => $request->input('first_name'), //mb_convert_encoding($request->input('first_name'), 'UTF-8', 'UTF-8'); //Unable to create payload: Malformed UTF-8 characters, possibly incorrectly encoded
            '{$contact_last_name}' => $request->input('last_name'),
            '{$contact_email}' => $request->input('email'),
            '{$message}' => $request->input('message'),
            '{$site_url}' => config('url.gate'),
        ];

        if (Auth::customerId()) {
            $vars = array_merge($vars, [
                '{$contact_first_name}' => Auth::customer()->first_name,
                '{$contact_last_name}' => Auth::customer()->last_name,
                '{$contact_email}' => Auth::customer()->email,
            ]);
        }

        $subject_vars['{$subject}'] = $request->input('subject');

        $sender = [implode(' ', array_filter([$vars['{$contact_first_name}'], $vars['{$contact_last_name}']])), $vars['{$contact_email}']];

        QueueNotifyAdmin::sendCommonNotification(setting('site_email'), 'contact', $vars, $subject_vars, null, $sender);

        try {
            event(new ContactsFormRequest(
                $vars['{$contact_email}'],
                $vars['{$contact_first_name}'],
                $vars['{$contact_last_name}'],
                $request->input('requested_product_id')
            ));
        } catch (Exception) {
            //
        }

        try {
            if ($productId = $request->input('requested_product_id')) {
                event(new ProductFormRequest(
                    (int)$productId,
                    $request->input('requested_variant_id'),
                    $vars['{$contact_email}'],
                    $vars['{$contact_first_name}'],
                    $vars['{$contact_last_name}']
                ));
            }
        } catch (Exception) {
            //
        }

        return response([
            'status' => 'success',
            'msg' => __('sf.widget.contact.form.succ.contact_request_sent'),
            'events' => ['cc.contact.form.sent']
        ]);
    }

    /**
     * @return array
     */
    public function getSeo(): array
    {
        return [
            'title' => __('seo.contacts.title'),
            'description' => __('seo.contacts.description')
        ];
    }

    private function indexSmartyEngine(Request $request, $subject, $message, $product, $variant)
    {
        if (isset($product) && $product) {
            $method = $request->ajax() ? 'modal' : 'mainResponse';
            return \Illuminate\Support\Facades\View::$method('widgets.contact.product-request', [
                'subject' => $subject,
                'message' => $message,
                'product' => $product,
                'variant' => $variant ?? null,
            ], null, false);
        }

        return \Illuminate\Support\Facades\View::mainResponse('contacts.contacts', [
            'subject' => $subject,
            'message' => $message,
        ]);
    }

    private function indexLiquidEngine(Request $request, $subject, $message, $product, $variant)
    {
        $form_action = route('contacts');
        if (isset($product) && $product) {
            $form_action = route('contacts', ['product_url_handle' => $product->url_handle, 'variant_id' => $request->query('variant_id')]);
        }

        $this->breadcrumbs()->add(
            __('pages.home'),
            route('site.home')
        );

        $this->breadcrumbs()->add(
            __('pages.contacts'),
            route('contacts')
        );

        $data = [
            'subject' => $subject ?? null,
            'content' => $message ?? null,
            'title' => __('pages.contacts'),
            'form_action' => $form_action,
            // From smarty
            'message' => $message,
            'product' => $product ?? null,
            'variant' => $variant ?? null,

            'template' => new Template('page.contact'),
        ];

        if ($request->ajax()) {
            config('googlerecaptchav3.background_mode', true);
        }

        event('controller:contacts', new Contacts($product ?? null));

        return response(liquid('templates/page.contact', $data));
    }

}
