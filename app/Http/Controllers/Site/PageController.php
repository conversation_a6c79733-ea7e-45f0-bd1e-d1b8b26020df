<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 30.11.2016 г.
 * Time: 00:09 ч.
 */

namespace App\Http\Controllers\Site;

use App\Applications\Facades\Apps;
use App\Exceptions\HttpNotFound;
use App\Exceptions\PaymentRequired;
use App\Helper\Catalog\Pages;
use App\Helper\PageBuilderContent;
use App\Helper\YesNo;
use App\LiquidEngine\Assetics\Subscribers\HomePage;
use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;
use App\Models\Order\Order;
use App\Models\Page\Page;
use App\Models\Page\PageHistory;
use Auth;
use Cookie;
use GDPR;
use Throwable;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Linker;
use Modules\Apps\Administration\Membership\Models\MembershipModel;
use Request;
use Widget;
use Illuminate\Support\Facades\File;
use App\LiquidEngine\Traits\ThemeBreadcrumbs;

/**
 * Class PageController
 * @package App\Http\Controllers\Site
 */
class PageController extends Controller
{
    use ThemeBreadcrumbs;

    /**
     * @var Page
     */
    protected $page;

    /**
     * @var Order
     */
    protected $order;

    /**
     * @param Page $page
     * @return $this
     */
    public function setPage(Page $page): static
    {
        $this->page = $page;
        return $this;
    }

    /**
     * @param Order|null $order
     * @return $this
     */
    public function setOrder(?Order $order = null): static
    {
        $this->order = $order;
        return $this;
    }

    /**
     * @param null $slug
     * @return RedirectResponse|Response
     */
    public function get($slug = null)
    {
        if (!$this->page) {
            $this->page = Pages::getPageByUrl($slug);
            if (!$this->page) {
                throw new HttpNotFound();
            }

            if (Apps::installed('membership') && $this->page->private == 'yes') {
                throw new HttpNotFound();
            }

            //redirect if link is 301
            Linker::handleUrlRedirect($slug, $this->page->url_handle);
        }

        if ($this->page->active != YesNo::True) {
            throw new HttpNotFound(__('sf.page.err.page_no_longer_active'));
        }

        if ($this->page->system_page == 'home' && activeRoute('page')) {
            return redirect()->route('site.home');
        }

        $this->getSeo();

        if ($this->page->type == 'builder') {
            return $this->preview($this->page);
        }

        if ($this->page->type == 'landing') {
            return new Response($this->page->content);
        }

        // if page is FAQ
        if ($this->page->type == 'faq') {
            $this->page->questions = $this->page->contents->pluck('name')->all();
            $this->page->answers = $this->page->contents->pluck('content')->all();
        }

        if ($this->page->type == 'regular') {
            $this->page = GDPR::renderCookiesTable($this->page);
        }

        if (request()->ajax()) {
            return new Response([
                'status' => 'success',
                'html' => \Illuminate\Support\Facades\View::make('page.page', ['page' => $this->page])->render(),
                'dependency' => [
                    'js' => Widget::getDependency('js'),
                    'css' => Widget::getDependency('css')
                ]
            ]);
        }

        return \Illuminate\Support\Facades\View::mainResponse('page.page', [
            'page' => $this->page,
            'order' => $this->order
        ]);
    }

    /**
     * @return array|null
     */
    public function getSeo(): array
    {
        return Pages::getSeo($this->page);
    }

    /**
     * @param Page|int $page
     * @param null|int $historyId
     * @return Response
     */
    public function preview($page, $historyId = null)
    {
        $this->page = $page instanceof Page ? $page : Page::findOrFail($page);
        if ($historyId) {
            $pageHistory = PageHistory::findOrFail($historyId);
            $this->page->content = json_encode($pageHistory->content);
        }

        if (request()->ajax() && activeRoute('page')) {
            $this->page->content = new PageBuilderContent($this->page->content)->toHTML();

            return new Response([
                'status' => 'success',
                'html' => \Illuminate\Support\Facades\View::make('page.page', ['page' => $this->page])->render(),
                'dependency' => [
                    'js' => Widget::getDependency('js'),
                    'css' => Widget::getDependency('css')
                ]
            ]);
        }

        return !isLiquidEngine() ? $this->previewSmartyEngine() : $this->previewLiquidEngine();
    }

    /**
     * @param $page
     * @return bool
     */
    private function checkAccess($page)
    {
        $user_id = Auth::customer()->id ?? null;
        if (!$user_id) {
            return false;
        }

        $getExpired = MembershipModel::where(['customer_id' => $user_id, 'page_id' => $page->id, 'expired' => null])->exists();
        if ($getExpired) {
            return true;
        }

        $getExpired = MembershipModel::where(['customer_id' => $user_id, 'page_id' => $page->id])->orderBy('expired', 'DESC')->first();
        if (!$getExpired) {
            return false;
        }

        if (!$getExpired->expired) {
            return true;
        }

        return !$getExpired->expired->isPast();
    }

    /**
     * @param mixed $slug
     * @return mixed
     */
    public function getPrivate($slug)
    {
        if (!Auth::customer()) {
            $cookie = Cookie::make('before_page', $slug, 1440);
            Cookie::queue($cookie);
            return redirect('/login');
        }

        if (!$this->page) {
            $this->page = Pages::getPageByUrl($slug);
            if (!$this->page) {
                throw new HttpNotFound();
            }

            if (!Apps::installed('membership') || $this->page->private == 'no') {
                throw new HttpNotFound();
            }

            if (!$this->checkAccess($this->page)) {
                throw new PaymentRequired(__('sf.page.no_access_msg'), 402);
            }

            //redirect if link is 301
            Linker::handleUrlRedirect($slug, $this->page->url_handle);
        }

        $widget = new \App\Helper\Widget();
        $widget->setSeo($this);

        if ($this->page->active != YesNo::True) {
            throw new HttpNotFound(__('sf.page.err.page_no_longer_active'));
        }

        if ($this->page->type == 'builder') {
            return $this->preview($this->page);
        }

        if ($this->page->type == 'landing') {
            return new Response($this->page->content);
        }

        if (Request::ajax()) {
            return new Response([
                'status' => 'success',
                'html' => \Illuminate\Support\Facades\View::make('page.page', ['page' => $this->page])->render(),
                'dependency' => [
                    'js' => Widget::getDependency('js'),
                    'css' => Widget::getDependency('css')
                ]
            ]);
        }

        return \Illuminate\Support\Facades\View::mainResponse('page.page', [
            'page' => $this->page,
            'order' => $this->order
        ]);
    }

    public function testLiquidTheme(string $template = 'index', ?string $page = null, array $data = [])
    {
        if ($page) {
            $template = $page;
        }

        return \response()->view($template, $data);
    }

    public function testLiquidThemeNatureface(?string $page = null)
    {
        $template = 'index';
        if ($page) {
            $template = $page;
        }

        $jsonPath = resource_path(sprintf(
            "themes/%s/templates/%s.json",
            site('template'),
            $template
        ));

        if (!file_exists($jsonPath)) {
             abort(404, "Template JSON '{$template}' not found.");
        }

        $data = json_decode(file_get_contents($jsonPath), true);

        $layout = $data['layout'] ?? 'theme';
        $sections = $data['sections'] ?? [];

        $sectionHtml = [];

        foreach ($sections as $id => $section) {
            $viewName = "sections::{$section['type']}";

            if (view()->exists($viewName)) {
                $sectionViewData = [
                    'section' => $section,
                    'sectionId' => $id,
                ];

                if ($section['type'] === 'tabs-collection') {
                    $sectionViewData['collection'] = [
                        'products' => \App\Models\Product\Product::paginate(12),
                    ];
                }

                $sectionHtml[$id] = view($viewName, $sectionViewData)->render();
            }
        }

        // dd(view()->getShared());

        return \Illuminate\Support\Facades\View::mainResponse("layout::{$layout}", [
            'template_name' => $template,
            'sections' => $sections,
            'content_for_layout' => implode('', $sectionHtml),
            'page_title' => 'TESTPAGE',
        ]);
    }

    public function testLiquidAssets(string $filename)
    {
        $filename = base64_decode($filename);

        // Use route directly to match the UrlGenerator implementation
        $url = route('liquid.assets', ['path' => $filename]);

        // Redirect to the new URL that will be handled by the AssetsController
        return redirect($url);
    }

    public function webfontsFile(?string $filename = null)
    {
        if (!$filename) {
            return null;
        }

        $path = resource_path(sprintf(
            "themes/%s/assets/webfonts/%s",
            site('template'),
            $filename
        ));

        if (! File::exists($path)) {
            $type = 'webfonts';
            $path = base_path("node_modules/@fortawesome/fontawesome-free/{$type}/{$filename}");
        }

        if (! File::exists($path)) {
            abort(404);
        }

        $ext = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        $mime = match ($ext) {
            'woff'  => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf'   => 'font/ttf',
            'eot'   => 'application/vnd.ms-fontobject',
            'svg'   => 'image/svg+xml',
            default => File::mimeType($path),
        };

        return response(File::get($path), 200)
            ->header('Content-Type', $mime)
            ->header('Cache-Control', 'public, max-age=31536000');
    }

    public function imagesFile(?string $filename = null)
    {
        if (!$filename) {
            return null;
        }

        $path = resource_path(sprintf(
            "themes/%s/assets/images/%s",
            site('template'),
            $filename
        ));

        if (! File::exists($path)) {
            abort(404);
        }

        $ext = strtolower(pathinfo($path, PATHINFO_EXTENSION));

        // explicit mapping for common types; fall back to File::mimeType()
        $mime = match ($ext) {
            'js'      => 'application/javascript',
            'css'     => 'text/css; charset=UTF-8',
            'map'     => 'application/json',
            'png'     => 'image/png',
            'jpe', 'jpeg' => 'image/jpeg',
            'jpg'     => 'image/jpeg',
            'gif'     => 'image/gif',
            'svg'     => 'image/svg+xml',
            'woff'    => 'font/woff',
            'woff2'   => 'font/woff2',
            'ttf'     => 'font/ttf',
            'eot'     => 'application/vnd.ms-fontobject',
            default   => File::mimeType($path),
        };

        return response(File::get($path), 200)
            ->header('Content-Type', $mime)
            // cache for a year
            ->header('Cache-Control', 'public, max-age=31536000');
    }

    private function previewSmartyEngine()
    {
        $widget = new \App\Helper\Widget();
        $widget->setSeo($this);

        $data = [
            'page' => $this->page,
            'order' => $this->order,
            'widget' => $widget,
            'pageBuilderContent' => new PageBuilderContent($this->page->content),
        ];

        return \Illuminate\Support\Facades\View::mainResponse('resources::site.page', $data);
    }

    private function previewLiquidEngine()
    {
        $template = new Template('index');

        $data = [
            'template' => new Template('index'),
        ];

        $this->breadcrumbs()->add(
            __('pages.home'),
            route('site.home')
        );

        event('controller:homePage', new HomePage());

        return response(liquid('templates/index', $data));
    }
}
