<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 16.8.2017 г.
 * Time: 11:30 ч.
 */

namespace App\Http\Request\SiteV2\Checkout;

use App\Http\Request\SiteV2\Customer\AddressRequest;
use App\Models\Customer\Customer;
use App\Models\Store\Cart as CartModel;
use Auth;

class BillingAddressRequest extends AddressRequest
{

    protected $_prefix = 'checkout.billing.address.';

    /**
     * @return array
     * @throws \Throwable
     */
    public function rules()
    {
        if ($this->getCart() && $this->getCart()->isCustomer() ? $this->totalCustomerAddresses() : false) {
            return [
                'checkout.billing.address.id' => 'exists:customers_billing_addresses,id',
            ];
        } else {
            return parent::rules();
        }
    }

    public function messages()
    {
        return array_merge(parent::messages(), [
            'checkout.billing.address.id.exists' => __('sf.global.err.address_no_longer_exists')
        ]);
    }

    public function attributes()
    {
        return parent::attributes();
    }

    /**
     * @return null|integer
     * @throws \Throwable
     */
    protected function getCustomerId()
    {
        return Auth::customerId() ?: ($this->getCartCustomer()->id ?? null);
    }

    /**
     * @return Customer|\Illuminate\Contracts\Auth\Authenticatable|null
     * @throws \Throwable
     */
    protected function getCustomer()
    {
        return Auth::customerId() ? Auth::customer() : $this->getCartCustomer();
    }

    /**
     * @return int
     * @throws \Throwable
     */
    protected function totalCustomerAddresses()
    {
        $customer = $this->getCustomer();
        return $customer ? $customer->billing_addresses()->count() : 0;
    }

    /**
     * @return CartModel|bool
     * @throws \Throwable
     */
    protected function getCart()
    {
        return CartModel::instance();
    }

    /**
     * @return \App\Models\Customer\Customer|null
     * @throws \Throwable
     */
    protected function getCartCustomer()
    {
        if (!($cartInstance = $this->getCart())) {
            return null;
        }

        return $cartInstance->customer;
    }
}
