<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 4.8.2017 г.
 * Time: 17:28 ч.
 */

namespace App\Http\Request\SiteV2\Checkout;

use App\Http\Request\SiteV2\Traits\CustomFields;
use App\Http\Request\SiteV2\Traits\GdprRequest;
use Illuminate\Foundation\Http\FormRequest;

class GuestAuthorizeRequest extends FormRequest
{

    protected $_prefix;

    use GdprRequest, CustomFields;

    /**
     * @return array
     */
    public function rules()
    {
        if ($this->getInputPrefix()) {
            $this->offsetSet('email', $this->input($this->getInputPrefix() . 'email'));
        }

        return array_merge([
            $this->getInputPrefix() . 'email' => 'required|email|guest_register|max:191',
        ], $this->getCustomRules(), $this->getGDPRRules('register'));
    }

    /**
     * @return array
     */
    public function messages()
    {
        return [
            $this->getInputPrefix() . 'email.required' => __('sf.widget.global.err.email_required'),
            $this->getInputPrefix() . 'email.email' => __('sf.widget.global.err.email_invalid'),
            $this->getInputPrefix() . 'email.max' => sprintf(__('sf.widget.global.err.email_max_chars_%1$s'), 191),
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return array_merge($this->getCustomAttributes(), $this->getGDPRAttributes('register'));
    }

    /**
     * @return null|string
     */
    public function getInputPrefix()
    {
        return activeRoute('checkout.shipping.address') ? 'checkout.shipping.address.' : '';
    }

}
