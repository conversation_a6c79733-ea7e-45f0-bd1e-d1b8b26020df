<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 18.9.2017 г.
 * Time: 13:27 ч.
 */

namespace App\Http\Request\SiteV2\Customer;

use Illuminate\Foundation\Http\FormRequest;

class AddressRequest extends FormRequest
{

    protected $_prefix;

    public function rules()
    {
        $rules = [
            $this->_prefix . 'first_name' => 'required|max:191|min:2',
            $this->_prefix . 'last_name' => 'required|max:191|min:2',
            $this->_prefix . 'phone' => 'required|phone_number:' . $this->_prefix . 'country.iso2',
            $this->_prefix . 'state.iso2' => 'required',
            $this->_prefix . 'post_code' => 'required|post_code:' . $this->_prefix . 'country.iso2',
            $this->_prefix . 'text' => 'required',
            $this->_prefix . 'city.name' => 'required',
//            $this->_prefix . 'country.name' => 'google_place:' . $this->_prefix . 'country.iso2',
            $this->_prefix . 'state.name' => 'google_place:' . $this->_prefix . 'state.iso2',
        ];

        if(hasGoogleMapKey()) {
            $rules[$this->_prefix . 'country.name'] = 'google_place:' . $this->_prefix . 'country.iso2';
        } else {
            $rules[$this->_prefix . 'country.iso2'] = 'required';
        }

        if($this->checkRequired('checkout_hide_street_name')) {
            $rules[$this->_prefix . 'street.name'] = 'required';
        }
        if($this->checkRequired('checkout_hide_street_number')) {
            $rules[$this->_prefix . 'street_number'] = 'required';
        }
        if($this->checkRequired('checkout_hide_additional_information')) {
            $rules[$this->_prefix . 'address1'] = 'required';
        }
        if($this->checkRequired('checkout_hide_company_name') && $this->checkRequiredBillingByRoute()) {
            $rules[$this->_prefix . 'company_name'] = 'required|max:191';
        }
        if($this->checkRequired('checkout_hide_company_vat') && $this->checkRequiredBillingByRoute()) {
            $rules[$this->_prefix . 'company_vat'] = 'required|max:191';
        }
        if(setting('post_code_not_required')) {
            $rules[$this->_prefix . 'post_code'] = 'post_code:' . $this->_prefix . 'country.iso2';
        }
        if($this->checkRequired('checkout_hide_company_bulstat') && $this->checkRequiredBillingByRoute()) {
            $rules[$this->_prefix . 'company_bulstat'] = 'required|max:191';
        }
        if($this->checkRequired('checkout_hide_company_mol') && $this->checkRequiredBillingByRoute()) {
            $rules[$this->_prefix . 'company_mol'] = 'required|max:191';
        }

        if(!hasGoogleMapKey()) {
            unset(
                $rules[$this->_prefix . 'state.iso2'],
                $rules[$this->_prefix . 'text'],
                $rules[$this->_prefix . 'state.name']
            );
            $rules = array_merge($rules, [
                $this->_prefix . 'state.name' => 'required',
            ]);
        }

        return $rules;
    }

    public function messages()
    {
        return [
            $this->_prefix . 'company_name.max' => sprintf(__('sf.widget.account.err.company_name_max_chars_%1$s'), 191),
            $this->_prefix . 'company_name.required' => __('sf.widget.account.err.company_name_required'),
            $this->_prefix . 'company_vat.max' => sprintf(__('sf.widget.account.err.company_vat_max_chars_%1$s'), 191),
            $this->_prefix . 'company_vat.required' => __('sf.widget.account.err.company_vat_required_for_company'),
        ];
    }

    public function attributes()
    {
        return [
            $this->_prefix . 'first_name' => __('sf.global.label.first_name'),
            $this->_prefix . 'last_name' => __('sf.global.label.last_name'),
            $this->_prefix . 'post_code' => __('sf.global.label.postal_code'),
            $this->_prefix . 'phone' => __('sf.global.label.phone'),
            $this->_prefix . 'text' => __('sf.global.ph.enter_your_address'),
            $this->_prefix . 'state.iso2' => __('sf.global.ph.enter_your_address'),
            $this->_prefix . 'street.name' => __('sf.global.ph.street_name'),
            $this->_prefix . 'street_number' => __('sf.global.ph.street_number'),
            $this->_prefix . 'city.name' => __('sf.global.ph.city'),
            $this->_prefix . 'state.name' => __('sf.global.ph.state'),
            $this->_prefix . 'country.name' => __('sf.global.ph.country'),
            $this->_prefix . 'country.iso2' => __('sf.global.ph.country'),
            $this->_prefix . 'address1' => __('sf.global.ph.address_additional_info'),
            $this->_prefix . 'company_name' => __('sf.global.ph.company_name'),
            $this->_prefix . 'company_vat' => __('sf.global.ph.company_vat'),
            $this->_prefix . 'text' => __('sf.global.ph.enter_your_address'),
            $this->_prefix . 'company_bulstat' => __('sf.global.ph.company_bulstat'),
            $this->_prefix . 'company_mol' => __('sf.global.ph.company_mol'),
        ];
    }

    /**
     * @param $prefix
     * @return $this
     */
    public function setInputPrefix($prefix) {
        $this->_prefix = $prefix;
        return $this;
    }

    /**
     * @return null|string
     */
    public function getInputPrefix() {
        return $this->_prefix;
    }

    /**
     * @param $field
     * @return bool
     */
    protected function checkRequired($field)
    {
        return setting($field) == 'required' || empty(setting($field));
    }

    /**
     * @return bool
     */
    protected function checkRequiredBillingByRoute() {
        $data = [];
        if(setting('require_registration_billing_address') && in_array($this->_prefix, ['billing.'])) {
            $data[] = 'site.auth.register';
        }

        return in_array(activeRoute(), array_merge([
            'site.account.address.billing.save',
            'site.account.address.billing.update',
            'checkout.billing.address.submit',
            //new engine checkout
            'checkout.billing.address.create',
            'checkout.billing.address.edit',
        ], $data));
    }

}
