<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 31.5.2019 г.
 * Time: 17:37 ч.
 */

namespace App\Events\Marketing;

use Modules\Marketing\Campaign\Core\Providers\CampaignListenerServiceProvider as CampaignsServiceProvider;
use Modules\JsResponse\AbstractJsResponse;
use Modules\JsResponse\Formatters\JsRow;

class SubscriberJsResponse extends AbstractJsResponse
{
    /**
     * {@inheritdoc}
     * @return array|null
     */
    public function getJsDynamic(): ?array
    {
        $data = null;

        if (\Cookie::has('fb_psid') && \Cookie::has('uuid')) {
            $subscriberScript = \Illuminate\Support\Facades\View::make(CampaignsServiceProvider::VIEWS_KEY . '::storefront.save_subscriber_script')
                ->render();

            $data[] = (new JsRow())->setRaw($subscriberScript);
        }

        return $data;
    }

    /**
     * {@inheritdoc}
     */
    public function getAjaxJsData(): ?array
    {
        return null;
    }

    /**
     * {@inheritdoc}
     */
    public function getJsData(): ?array
    {
        return null;
    }

    /**
     * {@inheritdoc}
     */
    public function getFiles(): ?array
    {
        return null;
    }
}
