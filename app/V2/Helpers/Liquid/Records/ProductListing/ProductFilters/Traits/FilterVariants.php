<?php

namespace App\V2\Helpers\Liquid\Records\ProductListing\ProductFilters\Traits;

use App\V2\Helpers\Liquid\Records\ProductListing\Filters\Results\UrlQueryData;
use App\V2\Helpers\Liquid\Records\ProductListing\Filters\Results\UrlQueryDataOption;
use Illuminate\Support\Collection;

trait FilterVariants
{
    /**
     * @param Collection $variants
     * @return Collection|null
     */
    protected function filterVariants(Collection $variants): ?Collection
    {
        $return = [];
        if($variants->isEmpty()) {
            return null;
        }

        /** @var UrlQueryData $property */
        foreach($variants as $property) {
            $return[$property->getId()] = function ($query) use ($property) {
                if($property->getValues()->isEmpty()) {
                    return null;
                }

                $query->where(function($query) use($property) {
                    for($i=1; $i<=3; $i++) {
                        $query->orWhere(function($query) use($i, $property) {
                            $query->where(sprintf($this->_table . '.p%d_id', $i), $property->getId());

                            if($this->_table == 'products') {
                                $query->whereHas('variants', function($query) use($i, $property) {
                                    $options = $property->getValues()->map(function(UrlQueryDataOption $option) {
                                        return $option->getId();
                                    });

                                    $query->withoutGlobalScopes()
                                        ->whereIn(sprintf('v%d_id', $i), $options);
                                });
                            } else {
                                $query->where(sprintf($this->_table . '.p%d_id', $i), $property->getId());
                            }
                        });
                    }
                });
            };
        }

        return collect($return);
    }
}