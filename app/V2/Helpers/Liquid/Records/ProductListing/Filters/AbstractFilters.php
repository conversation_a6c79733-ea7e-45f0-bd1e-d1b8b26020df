<?php

namespace App\V2\Helpers\Liquid\Records\ProductListing\Filters;

use App\Models\Product\Category;
use App\Models\Product\Tag;
use App\Models\Product\Vendor;
use App\Models\Selection\ProductSelection;
use App\V2\Helpers\Liquid\Records\ProductListing\Filters\Results\SidebarFilter;
use App\V2\Helpers\Liquid\Records\ProductListing\Filters\Results\UrlQueryData;
use App\V2\Helpers\Liquid\Records\ProductListing\ProductListingFilters;
use Illuminate\Support\Collection;
use lib\widget\product\Filters;

abstract class AbstractFilters
{
    /** @var Category|null $category */
    protected $category;
    /** @var Vendor|null $vendor */
    protected $vendor;
    /** @var ProductSelection|null $selection */
    protected $selection;
    /** @var Tag|null $tag */
    protected $tag;
    /** @var int $sort */
    protected $sort = 0;
    /** @var bool $visible */
    protected $visible = false;
    /** @var ProductListingFilters $productFilter */
    protected $productFilter;

    public function __construct(ProductListingFilters $filter)
    {
        $this->productFilter = $filter;
    }

    /**
     * @return bool
     */
    public function supportUrlQueryParse(): bool
    {
        return false;
    }

    /**
     * @return bool
     */
    public function supportSidebarFilter(): bool
    {
        return $this->isVisible();
    }

    /**
     * @return null|Collection<UrlQueryData>
     */
    public function getUrlQueryData(): ?Collection
    {
        return null;
    }

    /**
     * @return null|SidebarFilter
     */
    public function getSidebarFilterData(): ?SidebarFilter
    {
        return null;
    }

    /**
     * @param mixed $category
     * @return AbstractFilters
     */
    public function setCategory(?Category $category): AbstractFilters
    {
        $this->category = $category;
        return $this;
    }

    /**
     * @return Category|null
     */
    public function getCategory(): ?Category
    {
        return $this->category;
    }

    /**
     * @param Vendor|null $vendor
     * @return AbstractFilters
     */
    public function setVendor(?Vendor $vendor): AbstractFilters
    {
        $this->vendor = $vendor;
        return $this;
    }

    /**
     * @return Vendor|null
     */
    public function getVendor(): ?Vendor
    {
        return $this->vendor;
    }

    /**
     * @param ProductSelection|null $selection
     * @return AbstractFilters
     */
    public function setSelection(?ProductSelection $selection): AbstractFilters
    {
        $this->selection = $selection;
        return $this;
    }

    /**
     * @return ProductSelection|null
     */
    public function getSelection(): ?ProductSelection
    {
        return $this->selection;
    }

    /**
     * @return Tag|null
     */
    public function getTag(): ?Tag
    {
        return $this->tag;
    }

    /**
     * @param Tag|null $tag
     * @return AbstractFilters
     */
    public function setTag(?Tag $tag): AbstractFilters
    {
        $this->tag = $tag;
        return $this;
    }

    /**
     * @param int $sort
     * @return AbstractFilters
     */
    public function setSort(int $sort): AbstractFilters
    {
        $this->sort = $sort;
        return $this;
    }

    /**
     * @return int
     */
    public function getSort(): int
    {
        return $this->sort;
    }

    /**
     * @param bool $visible
     * @return AbstractFilters
     */
    public function setVisible(bool $visible): AbstractFilters
    {
        $this->visible = $visible;
        return $this;
    }

    /**
     * @return bool
     */
    public function isVisible(): bool
    {
        return $this->visible;
    }

    /**
     * @param ProductListingFilters $productFilter
     * @return AbstractFilters
     */
    public function setProductFilter(ProductListingFilters $productFilter): AbstractFilters
    {
        $this->productFilter = $productFilter;
        return $this;
    }

    /**
     * @return ProductListingFilters
     */
    public function getProductFilter(): ProductListingFilters
    {
        return $this->productFilter;
    }

    /**
     * @return bool
     */
    public function supportFiltersPagination(): bool
    {
        return in_array(site('template'), Filters::$_support_filters_pagination);
    }
}