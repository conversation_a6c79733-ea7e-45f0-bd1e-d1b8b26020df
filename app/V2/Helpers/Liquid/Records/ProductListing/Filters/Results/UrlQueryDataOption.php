<?php

namespace App\V2\Helpers\Liquid\Records\ProductListing\Filters\Results;

class UrlQueryDataOption extends AbstractResultsData
{
    /** @var int $sort */
    protected $sort = 0;
    /** @var null|string $key */
    protected $key;
    /** @var null|string $parameter */
    protected $parameter;

    /**
     * @return int
     */
    public function getSort(): int
    {
        return $this->sort;
    }

    /**
     * @param int $sort
     * @return UrlQueryDataOption
     */
    public function setSort(int $sort): UrlQueryDataOption
    {
        $this->sort = $sort;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getKey(): ?string
    {
        return $this->key;
    }

    /**
     * @param string|null $key
     * @return UrlQueryDataOption
     */
    public function setKey(?string $key): UrlQueryDataOption
    {
        $this->key = $key;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getParameter(): ?string
    {
        return $this->parameter;
    }

    /**
     * @param string|null $parameter
     * @return UrlQueryDataOption
     */
    public function setParameter(?string $parameter): UrlQueryDataOption
    {
        $this->parameter = $parameter;
        return $this;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'sort' => $this->getSort(),
            'key' => $this->getKey(),
            'parameter' => $this->getParameter(),
        ]);
    }
}