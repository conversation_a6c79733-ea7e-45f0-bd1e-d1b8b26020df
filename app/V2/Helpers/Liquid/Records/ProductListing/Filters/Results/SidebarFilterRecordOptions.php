<?php

namespace App\V2\Helpers\Liquid\Records\ProductListing\Filters\Results;

class SidebarFilterRecordOptions extends AbstractResultsData
{
    /** @var bool $selected */
    protected $selected = false;
    /** @var int $sort */
    protected $sort = 0;
    /** @var string|null $color */
    protected $color;
    /** @var string|null $image */
    protected $image;
    /** @var bool $has_image */
    protected $has_image = false;

    /**
     * @return int
     */
    public function getSort(): int
    {
        return $this->sort;
    }

    /**
     * @param int $sort
     * @return SidebarFilterRecordOptions
     */
    public function setSort(int $sort): SidebarFilterRecordOptions
    {
        $this->sort = $sort;
        return $this;
    }

    /**
     * @return bool
     */
    public function isSelected(): bool
    {
        return $this->selected;
    }

    /**
     * @param bool $selected
     * @return SidebarFilterRecordOptions
     */
    public function setSelected(bool $selected): SidebarFilterRecordOptions
    {
        $this->selected = $selected;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getColor(): ?string
    {
        return $this->color;
    }

    /**
     * @param string|null $color
     * @return SidebarFilterRecordOptions
     */
    public function setColor(?string $color): SidebarFilterRecordOptions
    {
        $this->color = $color;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getImage(): ?string
    {
        return $this->image;
    }

    /**
     * @param string|null $image
     * @return SidebarFilterRecordOptions
     */
    public function setImage(?string $image): SidebarFilterRecordOptions
    {
        $this->image = $image;
        return $this;
    }

    /**
     * @return bool
     */
    public function isHasImage(): bool
    {
        return $this->has_image;
    }

    /**
     * @param bool $has_image
     * @return SidebarFilterRecordOptions
     */
    public function setHasImage(bool $has_image): SidebarFilterRecordOptions
    {
        $this->has_image = $has_image;
        return $this;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'selected' => $this->isSelected(),
            'sort' => $this->getSort(),
            'color' => $this->getColor(),
            'image' => $this->getImage(),
            'has_image' => $this->isHasImage(),
        ]);
    }
}