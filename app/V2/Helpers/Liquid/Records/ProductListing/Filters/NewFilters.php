<?php

namespace App\V2\Helpers\Liquid\Records\ProductListing\Filters;

use App\Helper\YesNo;
use App\V2\Helpers\Liquid\Records\ProductListing\Filters\Results\SidebarFilter;
use App\V2\Helpers\Liquid\Records\ProductListing\Filters\Results\UrlQueryData;
use Illuminate\Support\Collection;

class NewFilters extends AbstractFilters
{

    const FILTER_KEY = 'new';

    /**
     * @return bool
     */
    public function supportUrlQueryParse(): bool
    {
        return true;
    }

    /**
     * @inerhitDoc
     */
    public function getUrlQueryData(): ?Collection
    {
        if(
            !$this->isVisible() ||
            !$this->supportUrlQueryParse() ||
            is_null($query = $this->getUrlQueryFiltered())
        ) {
            return null;
        }

        return collect([
            new UrlQueryData([
                'name' => __('sf.filters.title.' . static::FILTER_KEY),
                'url_handle' => static::FILTER_KEY,
                'value' => [
                    'name' => __('global.yes'),
                    'url_handle' => $query,
                ]
            ])
        ]);
    }

    /**
     * @return null|string
     */
    protected function getUrlQueryFiltered(): ?string
    {
        $query = request()->query(static::FILTER_KEY, YesNo::False);
        if($query != YesNo::True) {
            return null;
        }

        return $query;
    }

    /**
     * @return SidebarFilter|null
     */
    public function getSidebarFilterData(): ?SidebarFilter
    {
        /** @var Collection $active */
        $active = $this->getProductFilter()->getQueryFilters()->get(static::FILTER_KEY);

        return new SidebarFilter([
            'key' => static::FILTER_KEY,
            'url_handle' => static::FILTER_KEY,
            'name' => __('sf.filters.title.' . static::FILTER_KEY),
            'sort' => $this->getSort(),
            'selected' => $active && $active->first() && $active->first()->getValue(),
        ]);
    }
}