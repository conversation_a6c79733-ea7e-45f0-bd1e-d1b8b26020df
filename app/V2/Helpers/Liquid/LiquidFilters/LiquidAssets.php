<?php

namespace App\V2\Helpers\Liquid\LiquidFilters;

use App\Helper\Html;
use App\LiquidEngine\Services\UrlGenerator;
use Liquid\Context;
use Liquid\HtmlString;

/**
 * @deprecated This class is deprecated and will be removed in the next major version.
 * Use the following alternatives instead:
 * - For asset_url: Use App\LiquidEngine\LiquidHelpers\LiquidFilters\UrlFilters::asset_url()
 * - For stylesheet_tag: Use App\LiquidEngine\LiquidHelpers\LiquidFilters\All::stylesheet_tag()
 * - For script_tag: Use App\LiquidEngine\LiquidHelpers\LiquidFilters\All::script_tag()
 * 
 * Asset URL generation is now consolidated in the App\LiquidEngine\Services\UrlGenerator service.
 */
class LiquidAssets
{
    /**
     * @var Context
     */
    protected $context;

    /**
     * @var UrlGenerator|null
     */
    protected ?UrlGenerator $urlGenerator = null;

    public function __construct(Context $context = null)
    {
        $this->context = $context;
    }

    /**
     * Get the URL generator instance
     *
     * @return UrlGenerator
     */
    protected function getUrlGenerator(): UrlGenerator
    {
        if ($this->urlGenerator === null) {
            $this->urlGenerator = new UrlGenerator();
        }

        return $this->urlGenerator;
    }

    /**
     * asset url
     *
     * @param string $input
     *
     * @return string
     * @throws \Exception
     * @deprecated Use App\LiquidEngine\LiquidHelpers\LiquidFilters\UrlFilters::asset_url() instead.
     * This method will be removed in the next major version.
     */
    public function asset_url($input)
    {
        return $this->getUrlGenerator()->assetUrl($input);
    }

    /**
     * css link tag
     *
     * @param string $input
     *
     * @return HtmlString
     * @deprecated Use App\LiquidEngine\LiquidHelpers\LiquidFilters\All::stylesheet_tag() instead.
     * This method will be removed in the next major version.
     */
    public function stylesheet_tag($input): HtmlString
    {
        return new HtmlString(Html::cssFile($input));
    }

    /**
     * js link tag
     *
     * @param string $input
     *
     * @return HtmlString
     * @deprecated Use App\LiquidEngine\LiquidHelpers\LiquidFilters\All::script_tag() instead.
     * This method will be removed in the next major version.
     */
    public function script_tag($input): HtmlString
    {
        return new HtmlString(Html::scriptFile($input));
    }
}