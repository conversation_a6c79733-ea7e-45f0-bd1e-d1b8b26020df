<?php

declare(strict_types=1);

namespace App\Locale;

use Illuminate\Support\Facades\Cache;
use App\Helper\ArrayCache;
use Cloudcart\Localization\Locale\Currency as CoreCurrency;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

/**
 * Class Currency
 * @package App\Locale
 */
class Currency extends CoreCurrency
{
    /**
     * @inheritDoc
     */
    public function __construct($locale = null)
    {
        parent::__construct($locale ?: (site('language') ?: config('app.locale')));
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public static function getSign($currency = null)
    {
        if (!$currency) {
            $currency = site('currency');
        }

        return parent::getSign($currency);
    }

    /**
     * @param null $code
     * @return string
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public static function fromCountryCode($code = null): string
    {
        if (!$code) {
            $code = site()->getOperationCountry()->code ?? setting('country');
        }

        $all = self::all();
        $arr = [];

        foreach ($all as $item) {
            foreach ($item['country'] as $iso2 => $country) {
                $arr[$iso2] = $item['code'];
            }
        }

        return $arr[$code] ?? 'EUR';
    }

    /**
     * @return array
     */
    public static function countryCurrency(): array
    {
        $currencies = self::all();
        $result = [];
        foreach ($currencies as $currency => $data) {
            foreach ($data['country'] as $iso3 => $country) {
                $result[$iso3] = $currency;
            }
        }

        return $result;
    }

    /**
     * @return array
     */
    #[\Override]
    protected function load(): array
    {
        return ArrayCache::remember('currency-data-intl-v1-array:' . $this->language, fn () => Cache::remember('currency-data-intl-v1:' . $this->language, config('cache.ttl_1d'), fn () => parent::load()));
    }

}
