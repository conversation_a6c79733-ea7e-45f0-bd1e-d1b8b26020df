<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\Collections\Filters;

use App\Models\Product\Product;
use Closure;

class ProductType extends AbstractFilters
{
    /**
     * @param $value
     * @return Closure
     */
    public function filterEqual($value)
    {
        return function($query) use($value): void {
            /** @var Product $query */
            if($value == 'simple') {
                $query->where('products.type', $value)
                    ->orWhere('products.type', 'variant');
            } else {
                $query->where('products.type', $value);
            }
        };
    }

    /**
     * @param $value
     * @return Closure
     */
    public function filterNotEqual($value)
    {
        return function($query) use($value): void {
            /** @var Product $query */
            if($value == 'simple') {
                $query->where('products.type', '<>', $value)
                    ->where('products.type', '<>', 'variant');
            } else {
                $query->where('products.type', '<>', $value);
            }
        };
    }
}
