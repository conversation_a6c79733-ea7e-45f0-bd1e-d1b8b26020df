<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\Collections\Filters;

class CategoryTitle extends AbstractFilters
{
    /**
     * @param mixed $value
     * @return mixed
     */
    public function filterStartWith($value)
    {
        return function($query): void {

        };
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function filterEndWith($value)
    {
        return function($query): void {

        };
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function filterContains($value)
    {
        return function($query): void {

        };
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function filterNotContains($value)
    {
        return function($query): void {

        };
    }
}
