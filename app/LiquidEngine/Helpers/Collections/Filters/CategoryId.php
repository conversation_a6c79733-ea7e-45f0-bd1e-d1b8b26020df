<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\Collections\Filters;

use App\Models\Product\Product;
use Closure;
use Illuminate\Support\Arr;

class CategoryId extends AbstractFilters
{
    /**
     * @param $value
     * @return Closure
     */
    public function filterEqual($value)
    {
        return Arr::first(Product::makeWhereFiltersByType('category', $value));
    }

    /**
     * @param $value
     * @return Closure
     */
    public function filterNotEqual($value)
    {
        return Arr::first(Product::makeWhereFiltersByType('category', $value, 'is_not'));
    }
}
