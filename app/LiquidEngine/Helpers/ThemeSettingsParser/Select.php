<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\ThemeSettingsParser;

use Illuminate\Support\Arr;
use Illuminate\View\View;
use Parsedown;

class Select extends AbstractThemeSettingsParser
{

    public function render() : ?View
    {
        if(!$this->getId() || empty($label = $this->getLabel()) || empty($options = $this->getOptions())) {
            return null;
        }

        return view('storefront.theme.sections.settings.select', [
            'name' => $this->getId(),
            'value' => $this->config->get($this->getId(), $this->settings['default'] ?? null),
            'required' => $this->settings['required'] ?? false,
            'label' => $label,
            'values' => collect($options)->pluck('label', 'value'),
            'info' => $this->getInfo(),
            'noSearch' => true,
            'is_block' => $this->isBlock(),
        ]);
    }

    /**
     * @param array $data
     * @return array
     */
    public function update(array $data) : array
    {
        if(!$this->getId()) {
            return [];
        }

        return [$this->getId() => $data[$this->getId()] ?? null];
    }

}
