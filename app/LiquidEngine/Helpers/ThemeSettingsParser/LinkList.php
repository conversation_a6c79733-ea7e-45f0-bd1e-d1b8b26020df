<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\ThemeSettingsParser;

use App\Models\StoreFront\NavigationGroups;
use Illuminate\View\View;

class LinkList extends AbstractThemeSettingsParser
{

    public function render() : ?View
    {
        if(!$this->getId() || empty($label = $this->getLabel())) {
            return null;
        }

        return view('storefront.theme.sections.settings.select', [
            'name' => $this->getId(),
            'value' => $this->config->get($this->getId()),
            'required' => $this->settings['required'] ?? false,
            'label' => $label,
            'values' => NavigationGroups::orderBy('name')->pluck('name', 'mapping'),
            'empty' => true,
            'info' => $this->getInfo(),
            'is_block' => $this->isBlock(),
        ]);
    }

    /**
     * @param array $data
     * @return array
     */
    public function update(array $data) : array
    {
        if(!$this->getId()) {
            return [];
        }

        return [$this->getId() => ($data[$this->getId()] ?? null)];
    }

}
