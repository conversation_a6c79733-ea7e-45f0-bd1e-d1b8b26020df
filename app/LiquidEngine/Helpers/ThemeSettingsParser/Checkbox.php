<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\ThemeSettingsParser;

use Illuminate\Support\Arr;
use Illuminate\View\View;

class Checkbox extends AbstractThemeSettingsParser
{

    public function render() : ?View
    {
        if(!$this->getId() || empty($label = $this->getLabel())) {
            return null;
        }

        return view('storefront.theme.sections.settings.checkbox', [
            'name' => $this->getId(),
            'value' => $this->config->get($this->getId(), $this->settings['default'] ?? null),
            'required' => $this->settings['required'] ?? false,
            'label' => $label,
            'info' => $this->getInfo(),
            'is_block' => $this->isBlock(),
        ]);
    }

    /**
     * @param array $data
     * @return array
     */
    public function update(array $data) : array
    {
        if(empty($this->getId())) {
            return [];
        }

        return [$this->getId() => (bool)($data[$this->getId()] ?? false)];
    }

}
