<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\ThemeSettingsParser;

use Illuminate\Support\Arr;
use Illuminate\View\View;

class Number extends AbstractThemeSettingsParser
{

    public function render() : ?View
    {
        if(!$this->getId() || empty($label = $this->getLabel())) {
            return null;
        }

        $value = (float)$this->config->get($this->getId(), $this->settings['default'] ?? null);

        return view('storefront.theme.sections.settings.number', [
            'name' => $this->getId(),
            'value' => $value,
            'required' => $this->settings['required'] ?? false,
            'label' => $label,
            'min' => (float)($this->settings['min'] ?? 0),
            'max' => (float)($this->settings['max'] ?? 0),
            'step' => (float)($this->settings['step'] ?? 0),
            'info' => $this->getInfo(),
            'is_block' => $this->isBlock(),
        ]);
    }

    /**
     * @param array $data
     * @return array
     */
    public function update(array $data) : array
    {
        if(!$this->getId()) {
            return [];
        }

        if(!is_numeric($number = ($data[$this->getId()] ?? 0))) {
            $number = 0;
        }

        return [$this->getId() => $number];
    }

}
