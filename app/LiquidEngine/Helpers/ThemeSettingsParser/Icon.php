<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\ThemeSettingsParser;

use App\LiquidEngine\Helpers\FontAwesome;
use Illuminate\Support\Arr;
use Illuminate\View\View;
use Parsedown;

class Icon extends AbstractThemeSettingsParser
{

    public function render() : ?View
    {
        if(!$this->getId()) {
            return null;
        }

        $icons = new FontAwesome();
        $icons = collect($icons->getIcons());

        $newIcons = collect();
        if(!empty($this->settings['filter'])) {
            foreach((array)$this->settings['filter'] as $filter) {
                $newIcons = $newIcons->merge($icons->where('prefix', $filter));
            }
        }

        return view('storefront.theme.sections.settings.icon', [
            'name' => $this->getId(),
            'value' => $this->config->get($this->getId(), $this->settings['default'] ?? null),
            'required' => $this->settings['required'] ?? false,
            'label' => $this->getLabel() ? : 'Icon',
            'icons' => $newIcons,
            'info' => $this->getInfo(),
            'is_block' => $this->isBlock(),
        ]);
    }

    /**
     * @param array $data
     * @return array
     */
    public function update(array $data) : array
    {
        if(!$this->getId()) {
            return [];
        }

        return [$this->getId() => $data[$this->getId()] ?? null];
    }

}
