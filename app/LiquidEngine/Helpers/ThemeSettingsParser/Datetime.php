<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\ThemeSettingsParser;

use App\Common\DateTimeFormat;
use Carbon\Carbon;
use Illuminate\View\View;

class Datetime extends AbstractThemeSettingsParser
{

    public function render() : ?View
    {
        if(!$this->getId() || empty($label = $this->getLabel())) {
            return null;
        }

        if($value = $this->config->get($this->getId())) {
            $result = Carbon::createFromFormat('Y-m-d H:i', $value);
            $value = $result->format(DateTimeFormat::$date_formats[setting('date_format')]['format'] . ' H:i');
        }

        return view('storefront.theme.sections.settings.datetime', [
            'name' => $this->getId(),
            'value' => $value,
            'required' => $this->settings['required'] ?? false,
            'label' => $label,
            'info' => $this->getInfo(),
            'is_block' => $this->isBlock(),
            'type' => 'datetime',
            'date_format' => DateTimeFormat::$date_formats[setting('date_format')]['format_js'] . ' HH:mm',
        ]);
    }

    /**
     * @param array $data
     * @return array
     */
    public function update(array $data) : array
    {
        if(!$this->getId()) {
            return [];
        }

        $value = null;
        if(!empty($data[$this->getId()])) {
            if(is_array($data[$this->getId()])) {
                $value = [];
                foreach($data[$this->getId()] AS $k=>$v) {
                    $value[$k] = !empty($v) ? $this->formatValue($v) : null;
                }
            } else {
                $value = $this->formatValue($data[$this->getId()]);
            }
        }

        return [$this->getId() => $value];
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function formatValue($value): string
    {
        $result = Carbon::createFromFormat(DateTimeFormat::$date_formats[setting('date_format')]['format'] . ' H:i', $value);
        $date_error = Carbon::getLastErrors();
        if ($date_error === false) {
            $result = null;
        }

        if ($date_error['warning_count'] > 0) {
            $result = null;
        }

        if ($date_error['error_count'] > 0) {
            $result = null;
        }

        return $result->format('Y-m-d H:i');
    }

}
