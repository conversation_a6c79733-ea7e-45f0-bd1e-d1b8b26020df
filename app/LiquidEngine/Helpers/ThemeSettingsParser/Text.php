<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\ThemeSettingsParser;

use Illuminate\View\View;

class Text extends AbstractThemeSettingsParser
{

    protected $input_type = 'text';

    public function render() : ?View
    {
        if(!$this->getId() || empty($label = $this->getLabel())) {
            return null;
        }

        $value = $this->config->get($this->getId(), $this->settings['default'] ?? null);

        return view('storefront.theme.sections.settings.text', [
            'name' => $this->getId(),
            'value' => $value,
            'required' => $this->settings['required'] ?? false,
            'label' => $label,
            'info' => $this->getInfo(),
            'is_block' => $this->isBlock(),
            'input_type' => $this->input_type,
        ]);
    }

    /**
     * @param array $data
     * @return array
     */
    public function update(array $data) : array
    {
        if(!$this->getId()) {
            return [];
        }

        return [$this->getId() => $data[$this->getId()] ?? null];
    }

}
