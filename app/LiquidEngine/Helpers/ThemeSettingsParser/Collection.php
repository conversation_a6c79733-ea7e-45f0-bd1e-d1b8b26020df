<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\ThemeSettingsParser;

use App\Models\Collections\Collections;
use Illuminate\View\View;

class Collection extends AbstractThemeSettingsParser
{

    public function render() : ?View
    {
        if(!$this->getId()) {
            return null;
        }

        return view('storefront.theme.sections.settings.select', [
            'name' => $this->getId(),
            'value' => $this->config->get($this->getId(), $this->settings['default'] ?? null),
            'required' => $this->settings['required'] ?? false,
            'label' => $this->getLabel() ? : 'Collection',
            'values' => Collections::orderBy('name')->pluck('name', 'url_handle'),
            'empty' => true,
            'info' => $this->getInfo(),
            'is_block' => $this->isBlock(),
        ]);
    }

    /**
     * @param array $data
     * @return array
     */
    public function update(array $data) : array
    {
        if(!$this->getId()) {
            return [];
        }

        return [$this->getId() => ($data[$this->getId()] ?? null)];
    }

}
