<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers;

use App\Helper\Html;
use App\LiquidEngine\Assetics\Assetics;
use Modules\JsResponse\Manager;

class RenderJsContent
{

    protected \Modules\JsResponse\Manager $manager;

    public function __construct()
    {
        $this->manager = new Manager();
    }

    /**
     * @return string
     */
    public function renderHeader(): string
    {
        $result = '';
        $result .= $this->getExternalSystemScriptTag(); //
        $result .= $this->getSystemScriptTag();
        $result .= Assetics::getPre();
        $result .= $this->getSystemStyleTag();
        $result .= Assetics::getPost();
        $result .= Html::script($this->manager->render('top')); //
        $result .= $this->manager->renderHtml('top'); //

        return $result;
    }

    /**
     * @return string
     */
    public function renderFooter(): string
    {
        $result = Html::script($this->manager->render('bottom')); //
        $result .= $this->manager->renderHtml('bottom'); //

        $result .= Assetics::getEnd();

        return $result;

//        return Assetics::getEnd();
    }

    /**
     * @return string
     */
    protected function getExternalSystemScriptTag(): string
    {
        return $this->manager->renderFiles();
    }

    /**
     * @return string
     */
    protected function getSystemScriptTag(): ?string
    {
        if(request()->ajax()) {
            return null;
        }

        $script = [
            Html::scriptFile(config('url.img') . 'new-themes/system/js/analytics.js?' . app('last_build')),
            Html::scriptFile(config('url.img') . 'new-themes/system/js/core.js?' . app('last_build')),
            Html::scriptFile(config('url.img') . 'new-themes/system/js/events-listener.js?' . app('last_build')),
        ];

        if(activeRoute('checkout')) {
            $script[] = Html::scriptFile(
                config('url.img') . 'new-themes/system/js/checkout.js?' . app('last_build')
            );
        }

        return implode("\n", $script);
    }

    /**
     * @return string
     */
    protected function getSystemStyleTag(): ?string
    {
        if(request()->ajax()) {
            return null;
        }

        $style = [Html::cssFile(
            config('url.img') . 'new-themes/system/css/core.css?' . app('last_build')
        )];

        if(activeRoute('checkout')) {
            $style[] = Html::cssFile(
                config('url.img') . 'new-themes/system/css/checkout.css?' . app('last_build')
            );
        }

        return implode("\n", $style);
    }

}
