<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\Catalog\Product;

use App\Helper\Temp\ProductTemp;
use App\Models\Customer\Group;
use App\Models\Customer\Customer;
use App\Models\Product\Product;
use Auth;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Throwable;

class Sorting
{

    const DEFAULT_SORT = 'products.id';

    const DEFAULT_DIRECTION = 'desc';

    const DEFAULT_GUARD = 'customer';

    protected $_group_id;

    /**
     * @param string $sort
     * @param string $direction
     * @param Builder $model
     * @return Builder|Product
     */
    public static function sort(string $sort, string $direction, $model)
    {
        $instance = new static();
        if (method_exists($instance, $method = sprintf('_get_sort_by_%s', $sort))) {
            return $instance->$method($direction, $model);
        } else {
            return $instance->_get_default_sort($direction, $model);
        }
    }

    /**
     * @param string $sort
     * @return array
     */
    public static function parse(string $sort): array
    {
        $parts = explode('-', $sort, 2);
        if(count($parts) == 1 && in_array(strtolower($parts[0]), ['rand', 'match'])) {
            return [$parts[0], static::DEFAULT_DIRECTION];
        }

        if(count($parts) < 1) {
            return [static::DEFAULT_SORT, static::DEFAULT_DIRECTION];
        }

        if(!in_array($direction = strtolower($parts[1] ?? static::DEFAULT_DIRECTION), ['asc', 'desc'])) {
            $direction = static::DEFAULT_DIRECTION;
        }

        return [$parts[0], $direction];
    }

    /**
     * Sorting constructor.
     */
    protected function __construct()
    {
        //
    }

    /**
     * @param string $direction
     * @param Builder $model
     * @return Builder|Product
     */
    protected function _get_sort_by_id(string $direction, $model)
    {
        if ($direction == 'asc') {
            return $model->oldest('products.id');
        } else {
            return $model->latest('products.id');
        }
    }

    /**
     * @param string $direction
     * @param Builder $model
     * @return Builder|Product
     */
    protected function _get_sort_by_name(string $direction, $model)
    {
        if ($direction == 'asc') {
            return $model->oldest('products.name')
                ->latest('products.id');
        } else {
            return $model->latest('products.name')
                ->latest('products.id');
        }
    }

    /**
     * @param string $direction
     * @param Builder $model
     * @return Builder|Product
     */
    protected function _get_sort_by_featured(/** @noinspection PhpUnusedParameterInspection */ string $direction, $model)
    {
        return $model->latest('products.featured')
            ->latest('products.id');
    }

    /**
     * @param string $direction
     * @param Builder $model
     * @return Builder|Product
     */
    protected function _get_sort_by_price(string $direction, $model)
    {
        return $this->_get_sort_by_price_from($direction, $model);
    }

    /**
     * @param string $direction
     * @param Builder $model
     * @return Builder|Product
     */
    protected function _get_sort_by_price_from(string $direction, $model)
    {
        if (ProductTemp::hasTempTable($this->__getCustomerGroup())) {
            return $model->orderBy('min_price_with_discounted', $direction)
                ->latest('products.id');
        } else {
            return $model->orderBy('products.price_from', $direction)
                ->latest('products.id');
        }
    }

    /**
     * @param string $direction
     * @param Builder $model
     * @return Builder|Product
     */
    protected function _get_sort_by_price_to(string $direction, $model)
    {
        if (ProductTemp::hasTempTable($this->__getCustomerGroup())) {
            return $model->orderBy('min_price_with_discounted', $direction)
                ->latest('products.id');
        } else {
            return $model->orderBy('products.price_from', $direction)
                ->latest('products.id');
        }
    }

    /**
     * @param string $direction
     * @param Builder $model
     * @return Builder|Product
     */
    protected function _get_sort_by_sale(string $direction, $model)
    {
        if ($direction == 'asc') {
            $model->oldest('is_discounted');
        } else {
            $model->latest('is_discounted');
        }

        return $this->_get_sort_by_price_from($direction, $model);
    }

    /**
     * @param string $direction
     * @param Builder $model
     * @return Builder|Product
     */
    protected function _get_sort_by_save(/** @noinspection PhpUnusedParameterInspection */ string $direction, $model)
    {
        if (ProductTemp::hasTempTable($this->__getCustomerGroup())) {
            return $model->latest('product_to_discount_save')
                ->latest('products.id');
        } else {
            return $model->oldest('products.price_from')
                ->latest('products.id');
        }
    }

    /**
     * @param string $direction
     * @param Builder $model
     * @return Builder|Product
     */
    protected function _get_sort_by_sort_order(/** @noinspection PhpUnusedParameterInspection */ string $direction, $model)
    {
        return $model->latest('products.sort_order')
            ->latest('products.id');
    }

    /**
     * @param string $direction
     * @param Builder $model
     * @return Builder|Product
     */
    protected function _get_sort_by_rand(/** @noinspection PhpUnusedParameterInspection */ string $direction, $model)
    {
        return $model->inRandomOrder();
    }

    /**
     * @param string $direction
     * @param Builder|Product $model
     * @return Builder|Product
     */
    protected function _get_sort_by_match(/** @noinspection PhpUnusedParameterInspection */ string $direction, $model)
    {
        if(empty($model->parent) || !($model->parent instanceof Product)) {
            return $this->_get_sort_by_id('desc', $model);
        }

        /** @var Collection $tags */
        $tags = $model->parent->tags->pluck('id')->unique()->filter();
        if($tags->isEmpty()) {
            return $model->where($model->getTableAlias() . '.id', 0);
        }

        $model->join('tags__products_tags__items', 'tags__products_tags__items.item_id', '=', $model->getTableAlias() . '.id')
            ->whereIn('tags__products_tags__items.tag_id', $tags->all())
            ->selectRaw('COUNT(*) AS `total_match`')->where($model->getTableAlias() . '.id', '!=', $model->parent->id)
            ->groupBy($model->getTableAlias() . '.id');

        return $model->orderBy('total_match', 'desc');
    }

    /**
     * @param string $direction
     * @param Builder $model
     * @return Builder|Product
     */
    protected function _get_default_sort(/** @noinspection PhpUnusedParameterInspection */ string $direction, $model)
    {
        return $model->orderBy(static::DEFAULT_SORT, static::DEFAULT_DIRECTION);
    }

    /**
     * @return int
     */
    protected function __getCustomerGroup()
    {
        if (!is_null($this->_group_id)) {
            return $this->_group_id;
        }

        if (!is_null($group_id = ($this->__getCustomerGuard()->group_id ?? null))) {
            return $this->_group_id = $group_id;
        }

        return $this->_group_id = (Group::groupGetGuestsGroup()->id ?? 0);
    }

    /**
     * @return Authenticatable|Customer
     */
    protected function __getCustomerGuard()
    {
        if (app()->bound('auth')) {
            try {
                return Auth::guard(static::DEFAULT_GUARD)->user();
            } catch (Throwable $e) {
                return null;
            }
        }

        return null;
    }

}
