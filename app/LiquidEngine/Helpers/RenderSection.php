<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers;

use Exception;
use Illuminate\Contracts\View\View;
use Liquid\Context;
use Liquid\Exceptions\SyntaxError;
use Liquid\Factory;
use Liquid\LiquidException;
use Liquid\Tag\TagSection;
use ReflectionException;
use Throwable;

class RenderSection implements View
{

    protected $section;

    protected $with = [];

    /**
     * @param mixed $section
     * @return mixed
     */
    public function __construct($section)
    {
        $this->section = $section;
    }

    /**
     * Get the name of the view.
     *
     * @return string
     */
    public function name()
    {
        return $this->section;
    }

    /**
     * Add a piece of data to the view.
     *
     * @param  string|array  $key
     * @param  mixed   $value
     * @return $this
     */
    public function with($key, $value = null)
    {
        if(is_array($key)) {
            foreach($key AS $k => $v) {
                $this->with($k, $v);
            }

            return $this;
        } else {
            $this->with[$key] = $value;
        }

        return $this;
    }

    /**
     * Get the string contents of the view.
     *
     * @param  callable|null  $callback
     * @return string
     *
     * @throws Throwable
     */
    public function render(callable $callback = null)
    {
        if(!liquid()->exists($fullSection = 'section.' . $this->section)) {
            app()->abort(404, sprintf('Section "%s" is not exists!', $this->section));
        }

        try {
            $contents = $this->renderContents($fullSection);
            $response = isset($callback) ? call_user_func($callback, $this, $contents) : null;
            return ! is_null($response) ? $response : $contents;
        } catch (Exception $e) {

            throw $e;
        } catch (Throwable $e) {

            throw $e;
        }
    }

    /**
     * Get the contents of the view instance.
     *
     * @param $fullSection
     * @return string
     * @throws LiquidException
     * @throws ReflectionException
     * @throws SyntaxError
     */
    protected function renderContents($fullSection)
    {
        /** @var Factory $factory */
        $factory = liquid();

        $compiler = $factory->getCompiler();
        $source = $compiler->getTemplateSource($fullSection);
        $tokens = $compiler->tokenize($source);

        $section = new TagSection(sprintf('"%s"', $this->section), $tokens, null, $compiler);
        $contexts = new Context(array_merge($factory->getShared(), $this->with));
        foreach($compiler->getFilters() AS $filter) {
            $contexts->addFilter($filter);
        }

        return $section->render($contexts);
    }
}
