<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Applications;

use App\LiquidEngine\Assetics\Assetics;
use App\LiquidEngine\Assetics\Subscribers\Cart\AddBundle;
use App\LiquidEngine\Assetics\Subscribers\Cart\AddDiscountCode;
use App\LiquidEngine\Assetics\Subscribers\Cart\AddProduct;
use App\LiquidEngine\Assetics\Subscribers\Cart\ClearCart;
use App\LiquidEngine\Assetics\Subscribers\Cart\RemoveItem;
use App\LiquidEngine\Assetics\Subscribers\Cart\UpdateBulk;
use App\LiquidEngine\Assetics\Subscribers\Cart\UpdateItem;
use App\LiquidEngine\Assetics\Subscribers\Checkout\InitCheckout;
use App\LiquidEngine\Assetics\Subscribers\Checkout\ReturnPageOrder;
use App\LiquidEngine\Assetics\Subscribers\Checkout\SubmitOrder;
use App\LiquidEngine\Assetics\Subscribers\Compare;
use App\LiquidEngine\Assetics\Subscribers\Contacts;
use App\LiquidEngine\Assetics\Subscribers\Customer\Account;
use App\LiquidEngine\Assetics\Subscribers\Customer\ListBillingAddresses;
use App\LiquidEngine\Assetics\Subscribers\Customer\BillingAddress;
use App\LiquidEngine\Assetics\Subscribers\Customer\ListShippingAddresses;
use App\LiquidEngine\Assetics\Subscribers\Customer\ShippingAddress;
use App\LiquidEngine\Assetics\Subscribers\FastCheckout;
use App\LiquidEngine\Assetics\Subscribers\FastCheckoutSubmit;
use App\LiquidEngine\Assetics\Subscribers\HomePage;
use App\LiquidEngine\Assetics\Subscribers\ListArticles;
use App\LiquidEngine\Assetics\Subscribers\ListBundles;
use App\LiquidEngine\Assetics\Subscribers\ListCategories;
use App\LiquidEngine\Assetics\Subscribers\ListCollections;
use App\LiquidEngine\Assetics\Subscribers\ListStores;
use App\LiquidEngine\Assetics\Subscribers\ListVendors;
use App\LiquidEngine\Assetics\Subscribers\Page;
use App\LiquidEngine\Assetics\Subscribers\Products;
use App\LiquidEngine\Assetics\Subscribers\Search;
use App\LiquidEngine\Assetics\Subscribers\Cart\ViewCart;
use App\LiquidEngine\Assetics\Subscribers\ViewArticle;
use App\LiquidEngine\Assetics\Subscribers\ViewCategory;
use App\LiquidEngine\Assetics\Subscribers\ViewCollection;
use App\LiquidEngine\Assetics\Subscribers\ViewProduct;
use App\LiquidEngine\Assetics\Subscribers\ViewSelection;
use App\LiquidEngine\Assetics\Subscribers\ViewStore;
use App\LiquidEngine\Assetics\Subscribers\ViewTag;
use App\LiquidEngine\Assetics\Subscribers\ViewVendor;
use Illuminate\Events\Dispatcher;

class AbstractEventSubscriber
{

    /**
     * @param ViewProduct $event
     */
    public function viewProduct($event): void
    {
        //
    }

    /**
     * @param ListVendors $event
     */
    public function listVendors($event): void
    {
        //
    }

    /**
     * @param ViewVendor $event
     */
    public function viewVendor($event): void
    {
        //
    }

    /**
     * @param ViewTag $event
     */
    public function viewTag($event): void
    {
        //
    }

    /**
     * @param ViewSelection $event
     */
    public function viewSelection($event): void
    {
        //
    }

    /**
     * @param ListCollections $event
     */
    public function listCollections($event): void
    {
        //
    }

    /**
     * @param ViewCollection $event
     */
    public function viewCollection($event): void
    {
        //
    }

    /**
     * @param ListCategories $event
     */
    public function listCategories($event): void
    {
        //
    }

    /**
     * @param ViewCategory $event
     */
    public function viewCategory($event): void
    {
        //
    }

    /**
     * @param ListBundles $event
     */
    public function listBundles($event): void
    {
        //
    }

    /**
     * @param Compare $event
     */
    public function compare($event): void
    {
        //
    }

    /**
     * @param Search $event
     */
    public function search($event): void
    {
        //
    }

    /**
     * @param Products $event
     */
    public function products($event): void
    {
        //
    }

    /**
     * @param ViewCart $event
     */
    public function viewCart($event): void
    {
        //
    }

    /**
     * @param UpdateBulk $event
     */
    public function updateBulkCart($event): void
    {
        //
    }

    /**
     * @param RemoveItem $event
     * @return array
     */
    public function removeItemCart($event): array
    {
        return [];
    }

    /**
     * @param UpdateItem $event
     */
    public function updateItemCart($event): void
    {
        //
    }

    /**
     * @param ClearCart $event
     */
    public function clearCart($event): void
    {
        //
    }

    /**
     * @param AddDiscountCode $event
     */
    public function addDiscountCode($event): void
    {
        //
    }

    /**
     * @param AddProduct $event
     * @return array
     */
    public function addItemCart($event): array
    {
        return [];
    }

    /**
     * @param AddBundle $event
     * @return array
     */
    public function addBundleCart($event): array
    {
        return [];
    }

    /**
     * @param Contacts $event
     */
    public function contacts($event): void
    {
        //
    }

    /**
     * @param Contacts $event
     */
    public function contactsSend($event): void
    {
        //
    }

    /**
     * @param HomePage $event
     */
    public function homePage($event): void
    {
        //
    }

    /**
     * @param Page $event
     */
    public function page($event): void
    {
        //
    }

    /**
     * @param Page $event
     */
    public function maintenance($event): void
    {
        //
    }

    /**
     * @param ViewCart $event
     */
    public function restoreAbandoned($event): void
    {
        //
    }

    /**
     * @param ListStores $event
     */
    public function listStores($event): void
    {
        //
    }

    /**
     * @param ViewStore $event
     */
    public function viewStore($event): void
    {
        //
    }

    /**
     * @param ViewProduct $event
     * @return array
     */
    public function wishListAdded($event): array
    {
        return [];
    }

    /**
     * @param ViewProduct $event
     * @return array
     */
    public function wishListRemoved($event): array
    {
        return [];
    }

    /**
     * @param ListArticles $event
     */
    public function listArticles($event): void
    {
        //
    }

    /**
     * @param ViewArticle $event
     */
    public function viewArticle($event): void
    {
        //
    }

    /**
     * @param FastCheckout $event
     */
    public function fastCheckout($event): void
    {
        //
    }

    /**
     * @param FastCheckoutSubmit $event
     */
    public function fastCheckoutSubmit($event): void
    {
        //
    }

    /**
     * @param InitCheckout $event
     */
    public function checkout($event): void
    {
        //
    }

    /**
     * @param SubmitOrder $event
     */
    public function submitOrder($event): void
    {
        //
    }

    /**
     * @param ReturnPageOrder $event
     */
    public function returnPageOrder($event): void
    {
        //
    }

    /**
     *
     */
    public function allListen(): void
    {
        //
    }

    /**
     * @param $key
     * @param array $events
     */
    public function customerListAll($key, $events)
    {
        //
    }

    /**
     * @param Account $event
     */
    public function customerAccount($event)
    {
        //
    }

    /**
     * @param Account $event
     * @return array
     */
    public function customerAccountUpdate($event): array
    {
        return [];
    }

    /**
     * @param Account $event
     * @return array
     */
    public function customerAuthenticated($event): array
    {
        return [];
    }

    /**
     * @param ListBillingAddresses $event
     */
    public function customerListBillingAddresses($event): void
    {
        //
    }

    /**
     * @param BillingAddress $event
     * @return array
     */
    public function customerUpdateDefaultBillingAddress($event): array
    {
        return [];
    }

    /**
     * @param BillingAddress $event
     */
    public function customerEditBillingAddress($event): void
    {
        //
    }

    /**
     * @param BillingAddress $event
     * @return array
     */
    public function customerUpdateBillingAddress($event): array
    {
        return [];
    }

    /**
     * @param BillingAddress $event
     */
    public function customerCreateBillingAddress($event): void
    {
        //
    }

    /**
     * @param BillingAddress $event
     * @return array
     */
    public function customerStoreBillingAddress($event): array
    {
        return [];
    }

    /**
     * @param BillingAddress $event
     * @return array
     */
    public function customerRemoveBillingAddress($event): array
    {
        return [];
    }

    /**
     * @param ListShippingAddresses $event
     */
    public function customerListShippingAddresses($event): void
    {
        //
    }

    /**
     * @param ShippingAddress $event
     * @return array
     */
    public function customerUpdateDefaultShippingAddress($event): array
    {
        return [];
    }

    /**
     * @param ShippingAddress $event
     */
    public function customerEditShippingAddress($event): void
    {
        //
    }

    /**
     * @param ShippingAddress $event
     * @return array
     */
    public function customerUpdateShippingAddress($event): array
    {
        return [];
    }

    /**
     * @param ShippingAddress $event
     */
    public function customerCreateShippingAddress($event): void
    {
        //
    }

    /**
     * @param ShippingAddress $event
     * @return array
     */
    public function customerStoreShippingAddress($event): array
    {
        return [];
    }

    /**
     * @param ShippingAddress $event
     * @return array
     */
    public function customerRemoveShippingAddress($event): array
    {
        return [];
    }

    /**
     * @param string $string
     * @return mixed
     */
    protected function addPre(string $string)
    {
        Assetics::setPre($string);
    }

    /**
     * @param string $string
     * @return mixed
     */
    protected function addPost(string $string)
    {
        Assetics::setPost($string);
    }

    /**
     * @param string $string
     * @return void
     */
    protected function addEnd(string $string)
    {
        Assetics::setEnd($string);
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param Dispatcher $events
     */
    public function subscribe($events): void
    {
        $events->listen(
            'controller:*',
            [$this, 'allListen']
        );
        $events->listen(
            'controller:viewProduct',
            [$this, 'viewProduct']
        );
        $events->listen(
            'controller:listVendors',
            [$this, 'listVendors']
        );
        $events->listen(
            'controller:viewVendor',
            [$this, 'viewVendor']
        );
        $events->listen(
            'controller:viewTag',
            [$this, 'viewTag']
        );
        $events->listen(
            'controller:viewSelection',
            [$this, 'viewSelection']
        );
        $events->listen(
            'controller:listCollections',
            [$this, 'listCollections']
        );
        $events->listen(
            'controller:viewCollection',
            [$this, 'viewCollection']
        );
        $events->listen(
            'controller:listCategories',
            [$this, 'listCategories']
        );
        $events->listen(
            'controller:viewCategory',
            [$this, 'viewCategory']
        );
        $events->listen(
            'controller:listBundles',
            [$this, 'listBundles']
        );
        $events->listen(
            'controller:compare',
            [$this, 'compare']
        );
        $events->listen(
            'controller:search',
            [$this, 'search']
        );
        $events->listen(
            'controller:products',
            [$this, 'products']
        );
        $events->listen(
            'controller:viewCart',
            [$this, 'viewCart']
        );
        $events->listen(
            'controller:updateBulkCart',
            [$this, 'updateBulkCart']
        );
        $events->listen(
            'controller:removeItemCart',
            [$this, 'removeItemCart']
        );
        $events->listen(
            'controller:updateItemCart',
            [$this, 'updateItemCart']
        );
        $events->listen(
            'controller:clearCart',
            [$this, 'clearCart']
        );
        $events->listen(
            'controller:addDiscountCode',
            [$this, 'addDiscountCode']
        );
        $events->listen(
            'controller:addItemCart',
            [$this, 'addItemCart']
        );
        $events->listen(
            'controller:addBundleCart',
            [$this, 'addBundleCart']
        );
        $events->listen(
            'controller:contacts',
            [$this, 'contacts']
        );
        $events->listen(
            'controller:contactsSend',
            [$this, 'contactsSend']
        );
        $events->listen(
            'controller:homePage',
            [$this, 'homePage']
        );
        $events->listen(
            'controller:page',
            [$this, 'page']
        );
        $events->listen(
            'controller:maintenance',
            [$this, 'maintenance']
        );
        $events->listen(
            'controller:restoreAbandoned',
            [$this, 'restoreAbandoned']
        );
        $events->listen(
            'controller:listStores',
            [$this, 'listStores']
        );
        $events->listen(
            'controller:viewStore',
            [$this, 'viewStore']
        );
        $events->listen(
            'controller:wishListAdded',
            [$this, 'wishListAdded']
        );
        $events->listen(
            'controller:wishListRemoved',
            [$this, 'wishListRemoved']
        );
        $events->listen(
            'controller:listArticles',
            [$this, 'listArticles']
        );
        $events->listen(
            'controller:viewArticle',
            [$this, 'viewArticle']
        );
        $events->listen(
            'controller:fastCheckout',
            [$this, 'fastCheckout']
        );
        $events->listen(
            'controller:fastCheckoutSubmit',
            [$this, 'fastCheckoutSubmit']
        );
        $events->listen(
            'controller:checkout',
            [$this, 'checkout']
        );
        $events->listen(
            'controller:submitOrder',
            [$this, 'submitOrder']
        );
        $events->listen(
            'controller:returnPageOrder',
            [$this, 'returnPageOrder']
        );
        //customer all
        $events->listen(
            'controller:customer:*',
            [$this, 'customerListAll']
        );
        //customer
        $events->listen(
            'controller:controller:customer:account',
            [$this, 'customerAccount']
        );
        $events->listen(
            'controller:customer:accountUpdate',
            [$this, 'customerAccountUpdate']
        );
        $events->listen(
            'controller:customer:authenticated',
            [$this, 'customerAuthenticated']
        );
        //customer billing addresses
        $events->listen(
            'controller:customer:address:listBilling',
            [$this, 'customerListBillingAddresses']
        );
        $events->listen(
            'controller:customer:address:updateDefaultBilling',
            [$this, 'customerUpdateDefaultBillingAddress']
        );
        $events->listen(
            'controller:customer:address:createBillingAddress',
            [$this, 'customerCreateBillingAddress']
        );
        $events->listen(
            'controller:customer:address:storeBillingAddress',
            [$this, 'customerStoreBillingAddress']
        );
        $events->listen(
            'controller:customer:address:editBillingAddress',
            [$this, 'customerEditBillingAddress']
        );
        $events->listen(
            'controller:customer:address:updateBillingAddress',
            [$this, 'customerUpdateBillingAddress']
        );
        $events->listen(
            'controller:customer:address:removeBillingAddress',
            [$this, 'customerRemoveBillingAddress']
        );
        //customer shipping addresses
        $events->listen(
            'controller:customer:address:listShipping',
            [$this, 'customerListBillingAddresses']
        );
        $events->listen(
            'controller:customer:address:updateDefaultBilling',
            [$this, 'customerUpdateDefaultBillingAddress']
        );
        $events->listen(
            'controller:customer:address:createBillingAddress',
            [$this, 'customerCreateBillingAddress']
        );
        $events->listen(
            'controller:customer:address:storeBillingAddress',
            [$this, 'customerStoreBillingAddress']
        );
        $events->listen(
            'controller:customer:address:editBillingAddress',
            [$this, 'customerEditBillingAddress']
        );
        $events->listen(
            'controller:customer:address:updateBillingAddress',
            [$this, 'customerUpdateBillingAddress']
        );
        $events->listen(
            'controller:customer:address:removeBillingAddress',
            [$this, 'customerRemoveBillingAddress']
        );
    }

    //helpers
    /**
     * @return bool
     */
    protected function isAjax()
    {
        return request()->ajax();
    }

    /**
     * @return bool
     */
    protected function isGet(): bool
    {
        return request()->isMethod('get');
    }

    /**
     * @return bool
     */
    protected function isPost(): bool
    {
        return request()->isMethod('post');
    }

}
