<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Applications;

use App\Helper\Html;
use Apps;

class GoogleTagsEventSubscriber extends AbstractEventSubscriber
{

    public function allListen(): void
    {
        if(!$this->isAjax() && $this->isGet()) {
            $this->addPre(HTML::scriptFile($this->_getScript()));
        }
    }

    protected function _getScript()
    {
        view()->addNamespace('google_tags_v2', app_path('Integration/GoogleTags/views/'));

        $this->addPost(view('google_tags_v2::system')->render());

        if(Apps::enabled('google_tags') && !empty($code = Apps::setting('google_tags', 'code')) && is_string($code)) {
            $this->addPost(view('google_tags_v2::customer')->render());
            $this->addEnd(view('google_tags_v2::customer-no-script')->render());
        }
    }

}
