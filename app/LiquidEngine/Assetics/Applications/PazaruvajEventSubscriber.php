<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Applications;

use App\Exceptions\Error;
use App\Helper\Html;
use App\Integration\Pazaruvaj\Helpers\TrustedShop;
use App\Integration\Pazaruvaj\PazaruvajManager;
use App\Models\Order\Order;

class PazaruvajEventSubscriber extends AbstractEventSubscriber
{

    /**
     * @var PazaruvajManager $manager
     */
    protected $manager;

    /**
     * @inheritDoc
     * @throws Error
     */
    public function returnPageOrder($event): void
    {
        $this->sendData($event->order);
    }

    /**
     * @inheritDoc
     * @throws Error
     */
    public function fastCheckoutSubmit($event): void
    {
        $this->sendData($event->order);
    }

    /**
     * @param Order $order
     * @throws Error
     */
    public function sendData($order): void
    {
        $manager = $this->getManager();
        if(!$manager->isActive() && !trim($manager->getSetting('web_api_key')) || !$order || !$order->products->count()) {
            return;
        }

        $trusted = $this->getTrustedShop($manager->getSetting('web_api_key'));
        $trusted->SetEmail($order->customer_email);
        foreach ($order->products AS $product) {
            $trusted->AddProduct($product->name, $product->id);
        }

        if($prepare = $trusted->Prepare()) {
            $this->addEnd(Html::script($prepare));
        }
    }

    /**
     * @return PazaruvajManager
     */
    protected function getManager()
    {
        if(is_null($this->manager)) {
            $this->manager = new PazaruvajManager();
        }

        return $this->manager;
    }

    /**
     * @param $key
     * @return TrustedShop
     */
    protected function getTrustedShop($key)
    {
        return new TrustedShop($key);
    }
}
