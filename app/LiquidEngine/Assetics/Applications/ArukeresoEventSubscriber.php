<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Applications;

use App\Integration\Arukereso\ArukeresoManager;
use App\Integration\Arukereso\Helpers\TrustedShop;

class ArukeresoEventSubscriber extends PazaruvajEventSubscriber
{
    /**
     * @return ArukeresoManager
     */
    protected function getManager()
    {
        if(is_null($this->manager)) {
            $this->manager = new ArukeresoManager();
        }

        return $this->manager;
    }

    /**
     * @param $key
     * @return TrustedShop
     */
    protected function getTrustedShop($key)
    {
        return new TrustedShop($key);
    }
}
