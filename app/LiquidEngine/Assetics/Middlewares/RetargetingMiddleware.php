<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Middlewares;

use App\Integration\GDPR\Models\PolicyAcceptanceLog;
use App\Integration\Marketing\Campaigns\Providers\CampaignsServiceProvider;
use App\Integration\Retargeting\RetargetingManager;
use App\Models\System\AppsManager;
use App\Models\Customer\Customer;
use App\Models\Store\Cart;
use Auth;
use Closure;
use Cookie;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Throwable;

class RetargetingMiddleware
{

    /**
     * @var RetargetingManager $_manager
     */
    protected $_manager;

    /**
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        return $this->injectSubscriber($response);
    }

    /**
     * @param Response $response
     * @return Response
     */
    protected function injectSubscriber($response)
    {
        if(!$this->shouldResponse($response)) {
            return $response;
        }

        if($this->allowJsData()) {
            $data = [];
            if (($customer = $this->getCustomer())) {
                $data[] = view('resources::apps.retargeting.event', [
                    'event' => 'setEmail',
                    'arguments' => $customer->toArray()
                ])->render();

                if (AppsManager::isActive('gdpr')) {
                    $data[] = view('resources::apps.retargeting.event', [
                        'event' => 'subscribeEmail',
                        'arguments' => [
                            'email' => $customer->email,
                            'status' => $this->isPolicyAcceptance($customer)
                        ]
                    ])->render();
                }
            }

            $content = str_replace(
                '<RetargetingCustomerPlaceholder></RetargetingCustomerPlaceholder>',
                implode("\n", $data),
                $response->getContent()
            );

            $response->setContent($content);
        }

        return $response;
    }

    /**
     * @param Customer $customer
     * @return bool
     */
    protected function isPolicyAcceptance($customer) : bool
    {
        if(!($customer instanceof \App\Models\Customer\Customer)) {
            return false;
        }

        if(request()->cookie('policy-acceptance')) {
            return true;
        }

        $acceptance = PolicyAcceptanceLog::where('customer_id', $customer->id)
                ->orWhere('email', $customer->email)->value('id') > 0;

        if($acceptance) {
            Cookie::queue(Cookie::make('policy-acceptance', (int)$acceptance, 10));
        }

        return $acceptance;
    }

    /**
     * @param mixed $response
     * @return mixed
     */
    public function shouldResponse($response): bool
    {
        return $response->isSuccessful() && !$response->isRedirection() && !request()->ajax() && request()->isMethod('get') && $response instanceof Response;
    }

    /**
     * @return \App\Models\Customer\Customer|Authenticatable
     * @throws Throwable
     */
    protected function getCustomer()
    {
        return Auth::customerId() ? Auth::customer() : $this->getCartCustomer();
    }

    /**
     * @return \App\Models\Customer\Customer|null
     * @throws Throwable
     */
    protected function getCartCustomer()
    {
        if(!($cartInstance = $this->getCart())) {
            return null;
        }

        return $cartInstance->customer;
    }

    /**
     * @return Cart|bool
     * @throws Throwable
     */
    protected function getCart()
    {
        return Cart::instance();
    }

    /**
     * @return bool
     */
    protected function allowJsData(): bool
    {
        try {
            return $this->getManager()->isInstalled() && $this->getManager()->getSetting('tracking_api');
        } catch (Throwable $throwable) {
            return false;
        }
    }

    /**
     * @return RetargetingManager
     */
    protected function getManager(): RetargetingManager
    {
        if (is_null($this->_manager)) {
            $this->_manager = new RetargetingManager();
        }

        return $this->_manager;
    }

}
