<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Middlewares;

use Modules\Marketing\Campaign\Core\Providers\CampaignListenerServiceProvider as CampaignsServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SubscriberMiddleware
{

    /**
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        return $this->injectSubscriber($response);
    }

    /**
     * @param Response $response
     * @return Response
     */
    protected function injectSubscriber($response)
    {
        if (!$this->shouldResponse($response)) {
            return $response;
        }

        if (\Cookie::has('fb_psid') && \Cookie::has('uuid')) {
            $data = view(CampaignsServiceProvider::VIEWS_KEY . '::storefront.v2.save_subscriber_script');
        }

        $content = str_replace(
            '<SubscriberPlaceholder></SubscriberPlaceholder>',
            $data ?? '',
            $response->getContent()
        );

        $response->setContent($content);

        return $response;
    }

    /**
     * @param mixed $response
     * @return mixed
     */
    public function shouldResponse($response): bool
    {
        return $response->isSuccessful() && !$response->isRedirection() && !request()->ajax() && request()->isMethod('get') && $response instanceof Response;
    }

}
