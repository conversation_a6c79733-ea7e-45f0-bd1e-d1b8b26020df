<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Subscribers\Customer;

use App\Models\Customer\CustomerShippingAddress;
use Illuminate\Pagination\LengthAwarePaginator;

class ListShippingAddresses extends Account
{
    /**
     * @var LengthAwarePaginator|CustomerShippingAddress[]
     */
    public $addresses;

    /**
     * @param mixed $customer
     * @param mixed $addresses
     * @return mixed
     */
    public function __construct($customer, $addresses)
    {
        parent::__construct($customer);

        $this->addresses = $addresses;
    }

}
