<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Subscribers\Customer;

use App\Models\Customer\CustomerShippingAddress;

class ShippingAddress extends Account
{
    /**
     * @var CustomerShippingAddress
     */
    public $address;

    /**
     * @param mixed $customer
     * @param mixed $address
     * @return mixed
     */
    public function __construct($customer, $address)
    {
        parent::__construct($customer);

        $this->address = $address;
    }

}
