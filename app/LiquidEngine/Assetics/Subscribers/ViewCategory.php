<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Subscribers;

use App\Models\Product\Category;
use App\Models\Product\Product;
use Illuminate\Pagination\LengthAwarePaginator;

class ViewCategory
{
    /**
     * @var Category
     */
    public $category;

    /**
     * @var LengthAwarePaginator|Product[]
     */
    public $products;

    /**
     * @param mixed $category
     * @param mixed $products
     * @return mixed
     */
    public function __construct($category, $products)
    {
        $this->category = $category;
        $this->products = $products;
    }

}
