<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Subscribers;

use App\Models\Product\Product;
use Illuminate\Support\Collection;

class Compare
{
    /**
     * @var Collection|Product[]
     */
    public $products;

    /**
     * @var Collection
     */
    public $compare_groups;

    /**
     * @var Collection
     */
    public $compares;

    /**
     * @param mixed $products
     * @param mixed $compare_groups
     * @param mixed $compares
     * @return mixed
     */
    public function __construct($products, $compare_groups, $compares)
    {
        $this->products = $products;
        $this->compare_groups = $compare_groups;
        $this->compares = $compares;
    }

}
