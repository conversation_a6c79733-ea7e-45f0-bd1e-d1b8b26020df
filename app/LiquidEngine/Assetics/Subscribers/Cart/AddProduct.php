<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Subscribers\Cart;

use App\Models\Store\Cart;
use App\Models\Store\CartItem;

class AddProduct
{
    /**
     * @var false|Cart
     */
    public $cart;

    /**
     * @var CartItem
     */
    public $item;

    /**
     * @param mixed $cart
     * @param mixed $item
     * @return mixed
     */
    public function __construct($cart, $item)
    {
        $this->cart = $cart;
        $this->item = $item;
    }

}
