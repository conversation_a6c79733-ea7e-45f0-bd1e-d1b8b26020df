<?php

declare(strict_types=1);

namespace App\LiquidEngine\Models;

use App\Models\Base\AbstractThemeBlocks;
use Illuminate\Support\Collection;

/**
 * @property-read Collection|ThemeBlockConfig[] $config
 * @property-read ThemeBlockConfig $single_config
 * @property-read ThemeBlocks $block
 */
class ThemeBlocks extends AbstractThemeBlocks
{

    public function block()
    {
        return $this->hasOne(ThemeBlocks::class, 'block_id', 'id');
    }

    public function config()
    {
        return $this->hasMany(ThemeBlockConfig::class, 'block_id', 'id');
    }

    public function single_config()
    {
        return $this->hasOne(ThemeBlockConfig::class, 'group_id', 'id');
    }

}
