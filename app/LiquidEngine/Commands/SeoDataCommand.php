<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 20.12.2018 г.
 * Time: 16:21 ч.
 */
namespace App\LiquidEngine\Commands;

use App\Console\Traits\TargetSites;
use App\LiquidEngine\Helpers\TempMigrate\SeoDataMigration;
use Illuminate\Console\Command;

class SeoDataCommand extends Command
{

    use TargetSites {
        TargetSites::getOptions as globalOptions;
    }

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'newEngineMigrate:seo';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate seo data to new engine';

    protected $_mapping = [
        'login' => 'accountLogin'
    ];

    /**
     * Execute the console command.
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        $this->exec(function(): void {
            $migrator = new SeoDataMigration();
            $migrator->execute();
        });
    }

}
