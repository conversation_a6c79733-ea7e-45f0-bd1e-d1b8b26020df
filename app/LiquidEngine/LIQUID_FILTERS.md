# CloudCart Liquid Filters Organization

This document outlines the logical organization of Liquid filters in the CloudCart platform. It categorizes the different types of filters, explains their responsibilities, and provides guidelines for when to use each type.

## Filter Categories

### 1. Core Liquid Filters

These filters are provided by the Laravel-Liquid package and implement the standard Liquid template language functionality.

| Filter Class | Description | Example Usage |
|--------------|-------------|--------------|
| ArrFilters | Array manipulation | `{{ array | first }}`, `{{ array | join }}` |
| ColorFilters | Color manipulation | `{{ color | brightness_difference }}` |
| CookieFilters | Cookie access | `{{ 'name' | cookie }}` |
| CustomerAvatarFilters | Customer avatar handling | `{{ customer | avatar_url }}` |
| DateFilters | Date formatting | `{{ date | date: '%Y-%m-%d' }}` |
| DefFilters | Default value handling | `{{ nil | default: 'Default' }}` |
| FontModifyFilters | Font manipulation | `{{ 'font' | font_modify }}` |
| HelperFilters | Miscellaneous helpers | `{{ content | strip_html }}` |
| InlineAssetFilters | Inline asset embedding | `{{ 'script.js' | asset_inline }}` |
| LineItemsForCartFilters | Cart line item handling | `{{ cart | line_items_for_cart }}` |
| MathFilters | Mathematical operations | `{{ 10 | times: 2 }}` |
| MultyFilters | Multiple manipulations | `{{ input | multi_filters }}` |
| StrFilters | String manipulation | `{{ string | append: '.txt' }}` |
| StructuredDataFilters | JSON-LD structured data | `{{ product | structured_data }}` |

### 2. URL Generation Filters

These filters are provided by CloudCart's LiquidEngine and are responsible for generating URLs for various resources.

| Filter Class | Description | Example Usage |
|--------------|-------------|--------------|
| UrlFilters | Generate URLs for resources and assets | `{{ product | url_for_product }}`, `{{ 'style.css' | asset_url }}` |

#### UrlFilters

Provides methods for generating URLs for CloudCart-specific resources and assets:

Resource URL generation:
- `url_for_product`: Generate product URLs
- `url_for_collection`: Generate collection URLs
- `url_for_blog`: Generate blog URLs
- `url_for_article`: Generate article URLs
- `url_for_page`: Generate page URLs
- `url_for_policy`: Generate policy URLs

Asset URL generation:
- `asset_url`: Generate URLs for theme assets
- `img_url`: Generate image URLs with transformations
- `img_url_with_crop`: Apply cropping to images
- `img_url_with_format`: Convert images to different formats
- `img_url_with_scale`: Scale images
- `img_url_with_options`: Apply multiple transformations
- `file_extension`: Get file extension
- `file_name`: Get filename without extension

> Note: Asset URL functionality was previously split between `AssetFilters` and `UrlFilters`, but has been consolidated into `UrlFilters` for better organization. `AssetFilters` is now deprecated and will be removed in a future version.

### 3. Utility Filters

These filters provide general utility functions that don't fit into the above categories.

| Filter Class | Description | Example Usage |
|--------------|-------------|--------------|
| All | Various utility filters | `{{ variable | dump }}`, `{{ 'string' | t }}` |

#### All Filters

Provides various utility methods:

- `dump`: Debug output for variables
- `dd`: Dump and die for debugging
- `t`: Translation helper
- `stylesheet_tag`: Generate HTML for CSS inclusion
- `script_tag`: Generate HTML for JavaScript inclusion
- `ifelse`: Conditional operator as a filter
- `cookie`: Get cookie value

### 4. Collection Filtering

These filters are used for filtering collections of products and other resources.

| Filter Class | Description | Example Usage |
|--------------|-------------|--------------|
| ProductTitle | Filter by product title | `{% for product in collection.products | where: 'title', 'Example' %}` |
| ProductType | Filter by product type | `{% for product in collection.products | where: 'type', 'Shirt' %}` |
| ProductId | Filter by product ID | `{% for product in collection.products | where: 'id', '123' %}` |
| ProductFeatured | Filter by featured status | `{% for product in collection.products | where: 'featured', true %}` |
| ProductPrice | Filter by price range | `{% for product in collection.products | where_price_between: 10, 50 %}` |
| VendorId | Filter by vendor ID | `{% for product in collection.products | where: 'vendor_id', '123' %}` |
| CategoryId | Filter by category ID | `{% for product in collection.products | where: 'category_id', '123' %}` |

## Implementation Guidelines

### When to Use Each Filter Type

- **Core Liquid Filters**: Use for standard Liquid template language functionality
- **URL Generation Filters**: Use for generating URLs for resources and assets
- **Utility Filters**: Use for debugging and general utility functions
- **Collection Filtering**: Use for filtering collections of products and other resources

### Implementation Location

When implementing new filters:

1. **Core Liquid functionality**: Add to the Laravel-Liquid package
2. **URL Generation**: Add to `UrlFilters` class
3. **Utility Functions**: Add to `All` filter class
4. **Collection Filtering**: Add to the appropriate collection filter class

### URL Generation Implementation

For consistency, all URL generation methods should:

1. Be implemented in `UrlFilters` class
2. Use the `UrlGenerator` service internally
3. Handle null inputs gracefully
4. Return consistent, well-formed URLs

### Filter Registration

All filters must be registered in `config/liquid.php` under the `filters` key:

```php
'filters' => [
    // Core filters
    ArrFilters::class,
    // ... other core filters
    
    // Custom filters
    All::class,
    UrlFilters::class,
    // ... other custom filters
    
    // Legacy filters
    AssetFilters::class, // @deprecated - Use UrlFilters instead
],
```

## Best Practices

1. **Avoid Duplication**: Never implement the same filter in multiple filter classes
2. **Clear Boundaries**: Each filter class should have a clear, well-defined responsibility
3. **Consistent Naming**: Follow the established naming conventions
4. **Documentation**: Always document new filters with PHPDoc comments
5. **Backward Compatibility**: When deprecating filters, provide clear migration paths
6. **Service Usage**: Use service classes for complex logic rather than implementing it directly in filters
7. **Caching**: Use caching for expensive operations (especially URL generation) 