<?php

declare(strict_types=1);

namespace App\LiquidEngine\OldEngine\Widgets;

use App\Widgets\Code AS OldCode;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class Code extends OldCode implements Arrayable, Jsonable
{

    /**
     * @return Factory|View|string
     */
    public function render()
    {
        if(!$this->isEnabled()) {
            return '';
        }

        return $this->getSetting('code');
    }

    /**
     * Get the collection of items as JSON.
     *
     * @param int $options
     * @return string
     */
    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), $options);
    }

    /**
     * @return array
     */
    public function toArray()
    {
        if(!$this->isEnabled()) {
            return [];
        }

        return [
            'widget' => [
                'name' => $this->getWidgetName(),
                'description' => $this->getWidgetDescription(),
                'mapping' => $this->getWidgetMap()
            ],
            'code' => $this->getSetting('code')
        ];
    }
}
