<?php

declare(strict_types=1);

namespace App\LiquidEngine\OldEngine\Widgets;

use App\Helper\ArrayCache;
use App\Models\Product\BundleProduct;
use App\Widgets\ProductShowcase as OldProductShowcase;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Contracts\View\Factory;
use Illuminate\Database\Query\Expression;
use Illuminate\Support\Arr;
use Illuminate\View\View;
use App\Models\Product\Product as ProductModel;
use App\LiquidEngine\LiquidHelpers\Drop\ProductByType;

class ProductShowcase extends OldProductShowcase implements Arrayable, Jsonable
{
    /**
     * @return Factory|View|string
     */
    public function render()
    {
        if(!$this->isEnabled()) {
            return '';
        }

        $settings = [
            'is_slider' => $this->getSetting('enable_slider'),
//            'grid_items_per_row' => $this->getSetting('per_row', 4),
//            'image_size' => $this->getSetting('image_size', '300x300'),
        ];


        $key = md5(json_encode(Arr::only($this->getSettings(), [
            'products', 'per_page', 'order_by', 'order_direction',
            'new', 'sale', 'filter', 'filter_value', 'type', 'featured'
        ])));

        $results = ArrayCache::remember('showcase.products.' . $key, function() {
            return $this->getProducts();
        });

        return liquid('widgets.product-showcase', [
            'products' => $results->map(function(ProductModel $product) {
                return ProductByType::get($product);
            })->all(),
            //settings
            'title' => $this->getSetting('title'),
//            'color' => $this->getSetting('color'),
//            'icon' => $this->getSetting('icon'),
            'settings' => $settings
        ]);
    }

    /**
     * Get the collection of items as JSON.
     *
     * @param int $options
     * @return string
     */
    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), $options);
    }

    /**
     * @return array
     */
    public function toArray()
    {
        if(!$this->isEnabled()) {
            return [];
        }

        $settings = [
            'is_slider' => $this->getSetting('enable_slider'),
            'grid_items_per_row' => $this->getSetting('per_row', 4),
            'image_size' => $this->getSetting('image_size', '300x300'),
        ];

        return [
            'widget' => [
                'name' => $this->getWidgetName(),
                'description' => $this->getWidgetDescription(),
                'mapping' => $this->getWidgetMap()
            ],
            'products' => $this->getProducts()->map(function(ProductModel $product) {
                return ProductByType::get($product);
            })->all(),
            //settings
            'title' => $this->getSetting('title'),
//            'color' => $this->getSetting('color'),
//            'icon' => $this->getSetting('icon'),
            'is_slider' => !!$this->getSetting('enable_slider'),
            'grid_items_per_row' => $this->getSetting('per_row'),
            'image_size' => $this->getSetting('image_size', '300x300'),
            'settings' => $settings,
        ];
    }

    /**
     * @return \Illuminate\Support\Collection|ProductModel[]
     */
    public function getProducts()
    {
        if (!empty($this->_products)) {
            return $this->_products;
        }

        $this->_generateWhere();

        $model = $this->_getCommon();

        $order = $this->getSetting('order_by', 'id');
        $sort = $this->getSetting('order_direction', 'asc');
        if ($order == 'rand') {
            $order = 'id';
            $sort = mt_rand(0, 1) ? 'asc' : 'desc';
        }

        if ($this->getSetting('filter') == 'product') {
            $order = \Illuminate\Support\Facades\DB::raw('FIELD(`' . $model->getTableAlias() . '`.`id`, ' . implode(',', $this->getSetting('filter_value') ?: [0]) . ')');
            $sort = 'asc';
        }

        if (!($order instanceof Expression)) {
            $order = $model->getTableAlias() . '.' . $order;
        }

        return $this->_products = $model->freeShipping()
            ->orderBy($order, $sort)->limit($this->_settings['products'])->get();
    }

    /**
     * @return ProductModel|Builder
     */
    protected function _getCommon()
    {
        /** @var ProductModel $model */
        $model = $this->getModel();

        foreach ($this->_where AS $key => $where) {
            $model = $model->where($where);
        }

        if ($this->_join && is_array($this->_join)) {
            foreach ($this->_join AS $join) {
                $model = $join($model);
            }
        }

        return $model->listing();
    }

    /**
     * @param array $attributes
     * @return ProductModel
     */
    public function getModel(array $attributes = [])
    {
        if(activeRoute('bundles.list.*')) {
            if ($attributes) {
                return new BundleProduct($attributes);
            } elseif ($this->_model) {
                return $this->_model;
            }

            return $this->_model = new BundleProduct();
        } else {
            if ($attributes) {
                return new ProductModel($attributes);
            } elseif ($this->_model) {
                return $this->_model;
            }

            return $this->_model = new ProductModel();
        }
    }

}
