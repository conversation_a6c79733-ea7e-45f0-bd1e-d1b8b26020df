<?php

declare(strict_types=1);

namespace App\LiquidEngine\OldEngine\Widgets;

use App\Helper\ArrayTree;
use App\Models\Product\Product;
use App\Models\Product\Category;
use Apps;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Database\Query\JoinClause;
use Modules\BrandModel\Models\Brand;
use Modules\BrandModel\Models\Model;
use Modules\BrandModel\Widgets\HomePageFilter AS OldHomePageFilter;
use App\LiquidEngine\LiquidHelpers\Drop\BrandModel\Brand AS DropBrand;
use App\LiquidEngine\LiquidHelpers\Drop\Category\Category as DropCategory;
use Illuminate\Support\Collection;

class HomePageFilter extends OldHomePageFilter implements Arrayable, Jsonable
{

    protected static $_categories;

    /**
     * @return Factory|View|string
     */
    public function render()
    {
        if(!Apps::installed('brand_model') || !$this->isEnabled()) {
            return '';
        }

        $this->getFilterCategories();

        $categories = [];
        if($this->isCategoriesEnables()) {
            $categories = $this->getFilterCategories();
        }

        return liquid('widgets.brand-model', [
            'brands' => new Collection($this->getFilterBrands()->map(function($brand): \App\LiquidEngine\LiquidHelpers\Drop\BrandModel\Brand {
                return new DropBrand($brand->brand->setRelation('models', $brand->models));
            })),
            //settings
            'categories_enabled' => $this->isCategoriesEnables(),
            'categories' => $categories,
        ]);
    }

    /**
     * Get the collection of items as JSON.
     *
     * @param int $options
     * @return string
     */
    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), $options);
    }

    /**
     * @return array
     */
    public function toArray()
    {
        if(!$this->isEnabled()) {
            return [];
        }

        $categories = [];
        if($this->isCategoriesEnables()) {
            $categories = $this->getFilterCategories();
        }

        return [
            'widget' => [
                'name' => $this->getWidgetName(),
                'description' => $this->getWidgetDescription(),
                'mapping' => $this->getWidgetMap()
            ],
            'brands' => new Collection($this->getFilterBrands()->map(function($brand): \App\LiquidEngine\LiquidHelpers\Drop\BrandModel\Brand {
                return new DropBrand($brand->brand->setRelation('models', $brand->models));
            })),
            //settings
            'categories_enabled' => $this->isCategoriesEnables(),
            'categories' => new Collection($categories),
        ];
    }

    /**
     * @return array
     */
    protected function getFilterCategories()
    {
        if(!Apps::installed('brand_model')) {
            return [];
        }

        if (!empty(static::$_categories)) {
            return static::$_categories;
        }

        $product_models_filter = Product::listing()->leftJoin('product_to_category', function(JoinClause $join): void {
            $join->on('products.id', 'product_to_category.product_id');
        })->join('category_path', function(JoinClause $join): void {
            $join->on('category_path.category_id', 'products.category_id')
                ->orOn('category_path.category_id', 'product_to_category.category_id');
        })->join('type__products_categories', function(JoinClause $join): void {
            $join->on('type__products_categories.id', 'category_path.path_id');
        })->select('type__products_categories.*')->distinct()->setBindings([], 'select');

        $product_models_filter = Category::withoutGlobalScopes()->fromSub($product_models_filter, 'type__products_categories')
            ->orderBy('order', 'asc')->get();

        return static::$_categories = collect(ArrayTree::createAutocomplete($product_models_filter))->map(function(Category $category): \App\LiquidEngine\LiquidHelpers\Drop\Category\Category {
            return new DropCategory($category);
        })->all();
    }

    /**
     * @return Collection
     */
    protected function getFilterBrands()
    {
        if(!Apps::installed('brand_model')) {
            return collect();
        }

        if (!empty(static::$_brand_models_filter)) {
            return static::$_brand_models_filter;
        }

        $product_models_filter = Product::listing()->join('@brand_model_product_to_model', function (JoinClause $join): void {
            $join->on('@brand_model_product_to_model.product_id', 'products.id');
        })->join('@brand_model_models', function (JoinClause $join): void {
            $join->on('@brand_model_models.id', '@brand_model_product_to_model.model_id');
        })->select('@brand_model_models.*')->distinct()->setBindings([], 'select');

        return static::$_brand_models_filter = Model::fromSub($product_models_filter, '@brand_model_models')->with('brand')->whereHas('brand', function ($query): void {
            /** @var Brand $query */
            $query->active();
        })->active()->get()->groupBy('brand_id')->map(function(Collection $collection) {
            /** @var Brand $brand */
            return (object)[
                'brand' => $brand = $collection->first()->brand,
                'models' => $collection->map(function(Model $model) use($brand): \Modules\BrandModel\Models\Model {
                    $model->unsetRelation('brand');
                    return $model;
                })->sortBy('title')->values()
            ];
        })->sortBy('brand.title')->values();
    }

}
