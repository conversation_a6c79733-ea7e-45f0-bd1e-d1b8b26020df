<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers;

use App\Http\Controllers\Controller as BaseController;
use App\LiquidEngine\Services\TemplateContextProvider;
use App\Models\Product\Category;
use App\Models\Product\Product;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Str;

/**
 * Controller for handling collection pages
 */
class CollectionsController extends BaseController
{
    /**
     * @var TemplateContextProvider
     */
    protected TemplateContextProvider $contextProvider;
    
    /**
     * Constructor
     *
     * @param TemplateContextProvider $contextProvider
     */
    public function __construct(TemplateContextProvider $contextProvider)
    {
        $this->contextProvider = $contextProvider;
    }
    
    /**
     * Show all collections
     *
     * @param Request $request
     * @return Response|JsonResponse
     */
    public function index(Request $request): Response|JsonResponse
    {
        // Get all collections (categories)
        $categories = Category::active()->orderBy('order')->get();
        
        // Convert categories to collections for Liquid templates
        $collections = [];
        foreach ($categories as $category) {
            $collections[] = [
                'id' => $category->id,
                'title' => $category->name,
                'handle' => $category->url_handle,
                'description' => $category->description ?? '',
                'products_count' => $category->real_products_count ?? 0,
                'url' => route('category.view', ['slug' => $category->url_handle]),
                'image' => [
                    'src' => $category->image ?? '/assets/no-image.jpg',
                    'alt' => $category->name
                ]
            ];
        }
        
        // Prepare data for the template
        $data = [
            'page_title' => 'Collections',
            'title' => 'Collections',
            'collections' => $collections
        ];
        
        // Return JSON if requested
        if ($request->wantsJson() || Str::endsWith($request->path(), '.json')) {
            return response()->json($data);
        }
        
        try {
            // Create context with data
            $context = $this->contextProvider->createContext($data);
            
            // Render template through the Liquid engine
            $result = app('liquid')->parseTemplate('templates/collections', $context);
            
            return response($result);
        } catch (\Exception $e) {
            // Fallback to basic HTML
            $html = "<!DOCTYPE html><html><head><title>{$data['title']}</title>";
            $html .= "<style>* { font-family: sans-serif !important; }</style>";
            $html .= "</head><body>";
            $html .= "<h1>{$data['title']}</h1>";
            $html .= "<div class=\"collections-grid\">";
            
            foreach ($collections as $collection) {
                $html .= "<div class=\"collection-item\">";
                $html .= "<a href=\"{$collection['url']}\" class=\"collection-link\">";
                $html .= "<div class=\"collection-details\">";
                $html .= "<h2 class=\"collection-title\">{$collection['title']}</h2>";
                $html .= "<div class=\"collection-product-count\">{$collection['products_count']} products</div>";
                $html .= "</div>";
                $html .= "</a>";
                $html .= "</div>";
            }
            
            $html .= "</div>";
            $html .= "</body></html>";
            
            return response($html);
        }
    }
    
    /**
     * Show a single collection and its products
     *
     * @param Request $request
     * @param string $handle
     * @return Response|JsonResponse
     */
    public function show(Request $request, string $handle): Response|JsonResponse
    {
        // Find the category by URL handle
        $category = Category::where('url_handle', $handle)->first();
        
        if (!$category) {
            return response('<h1>Collection not found</h1>', 404);
        }
        
        // Get products for this category
        $products = Product::where('category_id', $category->id)
            ->whereNotNull('category_id')
            ->where('active', 'yes')
            ->whereNull('deleted_at')
            ->where('is_hidden', 0)
            ->where(function ($query) {
                $query->whereNull('active_to')
                    ->orWhere('active_to', '>=', now());
            })
            ->where(function ($query) {
                $query->whereNull('publish_date')
                    ->orWhere('publish_date', '<=', now());
            })
            ->orderBy('order', 'desc')
            ->limit(24)
            ->get();
        
        // Convert products to Liquid template format
        $liquidProducts = [];
        foreach ($products as $product) {
            $liquidProducts[] = [
                'id' => $product->id,
                'title' => $product->name,
                'handle' => $product->url_handle,
                'description' => $product->description ?? '',
                'price' => $product->price,
                'available' => $product->active === 'yes',
                'url' => route('product.view', [
                    'category' => $product->category->url_handle ?? 'default', 
                    'product' => $product->url_handle
                ]),
                'featured_image' => [
                    'src' => $product->main_image ? $product->main_image : '/assets/no-image.jpg',
                    'alt' => $product->name
                ]
            ];
        }
        
        // Prepare collection data for the template
        $collection = [
            'id' => $category->id,
            'title' => $category->name,
            'handle' => $category->url_handle,
            'description' => $category->description ?? '',
            'products_count' => count($liquidProducts),
            'products' => $liquidProducts,
            'url' => route('category.view', ['slug' => $category->url_handle]),
            'image' => [
                'src' => $category->image ?? '/assets/no-image.jpg',
                'alt' => $category->name
            ]
        ];
        
        // Prepare data for the template
        $data = [
            'page_title' => $category->name,
            'title' => $category->name,
            'collection' => $collection
        ];
        
        // Return JSON if requested
        if ($request->wantsJson() || Str::endsWith($request->path(), '.json')) {
            return response()->json($data);
        }
        
        try {
            // Create context with data
            $context = $this->contextProvider->createContext($data);
            
            // Render template through the Liquid engine
            $result = app('liquid')->parseTemplate('templates/collection', $context);
            
            return response($result);
        } catch (\Exception $e) {
            // Fallback to basic HTML
            $html = "<!DOCTYPE html><html><head><title>{$collection['title']}</title>";
            $html .= "<style>* { font-family: sans-serif !important; }</style>";
            $html .= "</head><body>";
            $html .= "<h1>{$collection['title']}</h1>";
            $html .= "<div class=\"products-grid\">";
            
            foreach ($liquidProducts as $product) {
                $html .= "<div class=\"product-item\">";
                $html .= "<a href=\"{$product['url']}\" class=\"product-link\">";
                $html .= "<div class=\"product-details\">";
                $html .= "<h2 class=\"product-title\">{$product['title']}</h2>";
                $html .= "<div class=\"product-price\">{$product['price']}</div>";
                $html .= "</div>";
                $html .= "</a>";
                $html .= "</div>";
            }
            
            $html .= "</div>";
            $html .= "</body></html>";
            
            return response($html);
        }
    }
} 