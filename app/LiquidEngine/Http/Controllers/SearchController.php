<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers;

use App\LiquidEngine\Services\TemplateContextProvider;
use App\LiquidEngine\Services\TemplateLoader;
use App\Models\Product\Product;
use App\Models\Blog\Article;
use App\Models\Page\Page;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

/**
 * Controller for handling search functionality
 */
class SearchController extends LiquidController
{
    /**
     * Show search results
     *
     * @param Request $request
     * @return Response|JsonResponse
     */
    public function index(Request $request): Response|JsonResponse
    {
        $query = $request->query('q', '');
        $type = $request->query('type', 'product');
        $page = max(1, (int) $request->query('page', 1));
        $perPage = (int) $request->query('per_page', 24);
        
        // Initialize results
        $results = null;
        $totalResults = 0;
        
        if (!empty($query)) {
            // Cache key for search results
            $cacheKey = "search:{$query}:{$type}:{$page}:{$perPage}";
            
            // Check cache first
            if (Cache::has($cacheKey)) {
                $cachedResults = Cache::get($cacheKey);
                $results = $cachedResults['results'];
                $totalResults = $cachedResults['total'];
            } else {
                // Perform search based on type
                switch ($type) {
                    case 'product':
                        $results = Product::where(function ($q) use ($query) {
                                $q->where('title', 'like', "%{$query}%")
                                    ->orWhere('description', 'like', "%{$query}%")
                                    ->orWhere('sku', 'like', "%{$query}%");
                            })
                            ->active()
                            ->paginate($perPage, ['*'], 'page', $page);
                        $totalResults = $results->total();
                        break;
                        
                    case 'article':
                        $results = Article::where(function ($q) use ($query) {
                                $q->where('title', 'like', "%{$query}%")
                                    ->orWhere('content', 'like', "%{$query}%");
                            })
                            ->active()
                            ->paginate($perPage, ['*'], 'page', $page);
                        $totalResults = $results->total();
                        break;
                        
                    case 'page':
                        $results = Page::where(function ($q) use ($query) {
                                $q->where('name', 'like', "%{$query}%")
                                    ->orWhere('content', 'like', "%{$query}%");
                            })
                            ->active()
                            ->paginate($perPage, ['*'], 'page', $page);
                        $totalResults = $results->total();
                        break;
                        
                    default:
                        // Combined search (products, articles, pages)
                        $productResults = Product::where(function ($q) use ($query) {
                                $q->where('title', 'like', "%{$query}%")
                                    ->orWhere('description', 'like', "%{$query}%");
                            })
                            ->active()
                            ->take($perPage)
                            ->get();
                            
                        $articleResults = Article::where(function ($q) use ($query) {
                                $q->where('title', 'like', "%{$query}%")
                                    ->orWhere('content', 'like', "%{$query}%");
                            })
                            ->active()
                            ->take($perPage)
                            ->get();
                            
                        $pageResults = Page::where(function ($q) use ($query) {
                                $q->where('name', 'like', "%{$query}%")
                                    ->orWhere('content', 'like', "%{$query}%");
                            })
                            ->active()
                            ->take($perPage)
                            ->get();
                            
                        $results = [
                            'products' => $productResults,
                            'articles' => $articleResults,
                            'pages' => $pageResults
                        ];
                        
                        $totalResults = $productResults->count() + $articleResults->count() + $pageResults->count();
                        break;
                }
                
                // Cache the results
                Cache::put($cacheKey, [
                    'results' => $results,
                    'total' => $totalResults
                ], now()->addMinutes(30));
            }
        }
        
        // Prepare data for the template
        $data = [
            'title' => empty($query) ? 'Search' : "Search results for \"{$query}\"",
            'query' => $query,
            'type' => $type,
            'results' => $results,
            'total_results' => $totalResults,
            'page' => $page,
            'per_page' => $perPage,
        ];
        
        // Render the template
        return $this->renderTemplate('templates/search', $data);
    }
} 