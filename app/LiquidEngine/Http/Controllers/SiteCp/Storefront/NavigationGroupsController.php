<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\SiteCp\Storefront;

use App\Helper\Grid;
use App\Http\Request\SitecpV2\NavigationGroupRequest;
use App\Models\StoreFront\NavigationGroups;
use Exception;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Response;
use LView;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class NavigationGroupsController extends Controller
{
    /**
     * @var integer
     */
    protected $records_exist;

    /**
     * @param Request $request
     * @return View|Response
     */
    public function index(Request $request)
    {
        $this->records_exist = NavigationGroups::count();

        return $request->ajax() ? $this->_grid() : $this->_init();
    }

    /**
     * @return Response
     */
    public function create()
    {
        $menu = new NavigationGroups;

        return LView::panel('storefront.navigation.group.form', [
            'action' => route('admin.navigation.create'),
            'title' => __('global.action.text.add3', ['name' => __('storefront.header.navigation')]),
            'menu' => $menu,
        ]);
    }

    /**
     * @param NavigationGroupRequest $request
     * @return ResponseFactory|Response
     * @throws \Throwable
     */
    public function store(NavigationGroupRequest $request)
    {
        try {
            \Illuminate\Support\Facades\DB::transaction(function () use ($request) {
                return NavigationGroups::create($request->all());
            });

            return response([
                'status' => 'success',
                'msg' => __('global.succ.add'),
            ]);
        } catch (Exception $exception) {
            return response([
                'status' => 'error',
                'msg' => $exception->getMessage()
            ]);
        }
    }

    /**
     * @param $menu_id
     * @return Response
     */
    public function edit($menu_id)
    {
        $menu = NavigationGroups::findOrFail($menu_id);

        return LView::panel('storefront.navigation.group.form', [
            'action' => route('admin.navigation.edit', ['menu_id' => $menu->id]),
            'title' => __('global.action.text.edit', ['name' => $menu->name]),
            'menu' => $menu,
        ]);
    }

    /**
     * @param NavigationGroupRequest $request
     * @param $menu_id
     * @return ResponseFactory|Response
     * @throws \Throwable
     */
    public function update(NavigationGroupRequest $request, $menu_id)
    {
        $menu = NavigationGroups::findOrFail($menu_id);

        try {
            \Illuminate\Support\Facades\DB::transaction(function () use ($request, $menu) {
                return $menu->update($request->all());
            });

            return response([
                'status' => 'success',
                'msg' => __('global.succ.edit'),
            ]);
        } catch (Exception $exception) {
            return response([
                'status' => 'error',
                'msg' => $exception->getMessage()
            ]);
        }
    }

    /**
     * @param $menu_id
     * @return Response
     * @throws Exception
     * @throws Error
     */
    public function delete($menu_id)
    {
        try {
            NavigationGroups::findOrFail($menu_id)
                ->delete();

            return response([
                'action' => 'delete',
                'status' => 'success',
                'msg' => __('global.success.delete')
            ]);
        } catch (Exception $exception) {
            throw new Error($exception->getMessage());
        }
    }

    /**
     * @return Response
     */
    private function _grid()
    {
        $grid = new Grid();

        $navigations = NavigationGroups::withCount('links')->getRelationList(null, null, $grid);

        return $grid->generateIlluminate($navigations->map->formatListCp(), '', ['records' => $this->records_exist]);
    }

    /**
     * @return View
     */
    private function _init()
    {
        return LView::main('storefront.navigation.group.list', [
            'records_exist' => $this->records_exist,
        ]);
    }

}
