<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\SiteCp\Storefront\ThemeSettings;

use App\Helper\Grid;
use App\Http\Controllers\Controller;
use App\LiquidEngine\Helpers\Theme;
use App\LiquidEngine\Helpers\ThemeBlockSettingsParser;
use App\LiquidEngine\Helpers\ThemeSettingsParser;
use Exception;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use LView;
use App\LiquidEngine\Models\Theme as ThemeModel;
use App\LiquidEngine\Models\ThemeBlockGroups;
use App\LiquidEngine\Models\ThemeGroup;
use Throwable;

class SettingsController extends Controller
{
    /**
     * @var integer
     */
    protected $records_exist;

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    public function index(Request $request)
    {
        $this->records_exist = Theme::getSections()->count();

        return $request->ajax() ? $this->_grid() : $this->_init();
    }

    /**
     * @param $section
     * @return View
     */
    public function edit($section)
    {
        $block = null;
        if(strpos($section, '@')) {
            list($section, $block) = explode('@', $section, 2);
        }

        if($block) {
            return $this->_editBlock($section, $block);
        }

        return $this->_editRegular($section);
    }

    /**
     * @param Request $request
     * @param $section
     * @return ResponseFactory|Application|Response|void
     * @throws Throwable
     */
    public function update(Request $request, $section)
    {
        $block = null;
        if(strpos($section, '@')) {
            list($section, $block) = explode('@', $section, 2);
        }

        if($block) {
            return $this->_updateBlock($request, $section, $block);
        }

        return $this->_updateRegular($request, $section);
    }

    /**
     * @return Response
     */
    protected function _grid()
    {
        $grid = new Grid();

        $sections = collect();
        if($this->records_exist) {
            $sections = Theme::getSections();
        }

        return $grid->generateIlluminate($sections->map(function(array $section): array {
            return [
                'name' => '<a class="editable" href="' . route('admin.theme.settings.edit', ['section' => $section['key']]) . '">' . $section['name'] . '</a>',
            ];
        }), '', ['records' => $this->records_exist]);
    }

    /**
     * @return View
     */
    protected function _init()
    {
        return LView::main('storefront.theme.sections.list', [
            'records_exist' => $this->records_exist,
        ]);
    }

    /**
     * @param $section
     * @return View
     */
    protected function _editRegular($section)
    {
        if(!($data = Theme::getSectionData($section)) || !$data->getName() || !$data->getSettings()) {
            app()->abort(404);
        }

        $groups = new ThemeSettingsParser($data->getSettings(), $section);
        $groups = $groups->format();

        return LView::main('storefront.theme.sections.form', [
            'groups' => $groups,
            'name' => $data->getName() ? : $section,
            'section' => $section,
        ]);
    }

    /**
     * @param $section
     * @param $block
     * @return View
     */
    protected function _editBlock($section, $block)
    {
        if(!($data = Theme::getSectionData($section)) || !$data->hasBlock($block) || !($blockData = $data->getBlock($block)) || !$blockData->getSettings()) {
            app()->abort(404);
        }

        $groups = new ThemeBlockSettingsParser($blockData->getSettings(), $section, $block);
        $groups = $groups->formatBlocks();

        return LView::main('storefront.theme.sections.form-group', [
            'groups' => $groups,
            'name' => sprintf('%s - %s', $data->getName() ? : $section, $blockData->getName() ? : $block),
            'section' => sprintf('%s@%s', $section, $block),
            'block' => $block,
            'max_blocks' => $data->getMaxBlocks(),
        ]);
    }

    /**
     * @param Request $request
     * @param $section
     * @return ResponseFactory|Application|Response|void
     * @throws Throwable
     */
    protected function _updateRegular(Request $request, $section)
    {
        if(!($data = Theme::getSectionData($section)) || !$data->getName() || !$data->getSettings()) {
            app()->abort(404);
        }

        $theme = ThemeModel::theme()->firstOrFail();
        /** @var ThemeGroup $group */
        $group = $theme->group()->firstOrCreate([
            'group' => $section
        ], [
            'name' => $data->getName() ? : $section
        ]);

        $postData = new ThemeSettingsParser($data->getSettings(), $section);
        $postData = $postData->update($request->all());

        try {
            \Illuminate\Support\Facades\DB::transaction(function() use($group, $postData): void {
                $group->config()->delete();
                foreach($postData AS $parameter => $value) {
                    $group->config()->create([
                        'parameter' => $parameter,
                        'value' => $value
                    ]);
                }
            });

            ThemeModel::clearCache();

            return response([
                'msg' => __('global.succ.edit'),
                'status' => 'success',
                'redirect' => route('admin.theme.settings.list')
            ]);
        } catch (Exception $exception) {
            return response([
                'msg' => $exception->getMessage(),
                'status' => 'error'
            ]);
        }
    }

    /**
     * @param Request $request
     * @param $section
     * @param $block
     * @return ResponseFactory|Application|Response|void
     * @throws Throwable
     */
    protected function _updateBlock(Request $request, $section, $block)
    {
        if(!($data = Theme::getSectionData($section)) || !$data->hasBlock($block) || !($blockData = $data->getBlock($block)) || !$blockData->getSettings()) {
            app()->abort(404);
        }

        $theme = ThemeModel::theme()->firstOrFail();
        /** @var ThemeBlockGroups $group */
        $group = $theme->block_groups()->firstOrCreate([
            'group' => $section
        ], [
            'name' => $data->getName() ? : $section
        ]);

        $postData = new ThemeBlockSettingsParser($blockData->getSettings(), $section, $block);
        $postData = $postData->updateBlock($request->all());

        try {
            \Illuminate\Support\Facades\DB::transaction(function() use($group, $postData, $blockData, $block): void {
                $group->blocks()->where('type', $blockData->getType())->delete();
                foreach($postData AS $blockPostData) {
                    $block = $group->blocks()->create([
                        'name' => $blockData->getName() ? : $block,
                        'type' => $blockData->getType()
                    ]);
                    foreach($blockPostData AS $parameter => $value) {
                        $block->config()->create([
                            'parameter' => $parameter,
                            'value' => $value
                        ]);
                    }
                }
            });

            ThemeModel::clearCache();

            return response([
                'msg' => __('global.succ.edit'),
                'status' => 'success',
                'redirect' => route('admin.theme.settings.list')
            ]);
        } catch (Exception $exception) {
            return response([
                'msg' => $exception->getMessage(),
                'status' => 'error'
            ]);
        }
    }

}
