<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\Site;

use Illuminate\Http\Response;
use Cache;
use Illuminate\Support\Str;

/**
 * Class CacheController
 * @package App\Http\Controllers\Site
 * @deprecated
 */
class CacheController extends AbstractEngineController
{
    /**
     * @param $key
     * @return Response
     */
    public function flush($key)
    {
        if (!$this->validateKey($key)) {
            app()->abort(403, 'Invalid key!');
        }

        $this->clearSiteCache();

        return response('OK');
    }

    /**
     * @return bool
     */
    protected function clearSiteCache()
    {
        return Cache::driver(config('cache.default') . '-global')->forget($this->getSiteCacheKey() . ':*');
    }

    /**
     * @param $key
     * @return bool
     */
    protected function validateKey($key)
    {
        if (inDevelopment()) {
            return true;
        }

        return Str::lower($key) == Str::lower(md5(site('site_id')));
    }

    /**
     * @return string
     */
    protected function getSiteCacheKey(): string
    {
        return 'builder:' . site('site_id');
    }
}
