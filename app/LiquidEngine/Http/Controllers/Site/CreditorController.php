<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\Site;

use App\Exceptions\HttpNotFound;
use App\Models\Gateway\PaymentProviders;
use App\Models\Product\Product;
use View;
use Widget;
use App\Common\Theme;
use lib\widget\store\Leasing;
use App\Traits\IController;
use App\Models\Store\Cart AS CartModel;

class CreditorController extends AbstractEngineController
{

    use IController;

    public function __construct()
    {
        if (1 == Theme::getThemeEngine()) {
            View::setTemplateDir(Theme::getEngineDir());
        }
    }

    /**
     * @param mixed $product_id
     * @param mixed $price
     * @return mixed
     */
    public function select($product_id, $price = null)
    {
        $product = Product::withHidden()->listing()
            ->findOrFail($product_id)->format();

        if(!request()->ajax()) {
            return $this->to($product->url());
        }

        /** @var Leasing $widget */
        $widget = Widget::get('store.leasing');
        if(!$widget->getActivePaymentProvidersForProduct($product)->count()) {
            throw new HttpNotFound;
        }

        return View::panel('widgets/payment/creditors.tpl', [
            'product' => $product,
            'price' => $price ?? $product->price_input,
            'providers' => Widget::get('store.leasing')->getActivePaymentProviders()
        ], __('sf.leasing.btn.calculator'));
    }

    /**
     * @param mixed $provider
     * @param mixed $price
     * @param mixed $product_id
     * @param mixed $downPayment
     * @param mixed $installment
     * @return mixed
     */
    public function pricing($provider, $price, $product_id, $downPayment = 0, $installment = 0)
    {
        /** @var Leasing $widget */
        $widget = Widget::get('store.leasing');

        return $widget->renderPriceTable($provider, $price, explode('|', $product_id), $downPayment, $installment);
    }

    /**
     * @param $provider
     * @param int $downPayment
     * @param int $installment
     * @return string
     * @throws \App\Exceptions\Error
     * @throws \SmartyException
     * @throws \Throwable
     */
    public function checkout($provider, $downPayment = 0, $installment = 0)
    {
        $cartData = $this->getCartData();

        /** @var Leasing $widget */
        $widget = Widget::get('store.leasing');
        return $widget->renderCheckoutForm($provider, $cartData->orderTotal, $cartData->productIds, $downPayment, $installment);
    }

    /**
     * @return object
     * @throws \Throwable
     */
    public function getCartData()
    {
        if(!($cartInstance = CartModel::instance())) {
            return (object)['orderTotal' => 0, 'productIds' => []];
        }

        return (object)['orderTotal' => $cartInstance->getTotal('input'), 'productIds' => $cartInstance->products()->pluck('product_id','product_id')->all()];
    }

    /**
     * @param $provider
     * @param int $downPayment
     * @param int $installment
     * @return string
     * @throws \App\Exceptions\Error
     * @throws \SmartyException
     * @throws \Throwable
     */
    public function checkoutChange($provider, $downPayment = 0, $installment = 0)
    {
        $cartData = $this->getCartData();

        /** @var Leasing $widget */
        $widget = Widget::get('store.leasing');
        return $widget->renderCheckoutForm($provider, $cartData->orderTotal, $cartData->productIds, $downPayment, $installment, true);
    }

    /**
     * @param mixed $provider
     * @return mixed
     */
    public function consent($provider)
    {
        if (!$provider || !($paymentProvider = PaymentProviders::whereProvider($provider)->first()) || !$paymentProvider->consent) {
            app()->abort(404, __('error.no_longer_exists.page'));
        }

        return response($paymentProvider->consent, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="consent.pdf"',
            'Content-Transfer-Encoding' => 'binary',
            'Content-Length' => strlen($paymentProvider->consent),
            'Expires' => '0',
            'Pragma' => 'public',
            'Cache-Control' => 'must-revalidate',
            'Connection' => 'close',
        ]);
    }

}
