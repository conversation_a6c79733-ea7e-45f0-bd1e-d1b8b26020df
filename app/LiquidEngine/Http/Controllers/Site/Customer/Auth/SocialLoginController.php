<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\Site\Customer\Auth;

use App\Models\Oauth\SocialAccount;
use Exception;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;
use Socialite;
use Socialite\Providers\FacebookProvider;
use Socialite\Providers\GoogleProvider;
use Throwable;

class SocialLoginController extends LoginController
{

    /**
     * @param $provider
     * @return \Symfony\Component\HttpFoundation\RedirectResponse
     */
    public function socialLogin($provider)
    {
        $redirectUrl = route('socialite.oauth', ['provider' => $provider]);
        $redirectUrl = str_replace('http://', 'https://', $redirectUrl);

        /** @var GoogleProvider|FacebookProvider $driver */
        $driver = Socialite::driver($provider)
            ->scopes(config(sprintf('services.%s.scope', $provider), []));

        $driver->setState([
            'next' => route('site.auth.login.social.callback')
        ])->redirectUrl($redirectUrl);

        return $driver->redirect();
    }

    /**
     * @param Request $request
     * @return RedirectResponse|Response|string
     * @throws Throwable
     */
    public function socialLoginCallback(Request $request)
    {
        try {
            $hash = $request->get('hash');
            $socialite = decrypt($hash);

            /** @var SocialAccount $socialAccount */
            if (!isset($socialite['social_account']) || !($socialAccount = SocialAccount::find($socialite['social_account']))) {
                app()->abort(400, 'Not enough permissions granted');
            }

            if (isset($socialite['customer_id']) && $this->attemptLoginById($socialite['customer_id'])) {
                return $this->sendLoginResponse($request);
            }

            $name = explode(' ', $socialAccount->name, 2);

            return forward('site.auth.register', [
                'request' => $request,
                'appends' => [
                    'hash' => $hash,
                    'social_first_name' => $name[0],
                    'social_last_name' => $name[1] ?? null,
                    'social_email' => $socialAccount->email,
                ]
            ]);
        } catch (Exception $exception) {
            $message = $exception->getMessage();
            $code = $exception->getCode();
            if ($exception instanceof ClientException && ($json = json_decode($exception->getResponse()->getBody()->getContents(), true)) && !empty($json['error']['message'])) {
                $message = $json['error']['message'];
                $code = $json['error']['code'];
            }

            $code = $code >= 100 && $code < 600 ? $code : 400;

            return $this->templateResponse(new Template('templates.404'), [
                'code' => $code,
                'message' => $message,
            ], $code);
        }
    }

    /**
     * Attempt to log the user into the application.
     * @param integer $customer_id
     * @return Authenticatable
     */
    protected function attemptLoginById($customer_id)
    {
        return $this->guard()->loginUsingId($customer_id);
    }


}
