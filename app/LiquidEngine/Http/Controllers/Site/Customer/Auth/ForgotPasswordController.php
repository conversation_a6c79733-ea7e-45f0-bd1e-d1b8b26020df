<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\Site\Customer\Auth;

use App\Events\CustomerForgottenPasswordRequest;
use App\Exceptions\Error;
use App\Helper\Format;
use App\Helper\YesNo;
use App\LiquidEngine\Http\Controllers\Site\AbstractEngineController;
use App\Models\Customer\Customer;
use App\LiquidEngine\Http\Requests\Customer\Auth\ForgotPasswordRequest;
use Illuminate\Foundation\Auth\SendsPasswordResetEmails;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;
use Illuminate\View\View;
use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;
use SmartyException;

class ForgotPasswordController extends AbstractEngineController
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset emails and
    | includes a trait which assists in sending these notifications from
    | your application to your users. Feel free to explore this trait.
    |
    */

    use SendsPasswordResetEmails;

    /**
     * Display the form to request a password reset link.
     *
     * @return View
     */
    public function showLinkRequestForm()
    {
        $template = new Template('templates.customer.auth.forgotten-password');

        $data = [
            //
        ];

        $this->breadcrumbs()->add(
            __('pages.home'),
            route('site.home')
        );

        $this->breadcrumbs()->add(
            __('customer.text.login'),
            route('site.auth.login')
        );

        $this->breadcrumbs()->add(
            __('customer.text.forgotten_password'),
            route('site.auth.forgotten')
        );

        $data = array_merge($data, $this->getSeoData('accountForgottenPassword'));

        return $this->templateJson($template, $data);
    }

    /**
     * Send a reset link to the given user.
     *
     * @param ForgotPasswordRequest $request
     * @return RedirectResponse|Response
     * @throws Error
     * @throws ValidationException
     */
    public function sendResetLinkEmail(ForgotPasswordRequest $request)
    {
        $user = Customer::customerEmail($request->input('email'))->first();

        event(new CustomerForgottenPasswordRequest($user));

        if ($user->banned == YesNo::True) {

            if($request->isMethod('get')) {
                return redirect(null, 422)
                    ->back()
                    ->withErrors(__('customer.error.auth.banned', ['reason' => $user->banned_reason, 'date' => Format::datetime($user->date_banned)]));
            }

            throw ValidationException::withMessages([
                'email' => [__('customer.error.auth.banned', ['reason' => $user->banned_reason, 'date' => Format::datetime($user->date_banned)])],
            ]);
        }

        $code_valid_till = $user->generateRestorePassCode();

        $response = [
            'message' => __('customer.success.reset_email_sent', ['date' => $code_valid_till]),
            'events' => ['cc.user.forgotten.sent']
        ];

        if(!$request->ajax()) {
            return redirect()->back()
                ->with('success', $response['message']);
        }

        return response($response);
    }
}
