<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 29.8.2017 г.
 * Time: 15:13 ч.
 */
namespace App\LiquidEngine\Http\Controllers\Site\Customer\Addresses;

use App\Events\CustomerEdit;
use App\LiquidEngine\Assetics\Subscribers\Customer\ListShippingAddresses;
use App\LiquidEngine\Assetics\Subscribers\Customer\ShippingAddress;
use App\LiquidEngine\Helpers\RenderSection;
use App\LiquidEngine\Traits\Http\Customer\CustomerShippingAddress;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\Factory;
use Illuminate\Foundation\Application;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use App\LiquidEngine\LiquidHelpers\Drop\{Customer\Address, Paginate, Settings\Template};
use Illuminate\View\View as IlluminateView;
use Throwable;
use View;
use Exception;
use Illuminate\Http\Response;
use App\Models\Customer\CustomerShippingAddress as CustomerShippingAddressModel;
use App\Http\Request\SiteV2\Customer\AddressRequest;

class ShippingAddressController extends BaseAddressController
{

    use CustomerShippingAddress {
        CustomerShippingAddress::getShippingAddressForm as form;
    }

    /**
     * @return Factory|IlluminateView
     * @throws Throwable
     */
    public function index()
    {
        $template = new Template('templates.customer.addresses.shipping.list');

        $customer = $this->getCustomer();

        /** @var LengthAwarePaginator $addresses */
        $addresses = $customer->shipping_addresses()
            ->whereNull('office_id')
            //@todo display first default address
            ->orderByRaw('FIELD(id,?) DESC', [$customer->default_address_id])
            ->orderByDesc('id')->paginate();

        $data = [
            'title' => __('customer.text.shipping_addresses'),
            'pagination' => new Paginate($addresses),
            'addresses' => $addresses->getCollection()->map(function(CustomerShippingAddressModel $address) use($customer): \App\LiquidEngine\LiquidHelpers\Drop\Customer\Address {
                return new Address($address, $customer->default_address_id == $address->id);
            })->all(),
            'type' => 'shipping'
        ];

        $this->getBreadcrumbs()->add(
            __('customer.text.shipping_addresses'),
            route('site.account.address.shipping.list')
        );

        event('controller:customer:address:listShipping', new ListShippingAddresses($customer, $addresses));

        return $this->template($template, $data, $this->getSeoData('accountAddressesShipping'));
    }

    /**
     * @return Factory|Application|IlluminateView
     * @throws Throwable
     */
    public function create()
    {
        return $this->getShippingAddressCreate();
    }

    /**
     * @param AddressRequest $request
     * @return ResponseFactory|RedirectResponse|Response
     * @throws Throwable
     */
    public function store(AddressRequest $request)
    {
        if($address = $this->postShippingAddressCreate($request)) {

            $response = [
                'address' => Address::make($address, $address->id == $address->customer->default_address_id)->toArray(),
                'message' => __('sf.widget.account.succ.address_added')
            ];

            if($eventDatas = event('controller:customer:address:storeShippingAddress', new ShippingAddress($this->getCustomer(), $address))) {
                foreach($eventDatas as $eventData) {
                    if(is_array($eventData)) {
                        $response = array_merge_recursive($response, $eventData);
                    }
                }
            }

            if($request->ajax()) {
                return response($response);
            }

            return redirect()->back()
                ->with('success', $response['message']);
        }

        $e = $this->getShippingAddressPostError();
        if($request->ajax()) {
            return response(['message' => $e->getMessage()], 422);
        }

        return redirect()->back()
            ->withErrors($e->getMessage());
    }

    /**
     * @param $address_id
     * @return Factory|Application|IlluminateView
     * @throws Throwable
     */
    public function edit($address_id)
    {
        return $this->getShippingAddressUpdate($address_id);
    }

    /**
     * @param AddressRequest $request
     * @param $address_id
     * @return RedirectResponse|Response
     * @throws Throwable
     */
    public function update(AddressRequest $request, $address_id)
    {
        if($address = $this->postShippingAddressUpdate($request, $address_id)) {

            $response = [
                'address' => Address::make($address, $address->id == $address->customer->default_address_id)->toArray(),
                'message' => __('sf.widget.account.succ.address_edited')
            ];

            if($eventDatas = event('controller:customer:address:updateShippingAddress', new ShippingAddress($this->getCustomer(), $address))) {
                foreach($eventDatas as $eventData) {
                    if(is_array($eventData)) {
                        $response = array_merge_recursive($response, $eventData);
                    }
                }
            }

            if($request->ajax()) {
                return response($response);
            }

            return redirect()->back()
                ->with('success', $response['message']);
        }

        $e = $this->getShippingAddressPostError();
        if($request->ajax()) {
            return response(['message' => $e->getMessage()], 422);
        }

        return redirect()->back()
            ->withErrors($e->getMessage());
    }

    /**
     * @param Request $request
     * @param $address_id
     * @return RedirectResponse|Response
     * @throws Throwable
     */
    public function setDefault(Request $request, $address_id)
    {
        $customer = $this->getCustomer();

        $address = $customer->shipping_addresses()->find($address_id);
        if (!$address || $address->customer_id != $customer->id) {
            app()->abort(404, __('error.no_longer_exists.address'));
        }

        try {
            $customer->update([
                'default_address_id' => $address->id
            ]);

            event(new CustomerEdit($customer));

            $response = [
                'address' => Address::make($address, $address->id == $customer->default_address_id)->toArray(),
                'message' => __('sf.widget.account.succ.default_address_changed')
            ];

            if($eventDatas = event('controller:customer:address:updateDefaultShipping', new ShippingAddress($customer, $address))) {
                foreach($eventDatas as $eventData) {
                    if(is_array($eventData)) {
                        $response = array_merge_recursive($response, $eventData);
                    }
                }
            }

            if($request->ajax()) {
                return response($response);
            }

            return redirect()->back()
                ->with('success', $response['message']);

        } catch (Exception $exception) {
            if($request->ajax()) {
                return response(['message' => $exception->getMessage()], 422);
            }

            return redirect()->back()
                ->withErrors($exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @param $address_id
     * @return RedirectResponse|Response
     * @throws Throwable
     */
    public function remove(Request $request, $address_id)
    {
        $customer = $this->getCustomer();

        $address = $customer->shipping_addresses()->find($address_id);
        if (!$address || $address->customer_id != $customer->id) {
            app()->abort(404, __('error.no_longer_exists.address'));
        }

        if ($address->is_default) {
            if($request->ajax()) {
                return response(['message' => __('sf.widget.account.err.cannot_delete_default_address')], 422);
            }

            return redirect()->back()
                ->withErrors(__('sf.widget.account.err.cannot_delete_default_address'));
        }

        try {
            $address->delete();

            $response = [
                'address' => Address::make($address, $address->id == $customer->default_address_id)->toArray(),
                'message' => __('sf.widget.account.succ.address_removed')
            ];

            if($eventDatas = event('controller:customer:address:removeShippingAddress', new ShippingAddress($customer, $address))) {
                foreach($eventDatas as $eventData) {
                    if(is_array($eventData)) {
                        $response = array_merge_recursive($response, $eventData);
                    }
                }
            }

            if($request->ajax()) {
                return response($response);
            }

            return redirect()->back()
                ->with('success', $response['message']);

        } catch (Exception $exception) {
            if($request->ajax()) {
                return response(['message' => $exception->getMessage()], 422);
            }

            return redirect()->back()
                ->withErrors($exception->getMessage());
        }
    }

    /**
     * @param CustomerShippingAddressModel $address
     * @return RenderSection|View
     * @throws Throwable
     */
    protected function getShippingAddressForm(CustomerShippingAddressModel $address)
    {
        $template = new Template('templates.customer.addresses.shipping.form');

        $this->getBreadcrumbs()->add(
            __('customer.text.shipping_addresses'),
            route('site.account.address.shipping.list')
        );

        if ($address->exists) {
            $this->breadcrumbs()->add(
                __('customer.text.edit_address'),
                route('site.account.address.shipping.edit', ['address_id' => $address->id])
            );
        } else {
            $this->breadcrumbs()->add(
                __('customer.text.create_address'),
                route('site.account.address.shipping.create')
            );
        }

        $data = [
            'content_for_index' => $this->form($address, [
                'action' => $address->exists ? route('site.account.address.shipping.edit', ['address_id' => $address->id])
                    : route('site.account.address.shipping.create'),
            ]),
            'title' => $address->exists ? __('customer.text.edit_address') : __('customer.text.create_address'),
        ];

        event(
            $address->exists ? 'controller:customer:address:editShippingAddress' : 'controller:customer:address:createShippingAddress',
            new ShippingAddress($this->getCustomer(), $address)
        );

        return $this->template($template, $data, $this->getSeoData('accountAddressesShipping'));
    }

}
