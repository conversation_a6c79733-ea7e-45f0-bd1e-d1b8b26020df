<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 29.8.2017 г.
 * Time: 15:13 ч.
 */
namespace App\LiquidEngine\Http\Controllers\Site\Customer\Addresses;

use App\Events\CustomerEdit;
use App\LiquidEngine\Assetics\Subscribers\Customer\ListBillingAddresses;
use App\LiquidEngine\Assetics\Subscribers\Customer\BillingAddress;
use App\LiquidEngine\Helpers\RenderSection;
use App\LiquidEngine\Traits\Http\Customer\CustomerBillingAddress;
use Illuminate\Contracts\View\Factory;
use Illuminate\Foundation\Application;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Routing\Redirector;
use Illuminate\View\View;
use App\LiquidEngine\LiquidHelpers\{
    Drop\Customer\Address,
    Drop\Paginate,
    Drop\Settings\Template};
use Throwable;
use Exception;
use Illuminate\Http\Response;
use App\Models\Customer\CustomerBillingAddress as CustomerBillingAddressModel;
use App\Http\Request\SiteV2\Customer\AddressRequest;

class BillingAddressController extends BaseAddressController
{

    use CustomerBillingAddress {
        CustomerBillingAddress::getBillingAddressForm as form;
    }

    /**
     * @return Factory|View
     * @throws Throwable
     */
    public function index()
    {
        $this->checkEnabledBillingAddress();

        $template = new Template('templates.customer.addresses.billing.list');

        $customer = $this->getCustomer();

        /** @var LengthAwarePaginator $addresses */
        $addresses = $customer->billing_addresses()
            //@todo display first default address
            ->orderByRaw('FIELD(id,?) DESC', [$customer->default_billing_address_id])
            ->orderByDesc('id')->paginate();

        $data = [
            'title' => __('customer.text.billing_addresses'),
            'pagination' => new Paginate($addresses),
            'addresses' => $addresses->getCollection()->map(function (CustomerBillingAddressModel $address) use($customer): \App\LiquidEngine\LiquidHelpers\Drop\Customer\Address {
                return new Address($address, $customer->default_billing_address_id == $address->id);
            })->all(),
            'type' => 'billing'
        ];

        $this->getBreadcrumbs()->add(
            __('customer.text.billing_addresses'),
            route('site.account.address.billing.list')
        );

        event('controller:customer:address:listBilling', new ListBillingAddresses($customer, $addresses));

        return $this->template($template, $data, $this->getSeoData('accountAddressesBilling'));
    }

    /**
     * @return Factory|Application|View
     * @throws Throwable
     */
    public function create()
    {
        return $this->getBillingAddressCreate();
    }

    /**
     * @param AddressRequest $request
     * @return RedirectResponse|Response
     * @throws Throwable
     */
    public function store(AddressRequest $request)
    {
        if($address = $this->postBillingAddressCreate($request)) {

            $response = [
                'address' => Address::make($address, $address->id == $address->customer->default_billing_address_id)->toArray(),
                'message' => __('sf.widget.account.succ.address_added')
            ];

            if($eventDatas = event('controller:customer:address:storeBillingAddress', new BillingAddress($this->getCustomer(), $address))) {
                foreach($eventDatas as $eventData) {
                    if(is_array($eventData)) {
                        $response = array_merge_recursive($response, $eventData);
                    }
                }
            }

            if($request->ajax()) {
                return response($response);
            }

            return redirect()->back()
                ->with('success', $response['message']);
        }

        $e = $this->getBillingAddressPostError();
        if($request->ajax()) {
            return response(['message' => $e->getMessage()], 422);
        }

        return redirect()->back()
            ->withErrors($e->getMessage());
    }

    /**
     * @param $address_id
     * @return RenderSection|RedirectResponse|Response|Redirector|View
     * @throws Throwable
     */
    public function edit($address_id)
    {
        return $this->getBillingAddressUpdate($address_id);
    }

    /**
     * @param AddressRequest $request
     * @param $address_id
     * @return RedirectResponse|Response
     * @throws Throwable
     */
    public function update(AddressRequest $request, $address_id)
    {
        if($address = $this->postBillingAddressUpdate($request, $address_id)) {

            $response = [
                'address' => Address::make($address, $address->id == $address->customer->default_billing_address_id)->toArray(),
                'message' => __('sf.widget.account.succ.address_edited')
            ];

            if($eventDatas = event('controller:customer:address:updateBillingAddress', new BillingAddress($this->getCustomer(), $address))) {
                foreach($eventDatas as $eventData) {
                    if(is_array($eventData)) {
                        $response = array_merge_recursive($response, $eventData);
                    }
                }
            }

            if($request->ajax()) {
                return response($response);
            }

            return redirect()->back()
                ->with('success', $response['message']);
        }

        $e = $this->getBillingAddressPostError();
        if($request->ajax()) {
            return response(['message' => $e->getMessage()], 422);
        }

        return redirect()->back()
            ->withErrors($e->getMessage());
    }

    /**
     * @param Request $request
     * @param $address_id
     * @return RedirectResponse|Response
     * @throws Throwable
     */
    public function setDefault(Request $request, $address_id)
    {
        $this->checkEnabledBillingAddress();

        $customer = $this->getCustomer();
        $address = $customer->billing_addresses()->find($address_id);
        if (!$address) {
            app()->abort(404, __('sf.account.details.warn.address_no_longer_exists'));
        }

        if ($address->customer_id != $customer->id) {
            app()->abort(403, __('global.err.this_address_is_not_your'));
        }

        try {
            $customer->update([
                'default_billing_address_id' => $address->id
            ]);

            event(new CustomerEdit($customer));

            $response = [
                'address' => Address::make($address, $address->id == $customer->default_billing_address_id)->toArray(),
                'message' => __('sf.widget.account.succ.default_address_changed')
            ];

            if($eventDatas = event('controller:customer:address:updateDefaultBilling', new BillingAddress($customer, $address))) {
                foreach($eventDatas as $eventData) {
                    if(is_array($eventData)) {
                        $response = array_merge_recursive($response, $eventData);
                    }
                }
            }

            if($request->ajax()) {
                return response($response);
            }

            return redirect()->back()
                ->with('success', $response['message']);
        } catch (Exception $exception) {
            if($request->ajax()) {
                return response(['message' => $exception->getMessage()], 422);
            }

            return redirect()->back()
                ->withErrors($exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @param $address_id
     * @return RedirectResponse|Response
     * @throws Throwable
     */
    public function remove(Request $request, $address_id)
    {
        $this->checkEnabledBillingAddress();

        $customer = $this->getCustomer();
        $address = $customer->billing_addresses()->find($address_id);
        if (!$address) {
            app()->abort(404, __('sf.account.details.warn.address_no_longer_exists'));
        }

        if ($address->customer_id != $customer->id) {
            app()->abort(403, __('global.err.this_address_is_not_your'));
        }

        if ($address->is_default) {
            if($request->ajax()) {
                return response(['message' => __('sf.widget.account.err.cannot_delete_default_address')], 422);
            }

            return redirect()->back()
                ->withErrors(__('sf.widget.account.err.cannot_delete_default_address'));
        }

        try {
            $address->delete();

            $response = [
                'address' => Address::make($address, $address->id == $customer->default_address_id)->toArray(),
                'message' => __('sf.widget.account.succ.address_removed')
            ];

            if($eventDatas = event('controller:customer:address:removeBillingAddress', new BillingAddress($customer, $address))) {
                foreach($eventDatas as $eventData) {
                    if(is_array($eventData)) {
                        $response = array_merge_recursive($response, $eventData);
                    }
                }
            }

            if($request->ajax()) {
                return response($response);
            }

            return redirect()->back()
                ->with('success', $response['message']);
        } catch (Exception $exception) {
            if($request->ajax()) {
                return response(['message' => $exception->getMessage()], 422);
            }

            return redirect()->back()
                ->withErrors($exception->getMessage());
        }
    }

    /**
     * @param CustomerBillingAddressModel $address
     * @return RenderSection|View
     * @throws Throwable
     */
    protected function getBillingAddressForm(CustomerBillingAddressModel $address)
    {
        $template = new Template('templates.customer.addresses.billing.form');

        $this->getBreadcrumbs()->add(
            __('customer.text.billing_addresses'),
            route('site.account.address.billing.list')
        );

        if ($address->exists) {
            $this->breadcrumbs()->add(
                __('customer.text.edit_address'),
                route('site.account.address.billing.edit', ['address_id' => $address->id])
            );
        } else {
            $this->breadcrumbs()->add(
                __('customer.text.create_address'),
                route('site.account.address.billing.create')
            );
        }

        $data = [
            'content_for_index' => $this->form($address, [
                'action' => $address->exists ? route('site.account.address.billing.edit', ['address_id' => $address->id])
                    : route('site.account.address.billing.create'),
            ]),
            'title' => $address->exists ? __('customer.text.edit_address') : __('customer.text.create_address'),
        ];

        event(
            $address->exists ? 'controller:customer:address:editBillingAddress' : 'controller:customer:address:createBillingAddress',
            new BillingAddress($this->getCustomer(), $address)
        );

        return $this->template($template, $data, $this->getSeoData('accountAddressesBilling'));
    }

}
