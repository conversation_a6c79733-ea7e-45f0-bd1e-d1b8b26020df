<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\Site;

use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;
use App\Models\Product\Product;

/**
 * Example controller demonstrating the new automatic template resolution
 */
class ExampleController extends AbstractEngineController
{
    /**
     * Display the index page
     * 
     * @return mixed
     */
    public function index()
    {
        // Get featured products
        $products = Product::where('featured', 1)
            ->where('status', 'active')
            ->limit(10)
            ->get();
            
        // Prepare data for the template
        $data = [
            'title' => 'Featured Products',
            'products' => $products,
            'meta_description' => 'Check out our featured products'
        ];
        
        // Using automatic template resolution (will look for templates.example.index)
        return $this->renderTemplate($data);
    }
    
    /**
     * Display a specific product
     * 
     * @param string $slug
     * @return mixed
     */
    public function show($slug)
    {
        // Find the product
        $product = Product::where('slug', $slug)
            ->where('status', 'active')
            ->firstOrFail();
            
        // Prepare data for the template
        $data = [
            'title' => $product->name,
            'product' => $product,
            'meta_description' => $product->seo_description ?? $product->short_description
        ];
        
        // Using automatic template resolution (will look for templates.example.show)
        return $this->renderTemplate($data);
    }
    
    /**
     * Example of using a specific template
     * 
     * @return mixed
     */
    public function custom()
    {
        $data = [
            'title' => 'Custom Template Example',
            'content' => 'This is rendered using a specific template'
        ];
        
        // Explicitly specify the template to use
        return $this->renderSpecificTemplate('templates.custom', $data);
    }
    
    /**
     * Example of returning JSON response
     * 
     * @return mixed
     */
    public function api()
    {
        $products = Product::where('status', 'active')
            ->limit(5)
            ->get(['id', 'name', 'price']);
            
        return $this->renderJson([
            'success' => true,
            'products' => $products
        ]);
    }
} 