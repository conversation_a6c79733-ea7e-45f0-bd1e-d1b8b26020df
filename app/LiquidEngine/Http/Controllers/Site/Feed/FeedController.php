<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\Site\Feed;

use App\Exceptions\Error;
use App\LiquidEngine\Helpers\Xml\RssFeed;
use App\LiquidEngine\Http\Controllers\Site\Product\AbstractProductsListing;
use Illuminate\Http\Response;

class FeedController extends AbstractProductsListing
{

    /**
     * @return Response
     * @throws Error
     */
    public function index()
    {
        $xsl = route('site.feed.xsl');

        $products = $this->getProducts();

        $feedGenerator = new RssFeed('feed');
        $feedGenerator->setXsl($xsl);

        /** @var Response $response */
        $response = response($feedGenerator->generate($products));
        $response->header('Content-Type', 'application/xml; charset=utf-8');

        return $response;
    }

    public function xsl()
    {
        $style = sprintf(
            '%snew-themes/system/rss/%s?%s',
            config('url.img'),
            'rss.css',
            app('last_build')
        );

        return response('<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0"
xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
<xsl:template match="/">
<html>
<head>
<link rel="stylesheet" type="text/css" href="' . $style . '" />
</head>
<body>

<xsl:for-each select="rss/channel/item">
<div class="product-rss">
    <xsl:value-of select="description" disable-output-escaping="yes"/>
</div>
</xsl:for-each>

</body>
</html>
</xsl:template>
</xsl:stylesheet>')
            ->header('content-type', 'text/xsl'); //xsl

/*        return '<?xml version="1.0" encoding="UTF-8"?> */
//<xsl:stylesheet version="1.0"
//xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
//<xsl:template match="/">
//<html>
//<head>
//<link rel="stylesheet" type="text/css" href="' . $style . '" />
//</head>
//<body>
//<h1 align="center">Students\' Basic Details</h1>
//<table border="3" align="center" >
//<tr>
//	<th>Name</th>
//	<th>Branch</th>
//</tr>
//	<xsl:for-each select="rss/channel/item">
//<tr>
//	<td>
//        <img>
//            <xsl:attribute name="src">
//                <xsl:value-of select="image" />
//            </xsl:attribute>
//        </img>
//    </td>
//	<td>
//	    <xsl:value-of select="description" disable-output-escaping="yes"/>
//    </td>
//</tr>
//	</xsl:for-each>
//	</table>
//</body>
//</html>
//</xsl:template>
//</xsl:stylesheet>
//';
    }
}
