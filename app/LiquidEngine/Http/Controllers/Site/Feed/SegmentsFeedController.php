<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 15.12.2016 г.
 * Time: 22:21 ч.
 */
namespace App\LiquidEngine\Http\Controllers\Site\Feed;

use App\Common\DateTimeFormat;
use App\Integration\Facebook\Models\MessengerSubscriber;
use App\Models\Customer\Customer;
use App\Models\Segment\CustomersSegment;
use App\LiquidEngine\Http\Controllers\Site\AbstractEngineController;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\StreamedResponse;

class SegmentsFeedController extends AbstractEngineController
{

    /**
     * @var resource $handle
     */
    protected $handle;

    /**
     * @param $hash
     * @return mixed
     */
    public function index($hash): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $segment = CustomersSegment::getByHash($hash);
        if(!$segment || !in_array($segment->channel, ['cloudcart', 'messenger'])) {
            app()->abort(404, __('error.no_longer_exists.page'));
        }

        $file_name = null;
        $data = [];
        if($segment->channel == 'cloudcart') {
            $file_name = 'customers_by_segment.csv';
            $data = function() use($segment) {
                return $this->getCustomersFeedData($segment->id);
            };
        } elseif($segment->channel == 'messenger') {
            $file_name = 'messenger_subscribers_by_segment.csv';
            $data = function() use($segment) {
                return $this->getMessengerSubscribersFeedData($segment->id);
            };
        }

        $headers = [
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Content-type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename=' . $file_name,
            'Expires' => '0',
            'Pragma' => 'public',
        ];

        set_time_limit(0);

        $this->handle = fopen('php://output', 'a');
        //@todo add utf-8BOM
        fputs($this->handle, chr(0xEF) . chr(0xBB) . chr(0xBF));

        return new StreamedResponse(function () use($data): void {
            foreach (value($data) AS $values) {
                fputcsv($this->handle, $values);
            }

            fclose($this->handle);

        }, 200, $headers);
    }

    /**
     * @param $segmentId
     * @return Collection
     */
    protected function getCustomersFeedData($segmentId)
    {
        return Customer::whereExists(function($query) use($segmentId): void {
            /** @var Builder $query */
            $query->from('customer_to_segments')
                ->where('segment_id', $segmentId)
                ->whereColumn('customer_to_segments.customer_id', 'customers.id');
        })->get(['first_name', 'last_name', 'email', 'date_added'])
            ->map(function(Customer $customer): array {
                return [
                    $customer->first_name,
                    $customer->last_name,
                    $customer->email,
                    $customer->date_added->format(DateTimeFormat::$date_formats[setting('date_format')]['format']),
                ];
            })->prepend([
            __('customer.label.first_name'),
            __('customer.label.last_name'),
            __('customer.label.email'),
            __('customer.date_added')
        ]);
    }

    /**
     * @param $segmentId
     * @return Collection
     */
    protected function getMessengerSubscribersFeedData($segmentId)
    {
        return MessengerSubscriber::whereExists(function($query) use($segmentId): void {
            /** @var Builder $query */
            $query->from('messenger_subscriber_to_segments')
                ->where('segment_id', $segmentId)
                ->whereColumn('messenger_subscriber_to_segments.subscriber_id', 'messenger_subscribers.id');
        })->get(['first_name', 'last_name', 'created_at'])
            ->map(function(MessengerSubscriber $subscriber): array {
                return [
                    $subscriber->first_name,
                    $subscriber->last_name,
                    $subscriber->created_at->format(DateTimeFormat::$date_formats[setting('date_format')]['format']),
                ];
            })->prepend([
            __('customer.label.first_name'),
            __('customer.label.last_name'),
            __('customer.date_added')
        ]);
    }
}
