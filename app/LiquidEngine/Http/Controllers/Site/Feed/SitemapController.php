<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 19.9.2016 г.
 * Time: 14:09 ч.
 */
namespace App\LiquidEngine\Http\Controllers\Site\Feed;

use App\Helper\Sitemap;
use App\LiquidEngine\Http\Controllers\Site\AbstractEngineController;
use Illuminate\Http\Response;

class SitemapController extends AbstractEngineController
{

    /**
     * @param $extension
     * @return Response
     */
    public function base($extension)
    {
        $sitemap = new Sitemap('base', 1, $extension);
        return $this->response($sitemap->render(), $sitemap->lastModifiedUtc());
    }

    /**
     * @param $name
     * @param $page
     * @param $extension
     * @return Response
     */
    public function named($name, $page, $extension)
    {
        $sitemap = new Sitemap($name, $page, $extension);
        return $this->response($sitemap->render(), $sitemap->lastModifiedUtc());
    }

    /**
     * @param string $output
     * @param $last_modified
     * @return Response
     */
    public function response($output, string $last_modified)
    {
        $response = response($output);
        $response->header('Expires', 'Mon, 26 Jul 1997 05:00:00 GMT');
        $response->header('Last-Modified', $last_modified . " GMT");
        $response->header('Cache-Control', 'no-store, no-cache, must-revalidate');
        $response->header('Cache-Control', 'post-check=0, pre-check=0', false);
        $response->header('Pragma', 'no-cache');
        $response->header('Content-Type', 'application/xml; charset=utf-8');
        $response->header('Content-Length', strlen($output), true);
        return $response;
    }

}
