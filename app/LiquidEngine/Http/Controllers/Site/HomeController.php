<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 26.7.2017 г.
 * Time: 13:13
 */
namespace App\LiquidEngine\Http\Controllers\Site;

use App\Helper\CustomHomePage;
use App\LiquidEngine\Assetics\Subscribers\HomePage;
use App\LiquidEngine\Helpers\RenderSection;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\View\View;
use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;
use Liquid\Factory;
use Throwable;
use Illuminate\Support\Facades\Log;

class HomeController extends AbstractEngineController
{

    /**
     * @param Request $request
     * @return RenderSection|Factory
     */
    public function index(Request $request)
    {
//        if (CustomHomePage::showCustomHomePage($request) && ($page = CustomHomePage::getSystemHomePage())) {
//            $pageController = new PageController();
//            $pageController->setPage($page);
//
//            return response($pageController->get());
//        }

        $template = new Template('index');

        $data = [
            //
        ];

        $this->breadcrumbs()->add(
            __('pages.home'),
            route('site.home')
        );

        event('controller:homePage', new HomePage());

        try {
            return $this->template($template, $data, $this->getSeoData('home'));
        } catch (Throwable $e) {
            // Return a simple fallback page
            return response('Error loading home page: ' . $e->getMessage());
        }
    }

}
