<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\Site\Marketing\UpSell;

use App\Helper\Format;
use App\Models\Marketing\UpSell\UpSell;
use App\Models\Product\Product;
use App\Models\Store\Cart as CartModel;
use App\LiquidEngine\Http\Controllers\Site\AbstractEngineController;
use LView;

class IndexController extends AbstractEngineController
{
    /**
     * @param $up_sell_id
     * @param $variant_id
     * @param $quantity
     * @return \Illuminate\Http\Response
     * @throws \Throwable
     */
    public function index($up_sell_id, $variant_id, $quantity)
    {
        $cartInstance = CartModel::instance();
        /** @var UpSell $up_sell */
        $up_sell = UpSell::active()->whereTriggerVariantId($variant_id)
            ->whereNotIn('offer_variant_id', $cartInstance ? $cartInstance->items->pluck('variant_id') : [])
            ->whereHas('trigger_variant')->whereHas('offer_variant')
            ->whereHas('trigger_product', function ($query): void {
                /** @var Product $query */
                $query->visible();
            })->whereHas('offer_product', function ($query): void {
                /** @var Product $query */
                $query->visible();
            })->inStockOfferCheck()->outStockTriggerCheck()->with(['child_no' => function ($query): void {
                /** @var UpSell $query */
                $query->active();
            }, 'child_yes' => function ($query): void {
                /** @var UpSell $query */
                $query->active();
            }, 'trigger_variant.detailed_discount', 'offer_variant.detailed_discount', 'trigger_variant.item' => function ($query): void {
                /** @var Product $query */
                $query->listing();
            }, 'offer_variant.item' => function ($query): void {
                /** @var Product $query */
                $query->listing();
            }])->findOrFail($up_sell_id);

        $up_sell->format();

        $variants = [];
        for ($i = 1; $i <= $up_sell->offer_variant->item->total_variants; $i++) {
            $variants[] = [
                'name' => $up_sell->offer_variant->item->getAttribute('p' . $i),
                'parameter_id' => $up_sell->offer_variant->item->getAttribute('p' . $i . '_id'),
                'value' => $up_sell->offer_variant->getAttribute('v' . $i),
                'parameter_option_id' => $up_sell->offer_variant->getAttribute('v' . $i . '_id'),
            ];
        }

        dd($up_sell);

        $view = LView::modal('upSell.popup', [
            'up_sell' => $up_sell,
            'product_id' => $up_sell->trigger_product_id,
            'variant_id' => $variant_id,
            'quantity' => $up_sell->offer_quantity > 1 ? $up_sell->offer_quantity : ((int)$quantity > 0 ? (int)$quantity : 1),
            'only' => Format::money($up_sell->only),
            'variants' => $variants,
            'cc_cart' => $cartInstance
        ], null, false, null, ['dependency' => \CatalogProduct::getDependency()]);

        $up_sell->increment('views');

        return $view;
    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Symfony\Component\HttpFoundation\Response
     */
    public function discard($id)
    {
        try {
            if(!is_null($record = UpSell::find($id))) {
                $record->update([
                    'total_cancel' => $record->total_cancel + 1
                ]);
            }

            return response([
                'status' => 'success'
            ]);
        } catch (\Exception $exception) {
            return response([
                'status' => 'error'
            ]);
        }
    }
}
