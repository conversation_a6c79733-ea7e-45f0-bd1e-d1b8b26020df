<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\Site\Product;

use App\LiquidEngine\Assetics\Subscribers\ListBundles;
use App\Models\Product\BundleProduct;
use App\Models\Product\Category;
use App\Models\Product\Product;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\View\View;
use App\LiquidEngine\LiquidHelpers\Drop\{Paginate, ProductByType, Settings\Template};

class BundlesController extends AbstractProductsListing
{

    /**
     * @param Request $request
     * @return ResponseFactory|Response|View
     */
    public function index(Request $request)
    {
        return $this->category($request);
    }

    /**
     * @param Request $request
     * @param null $url_handle
     * @return ResponseFactory|Response|View
     */
    public function category(Request $request, $url_handle = null)
    {
        $filters = null;
        $category = null;
        if($url_handle) {
            $category = Category::urlHandle($url_handle)->first();

            if(!$category) {
                app()->abort(404, __('error.no_longer_exists.category'));
            }

            $filters['category_id'] = $category->id;
        }

        $template = new Template('templates.list-bundles');

        /** @var LengthAwarePaginator $products */
        $products = $this->getProducts($filters, BundleProduct::class);

        $data = [
            'pagination' => new Paginate($products),
            'active_filters' => $this->getAllowedActiveFilters(),
            'filters' => $this->getFilters(),

            'products' => $products->getCollection()->map(function(Product $product) {
                return ProductByType::get($product);
            })->all(),
            'title' => __('pages.bundles'),
            'page' => 'bundles',
        ];

        $this->breadcrumbs()->add(
            __('pages.home'),
            route('site.home')
        );

        if($category) {
            $this->breadcrumbs()->add(
                __('pages.bundles_category', ['category' => $category->name]),
                route('bundles.list.category', ['slug' => $category->url_handle])
            );
        } else {
            $this->breadcrumbs()->add(
                __('pages.bundles'),
                route('bundles.list.list')
            );
        }

        event('controller:listBundles', new ListBundles($category, $products));

        return $this->template($template, $data, $this->getSeoData('bundles'));
    }

}
