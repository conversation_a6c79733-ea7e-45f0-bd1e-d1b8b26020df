<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\Site\Product;

use App\Models\Product\Product;
use App\LiquidEngine\Assetics\Subscribers\Search;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\View\View;
use App\LiquidEngine\LiquidHelpers\Drop\{Paginate, ProductByType, Settings\Template};

class SearchController extends AbstractProductsListing
{
    /**
     * @param Request $request
     * @return Factory|\Illuminate\Contracts\View\View|View
     */
    public function index(Request $request)
    {
        $template = new Template('templates.search');

        /** @var LengthAwarePaginator $products */
        $products = $this->getProducts(['search' => $search = $request->query('search')]);

        $data = [
            'pagination' => new Paginate($products),
            'active_filters' => $this->getAllowedActiveFilters(),
            'filters' => $this->getFilters(),

            'products' => $products->getCollection()->map(function(Product $product) {
                return ProductByType::get($product);
            })->all(),
            'title' => __('pages.search'),
            'page' => 'search',
        ];

        $this->breadcrumbs()->add(
            __('pages.home'),
            route('site.home')
        );

        $this->breadcrumbs()->add(
            __('global.text.search_results', ['search' => $search]),
            route('search', ['search' => $search])
        );

        event('controller:search', new Search($search, $products));

        return $this->template($template, $data, $this->getSeoData('searchResults'));
    }

    /**
     * @param Request $request
     * @return array|ResponseFactory|Response
     */
    public function autoComplete(Request $request)
    {
        if(empty($search = $request->get('term'))) {
            return [];
        }

        /** @var LengthAwarePaginator $products */
        $products = $this->getProducts(['search' => $search]);

        $products = $products->getCollection()->map(function(Product $product) {
            return ProductByType::get($product);
        })->all();

        return $this->response($products);
    }
}
