<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\Site\Product;

use App\Models\Product\Product;
use App\Models\Selection\ProductSelection;
use App\LiquidEngine\Assetics\Subscribers\ViewSelection;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\View\View;
use App\LiquidEngine\LiquidHelpers\Drop\{Paginate, ProductByType, Selection, Settings\Template};

class SelectionController extends AbstractProductsListing
{

    /**
     * @param Request $request
     * @param $slug
     * @return ResponseFactory|Response|View
     */
    public function index(Request $request, $slug)
    {
        $selection = ProductSelection::urlHandle($slug)->first();

        if(!$selection) {
            app()->abort(404, __('error.no_longer_exists.selection'));
        }

        $template = new Template('templates.list-products');

        /** @var LengthAwarePaginator $products */
        $products = $this->getProducts(['selection_id' => $selection->id]);

        $data = [
            'pagination' => new Paginate($products),
            'active_filters' => $this->getAllowedActiveFilters(),
            'filters' => $this->getFilters(),
            'seo_title' => $selection->seo_title?:$selection->name,
            'seo_description' => $selection->seo_description,

            'products' => $products->getCollection()->map(function(Product $product) {
                return ProductByType::get($product);
            })->all(),
            'title' => $selection->name,
            'selection' => new Selection($selection),
            'page' => 'selection',
        ];

        $this->breadcrumbs()->add(
            __('pages.home'),
            route('site.home')
        );

        $this->breadcrumbs()->add(
            $selection->name,
            $selection->url
        );

        event('controller:viewSelection', new ViewSelection($selection, $products));

        return $this->template($template, $data);
    }
}
