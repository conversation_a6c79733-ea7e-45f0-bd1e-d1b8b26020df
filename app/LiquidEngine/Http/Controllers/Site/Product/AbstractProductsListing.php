<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.4.2019 г.
 * Time: 11:50 ч.
 */
namespace App\LiquidEngine\Http\Controllers\Site\Product;

use Apps;
use App\Helper\Format;
use App\Helper\Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\LiquidEngine\Helpers\Catalog\Product\Sorting;
use App\LiquidEngine\Helpers\ThemeSettingsParser\OrderBySelect;
use App\LiquidEngine\Http\Controllers\Site\AbstractEngineController;
use App\Models\Product\BundleProduct;
use App\Models\Product\Parameter;
use App\Models\Product\ParameterOption;
use App\Models\Product\Vendor;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Models\Product\Product;
use App\Models\Category\Property;
use Illuminate\Database\Eloquent\Builder;
use Modules\BrandModel\Models\Brand;
use Modules\BrandModel\Models\Model;
use App\LiquidEngine\LiquidHelpers\Drop\Filters\ActiveFilters;
use App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\PriceRanges;
use App\LiquidEngine\LiquidHelpers\Drop\Filters\Filters;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use App\LiquidEngine\Models\Theme;

abstract class AbstractProductsListing extends AbstractEngineController
{

    protected $_filters = [];

    /**
     * @param array|null $filters
     * @param string|Product|BundleProduct $model
     * @return LengthAwarePaginator
     */
    protected function getProducts(array $filters = null, $model = Product::class)
    {
        $model = $model::listing();

        //if($model instanceof BundleProductModel) {
            $model->with(['bundle_products' => function($q): void {
                /** @var Product|BelongsToMany $q */
                $q->listing(true, true)->addSelect('products.id AS id');
            }]);
        //}

        if(is_array($filters)) {
            $this->_filters = $filters;
            foreach($filters AS $column => $value) {
                if($filters = Product::makeWhereFilters([$column => $value])) {
                    array_map([$model, 'where'], $filters);
                }
            }
        }

        if($filters && array_key_exists('search', $filters)) {
            $model->orderByMatchByWords('desc');
        }

        //@todo clone model for filters
        $modelFilter = clone $model;

        $makeFilters = null;
        if($makeFilters = Product::makeWhereFilters($this->getQueryFilters())) {
            foreach($makeFilters AS $filter) {
                $model->where($filter);
            }
        }

        $this->templateShare([
            'filters' => new Filters(
                $this->_filters,
                $modelFilter,
                $makeFilters
            ),
        ]);

        $model = $this->_getSorting($model);

        /** @var LengthAwarePaginator $products */
        $products = $model->freeShipping()->paginate($this->getPerPage());

        return $products;
    }

    /**
     * @return int
     */
    protected function getPerPage()
    {
        $settings = Theme::getThemeSystemConfigs()->get('@system.products.per_page');
        if(
            !($settings['enabled']??false) ||
            empty($settings['_per_page_data'])) {
            return 9;
        }

        if(is_numeric($per_page = request()->query('per_page')) && $per_page && in_array($per_page, $settings['_per_page_data'])) {
            return $per_page;
        }

        $default = ($settings['_per_page_default'] ?? 9);
        if(!in_array($default, $settings['_per_page_data'])) {
            $default = 9;
        }

        return $default;
    }

    /**
     * @return null|Filters
     */
    protected function getFilters()
    {
        return $this->templateShare('filters')->toArray();
    }

    /**
     * @param Product|Builder $model
     * @return Product|Builder
     */
    protected function _getSorting($model)
    {
        $settings = Theme::getThemeSystemConfigs()->get('@system.products.order_by');
        if(empty($settings) || !($settings['enabled'] ?? false)) {
            return $model->orderBy('products.id', 'desc');
        }

        if(empty($configAllowed = $settings['_allowed_order_by'] ?? [])) {
            return $model->orderBy('products.id', 'desc');
        }

        $allowed = [];
        $types = OrderBySelect::getAllowed();
        foreach ($types AS $type => $directions) {
            if($configAllowed[$type] ?? false) {
                foreach($directions AS $direction) {
                    $allowed[] = sprintf('%s-%s', $type, $direction);
                }
            }
        }

        if($allowed) {
            if(is_scalar($sorting = request()->query('sort')) && $sorting && in_array($sorting, $allowed)) {
                list($sort, $direction) = Sorting::parse($sorting);
                return Sorting::sort($sort, $direction, $model);
            } elseif(($default = ($settings['_default_order_by'] ?? null)) && in_array($default, $allowed)) {
                list($sort, $direction) = Sorting::parse($default);
                return Sorting::sort($sort, $direction, $model);
            }
        }

        return $model->orderBy('products.id', 'desc');
    }

    /**
     * @param $sorting
     * @return bool
     */
    protected function setSorting($sorting)
    {
        $settings = Theme::getThemeSystemConfigs()->get('@system.products.order_by');
        if(empty($settings) || !($settings['enabled'] ?? false)) {
            return false;
        }

        if(empty($configAllowed = $settings['_allowed_order_by'] ?? [])) {
            return false;
        }

        $allowed = [];
        $types = OrderBySelect::getAllowed();
        foreach ($types AS $type => $directions) {
            if($configAllowed[$type] ?? false) {
                foreach($directions AS $direction) {
                    $allowed[] = sprintf('%s-%s', $type, $direction);
                }
            }
        }

        if($allowed) {
            if($sorting && in_array($sorting, $allowed)) {
                $settings['_default_order_by'] = $sorting;
                return !!Theme::getThemeSystemConfigs()->put('@system.products.order_by', $settings);
            }
        }

        return false;
    }

    /**
     * @return ActiveFilters
     */
    private function getActiveFilters(): \App\LiquidEngine\LiquidHelpers\Drop\Filters\ActiveFilters
    {
        $active = [];
        if($filters = $this->getQueryFilters()) {
            foreach($filters AS $group => $data) {
                if(in_array($group, ['sale', 'new', 'featured'])) {
                    $active[$group][] = [
                        'group' => $group,
                        'name' => $group,
                        'key' => request()->input($group)
                    ];
                } elseif($group == 'vendor' && $data) {
                    $vendors = Vendor::urlHandle($data)->get()->map(function(Vendor $vendor) use($group): array {
                        return [
                            'group' => $group,
                            'name' => $vendor->name,
                            'key' => $vendor->url_handle
                        ];
                    });
                    if($vendors->isNotEmpty()) {
                        $active[$group] = $vendors->all();
                    }
                } elseif($group == 'variants' && $data) {
                    $properties = Parameter::with(['options' => function($query) use($data): void {
                        /** @var ParameterOption $query */
                        $query->whereIn('id', collect($data)->collapse());
                    }])->whereHas('options', function($query) use($data): void {
                        /** @var ParameterOption $query */
                        $query->whereIn('id', collect($data)->collapse());
                    })->whereIn('id', array_keys($data))->get()->map(function(Parameter $parameter) {
                        return $parameter->options->map(function(ParameterOption $option) use($parameter): array {
                            return [
                                'group' => sprintf('variant[%d]', $parameter->id),
                                'name' => sprintf('%s: %s', $parameter->name, $option->name),
                                'key' => $option->id
                            ];
                        });
                    })->collapse();
                    if($properties->isNotEmpty()) {
                        $active[$group] = $properties->all();
                    }
                } elseif($group == 'category_properties' && !empty($data->property_active)) {

                    foreach($data->property_active AS $properties) {
                        foreach($properties AS $property) {
                            $active['property'][] = [
                                'group' => sprintf('property[%s]', $property['property_url']),
                                'name' => sprintf('%s: %s', $property['property'], $property['value']),
                                'key' => $property['value_url']
                            ];
                        }
                    }

                } elseif($group == 'price_from' && $data > 0) {
                    $active[$group][] = [
                        'group' => $group,
                        'name' => __('item.price.price_from', ['price'=> Format::money($data)]),
                        'key' => $data,
                        'parts' => PriceParts::format(Format::moneyInput($data))
                    ];
                } elseif($group == 'price_to' && $data > 0) {
                    $active[$group][] = [
                        'group' => $group,
                        'name' => __('item.price.price_to', ['price'=> Format::money($data)]),
                        'key' => $data,
                        'parts' => PriceParts::format(Format::moneyInput($data))
                    ];
                } elseif($group == 'brand') {
                    $active[$group][] = [
                        'group' => $group,
                        'name' => __('brand_model.text.brand_active', ['brand' => $data->title]),
                        'key' => $data->url_handle
                    ];
                } elseif($group == 'model') {
                    $active[$group][] = [
                        'group' => $group,
                        'name' => __('brand_model.text.model_active', ['model' => $data->title]),
                        'key' => $data->url_handle
                    ];
                }
            }
        }

        return new ActiveFilters(array_values($active));
    }

    /**
     * @return ActiveFilters
     */
    protected function getAllowedActiveFilters()
    {
        if($this->allowActiveFilters()) {
            return $this->getActiveFilters();
        }

        return new ActiveFilters([]);
    }

    /**
     * @return bool
     */
    protected function allowActiveFilters()
    {
        return (bool)(Theme::getThemeSystemConfigs()->get('@system.products.filters')['active_filters'] ?? false);
    }

    /**
     * @return array
     */
    protected function getQueryFilters()
    {
        $settings = Theme::getThemeSystemConfigs()->get('@system.products.filters');

        $filters = [];
        if(($settings['filters']['vendors'] ?? false) && ($vendor = trim(request()->input('vendor')))) {
            $filters['vendor'] = explode(',', $vendor);
        }

        foreach(['sale', 'new', 'featured'] AS $fKey) {
            if(($settings['filters']['other'][$fKey] ?? false) && request()->query($fKey)) {
                $filters[$fKey] = 1;
            }
        }

        if(($settings['filters']['variants'] ?? false) && is_array($variants = request()->input('variant')) && count($variants)) {
            $filters['variants'] = array_filter(array_map(function($variant): array {
                return array_filter(explode(',', $variant));
            }, $variants));
        }

        if(($settings['filters']['category_properties'] ?? false) && is_array($properties = request()->input('property')) && count($properties)) {
            $filters['category_properties'] = Property::getMatchingFiltersForQuery($this->_filters['category_id'] ?? null, $properties);
        }

        if(($settings['filters']['brand_model'] ?? false) && Apps::installed('brand_model')) {
            if(($model = request()->query('model')) && ($dbModel = Model::urlHandle($model)->first())) {
                if(($brand = request()->query('brand')) && ($dbBrand = Brand::urlHandle($brand)->first())) {
                    $filters['brand'] = $dbBrand;
                }

                $filters['model'] = $dbModel;
            } elseif(($brand = request()->query('brand')) && ($dbBrand = Brand::urlHandle($brand)->first())) {
                $filters['brand'] = $dbBrand;
            }
        }

        if(($settings['filters']['price_ranges'] ?? false) && ($price_ranges = PriceRanges::parseQueryPriceRange())) {
            $filters = array_merge($filters, array_map(\App\Helper\Format::class . '::toIntegerPrice', $price_ranges));
        }

        return $filters;
    }

}
