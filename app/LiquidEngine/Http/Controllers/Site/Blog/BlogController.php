<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\Site\Blog;

use App\Models\Blog\Article;
use App\Models\Blog\Blog;
use App\Models\Blog\Tag;
use App\LiquidEngine\Assetics\Subscribers\ListArticles;
use App\LiquidEngine\Http\Controllers\Site\AbstractEngineController;
use Closure;
use Illuminate\Contracts\View\Factory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\View\View;
use App\LiquidEngine\LiquidHelpers\Drop\Paginate;
use App\LiquidEngine\LiquidHelpers\Drop\Blog\Article as LiquidArticle;
use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;
use App\LiquidEngine\Models\Theme;

class BlogController extends AbstractEngineController
{

    /**
     * @return Factory|\Illuminate\Contracts\View\View|View
     */
    public function index()
    {
        return $this->_getPosts();
    }

    /**
     * @param $filter
     * @param $slug
     * @return Factory|\Illuminate\Contracts\View\View|View
     */
    public function view($filter, $slug)
    {
        if ($filter == 'category') {
            $tag = null;
            if (!$blog = Blog::urlHandle($slug)->first()) {
                app()->abort(404, __('error.no_longer_exists.blog_category'));
            }
        } else {
            $blog = null;
            if (!$tag = Tag::urlHandle($slug)->first()) {
                app()->abort(404, __('error.no_longer_exists.blog_tag'));
            }
        }

        return $this->_getPosts(function (Builder $query) use ($blog, $tag): array {
            if ($blog) {
                $query->where('blog_id', $blog->id);

                $this->breadcrumbs()->add(
                    $blog->name,
                    route('blog.view', ['filter' => 'category', 'slug' => $blog->url_handle])
                );

                return [$blog->name, $blog->seo_title, $blog->seo_description, 'blog', $blog];
            } else {
                $query->whereHas('tags', function ($query) use ($tag): void {
                    /** @var Builder $query */
                    $query->where('id', $tag->id);
                });

                $this->breadcrumbs()->add(
                    $tag->tag,
                    route('blog.view', ['filter' => 'tag', 'slug' => $tag->url_handle])
                );

                return [$tag->tag, $tag->tag, $tag->tag, 'tag', $tag];
            }
        });
    }

    /**
     * @param Closure|null $callback
     * @return Factory|View
     */
    protected function _getPosts(?Closure $callback = null)
    {
//        $template = new Template('templates.list-blog');
//        $template = new Template('list-blog'); // zora-liquid, freedom-liquid
        $template = new Template('blog');

        $model = Article::has('blog')
            ->withCount('comments as comments_count')->with(['blog', 'author', 'tags'])
            ->orderBy('id', 'desc');

        $this->breadcrumbs()->add(
            __('pages.home'),
            route('site.home')
        );

        $this->breadcrumbs()->add(
            __('pages.blog'),
            route('blog.list')
        );

        $name = __('pages.blog');
        $seo_title = __('sf.seo.blog.title');
        $seo_description = __('sf.seo.blog.description');
        $type = null;
        $record = null;
        if ($callback !== null) {
            list($name, $seo_title, $seo_description, $type, $record) = $callback($model);
        }

        /** @var LengthAwarePaginator $articles */
        $articles = $model->paginate($this->getPerPage());

        $data = [
            'pagination' => new Paginate($articles),
            'articles' => $articles->getCollection()->map(function (Article $article): \App\LiquidEngine\LiquidHelpers\Drop\Blog\Article {
                return new LiquidArticle($article);
            })->all(),
            'title' => $name,
        ];

        $subscriber = new ListArticles($articles);
        if ($type) {
            $subscriber->{$type} = $record;
        }

        event('controller:listArticles', $subscriber);

        return $this->templateJson($template, $data, [
            'page_title' => $seo_title,
            'page_description' => $seo_description,
        ]);
    }

    /**
     * @return int
     */
    protected function getPerPage()
    {
        $settings = Theme::getThemeSystemConfigs()->get('@system.articles.per_page');
        if (
            !($settings['enabled'] ?? false) ||
            empty($settings['_per_page_data'])) {
            return 9;
        }

        if (is_numeric($per_page = request()->query('per_page')) && $per_page && in_array($per_page, $settings['_per_page_data'])) {
            return $per_page;
        }

        $default = ($settings['_per_page_default'] ?? 9);
        if (!in_array($default, $settings['_per_page_data'])) {
            $default = 9;
        }

        return $default;
    }

}
