<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers;

use App\LiquidEngine\Services\TemplateContextProvider;
use App\LiquidEngine\Services\TemplateLoader;
use App\Models\Page\Page;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

/**
 * Controller for handling policy pages
 */
class PoliciesController extends LiquidController
{
    /**
     * Standard policy types
     */
    protected const POLICY_TYPES = [
        'refund-policy',
        'privacy-policy',
        'terms-of-service',
        'shipping-policy',
        'legal-notice',
        'contact-information',
    ];
    
    /**
     * Show all policies
     *
     * @param Request $request
     * @return Response|JsonResponse
     */
    public function index(Request $request): Response|JsonResponse
    {
        // Get all policies
        $policies = Page::whereIn('system_page', self::POLICY_TYPES)
            ->orWhere('page_type', 'policy')
            ->active()
            ->get();
        
        // Group policies by type
        $groupedPolicies = [
            'legal' => $policies->filter(function ($policy) {
                return in_array($policy->system_page, ['terms-of-service', 'legal-notice']);
            }),
            'privacy' => $policies->filter(function ($policy) {
                return $policy->system_page === 'privacy-policy';
            }),
            'shopping' => $policies->filter(function ($policy) {
                return in_array($policy->system_page, ['refund-policy', 'shipping-policy']);
            }),
            'other' => $policies->filter(function ($policy) {
                return !in_array($policy->system_page, ['terms-of-service', 'legal-notice', 'privacy-policy', 'refund-policy', 'shipping-policy']);
            }),
        ];
        
        // Prepare data for the template
        $data = [
            'title' => 'Policies',
            'policies' => $policies,
            'grouped_policies' => $groupedPolicies,
        ];
        
        // Check for custom template
        $template = $this->getTemplateFromRequest($request, 'templates/policies');
        
        // Render the template
        return $this->renderTemplate($template, $data);
    }
    
    /**
     * Show a specific policy
     *
     * @param Request $request
     * @param string $handle
     * @return Response|JsonResponse
     */
    public function show(Request $request, string $handle): Response|JsonResponse
    {
        // Check if this is a standard policy type
        $isStandardPolicy = in_array($handle, self::POLICY_TYPES);
        
        // Find the policy page by handle or system page type
        $policy = Page::where(function ($query) use ($handle) {
                $query->where('url_handle', $handle)
                    ->orWhere('system_page', $handle);
            })
            ->active()
            ->first();
        
        if (!$policy) {
            // If no specific policy found but it's a standard type, create a placeholder
            if ($isStandardPolicy) {
                $policy = new Page();
                $policy->name = Str::title(str_replace('-', ' ', $handle));
                $policy->url_handle = $handle;
                $policy->system_page = $handle;
                $policy->content = $this->getDefaultPolicyContent($handle);
            } else {
                return $this->notFound('Policy not found');
            }
        }
        
        // Get other policies for related links
        $otherPolicies = Page::whereIn('system_page', self::POLICY_TYPES)
            ->where(function ($query) use ($policy) {
                if ($policy->id) {
                    $query->where('id', '!=', $policy->id);
                }
            })
            ->active()
            ->get();
        
        // Prepare data for the template
        $data = [
            'title' => $policy->name,
            'policy' => $policy,
            'policy_type' => $handle,
            'other_policies' => $otherPolicies,
            'page_description' => $policy->seo_description,
            'page' => $policy, // For compatibility with Shopify templates
        ];
        
        // Check if JSON response is requested
        if ($request->wantsJson() || Str::endsWith($request->path(), '.json')) {
            return response()->json([
                'policy' => [
                    'id' => $policy->id ?? null,
                    'title' => $policy->name,
                    'handle' => $policy->url_handle,
                    'body' => $policy->content,
                    'url' => url("/policies/{$handle}"),
                    'type' => $handle,
                ]
            ]);
        }
        
        // Check for custom template
        $template = $this->getTemplateFromRequest($request, 'templates/policy');
        
        // Render the template
        return $this->renderTemplate($template, $data, [
            "templates/policy.{$handle}",
            "templates/policy"
        ]);
    }
    
    /**
     * Get default content for standard policy types
     *
     * @param string $policyType
     * @return string
     */
    protected function getDefaultPolicyContent(string $policyType): string
    {
        switch ($policyType) {
            case 'privacy-policy':
                return '<h2>Privacy Policy</h2><p>This is a placeholder for your store\'s privacy policy. You should replace this with your actual privacy policy content.</p><p>Your privacy policy should include information about:</p><ul><li>What personal information you collect</li><li>How you use that information</li><li>How you protect customer data</li><li>Whether you share data with third parties</li><li>How customers can access or delete their data</li></ul>';
                
            case 'terms-of-service':
                return '<h2>Terms of Service</h2><p>This is a placeholder for your store\'s terms of service. You should replace this with your actual terms of service content.</p><p>Your terms of service should include information about:</p><ul><li>User rights and responsibilities</li><li>Intellectual property rights</li><li>Prohibited activities</li><li>Limitation of liability</li><li>Governing law and dispute resolution</li></ul>';
                
            case 'refund-policy':
                return '<h2>Refund Policy</h2><p>This is a placeholder for your store\'s refund policy. You should replace this with your actual refund policy content.</p><p>Your refund policy should include information about:</p><ul><li>Return eligibility and timeframes</li><li>How to initiate a return</li><li>Refund processing times</li><li>Exchanges vs. refunds</li><li>Exceptions or special cases</li></ul>';
                
            case 'shipping-policy':
                return '<h2>Shipping Policy</h2><p>This is a placeholder for your store\'s shipping policy. You should replace this with your actual shipping policy content.</p><p>Your shipping policy should include information about:</p><ul><li>Shipping methods and carriers</li><li>Processing and delivery times</li><li>Shipping rates and free shipping thresholds</li><li>International shipping details</li><li>Tracking information</li></ul>';
                
            case 'legal-notice':
                return '<h2>Legal Notice</h2><p>This is a placeholder for your store\'s legal notice. You should replace this with your actual legal notice content.</p><p>Your legal notice should include information about:</p><ul><li>Company registration details</li><li>VAT or tax identification numbers</li><li>Authorized representatives</li><li>Regulatory compliance information</li><li>Contact information for legal matters</li></ul>';
                
            case 'contact-information':
                return '<h2>Contact Information</h2><p>This is a placeholder for your store\'s contact information. You should replace this with your actual contact details.</p><p>Your contact information should include:</p><ul><li>Physical address</li><li>Phone number</li><li>Email address</li><li>Business hours</li><li>Social media profiles</li></ul>';
                
            default:
                return '<h2>Policy Information</h2><p>This is a placeholder for your store\'s policy content. Please add your specific policy details here.</p>';
        }
    }
} 