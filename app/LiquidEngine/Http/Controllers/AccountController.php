<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers;

use App\LiquidEngine\Services\TemplateContextProvider;
use App\LiquidEngine\Services\TemplateLoader;
use App\Models\Customer\Customer;
use App\Models\Customer\CustomerBillingAddress;
use App\Models\Customer\CustomerShippingAddress;
use App\Models\Order\Order;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Throwable;

/**
 * Controller for handling customer account functionality
 */
class AccountController extends LiquidController
{
    /**
     * Show the account dashboard
     *
     * @param Request $request
     * @return Response|JsonResponse|RedirectResponse
     */
    public function index(Request $request): Response|JsonResponse|RedirectResponse
    {
        // Check if user is logged in
        if (!Auth::guard('customer')->check()) {
            return redirect()->route('liquid.account.login');
        }
        
        $customer = Auth::guard('customer')->user();
        
        // Get recent orders
        $recentOrders = Order::where('user_id', $customer->id)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();
        
        // Prepare data for the template
        $data = [
            'title' => 'My Account',
            'customer' => $customer,
            'recent_orders' => $recentOrders,
        ];
        
        // Render the template
        return $this->renderTemplate('templates/account', $data);
    }
    
    /**
     * Show the login page
     *
     * @param Request $request
     * @return Response|JsonResponse|RedirectResponse
     */
    public function login(Request $request): Response|JsonResponse|RedirectResponse
    {
        // Check if user is already logged in
        if (Auth::guard('customer')->check()) {
            return redirect()->route('liquid.account');
        }
        
        // Prepare data for the template
        $data = [
            'title' => 'Login',
        ];
        
        // Render the template
        return $this->renderTemplate('templates/login', $data);
    }
    
    /**
     * Show the registration page
     *
     * @param Request $request
     * @return Response|JsonResponse|RedirectResponse
     */
    public function register(Request $request): Response|JsonResponse|RedirectResponse
    {
        // Check if user is already logged in
        if (Auth::guard('customer')->check()) {
            return redirect()->route('liquid.account');
        }
        
        // Prepare data for the template
        $data = [
            'title' => 'Register',
        ];
        
        // Render the template
        return $this->renderTemplate('templates/register', $data);
    }
    
    /**
     * Show the orders page
     *
     * @param Request $request
     * @return Response|JsonResponse|RedirectResponse
     */
    public function orders(Request $request): Response|JsonResponse|RedirectResponse
    {
        // Check if user is logged in
        if (!Auth::guard('customer')->check()) {
            return redirect()->route('liquid.account.login');
        }
        
        $customer = Auth::guard('customer')->user();
        
        // Get pagination parameters
        $page = max(1, (int) $request->query('page', 1));
        $perPage = (int) $request->query('per_page', 10);
        
        // Get orders
        $orders = Order::where('user_id', $customer->id)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);
        
        // Prepare data for the template
        $data = [
            'title' => 'Order History',
            'customer' => $customer,
            'orders' => $orders,
            'current_page' => $page,
        ];
        
        // Render the template
        return $this->renderTemplate('templates/orders', $data);
    }
    
    /**
     * Show the addresses page
     *
     * @param Request $request
     * @return Response|JsonResponse|RedirectResponse
     */
    public function addresses(Request $request): Response|JsonResponse|RedirectResponse
    {
        // Check if user is logged in
        if (!Auth::guard('customer')->check()) {
            return redirect()->route('liquid.account.login');
        }
        
        $customer = Auth::guard('customer')->user();
        
        // Get billing addresses
        $billingAddresses = CustomerBillingAddress::where('customer_id', $customer->id)
            ->orderBy('is_default', 'desc')
            ->get();
            
        // Get shipping addresses
        $shippingAddresses = CustomerShippingAddress::where('customer_id', $customer->id)
            ->orderBy('is_default', 'desc')
            ->get();
        
        // Prepare data for the template
        $data = [
            'title' => 'Addresses',
            'customer' => $customer,
            'billing_addresses' => $billingAddresses,
            'shipping_addresses' => $shippingAddresses,
        ];
        
        // Render the template
        return $this->renderTemplate('templates/addresses', $data);
    }
} 