<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers;

use App\LiquidEngine\Services\TemplateContextProvider;
use App\LiquidEngine\Services\TemplateLoader;
use App\Models\Product\Product;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

/**
 * Controller for handling product pages
 */
class ProductsController extends LiquidController
{
    /**
     * Show a specific product
     *
     * @param Request $request
     * @param string $handle
     * @return Response|JsonResponse
     */
    public function show(Request $request, string $handle): Response|JsonResponse
    {
        // Find the product by handle
        $product = Product::where('url_handle', $handle)->active()->first();
        
        if (!$product) {
            return $this->notFound('Product not found');
        }
        
        // Get variant if specified
        $variantId = $request->query('variant');
        $variant = null;
        
        if ($variantId) {
            $variant = $product->variants()->where('id', $variantId)->first();
        }
        
        if (!$variant) {
            $variant = $product->defaultVariant;
        }
        
        // Get related products
        $relatedProducts = $product->related()->active()->take(4)->get();
        
        // Prepare data for the template
        $data = [
            'title' => $product->title,
            'product' => $product,
            'variant' => $variant,
            'related_products' => $relatedProducts,
            'page_description' => $product->description,
        ];
        
        // Check for custom template
        $template = $this->getTemplateFromRequest($request, 'templates/product');
        
        // Render the template
        return $this->renderTemplate($template, $data, [
            "templates/product.{$handle}",
            "templates/product"
        ]);
    }
} 