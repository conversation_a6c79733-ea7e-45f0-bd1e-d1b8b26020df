<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 4.9.2017 г.
 * Time: 17:55 ч.
 */
namespace App\LiquidEngine\Http\Requests\Site\GeoZones;

use Illuminate\Foundation\Http\FormRequest;

class GeoZoneFormatRequest extends FormRequest
{
    /**
     * @return array
     */
    public function rules(): array
    {
        if (!is_null($send_from = $this->sendFrom())) {
            return [
                $send_from => 'required',
            ];
        }

        return [];
    }

    /**
     * @return array
     */
    public function messages()
    {
        return [
            'locality.required' => __('sf.global.ph.enter_your_address'),
            'country.required' => __('sf.global.ph.enter_your_country'),
            'administrative_area_level_1.required' => __('sf.global.ph.enter_your_state'),
            'political.required' => __('sf.global.ph.enter_your_city'),
        ];
    }

    /**
     * @return string|null
     */
    protected function sendFrom()
    {
        if (in_array($send_from = $this->input('send_from'), ['locality', 'country', 'administrative_area_level_1', 'political'])) {
            return $send_from;
        }

        return null;
    }
}
