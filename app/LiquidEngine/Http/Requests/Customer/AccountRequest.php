<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Requests\Customer;

use App\LiquidEngine\Traits\Request\CustomFields;
use Auth;
use Illuminate\Foundation\Http\FormRequest;

class AccountRequest extends FormRequest
{

    use CustomFields;

    public function rules(): array
    {
        return array_merge([
            'email' => 'required|email|max:191|unique_customer_email:' . Auth::customerId(),
            'first_name' => 'required|max:191',
            'last_name' => 'required|max:191',
        ], $this->getCustomRules());
    }

    public function messages()
    {
        return [
            'email.required' => __('sf.widget.global.err.email_required'),
            'email.email' => __('sf.widget.global.err.email_invalid'),
            'email.max' => sprintf(__('sf.widget.global.err.email_max_chars_%1$s'), 191),
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return $this->getCustomAttributes();
    }

}
