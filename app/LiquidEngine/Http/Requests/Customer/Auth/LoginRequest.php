<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Requests\Customer\Auth;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{

    public function rules(): array
    {
        return [
            'email' => 'required|email|max:191',
            'password' => 'required|min:3|max:20',
        ];
    }

    public function messages()
    {
        return [
            'email.required' => __('validation.email.required'),
            'email.email' => __('validation.email.invalid'),
            'email.max' => __('validation.email.max'),
            'password.required' => __('validation.password.required'),
            'password.min' => __('validation.password.min'),
            'password.max' => __('validation.password.max'),
        ];
    }

}
