# CloudCart Liquid Template System

This document provides an overview of CloudCart's Liquid template system, which has been enhanced to better align with Shopify's Liquid implementation while maintaining backward compatibility.

## Overview

The CloudCart Liquid template system provides a flexible way to render templates with data. It supports both direct template loading and JSON-based templates, and provides a standardized context for all templates.

## Key Components

### 1. Template Resolver

The `TemplateResolver` service is responsible for resolving template paths based on various inputs:

- Template objects
- Controller and action names
- Template names

It also provides fallback mechanisms to ensure that templates can be found even if the primary template is missing.

### 2. Template Context Provider

The `TemplateContextProvider` service creates a standardized context for all templates, including:

- Global objects (shop, cart, customer)
- Request-specific data
- SEO data

### 3. Template Loader

The `TemplateLoader` service loads templates with the given data, supporting both direct templates and JSON-based templates.

### 4. Drop Classes

Drop classes provide a Shopify-compatible object model for various entities:

- `BaseDrop`: Base class for all Drop classes, providing common functionality
- `PageDrop`: Shopify-compatible page object
- `ProductDrop`: Shopify-compatible product object

## Usage

### Basic Usage

The simplest way to render a template is to use the `renderTemplate` method in your controller:

```php
public function show($slug)
{
    $page = Page::urlHandle($slug)->firstOrFail();
    $pageDrop = new PageDrop($page);
    
    $data = [
        'title' => $page->name,
        'page' => $pageDrop,
        'template' => 'page',
    ];
    
    return $this->renderTemplate($data);
}
```

This will automatically resolve the template based on the controller and action, and render it with the given data.

### Explicit Template Specification

If you need more control over the template resolution, you can use the `template` method with a `Template` object:

```php
public function show($slug)
{
    $page = Page::urlHandle($slug)->firstOrFail();
    $pageDrop = new PageDrop($page);
    
    $template = new Template('templates.page');
    
    $data = [
        'title' => $page->name,
        'page' => $pageDrop,
    ];
    
    return $this->template($template, $data);
}
```

### Helper Functions

For backward compatibility, you can also use the helper functions:

```php
public function show($slug)
{
    $page = Page::urlHandle($slug)->firstOrFail();
    $pageDrop = new PageDrop($page);
    
    $data = [
        'title' => $page->name,
        'page' => $pageDrop,
    ];
    
    return response(liquid('templates/page', $data));
}
```

## Drop Classes

### BaseDrop

The `BaseDrop` class provides common functionality for all Drop classes, including:

- Dynamic property access with snake_case to camelCase conversion
- Method call handling with snake_case to camelCase conversion
- Conversion to array

### PageDrop

The `PageDrop` class provides a Shopify-compatible page object with properties like:

- id
- title
- handle
- content
- url
- author
- published_at
- modified_at
- template_suffix

### ProductDrop

The `ProductDrop` class provides a Shopify-compatible product object with properties like:

- id
- title
- handle
- content
- url
- vendor
- product_type
- published_at
- price
- compare_at_price
- variants
- options
- featured_image
- images

## Global Objects

The following objects are available in all templates:

- `shop`: Information about the store
- `settings`: Store settings
- `request`: Information about the current request
- `current_url`: The current URL
- `canonical_url`: The canonical URL for SEO

## Template Resolution

Templates are resolved in the following order:

1. Explicit template specification (if provided)
2. Controller and action-based resolution
3. Template name-based resolution
4. Fallback templates

## JSON-Based Templates

JSON-based templates are supported through the `TemplateLoader` service. These templates are defined in JSON files and can be used to create more dynamic templates.

## Backward Compatibility

The new Liquid template system is backward compatible with existing code. You can continue to use the `template` method with a `Template` object, or you can use the new `renderTemplate` method for automatic template resolution.

## Examples

### Page Controller

```php
public function show($slug)
{
    $page = Page::urlHandle($slug)->firstOrFail();
    $pageDrop = new PageDrop($page);
    
    $data = [
        'title' => $page->name,
        'page' => $pageDrop,
        'template' => 'page',
    ];
    
    return $this->renderTemplate($data);
}
```

### Product Controller

```php
public function show($slug)
{
    $product = Product::urlHandle($slug)->firstOrFail();
    $productDrop = new ProductDrop($product);
    
    $data = [
        'title' => $product->name,
        'product' => $productDrop,
        'template' => 'product',
    ];
    
    return $this->renderTemplate($data);
}
```

## Best Practices

1. **Use Drop Classes**: Always use the appropriate Drop class for your data to ensure compatibility with Shopify Liquid templates.

2. **Provide Template Hints**: Always include a `template` key in your data to help with template resolution.

3. **Use Automatic Resolution**: Use the `renderTemplate` method for automatic template resolution when possible.

4. **Test with Different Templates**: Test your controllers with different templates to ensure that they work correctly.

5. **Use Global Objects**: Make use of global objects like `shop` and `settings` instead of passing them explicitly.

## Conclusion

The CloudCart Liquid template system provides a flexible and powerful way to render templates with data. By following the guidelines in this document, you can create controllers that work seamlessly with Shopify-compatible Liquid templates. 
