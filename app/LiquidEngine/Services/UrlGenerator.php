<?php

declare(strict_types=1);

namespace App\LiquidEngine\Services;

use App\Models\Blog\Article;
use App\Models\Blog\Blog;
use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Models\Page\Page;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

/**
 * URL Generator Service
 *
 * Provides methods for generating URLs for products, collections, blogs, and pages.
 */
class UrlGenerator
{
    /**
     * Cache lifetime in minutes
     *
     * @var int
     */
    protected int $cacheLifetime = 60;

    /**
     * @var ThemeAssetsMapper
     */
    protected ThemeAssetsMapper $assetsMapper;

    /**
     * Constructor
     *
     * @param ThemeAssetsMapper|null $assetsMapper
     */
    public function __construct(?ThemeAssetsMapper $assetsMapper = null)
    {
        $this->assetsMapper = $assetsMapper ?? app()->make(ThemeAssetsMapper::class);
    }

    /**
     * Generate a URL for a product
     *
     * @param Product|int|string $product Product model, ID, or handle
     * @param array $options Optional parameters (variant_id, etc.)
     * @return string
     */
    public function productUrl($product, array $options = []): string
    {
        if (is_numeric($product)) {
            $product = Product::find($product);
        } elseif (is_string($product) && !($product instanceof Product)) {
            $product = Product::where('url_handle', $product)->first();
        }

        if (!$product) {
            return '#invalid-product';
        }

        $url = '/products/' . $product->url_handle;

        if (!empty($options)) {
            $url .= '?' . http_build_query($options);
        }

        return $url;
    }

    /**
     * Generate a URL for a collection
     *
     * @param Category|int|string $collection Category model, ID, or handle
     * @param array $options Optional parameters (sort_by, filter, etc.)
     * @return string
     */
    public function collectionUrl($collection, array $options = []): string
    {
        if (is_numeric($collection)) {
            $collection = Category::find($collection);
        } elseif (is_string($collection) && !($collection instanceof Category)) {
            $collection = Category::where('url_handle', $collection)->first();
        }

        if (!$collection) {
            return '#invalid-collection';
        }

        $url = '/collections/' . $collection->url_handle;

        if (!empty($options)) {
            $url .= '?' . http_build_query($options);
        }

        return $url;
    }

    /**
     * Generate a URL for a blog
     *
     * @param Blog|int|string $blog Blog model, ID, or handle
     * @param array $options Optional parameters (page, tag, etc.)
     * @return string
     */
    public function blogUrl($blog, array $options = []): string
    {
        if (is_numeric($blog)) {
            $blog = Blog::find($blog);
        } elseif (is_string($blog) && !($blog instanceof Blog)) {
            $blog = Blog::where('url_handle', $blog)->first();
        }

        if (!$blog) {
            return '#invalid-blog';
        }

        $url = '/blogs/' . $blog->url_handle;

        if (!empty($options)) {
            $url .= '?' . http_build_query($options);
        }

        return $url;
    }

    /**
     * Generate a URL for an article
     *
     * @param Article|int|string $article Article model, ID, or handle
     * @param Blog|int|string|null $blog Blog model, ID, or handle (optional)
     * @return string
     */
    public function articleUrl($article, $blog = null): string
    {
        if (is_numeric($article)) {
            $article = Article::find($article);
        } elseif (is_string($article) && !($article instanceof Article)) {
            $article = Article::where('url_handle', $article)->first();
        }

        if (!$article) {
            return '#invalid-article';
        }

        if (!$blog) {
            $blog = $article->blog;
        } elseif (is_numeric($blog)) {
            $blog = Blog::find($blog);
        } elseif (is_string($blog) && !($blog instanceof Blog)) {
            $blog = Blog::where('url_handle', $blog)->first();
        }

        if (!$blog) {
            return '#invalid-blog';
        }

        return '/blogs/' . $blog->url_handle . '/' . $article->url_handle;
    }

    /**
     * Generate a URL for a page
     *
     * @param Page|int|string $page Page model, ID, or handle
     * @return string
     */
    public function pageUrl($page): string
    {
        if (is_numeric($page)) {
            $page = Page::find($page);
        } elseif (is_string($page) && !($page instanceof Page)) {
            $page = Page::where('url_handle', $page)->first();
        }

        if (!$page) {
            return '#invalid-page';
        }

        return '/pages/' . $page->url_handle;
    }

    /**
     * Generate a URL for a policy
     *
     * @param string $handle Policy handle
     * @return string
     */
    public function policyUrl(string $handle): string
    {
        return '/policies/' . $handle;
    }

    /**
     * Generate an asset URL
     *
     * The canonical implementation for generating asset URLs throughout the application.
     * This method handles asset paths consistently, ensuring they're properly formatted
     * and routed to the correct endpoint.
     *
     * @param string|null $path Asset path
     * @param array $options Optional query parameters
     * @return string
     */
    public function assetUrl(?string $path = null, array $options = []): string
    {
        // Handle null or empty paths
        if ($path === null || $path === '') {
            return '';
        }

        // Generate a cache key for this asset URL
        $cacheKey = 'asset_url:' . md5($path . json_encode($options) . '_site_id:' . site()->site_id);

        // Try to retrieve from cache first
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // If the path is already a full URL, return it as is
        if (Str::startsWith($path, ['http://', 'https://'])) {
            return $path;
        }

        // Remove leading slash if present
        $path = ltrim($path, '/');

        // Use the asset mapper to get the correct theme-specific path
        $mappedPath = $this->assetsMapper->getMappedAssetPath($path);

        // Use the correct liquid.assets route
        $url = route('liquid.assets', ['path' => $mappedPath]);

        // Remove /admin/ from the URL if present (this is a workaround for a routing issue)
        $url = str_replace('/admin/assets/', '/assets/', $url);

        // Add any additional query parameters
        if (!empty($options)) {
            $url .= (Str::contains($url, '?') ? '&' : '?') . http_build_query($options);
        }

        // Cache the result
        Cache::put($cacheKey, $url, now()->addMinutes($this->cacheLifetime));

        return $url;
    }

    /**
     * Generate an image URL with transformations
     *
     * @param string $src Image source URL
     * @param string $size Size transformation (e.g., '100x100', '300x', 'x400')
     * @param array $options Additional options (crop, scale, format, etc.)
     * @return string
     */
    public function imageUrl(string $src, string $size = '', array $options = []): string
    {
        // If the source is already a full URL, return it as is
        if (Str::startsWith($src, ['http://', 'https://'])) {
            return $src;
        }

        // Remove leading slash if present
        $src = ltrim($src, '/');

        // Generate a cache key for this image URL
        $cacheKey = 'image_url:' . md5($src . $size . json_encode($options)). '_site_id:' . site()->site_id;

        // Try to retrieve from cache first
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Parse size into width and height
        $dimensions = [];
        if (!empty($size)) {
            if (Str::contains($size, 'x')) {
                [$width, $height] = explode('x', $size);
                if (!empty($width)) {
                    $dimensions['width'] = (int) $width;
                }
                if (!empty($height)) {
                    $dimensions['height'] = (int) $height;
                }
            } else {
                $dimensions['width'] = (int) $size;
            }
        }

        // Merge dimensions with other options
        $transformations = array_merge($dimensions, $options);

        // Use the assets.images route if transformations are present
        if (!empty($transformations)) {
            $url = route('liquid.assets.images', ['path' => $src]);
            $url .= '?' . http_build_query($transformations);
        } else {
            // Otherwise use the standard assets route
            $url = route('liquid.assets', ['path' => $src]);
        }

        // Cache the result
        Cache::put($cacheKey, $url, now()->addMinutes($this->cacheLifetime));

        return $url;
    }

    public function fontUrl(?string $path = null, array $options = []): string
    {
        // Handle null or empty paths
        if ($path === null || $path === '') {
            return '';
        }

        if ($path === 'assistant_n4') {
            // $path = 'fa-regular-400'; // This one exists in node_modules for default replacement of shopify assistant_n4
        }

        // Generate a cache key for this asset URL
        $cacheKey = 'font_url:' . md5($path . json_encode($options) . '_site_id:' . site()->site_id);

        // Try to retrieve from cache first
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // If the path is already a full URL, return it as is
        if (Str::startsWith($path, ['http://', 'https://'])) {
            return $path;
        }

        // Remove leading slash if present
        $path = ltrim($path, '/');

        // Use the asset mapper to get the correct theme-specific path
        $mappedPath = $this->assetsMapper->getMappedAssetPath($path);

        // Use the correct liquid.assets route
        $url = route('webfonts.file', ['file' => $mappedPath]);

        // Remove /admin/ from the URL if present (this is a workaround for a routing issue)
        $url = str_replace('/admin/webfonts/', '/webfonts/', $url);

        // Add any additional query parameters
        if (!empty($options)) {
            $url .= (Str::contains($url, '?') ? '&' : '?') . http_build_query($options);
        }

        // Cache the result
        Cache::put($cacheKey, $url, now()->addMinutes($this->cacheLifetime));

        return $url;
    }
}
