<?php

declare(strict_types=1);

namespace App\LiquidEngine\Services;

use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;
use App\Models\Page\Page;
use App\Models\Blog\Blog;
use App\Models\Blog\Article;
use App\Models\Product\Product;
use App\Models\Product\Category;
use Illuminate\Support\Str;

/**
 * Service for resolving routes to templates and models
 */
class RouteResolver
{
    /**
     * Map of route types to template paths
     * 
     * @var array
     */
    protected $routeTemplateMap = [
        'page' => 'templates.page',
        'blog' => 'templates.blog',
        'article' => 'templates.article',
        'collection' => 'templates.collection',
        'collections' => 'templates.collections',
        'product' => 'templates.product',
        'cart' => 'templates.cart',
        'search' => 'templates.search',
        '404' => 'templates.404',
    ];

    /**
     * Resolve a path to a template and model
     * 
     * @param string $path The URL path to resolve
     * @return array|null The resolved route data or null if not found
     */
    public function resolvePath(string $path): ?array
    {
        // Normalize the path
        $path = $this->normalizePath($path);
        
        // Check for static routes first
        $staticRoute = $this->resolveStaticRoute($path);
        if ($staticRoute) {
            return $staticRoute;
        }
        
        // Check for dynamic routes
        return $this->resolveDynamicRoute($path);
    }

    /**
     * Normalize a path for consistent handling
     * 
     * @param string $path The path to normalize
     * @return string The normalized path
     */
    protected function normalizePath(string $path): string
    {
        // Remove leading and trailing slashes
        $path = trim($path, '/');
        
        // Remove query string if present
        if (($pos = strpos($path, '?')) !== false) {
            $path = substr($path, 0, $pos);
        }
        
        return $path;
    }

    /**
     * Resolve static routes like /cart, /search, etc.
     * 
     * @param string $path The normalized path
     * @return array|null The resolved route data or null if not found
     */
    protected function resolveStaticRoute(string $path): ?array
    {
        $staticRoutes = [
            'cart' => [
                'type' => 'cart',
                'template' => $this->routeTemplateMap['cart'],
                'controller' => 'cart',
            ],
            'search' => [
                'type' => 'search',
                'template' => $this->routeTemplateMap['search'],
                'controller' => 'search',
            ],
            'collections' => [
                'type' => 'collections',
                'template' => $this->routeTemplateMap['collections'],
                'controller' => 'collections',
            ],
            '404' => [
                'type' => '404',
                'template' => $this->routeTemplateMap['404'],
                'controller' => 'notFound',
            ],
        ];
        
        return $staticRoutes[$path] ?? null;
    }

    /**
     * Resolve dynamic routes like /pages/{handle}, /products/{handle}, etc.
     * 
     * @param string $path The normalized path
     * @return array|null The resolved route data or null if not found
     */
    protected function resolveDynamicRoute(string $path): ?array
    {
        // Split path into segments
        $segments = explode('/', $path);
        
        // Handle /pages/{handle}
        if (count($segments) == 2 && $segments[0] == 'pages') {
            $page = Page::urlHandle($segments[1])->first();
            if ($page) {
                return [
                    'type' => 'page',
                    'template' => $this->routeTemplateMap['page'],
                    'controller' => 'page',
                    'params' => ['handle' => $segments[1]],
                    'model' => $page,
                ];
            }
        }
        
        // Handle /blogs/{handle}
        if (count($segments) == 2 && $segments[0] == 'blogs') {
            $blog = Blog::where('url_handle', $segments[1])->first();
            if ($blog) {
                return [
                    'type' => 'blog',
                    'template' => $this->routeTemplateMap['blog'],
                    'controller' => 'blog',
                    'params' => ['handle' => $segments[1]],
                    'model' => $blog,
                ];
            }
        }
        
        // Handle /blogs/{blog-handle}/{article-handle}
        if (count($segments) == 3 && $segments[0] == 'blogs') {
            $blog = Blog::where('url_handle', $segments[1])->first();
            if ($blog) {
                $article = Article::where('url_handle', $segments[2])
                    ->where('blog_id', $blog->id)
                    ->first();
                    
                if ($article) {
                    return [
                        'type' => 'article',
                        'template' => $this->routeTemplateMap['article'],
                        'controller' => 'article',
                        'params' => [
                            'blogHandle' => $segments[1],
                            'articleHandle' => $segments[2],
                        ],
                        'model' => $article,
                    ];
                }
            }
        }
        
        // Handle /collections/{handle}
        if (count($segments) == 2 && $segments[0] == 'collections') {
            $collection = Category::where('url_handle', $segments[1])
                ->where('active', 1)
                ->first();
                
            if ($collection) {
                return [
                    'type' => 'collection',
                    'template' => $this->routeTemplateMap['collection'],
                    'controller' => 'collection',
                    'params' => ['handle' => $segments[1]],
                    'model' => $collection,
                ];
            }
        }
        
        // Handle /products/{handle}
        if (count($segments) == 2 && $segments[0] == 'products') {
            $product = Product::where('url_handle', $segments[1])
                ->where('active', 1)
                ->first();
                
            if ($product) {
                return [
                    'type' => 'product',
                    'template' => $this->routeTemplateMap['product'],
                    'controller' => 'product',
                    'params' => ['handle' => $segments[1]],
                    'model' => $product,
                ];
            }
        }
        
        // No matching route found
        return null;
    }

    /**
     * Get template for a route type
     * 
     * @param string $type The route type
     * @param string|null $customTemplate Optional custom template name
     * @return Template The template instance
     */
    public function getTemplateForType(string $type, ?string $customTemplate = null): Template
    {
        if ($customTemplate) {
            $template = new Template($customTemplate);
            if ($template->exists()) {
                return $template;
            }
        }
        
        return new Template($this->routeTemplateMap[$type] ?? $this->routeTemplateMap['404']);
    }

    /**
     * Check if a template exists
     * 
     * @param string $templatePath The template path
     * @return bool Whether the template exists
     */
    public function templateExists(string $templatePath): bool
    {
        $template = new Template($templatePath);
        return $template->exists();
    }
} 