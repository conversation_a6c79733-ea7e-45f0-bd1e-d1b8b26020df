<?php

declare(strict_types=1);

namespace App\LiquidEngine\Services;

use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * Service for loading and rendering Liquid templates
 *
 * This class handles the loading of both direct Liquid templates and JSON-based templates,
 * providing a consistent interface for template rendering.
 *
 * @deprecated 
 */
class TemplateLoader
{
    /**
     * The template resolver instance
     *
     * @var TemplateResolver
     */
    protected $templateResolver;

    /**
     * The template context provider instance
     *
     * @var TemplateContextProvider
     */
    protected $contextProvider;

    /**
     * Create a new template loader instance
     *
     * @param TemplateResolver $templateResolver
     * @param TemplateContextProvider $contextProvider
     */
    public function __construct(
        TemplateResolver $templateResolver,
        TemplateContextProvider $contextProvider
    ) {
        $this->templateResolver = $templateResolver;
        $this->contextProvider = $contextProvider;
    }

    /**
     * Load a template with the given data
     *
     * @param Template|string|null $template The template to load
     * @param array $data The data to pass to the template
     * @param array $mergeData Additional data to merge
     * @return \Liquid\Factory|\Illuminate\Http\Response The Liquid factory instance with the template or a response
     */
    public function load($template, array $data = [], array $mergeData = [])
    {
        // Resolve the template path
        $templatePath = $this->resolveTemplatePath($template);

        // Create the context
        $context = $this->contextProvider->createContext($data, $mergeData);

        // Try to load as JSON template first
        try {
            if ($this->isJsonTemplate($templatePath)) {
                $result = $this->loadJsonTemplate($templatePath, $context);
                if ($result) {
                    Log::debug("Successfully loaded JSON template");
                    return $result;
                }
            }
        } catch (\Exception $e) {
            Log::debug("Error loading JSON template: " . $e->getMessage(), [
                'exception' => get_class($e),
                'trace' => $e->getTraceAsString()
            ]);
        }

        // If JSON template failed or doesn't exist, try direct template
        try {
            Log::debug("Attempting to load direct template");
            return $this->loadDirectTemplate($templatePath, $context);
        } catch (\Exception $e) {
            Log::debug("Error loading direct template: " . $e->getMessage(), [
                'exception' => get_class($e),
                'trace' => $e->getTraceAsString()
            ]);

            // Create a simple fallback HTML response
            $title = $data['page_title'] ?? ($data['title'] ?? 'Page');
            $content = '';

            if (isset($data['page'])) {
                if (is_object($data['page'])) {
                    if (method_exists($data['page'], 'toArray')) {
                        $page = $data['page']->toArray();
                        $content .= $page['content'] ?? '';
                    } elseif (method_exists($data['page'], 'content')) {
                        $content .= $data['page']->content;
                    }
                } elseif (is_array($data['page']) && isset($data['page']['content'])) {
                    $content .= $data['page']['content'];
                }
            } elseif (isset($data['content'])) {
                $content .= $data['content'];
            }

            $fallbackHtml = "<!DOCTYPE html><html><head><title>{$title}</title></head><body>";
            $fallbackHtml .= "<h1>{$title}</h1>";
            $fallbackHtml .= "<div>{$content}</div>";
            $fallbackHtml .= "<div>Error loading template: " . htmlspecialchars($e->getMessage()) . "</div>";
            $fallbackHtml .= "</body></html>";

            // Create a response object directly
            return response($fallbackHtml);
        }
    }

    /**
     * Resolve template path from various input types
     *
     * @param Template|string|null $template
     * @return string|array
     */
    protected function resolveTemplatePath($template)
    {
        // Handle Template objects
        if ($template instanceof Template) {
            return $template->getTemplates();
        }

        // Handle string paths
        if (is_string($template)) {
            // If the path doesn't contain dots or slashes, assume it's a template name
            if (!Str::contains($template, ['.', '/'])) {
                return "templates.{$template}";
            }

            // Return the path as is - formatting will be handled in loadDirectTemplate
            return $template;
        }

        // If null, use default template
        return Config::get('liquid.default_template', 'templates.default');
    }

    /**
     * Check if a template is JSON-based
     *
     * @param string|array $templatePath
     * @return bool
     */
    protected function isJsonTemplate($templatePath): bool
    {
        $theme = site('template');

        if (is_array($templatePath)) {
            foreach ($templatePath as $path) {
                $jsonPath = resource_path("themes/{$theme}/templates/{$path}.json");

                if (File::exists($jsonPath)) {
                    return true;
                }
            }
            return false;
        }

        $path = $templatePath;
        $jsonPath = resource_path("themes/{$theme}/templates/{$path}.json");
        return File::exists($jsonPath);
    }

    /**
     * Load a direct template
     *
     * @param string|array $templatePath
     * @param array $context
     * @return \Liquid\Factory|\Illuminate\Http\Response
     */
    protected function loadDirectTemplate($templatePath, array $context)
    {
        // Convert to array if string
        if (is_string($templatePath)) {
            $templatePath = [$templatePath];
        }

        // Process each template path to ensure proper formatting
        $formattedPaths = [];
        foreach ($templatePath as $path) {
            // Convert slashes to dots for Laravel view finder
            if (is_string($path) && Str::contains($path, '/') && !Str::contains($path, '.')) {
                $formattedPaths[] = str_replace('/', '.', $path);
            }
            // Convert dots to slashes for file paths
            elseif (is_string($path) && Str::contains($path, '.') && !Str::contains($path, '/')) {
                $formattedPaths[] = str_replace('.', '/', $path);
            }
            else {
                $formattedPaths[] = $path;
            }

            // Try with and without .liquid extension
            if (is_string($path) && !Str::endsWith($path, '.liquid')) {
                $formattedPaths[] = $path . '.liquid';
            }
        }

        // Add fallback templates
        $formattedPaths[] = 'templates.page';
        $formattedPaths[] = 'templates.default';

        // Log the paths we're trying
        Log::debug("Trying to load templates", [
            'paths' => $formattedPaths,
            'theme' => site('theme.key')
        ]);

        // Convert any Drop objects to arrays to avoid "Cannot use object as array" errors
        $safeContext = $this->convertDropObjectsToArrays($context);

        // First try with current theme
        try {
            // Use the liquidEngine() helper to render the template with formatted paths
            $result = liquid()->first($formattedPaths, $safeContext);
            if ($result) {
                return $result;
            }
        } catch (\Exception $e) {
            Log::debug("Error loading template with current theme: " . $e->getMessage());
        }

        // If template not found in current theme, try with _global theme
        try {
            $currentTheme = site('theme.key');
            if ($currentTheme !== '_global') {
                Log::debug("Trying with _global theme");
                site()->theme->key = '_global';

                try {
                    $result = liquid()->first($formattedPaths, $safeContext);
                    if ($result) {
                        Log::debug("Template found in _global theme");
                        return $result;
                    }
                } finally {
                    // Always restore the original theme
                    site()->theme->key = $currentTheme;
                }
            }
        } catch (\Exception $e) {
            Log::debug("Error loading template from _global theme: " . $e->getMessage());
        }

        // If all else fails, try to load the template file directly
        try {
            foreach ($formattedPaths as $path) {
                // Try current theme
                $theme = site('theme.key');
                if (!empty($theme)) {
                    $templateFile = resource_path("themes/{$theme}/{$path}");
                    if (File::exists($templateFile)) {
                        Log::debug("Found template file: {$templateFile}");
                        $templateContent = File::get($templateFile);
                        return liquid()->first(['string:' . $templateContent], $safeContext);
                    }
                }

                // Try global theme
                $templateFile = resource_path("themes/_global/{$path}");
                if (File::exists($templateFile)) {
                    Log::debug("Found global template file: {$templateFile}");
                    $templateContent = File::get($templateFile);
                    return liquid()->first(['string:' . $templateContent], $safeContext);
                }
            }
        } catch (\Exception $e) {
            Log::debug("Error loading template file directly: " . $e->getMessage());
        }

        // Create a simple fallback HTML response as last resort
        Log::warning("All template loading methods failed, using fallback HTML");
        $title = $context['page_title'] ?? 'Page';
        $content = '';

        if (isset($context['page'])) {
            if (is_object($context['page']) && method_exists($context['page'], 'toArray')) {
                $page = $context['page']->toArray();
                $content .= $page['content'] ?? '';
            } elseif (is_array($context['page']) && isset($context['page']['content'])) {
                $content .= $context['page']['content'];
            }
        } elseif (isset($context['content'])) {
            $content .= $context['content'];
        }

        $fallbackHtml = "<!DOCTYPE html><html><head><title>{$title}</title></head><body>";
        $fallbackHtml .= "<h1>{$title}</h1>";
        $fallbackHtml .= "<div>{$content}</div>";
        $fallbackHtml .= "</body></html>";

        // Create a response object directly
        return response($fallbackHtml);
    }

    /**
     * Load a JSON-based template
     *
     * @param string|array $templatePath
     * @param array $context
     * @return \Liquid\Factory|null|\Illuminate\Http\Response
     */
    protected function loadJsonTemplate($templatePath, array $context)
    {
        $theme = site('template');
        $jsonPath = null;

        if (is_array($templatePath)) {
            foreach ($templatePath as $path) {
                $tempJsonPath = resource_path("themes/{$theme}/templates/{$path}.json");
                if (File::exists($tempJsonPath)) {
                    $jsonPath = $tempJsonPath;
                    $templatePath = $path;
                    break;
                }
            }
        } else {
            $path = $templatePath;
            $jsonPath = resource_path("themes/{$theme}/templates/{$path}.json");
        }

        if (!$jsonPath || !File::exists($jsonPath)) {
            // Log that we couldn't find the JSON file
            Log::debug("JSON template not found: {$jsonPath}, theme: {$theme}");

            // Try global theme
            $globalJsonPath = resource_path("themes/_global/global-theme/templates/{$templatePath}.json");
            if (File::exists($globalJsonPath)) {
                $jsonPath = $globalJsonPath;
                Log::debug("Found JSON template in global theme: {$jsonPath}");
            } else {
                return null;
            }
        }

        try {
            // Load the JSON configuration
            $jsonData = json_decode(File::get($jsonPath), true);

            // dd($jsonPath, json_encode($jsonData)); // the schema I gave you

            if (!$jsonData || !isset($jsonData['sections'])/* || !isset($jsonData['order'])*/) {
                // Invalid JSON or missing required structure
                Log::warning("Invalid JSON template structure: {$jsonPath}");
                return null;
            }

            $layout = $jsonData['layout'] ?? 'theme';
            $sections = $jsonData['sections'] ?? [];

            $sectionHtml = [];

            foreach ($sections as $id => $section) {
                $viewName = "sections::{$section['type']}";

                if (view()->exists($viewName)) {
                    $sectionViewData = [
                        'section' => $section,
                        'sectionId' => $id,
                    ];

                    if ($section['type'] === 'tabs-collection') {
                        $sectionViewData['collection'] = [
                            'products' => \App\Models\Product\Product::paginate(12),
                        ];
                    }

                    $sectionHtml[$id] = view($viewName, $sectionViewData)->render();
                }
            }

            try {
                return \Illuminate\Support\Facades\View::mainResponse("layout::{$layout}", array_merge($context, [
                    'template_name' => $templatePath,
                    'sections' => $sections,
                    'content_for_layout' => implode('', $sectionHtml),
                    /*'content_for' => [
                        'blocks' => implode('', $sectionHtml),
                    ],*/
                ]));

            } catch (\Exception $e) {
                Log::error("Error rendering JSON template: " . $e->getMessage());

                // Create a simple fallback HTML response
                $title = $context['page_title'] ?? 'Page';
                $content = '';

                if (isset($context['page'])) {
                    if (is_object($context['page']) && method_exists($context['page'], 'toArray')) {
                        $page = $context['page']->toArray();
                        $content .= $page['content'] ?? '';
                    } elseif (is_array($context['page']) && isset($context['page']['content'])) {
                        $content .= $context['page']['content'];
                    }
                } elseif (isset($context['content'])) {
                    $content .= $context['content'];
                }

                $fallbackHtml = "<!DOCTYPE html><html><head><title>{$title}</title></head><body>";
                $fallbackHtml .= "<h1>{$title}</h1>";
                $fallbackHtml .= "<div>{$content}</div>";
                $fallbackHtml .= "<div>Sections that should be rendered:<br>" . nl2br(htmlspecialchars($sectionsContent)) . "</div>";
                $fallbackHtml .= "</body></html>";

                // Create a response object directly
                return response($fallbackHtml);
            }
        } catch (\Throwable $e) {
            dd([
                'message' => $e->getMessage(),
                'trace' => $e->getTrace(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
            Log::error("Error processing JSON template: {$jsonPath}", [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Create a simple fallback HTML response
            $title = $context['page_title'] ?? 'Page';
            $content = '';

            if (isset($context['page'])) {
                if (is_object($context['page']) && method_exists($context['page'], 'toArray')) {
                    $page = $context['page']->toArray();
                    $content .= $page['content'] ?? '';
                } elseif (is_array($context['page']) && isset($context['page']['content'])) {
                    $content .= $context['page']['content'];
                }
            } elseif (isset($context['content'])) {
                $content .= $context['content'];
            }

            $fallbackHtml = "<!DOCTYPE html><html><head><title>{$title}</title></head><body>";
            $fallbackHtml .= "<h1>{$title}</h1>";
            $fallbackHtml .= "<div>{$content}</div>";
            $fallbackHtml .= "<div>Error processing template: " . htmlspecialchars($e->getMessage()) . "</div>";
            $fallbackHtml .= "</body></html>";

            // Create a response object directly
            return response($fallbackHtml);
        }
    }

    /**
     * Convert any Drop objects in the context to arrays
     *
     * @param array $context
     * @return array
     */
    protected function convertDropObjectsToArrays(array $context): array
    {
        $result = [];

        foreach ($context as $key => $value) {
            if (is_object($value)) {
                if (method_exists($value, 'toArray')) {
                    // If the object has a toArray method, use it
                    $result[$key] = $value->toArray();
                } elseif (method_exists($value, 'toJson')) {
                    // If the object has a toJson method, decode it back to array
                    $result[$key] = json_decode($value->toJson(), true);
                } elseif ($value instanceof \Liquid\Drop) {
                    // If it's a Liquid Drop, just pass it through as Liquid will handle it
                    $result[$key] = $value;
                } else {
                    // For other objects, try to convert to array or just pass through
                    $result[$key] = (array) $value;
                }
            } elseif (is_array($value)) {
                // Recursively convert nested arrays
                $result[$key] = $this->convertDropObjectsToArrays($value);
            } else {
                // For scalar values, just pass through
                $result[$key] = $value;
            }
        }

        return $result;
    }
}
