<?php

declare(strict_types=1);

namespace App\LiquidEngine\Providers;

use Liquid\LiquidServiceProvider as BaseLiquidServiceProvider;
use Liquid\ViewFinders\FileViewFinder;
use App\LiquidEngine\ViewFinder\CustomDatabaseViewFinder;

/**
 * CloudCart-specific Liquid service provider
 *
 * This provider extends the base Laravel-Liquid service provider
 * and adds CloudCart-specific custom view finder drivers.
 * The base CompiledStoreManager functionality is handled in the base provider.
 */
class LiquidServiceProvider extends BaseLiquidServiceProvider
{
    public function register(): void
    {
        parent::register(); // This registers CompiledStoreManager and base functionality

        // Alias "liquid.factory" to "liquid" for CloudCart convenience
        // so that `app('liquid')` and `liquid()` both resolve to the factory.
        $this->app->alias('liquid.factory', 'liquid');
    }

    public function boot(): void
    {
        // Register CloudCart-specific custom drivers
        $this->registerCustomDatabaseDriver();
        $this->registerCustomFileDriver();
    }

    /**
     * Register the custom database driver for multi-tenant theme templates
     */
    protected function registerCustomDatabaseDriver()
    {
        $this->app->make('liquid.view.finder.manager')->extend('custom_database', function (array $app, $config): \App\LiquidEngine\ViewFinder\CustomDatabaseViewFinder {
            $connection = $app['db']->connection($config['connection'] ?? null);

            return new CustomDatabaseViewFinder($connection, $config['table']);
        });
    }

    /**
     * Register the custom file driver for theme-specific templates
     */
    protected function registerCustomFileDriver()
    {
        $this->app->make('liquid.view.finder.manager')->extend('custom_file', function (array $app, $config): \Liquid\ViewFinders\FileViewFinder {
            $paths = array_map(function ($path) {
                return sprintf($path, site('theme.key'));
            }, (array)($config['path'] ?? []));

            return new FileViewFinder($app['files'], $paths);
        });
    }
}
