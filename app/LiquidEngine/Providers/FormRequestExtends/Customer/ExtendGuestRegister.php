<?php

declare(strict_types=1);

namespace App\LiquidEngine\Providers\FormRequestExtends\Customer;

use App\Exceptions\NewRedirect;
use App\Models\Store\Cart as CartModel;
use App\Models\Customer\Customer;
use Illuminate\Validation\Validator;
use Validation;
use Auth;

trait ExtendGuestRegister
{

    /**
     * Validate customer exists by email
     */
    protected function bootExtendGuestRegister()
    {
        Validation::extendImplicit('guest_register', function(string $attribute, $value, $parameters, \Illuminate\Contracts\Validation\Validator $validator): bool {
            if(($cartInstance = CartModel::instance()) && $cartInstance->hasDigital()) {
                $validator->setCustomMessages([
                    $attribute . '.guest_register' => __('sf.widget.checkout.err.only_registered_customers_can_buy_digital_products')
                ]);
                return false;
            }

            if(Auth::getCustomerAccess() == 'member') {
                $validator->setCustomMessages([
                    $attribute . '.guest_register' => __('sf.widget.checkout.err.store_does_not_allow_guest_accounts')
                ]);
                return false;
            }

            // email exists
            if (Customer::existsByEmail($value)) {
                $validator->setCustomMessages([
                    $attribute . '.guest_register' => __('sf.checkout.email.taken')
                ]);
                return false;
//                throw new NewRedirect(route('checkout.authorize.login'), __('sf.checkout.email.taken'));
            }

            return true;
        });
    }

}
