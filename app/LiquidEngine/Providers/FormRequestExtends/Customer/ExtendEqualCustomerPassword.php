<?php

declare(strict_types=1);

namespace App\LiquidEngine\Providers\FormRequestExtends\Customer;

use App\Models\Customer\Customer;
use Auth;
use Illuminate\Validation\Validator;
use Validation;

trait ExtendEqualCustomerPassword
{
    /**
     * Validate customer password equal
     */
    protected function bootExtendEqualCustomerPassword()
    {
        Validation::extendImplicit('equal_customer_password', function (string $attribute, $value, $parameters, \Illuminate\Contracts\Validation\Validator $validator): bool {
            $customer = Customer::find(!empty($parameters[0]) ? $parameters[0] : Auth::customerId());
            if ($customer && $customer->generatePasswordHash($value) == $customer->password) {
                return true;
            }

            $validator->setCustomMessages([
                $attribute . '.equal_customer_password' => __('customer.error.invalid_old_password')
            ]);

            return false;
        });
    }
}
