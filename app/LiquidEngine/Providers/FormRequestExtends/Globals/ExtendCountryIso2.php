<?php

declare(strict_types=1);

namespace App\LiquidEngine\Providers\FormRequestExtends\Globals;

use App\Locale\Country;
use Illuminate\Validation\Validator;
use Validation;

trait ExtendCountryIso2
{

    /**
     * Validate post code
     */
    protected function bootExtendCountryIso2()
    {
        Validation::extendImplicit('country_iso2', function ( string $attribute,  $value,  $parameters,  \Illuminate\Contracts\Validation\Validator $validator): bool {
            $countries = Country::all();
            if (!isset($countries[strtoupper($value)])) {
                $validator->setCustomMessages([
                    $attribute . '.country_iso2' => __('ssl.error.country_invalid')
                ]);
                return false;
            }

            return true;
        });
    }
}
