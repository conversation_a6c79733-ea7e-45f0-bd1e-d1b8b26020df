<?php

declare(strict_types=1);

namespace App\LiquidEngine\Providers\FormRequestExtends\Store\Checkout;

use Validation;

trait ExtendPaymentProvider
{

    protected $shipping_provider;

    protected function bootExtendPaymentProvider()
    {
        $this->registerValidatePaymentProviderCod();
    }

    protected function registerValidatePaymentProviderCod()
    {
        // custom validation provider
        Validation::extendImplicit('validate_payment_provider_cod', function($attribute, $value) {
            if('cod' == $value && $this->getCart() && !is_null($shipping = $this->getShippingProvider())) {
                $manager = $shipping->manager;
                $manager->setOrder($this->getCart()->getCheckout());
                return $manager->supportsCashOnDelivery();
            }

            return true;
        });
    }

    /**
     * @return \App\Models\Shipping\ShippingProvider|null
     * @throws \Throwable
     */
    protected function getShippingProvider()
    {
        if(is_null($this->shipping_provider) && ($cartInstance = $this->getCart())) {
            $this->shipping_provider = $cartInstance->getShipping();
        }

        return $this->shipping_provider;
    }

}
