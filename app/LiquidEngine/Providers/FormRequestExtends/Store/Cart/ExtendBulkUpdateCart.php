<?php

declare(strict_types=1);

namespace App\LiquidEngine\Providers\FormRequestExtends\Store\Cart;

use App\Helper\ArrayCache;
use App\Helper\YesNo;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Store\Cart;
use Illuminate\Validation\Validator;
use Validation;

trait ExtendBulkUpdateCart
{

    protected function bootExtendBulkUpdateCart()
    {
        $this->registerEnableSell();
        $this->registerCustomExists();
    }

    protected function registerEnableSell()
    {
        Validation::extendImplicit('cart_bulk_enable_sell', function (string $attribute, $value, $parameters, \Illuminate\Contracts\Validation\Validator $validator): bool {
            $variant = $this->getVariantFromCart($attribute);

            if ($variant->item->tracking == YesNo::True && $variant->item->continue_selling == YesNo::False) {
                if(!($enabled = !!$variant->quantity)) {
                    $validator->setCustomMessages([
                        $attribute . '.cart_bulk_enable_sell' => __('item.out_of_stock')
                    ]);
                }

                return $enabled;
            }

            return true;
        });
    }

    protected function registerCustomExists()
    {
        Validation::extendImplicit('cart_bulk_custom_exists', function (string $attribute, $value, $parameters, \Illuminate\Contracts\Validation\Validator $validator): bool {
            if (($cartInstance = Cart::instance()) && $cartInstance->hasProduct($this->getIdFromDotString($attribute))) {
                return true;
            }

            $validator->setCustomMessages([
                $attribute . '.cart_bulk_custom_exists' => __('error.no_longer_exists.product')
            ]);

            return false;
        });
    }

    /**
     * @param $attribute
     * @return Variant
     * @throws \Throwable
     */
    private function getVariantFromCart(string $attribute)
    {
        $cartInstance = Cart::instance();
        return ArrayCache::remember('cart.variant.' . $attribute, function () use ($attribute, $cartInstance) {
            return Variant::whereHas('item', function ($query): void {
                /** @var Product $query */
                $query->active();
            })->with(['item' => function ($query): void {
                /** @var Product $query */
                $query->active();
            }])->find($cartInstance ? $cartInstance->product($this->getIdFromDotString($attribute))->variant_id : null);
        });
    }

    /**
     * @param $string
     * @return string|integer
     */
    private function getIdFromDotString($string): string
    {
        $parts = explode('.', $string);
        return end($parts);
    }

}
