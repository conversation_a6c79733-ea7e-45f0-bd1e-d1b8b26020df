<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 11:33 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\LiquidFilters;

use App\LiquidEngine\Services\UrlGenerator;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Arr;
use Illuminate\Support\HtmlString;
use Liquid\Filters\AbstractFilters;

/**
 * CloudCart-specific custom filters that extend Laravel-Liquid
 *
 * This class provides additional filters for use in Liquid templates
 * that are specific to CloudCart functionality.
 */
class All extends AbstractFilters
{
    /**
     * @var UrlGenerator|null
     */
    protected ?UrlGenerator $urlGenerator = null;

    /**
     * Get the URL generator instance
     *
     * @return UrlGenerator
     */
    protected function getUrlGenerator(): UrlGenerator
    {
        if ($this->urlGenerator === null) {
            $this->urlGenerator = new UrlGenerator();
        }

        return $this->urlGenerator;
    }

    /**
     * Dump variable contents for debugging
     *
     * @param mixed $var
     * @return string
     */
    public function dump($var): string|false
    {
        ob_start();
        foreach (func_get_args() as $arg) {
            dump($this->_dataToArray($arg));
        }

        return ob_get_clean();
    }

    /**
     * Dump and die for debugging
     *
     * @param mixed $var
     * @return void
     */
    public function dd($var): void
    {
        foreach (func_get_args() as $arg) {
            ddob($this->_dataToArray($arg));
        }
    }

    /**
     * Translate a string using Laravel's translation system
     *
     * @param string $key
     * @param mixed ...$args
     * @return string
     */
    public function t($key, ...$args)
    {
        if (empty($key)) {
            return $key;
        }

        if (!is_array($args)) {
            $args = [$args];
        }

        $replace = [];
        for ($i = 0; $i < count($args); $i = $i + 2) {
            if (isset($args[$i + 1])) {
                $replace[$args[$i]] = $args[$i + 1];
            }
        }

        $return = __($key, $replace);

        if (!is_string($return)) {
            return $key;
        }

        return $return;
    }

    /**
     * Generate asset URL for theme assets
     *
     * @param string|null $input
     * @return string
     * @throws \Exception
     * @deprecated Use UrlFilters::asset_url() instead
     */
    public function asset_url(?string $input = null): string
    {
        return $this->getUrlGenerator()->assetUrl($input);
    }

    /**
     * Generate HTML for CSS stylesheet inclusion
     *
     * @param string|null $input
     * @return HtmlString|null
     */
    public function stylesheet_tag(?string $input = null): ?HtmlString
    {
        if ($input === null) {
            return null;
        }

        // Add integrity and crossorigin attributes for CDN resources
        $attributes = '';
        if (str_starts_with($input, 'http')) {
            $attributes = ' crossorigin="anonymous"';
        }

        $tag = "<link rel=\"stylesheet\" href=\"{$input}\"{$attributes}>";

        return new HtmlString($tag);
    }

    /**
     * Generate HTML for JavaScript file inclusion
     *
     * @param string|null $input
     * @return HtmlString|null
     */
    public function script_tag(?string $input = null): ?HtmlString
    {
        if ($input === null) {
            return null;
        }

        // Add integrity and crossorigin attributes for CDN resources
        $attributes = '';
        if (str_starts_with($input, 'http')) {
            $attributes = ' crossorigin="anonymous"';
        }

        $tag = "<script src=\"{$input}\"{$attributes}></script>";

        return new HtmlString($tag);
    }

    /**
     * Conditional operator as a filter
     *
     * @param mixed $input
     * @param string $true
     * @param string|null $false
     * @return string
     */
    public function ifelse($input, $true, $false = null)
    {
        return $input ? $true : $false;
    }

    /**
     * Get cookie value
     *
     * @param string $name
     * @param mixed $default
     * @return mixed
     */
    public function cookie($name, $default = null)
    {
        $name = preg_replace('~[^a-zA-Z0-9\_]~', '_', $clearName = $name);
        return Arr::get($_COOKIE ?? [], $name, Arr::get($_COOKIE ?? [], $clearName, $default));
    }

    /**
     * Convert data to array recursively
     *
     * @param mixed $items
     * @return array|mixed
     */
    protected function _dataToArray($items)
    {
        if ($items instanceof Arrayable) {
            $items = $items->toArray();
        }

        if (!is_array($items)) {
            return $items;
        }

        return array_map([$this, '_dataToArray'], $items);
    }
}
