<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Tags;

use App\LiquidEngine\Traits\Customer\SocialConnect;
use Liquid\AbstractBlock;
use Liquid\Context;
use Liquid\Document;
use Liquid\Exceptions\SyntaxError;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;

class TagSocials extends AbstractBlock
{
    use SocialConnect;

    /**
     * @var string The name of the template
     */
    private $form;

    /**
     * @var string
     */
    private $as = 'socials';

    /**
     * @var Document The Document that represents the included template
     */
    private $document;

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     * @param LiquidCompiler $compiler
     * @throws SyntaxError
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $regex = new Regexp('/^[\'\"]([a-zA-Z_][\w]*)[\'\"]?(?:\s+as\s+[\'\"]([a-zA-Z_][\w]*)[\'\"])?$/');

        if ($regex->match($markup)) {
            $this->form = $regex->matches[1];

            if (!empty($regex->matches[3])) {
                $this->as = $regex->matches[3];
            }
        } else {
            throw new SyntaxError("Error in tag 'socials' - Valid syntax: socials '[form]' [as 'variable']", -1, $compiler);
        }

        parent::__construct($markup, $tokens, $compiler);
    }

    /**
     * Renders the block
     *
     * @param Context $context
     *
     * @return string
     * @throws LiquidException
     */
    public function render(Context $context): string
    {
        $socials = [];
        if ($this->form == 'login') {
            $socials = $this->getSocialLogin();
        } elseif ($this->form == 'account') {
            $socials = $this->getSocialConnections();
        }

        if (empty($socials)) {
            return '';
        }

        $context->push();

        $context->set($this->as, $socials);

        $output = parent::render($context);

        $context->pop();

        return $output;
    }

}
