<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Tags;

use App\Exceptions\Error;
use App\Integration\Instagram\InstagramManager;
use Liquid\AbstractBlock;
use Liquid\Context;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;

class TagInstagram extends AbstractBlock
{

    /**
     * @var string
     */
    private string $as = 'instagram';

    /**
     * @var string
     */
    private string $section = 'global';

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     * @param LiquidCompiler $compiler
     * @throws LiquidException
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $regex = new Regexp('/^(?:[\'\"]([a-zA-Z_][\w]*)[\'\"])?(?:\s+as\s+[\'\"]([a-zA-Z_][\w]*)[\'\"])?$/');

        $regex->match($markup);

        if (!empty($regex->matches[2])) {
            $this->section = $regex->matches[2];
        }

        if (!empty($regex->matches[5])) {
            $this->as = $regex->matches[5];
        }

        parent::__construct($markup, $tokens, $compiler);
    }

    /**
     * Returns the string that delimits the end of the block
     *
     * @return string
     */
    protected function blockDelimiter(): string
    {
        return "endinstagram";
    }

    /**
     * Renders the block
     *
     * @param Context $context
     *
     * @return string
     * @throws Error
     */
    public function render(Context $context): string
    {
        $manager = new InstagramManager();
        if (!$manager->isActive()) {
            return '';
        }

        return '';

//        dd($manager->getSettings());
//
//        $policies = collect();
//        if(\Apps::enabled('gdpr')) {
//            $policies = \GDPR::getFormPolicies($this->form);
//        }
//
//        if($policies->isEmpty()) {
//            return '';
//        }
//
//        $policies = new ArrayJson($policies->map(function(PolicyModel $policy) {
//            return new PolicyDrop($policy);
//        }));
//
//        $context->push();
//        $context->set($this->as, $policies);
//
//        $output = parent::render($context);
//
//        $context->pop();
//
//        return $output;
    }

}
