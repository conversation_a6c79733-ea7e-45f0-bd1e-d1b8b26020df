<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Tags;

use App\LiquidEngine\Helpers\ThemeSettingsParser\OrderBySelect;
use Liquid\AbstractBlock;
use Liquid\Context;
use Liquid\Exceptions\SyntaxError;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;
use App\LiquidEngine\Models\Theme;

class TagOrderBy extends AbstractBlock
{
    /**
     * @var string
     */
    private $as = 'sorting';

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     * @param LiquidCompiler $compiler
     * @throws SyntaxError
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $regexAs = new Regexp('/^(?:\s+as\s+[\'\"]([a-zA-Z_][\w]*)[\'\"])?$/');
        if ($regexAs->match($markup)) {
            if (!empty($regexAs->matches[2])) {
                $this->as = $regexAs->matches[2];
            }
        } else {
            throw new SyntaxError("Error in tag 'orderBy' - Valid syntax: orderBy [as 'variable']", -1, $compiler);
        }

        parent::__construct($markup, $tokens, $compiler);
    }

    /**
     * Returns the string that delimits the end of the block
     *
     * @return string
     */
    protected function blockDelimiter(): string
    {
        return "endorderBy";
    }

    /**
     * Renders the block
     *
     * @param Context $context
     *
     * @return string
     * @throws LiquidException
     */
    public function render(Context $context): string
    {
        $settings = Theme::getThemeSystemConfigs()->get('@system.products.order_by');
        if (!($settings['enabled'] ?? false)) {
            return '';
        }

        $results = [];
        $allowed = OrderBySelect::getAllowed();
        foreach ($allowed as $key => $directions) {
            if ($settings['_allowed_order_by'][$key] ?? false) {
                foreach ($directions as $direction) {
                    $results[] = [
                        'id' => ($id = sprintf('%s-%s', $key, $direction)),
                        'label_suffix' => sprintf('.order_by.%s.%s', $key, $direction),
                        'selected' => is_scalar(request()->query('sort')) && request()->query('sort') === $id,
                        'default' => $settings['_default_order_by'] ?? null
                    ];
                }
            }
        }

        $results = collect($results);
        if (!$results->firstWhere('selected', true)) {
            $results = $results->map(function (array $result) use ($settings): array {
                $result['selected'] = $result['id'] === ($settings['_default_order_by'] ?? null);
                return $result;
            })->all();
        }

        if (!count($results)) {
            return '';
        }

        $context->push();

        $context->set($this->as, $results);

        $output = parent::render($context);

        $context->pop();

        return $output;
    }

}
