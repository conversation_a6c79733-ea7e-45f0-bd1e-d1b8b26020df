<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Tags;

use Illuminate\Support\Arr;
use Liquid\AbstractBlock;
use Liquid\Context;
use Liquid\Exceptions\SyntaxError;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;
use App\LiquidEngine\Models\Theme;

class TagPerPage extends AbstractBlock
{
    /**
     * @var string
     */
    private $as = 'steps';

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     * @param LiquidCompiler $compiler
     * @throws SyntaxError
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $regexAs = new Regexp('/^(?:\s+as\s+[\'\"]([a-zA-Z_][\w]*)[\'\"])?$/');
        if ($regexAs->match($markup)) {
            if (!empty($regexAs->matches[2])) {
                $this->as = $regexAs->matches[2];
            }
        } else {
            throw new SyntaxError("Error in tag 'orderBy' - Valid syntax: perPage [as 'variable']", -1, $compiler);
        }

        parent::__construct($markup, $tokens, $compiler);
    }

    /**
     * Returns the string that delimits the end of the block
     *
     * @return string
     */
    protected function blockDelimiter(): string
    {
        return "endperPage";
    }

    /**
     * Renders the block
     *
     * @param Context $context
     *
     * @return string
     * @throws LiquidException
     */
    public function render(Context $context): string
    {
        if (activeRoute('blog.list blog.view')) {
            $settings = Theme::getThemeSystemConfigs()->get('@system.articles.per_page');
        } else {
            $settings = Theme::getThemeSystemConfigs()->get('@system.products.per_page');
        }

        if (!($settings['enabled'] ?? false) || empty($settings['_per_page_data'])) {
            return '';
        }

        $default = $settings['_per_page_default'] ?? 9;
        if (!in_array($default, $settings['_per_page_data'])) {
            $default = Arr::first($settings['_per_page_data']);
        }

        $results = [];
        foreach ($settings['_per_page_data'] as $key => $per_page) {
            $results[] = [
                'id' => $per_page,
                'label_suffix' => $per_page,
                'selected' => is_numeric($query_per_page = request()->query('per_page')) && (int)$query_per_page === $per_page,
                'default' => $default
            ];
        }

        $results = collect($results);
        if (!$results->firstWhere('selected', true)) {
            $results = $results->map(function (array $result) use ($default): array {
                $result['selected'] = $result['id'] === $default;
                return $result;
            })->all();
        }

        if (count($results) < 2) {
            return '';
        }

        $context->push();

        $context->set($this->as, $results);

        $output = parent::render($context);

        $context->pop();

        return $output;
    }

}
