<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Tags;

use Auth;
use Liquid\AbstractBlock;
use Liquid\Context;
use Liquid\Exceptions\SyntaxError;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Traits\DecisionTrait;
use Liquid\Traits\HelpersTrait;
use App\LiquidEngine\LiquidHelpers\Drop\Customer;
use ReflectionException;

/**
 * An auth statement - CloudCart custom tag that extends Laravel-Liquid
 *
 * This tag extends the core Laravel-Liquid functionality to provide
 * authentication-specific template logic for CloudCart.
 *
 * Example:
 *
 *     {% auth %} {{auth.first_name}} {% else %} Hi guest {% endif %}
 *
 */
class TagAuth extends AbstractBlock
{
    use DecisionTrait;
    use HelpersTrait;

    /**
     * Array holding the nodes to render for each logical block
     */
    private array $nodelistHolders = [];

    /**
     * Array holding the block type, block markup (conditions) and block nodelist
     *
     * @var array
     */
    protected $blocks = [];

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     * @param LiquidCompiler $compiler
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $this->nodelist = &$this->nodelistHolders[count($this->blocks)];

        $this->blocks[] = ['auth', $markup, &$this->nodelist];

        parent::__construct($markup, $tokens, $compiler);
    }

    /**
     * Handle unknown tags
     *
     * @param string $tag
     * @param array|string $params
     * @param array $tokens
     * @param string|null $file
     * @param int $line
     * @return void
     * @throws LiquidException
     * @throws ReflectionException
     * @throws SyntaxError
     */
    protected function unknownTag(string $tag, array|string $params, array $tokens, ?string $file = null, int $line = 0)
    {
        if (in_array($tag, ['else'])) {
            // Update reference to nodelistHolder for this block
            $this->nodelist = &$this->nodelistHolders[count($this->blocks) + 1];
            $this->nodelistHolders[count($this->blocks) + 1] = array();

            $this->blocks[] = [$tag, $params, &$this->nodelist];
        } else {
            parent::unknownTag($tag, $params, $tokens, $file, $line);
        }
    }

    /**
     * Render the tag
     *
     * @param Context $context
     *
     * @return string
     * @throws LiquidException
     */
    public function render(Context $context): string
    {
        $context->push();

        $result = '';
        foreach ($this->blocks as $block) {
            if ($block[0] == 'auth' && $this->isLogged()) {
                $context->set('auth', $this->getCustomer());

                $result = $this->renderAll($block[2], $context);
                break;
            } elseif ($block[0] == 'else' && !$this->isLogged()) {
                $result = $this->renderAll($block[2], $context);
                break;
            }
        }

        $context->pop();

        return $result;
    }

    /**
     * Check if customer is logged in
     *
     * @return bool
     */
    protected function isLogged(): bool
    {
        return !!Auth::customerId();
    }

    /**
     * Get customer drop object
     *
     * @return Customer|null
     */
    protected function getCustomer(): ?\App\LiquidEngine\LiquidHelpers\Drop\Customer
    {
        if ($this->isLogged()) {
            return new Customer(Auth::customer());
        }

        return null;
    }
}
