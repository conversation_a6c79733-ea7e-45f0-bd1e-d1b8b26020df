<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 4.9.2019 г.
 * Time: 14:37 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Format;

use App\LiquidEngine\LiquidHelpers\Drop\Currency;

class PriceParts
{

    /**
     * @param mixed $price
     * @param mixed $currencyCode
     * @param mixed $languageCode
     * @return mixed
     */
    public static function format($price, $currencyCode = null, $languageCode = null): array
    {
        $currency = new Currency($currencyCode ?: site('currency'), $languageCode ?: setting('language'));

        $parts = explode('.', number_format(floatval($price), 2, '.', ''));

        return [
            'price' => $parts[0],
            'decimal' => $parts[1] ?? null,
            'currency' => [
                'code' => $currency->code(),
                'decimal_point' => $currency->decimal_point(),
                'thousands_sep' => $currency->thousands_separator(),
                'coins_pad' => $currency->coins_pad(),
                'sign_left' => $currency->sign_left(),
                'sign_right' => $currency->sign_right(),
            ]
        ];
    }

}
