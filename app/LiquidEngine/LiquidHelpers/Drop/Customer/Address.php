<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Customer;

use App\Models\Customer\CustomerShippingAddress;
use App\LiquidEngine\LiquidHelpers\Drop\Customer\Address\Office;
use App\LiquidEngine\LiquidHelpers\Drop\Phone;
use App\LiquidEngine\LiquidHelpers\Drop\Customer\Address\Quarter;
use App\LiquidEngine\LiquidHelpers\Drop\Helpers\Address AS HelperAddress;

class Address extends HelperAddress
{
    /**
     * Address type constants for classification
     */
    public const TYPE_BILLING = 'billing';
    public const TYPE_SHIPPING = 'shipping';
    public const TYPE_DEFAULT = 'default';
    public const TYPE_HOME = 'home';
    public const TYPE_WORK = 'work';
    public const TYPE_OTHER = 'other';

    /**
     * Address validation status constants
     */
    public const VALIDATION_UNKNOWN = 'unknown';
    public const VALIDATION_VALID = 'valid';
    public const VALIDATION_INVALID = 'invalid';
    public const VALIDATION_PARTIAL = 'partial';

    protected bool $_default;

    /**
     * @param mixed $address
     * @param bool $default
     * @return mixed
     */
    public function __construct($address, bool $default = false)
    {
        parent::__construct($address);
        $this->_default = $default;
    }

    /**
     * @param $address
     * @param bool $default
     * @return Address
     */
    public static function make($address, bool $default = false): static
    {
        return new static($address, $default);
    }

    /**
     * @return string
     */
    public function first_name()
    {
        return $this->_address->first_name;
    }

    /**
     * @return string
     */
    public function last_name()
    {
        return $this->_address->last_name;
    }

    /**
     * @return string
     */
    public function full_name()
    {
        return $this->_address->full_name;
    }

    /**
     * @return string
     */
    public function type(): string
    {
        return $this->_address instanceof CustomerShippingAddress ? 'shipping' : 'billing';
    }

    /**
     * @return Phone
     */
    public function phone(): \App\LiquidEngine\LiquidHelpers\Drop\Phone
    {
        return new Phone($this->_address->phone, $this->_address->phone_country_iso2);
    }

    /**
     * @return null|Quarter
     */
    public function quarter(): ?\App\LiquidEngine\LiquidHelpers\Drop\Customer\Address\Quarter
    {
        if($quarter = $this->_address->address->getQuarter()) {
            return new Quarter($quarter);
        }

        return null;
    }

    /**
     * @return null|Office
     */
    public function office(): ?\App\LiquidEngine\LiquidHelpers\Drop\Customer\Address\Office
    {
        if($office = $this->_address->address->getOffice()) {
            return new Office($office);
        }

        return null;
    }

    public function default(): bool
    {
        return $this->_default;
    }

    public function action(): array
    {
        return [
            'set_as_default' => route(sprintf('site.account.address.%s.default', $this->type()), ['address_id' => $this->id()]),
            'edit' => route(sprintf('site.account.address.%s.edit', $this->type()), ['address_id' => $this->id()]),
            'delete' => route(sprintf('site.account.address.%s.remove', $this->type()), ['address_id' => $this->id()]),
        ];
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'first_name' => $this->first_name(),
            'last_name' => $this->last_name(),
            'full_name' => $this->full_name(),
            'phone' => $this->phone(),
            'quarter' => $this->quarter(),
            'office' => $this->office(),
            'default' => $this->default(),
            'action' => $this->action(),
        ]);
    }

    /**
     * Get the first address line (street address)
     * Shopify compatibility: Standard address1 property
     *
     * @return string The first line of the address
     */
    public function address1(): string
    {
        return (string) $this->_address->address;
    }

    /**
     * Get the second address line (apartment, suite, etc.)
     * Shopify compatibility: Standard address2 property
     *
     * @return string The second line of the address
     */
    public function address2(): string
    {
        return (string) $this->_address->address2;
    }

    /**
     * Get the city name as string
     * Shopify compatibility: Standard city property
     *
     * @return string The city name
     */
    public function city_name(): string
    {
        return (string) $this->_address->city;
    }

    /**
     * Get the company name associated with this address
     * Shopify compatibility: Standard company property
     *
     * @return string The company name
     */
    public function company(): string
    {
        return (string) $this->_address->company;
    }

    /**
     * Get the country code (ISO 3166-1 alpha-2)
     * Shopify compatibility: Standard country_code property
     *
     * @return string Two-letter country code (e.g., 'US', 'CA', 'GB')
     */
    public function country_code(): string
    {
        return strtoupper((string) $this->_address->country_code);
    }

    /**
     * Get the country name in the current locale
     * Shopify compatibility: Standard country property
     *
     * @return string Full country name (e.g., 'United States', 'Canada')
     */
    public function country_name(): string
    {
        return (string) $this->_address->country;
    }

    /**
     * Get the full name (first + last)
     * Shopify compatibility: Standard name property
     *
     * @return string The full name
     */
    public function name(): string
    {
        $parts = array_filter([
            $this->first_name(),
            $this->last_name()
        ]);

        return implode(' ', $parts);
    }

    /**
     * Get the phone number as string
     * Shopify compatibility: Standard phone property
     *
     * @return string The phone number
     */
    public function phone_number(): string
    {
        return (string) $this->_address->phone;
    }

    /**
     * Get the province/state code
     * Shopify compatibility: Standard province_code property
     *
     * @return string Province/state code (e.g., 'NY', 'CA', 'ON')
     */
    public function province_code(): string
    {
        return strtoupper((string) $this->_address->zone_code);
    }

    /**
     * Get the province/state name
     * Shopify compatibility: Standard province property
     *
     * @return string Full province/state name (e.g., 'New York', 'California')
     */
    public function province(): string
    {
        return (string) $this->_address->zone;
    }

    /**
     * Get the postal/ZIP code
     * Shopify compatibility: Standard zip property
     *
     * @return string The postal/ZIP code
     */
    public function zip(): string
    {
        return (string) $this->_address->postcode;
    }

    /**
     * Get formatted address as a single string
     * Shopify compatibility: Standard formatted_address property
     *
     * @return string Complete formatted address
     */
    public function formatted_address(): string
    {
        $lines = [];

        // Add name if available
        if ($this->name()) {
            $lines[] = $this->name();
        }

        // Add company if available
        if ($this->company()) {
            $lines[] = $this->company();
        }

        // Add address lines
        if ($this->address1()) {
            $lines[] = $this->address1();
        }

        if ($this->address2()) {
            $lines[] = $this->address2();
        }

        // Add city, province, zip
        $cityLine = [];
        if ($this->city()) {
            $cityLine[] = $this->city();
        }

        if ($this->province()) {
            $cityLine[] = $this->province();
        }

        if ($this->zip()) {
            $cityLine[] = $this->zip();
        }

        if (!empty($cityLine)) {
            $lines[] = implode(', ', $cityLine);
        }

        // Add country
        if ($this->country()) {
            $lines[] = $this->country();
        }

        return implode("\n", $lines);
    }

    /**
     * Get address formatted for display with HTML line breaks
     *
     * @return string HTML-formatted address with <br> tags
     */
    public function formatted_address_html(): string
    {
        return nl2br($this->formatted_address());
    }

    /**
     * Get single-line formatted address (comma-separated)
     *
     * @return string Single line address format
     */
    public function single_line(): string
    {
        return str_replace("\n", ', ', $this->formatted_address());
    }

    /**
     * Get abbreviated formatted address (no country)
     *
     * @return string Abbreviated address format
     */
    public function abbreviated(): string
    {
        $parts = [];

        if ($this->address1()) {
            $parts[] = $this->address1();
        }

        if ($this->city()) {
            $parts[] = $this->city();
        }

        if ($this->province_code()) {
            $parts[] = $this->province_code();
        }

        if ($this->zip()) {
            $parts[] = $this->zip();
        }

        return implode(', ', $parts);
    }

    /**
     * Get the address type/classification
     *
     * @return string Address type (billing, shipping, etc.)
     */
    public function address_type(): string
    {
        // Determine type based on context or source data
        if (isset($this->_address->type)) {
            return (string) $this->_address->type;
        }

        // Default determination logic
        if (isset($this->_address->is_default) && $this->_address->is_default) {
            return static::TYPE_DEFAULT;
        }

        return static::TYPE_OTHER;
    }

    /**
     * Check if this is the default address
     *
     * @return bool True if this is the default address
     */
    public function is_default(): bool
    {
        return (bool) ($this->_address->is_default ?? false);
    }

    /**
     * Check if this is a billing address
     *
     * @return bool True if this is a billing address
     */
    public function is_billing(): bool
    {
        return $this->address_type() === static::TYPE_BILLING;
    }

    /**
     * Check if this is a shipping address
     *
     * @return bool True if this is a shipping address
     */
    public function is_shipping(): bool
    {
        return $this->address_type() === static::TYPE_SHIPPING;
    }

    /**
     * Check if this is a business address (has company name)
     *
     * @return bool True if this appears to be a business address
     */
    public function is_business(): bool
    {
        return !empty($this->company());
    }

    /**
     * Check if this is a residential address (no company name)
     *
     * @return bool True if this appears to be a residential address
     */
    public function is_residential(): bool
    {
        return !$this->is_business();
    }

    /**
     * Check if the address appears complete
     *
     * @return bool True if address has all required components
     */
    public function is_complete(): bool
    {
        return !empty($this->address1()) &&
               !empty($this->city()) &&
               !empty($this->country_code());
    }

    /**
     * Check if the address is in a specific country
     *
     * @param string $countryCode Two-letter country code
     * @return bool True if address is in the specified country
     */
    public function is_in_country(string $countryCode): bool
    {
        return strtoupper($this->country_code()) === strtoupper($countryCode);
    }

    /**
     * Check if the address is in a specific province/state
     *
     * @param string $provinceCode Province/state code
     * @return bool True if address is in the specified province/state
     */
    public function is_in_province(string $provinceCode): bool
    {
        return strtoupper($this->province_code()) === strtoupper($provinceCode);
    }

    /**
     * Get distance to another address (if coordinates available)
     *
     * @param Address $otherAddress Another address to compare
     * @return float|null Distance in kilometers, null if coordinates unavailable
     */
    public function distance_to(Address $otherAddress): ?float
    {
        if (!$this->has_coordinates() || !$otherAddress->has_coordinates()) {
            return null;
        }

        return $this->calculate_distance(
            $this->latitude(),
            $this->longitude(),
            $otherAddress->latitude(),
            $otherAddress->longitude()
        );
    }

    /**
     * Check if the address has geographic coordinates
     *
     * @return bool True if latitude and longitude are available
     */
    public function has_coordinates(): bool
    {
        return !empty($this->_address->latitude) && !empty($this->_address->longitude);
    }

    /**
     * Get the latitude coordinate
     *
     * @return float|null Latitude or null if not available
     */
    public function latitude(): ?float
    {
        return isset($this->_address->latitude) ? (float) $this->_address->latitude : null;
    }

    /**
     * Get the longitude coordinate
     *
     * @return float|null Longitude or null if not available
     */
    public function longitude(): ?float
    {
        return isset($this->_address->longitude) ? (float) $this->_address->longitude : null;
    }

    /**
     * Get Google Maps URL for this address
     *
     * @return string Google Maps URL
     */
    public function google_maps_url(): string
    {
        $address = urlencode($this->single_line());
        return "https://www.google.com/maps/search/?api=1&query={$address}";
    }

    /**
     * Get map embed URL for this address
     *
     * @return string Map embed URL
     */
    public function map_embed_url(): string
    {
        $address = urlencode($this->single_line());
        return "https://www.google.com/maps/embed/v1/place?key=&q={$address}";
    }

    /**
     * Get address validation status
     *
     * @return string Validation status (valid, invalid, partial, unknown)
     */
    public function validation_status(): string
    {
        if (isset($this->_address->validation_status)) {
            return (string) $this->_address->validation_status;
        }

        // Basic validation logic
        if ($this->is_complete()) {
            return static::VALIDATION_VALID;
        } elseif (!empty($this->address1()) || !empty($this->city())) {
            return static::VALIDATION_PARTIAL;
        }

        return static::VALIDATION_UNKNOWN;
    }

    /**
     * Check if the address is validated
     *
     * @return bool True if address is validated
     */
    public function is_validated(): bool
    {
        return $this->validation_status() === static::VALIDATION_VALID;
    }

    /**
     * Get CSS class for address styling based on type and status
     *
     * @return string CSS class string
     */
    public function css_class(): string
    {
        $classes = ['address'];

        // Add type class
        $classes[] = 'address-' . str_replace('_', '-', $this->address_type());

        // Add status classes
        if ($this->is_default()) {
            $classes[] = 'address-default';
        }

        if ($this->is_business()) {
            $classes[] = 'address-business';
        } else {
            $classes[] = 'address-residential';
        }

        if ($this->is_validated()) {
            $classes[] = 'address-validated';
        }

        if (!$this->is_complete()) {
            $classes[] = 'address-incomplete';
        }

        return implode(' ', $classes);
    }

    /**
     * Get timezone for this address based on location
     *
     * @return string|null Timezone identifier or null if not determinable
     */
    public function timezone(): ?string
    {
        // Basic timezone mapping by country
        $timezoneMap = [
            'US' => 'America/New_York',
            'CA' => 'America/Toronto',
            'GB' => 'Europe/London',
            'DE' => 'Europe/Berlin',
            'FR' => 'Europe/Paris',
            'AU' => 'Australia/Sydney',
            'JP' => 'Asia/Tokyo',
        ];

        return $timezoneMap[$this->country_code()] ?? null;
    }



    /**
     * Convert address to array for API responses
     *
     * @return array Address data as associative array
     */
    public function to_array(): array
    {
        return [
            'address1' => $this->address1(),
            'address2' => $this->address2(),
            'city' => $this->city(),
            'company' => $this->company(),
            'country' => $this->country(),
            'country_code' => $this->country_code(),
            'first_name' => $this->first_name(),
            'last_name' => $this->last_name(),
            'name' => $this->name(),
            'phone' => $this->phone(),
            'province' => $this->province(),
            'province_code' => $this->province_code(),
            'zip' => $this->zip(),
            'formatted_address' => $this->formatted_address(),
            'is_default' => $this->is_default(),
            'is_business' => $this->is_business(),
            'address_type' => $this->address_type(),
            'validation_status' => $this->validation_status(),
        ];
    }

    /**
     * Calculate distance between two points using Haversine formula
     *
     * @param float $lat1 Latitude of first point
     * @param float $lon1 Longitude of first point
     * @param float $lat2 Latitude of second point
     * @param float $lon2 Longitude of second point
     * @return float Distance in kilometers
     */
    protected function calculate_distance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371; // Earth radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLon / 2) * sin($dLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Legacy method aliases for backward compatibility
     */

    /**
     * @deprecated Use address1() instead
     */
    public function street_name(): string
    {
        return $this->address1();
    }

    /**
     * @deprecated Use zip() instead
     */
    public function postal_code(): string
    {
        return $this->zip();
    }

    /**
     * @deprecated Use province() instead
     */
    public function state_name(): string
    {
        return $this->province();
    }

    /**
     * @deprecated Use province_code() instead
     */
    public function state_code(): string
    {
        return $this->province_code();
    }
}
