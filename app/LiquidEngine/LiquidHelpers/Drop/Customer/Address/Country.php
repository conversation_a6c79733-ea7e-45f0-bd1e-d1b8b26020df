<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Customer\Address;

use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use Omniship\Address\Country as OmnishipCountry;

/**
 * Represents a country in address components with comprehensive
 * Shopify compatibility and advanced country intelligence features.
 * 
 * Provides complete country object functionality including:
 * - Standard Shopify country properties and methods
 * - Geographic information and continental data
 * - Economic indicators and development data
 * - Cultural features (languages, currencies, calling codes)
 * - Timezone and regional information
 * - Political and administrative details
 * - Multi-language name support
 * - Business intelligence and demographics
 * 
 * <AUTHOR> LLC
 * @copyright CloudCart LLC
 * @since 2.0.0 Enhanced with full Shopify compatibility
 */
class Country extends AbstractDrop
{
    /**
     * Development level constants
     */
    public const DEVELOPMENT_DEVELOPED = 'developed';
    public const DEVELOPMENT_DEVELOPING = 'developing';
    public const DEVELOPMENT_LEAST_DEVELOPED = 'least_developed';

    /**
     * Economic classification constants
     */
    public const ECONOMY_HIGH_INCOME = 'high_income';
    public const ECONOMY_UPPER_MIDDLE = 'upper_middle_income';
    public const ECONOMY_LOWER_MIDDLE = 'lower_middle_income';
    public const ECONOMY_LOW_INCOME = 'low_income';

    /**
     * Continental classification constants
     */
    public const CONTINENT_EUROPE = 'Europe';
    public const CONTINENT_ASIA = 'Asia';
    public const CONTINENT_AFRICA = 'Africa';
    public const CONTINENT_NORTH_AMERICA = 'North America';
    public const CONTINENT_SOUTH_AMERICA = 'South America';
    public const CONTINENT_OCEANIA = 'Oceania';
    public const CONTINENT_ANTARCTICA = 'Antarctica';

    /**
     * Regional classification constants
     */
    public const REGION_WESTERN_EUROPE = 'Western Europe';
    public const REGION_EASTERN_EUROPE = 'Eastern Europe';
    public const REGION_NORTHERN_EUROPE = 'Northern Europe';
    public const REGION_SOUTHERN_EUROPE = 'Southern Europe';
    public const REGION_SOUTHEAST_ASIA = 'Southeast Asia';
    public const REGION_EAST_ASIA = 'East Asia';
    public const REGION_MIDDLE_EAST = 'Middle East';
    public const REGION_NORTH_AFRICA = 'North Africa';

    /**
     * @var OmnishipCountry $_country
     */
    protected \Omniship\Address\Country $_country;

    /**
     * @param \Omniship\Address\Country $country
     */
    public function __construct(OmnishipCountry $country)
    {
        $this->_country = $country;
    }

    /**
     * Get the country ID
     * Shopify compatibility: Standard id property
     * 
     * @return int|string|null The country identifier
     */
    public function id(): int|string|null
    {
        return $this->_country->getId();
    }

    /**
     * Get the country name
     * Shopify compatibility: Standard name property
     * 
     * @return string|null The country name
     */
    public function name(): ?string
    {
        return $this->_country->getName();
    }

    /**
     * Get the country title (alias for name)
     * Shopify compatibility: Standard title property
     * 
     * @return string|null The country title
     */
    public function title(): ?string
    {
        return $this->name();
    }

    /**
     * Get the country value (alias for name)
     * Shopify compatibility: Standard value property
     * 
     * @return string|null The country value
     */
    public function value(): ?string
    {
        return $this->name();
    }

    /**
     * Get the ISO 3166-1 alpha-2 country code
     * Shopify compatibility: Standard code property
     * 
     * @return string|null Two-letter country code (e.g., 'US', 'GB', 'BG')
     */
    public function iso2(): ?string
    {
        return $this->_country->getIso2();
    }

    /**
     * Get the ISO 3166-1 alpha-3 country code
     * 
     * @return string|null Three-letter country code (e.g., 'USA', 'GBR', 'BGR')
     */
    public function iso3(): ?string
    {
        return $this->_country->getIso3();
    }

    /**
     * Get the country code (alias for iso2)
     * Shopify compatibility: Standard country_code property
     * 
     * @return string|null Two-letter country code
     */
    public function country_code(): ?string
    {
        return $this->iso2();
    }

    /**
     * Get the country code (alternative alias)
     * 
     * @return string|null Two-letter country code
     */
    public function code(): ?string
    {
        return $this->iso2();
    }

    /**
     * Get the display name for the country
     * 
     * @return string The formatted display name
     */
    public function display_name(): string
    {
        return $this->name() ?? 'Unknown Country';
    }

    /**
     * Get the continent for this country
     * 
     * @return string|null The continent name
     */
    public function continent(): ?string
    {
        if (method_exists($this->_country, 'getContinent')) {
            return $this->_country->getContinent();
        }
        
        // Fallback continent mapping based on ISO2 code
        $continentMap = [
            // Europe
            'AD', 'AL', 'AT', 'BA', 'BE', 'BG', 'BY', 'CH', 'CZ', 'DE', 'DK', 'EE', 'ES', 'FI', 'FR', 'GB', 'GR', 'HR', 'HU', 'IE', 'IS', 'IT', 'LI', 'LT', 'LU', 'LV', 'MC', 'MD', 'ME', 'MK', 'MT', 'NL', 'NO', 'PL', 'PT', 'RO', 'RS', 'RU', 'SE', 'SI', 'SK', 'SM', 'UA', 'VA' => static::CONTINENT_EUROPE,
            
            // Asia
            'AF', 'AM', 'AZ', 'BD', 'BH', 'BN', 'BT', 'CN', 'GE', 'HK', 'ID', 'IL', 'IN', 'IQ', 'IR', 'JO', 'JP', 'KG', 'KH', 'KP', 'KR', 'KW', 'KZ', 'LA', 'LB', 'LK', 'MM', 'MN', 'MO', 'MV', 'MY', 'NP', 'OM', 'PH', 'PK', 'PS', 'QA', 'SA', 'SG', 'SY', 'TH', 'TJ', 'TL', 'TM', 'TR', 'TW', 'UZ', 'VN', 'YE' => static::CONTINENT_ASIA,
            
            // North America
            'AG', 'BS', 'BB', 'BZ', 'CA', 'CR', 'CU', 'DM', 'DO', 'GD', 'GT', 'HN', 'HT', 'JM', 'KN', 'LC', 'MX', 'NI', 'PA', 'SV', 'TT', 'US', 'VC' => static::CONTINENT_NORTH_AMERICA,
            
            // South America
            'AR', 'BO', 'BR', 'CL', 'CO', 'EC', 'FK', 'GF', 'GY', 'PE', 'PY', 'SR', 'UY', 'VE' => static::CONTINENT_SOUTH_AMERICA,
            
            // Africa
            'AO', 'BF', 'BI', 'BJ', 'BW', 'CD', 'CF', 'CG', 'CI', 'CM', 'CV', 'DJ', 'DZ', 'EG', 'EH', 'ER', 'ET', 'GA', 'GH', 'GM', 'GN', 'GQ', 'GW', 'KE', 'KM', 'LR', 'LS', 'LY', 'MA', 'MG', 'ML', 'MR', 'MU', 'MW', 'MZ', 'NA', 'NE', 'NG', 'RW', 'SC', 'SD', 'SL', 'SN', 'SO', 'SS', 'ST', 'SZ', 'TD', 'TG', 'TN', 'TZ', 'UG', 'ZA', 'ZM', 'ZW' => static::CONTINENT_AFRICA,
            
            // Oceania
            'AS', 'AU', 'CK', 'FJ', 'FM', 'GU', 'KI', 'MH', 'MP', 'NC', 'NR', 'NU', 'NZ', 'PF', 'PG', 'PN', 'PW', 'SB', 'TK', 'TO', 'TV', 'VU', 'WF', 'WS' => static::CONTINENT_OCEANIA,
        ];
        
        $iso2 = $this->iso2();
        foreach ($continentMap as $codes => $continent) {
            if (in_array($iso2, explode(', ', $codes))) {
                return $continent;
            }
        }
        
        return null;
    }

    /**
     * Get the geographic region for this country
     * 
     * @return string|null The region name
     */
    public function region(): ?string
    {
        if (method_exists($this->_country, 'getRegion')) {
            return $this->_country->getRegion();
        }
        
        // Basic region mapping
        $iso2 = $this->iso2();
        $regionMap = [
            'Western Europe' => ['AT', 'BE', 'CH', 'DE', 'FR', 'LI', 'LU', 'MC', 'NL'],
            'Northern Europe' => ['DK', 'EE', 'FI', 'IE', 'IS', 'LV', 'LT', 'NO', 'SE', 'GB'],
            'Eastern Europe' => ['BY', 'BG', 'CZ', 'HU', 'MD', 'PL', 'RO', 'RU', 'SK', 'UA'],
            'Southern Europe' => ['AD', 'AL', 'BA', 'ES', 'GR', 'HR', 'IT', 'ME', 'MK', 'MT', 'PT', 'RS', 'SI', 'SM', 'VA'],
            'Southeast Asia' => ['BN', 'KH', 'ID', 'LA', 'MY', 'MM', 'PH', 'SG', 'TH', 'TL', 'VN'],
            'East Asia' => ['CN', 'HK', 'JP', 'KP', 'KR', 'MN', 'MO', 'TW'],
            'Middle East' => ['AE', 'BH', 'CY', 'IQ', 'IR', 'IL', 'JO', 'KW', 'LB', 'OM', 'PS', 'QA', 'SA', 'SY', 'TR', 'YE'],
        ];
        
        foreach ($regionMap as $region => $codes) {
            if (in_array($iso2, $codes)) {
                return $region;
            }
        }
        
        return $this->continent();
    }

    /**
     * Get the primary currency for this country
     * 
     * @return string|null Currency code (e.g., 'USD', 'EUR', 'BGN')
     */
    public function currency(): ?string
    {
        if (method_exists($this->_country, 'getCurrency')) {
            return $this->_country->getCurrency();
        }
        
        // Basic currency mapping
        $currencyMap = [
            'USD' => ['US', 'EC', 'SV', 'MH', 'FM', 'PW', 'TL', 'ZW'],
            'EUR' => ['AD', 'AT', 'BE', 'CY', 'DE', 'EE', 'ES', 'FI', 'FR', 'GR', 'IE', 'IT', 'LT', 'LU', 'LV', 'MC', 'ME', 'MT', 'NL', 'PT', 'SI', 'SK', 'SM', 'VA'],
            'BGN' => ['BG'],
            'GBP' => ['GB'],
            'CHF' => ['CH', 'LI'],
            'CAD' => ['CA'],
            'AUD' => ['AU', 'KI', 'NR', 'TV'],
            'JPY' => ['JP'],
            'CNY' => ['CN'],
            'RUB' => ['RU'],
            'INR' => ['IN', 'BT'],
            'BRL' => ['BR'],
            'MXN' => ['MX'],
        ];
        
        $iso2 = $this->iso2();
        foreach ($currencyMap as $currency => $codes) {
            if (in_array($iso2, $codes)) {
                return $currency;
            }
        }
        
        return null;
    }

    /**
     * Get the international calling code for this country
     * 
     * @return string|null The calling code (e.g., '+1', '+359', '+44')
     */
    public function calling_code(): ?string
    {
        if (method_exists($this->_country, 'getCallingCode')) {
            return $this->_country->getCallingCode();
        }
        
        // Basic calling code mapping
        $callingCodeMap = [
            '+1' => ['US', 'CA', 'AG', 'BS', 'BB', 'BZ', 'DM', 'DO', 'GD', 'GU', 'HT', 'JM', 'KN', 'KY', 'LC', 'MP', 'MS', 'PR', 'SX', 'TC', 'TT', 'VC', 'VG', 'VI'],
            '+7' => ['RU', 'KZ'],
            '+20' => ['EG'],
            '+27' => ['ZA'],
            '+30' => ['GR'],
            '+31' => ['NL'],
            '+32' => ['BE'],
            '+33' => ['FR'],
            '+34' => ['ES'],
            '+36' => ['HU'],
            '+39' => ['IT', 'SM', 'VA'],
            '+40' => ['RO'],
            '+41' => ['CH'],
            '+43' => ['AT'],
            '+44' => ['GB'],
            '+45' => ['DK'],
            '+46' => ['SE'],
            '+47' => ['NO'],
            '+48' => ['PL'],
            '+49' => ['DE'],
            '+52' => ['MX'],
            '+53' => ['CU'],
            '+54' => ['AR'],
            '+55' => ['BR'],
            '+56' => ['CL'],
            '+57' => ['CO'],
            '+58' => ['VE'],
            '+60' => ['MY'],
            '+61' => ['AU'],
            '+62' => ['ID'],
            '+63' => ['PH'],
            '+64' => ['NZ'],
            '+65' => ['SG'],
            '+66' => ['TH'],
            '+81' => ['JP'],
            '+82' => ['KR'],
            '+84' => ['VN'],
            '+86' => ['CN'],
            '+90' => ['TR'],
            '+91' => ['IN'],
            '+92' => ['PK'],
            '+93' => ['AF'],
            '+94' => ['LK'],
            '+95' => ['MM'],
            '+98' => ['IR'],
            '+212' => ['MA'],
            '+213' => ['DZ'],
            '+216' => ['TN'],
            '+218' => ['LY'],
            '+220' => ['GM'],
            '+221' => ['SN'],
            '+222' => ['MR'],
            '+223' => ['ML'],
            '+224' => ['GN'],
            '+225' => ['CI'],
            '+226' => ['BF'],
            '+227' => ['NE'],
            '+228' => ['TG'],
            '+229' => ['BJ'],
            '+230' => ['MU'],
            '+231' => ['LR'],
            '+232' => ['SL'],
            '+233' => ['GH'],
            '+234' => ['NG'],
            '+235' => ['TD'],
            '+236' => ['CF'],
            '+237' => ['CM'],
            '+238' => ['CV'],
            '+239' => ['ST'],
            '+240' => ['GQ'],
            '+241' => ['GA'],
            '+242' => ['CG'],
            '+243' => ['CD'],
            '+244' => ['AO'],
            '+245' => ['GW'],
            '+246' => ['IO'],
            '+248' => ['SC'],
            '+249' => ['SD'],
            '+250' => ['RW'],
            '+251' => ['ET'],
            '+252' => ['SO'],
            '+253' => ['DJ'],
            '+254' => ['KE'],
            '+255' => ['TZ'],
            '+256' => ['UG'],
            '+257' => ['BI'],
            '+258' => ['MZ'],
            '+260' => ['ZM'],
            '+261' => ['MG'],
            '+262' => ['RE', 'YT'],
            '+263' => ['ZW'],
            '+264' => ['NA'],
            '+265' => ['MW'],
            '+266' => ['LS'],
            '+267' => ['BW'],
            '+268' => ['SZ'],
            '+269' => ['KM'],
            '+290' => ['SH'],
            '+291' => ['ER'],
            '+297' => ['AW'],
            '+298' => ['FO'],
            '+299' => ['GL'],
            '+350' => ['GI'],
            '+351' => ['PT'],
            '+352' => ['LU'],
            '+353' => ['IE'],
            '+354' => ['IS'],
            '+355' => ['AL'],
            '+356' => ['MT'],
            '+357' => ['CY'],
            '+358' => ['FI'],
            '+359' => ['BG'],
            '+370' => ['LT'],
            '+371' => ['LV'],
            '+372' => ['EE'],
            '+373' => ['MD'],
            '+374' => ['AM'],
            '+375' => ['BY'],
            '+376' => ['AD'],
            '+377' => ['MC'],
            '+378' => ['SM'],
            '+380' => ['UA'],
            '+381' => ['RS'],
            '+382' => ['ME'],
            '+383' => ['XK'],
            '+385' => ['HR'],
            '+386' => ['SI'],
            '+387' => ['BA'],
            '+389' => ['MK'],
            '+420' => ['CZ'],
            '+421' => ['SK'],
            '+423' => ['LI'],
            '+500' => ['FK'],
            '+501' => ['BZ'],
            '+502' => ['GT'],
            '+503' => ['SV'],
            '+504' => ['HN'],
            '+505' => ['NI'],
            '+506' => ['CR'],
            '+507' => ['PA'],
            '+508' => ['PM'],
            '+509' => ['HT'],
            '+590' => ['GP', 'BL', 'MF'],
            '+591' => ['BO'],
            '+592' => ['GY'],
            '+593' => ['EC'],
            '+594' => ['GF'],
            '+595' => ['PY'],
            '+596' => ['MQ'],
            '+597' => ['SR'],
            '+598' => ['UY'],
            '+599' => ['CW', 'BQ'],
            '+670' => ['TL'],
            '+672' => ['NF'],
            '+673' => ['BN'],
            '+674' => ['NR'],
            '+675' => ['PG'],
            '+676' => ['TO'],
            '+677' => ['SB'],
            '+678' => ['VU'],
            '+679' => ['FJ'],
            '+680' => ['PW'],
            '+681' => ['WF'],
            '+682' => ['CK'],
            '+683' => ['NU'],
            '+684' => ['AS'],
            '+685' => ['WS'],
            '+686' => ['KI'],
            '+687' => ['NC'],
            '+688' => ['TV'],
            '+689' => ['PF'],
            '+690' => ['TK'],
            '+691' => ['FM'],
            '+692' => ['MH'],
            '+850' => ['KP'],
            '+852' => ['HK'],
            '+853' => ['MO'],
            '+855' => ['KH'],
            '+856' => ['LA'],
            '+880' => ['BD'],
            '+886' => ['TW'],
            '+960' => ['MV'],
            '+961' => ['LB'],
            '+962' => ['JO'],
            '+963' => ['SY'],
            '+964' => ['IQ'],
            '+965' => ['KW'],
            '+966' => ['SA'],
            '+967' => ['YE'],
            '+968' => ['OM'],
            '+970' => ['PS'],
            '+971' => ['AE'],
            '+972' => ['IL'],
            '+973' => ['BH'],
            '+974' => ['QA'],
            '+975' => ['BT'],
            '+976' => ['MN'],
            '+977' => ['NP'],
            '+992' => ['TJ'],
            '+993' => ['TM'],
            '+994' => ['AZ'],
            '+995' => ['GE'],
            '+996' => ['KG'],
            '+998' => ['UZ'],
        ];
        
        $iso2 = $this->iso2();
        foreach ($callingCodeMap as $code => $codes) {
            if (in_array($iso2, $codes)) {
                return $code;
            }
        }
        
        return null;
    }

    /**
     * Get the primary timezone for this country
     * 
     * @return string|null The timezone identifier
     */
    public function timezone(): ?string
    {
        if (method_exists($this->_country, 'getTimezone')) {
            return $this->_country->getTimezone();
        }
        
        // Basic timezone mapping (primary timezone for each country)
        $timezoneMap = [
            'US' => 'America/New_York',
            'CA' => 'America/Toronto',
            'GB' => 'Europe/London',
            'DE' => 'Europe/Berlin',
            'FR' => 'Europe/Paris',
            'IT' => 'Europe/Rome',
            'ES' => 'Europe/Madrid',
            'RU' => 'Europe/Moscow',
            'CN' => 'Asia/Shanghai',
            'JP' => 'Asia/Tokyo',
            'IN' => 'Asia/Kolkata',
            'AU' => 'Australia/Sydney',
            'BR' => 'America/Sao_Paulo',
            'MX' => 'America/Mexico_City',
            'AR' => 'America/Argentina/Buenos_Aires',
            'ZA' => 'Africa/Johannesburg',
            'EG' => 'Africa/Cairo',
            'BG' => 'Europe/Sofia',
            'GR' => 'Europe/Athens',
            'TR' => 'Europe/Istanbul',
        ];
        
        return $timezoneMap[$this->iso2()] ?? null;
    }

    /**
     * Get the population of the country
     * 
     * @return int|null The population count
     */
    public function population(): ?int
    {
        if (method_exists($this->_country, 'getPopulation')) {
            return $this->_country->getPopulation();
        }
        
        return null;
    }

    /**
     * Get formatted population with thousands separators
     * 
     * @return string|null Formatted population
     */
    public function population_formatted(): ?string
    {
        $population = $this->population();
        return $population ? number_format($population) : null;
    }

    /**
     * Get the capital city of the country
     * 
     * @return string|null The capital city name
     */
    public function capital(): ?string
    {
        if (method_exists($this->_country, 'getCapital')) {
            return $this->_country->getCapital();
        }
        
        // Basic capital mapping
        $capitalMap = [
            'US' => 'Washington D.C.',
            'CA' => 'Ottawa',
            'GB' => 'London',
            'DE' => 'Berlin',
            'FR' => 'Paris',
            'IT' => 'Rome',
            'ES' => 'Madrid',
            'RU' => 'Moscow',
            'CN' => 'Beijing',
            'JP' => 'Tokyo',
            'IN' => 'New Delhi',
            'AU' => 'Canberra',
            'BR' => 'Brasília',
            'MX' => 'Mexico City',
            'AR' => 'Buenos Aires',
            'ZA' => 'Cape Town',
            'EG' => 'Cairo',
            'BG' => 'Sofia',
            'GR' => 'Athens',
            'TR' => 'Ankara',
            'NL' => 'Amsterdam',
            'BE' => 'Brussels',
            'CH' => 'Bern',
            'AT' => 'Vienna',
            'SE' => 'Stockholm',
            'NO' => 'Oslo',
            'DK' => 'Copenhagen',
            'FI' => 'Helsinki',
            'PL' => 'Warsaw',
            'CZ' => 'Prague',
            'HU' => 'Budapest',
            'PT' => 'Lisbon',
        ];
        
        return $capitalMap[$this->iso2()] ?? null;
    }

    /**
     * Get the official languages of the country
     * 
     * @return array Array of language codes
     */
    public function languages(): array
    {
        if (method_exists($this->_country, 'getLanguages')) {
            return $this->_country->getLanguages();
        }
        
        // Basic language mapping
        $languageMap = [
            'US' => ['en'],
            'CA' => ['en', 'fr'],
            'GB' => ['en'],
            'DE' => ['de'],
            'FR' => ['fr'],
            'IT' => ['it'],
            'ES' => ['es'],
            'RU' => ['ru'],
            'CN' => ['zh'],
            'JP' => ['ja'],
            'IN' => ['hi', 'en'],
            'AU' => ['en'],
            'BR' => ['pt'],
            'MX' => ['es'],
            'AR' => ['es'],
            'ZA' => ['af', 'en', 'zu', 'xh'],
            'EG' => ['ar'],
            'BG' => ['bg'],
            'GR' => ['el'],
            'TR' => ['tr'],
            'NL' => ['nl'],
            'BE' => ['nl', 'fr', 'de'],
            'CH' => ['de', 'fr', 'it', 'rm'],
            'AT' => ['de'],
            'SE' => ['sv'],
            'NO' => ['no'],
            'DK' => ['da'],
            'FI' => ['fi', 'sv'],
            'PL' => ['pl'],
            'CZ' => ['cs'],
            'HU' => ['hu'],
            'PT' => ['pt'],
        ];
        
        return $languageMap[$this->iso2()] ?? [];
    }

    /**
     * Get the primary language of the country
     * 
     * @return string|null Primary language code
     */
    public function primary_language(): ?string
    {
        $languages = $this->languages();
        return !empty($languages) ? $languages[0] : null;
    }

    /**
     * Get the development level classification
     * 
     * @return string Development level classification
     */
    public function development_level(): string
    {
        // Basic development classification
        $developedCountries = ['US', 'CA', 'GB', 'DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'CH', 'AT', 'SE', 'NO', 'DK', 'FI', 'AU', 'NZ', 'JP', 'KR', 'SG', 'HK', 'LU', 'IE', 'IS'];
        $leastDevelopedCountries = ['AF', 'BD', 'BF', 'BI', 'BJ', 'BTN', 'CAF', 'TCD', 'COM', 'COD', 'DJI', 'ERI', 'ETH', 'GMB', 'GIN', 'GNB', 'HTI', 'KHM', 'KIR', 'LAO', 'LBR', 'LSO', 'MDG', 'MWI', 'MLI', 'MMR', 'MOZ', 'MRT', 'NPL', 'NER', 'RWA', 'SEN', 'SLE', 'SLB', 'SOM', 'SSD', 'STP', 'TLS', 'TGO', 'TUV', 'TZA', 'UGA', 'VUT', 'YEM', 'ZMB'];
        
        $iso3 = $this->iso3();
        $iso2 = $this->iso2();
        
        if (in_array($iso2, $developedCountries)) {
            return static::DEVELOPMENT_DEVELOPED;
        } elseif (in_array($iso3, $leastDevelopedCountries)) {
            return static::DEVELOPMENT_LEAST_DEVELOPED;
        } else {
            return static::DEVELOPMENT_DEVELOPING;
        }
    }

    /**
     * Get the economic classification
     * 
     * @return string Economic classification
     */
    public function economic_classification(): string
    {
        // Based on World Bank income classifications
        $highIncome = ['US', 'CA', 'GB', 'DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'CH', 'AT', 'SE', 'NO', 'DK', 'FI', 'AU', 'NZ', 'JP', 'KR', 'SG', 'HK', 'AE', 'SA', 'KW', 'QA', 'BH', 'OM'];
        $lowIncome = ['AF', 'BF', 'BI', 'CAF', 'TCD', 'COD', 'ERI', 'ETH', 'GMB', 'GIN', 'GNB', 'LBR', 'MDG', 'MWI', 'MLI', 'MOZ', 'NER', 'RWA', 'SLE', 'SOM', 'SSD', 'TGO', 'UGA', 'YEM'];
        
        $iso2 = $this->iso2();
        
        if (in_array($iso2, $highIncome)) {
            return static::ECONOMY_HIGH_INCOME;
        } elseif (in_array($iso2, $lowIncome)) {
            return static::ECONOMY_LOW_INCOME;
        } else {
            // Most others fall into middle income categories
            $upperMiddle = ['BR', 'RU', 'CN', 'MX', 'AR', 'TR', 'ZA', 'MY', 'TH', 'BG', 'RO', 'HR'];
            return in_array($iso2, $upperMiddle) ? static::ECONOMY_UPPER_MIDDLE : static::ECONOMY_LOWER_MIDDLE;
        }
    }

    /**
     * Get URL handle for the country (SEO-friendly)
     * 
     * @return string URL-safe handle
     */
    public function handle(): string
    {
        $name = $this->name() ?? 'unknown-country';
        return \Illuminate\Support\Str::slug($name);
    }

    /**
     * Check if the country is in the European Union
     * 
     * @return bool True if EU member
     */
    public function is_eu_member(): bool
    {
        $euMembers = ['AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR', 'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL', 'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE'];
        return in_array($this->iso2(), $euMembers);
    }

    /**
     * Check if the country uses the Euro currency
     * 
     * @return bool True if Eurozone member
     */
    public function is_eurozone(): bool
    {
        return $this->currency() === 'EUR';
    }

    /**
     * Check if the country is landlocked
     * 
     * @return bool True if landlocked
     */
    public function is_landlocked(): bool
    {
        $landlocked = ['AD', 'AF', 'AM', 'AT', 'AZ', 'BE', 'BF', 'BG', 'BH', 'BI', 'BT', 'BO', 'BW', 'BY', 'CAF', 'CH', 'CZ', 'ET', 'HU', 'KG', 'KZ', 'LA', 'LI', 'LU', 'LV', 'MC', 'MD', 'MK', 'ML', 'MN', 'NE', 'NP', 'PY', 'RW', 'SJ', 'SK', 'SM', 'SS', 'SZ', 'TCD', 'TJ', 'TM', 'UG', 'UZ', 'VA', 'ZM', 'ZW'];
        return in_array($this->iso2(), $landlocked);
    }

    /**
     * Check if the country is an island nation
     * 
     * @return bool True if island nation
     */
    public function is_island(): bool
    {
        $islands = ['AU', 'BB', 'BS', 'CU', 'CY', 'DM', 'FJ', 'GB', 'GD', 'IC', 'IE', 'IS', 'JM', 'JP', 'KI', 'KN', 'LC', 'LK', 'MH', 'MT', 'MU', 'MV', 'NR', 'NZ', 'PH', 'PW', 'SB', 'SC', 'SG', 'ST', 'TO', 'TT', 'TV', 'VC', 'VU', 'WS'];
        return in_array($this->iso2(), $islands);
    }

    /**
     * Get Google Maps URL for this country
     * 
     * @return string Google Maps URL
     */
    public function google_maps_url(): string
    {
        $countryName = urlencode($this->display_name());
        return "https://www.google.com/maps/search/?api=1&query={$countryName}";
    }

    /**
     * Get Wikipedia URL for this country
     * 
     * @param string $language Language code for Wikipedia (default: 'en')
     * @return string Wikipedia URL
     */
    public function wikipedia_url(string $language = 'en'): string
    {
        $countryName = str_replace(' ', '_', $this->display_name());
        return "https://{$language}.wikipedia.org/wiki/{$countryName}";
    }

    /**
     * Get CSS class for country styling based on attributes
     * 
     * @return string CSS class string
     */
    public function css_class(): string
    {
        $classes = ['country'];
        
        // Add continent class
        if ($continent = $this->continent()) {
            $classes[] = 'continent-' . \Illuminate\Support\Str::slug($continent);
        }
        
        // Add region class
        if ($region = $this->region()) {
            $classes[] = 'region-' . \Illuminate\Support\Str::slug($region);
        }
        
        // Add development level class
        $classes[] = 'development-' . str_replace('_', '-', $this->development_level());
        
        // Add economic class
        $classes[] = 'economy-' . str_replace('_', '-', $this->economic_classification());
        
        // Add special classes
        if ($this->is_eu_member()) {
            $classes[] = 'country-eu';
        }
        
        if ($this->is_eurozone()) {
            $classes[] = 'country-eurozone';
        }
        
        if ($this->is_landlocked()) {
            $classes[] = 'country-landlocked';
        }
        
        if ($this->is_island()) {
            $classes[] = 'country-island';
        }
        
        return implode(' ', $classes);
    }

    /**
     * Get the collection of items as a plain array
     *
     * @return array Complete country data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'title' => $this->title(),
            'value' => $this->value(),
            'display_name' => $this->display_name(),
            'iso2' => $this->iso2(),
            'iso3' => $this->iso3(),
            'country_code' => $this->country_code(),
            'code' => $this->code(),
            'continent' => $this->continent(),
            'region' => $this->region(),
            'currency' => $this->currency(),
            'calling_code' => $this->calling_code(),
            'timezone' => $this->timezone(),
            'population' => $this->population(),
            'population_formatted' => $this->population_formatted(),
            'capital' => $this->capital(),
            'languages' => $this->languages(),
            'primary_language' => $this->primary_language(),
            'development_level' => $this->development_level(),
            'economic_classification' => $this->economic_classification(),
            'handle' => $this->handle(),
            'is_eu_member' => $this->is_eu_member(),
            'is_eurozone' => $this->is_eurozone(),
            'is_landlocked' => $this->is_landlocked(),
            'is_island' => $this->is_island(),
            'google_maps_url' => $this->google_maps_url(),
            'css_class' => $this->css_class(),
        ];
    }

    /**
     * String representation of the country
     * 
     * @return string Country name
     */
    public function __toString(): string
    {
        return $this->display_name();
    }
}
