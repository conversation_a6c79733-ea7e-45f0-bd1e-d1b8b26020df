<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Customer\Address;

use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use Omniship\Address\Quarter as OmnishipQuarter;

class Quarter extends AbstractDrop
{
    /**
     * @var OmnishipQuarter|array $_quarter
     */
    protected $_quarter;

    /**
     * @param OmnishipQuarter|array $quarter
     */
    public function __construct($quarter)
    {
        $this->_quarter = $quarter;
    }

    /**
     * Get the unique quarter ID
     * @return int|string|null
     */
    public function id(): int|string|null
    {
        return is_object($this->_quarter) && method_exists($this->_quarter, 'getId')
            ? $this->_quarter->getId()
            : ($this->_quarter['id'] ?? null);
    }

    /**
     * Get the quarter name
     * @return string|null
     */
    public function name(): ?string
    {
        return is_object($this->_quarter) && method_exists($this->_quarter, 'getName')
            ? $this->_quarter->getName()
            : ($this->_quarter['name'] ?? null);
    }

    /** Shopify-compatible alias for name */
    public function title(): ?string { return $this->name(); }
    /** Value for form fields (ID or code) */
    public function value(): int|string|null { return $this->id(); }

    /**
     * Display name (with type or code if available)
     * @return string|null
     */
    public function display_name(): ?string
    {
        $name = $this->name();
        $type = $this->type();
        $code = $this->code();
        if ($type && $code) return "$name ($type, $code)";
        if ($type) return "$name ($type)";
        if ($code) return "$name ($code)";
        return $name;
    }

    /** Quarter code (if available) */
    public function code(): ?string {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'code')) return $this->_quarter->code;
        if (is_array($this->_quarter) && isset($this->_quarter['code'])) return $this->_quarter['code'];
        return null;
    }

    /** SEO-friendly handle for URLs */
    public function handle(): ?string {
        $name = $this->name();
        return $name ? strtolower(preg_replace('/[^a-z0-9]+/', '-', trim($name))) : null;
    }

    /** Quarter type/classification (e.g., district, neighborhood) */
    public function type(): ?string {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'type')) return $this->_quarter->type;
        if (is_array($this->_quarter) && isset($this->_quarter['type'])) return $this->_quarter['type'];
        return null;
    }

    /** City name (if available) */
    public function city(): ?string {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'city')) return $this->_quarter->city;
        if (is_array($this->_quarter) && isset($this->_quarter['city'])) return $this->_quarter['city'];
        return null;
    }

    /** State/region name (if available) */
    public function state(): ?string {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'state')) return $this->_quarter->state;
        if (is_array($this->_quarter) && isset($this->_quarter['state'])) return $this->_quarter['state'];
        return null;
    }

    /** Country name (if available) */
    public function country(): ?string {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'country')) return $this->_quarter->country;
        if (is_array($this->_quarter) && isset($this->_quarter['country'])) return $this->_quarter['country'];
        return null;
    }

    /** Postal code (if available) */
    public function post_code(): ?string {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'post_code')) return $this->_quarter->post_code;
        if (is_array($this->_quarter) && isset($this->_quarter['post_code'])) return $this->_quarter['post_code'];
        return null;
    }

    /** Latitude (if available) */
    public function latitude(): ?float {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'latitude')) return (float)$this->_quarter->latitude;
        if (is_array($this->_quarter) && isset($this->_quarter['latitude'])) return (float)$this->_quarter['latitude'];
        return null;
    }

    /** Longitude (if available) */
    public function longitude(): ?float {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'longitude')) return (float)$this->_quarter->longitude;
        if (is_array($this->_quarter) && isset($this->_quarter['longitude'])) return (float)$this->_quarter['longitude'];
        return null;
    }

    /** Google Maps URL (if coordinates available) */
    public function google_maps_url(): ?string {
        $lat = $this->latitude();
        $lng = $this->longitude();
        if ($lat && $lng) return "https://maps.google.com/?q={$lat},{$lng}";
        return null;
    }

    /**
     * Quarter type constants
     */
    public const TYPE_DISTRICT = 'district';
    public const TYPE_NEIGHBORHOOD = 'neighborhood';
    public const TYPE_SUBURB = 'suburb';
    public const TYPE_WARD = 'ward';
    public const TYPE_BLOCK = 'block';
    public const TYPE_SECTOR = 'sector';
    public const TYPE_ZONE = 'zone';
    public const TYPE_RESIDENTIAL = 'residential';
    public const TYPE_COMMERCIAL = 'commercial';
    public const TYPE_INDUSTRIAL = 'industrial';

    /**
     * Quarter status constants
     */
    public const STATUS_ACTIVE = 'active';
    public const STATUS_INACTIVE = 'inactive';
    public const STATUS_MAINTENANCE = 'maintenance';
    public const STATUS_DEPRECATED = 'deprecated';

    /**
     * Business hours constants
     */
    public const HOURS_24_7 = '24/7';
    public const HOURS_BUSINESS = 'business';
    public const HOURS_CUSTOM = 'custom';
    public const HOURS_CLOSED = 'closed';

    /**
     * Get the quarter operational status
     * @return string|null
     */
    public function status(): ?string
    {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'status')) return $this->_quarter->status;
        if (is_array($this->_quarter) && isset($this->_quarter['status'])) return $this->_quarter['status'];
        return null;
    }

    /**
     * Get structured business hours (if available)
     * @return array|null
     */
    public function business_hours(): ?array
    {
        $hours = null;
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'business_hours')) $hours = $this->_quarter->business_hours;
        if (is_array($this->_quarter) && isset($this->_quarter['business_hours'])) $hours = $this->_quarter['business_hours'];
        if (is_string($hours)) {
            $decoded = json_decode($hours, true);
            if (is_array($decoded)) return $decoded;
            return [$hours];
        }
        return is_array($hours) ? $hours : null;
    }

    /**
     * Is the quarter accessible (wheelchair, etc)?
     * @return bool|null
     */
    public function is_accessible(): ?bool
    {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'is_accessible')) return (bool)$this->_quarter->is_accessible;
        if (is_array($this->_quarter) && isset($this->_quarter['is_accessible'])) return (bool)$this->_quarter['is_accessible'];
        return null;
    }

    /**
     * Does the quarter have parking?
     * @return bool|null
     */
    public function has_parking(): ?bool
    {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'has_parking')) return (bool)$this->_quarter->has_parking;
        if (is_array($this->_quarter) && isset($this->_quarter['has_parking'])) return (bool)$this->_quarter['has_parking'];
        return null;
    }

    /**
     * Does the quarter have WiFi?
     * @return bool|null
     */
    public function has_wifi(): ?bool
    {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'has_wifi')) return (bool)$this->_quarter->has_wifi;
        if (is_array($this->_quarter) && isset($this->_quarter['has_wifi'])) return (bool)$this->_quarter['has_wifi'];
        return null;
    }

    /**
     * Does the quarter have ATM?
     * @return bool|null
     */
    public function has_atm(): ?bool
    {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'has_atm')) return (bool)$this->_quarter->has_atm;
        if (is_array($this->_quarter) && isset($this->_quarter['has_atm'])) return (bool)$this->_quarter['has_atm'];
        return null;
    }

    /**
     * Does the quarter have restroom?
     * @return bool|null
     */
    public function has_restroom(): ?bool
    {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'has_restroom')) return (bool)$this->_quarter->has_restroom;
        if (is_array($this->_quarter) && isset($this->_quarter['has_restroom'])) return (bool)$this->_quarter['has_restroom'];
        return null;
    }

    /**
     * Does the quarter have 24/7 access?
     * @return bool|null
     */
    public function has_24_7_access(): ?bool
    {
        $hours = $this->business_hours();
        if (is_array($hours)) {
            foreach ($hours as $h) {
                if (is_string($h) && stripos($h, '24/7') !== false) return true;
            }
            return false;
        }
        if (is_string($hours) && stripos($hours, '24/7') !== false) return true;
        return null;
    }

    /**
     * Get a summary of available services (if available)
     * @return array|null
     */
    public function services(): ?array
    {
        if (is_object($this->_quarter) && property_exists($this->_quarter, 'services')) return (array)$this->_quarter->services;
        if (is_array($this->_quarter) && isset($this->_quarter['services'])) return (array)$this->_quarter['services'];
        return null;
    }

    /**
     * Is the quarter currently open? (basic check based on business_hours)
     * @return bool|null
     */
    public function is_open(): ?bool
    {
        $hours = $this->business_hours();
        if (is_array($hours)) {
            foreach ($hours as $h) {
                if (is_string($h) && stripos($h, '24/7') !== false) return true;
                if (is_string($h) && stripos($h, 'closed') !== false) return false;
            }
            return null;
        }
        if (is_string($hours) && stripos($hours, '24/7') !== false) return true;
        if (is_string($hours) && stripos($hours, 'closed') !== false) return false;
        return null;
    }

    /**
     * Enhanced CSS class for type/status
     * @return string|null
     */
    public function css_class(): ?string
    {
        $type = $this->type();
        $status = $this->status();
        $classes = ['quarter'];
        if ($type) $classes[] = 'quarter-type-' . strtolower(preg_replace('/[^a-z0-9]+/', '-', $type));
        if ($status) $classes[] = 'quarter-status-' . strtolower(preg_replace('/[^a-z0-9]+/', '-', $status));
        return implode(' ', $classes);
    }

    /** String representation (name) */
    public function __toString(): string { return (string) $this->display_name(); }

    /**
     * Expanded toArray for API/UI completeness
     * @return array
     */
    public function toArray(): array
    {
        return [
            'status' => $this->status(),
            'business_hours' => $this->business_hours(),
            'is_accessible' => $this->is_accessible(),
            'has_parking' => $this->has_parking(),
            'has_wifi' => $this->has_wifi(),
            'has_atm' => $this->has_atm(),
            'has_restroom' => $this->has_restroom(),
            'has_24_7_access' => $this->has_24_7_access(),
            'services' => $this->services(),
            'is_open' => $this->is_open(),
            'css_class' => $this->css_class(),
        ];
    }
}
