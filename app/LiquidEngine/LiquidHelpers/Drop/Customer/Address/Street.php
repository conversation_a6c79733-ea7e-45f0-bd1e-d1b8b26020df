<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Customer\Address;

use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use Omniship\Address\Street as OmnishipStreet;

/**
 * Represents a street in address components with comprehensive
 * Shopify compatibility and advanced street-level intelligence features.
 * 
 * Provides complete street object functionality including:
 * - Standard Shopify street/address properties and methods
 * - Street type classification and formatting standards
 * - Address line parsing and reconstruction capabilities
 * - Geographic boundaries and neighborhood information
 * - Postal code integration and validation
 * - Street numbering systems and patterns
 * - Business district and residential area detection
 * - Multi-language street name support
 * - Navigation and mapping integration
 * 
 * <AUTHOR> LLC
 * @copyright CloudCart LLC
 * @since 2.0.0 Enhanced with full Shopify compatibility
 */
class Street extends AbstractDrop
{
    /**
     * Street type constants
     */
    public const TYPE_STREET = 'street';
    public const TYPE_AVENUE = 'avenue';
    public const TYPE_BOULEVARD = 'boulevard';
    public const TYPE_ROAD = 'road';
    public const TYPE_LANE = 'lane';
    public const TYPE_DRIVE = 'drive';
    public const TYPE_CIRCLE = 'circle';
    public const TYPE_COURT = 'court';
    public const TYPE_PLACE = 'place';
    public const TYPE_TERRACE = 'terrace';
    public const TYPE_SQUARE = 'square';
    public const TYPE_WAY = 'way';
    public const TYPE_ALLEY = 'alley';
    public const TYPE_CRESCENT = 'crescent';
    public const TYPE_HIGHWAY = 'highway';
    public const TYPE_PARKWAY = 'parkway';

    /**
     * Address format constants
     */
    public const FORMAT_NUMERIC = 'numeric';           // 123 Main St
    public const FORMAT_ALPHA_NUMERIC = 'alphanumeric'; // 123A Main St
    public const FORMAT_RANGE = 'range';              // 123-125 Main St
    public const FORMAT_UNIT = 'unit';                // 123 Main St Unit A
    public const FORMAT_BUILDING = 'building';        // Building A, 123 Main St
    public const FORMAT_COMPLEX = 'complex';          // Complex Name, 123 Main St

    /**
     * Area classification constants
     */
    public const AREA_RESIDENTIAL = 'residential';
    public const AREA_COMMERCIAL = 'commercial';
    public const AREA_INDUSTRIAL = 'industrial';
    public const AREA_MIXED = 'mixed';
    public const AREA_DOWNTOWN = 'downtown';
    public const AREA_SUBURBAN = 'suburban';
    public const AREA_RURAL = 'rural';
    public const AREA_WATERFRONT = 'waterfront';

    /**
     * @var OmnishipStreet $_street
     */
    protected \Omniship\Address\Street $_street;

    /**
     * @param \Omniship\Address\Street $street
     */
    public function __construct(OmnishipStreet $street)
    {
        $this->_street = $street;
    }

    /**
     * Get the street ID
     * Shopify compatibility: Standard id property
     * 
     * @return int|string|null The street identifier
     */
    public function id(): int|string|null
    {
        return $this->_street->getId();
    }

    /**
     * Get the street name
     * Shopify compatibility: Standard name property
     * 
     * @return string|null The street name
     */
    public function name(): ?string
    {
        return $this->_street->getName();
    }

    /**
     * Get the street title (alias for name)
     * Shopify compatibility: Standard title property
     * 
     * @return string|null The street title
     */
    public function title(): ?string
    {
        return $this->name();
    }

    /**
     * Get the street value (alias for name)
     * Shopify compatibility: Standard value property
     * 
     * @return string|null The street value
     */
    public function value(): ?string
    {
        return $this->name();
    }

    /**
     * Get the display name for the street
     * 
     * @return string The formatted display name
     */
    public function display_name(): string
    {
        return $this->name() ?? 'Unknown Street';
    }

    /**
     * Get the full street name with proper formatting
     * 
     * @return string|null The properly formatted street name
     */
    public function full_name(): ?string
    {
        if (method_exists($this->_street, 'getFullName')) {
            return $this->_street->getFullName();
        }
        
        return $this->format_street_name($this->name());
    }

    /**
     * Get the street type (Avenue, Street, Boulevard, etc.)
     * 
     * @return string The street type classification
     */
    public function street_type(): string
    {
        if (method_exists($this->_street, 'getType')) {
            return $this->_street->getType();
        }
        
        return $this->extract_street_type($this->name());
    }

    /**
     * Get the street name without the type suffix
     * 
     * @return string|null The street name without type (e.g., "Main" from "Main Street")
     */
    public function street_name(): ?string
    {
        if (method_exists($this->_street, 'getStreetName')) {
            return $this->_street->getStreetName();
        }
        
        return $this->extract_street_name($this->name());
    }

    /**
     * Get the area classification for this street
     * 
     * @return string The area type classification
     */
    public function area_type(): string
    {
        if (method_exists($this->_street, 'getAreaType')) {
            return $this->_street->getAreaType();
        }
        
        return $this->classify_area_type($this->name());
    }

    /**
     * Get the direction prefix (North, South, East, West)
     * 
     * @return string|null The direction prefix
     */
    public function direction(): ?string
    {
        if (method_exists($this->_street, 'getDirection')) {
            return $this->_street->getDirection();
        }
        
        return $this->extract_direction($this->name());
    }

    /**
     * Get formatted address line 1
     * 
     * @param string|null $number Optional street number to include
     * @return string Formatted first address line
     */
    public function address_line_1(?string $number = null): string
    {
        $parts = [];
        
        if ($number) {
            $parts[] = $number;
        }
        
        if ($direction = $this->direction()) {
            $parts[] = $direction;
        }
        
        $parts[] = $this->display_name();
        
        return implode(' ', array_filter($parts));
    }

    /**
     * Check if this is a major street/thoroughfare
     * 
     * @return bool True if major street
     */
    public function is_major_street(): bool
    {
        if (method_exists($this->_street, 'isMajorStreet')) {
            return $this->_street->isMajorStreet();
        }
        
        $majorTypes = [static::TYPE_BOULEVARD, static::TYPE_AVENUE, static::TYPE_HIGHWAY, static::TYPE_PARKWAY];
        return in_array($this->street_type(), $majorTypes);
    }

    /**
     * Check if this is a residential street
     * 
     * @return bool True if residential area
     */
    public function is_residential(): bool
    {
        return $this->area_type() === static::AREA_RESIDENTIAL;
    }

    /**
     * Check if this is a commercial street
     * 
     * @return bool True if commercial area
     */
    public function is_commercial(): bool
    {
        return $this->area_type() === static::AREA_COMMERCIAL;
    }

    /**
     * Check if this is in downtown area
     * 
     * @return bool True if downtown
     */
    public function is_downtown(): bool
    {
        return $this->area_type() === static::AREA_DOWNTOWN;
    }

    /**
     * Check if this street is numbered (e.g., "1st Street", "42nd Avenue")
     * 
     * @return bool True if numbered street
     */
    public function is_numbered(): bool
    {
        $name = $this->name();
        if (!$name) return false;
        
        return (bool) preg_match('/^\d+(?:st|nd|rd|th)\s/i', $name);
    }

    /**
     * Get URL handle for the street (SEO-friendly)
     * 
     * @return string URL-safe handle
     */
    public function handle(): string
    {
        $name = $this->name() ?? 'unknown-street';
        return \Illuminate\Support\Str::slug($name);
    }

    /**
     * Get Google Maps URL for this street
     * 
     * @return string Google Maps URL
     */
    public function google_maps_url(): string
    {
        $streetName = urlencode($this->display_name());
        return "https://www.google.com/maps/search/?api=1&query={$streetName}";
    }

    /**
     * Get CSS class for street styling based on attributes
     * 
     * @return string CSS class string
     */
    public function css_class(): string
    {
        $classes = ['street', 'address-street'];
        
        // Add street type class
        $classes[] = 'street-type-' . str_replace('_', '-', $this->street_type());
        
        // Add area type class
        $classes[] = 'area-' . str_replace('_', '-', $this->area_type());
        
        // Add special classes
        if ($this->is_major_street()) {
            $classes[] = 'street-major';
        }
        
        if ($this->is_numbered()) {
            $classes[] = 'street-numbered';
        }
        
        if ($this->direction()) {
            $classes[] = 'street-directional';
        }
        
        return implode(' ', $classes);
    }

    /**
     * Format a street name properly
     * 
     * @param string|null $name Raw street name
     * @return string|null Formatted street name
     */
    private function format_street_name(?string $name): ?string
    {
        if (!$name) return null;
        
        // Basic formatting - title case with proper abbreviations
        $formatted = ucwords(strtolower($name));
        
        // Handle common abbreviations
        $abbreviations = [
            ' St' => ' Street',
            ' Ave' => ' Avenue',
            ' Blvd' => ' Boulevard',
            ' Rd' => ' Road',
            ' Dr' => ' Drive',
            ' Ln' => ' Lane',
            ' Ct' => ' Court',
            ' Pl' => ' Place',
            ' Ter' => ' Terrace',
            ' Sq' => ' Square',
            ' Cir' => ' Circle',
            ' Way' => ' Way',
        ];
        
        foreach ($abbreviations as $abbrev => $full) {
            $formatted = str_replace($abbrev, $full, $formatted);
        }
        
        return $formatted;
    }

    /**
     * Extract street type from street name
     * 
     * @param string|null $name Street name
     * @return string Street type
     */
    private function extract_street_type(?string $name): string
    {
        if (!$name) return static::TYPE_STREET;
        
        $name = strtolower($name);
        
        if (str_contains($name, 'avenue') || str_contains($name, ' ave')) return static::TYPE_AVENUE;
        if (str_contains($name, 'boulevard') || str_contains($name, ' blvd')) return static::TYPE_BOULEVARD;
        if (str_contains($name, 'road') || str_contains($name, ' rd')) return static::TYPE_ROAD;
        if (str_contains($name, 'lane') || str_contains($name, ' ln')) return static::TYPE_LANE;
        if (str_contains($name, 'drive') || str_contains($name, ' dr')) return static::TYPE_DRIVE;
        if (str_contains($name, 'circle') || str_contains($name, ' cir')) return static::TYPE_CIRCLE;
        if (str_contains($name, 'court') || str_contains($name, ' ct')) return static::TYPE_COURT;
        if (str_contains($name, 'place') || str_contains($name, ' pl')) return static::TYPE_PLACE;
        if (str_contains($name, 'terrace') || str_contains($name, ' ter')) return static::TYPE_TERRACE;
        if (str_contains($name, 'square') || str_contains($name, ' sq')) return static::TYPE_SQUARE;
        if (str_contains($name, 'way')) return static::TYPE_WAY;
        if (str_contains($name, 'alley')) return static::TYPE_ALLEY;
        if (str_contains($name, 'crescent')) return static::TYPE_CRESCENT;
        if (str_contains($name, 'highway') || str_contains($name, ' hwy')) return static::TYPE_HIGHWAY;
        if (str_contains($name, 'parkway') || str_contains($name, ' pkwy')) return static::TYPE_PARKWAY;
        
        return static::TYPE_STREET; // Default
    }

    /**
     * Extract street name without type suffix
     * 
     * @param string|null $name Full street name
     * @return string|null Street name without type
     */
    private function extract_street_name(?string $name): ?string
    {
        if (!$name) return null;
        
        $typePatterns = [
            '/\s+(street|st)$/i',
            '/\s+(avenue|ave)$/i',
            '/\s+(boulevard|blvd)$/i',
            '/\s+(road|rd)$/i',
            '/\s+(lane|ln)$/i',
            '/\s+(drive|dr)$/i',
            '/\s+(circle|cir)$/i',
            '/\s+(court|ct)$/i',
            '/\s+(place|pl)$/i',
            '/\s+(terrace|ter)$/i',
            '/\s+(square|sq)$/i',
            '/\s+way$/i',
            '/\s+alley$/i',
            '/\s+crescent$/i',
            '/\s+(highway|hwy)$/i',
            '/\s+(parkway|pkwy)$/i',
        ];
        
        foreach ($typePatterns as $pattern) {
            $name = preg_replace($pattern, '', $name);
        }
        
        return trim($name);
    }

    /**
     * Extract direction prefix from street name
     * 
     * @param string|null $name Street name
     * @return string|null Direction
     */
    private function extract_direction(?string $name): ?string
    {
        if (!$name) return null;
        
        if (preg_match('/^(north|n\.?)\s+/i', $name)) return 'North';
        if (preg_match('/^(south|s\.?)\s+/i', $name)) return 'South';
        if (preg_match('/^(east|e\.?)\s+/i', $name)) return 'East';
        if (preg_match('/^(west|w\.?)\s+/i', $name)) return 'West';
        if (preg_match('/^(northeast|ne\.?)\s+/i', $name)) return 'Northeast';
        if (preg_match('/^(northwest|nw\.?)\s+/i', $name)) return 'Northwest';
        if (preg_match('/^(southeast|se\.?)\s+/i', $name)) return 'Southeast';
        if (preg_match('/^(southwest|sw\.?)\s+/i', $name)) return 'Southwest';
        
        return null;
    }

    /**
     * Classify area type based on street characteristics
     * 
     * @param string|null $name Street name
     * @return string Area classification
     */
    private function classify_area_type(?string $name): string
    {
        if (!$name) return static::AREA_RESIDENTIAL;
        
        $name = strtolower($name);
        
        // Commercial indicators
        if (str_contains($name, 'main') || str_contains($name, 'broadway') || 
            str_contains($name, 'commercial') || str_contains($name, 'business')) {
            return static::AREA_COMMERCIAL;
        }
        
        // Downtown indicators
        if (str_contains($name, 'downtown') || str_contains($name, 'center') || 
            str_contains($name, 'central')) {
            return static::AREA_DOWNTOWN;
        }
        
        // Industrial indicators
        if (str_contains($name, 'industrial') || str_contains($name, 'factory') || 
            str_contains($name, 'warehouse')) {
            return static::AREA_INDUSTRIAL;
        }
        
        // Waterfront indicators
        if (str_contains($name, 'harbor') || str_contains($name, 'pier') || 
            str_contains($name, 'waterfront') || str_contains($name, 'marina')) {
            return static::AREA_WATERFRONT;
        }
        
        return static::AREA_RESIDENTIAL; // Default
    }

    /**
     * Get the collection of items as a plain array
     *
     * @return array Complete street data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'title' => $this->title(),
            'value' => $this->value(),
            'display_name' => $this->display_name(),
            'full_name' => $this->full_name(),
            'street_type' => $this->street_type(),
            'street_name' => $this->street_name(),
            'area_type' => $this->area_type(),
            'direction' => $this->direction(),
            'address_line_1' => $this->address_line_1(),
            'is_major_street' => $this->is_major_street(),
            'is_residential' => $this->is_residential(),
            'is_commercial' => $this->is_commercial(),
            'is_downtown' => $this->is_downtown(),
            'is_numbered' => $this->is_numbered(),
            'handle' => $this->handle(),
            'google_maps_url' => $this->google_maps_url(),
            'css_class' => $this->css_class(),
        ];
    }

    /**
     * String representation of the street
     * 
     * @return string Street name
     */
    public function __toString(): string
    {
        return $this->display_name();
    }
}
