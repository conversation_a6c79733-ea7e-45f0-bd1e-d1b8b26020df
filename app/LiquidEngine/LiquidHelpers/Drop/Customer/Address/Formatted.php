<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Customer\Address;

use Modules\Apps\Shippings\Omniship\Helpers\Address;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\Helper\Format;

class Formatted extends AbstractDrop
{
    /**
     * Format constants for different address styles
     */
    public const FORMAT_STYLE_DEFAULT = 'default';
    public const FORMAT_STYLE_SHOPIFY = 'shopify';
    public const FORMAT_STYLE_EUROPEAN = 'european';
    public const FORMAT_STYLE_ASIAN = 'asian';
    public const FORMAT_STYLE_AMERICAN = 'american';
    public const FORMAT_STYLE_COMPACT = 'compact';
    public const FORMAT_STYLE_VERBOSE = 'verbose';

    /**
     * HTML formatting constants
     */
    public const HTML_CLASS_ADDRESS = 'address-formatted';
    public const HTML_CLASS_LINE = 'address-line';
    public const HTML_CLASS_NAME = 'address-name';
    public const HTML_CLASS_COMPANY = 'address-company';
    public const HTML_CLASS_STREET = 'address-street';
    public const HTML_CLASS_CITY = 'address-city';
    public const HTML_CLASS_COUNTRY = 'address-country';

    /**
     * @var Address $_address
     */
    protected $_address;

    /**
     * Formatted constructor.
     * @param Address $address
     */
    public function __construct($address)
    {
        $this->_address = $address;
    }

    /**
     * Get HTML formatted address with customer name
     * Shopify compatibility: formatted_address_html
     * 
     * @return string
     */
    public function html_with_customer(): string
    {
        return $this->format_html(self::FORMAT_STYLE_DEFAULT, true);
    }

    /**
     * Get HTML formatted address without customer name
     * 
     * @return string
     */
    public function html_without_customer(): string
    {
        return $this->format_html(self::FORMAT_STYLE_DEFAULT, false);
    }

    /**
     * Get text formatted address with customer name
     * Shopify compatibility: formatted_address
     * 
     * @return string
     */
    public function text_with_customer(): string
    {
        return $this->format(self::FORMAT_STYLE_DEFAULT, true);
    }

    /**
     * Get text formatted address without customer name
     * 
     * @return string
     */
    public function text_without_customer(): string
    {
        return $this->format(self::FORMAT_STYLE_DEFAULT, false);
    }

    /**
     * Get Shopify-compatible formatted address
     * 
     * @return string
     */
    public function shopify(): string
    {
        return $this->format(self::FORMAT_STYLE_SHOPIFY);
    }

    /**
     * Get European-style formatted address
     * 
     * @return string
     */
    public function european(): string
    {
        return $this->format(self::FORMAT_STYLE_EUROPEAN);
    }

    /**
     * Get Asian-style formatted address
     * 
     * @return string
     */
    public function asian(): string
    {
        return $this->format(self::FORMAT_STYLE_ASIAN);
    }

    /**
     * Get American-style formatted address
     * 
     * @return string
     */
    public function american(): string
    {
        return $this->format(self::FORMAT_STYLE_AMERICAN);
    }

    /**
     * Get compact formatted address (single line)
     * 
     * @return string
     */
    public function compact(): string
    {
        return $this->format(self::FORMAT_STYLE_COMPACT);
    }

    /**
     * Get verbose formatted address (with all details)
     * 
     * @return string
     */
    public function verbose(): string
    {
        return $this->format(self::FORMAT_STYLE_VERBOSE);
    }

    /**
     * Get address formatted with specific style
     * 
     * @param string $style
     * @param bool $includeCustomer
     * @return string
     */
    public function format(string $style = self::FORMAT_STYLE_DEFAULT, bool $includeCustomer = true): string
    {
        $address = $this->_address;
        $parts = [];

        if ($includeCustomer && $address->getFirstName() && $address->getLastName()) {
            $parts[] = trim($address->getFirstName() . ' ' . $address->getLastName());
        }

        switch ($style) {
            case self::FORMAT_STYLE_SHOPIFY:
                $street = $address->getStreet();
                $parts[] = $street ? $street->getName() : '';
                if ($address->getStreetNumber()) {
                    $parts[] = $address->getStreetNumber();
                }
                $parts[] = sprintf('%s, %s %s', 
                    $address->getCity()->getName(),
                    $address->getState()->getIso2(),
                    $address->getPostCode()
                );
                $parts[] = $address->getCountry()->getName();
                break;

            case self::FORMAT_STYLE_EUROPEAN:
                $street = $address->getStreet();
                $parts[] = $street ? $street->getName() : '';
                if ($address->getStreetNumber()) {
                    $parts[] = $address->getStreetNumber();
                }
                $parts[] = sprintf('%s %s', 
                    $address->getPostCode(),
                    $address->getCity()->getName()
                );
                $parts[] = $address->getCountry()->getName();
                break;

            case self::FORMAT_STYLE_ASIAN:
                $parts[] = $address->getCountry()->getName();
                $parts[] = sprintf('%s %s', 
                    $address->getPostCode(),
                    $address->getCity()->getName()
                );
                $street = $address->getStreet();
                $parts[] = $street ? $street->getName() : '';
                if ($address->getStreetNumber()) {
                    $parts[] = $address->getStreetNumber();
                }
                break;

            case self::FORMAT_STYLE_AMERICAN:
                $street = $address->getStreet();
                $parts[] = $street ? $street->getName() : '';
                if ($address->getStreetNumber()) {
                    $parts[] = $address->getStreetNumber();
                }
                $parts[] = sprintf('%s, %s %s', 
                    $address->getCity()->getName(),
                    $address->getState()->getIso2(),
                    $address->getPostCode()
                );
                $parts[] = $address->getCountry()->getName();
                break;

            case self::FORMAT_STYLE_COMPACT:
                $street = $address->getStreet();
                $parts = array_filter([
                    $street ? $street->getName() : '',
                    $address->getStreetNumber(),
                    $address->getCity()->getName(),
                    $address->getState()->getIso2(),
                    $address->getPostCode(),
                    $address->getCountry()->getName()
                ]);
                return implode(', ', $parts);

            case self::FORMAT_STYLE_VERBOSE:
                if ($address->getCompanyName()) {
                    $parts[] = $address->getCompanyName();
                }
                $street = $address->getStreet();
                $parts[] = $street ? $street->getName() : '';
                if ($address->getStreetNumber()) {
                    $parts[] = $address->getStreetNumber();
                }
                if ($address->getQuarter()) {
                    $parts[] = $address->getQuarter()->getName();
                }
                if ($address->getOffice()) {
                    $parts[] = $address->getOffice()->getName();
                }
                $parts[] = sprintf('%s, %s %s', 
                    $address->getCity()->getName(),
                    $address->getState()->getName(),
                    $address->getPostCode()
                );
                $parts[] = $address->getCountry()->getName();
                if ($address->getPhone()) {
                    $parts[] = $address->getPhone();
                }
                break;

            default:
                return $this->text_with_customer();
        }

        return implode("\n", array_filter($parts));
    }

    /**
     * Get HTML formatted address with specific style
     * 
     * @param string $style
     * @param bool $includeCustomer
     * @return string
     */
    public function format_html(string $style = self::FORMAT_STYLE_DEFAULT, bool $includeCustomer = true): string
    {
        $lines = explode("\n", $this->format($style, $includeCustomer));
        $html = [];
        $street = $this->_address->getStreet();
        $streetName = $street ? $street->getName() : '';
        
        foreach ($lines as $index => $line) {
            $class = self::HTML_CLASS_LINE;
            
            // Add specific classes based on line content
            if ($index === 0 && $includeCustomer) {
                $class .= ' ' . self::HTML_CLASS_NAME;
            } elseif (strpos($line, $this->_address->getCompanyName()) !== false) {
                $class .= ' ' . self::HTML_CLASS_COMPANY;
            } elseif ($streetName && strpos($line, $streetName) !== false) {
                $class .= ' ' . self::HTML_CLASS_STREET;
            } elseif (strpos($line, $this->_address->getCity()->getName()) !== false) {
                $class .= ' ' . self::HTML_CLASS_CITY;
            } elseif (strpos($line, $this->_address->getCountry()->getName()) !== false) {
                $class .= ' ' . self::HTML_CLASS_COUNTRY;
            }
            
            $html[] = sprintf('<div class="%s">%s</div>', $class, htmlspecialchars($line));
        }
        
        return sprintf('<div class="%s">%s</div>', 
            self::HTML_CLASS_ADDRESS,
            implode("\n", $html)
        );
    }

    /**
     * Get address formatted for Google Maps
     * 
     * @return string
     */
    public function for_maps(): string
    {
        $address = $this->_address;
        $parts = [
            $address->getStreet(),
            $address->getStreetNumber(),
            $address->getCity()->getName(),
            $address->getState()->getName(),
            $address->getPostCode(),
            $address->getCountry()->getName()
        ];
        return implode(', ', array_filter($parts));
    }

    /**
     * Get address formatted for shipping labels
     * 
     * @return string
     */
    public function for_shipping(): string
    {
        $address = $this->_address;
        $parts = [
            trim($address->getFirstName() . ' ' . $address->getLastName()),
            $address->getStreet(),
            $address->getStreetNumber(),
            sprintf('%s, %s %s', 
                $address->getCity()->getName(),
                $address->getState()->getIso2(),
                $address->getPostCode()
            ),
            $address->getCountry()->getName()
        ];
        return implode("\n", array_filter($parts));
    }

    /**
     * Get address formatted for billing
     * 
     * @return string
     */
    public function for_billing(): string
    {
        $address = $this->_address;
        $parts = [
            $address->getCompanyName(),
            trim($address->getFirstName() . ' ' . $address->getLastName()),
            $address->getStreet(),
            $address->getStreetNumber(),
            sprintf('%s, %s %s', 
                $address->getCity()->getName(),
                $address->getState()->getIso2(),
                $address->getPostCode()
            ),
            $address->getCountry()->getName()
        ];
        return implode("\n", array_filter($parts));
    }

    /**
     * Get address formatted for international shipping
     * 
     * @return string
     */
    public function for_international(): string
    {
        $address = $this->_address;
        $parts = [
            trim($address->getFirstName() . ' ' . $address->getLastName()),
            $address->getStreet(),
            $address->getStreetNumber(),
            sprintf('%s, %s %s', 
                $address->getCity()->getName(),
                $address->getState()->getName(),
                $address->getPostCode()
            ),
            $address->getCountry()->getName(),
            $address->getPhone()
        ];
        return implode("\n", array_filter($parts));
    }

    /**
     * Get the collection of items as a plain array.
     * Shopify compatibility: toArray method
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'html_with_customer' => $this->html_with_customer(),
            'html_without_customer' => $this->html_without_customer(),
            'text_with_customer' => $this->text_with_customer(),
            'text_without_customer' => $this->text_without_customer(),
            'shopify' => $this->shopify(),
            'european' => $this->european(),
            'asian' => $this->asian(),
            'american' => $this->american(),
            'compact' => $this->compact(),
            'verbose' => $this->verbose(),
            'for_maps' => $this->for_maps(),
            'for_shipping' => $this->for_shipping(),
            'for_billing' => $this->for_billing(),
            'for_international' => $this->for_international(),
            'formatted_address' => $this->text_with_customer(),
            'formatted_address_html' => $this->html_with_customer()
        ];
    }
}
