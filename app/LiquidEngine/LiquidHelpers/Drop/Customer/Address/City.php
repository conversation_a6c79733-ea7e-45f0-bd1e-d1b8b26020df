<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Customer\Address;

use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use Omniship\Address\City as OmnishipCity;

/**
 * Represents a city in address components with comprehensive
 * Shopify compatibility and advanced city intelligence features.
 * 
 * Provides complete city object functionality including:
 * - Standard Shopify city properties and methods
 * - Geographic information and coordinates
 * - Administrative and postal data
 * - Population and demographic information
 * - Timezone and localization support
 * - URL generation for mapping and information
 * - Multi-language name support
 * 
 * <AUTHOR> LLC
 * @copyright CloudCart LLC
 * @since 2.0.0 Enhanced with full Shopify compatibility
 */
class City extends AbstractDrop
{
    /**
     * City type constants for classification
     */
    public const TYPE_CITY = 'city';
    public const TYPE_TOWN = 'town';
    public const TYPE_VILLAGE = 'village';
    public const TYPE_MUNICIPALITY = 'municipality';
    public const TYPE_DISTRICT = 'district';
    public const TYPE_BOROUGH = 'borough';
    public const TYPE_SUBURB = 'suburb';

    /**
     * Population size classification constants
     */
    public const SIZE_MEGACITY = 'megacity';        // 10M+
    public const SIZE_METROPOLIS = 'metropolis';    // 1M-10M
    public const SIZE_LARGE_CITY = 'large_city';    // 300K-1M
    public const SIZE_MEDIUM_CITY = 'medium_city';  // 100K-300K
    public const SIZE_SMALL_CITY = 'small_city';    // 50K-100K
    public const SIZE_TOWN = 'town';                // 5K-50K
    public const SIZE_SMALL_TOWN = 'small_town';    // 1K-5K
    public const SIZE_VILLAGE = 'village';          // <1K

    /**
     * @var OmnishipCity $_city
     */
    protected \Omniship\Address\City $_city;

    /**
     * @param \Omniship\Address\City $city
     */
    public function __construct(OmnishipCity $city)
    {
        $this->_city = $city;
    }

    /**
     * Get the city ID
     * Shopify compatibility: Standard id property
     * 
     * @return int|string|null The city identifier
     */
    public function id(): int|string|null
    {
        return $this->_city->getId();
    }

    /**
     * Get the city name
     * Shopify compatibility: Standard name property
     * 
     * @return string|null The city name
     */
    public function name(): ?string
    {
        return $this->_city->getName();
    }

    /**
     * Get the city title (alias for name)
     * Shopify compatibility: Standard title property
     * 
     * @return string|null The city title
     */
    public function title(): ?string
    {
        return $this->name();
    }

    /**
     * Get the city value (alias for name)
     * Shopify compatibility: Standard value property
     * 
     * @return string|null The city value
     */
    public function value(): ?string
    {
        return $this->name();
    }

    /**
     * Get the display name for the city
     * 
     * @return string The formatted display name
     */
    public function display_name(): string
    {
        return $this->name() ?? 'Unknown City';
    }

    /**
     * Get the city code or abbreviation
     * 
     * @return string|null The city code
     */
    public function code(): ?string
    {
        // Try to get code from the Omniship city object
        if (method_exists($this->_city, 'getCode')) {
            return $this->_city->getCode();
        }
        
        // Generate a code from the name if not available
        if ($name = $this->name()) {
            return strtoupper(substr(preg_replace('/[^A-Za-z]/', '', $name), 0, 3));
        }
        
        return null;
    }

    /**
     * Get the city type classification
     * 
     * @return string The city type
     */
    public function city_type(): string
    {
        // Determine type based on population or name patterns
        $population = $this->population();
        
        if ($population && $population > 1000000) {
            return static::TYPE_CITY;
        } elseif ($population && $population > 50000) {
            return static::TYPE_CITY;
        } elseif ($population && $population > 5000) {
            return static::TYPE_TOWN;
        } elseif ($population) {
            return static::TYPE_VILLAGE;
        }
        
        // Default based on name patterns or fallback to city
        $name = strtolower($this->name() ?? '');
        if (strpos($name, 'village') !== false || strpos($name, 'село') !== false) {
            return static::TYPE_VILLAGE;
        } elseif (strpos($name, 'town') !== false || strpos($name, 'град') !== false) {
            return static::TYPE_TOWN;
        }
        
        return static::TYPE_CITY;
    }

    /**
     * Get the population size classification
     * 
     * @return string The size classification
     */
    public function size_classification(): string
    {
        $population = $this->population();
        
        if (!$population) {
            return static::SIZE_TOWN; // Default assumption
        }
        
        if ($population >= 10000000) {
            return static::SIZE_MEGACITY;
        } elseif ($population >= 1000000) {
            return static::SIZE_METROPOLIS;
        } elseif ($population >= 300000) {
            return static::SIZE_LARGE_CITY;
        } elseif ($population >= 100000) {
            return static::SIZE_MEDIUM_CITY;
        } elseif ($population >= 50000) {
            return static::SIZE_SMALL_CITY;
        } elseif ($population >= 5000) {
            return static::SIZE_TOWN;
        } elseif ($population >= 1000) {
            return static::SIZE_SMALL_TOWN;
        } else {
            return static::SIZE_VILLAGE;
        }
    }

    /**
     * Get the city population
     * 
     * @return int|null The population count
     */
    public function population(): ?int
    {
        // Try to get population from the Omniship city object
        if (method_exists($this->_city, 'getPopulation')) {
            return $this->_city->getPopulation();
        }
        
        return null;
    }

    /**
     * Get formatted population with thousands separators
     * 
     * @return string|null Formatted population
     */
    public function population_formatted(): ?string
    {
        $population = $this->population();
        return $population ? number_format($population) : null;
    }

    /**
     * Get the geographic coordinates
     * 
     * @return array|null Array with lat/lng or null if not available
     */
    public function coordinates(): ?array
    {
        $lat = $this->latitude();
        $lng = $this->longitude();
        
        if ($lat !== null && $lng !== null) {
            return [
                'latitude' => $lat,
                'longitude' => $lng
            ];
        }
        
        return null;
    }

    /**
     * Get the latitude coordinate
     * 
     * @return float|null Latitude or null if not available
     */
    public function latitude(): ?float
    {
        if (method_exists($this->_city, 'getLatitude')) {
            $lat = $this->_city->getLatitude();
            return $lat !== null ? (float) $lat : null;
        }
        
        return null;
    }

    /**
     * Get the longitude coordinate
     * 
     * @return float|null Longitude or null if not available
     */
    public function longitude(): ?float
    {
        if (method_exists($this->_city, 'getLongitude')) {
            $lng = $this->_city->getLongitude();
            return $lng !== null ? (float) $lng : null;
        }
        
        return null;
    }

    /**
     * Check if the city has geographic coordinates
     * 
     * @return bool True if coordinates are available
     */
    public function has_coordinates(): bool
    {
        return $this->latitude() !== null && $this->longitude() !== null;
    }

    /**
     * Get the timezone for this city
     * 
     * @return string|null Timezone identifier
     */
    public function timezone(): ?string
    {
        if (method_exists($this->_city, 'getTimezone')) {
            return $this->_city->getTimezone();
        }
        
        // Basic timezone estimation based on coordinates or country
        if ($this->has_coordinates()) {
            $lat = $this->latitude();
            $lng = $this->longitude();
            
            // Simple timezone approximation (this could be enhanced with a proper timezone API)
            if ($lng > -150 && $lng < -120) return 'America/Los_Angeles';
            if ($lng > -120 && $lng < -90) return 'America/Denver';
            if ($lng > -90 && $lng < -60) return 'America/Chicago';
            if ($lng > -60 && $lng < -30) return 'America/New_York';
            if ($lng > -30 && $lng < 30) return 'Europe/London';
            if ($lng > 30 && $lng < 60) return 'Europe/Moscow';
            if ($lng > 60 && $lng < 120) return 'Asia/Bangkok';
            if ($lng > 120 && $lng < 180) return 'Asia/Tokyo';
        }
        
        return null;
    }

    /**
     * Get postal codes associated with this city
     * 
     * @return array Array of postal codes
     */
    public function postal_codes(): array
    {
        if (method_exists($this->_city, 'getPostalCodes')) {
            return $this->_city->getPostalCodes();
        }
        
        return [];
    }

    /**
     * Get the primary postal code for this city
     * 
     * @return string|null Primary postal code
     */
    public function postal_code(): ?string
    {
        $codes = $this->postal_codes();
        return !empty($codes) ? $codes[0] : null;
    }

    /**
     * Get administrative area information
     * 
     * @return string|null Administrative area (district, region, etc.)
     */
    public function administrative_area(): ?string
    {
        if (method_exists($this->_city, 'getAdministrativeArea')) {
            return $this->_city->getAdministrativeArea();
        }
        
        return null;
    }

    /**
     * Get alternative names for the city
     * 
     * @return array Array of alternative names
     */
    public function alternative_names(): array
    {
        if (method_exists($this->_city, 'getAlternativeNames')) {
            return $this->_city->getAlternativeNames();
        }
        
        return [];
    }

    /**
     * Get the city name in a specific language
     * 
     * @param string $language Language code (e.g., 'en', 'bg', 'de')
     * @return string|null City name in specified language
     */
    public function name_in_language(string $language): ?string
    {
        if (method_exists($this->_city, 'getNameInLanguage')) {
            return $this->_city->getNameInLanguage($language);
        }
        
        // Fallback to main name if language-specific method not available
        return $this->name();
    }

    /**
     * Get URL handle for the city (SEO-friendly)
     * 
     * @return string URL-safe handle
     */
    public function handle(): string
    {
        $name = $this->name() ?? 'unknown-city';
        return \Illuminate\Support\Str::slug($name);
    }

    /**
     * Get Google Maps URL for this city
     * 
     * @return string Google Maps URL
     */
    public function google_maps_url(): string
    {
        if ($this->has_coordinates()) {
            $lat = $this->latitude();
            $lng = $this->longitude();
            return "https://www.google.com/maps/search/?api=1&query={$lat},{$lng}";
        }
        
        $cityName = urlencode($this->display_name());
        return "https://www.google.com/maps/search/?api=1&query={$cityName}";
    }

    /**
     * Get Wikipedia URL for this city
     * 
     * @param string $language Language code for Wikipedia (default: 'en')
     * @return string Wikipedia URL
     */
    public function wikipedia_url(string $language = 'en'): string
    {
        $cityName = str_replace(' ', '_', $this->display_name());
        return "https://{$language}.wikipedia.org/wiki/{$cityName}";
    }

    /**
     * Check if the city is a capital
     * 
     * @return bool True if the city is a capital
     */
    public function is_capital(): bool
    {
        if (method_exists($this->_city, 'isCapital')) {
            return $this->_city->isCapital();
        }
        
        // Basic check for common capital indicators
        $name = strtolower($this->name() ?? '');
        $capitalIndicators = ['capital', 'столица', 'hauptstadt', 'capitale'];
        
        foreach ($capitalIndicators as $indicator) {
            if (strpos($name, $indicator) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if the city is a major metropolitan area
     * 
     * @return bool True if the city is a major metro area
     */
    public function is_metropolitan(): bool
    {
        $classification = $this->size_classification();
        return in_array($classification, [
            static::SIZE_MEGACITY,
            static::SIZE_METROPOLIS,
            static::SIZE_LARGE_CITY
        ]);
    }

    /**
     * Get CSS class for city styling based on type and size
     * 
     * @return string CSS class string
     */
    public function css_class(): string
    {
        $classes = ['city'];
        
        // Add type class
        $classes[] = 'city-type-' . str_replace('_', '-', $this->city_type());
        
        // Add size class
        $classes[] = 'city-size-' . str_replace('_', '-', $this->size_classification());
        
        // Add special classes
        if ($this->is_capital()) {
            $classes[] = 'city-capital';
        }
        
        if ($this->is_metropolitan()) {
            $classes[] = 'city-metropolitan';
        }
        
        if ($this->has_coordinates()) {
            $classes[] = 'city-geo-enabled';
        }
        
        return implode(' ', $classes);
    }

    /**
     * Get distance to another city (if coordinates available)
     * 
     * @param City $otherCity Another city to compare
     * @return float|null Distance in kilometers, null if coordinates unavailable
     */
    public function distance_to(City $otherCity): ?float
    {
        if (!$this->has_coordinates() || !$otherCity->has_coordinates()) {
            return null;
        }
        
        return $this->calculate_distance(
            $this->latitude(),
            $this->longitude(),
            $otherCity->latitude(),
            $otherCity->longitude()
        );
    }

    /**
     * Get the collection of items as JSON
     *
     * @param int $options JSON encoding options
     * @return string JSON representation
     */
    public function toJson($options = 0): string
    {
        return json_encode($this->toArray(), $options);
    }

    /**
     * Get the collection of items as a plain array
     *
     * @return array Complete city data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'title' => $this->title(),
            'value' => $this->value(),
            'display_name' => $this->display_name(),
            'code' => $this->code(),
            'city_type' => $this->city_type(),
            'size_classification' => $this->size_classification(),
            'population' => $this->population(),
            'population_formatted' => $this->population_formatted(),
            'coordinates' => $this->coordinates(),
            'latitude' => $this->latitude(),
            'longitude' => $this->longitude(),
            'has_coordinates' => $this->has_coordinates(),
            'timezone' => $this->timezone(),
            'postal_codes' => $this->postal_codes(),
            'postal_code' => $this->postal_code(),
            'administrative_area' => $this->administrative_area(),
            'alternative_names' => $this->alternative_names(),
            'handle' => $this->handle(),
            'google_maps_url' => $this->google_maps_url(),
            'is_capital' => $this->is_capital(),
            'is_metropolitan' => $this->is_metropolitan(),
            'css_class' => $this->css_class(),
        ];
    }

    /**
     * Calculate distance between two points using Haversine formula
     * 
     * @param float $lat1 Latitude of first point
     * @param float $lon1 Longitude of first point
     * @param float $lat2 Latitude of second point
     * @param float $lon2 Longitude of second point
     * @return float Distance in kilometers
     */
    protected function calculate_distance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371; // Earth radius in kilometers
        
        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);
        
        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLon / 2) * sin($dLon / 2);
        
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        
        return $earthRadius * $c;
    }

    /**
     * String representation of the city
     * 
     * @return string City name
     */
    public function __toString(): string
    {
        return $this->display_name();
    }
}
