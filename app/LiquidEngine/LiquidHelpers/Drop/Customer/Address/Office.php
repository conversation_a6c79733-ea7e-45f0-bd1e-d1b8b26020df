<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Customer\Address;

use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use Omniship\Address\Office as OmnishipOffice;

class Office extends AbstractDrop
{
    /**
     * @var OmnishipOffice|array $_office
     */
    protected $_office;

    // Office type constants
    public const TYPE_POST = 'post';
    public const TYPE_COURIER = 'courier';
    public const TYPE_PICKUP = 'pickup';
    public const TYPE_BRANCH = 'branch';
    public const TYPE_HEADQUARTERS = 'headquarters';
    public const TYPE_WAREHOUSE = 'warehouse';
    public const TYPE_STORE = 'store';
    public const TYPE_SERVICE = 'service';

    // Office status constants
    public const STATUS_ACTIVE = 'active';
    public const STATUS_INACTIVE = 'inactive';
    public const STATUS_TEMPORARY = 'temporary';
    public const STATUS_PERMANENT = 'permanent';
    public const STATUS_MAINTENANCE = 'maintenance';

    // Business hours constants
    public const HOURS_24_7 = '24/7';
    public const HOURS_BUSINESS = 'business';
    public const HOURS_CUSTOM = 'custom';
    public const HOURS_CLOSED = 'closed';

    /**
     * @param OmnishipOffice|array $office
     */
    public function __construct($office)
    {
        $this->_office = $office;
    }

    /**
     * Get the unique office ID
     * @return int|string|null
     */
    public function id(): int|string|null
    {
        return is_object($this->_office) && method_exists($this->_office, 'getId')
            ? $this->_office->getId()
            : ($this->_office['id'] ?? null);
    }

    /**
     * Get the office name
     * @return string|null
     */
    public function name(): ?string
    {
        return is_object($this->_office) && method_exists($this->_office, 'getName')
            ? $this->_office->getName()
            : ($this->_office['name'] ?? null);
    }

    /**
     * Shopify-compatible alias for name
     * @return string|null
     */
    public function title(): ?string
    {
        return $this->name();
    }

    /**
     * Value for form fields (ID or code)
     * @return int|string|null
     */
    public function value(): int|string|null
    {
        return $this->id();
    }

    /**
     * Display name (with type or code if available)
     * @return string|null
     */
    public function display_name(): ?string
    {
        $name = $this->name();
        $type = $this->type();
        $code = $this->code();
        if ($type && $code) {
            return "$name ($type, $code)";
        } elseif ($type) {
            return "$name ($type)";
        } elseif ($code) {
            return "$name ($code)";
        }
        return $name;
    }

    /**
     * Office code (if available)
     * @return string|null
     */
    public function code(): ?string
    {
        if (is_object($this->_office) && property_exists($this->_office, 'code')) return $this->_office->code;
        if (is_array($this->_office) && isset($this->_office['code'])) return $this->_office['code'];
        return null;
    }

    /**
     * SEO-friendly handle for URLs
     * @return string|null
     */
    public function handle(): ?string
    {
        $name = $this->name();
        return $name ? strtolower(preg_replace('/[^a-z0-9]+/', '-', trim($name))) : null;
    }

    /**
     * Office type/classification (e.g., post, courier, pickup)
     * @return string|null
     */
    public function type(): ?string
    {
        if (is_object($this->_office) && property_exists($this->_office, 'type')) return $this->_office->type;
        if (is_array($this->_office) && isset($this->_office['type'])) return $this->_office['type'];
        return null;
    }

    /**
     * Office address (string, if available)
     * @return string|null
     */
    public function address(): ?string
    {
        if (is_object($this->_office) && property_exists($this->_office, 'address')) return $this->_office->address;
        if (is_array($this->_office) && isset($this->_office['address'])) return $this->_office['address'];
        return null;
    }

    /**
     * City name (if available)
     * @return string|null
     */
    public function city(): ?string
    {
        if (is_object($this->_office) && property_exists($this->_office, 'city')) return $this->_office->city;
        if (is_array($this->_office) && isset($this->_office['city'])) return $this->_office['city'];
        return null;
    }

    /**
     * State/region name (if available)
     * @return string|null
     */
    public function state(): ?string
    {
        if (is_object($this->_office) && property_exists($this->_office, 'state')) return $this->_office->state;
        if (is_array($this->_office) && isset($this->_office['state'])) return $this->_office['state'];
        return null;
    }

    /**
     * Country name (if available)
     * @return string|null
     */
    public function country(): ?string
    {
        if (is_object($this->_office) && property_exists($this->_office, 'country')) return $this->_office->country;
        if (is_array($this->_office) && isset($this->_office['country'])) return $this->_office['country'];
        return null;
    }

    /**
     * Postal code (if available)
     * @return string|null
     */
    public function post_code(): ?string
    {
        if (is_object($this->_office) && property_exists($this->_office, 'post_code')) return $this->_office->post_code;
        if (is_array($this->_office) && isset($this->_office['post_code'])) return $this->_office['post_code'];
        return null;
    }

    /**
     * Latitude (if available)
     * @return float|null
     */
    public function latitude(): ?float
    {
        if (is_object($this->_office) && property_exists($this->_office, 'latitude')) return (float)$this->_office->latitude;
        if (is_array($this->_office) && isset($this->_office['latitude'])) return (float)$this->_office['latitude'];
        return null;
    }

    /**
     * Longitude (if available)
     * @return float|null
     */
    public function longitude(): ?float
    {
        if (is_object($this->_office) && property_exists($this->_office, 'longitude')) return (float)$this->_office->longitude;
        if (is_array($this->_office) && isset($this->_office['longitude'])) return (float)$this->_office['longitude'];
        return null;
    }

    /**
     * Google Maps URL (if coordinates available)
     * @return string|null
     */
    public function google_maps_url(): ?string
    {
        $lat = $this->latitude();
        $lng = $this->longitude();
        if ($lat && $lng) {
            return "https://maps.google.com/?q={$lat},{$lng}";
        }
        return null;
    }

    /**
     * Working hours (if available)
     * @return string|null
     */
    public function working_hours(): ?string
    {
        if (is_object($this->_office) && property_exists($this->_office, 'working_hours')) return $this->_office->working_hours;
        if (is_array($this->_office) && isset($this->_office['working_hours'])) return $this->_office['working_hours'];
        return null;
    }

    /**
     * Contact phone (if available)
     * @return string|null
     */
    public function phone(): ?string
    {
        if (is_object($this->_office) && property_exists($this->_office, 'phone')) return $this->_office->phone;
        if (is_array($this->_office) && isset($this->_office['phone'])) return $this->_office['phone'];
        return null;
    }

    /**
     * Email (if available)
     * @return string|null
     */
    public function email(): ?string
    {
        if (is_object($this->_office) && property_exists($this->_office, 'email')) return $this->_office->email;
        if (is_array($this->_office) && isset($this->_office['email'])) return $this->_office['email'];
        return null;
    }

    /**
     * UI CSS class for office type
     * @return string|null
     */
    public function css_class(): ?string
    {
        $type = $this->type();
        return $type ? 'office-type-' . strtolower(preg_replace('/[^a-z0-9]+/', '-', $type)) : null;
    }

    /**
     * String representation (name)
     * @return string
     */
    public function __toString(): string
    {
        return (string) $this->display_name();
    }

    /**
     * Get the office operational status
     * @return string|null
     */
    public function status(): ?string
    {
        if (is_object($this->_office) && property_exists($this->_office, 'status')) return $this->_office->status;
        if (is_array($this->_office) && isset($this->_office['status'])) return $this->_office['status'];
        return null;
    }

    /**
     * Get structured business hours (if available)
     * @return array|null
     */
    public function business_hours(): ?array
    {
        $hours = null;
        if (is_object($this->_office) && property_exists($this->_office, 'business_hours')) $hours = $this->_office->business_hours;
        if (is_array($this->_office) && isset($this->_office['business_hours'])) $hours = $this->_office['business_hours'];
        if (is_string($hours)) {
            // Try to decode JSON or parse as array
            $decoded = json_decode($hours, true);
            if (is_array($decoded)) return $decoded;
            return [$hours];
        }
        return is_array($hours) ? $hours : null;
    }

    /**
     * Is the office currently open? (basic check based on working_hours)
     * @return bool|null
     */
    public function is_open(): ?bool
    {
        $hours = $this->working_hours();
        if (!$hours) return null;
        if (stripos($hours, '24/7') !== false) return true;
        if (stripos($hours, 'closed') !== false) return false;
        // For more advanced logic, parse hours and compare to current time
        return null;
    }

    /**
     * Does the office have parking?
     * @return bool|null
     */
    public function has_parking(): ?bool
    {
        if (is_object($this->_office) && property_exists($this->_office, 'has_parking')) return (bool)$this->_office->has_parking;
        if (is_array($this->_office) && isset($this->_office['has_parking'])) return (bool)$this->_office['has_parking'];
        return null;
    }

    /**
     * Is the office accessible (wheelchair, etc)?
     * @return bool|null
     */
    public function is_accessible(): ?bool
    {
        if (is_object($this->_office) && property_exists($this->_office, 'is_accessible')) return (bool)$this->_office->is_accessible;
        if (is_array($this->_office) && isset($this->_office['is_accessible'])) return (bool)$this->_office['is_accessible'];
        return null;
    }

    /**
     * Does the office have WiFi?
     * @return bool|null
     */
    public function has_wifi(): ?bool
    {
        if (is_object($this->_office) && property_exists($this->_office, 'has_wifi')) return (bool)$this->_office->has_wifi;
        if (is_array($this->_office) && isset($this->_office['has_wifi'])) return (bool)$this->_office['has_wifi'];
        return null;
    }

    /**
     * Does the office have ATM?
     * @return bool|null
     */
    public function has_atm(): ?bool
    {
        if (is_object($this->_office) && property_exists($this->_office, 'has_atm')) return (bool)$this->_office->has_atm;
        if (is_array($this->_office) && isset($this->_office['has_atm'])) return (bool)$this->_office['has_atm'];
        return null;
    }

    /**
     * Does the office have restroom?
     * @return bool|null
     */
    public function has_restroom(): ?bool
    {
        if (is_object($this->_office) && property_exists($this->_office, 'has_restroom')) return (bool)$this->_office->has_restroom;
        if (is_array($this->_office) && isset($this->_office['has_restroom'])) return (bool)$this->_office['has_restroom'];
        return null;
    }

    /**
     * Does the office have 24/7 access?
     * @return bool|null
     */
    public function has_24_7_access(): ?bool
    {
        $hours = $this->working_hours();
        return $hours && stripos($hours, '24/7') !== false;
    }

    /**
     * Get a summary of available services (if available)
     * @return array|null
     */
    public function services(): ?array
    {
        if (is_object($this->_office) && property_exists($this->_office, 'services')) return (array)$this->_office->services;
        if (is_array($this->_office) && isset($this->_office['services'])) return (array)$this->_office['services'];
        return null;
    }

    /**
     * Get the collection of items as a plain array (expanded)
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'status' => $this->status(),
            'business_hours' => $this->business_hours(),
            'is_open' => $this->is_open(),
            'has_parking' => $this->has_parking(),
            'is_accessible' => $this->is_accessible(),
            'has_wifi' => $this->has_wifi(),
            'has_atm' => $this->has_atm(),
            'has_restroom' => $this->has_restroom(),
            'has_24_7_access' => $this->has_24_7_access(),
            'services' => $this->services(),
        ];
    }

}
