<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Customer\Address;

use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use Omniship\Address\State as OmnishipState;

/**
 * Represents a state/province in address components with comprehensive
 * Shopify compatibility and advanced administrative area intelligence features.
 * 
 * Provides complete state/province object functionality including:
 * - Standard Shopify state/province properties and methods
 * - Administrative classification and hierarchy information
 * - Geographic boundaries and regional data
 * - Economic indicators and demographic information
 * - Timezone and postal code integration
 * - Political structure and governance details
 * - Multi-language name support
 * - Business intelligence and market analysis
 * 
 * <AUTHOR> LLC
 * @copyright CloudCart LLC
 * @since 2.0.0 Enhanced with full Shopify compatibility
 */
class State extends AbstractDrop
{
    /**
     * Administrative type constants
     */
    public const TYPE_STATE = 'state';
    public const TYPE_PROVINCE = 'province';
    public const TYPE_TERRITORY = 'territory';
    public const TYPE_REGION = 'region';
    public const TYPE_DISTRICT = 'district';
    public const TYPE_DEPARTMENT = 'department';
    public const TYPE_PREFECTURE = 'prefecture';
    public const TYPE_CANTON = 'canton';
    public const TYPE_COUNTY = 'county';
    public const TYPE_OBLAST = 'oblast';
    public const TYPE_EMIRATE = 'emirate';
    public const TYPE_BUNDESLAND = 'bundesland';

    /**
     * Economic classification constants
     */
    public const ECONOMY_METROPOLITAN = 'metropolitan';
    public const ECONOMY_URBAN = 'urban';
    public const ECONOMY_RURAL = 'rural';
    public const ECONOMY_INDUSTRIAL = 'industrial';
    public const ECONOMY_AGRICULTURAL = 'agricultural';
    public const ECONOMY_TOURISM = 'tourism';
    public const ECONOMY_MINING = 'mining';
    public const ECONOMY_SERVICE = 'service';

    /**
     * Political status constants
     */
    public const STATUS_AUTONOMOUS = 'autonomous';
    public const STATUS_FEDERAL = 'federal';
    public const STATUS_DEPENDENT = 'dependent';
    public const STATUS_SPECIAL = 'special';
    public const STATUS_UNINCORPORATED = 'unincorporated';

    /**
     * @var OmnishipState $_state
     */
    protected \Omniship\Address\State $_state;

    /**
     * @param \Omniship\Address\State $state
     */
    public function __construct(OmnishipState $state)
    {
        $this->_state = $state;
    }

    /**
     * Get the state/province ID
     * Shopify compatibility: Standard id property
     * 
     * @return int|string|null The state identifier
     */
    public function id(): int|string|null
    {
        if (method_exists($this->_state, 'getId')) {
            return $this->_state->getId();
        }
        
        return $this->iso2();
    }

    /**
     * Get the state/province name
     * Shopify compatibility: Standard name property
     * 
     * @return string|null The state/province name
     */
    public function name(): ?string
    {
        return $this->_state->getName();
    }

    /**
     * Get the state/province title (alias for name)
     * Shopify compatibility: Standard title property
     * 
     * @return string|null The state/province title
     */
    public function title(): ?string
    {
        return $this->name();
    }

    /**
     * Get the state/province value (alias for name)
     * Shopify compatibility: Standard value property
     * 
     * @return string|null The state/province value
     */
    public function value(): ?string
    {
        return $this->name();
    }

    /**
     * Get the ISO 3166-2 subdivision code
     * Shopify compatibility: Standard code property
     * 
     * @return int|string|null Two-letter state/province code
     */
    public function iso2(): int|string|null
    {
        return $this->_state->getIso2();
    }

    /**
     * Get the province code (alias for iso2)
     * Shopify compatibility: Standard province_code property
     * 
     * @return int|string|null State/province code
     */
    public function province_code(): int|string|null
    {
        return $this->iso2();
    }

    /**
     * Get the state code (alternative alias)
     * 
     * @return int|string|null State/province code
     */
    public function state_code(): int|string|null
    {
        return $this->iso2();
    }

    /**
     * Get the administrative code (general alias)
     * 
     * @return int|string|null Administrative code
     */
    public function code(): int|string|null
    {
        return $this->iso2();
    }

    /**
     * Get the display name for the state/province
     * 
     * @return string The formatted display name
     */
    public function display_name(): string
    {
        return $this->name() ?? 'Unknown State/Province';
    }

    /**
     * Get the full official name of the state/province
     * 
     * @return string|null The official administrative name
     */
    public function official_name(): ?string
    {
        if (method_exists($this->_state, 'getOfficialName')) {
            return $this->_state->getOfficialName();
        }
        
        // Construct official name based on type and name
        $name = $this->name();
        $type = $this->administrative_type();
        
        if ($name && $type && $type !== 'state') {
            return ucfirst($type) . ' of ' . $name;
        }
        
        return $name;
    }

    /**
     * Get the administrative type of this subdivision
     * 
     * @return string The administrative classification
     */
    public function administrative_type(): string
    {
        if (method_exists($this->_state, 'getType')) {
            return $this->_state->getType();
        }
        
        // Basic type mapping based on country and name patterns
        $countryCode = $this->country_code();
        $name = $this->name();
        
        if ($countryCode) {
            // Country-specific administrative types
            $typeMap = [
                'US' => static::TYPE_STATE,
                'CA' => static::TYPE_PROVINCE,
                'AU' => static::TYPE_STATE,
                'DE' => static::TYPE_BUNDESLAND,
                'FR' => static::TYPE_REGION,
                'IT' => static::TYPE_REGION,
                'ES' => static::TYPE_REGION,
                'RU' => static::TYPE_OBLAST,
                'CN' => static::TYPE_PROVINCE,
                'IN' => static::TYPE_STATE,
                'BR' => static::TYPE_STATE,
                'MX' => static::TYPE_STATE,
                'AR' => static::TYPE_PROVINCE,
                'AE' => static::TYPE_EMIRATE,
                'CH' => static::TYPE_CANTON,
                'BG' => static::TYPE_REGION,
            ];
            
            if (isset($typeMap[$countryCode])) {
                return $typeMap[$countryCode];
            }
        }
        
        // Name pattern-based detection
        if ($name) {
            if (str_contains(strtolower($name), 'territory')) return static::TYPE_TERRITORY;
            if (str_contains(strtolower($name), 'district')) return static::TYPE_DISTRICT;
            if (str_contains(strtolower($name), 'county')) return static::TYPE_COUNTY;
            if (str_contains(strtolower($name), 'department')) return static::TYPE_DEPARTMENT;
        }
        
        return static::TYPE_STATE; // Default fallback
    }

    /**
     * Get the country code for this state/province
     * 
     * @return string|null The parent country code
     */
    public function country_code(): ?string
    {
        if (method_exists($this->_state, 'getCountryCode')) {
            return $this->_state->getCountryCode();
        }
        
        // Extract from full ISO code if available
        $iso2 = $this->iso2();
        if (is_string($iso2) && str_contains($iso2, '-')) {
            return explode('-', $iso2)[0];
        }
        
        return null;
    }

    /**
     * Get the country object for this state/province
     * 
     * @return Country|null The parent country object
     */
    public function country(): ?Country
    {
        if (method_exists($this->_state, 'getCountry')) {
            $country = $this->_state->getCountry();
            if ($country) {
                return new Country($country);
            }
        }
        
        return null;
    }

    /**
     * Get the capital city of the state/province
     * 
     * @return string|null The capital city name
     */
    public function capital(): ?string
    {
        if (method_exists($this->_state, 'getCapital')) {
            return $this->_state->getCapital();
        }
        
        // Basic capital mapping for major states/provinces
        $capitalMap = [
            // US States
            'US-CA' => 'Sacramento',
            'US-NY' => 'Albany',
            'US-TX' => 'Austin',
            'US-FL' => 'Tallahassee',
            'US-IL' => 'Springfield',
            'US-PA' => 'Harrisburg',
            'US-OH' => 'Columbus',
            'US-GA' => 'Atlanta',
            'US-NC' => 'Raleigh',
            'US-MI' => 'Lansing',
            'US-NJ' => 'Trenton',
            'US-VA' => 'Richmond',
            'US-WA' => 'Olympia',
            'US-AZ' => 'Phoenix',
            'US-MA' => 'Boston',
            'US-TN' => 'Nashville',
            'US-IN' => 'Indianapolis',
            'US-MO' => 'Jefferson City',
            'US-MD' => 'Annapolis',
            'US-WI' => 'Madison',
            
            // Canadian Provinces
            'CA-ON' => 'Toronto',
            'CA-QC' => 'Quebec City',
            'CA-BC' => 'Victoria',
            'CA-AB' => 'Edmonton',
            'CA-MB' => 'Winnipeg',
            'CA-SK' => 'Regina',
            'CA-NS' => 'Halifax',
            'CA-NB' => 'Fredericton',
            'CA-NL' => 'St. John\'s',
            'CA-PE' => 'Charlottetown',
            'CA-NT' => 'Yellowknife',
            'CA-YT' => 'Whitehorse',
            'CA-NU' => 'Iqaluit',
            
            // Australian States
            'AU-NSW' => 'Sydney',
            'AU-VIC' => 'Melbourne',
            'AU-QLD' => 'Brisbane',
            'AU-WA' => 'Perth',
            'AU-SA' => 'Adelaide',
            'AU-TAS' => 'Hobart',
            'AU-ACT' => 'Canberra',
            'AU-NT' => 'Darwin',
            
            // German States
            'DE-BW' => 'Stuttgart',
            'DE-BY' => 'Munich',
            'DE-BE' => 'Berlin',
            'DE-BB' => 'Potsdam',
            'DE-HB' => 'Bremen',
            'DE-HH' => 'Hamburg',
            'DE-HE' => 'Wiesbaden',
            'DE-MV' => 'Schwerin',
            'DE-NI' => 'Hanover',
            'DE-NW' => 'Düsseldorf',
            'DE-RP' => 'Mainz',
            'DE-SL' => 'Saarbrücken',
            'DE-SN' => 'Dresden',
            'DE-ST' => 'Magdeburg',
            'DE-SH' => 'Kiel',
            'DE-TH' => 'Erfurt',
        ];
        
        $fullCode = $this->country_code() . '-' . $this->iso2();
        return $capitalMap[$fullCode] ?? null;
    }

    /**
     * Get the population of the state/province
     * 
     * @return int|null The population count
     */
    public function population(): ?int
    {
        if (method_exists($this->_state, 'getPopulation')) {
            return $this->_state->getPopulation();
        }
        
        return null;
    }

    /**
     * Get formatted population with thousands separators
     * 
     * @return string|null Formatted population
     */
    public function population_formatted(): ?string
    {
        $population = $this->population();
        return $population ? number_format($population) : null;
    }

    /**
     * Get the area of the state/province in square kilometers
     * 
     * @return float|null The area in km²
     */
    public function area(): ?float
    {
        if (method_exists($this->_state, 'getArea')) {
            return $this->_state->getArea();
        }
        
        return null;
    }

    /**
     * Get formatted area with unit
     * 
     * @param string $unit Unit of measurement ('km2', 'mi2')
     * @return string|null Formatted area with unit
     */
    public function area_formatted(string $unit = 'km2'): ?string
    {
        $area = $this->area();
        if (!$area) return null;
        
        if ($unit === 'mi2') {
            // Convert to square miles
            $area = $area * 0.386102;
            return number_format($area, 0) . ' sq mi';
        }
        
        return number_format($area, 0) . ' km²';
    }

    /**
     * Get the population density (people per km²)
     * 
     * @return float|null Population density
     */
    public function population_density(): ?float
    {
        $population = $this->population();
        $area = $this->area();
        
        if ($population && $area && $area > 0) {
            return round($population / $area, 2);
        }
        
        return null;
    }

    /**
     * Get the primary timezone for this state/province
     * 
     * @return string|null The timezone identifier
     */
    public function timezone(): ?string
    {
        if (method_exists($this->_state, 'getTimezone')) {
            return $this->_state->getTimezone();
        }
        
        // Basic timezone mapping for major states/provinces
        $timezoneMap = [
            // US States (primary timezone)
            'US-CA' => 'America/Los_Angeles',
            'US-NY' => 'America/New_York',
            'US-TX' => 'America/Chicago',
            'US-FL' => 'America/New_York',
            'US-IL' => 'America/Chicago',
            'US-PA' => 'America/New_York',
            'US-OH' => 'America/New_York',
            'US-GA' => 'America/New_York',
            'US-NC' => 'America/New_York',
            'US-MI' => 'America/New_York',
            'US-NJ' => 'America/New_York',
            'US-VA' => 'America/New_York',
            'US-WA' => 'America/Los_Angeles',
            'US-AZ' => 'America/Phoenix',
            'US-MA' => 'America/New_York',
            'US-TN' => 'America/Chicago',
            'US-IN' => 'America/New_York',
            'US-MO' => 'America/Chicago',
            'US-MD' => 'America/New_York',
            'US-WI' => 'America/Chicago',
            
            // Canadian Provinces
            'CA-ON' => 'America/Toronto',
            'CA-QC' => 'America/Montreal',
            'CA-BC' => 'America/Vancouver',
            'CA-AB' => 'America/Edmonton',
            'CA-MB' => 'America/Winnipeg',
            'CA-SK' => 'America/Regina',
            'CA-NS' => 'America/Halifax',
            'CA-NB' => 'America/Moncton',
            'CA-NL' => 'America/St_Johns',
            'CA-PE' => 'America/Halifax',
            'CA-NT' => 'America/Yellowknife',
            'CA-YT' => 'America/Whitehorse',
            'CA-NU' => 'America/Iqaluit',
            
            // Australian States
            'AU-NSW' => 'Australia/Sydney',
            'AU-VIC' => 'Australia/Melbourne',
            'AU-QLD' => 'Australia/Brisbane',
            'AU-WA' => 'Australia/Perth',
            'AU-SA' => 'Australia/Adelaide',
            'AU-TAS' => 'Australia/Hobart',
            'AU-ACT' => 'Australia/Sydney',
            'AU-NT' => 'Australia/Darwin',
        ];
        
        $fullCode = $this->country_code() . '-' . $this->iso2();
        return $timezoneMap[$fullCode] ?? null;
    }

    /**
     * Get postal codes that belong to this state/province
     * 
     * @return array Array of postal code ranges or patterns
     */
    public function postal_codes(): array
    {
        if (method_exists($this->_state, 'getPostalCodes')) {
            return $this->_state->getPostalCodes();
        }
        
        return [];
    }

    /**
     * Get the economic classification of the state/province
     * 
     * @return string Economic classification
     */
    public function economic_classification(): string
    {
        if (method_exists($this->_state, 'getEconomicClassification')) {
            return $this->_state->getEconomicClassification();
        }
        
        // Basic economic classification based on known characteristics
        $name = strtolower($this->name() ?? '');
        $code = $this->iso2();
        
        // Metropolitan areas
        $metropolitan = ['CA', 'NY', 'TX', 'FL', 'ON', 'QC', 'BC', 'NSW', 'VIC'];
        if (in_array($code, $metropolitan)) {
            return static::ECONOMY_METROPOLITAN;
        }
        
        // Industrial regions
        if (str_contains($name, 'ruhr') || str_contains($name, 'industrial')) {
            return static::ECONOMY_INDUSTRIAL;
        }
        
        // Tourism-focused areas
        if (str_contains($name, 'hawaii') || str_contains($name, 'florida') || str_contains($name, 'tourism')) {
            return static::ECONOMY_TOURISM;
        }
        
        // Agricultural areas
        if (str_contains($name, 'iowa') || str_contains($name, 'nebraska') || str_contains($name, 'kansas')) {
            return static::ECONOMY_AGRICULTURAL;
        }
        
        return static::ECONOMY_SERVICE; // Default
    }

    /**
     * Get the political status of the state/province
     * 
     * @return string Political status classification
     */
    public function political_status(): string
    {
        if (method_exists($this->_state, 'getPoliticalStatus')) {
            return $this->_state->getPoliticalStatus();
        }
        
        $name = strtolower($this->name() ?? '');
        $countryCode = $this->country_code();
        
        // Special administrative regions
        if (str_contains($name, 'autonomous') || str_contains($name, 'special')) {
            return static::STATUS_AUTONOMOUS;
        }
        
        // Territories
        if (str_contains($name, 'territory') || in_array($this->administrative_type(), [static::TYPE_TERRITORY])) {
            return static::STATUS_DEPENDENT;
        }
        
        // Federal jurisdictions
        if ($countryCode === 'US' && in_array($this->iso2(), ['DC'])) {
            return static::STATUS_SPECIAL;
        }
        
        return static::STATUS_FEDERAL; // Default for most states/provinces
    }

    /**
     * Get the abbreviation for the state/province
     * 
     * @return string|null Short abbreviation
     */
    public function abbreviation(): ?string
    {
        // ISO2 code often serves as abbreviation
        $code = $this->iso2();
        if (is_string($code) && strlen($code) <= 3) {
            return strtoupper($code);
        }
        
        // Generate abbreviation from name
        $name = $this->name();
        if ($name) {
            $words = explode(' ', $name);
            if (count($words) > 1) {
                return strtoupper(substr($words[0], 0, 1) . substr($words[1], 0, 1));
            } else {
                return strtoupper(substr($name, 0, 2));
            }
        }
        
        return null;
    }

    /**
     * Get the largest city in the state/province
     * 
     * @return string|null The largest city name
     */
    public function largest_city(): ?string
    {
        if (method_exists($this->_state, 'getLargestCity')) {
            return $this->_state->getLargestCity();
        }
        
        // Basic mapping for major states/provinces
        $largestCityMap = [
            // US States
            'US-CA' => 'Los Angeles',
            'US-NY' => 'New York City',
            'US-TX' => 'Houston',
            'US-FL' => 'Jacksonville',
            'US-IL' => 'Chicago',
            'US-PA' => 'Philadelphia',
            'US-OH' => 'Columbus',
            'US-GA' => 'Atlanta',
            'US-NC' => 'Charlotte',
            'US-MI' => 'Detroit',
            'US-NJ' => 'Newark',
            'US-VA' => 'Virginia Beach',
            'US-WA' => 'Seattle',
            'US-AZ' => 'Phoenix',
            'US-MA' => 'Boston',
            'US-TN' => 'Memphis',
            'US-IN' => 'Indianapolis',
            'US-MO' => 'Kansas City',
            'US-MD' => 'Baltimore',
            'US-WI' => 'Milwaukee',
            
            // Canadian Provinces
            'CA-ON' => 'Toronto',
            'CA-QC' => 'Montreal',
            'CA-BC' => 'Vancouver',
            'CA-AB' => 'Calgary',
            'CA-MB' => 'Winnipeg',
            'CA-SK' => 'Saskatoon',
            'CA-NS' => 'Halifax',
            'CA-NB' => 'Saint John',
            'CA-NL' => 'St. John\'s',
            'CA-PE' => 'Charlottetown',
            
            // Australian States
            'AU-NSW' => 'Sydney',
            'AU-VIC' => 'Melbourne',
            'AU-QLD' => 'Brisbane',
            'AU-WA' => 'Perth',
            'AU-SA' => 'Adelaide',
            'AU-TAS' => 'Hobart',
        ];
        
        $fullCode = $this->country_code() . '-' . $this->iso2();
        return $largestCityMap[$fullCode] ?? $this->capital();
    }

    /**
     * Check if this is an autonomous region
     * 
     * @return bool True if autonomous
     */
    public function is_autonomous(): bool
    {
        return $this->political_status() === static::STATUS_AUTONOMOUS;
    }

    /**
     * Check if this is a territory
     * 
     * @return bool True if territory
     */
    public function is_territory(): bool
    {
        return $this->administrative_type() === static::TYPE_TERRITORY;
    }

    /**
     * Check if this is a metropolitan area
     * 
     * @return bool True if metropolitan
     */
    public function is_metropolitan(): bool
    {
        return $this->economic_classification() === static::ECONOMY_METROPOLITAN;
    }

    /**
     * Check if this state/province has coastal access
     * 
     * @return bool True if coastal
     */
    public function is_coastal(): bool
    {
        if (method_exists($this->_state, 'isCoastal')) {
            return $this->_state->isCoastal();
        }
        
        // Basic coastal detection for major states/provinces
        $coastalRegions = [
            // US coastal states
            'CA', 'WA', 'OR', 'AK', 'HI', 'TX', 'LA', 'MS', 'AL', 'FL', 'GA', 'SC', 'NC', 'VA', 'MD', 'DE', 'NJ', 'NY', 'CT', 'RI', 'MA', 'NH', 'ME',
            // Canadian coastal provinces
            'BC', 'YT', 'NT', 'NU', 'MB', 'ON', 'QC', 'NB', 'PE', 'NS', 'NL',
            // Australian coastal states
            'WA', 'NT', 'QLD', 'NSW', 'VIC', 'TAS', 'SA',
        ];
        
        return in_array($this->iso2(), $coastalRegions);
    }

    /**
     * Get URL handle for the state/province (SEO-friendly)
     * 
     * @return string URL-safe handle
     */
    public function handle(): string
    {
        $name = $this->name() ?? 'unknown-state';
        return \Illuminate\Support\Str::slug($name);
    }

    /**
     * Get Google Maps URL for this state/province
     * 
     * @return string Google Maps URL
     */
    public function google_maps_url(): string
    {
        $stateName = urlencode($this->display_name());
        return "https://www.google.com/maps/search/?api=1&query={$stateName}";
    }

    /**
     * Get Wikipedia URL for this state/province
     * 
     * @param string $language Language code for Wikipedia (default: 'en')
     * @return string Wikipedia URL
     */
    public function wikipedia_url(string $language = 'en'): string
    {
        $stateName = str_replace(' ', '_', $this->display_name());
        return "https://{$language}.wikipedia.org/wiki/{$stateName}";
    }

    /**
     * Get CSS class for state/province styling based on attributes
     * 
     * @return string CSS class string
     */
    public function css_class(): string
    {
        $classes = ['state', 'province', 'administrative-area'];
        
        // Add administrative type class
        $classes[] = 'type-' . str_replace('_', '-', $this->administrative_type());
        
        // Add economic classification class
        $classes[] = 'economy-' . str_replace('_', '-', $this->economic_classification());
        
        // Add political status class
        $classes[] = 'status-' . str_replace('_', '-', $this->political_status());
        
        // Add special classes
        if ($this->is_coastal()) {
            $classes[] = 'state-coastal';
        }
        
        if ($this->is_metropolitan()) {
            $classes[] = 'state-metropolitan';
        }
        
        if ($this->is_autonomous()) {
            $classes[] = 'state-autonomous';
        }
        
        if ($this->is_territory()) {
            $classes[] = 'state-territory';
        }
        
        // Add country class
        if ($countryCode = $this->country_code()) {
            $classes[] = 'country-' . strtolower($countryCode);
        }
        
        return implode(' ', $classes);
    }

    /**
     * Get the collection of items as a plain array
     *
     * @return array Complete state/province data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'title' => $this->title(),
            'value' => $this->value(),
            'display_name' => $this->display_name(),
            'official_name' => $this->official_name(),
            'iso2' => $this->iso2(),
            'province_code' => $this->province_code(),
            'state_code' => $this->state_code(),
            'code' => $this->code(),
            'abbreviation' => $this->abbreviation(),
            'administrative_type' => $this->administrative_type(),
            'country_code' => $this->country_code(),
            'capital' => $this->capital(),
            'largest_city' => $this->largest_city(),
            'population' => $this->population(),
            'population_formatted' => $this->population_formatted(),
            'area' => $this->area(),
            'area_formatted' => $this->area_formatted(),
            'population_density' => $this->population_density(),
            'timezone' => $this->timezone(),
            'postal_codes' => $this->postal_codes(),
            'economic_classification' => $this->economic_classification(),
            'political_status' => $this->political_status(),
            'handle' => $this->handle(),
            'is_autonomous' => $this->is_autonomous(),
            'is_territory' => $this->is_territory(),
            'is_metropolitan' => $this->is_metropolitan(),
            'is_coastal' => $this->is_coastal(),
            'google_maps_url' => $this->google_maps_url(),
            'css_class' => $this->css_class(),
        ];
    }

    /**
     * String representation of the state/province
     * 
     * @return string State/province name
     */
    public function __toString(): string
    {
        return $this->display_name();
    }
}
