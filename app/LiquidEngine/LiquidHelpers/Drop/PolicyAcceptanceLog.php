<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Modules\Apps\Others\Gdpr\Models\PolicyAcceptanceLog as PolicyAcceptanceLogModel;

/**
 * PolicyAcceptanceLog Drop class for handling GDPR policy acceptance logs in Liquid templates.
 *
 * This class represents a GDPR policy acceptance log entry in the Liquid template system,
 * providing access to policy details, acceptance timestamps, and related content. It wraps
 * the PolicyAcceptanceLogModel and exposes log-specific details for use in Liquid templates.
 *
 * Usage:
 *   {{ policy_log.id }}
 *   {{ policy_log.name }}
 *   {{ policy_log.created }}
 *   {{ policy_log.url }}
 *
 * Properties:
 * - id: int
 * - name: string|null
 * - content: string|null
 * - created: Date
 * - updated: Date
 * - url: string
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class PolicyAcceptanceLog extends AbstractDrop
{
    /**
     * The underlying policy acceptance log model instance.
     * Contains the raw log data from the database.
     *
     * @var PolicyAcceptanceLogModel
     */
    protected PolicyAcceptanceLogModel $_log;

    /**
     * Create a new PolicyAcceptanceLog instance.
     * Initializes the drop with a PolicyAcceptanceLogModel instance.
     *
     * @param PolicyAcceptanceLogModel $log The policy acceptance log model instance
     */
    public function __construct(PolicyAcceptanceLogModel $log)
    {
        $this->_log = $log;
    }

    /**
     * Get the unique identifier of the policy acceptance log.
     * Returns the primary key of the underlying log model.
     *
     * @return int The log ID
     */
    public function id(): int
    {
        return (int) $this->_log->id;
    }

    /**
     * Get the name/title of the policy.
     * Returns null if the content relation is not loaded or title is not available.
     *
     * @return string|null The policy name
     */
    public function name(): ?string
    {
        if ($this->_log->relationLoaded('content') && ($title = ($this->_log->content->title ?? null))) {
            return (string) $title;
        }

        return null;
    }

    /**
     * Get the content of the policy.
     * Returns null if the content relation is not loaded or content is not available.
     *
     * @return string|null The policy content
     */
    public function content(): ?string
    {
        if ($this->_log->relationLoaded('content') && ($content = ($this->_log->content->content ?? null))) {
            return (string) $content;
        }

        return null;
    }

    /**
     * Get the creation date of the policy acceptance log.
     * Returns a Date object representing when the log was created.
     *
     * @return Date The creation date
     */
    public function created(): Date
    {
        return new Date($this->_log->created_at);
    }

    /**
     * Get the last update date of the policy acceptance log.
     * Returns a Date object representing when the log was last updated.
     *
     * @return Date The last update date
     */
    public function updated(): Date
    {
        return new Date($this->_log->updated_at);
    }

    /**
     * Get the URL for viewing the policy details.
     * Returns the route to view the policy acceptance log details.
     *
     * @return string The policy details URL
     */
    public function url(): string
    {
        return route('site.account.gdpr.view', ['gdpr_id' => $this->id()]);
    }

    /**
     * Get the collection of items as a plain array.
     * Returns an array representation of the policy acceptance log with all its properties.
     * This method is used by Liquid templates to access the log data.
     *
     * @return array{
     *     id: int,
     *     name: string|null,
     *     content: string|null,
     *     created: Date,
     *     updated: Date,
     *     url: string
     * } The policy acceptance log data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'content' => $this->content(),
            'created' => $this->created(),
            'updated' => $this->updated(),
            'url' => $this->url(),
        ];
    }
}
