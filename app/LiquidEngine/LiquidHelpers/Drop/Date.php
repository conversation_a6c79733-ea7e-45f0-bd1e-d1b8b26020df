<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Common\DateTimeFormat;
use Carbon\Carbon;

/**
 * Class Date
 * 
 * Provides date and time formatting functionality.
 * Implements Shopify-compatible date methods.
 * 
 * @property-read Carbon $_date The underlying Carbon date instance
 * @property-read string $date Formatted date string
 * @property-read string $time Formatted time string
 * @property-read string $datetime Formatted date and time string
 * @property-read int $timestamp Unix timestamp
 * @property-read string $date_format Current date format
 * @property-read string $time_format Current time format
 * @property-read Timezone $timezone Timezone information
 */
class Date extends AbstractDrop
{
    /**
     * The underlying Carbon date instance.
     *
     * @var Carbon
     */
    protected Carbon $_date;

    /**
     * Create a new Date instance.
     *
     * @param Carbon $date The Carbon date instance
     */
    public function __construct(Carbon $date)
    {
        $this->_date = $date;
    }

    /**
     * Get the formatted date string.
     *
     * @return string
     */
    public function date(): string
    {
        return DateTimeFormat::getFormat($this->_date, '{date}');
    }

    /**
     * Get the current date format.
     *
     * @return string
     */
    public function date_format(): string
    {
        return DateTimeFormat::getSiteCurrentDateFormat()['format'];
    }

    /**
     * Get the formatted time string.
     *
     * @return string
     */
    public function time(): string
    {
        return DateTimeFormat::getFormat($this->_date, '{time}');
    }

    /**
     * Get the current time format.
     *
     * @return string
     */
    public function time_format(): string
    {
        return DateTimeFormat::getSiteCurrentTimeFormat()['format'];
    }

    /**
     * Get the formatted date and time string.
     *
     * @return string
     */
    public function datetime(): string
    {
        return DateTimeFormat::getFormat($this->_date);
    }

    /**
     * Get the Unix timestamp.
     *
     * @return int
     */
    public function timestamp(): int
    {
        return $this->_date->timestamp;
    }

    /**
     * Get the timezone information.
     *
     * @return Timezone
     */
    public function timezone(): Timezone
    {
        return new Timezone($this->_date->timezone);
    }

    /**
     * Get accessibility attributes
     * @return array{
     *     aria_label: string,
     *     role: string
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => 'Date and time information',
            'role' => 'status'
        ];
    }

    /**
     * Get structured data for SEO
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'DateTime',
            'dateTime' => $this->_date->toIso8601String(),
            'timezone' => $this->_date->timezone->getName(),
            'format' => [
                'date' => $this->date_format(),
                'time' => $this->time_format()
            ]
        ];
    }

    /**
     * Convert to Shopify format
     * @return array{
     *     date: string,
     *     time: string,
     *     datetime: string,
     *     timestamp: int,
     *     timezone: Timezone,
     *     date_format: string,
     *     time_format: string,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toShopifyArray(): array
    {
        return [
            'date' => $this->date(),
            'time' => $this->time(),
            'datetime' => $this->datetime(),
            'timestamp' => $this->timestamp(),
            'timezone' => $this->timezone(),
            'date_format' => $this->date_format(),
            'time_format' => $this->time_format(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to SEO array format
     * @return array{
     *     date: string,
     *     time: string,
     *     datetime: string,
     *     timestamp: int,
     *     timezone: Timezone,
     *     date_format: string,
     *     time_format: string,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toSeoArray(): array
    {
        return [
            'date' => $this->date(),
            'time' => $this->time(),
            'datetime' => $this->datetime(),
            'timestamp' => $this->timestamp(),
            'timezone' => $this->timezone(),
            'date_format' => $this->date_format(),
            'time_format' => $this->time_format(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to analytics array format
     * @return array{
     *     date: string,
     *     time: string,
     *     datetime: string,
     *     timestamp: int,
     *     timezone: Timezone,
     *     date_format: string,
     *     time_format: string,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toAnalyticsArray(): array
    {
        return [
            'date' => $this->date(),
            'time' => $this->time(),
            'datetime' => $this->datetime(),
            'timestamp' => $this->timestamp(),
            'timezone' => $this->timezone(),
            'date_format' => $this->date_format(),
            'time_format' => $this->time_format(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Get the collection as a plain array.
     *
     * @return array{
     *     date: string,
     *     time: string,
     *     datetime: string,
     *     timestamp: int,
     *     timezone: Timezone,
     *     date_format: string,
     *     time_format: string,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string},
     *     shopify: array{date: string, time: string, datetime: string, timestamp: int, timezone: Timezone, date_format: string, time_format: string, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     seo: array{date: string, time: string, datetime: string, timestamp: int, timezone: Timezone, date_format: string, time_format: string, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     analytics: array{date: string, time: string, datetime: string, timestamp: int, timezone: Timezone, date_format: string, time_format: string, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}}
     * }
     */
    public function toArray(): array
    {
        return [
            'date' => $this->date(),
            'time' => $this->time(),
            'datetime' => $this->datetime(),
            'timestamp' => $this->timestamp(),
            'timezone' => $this->timezone(),
            'date_format' => $this->date_format(),
            'time_format' => $this->time_format(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics' => $this->toAnalyticsArray()
        ];
    }
}
