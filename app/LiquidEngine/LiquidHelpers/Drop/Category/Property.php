<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Category;

use App\Helper\Format;
use App\Models\Category\PropertyOption as CategoryPropertyOptionModel;
use App\Models\Category\Property as CategoryPropertyModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;

/**
 * Property Drop Class
 *
 * This class represents a category property in Liquid templates. It provides access to property data
 * and implements Shopify compatibility, accessibility, and SEO features. It supports various property
 * types including range-based properties with min/max values and step increments.
 *
 * @property-read int $id The property ID
 * @property-read string $name The property name
 * @property-read string $type The property type
 * @property-read int $decimal_points The number of decimal points
 * @property-read string $url_handle The URL handle
 * @property-read bool $has_image Whether the property has an image
 * @property-read float|int|null $step The step increment for range properties
 * @property-read string|null $min The minimum value for range properties
 * @property-read string|null $max The maximum value for range properties
 * @property-read array|null $values The min/max values for range properties
 * @property-read string|null $value_from The minimum value for range properties
 * @property-read string|null $value_to The maximum value for range properties
 * @property-read \App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute $image The property image
 * @property-read array<\App\LiquidEngine\LiquidHelpers\Drop\Category\PropertyOption> $options The property options
 */
class Property extends AbstractDrop
{

    use DropImageToArray;

    protected CategoryPropertyModel $_property;

    /**
     * Property constructor.
     * @param CategoryPropertyModel $property The property model instance
     */
    public function __construct(CategoryPropertyModel $property)
    {
        $this->_property = $property;
    }

    /**
     * Get the property ID
     * @return int
     */
    public function id(): int
    {
        return $this->_property->id;
    }

    /**
     * Get the property name
     * @return string
     */
    public function name(): string
    {
        return $this->_property->name;
    }

    /**
     * Get the property type
     * @return string
     */
    public function type(): string
    {
        return $this->_property->type;
    }

    /**
     * Get the number of decimal points
     * @return int
     */
    public function decimal_points(): int
    {
        return is_numeric($this->_property->dec_points) ? (int)$this->_property->dec_points : 0;
    }

    /**
     * Get the URL handle
     * @return string
     */
    public function url_handle(): string
    {
        return $this->_property->url_handle;
    }

    /**
     * Check if the property has an image
     * @return bool
     */
    public function has_image(): bool
    {
        return $this->_property->hasImage();
    }

    //range type

    /**
     * Get the step increment for range properties
     * @return float|int|null
     */
    public function step(): float|int|null
    {
        if ($this->type() !== 'range' || !$this->_property->relationLoaded('options')) {
            return null;
        }

        if (!is_null($this->_property->step)) {
            return $this->_property->step;
        }

        if ($this->_property->relationLoaded('options')) {
            return findMinDiff($this->_property->options->pluck('value'));
        }

        return null;
    }

    /**
     * Get the minimum value for range properties
     * @return string|null
     */
    public function min(): ?string
    {
        if ($this->type() !== 'range' || !$this->_property->relationLoaded('options')) {
            return null;
        }

        return Format::number_format($this->_property->options->min('value'), $this->decimal_points(), '.', '');
    }

    /**
     * Get the maximum value for range properties
     * @return string|null
     */
    public function max(): ?string
    {
        if ($this->type() !== 'range' || !$this->_property->relationLoaded('options')) {
            return null;
        }

        return Format::number_format($this->_property->options->max('value'), $this->decimal_points(), '.', '');
    }

    /**
     * Get the min/max values for range properties
     * @return array{0: string, 1: string}|null
     */
    public function values(): ?array
    {
        if ($this->type() !== 'range' || !$this->_property->relationLoaded('options')) {
            return null;
        }

        return [
            Format::number_format($this->_property->options->min('value'), $this->decimal_points(), '.', ''),
            Format::number_format($this->_property->options->max('value'), $this->decimal_points(), '.', '')
        ];
    }

    /**
     * Get the minimum value for range properties
     * @return string|null
     */
    public function value_from(): ?string
    {
        if ($this->type() !== 'range' || !$this->_property->relationLoaded('options')) {
            return null;
        }

        return $this->values()[0] ?? null;
    }

    /**
     * Get the maximum value for range properties
     * @return string|null
     */
    public function value_to(): ?string
    {
        if ($this->type() !== 'range' || !$this->_property->relationLoaded('options')) {
            return null;
        }

        return $this->values()[1] ?? null;
    }

    //end range type

    /**
     * Get the property image
     * @return \App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute
     */
    public function image(): \App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute
    {
        $formatter = new UrlImageFormat($this->_property);
        return $formatter->getLiquidImage();
    }

    /**
     * Get the property options
     * @return array<\App\LiquidEngine\LiquidHelpers\Drop\Category\PropertyOption>
     */
    public function options(): array
    {
        if (!$this->_property->relationLoaded('options') || $this->type() === 'range') {
            return [];
        }

        return $this->_property->options
            ->sortBy('sort', SORT_ASC)
            ->map(fn(CategoryPropertyOptionModel $option): PropertyOption => new PropertyOption($option))
            ->values()
            ->all();
    }

    /**
     * Shopify compatibility: handle
     * Returns the property handle (url_handle)
     * @return string
     */
    public function handle(): string
    {
        return $this->url_handle();
    }

    /**
     * Shopify compatibility: title
     * Returns the property title (name)
     * @return string
     */
    public function title(): string
    {
        return $this->name();
    }

    /**
     * Shopify compatibility: description
     * Returns the property description
     * @return string|null
     */
    public function description(): ?string
    {
        return $this->_property->description;
    }

    /**
     * Shopify compatibility: metafields
     * Returns the property metafields as an array
     * @return array<string, mixed>
     */
    public function metafields(): array
    {
        return $this->_property->metafields ?? [];
    }

    /**
     * Shopify compatibility: published_at
     * Returns the published date as a Date Drop
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Date|null
     */
    public function published_at(): ?\App\LiquidEngine\LiquidHelpers\Drop\Date
    {
        return $this->_property->created_at
            ? new \App\LiquidEngine\LiquidHelpers\Drop\Date($this->_property->created_at)
            : null;
    }

    /**
     * Shopify compatibility: updated_at
     * Returns the updated date as a Date Drop
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Date|null
     */
    public function updated_at(): ?\App\LiquidEngine\LiquidHelpers\Drop\Date
    {
        return $this->_property->updated_at
            ? new \App\LiquidEngine\LiquidHelpers\Drop\Date($this->_property->updated_at)
            : null;
    }

    /**
     * Shopify compatibility: sort_order
     * Returns the sort order of the property
     * @return int|null
     */
    public function sort_order(): ?int
    {
        return $this->_property->sort;
    }

    /**
     * Shopify compatibility: template_suffix
     * Returns the template suffix for the property
     * @return string|null
     */
    public function template_suffix(): ?string
    {
        return $this->_property->template_suffix;
    }

    /**
     * SEO: Returns the meta title for the property
     * @return string
     */
    public function meta_title(): string
    {
        return $this->_property->seo_title ?? $this->name();
    }

    /**
     * SEO: Returns the meta description for the property
     * @return string|null
     */
    public function meta_description(): ?string
    {
        return $this->_property->seo_description ?? $this->description();
    }

    /**
     * SEO: Returns the canonical URL for the property
     * @return string
     */
    public function canonical_url(): string
    {
        return route('property.show', ['handle' => $this->handle()]);
    }

    /**
     * SEO: Returns the robots meta directive
     * @return string
     */
    public function robots(): string
    {
        return 'index, follow';
    }

    /**
     * SEO: Returns structured data for the property
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        $data = [
            '@context' => 'https://schema.org',
            '@type' => 'Property',
            'name' => $this->title(),
            'description' => $this->description(),
            'identifier' => $this->id(),
            'url' => $this->canonical_url()
        ];

        if ($this->type() === 'range') {
            $data['value'] = [
                '@type' => 'QuantitativeValue',
                'minValue' => $this->min(),
                'maxValue' => $this->max(),
                'unitCode' => $this->_property->unit ?? null
            ];
        }

        return $data;
    }

    /**
     * Get the number of times this property is used
     * @return int
     */
    public function usage_count(): int
    {
        return $this->_property->usage_count ?? 0;
    }

    /**
     * Check if the property is required
     * @return bool
     */
    public function is_required(): bool
    {
        return (bool)$this->_property->required;
    }

    /**
     * Check if the property is filterable
     * @return bool
     */
    public function is_filterable(): bool
    {
        return (bool)$this->_property->filterable;
    }

    /**
     * Analytics: Returns analytics data for the property
     * @return array<string, mixed>
     */
    public function analytics(): array
    {
        return [
            'property_id' => $this->id(),
            'property_name' => $this->name(),
            'property_type' => $this->type(),
            'property_handle' => $this->handle(),
            'has_image' => $this->has_image(),
            'has_options' => !empty($this->options()),
            'is_range' => $this->type() === 'range',
            'is_required' => $this->is_required(),
            'is_filterable' => $this->is_filterable(),
            'usage_count' => $this->usage_count()
        ];
    }

    /**
     * Convert the property to an array
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'type' => $this->type(),
            'decimal_points' => $this->decimal_points(),
            'url_handle' => $this->url_handle(),
            'has_image' => $this->has_image(),
            'step' => $this->step(),
            'min' => $this->min(),
            'max' => $this->max(),
            'values' => $this->values(),
            'value_from' => $this->value_from(),
            'value_to' => $this->value_to(),
            'image' => $this->has_image() ? $this->image()->toArray() : null,
            'options' => $this->options(),
            'handle' => $this->handle(),
            'title' => $this->title(),
            'description' => $this->description(),
            'metafields' => $this->metafields(),
            'published_at' => $this->published_at()?->toArray(),
            'updated_at' => $this->updated_at()?->toArray(),
            'sort_order' => $this->sort_order(),
            'template_suffix' => $this->template_suffix(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'canonical_url' => $this->canonical_url(),
            'robots' => $this->robots(),
            'structured_data' => $this->structured_data(),
            'usage_count' => $this->usage_count(),
            'is_required' => $this->is_required(),
            'is_filterable' => $this->is_filterable(),
            'analytics' => $this->analytics()
        ];
    }

}
