<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 5.9.2019 г.
 * Time: 09:10 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Category;

use App\Models\Product\Category as CategoryModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;

/**
 * Category Drop Class
 *
 * This class represents a product category in Liquid templates. It provides access to category data
 * and implements Shopify compatibility, accessibility, and SEO features.
 *
 * @property-read int $id The category ID
 * @property-read string $name The category name
 * @property-read string $description The category description
 * @property-read string $seo_title The SEO title
 * @property-read string $seo_description The SEO description
 * @property-read string $url_handle The URL handle
 * @property-read string $url The category URL
 * @property-read int $total_properties The total number of properties
 * @property-read string $color The category color
 * @property-read int $order The category order
 * @property-read int|null $level The category level
 * @property-read array $path The category path
 * @property-read array $dropdown The category dropdown
 * @property-read \App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute $image The category image
 * @property-read bool $has_image Whether the category has an image
 * @property-read \App\LiquidEngine\LiquidHelpers\Drop\Category\GoogleTaxonomy|null $google_taxonomy The Google taxonomy
 */
class Category extends AbstractDrop
{

    use DropImageToArray;

    protected CategoryModel $_category;

    /**
     * Category constructor.
     * @param CategoryModel $category The category model instance
     */
    public function __construct(CategoryModel $category)
    {
        $this->_category = $category;
    }

    /**
     * Get the category ID
     * @return int
     */
    public function id(): int
    {
        return $this->_category->id;
    }

    /**
     * Get the category name
     * @return string
     */
    public function name(): string
    {
        return $this->_category->name;
    }

    /**
     * Get the category description
     * @return string|null
     */
    public function description(): ?string
    {
        return $this->_category->description;
    }

    /**
     * Get the SEO title
     * @return string|null
     */
    public function seo_title(): ?string
    {
        return $this->_category->seo_title;
    }

    /**
     * Get the SEO description
     * @return string|null
     */
    public function seo_description(): ?string
    {
        return $this->_category->seo_description;
    }

    /**
     * Get the URL handle
     * @return string
     */
    public function url_handle(): string
    {
        return $this->_category->url_handle;
    }

    /**
     * Get the category URL
     * @return string
     */
    public function url(): string
    {
        return $this->_category->url;
    }

    /**
     * Get the total number of properties
     * @return int
     */
    public function total_properties(): int
    {
        return $this->_category->properties_count;
    }

    /**
     * Get the category color
     * @return string|null
     */
    public function color(): ?string
    {
        return $this->_category->color;
    }

    /**
     * Get the category order
     * @return int
     */
    public function order(): int
    {
        return $this->_category->order;
    }

    /**
     * Get the category level
     * @return int|null
     */
    public function level(): ?int
    {
        if (is_numeric($this->_category->level)) {
            return (int)$this->_category->level;
        }
        return null;
    }

    /**
     * Get the category path
     * @return array<self>
     */
    public function path(): array
    {
        $path = [];
        if ($this->_category->relationLoaded('path')) {
            $path = $this->_category->path->reverse()
                ->where('id', '!=', $this->id())
                ->map(fn(CategoryModel $category): self => new self($category))
                ->values()
                ->all();
        }
        return $path;
    }

    /**
     * Get the category dropdown
     * @return array<self>
     */
    public function dropdown(): array
    {
        $categories = [];
        if ($this->_category->relationLoaded('childrenRecursive')) {
            $categories = $this->_category->childrenRecursive
                ->map(fn(CategoryModel $category): self => new self($category))
                ->all();
        }
        return $categories;
    }

    /**
     * Get the category image
     * @return \App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute
     */
    public function image(): \App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute
    {
        $formatter = new UrlImageFormat($this->_category);
        return $formatter->getLiquidImage();
    }

    /**
     * Check if the category has an image
     * @return bool
     */
    public function has_image(): bool
    {
        return (bool)$this->_category->hasImage();
    }

    /**
     * Get the Google taxonomy
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Category\GoogleTaxonomy|null
     */
    public function google_taxonomy(): ?\App\LiquidEngine\LiquidHelpers\Drop\Category\GoogleTaxonomy
    {
        return $this->_category->taxonomy_id && $this->_category->google_taxonomy
            ? new GoogleTaxonomy($this->_category->google_taxonomy)
            : null;
    }

    /**
     * Shopify compatibility: handle
     * Returns the category handle (url_handle)
     * @return string
     */
    public function handle(): string
    {
        return $this->url_handle();
    }

    /**
     * Shopify compatibility: title
     * Returns the category title (name)
     * @return string
     */
    public function title(): string
    {
        return $this->name();
    }

    /**
     * Shopify compatibility: products
     * Returns the products in this category
     * @return array<\App\LiquidEngine\LiquidHelpers\Drop\Product>
     */
    public function products(): array
    {
        if ($this->_category->relationLoaded('products')) {
            return $this->_category->products
                ->map(fn($product) => new \App\LiquidEngine\LiquidHelpers\Drop\Product($product))
                ->all();
        }
        return [];
    }

    /**
     * Shopify compatibility: metafields
     * Returns the category metafields as an array
     * @return array<string, mixed>
     */
    public function metafields(): array
    {
        return $this->_category->metafields ?? [];
    }

    /**
     * Shopify compatibility: template_suffix
     * Returns the template suffix for the category
     * @return string|null
     */
    public function template_suffix(): ?string
    {
        return $this->_category->template_suffix ?? null;
    }

    /**
     * Shopify compatibility: published_at
     * Returns the published date as a Date Drop
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Date|null
     */
    public function published_at(): ?\App\LiquidEngine\LiquidHelpers\Drop\Date
    {
        return $this->_category->published_at
            ? new \App\LiquidEngine\LiquidHelpers\Drop\Date($this->_category->published_at)
            : null;
    }

    /**
     * Shopify compatibility: updated_at
     * Returns the updated date as a Date Drop
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Date|null
     */
    public function updated_at(): ?\App\LiquidEngine\LiquidHelpers\Drop\Date
    {
        return $this->_category->updated_at
            ? new \App\LiquidEngine\LiquidHelpers\Drop\Date($this->_category->updated_at)
            : null;
    }

    /**
     * Shopify compatibility: sort_order
     * Returns the sort order of the category
     * @return int|null
     */
    public function sort_order(): ?int
    {
        return $this->_category->order ?? null;
    }

    /**
     * Shopify compatibility: all_products_count
     * Returns the total number of products in the category
     * @return int
     */
    public function all_products_count(): int
    {
        return $this->_category->products_count ?? 0;
    }

    /**
     * SEO: Returns the meta title for the category
     * @return string
     */
    public function meta_title(): string
    {
        return $this->_category->seo_title ?? $this->title();
    }

    /**
     * SEO: Returns the meta description for the category
     * @return string|null
     */
    public function meta_description(): ?string
    {
        return $this->_category->seo_description ?? $this->description();
    }

    /**
     * SEO: Returns the canonical URL for the category
     * @return string
     */
    public function canonical_url(): string
    {
        return $this->url();
    }

    /**
     * SEO: Returns structured data for the category
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'CollectionPage',
            'name' => $this->title(),
            'description' => $this->description(),
            'url' => $this->canonical_url(),
            'image' => $this->has_image() ? $this->image()->src() : null,
            'numberOfItems' => $this->all_products_count(),
            'isPartOf' => [
                '@type' => 'WebSite',
                'url' => config('app.url')
            ]
        ];
    }

    /**
     * Analytics: Returns analytics data for the category
     * @return array<string, mixed>
     */
    public function analytics(): array
    {
        return [
            'category_id' => $this->id(),
            'category_name' => $this->name(),
            'category_url' => $this->url(),
            'product_count' => $this->all_products_count(),
            'has_image' => $this->has_image(),
            'level' => $this->level(),
            'properties_count' => $this->total_properties()
        ];
    }

    /**
     * Get the parent category
     * @return self|null
     */
    public function parent(): ?self
    {
        if ($this->_category->relationLoaded('parent') && $this->_category->parent) {
            return new self($this->_category->parent);
        }
        return null;
    }

    /**
     * Get the child categories
     * @return array<self>
     */
    public function children(): array
    {
        if ($this->_category->relationLoaded('children')) {
            return $this->_category->children
                ->map(fn(CategoryModel $category): self => new self($category))
                ->all();
        }
        return [];
    }

    /**
     * Get the category image as an array
     * @return array<string, mixed>|null
     */
    public function image_array(): ?array
    {
        return $this->has_image() ? $this->image()->toArray() : null;
    }

    /**
     * Convert the category to an array
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'description' => $this->description(),
            'seo_title' => $this->seo_title(),
            'seo_description' => $this->seo_description(),
            'url_handle' => $this->url_handle(),
            'url' => $this->url(),
            'total_properties' => $this->total_properties(),
            'color' => $this->color(),
            'order' => $this->order(),
            'level' => $this->level(),
            'path' => $this->path(),
            'dropdown' => $this->dropdown(),
            'image' => $this->image_array(),
            'has_image' => $this->has_image(),
            'google_taxonomy' => $this->google_taxonomy()?->toArray(),
            'products' => $this->products(),
            'metafields' => $this->metafields(),
            'template_suffix' => $this->template_suffix(),
            'published_at' => $this->published_at()?->toArray(),
            'updated_at' => $this->updated_at()?->toArray(),
            'sort_order' => $this->sort_order(),
            'all_products_count' => $this->all_products_count(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'canonical_url' => $this->canonical_url(),
            'structured_data' => $this->structured_data(),
            'analytics' => $this->analytics(),
            'parent' => $this->parent()?->toArray(),
            'children' => $this->children()
        ];
    }

}
