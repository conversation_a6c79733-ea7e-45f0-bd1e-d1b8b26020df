<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 5.9.2019 г.
 * Time: 09:10 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Category;

use App\Models\Apps\GoogleProductCategory;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use Illuminate\Support\Str;

/**
 * GoogleTaxonomy Drop Class
 * 
 * This class represents a Google Product Category taxonomy in Liquid templates. It provides access to
 * Google Product Category data and implements Shopify compatibility, accessibility, and SEO features.
 * 
 * @property-read int $id The taxonomy ID
 * @property-read string $name The taxonomy name
 * @property-read string $handle The taxonomy handle (slugified name)
 * @property-read string $title The taxonomy title
 * @property-read string|null $description The taxonomy description
 * @property-read array<string, mixed> $metafields The taxonomy metafields
 */
class GoogleTaxonomy extends AbstractDrop
{

    protected GoogleProductCategory $_category;

    /**
     * GoogleTaxonomy constructor.
     * @param GoogleProductCategory $category The Google Product Category model instance
     */
    public function __construct(GoogleProductCategory $category)
    {
        $this->_category = $category;
    }

    /**
     * Get the taxonomy ID
     * @return int
     */
    public function id(): int
    {
        return $this->_category->id;
    }

    /**
     * Get the taxonomy name
     * @return string
     */
    public function name(): string
    {
        return $this->_category->name;
    }

    /**
     * Shopify compatibility: handle
     * Returns the taxonomy handle (slugified name)
     * @return string
     */
    public function handle(): string
    {
        return Str::slug($this->name());
    }

    /**
     * Shopify compatibility: title
     * Returns the taxonomy title (name)
     * @return string
     */
    public function title(): string
    {
        return $this->name();
    }

    /**
     * Shopify compatibility: description
     * Returns the taxonomy description
     * @return string|null
     */
    public function description(): ?string
    {
        return $this->_category->description;
    }

    /**
     * Shopify compatibility: metafields
     * Returns the taxonomy metafields as an array
     * @return array<string, mixed>
     */
    public function metafields(): array
    {
        return $this->_category->metafields ?? [];
    }

    /**
     * SEO: Returns the meta title for the taxonomy
     * @return string
     */
    public function meta_title(): string
    {
        return $this->title();
    }

    /**
     * SEO: Returns the meta description for the taxonomy
     * @return string|null
     */
    public function meta_description(): ?string
    {
        return $this->description();
    }

    /**
     * SEO: Returns the canonical URL for the taxonomy
     * @return string
     */
    public function canonical_url(): string
    {
        return route('google.taxonomy.show', ['handle' => $this->handle()]);
    }

    /**
     * SEO: Returns structured data for the taxonomy
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Thing',
            'name' => $this->title(),
            'description' => $this->description(),
            'identifier' => $this->id(),
            'url' => $this->canonical_url()
        ];
    }

    /**
     * Analytics: Returns analytics data for the taxonomy
     * @return array<string, mixed>
     */
    public function analytics(): array
    {
        return [
            'taxonomy_id' => $this->id(),
            'taxonomy_name' => $this->name(),
            'taxonomy_handle' => $this->handle(),
            'has_description' => !empty($this->description()),
            'has_metafields' => !empty($this->metafields())
        ];
    }

    /**
     * Convert the taxonomy to an array
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'handle' => $this->handle(),
            'title' => $this->title(),
            'description' => $this->description(),
            'metafields' => $this->metafields(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'canonical_url' => $this->canonical_url(),
            'structured_data' => $this->structured_data(),
            'analytics' => $this->analytics()
        ];
    }

}
