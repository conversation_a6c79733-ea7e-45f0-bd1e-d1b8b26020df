<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Models\Product\Upload;

/**
 * OrderDownload Drop class for handling order file downloads in Liquid templates.
 *
 * This class represents a downloadable file associated with an order in the Liquid template system,
 * providing access to file details, order information, and download URLs. It wraps the Upload model
 * and exposes download-specific details for use in Liquid templates.
 *
 * Usage:
 *   {{ order_download.id }}
 *   {{ order_download.name }}
 *   {{ order_download.date }}
 *   {{ order_download.url }}
 *
 * Properties:
 * - id: int
 * - order_id: int
 * - name: string
 * - product_name: string
 * - vendor_name: string
 * - date: Date
 * - url: string
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class OrderDownload extends AbstractDrop
{
    /**
     * The underlying upload model instance.
     * Contains the raw upload data from the database.
     *
     * @var Upload
     */
    protected Upload $_upload;

    /**
     * Create a new OrderDownload instance.
     * Initializes the drop with an Upload model instance.
     *
     * @param Upload $upload The upload model instance
     */
    public function __construct(Upload $upload)
    {
        $this->_upload = $upload;
    }

    /**
     * Get the unique identifier of the download.
     * Returns the primary key of the underlying upload model.
     *
     * @return int The download ID
     */
    public function id(): int
    {
        return (int) $this->_upload->id;
    }

    /**
     * Get the associated order ID.
     * Returns the order ID associated with the download.
     *
     * @return int The order ID
     */
    public function order_id(): int
    {
        return (int) $this->_upload->order_id;
    }

    /**
     * Get the name of the downloadable file.
     * Returns the file name from the upload model.
     *
     * @return string The file name
     */
    public function name(): string
    {
        return (string) $this->_upload->name;
    }

    /**
     * Get the name of the product associated with the download.
     * Returns the product name from the upload model.
     *
     * @return string The product name
     */
    public function product_name(): string
    {
        return (string) $this->_upload->order_product_name;
    }

    /**
     * Get the name of the vendor associated with the download.
     * Returns the vendor name from the upload model.
     *
     * @return string The vendor name
     */
    public function vendor_name(): string
    {
        return (string) $this->_upload->order_product_vendor_name;
    }

    /**
     * Get the date when the file was added.
     * Returns a Date object representing when the file was added.
     *
     * @return Date The date added
     */
    public function date(): Date
    {
        return new Date($this->_upload->date_added);
    }

    /**
     * Get the URL for downloading the file.
     * Returns the route to download the file.
     *
     * @return string The download URL
     */
    public function url(): string
    {
        return route('site.account.file.download', ['download_id' => $this->id()]);
    }

    /**
     * Get the collection of items as a plain array.
     * Returns an array representation of the order download with all its properties.
     * This method is used by Liquid templates to access the download data.
     *
     * @return array{
     *     id: int,
     *     order_id: int,
     *     name: string,
     *     product_name: string,
     *     vendor_name: string,
     *     date: Date,
     *     url: string
     * } The order download data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'order_id' => $this->order_id(),
            'name' => $this->name(),
            'product_name' => $this->product_name(),
            'vendor_name' => $this->vendor_name(),
            'date' => $this->date(),
            'url' => $this->url(),
        ];
    }
}
