<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.4.2019 г.
 * Time: 12:02 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\UrlWindow;
use Illuminate\Support\Arr;

class Paginate extends AbstractDrop
{
    protected \Illuminate\Pagination\LengthAwarePaginator $items;

    /**
     * @var array $elements
     */
    protected $elements;

    /**
     * Paginate constructor.
     * @param LengthAwarePaginator $pagination
     */
    public function __construct(LengthAwarePaginator $pagination)
    {
        $this->items = $pagination;
        $this->elements = UrlWindow::make($pagination);
    }

    /**
     * @return int
     */
    public function page_size(): int
    {
        return $this->items->perPage();
    }

    /**
     * @return int
     */
    public function current_page(): int
    {
        return $this->items->currentPage();
    }

    /**
     * @return int
     */
    public function current_offset(): int
    {
        return ($this->current_page() - 1) * $this->page_size();
    }

    /**
     * @return int
     */
    public function pages(): int
    {
        return $this->items->lastPage();
    }

    /**
     * @return int
     */
    public function items(): int
    {
        return $this->items->total();
    }

    /**
     * @return null|array
     */
    public function previous(): ?array
    {
        if ($previous = $this->items->previousPageUrl()) {
            return [
                'title' => $this->current_page() - 1,
                'url' => $this->removeQueryParam($previous, ['callback', '_', 'section_id']),
                'is_link' => true,
            ];
        }

        return null;
    }

    /**
     * @return null|array
     */
    public function next(): ?array
    {
        if ($next = $this->items->nextPageUrl()) {
            return [
                'title' => $this->current_page() + 1,
                'url' => $this->removeQueryParam($next, ['callback', '_', 'section_id']),
                'is_link' => true,
            ];
        }

        return null;
    }

    /**
     * @return array
     */
    public function parts(): array
    {
        $result = [];
        $elements = array_filter([
            $this->elements['first'],
            is_array($this->elements['slider']) ? '...' : null,
            $this->elements['slider'],
            is_array($this->elements['last']) ? '...' : null,
            $this->elements['last'],
        ]);

        foreach ($elements as $element) {
            if (is_array($element)) {
                foreach ($element as $page => $link) {
                    $result[] = [
                        'title' => $page,
                        'url' => $this->removeQueryParam($link, ['callback', '_', 'section_id']),
                        'is_link' => true,
                    ];
                }
            } else {
                $result[] = [
                    'title' => $element,
                    'url' => null,
                    'is_link' => false,
                ];
            }
        }

        return $result;
    }

    // START: Shopify-compatible methods for Paginate
    // These methods provide compatibility with Shopify's paginate object structure

    /**
     * Get the URL parameter denoting the pagination
     * Shopify compatible: paginate.page_param
     */
    public function page_param(): string
    {
        return 'page';
    }

    /**
     * Get pagination interval (default items per page)
     * Shopify compatible: paginate.default_page_size  
     */
    public function default_page_size(): int
    {
        return $this->page_size();
    }

    /**
     * Get pagination parts with extended info
     * Shopify compatible: paginate.parts (enhanced)
     */
    public function parts_extended(): array
    {
        $result = [];
        $parts = $this->parts();
        
        foreach ($parts as $part) {
            $result[] = [
                'title' => $part['title'],
                'url' => $part['url'],
                'is_link' => $part['is_link'],
                'class' => $part['is_link'] ? 'page' : 'current'
            ];
        }
        
        return $result;
    }

    /**
     * Check if pagination is enabled (has multiple pages)
     * Shopify compatible: paginate.enabled  
     */
    public function enabled(): bool
    {
        return $this->pages() > 1;
    }

    /**
     * Get pagination window size
     * Shopify compatible: paginate.window_size
     */
    public function window_size(): int
    {
        return 5; // Default window size
    }

    /**
     * Check if there are previous pages
     * Shopify compatible: paginate.has_previous
     */
    public function has_previous(): bool
    {
        return $this->previous() !== null;
    }

    /**
     * Check if there are next pages  
     * Shopify compatible: paginate.has_next
     */
    public function has_next(): bool
    {
        return $this->next() !== null;
    }

    /**
     * Get first page number
     * Shopify compatible: paginate.first_page
     */
    public function first_page(): int
    {
        return 1;
    }

    /**
     * Get last page number
     * Shopify compatible: paginate.last_page
     */
    public function last_page(): int
    {
        return $this->pages();
    }

    /**
     * Get range of visible pages around current page
     * Shopify compatible: paginate.visible_pages
     */
    public function visible_pages(): array
    {
        $visible = [];
        $start = max(1, $this->current_page() - 2);
        $end = min($this->pages(), $this->current_page() + 2);
        
        for ($i = $start; $i <= $end; $i++) {
            $visible[] = $i;
        }
        
        return $visible;
    }

    /**
     * Get pagination info text
     * Shopify compatible: paginate.info  
     */
    public function info(): string
    {
        $start = $this->current_offset() + 1;
        $end = min($this->current_offset() + $this->page_size(), $this->items());
        
        return "Showing {$start}-{$end} of {$this->items()} items";
    }

    /**
     * Check if we're on the first page
     * Shopify compatible: paginate.is_first_page
     */
    public function is_first_page(): bool
    {
        return $this->current_page() === 1;
    }

    /**
     * Check if we're on the last page
     * Shopify compatible: paginate.is_last_page  
     */
    public function is_last_page(): bool
    {
        return $this->current_page() === $this->pages();
    }

    /**
     * Get URL for a specific page
     * Shopify compatible: paginate.url_for_page
     */
    public function url_for_page(int $page): string
    {
        if ($page < 1 || $page > $this->pages()) {
            return '';
        }
        
        $url = parse_url(request()->fullUrl());
        parse_str($url['query'] ?? '', $params);
        
        if ($page === 1) {
            unset($params['page']);
        } else {
            $params['page'] = $page;
        }
        
        $queryString = http_build_query($params);
        $baseUrl = ($url['scheme'] ?? 'http') . '://' . ($url['host'] ?? '') . ($url['path'] ?? '');
        
        return $baseUrl . ($queryString ? '?' . $queryString : '');
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'page_size' => $this->page_size(),
            'current_page' => $this->current_page(),
            'current_offset' => $this->current_offset(),
            'pages' => $this->pages(),
            'items' => $this->items(),
            'previous' => $this->previous() ?: false,
            'next' => $this->next() ?: false,
            'parts' => $this->parts(),
            // Shopify-compatible fields
            'page_param' => $this->page_param(),
            'default_page_size' => $this->default_page_size(),
            'enabled' => $this->enabled(),
            'window_size' => $this->window_size(),
            'has_previous' => $this->has_previous(),
            'has_next' => $this->has_next(),
            'first_page' => $this->first_page(),
            'last_page' => $this->last_page(),
            'visible_pages' => $this->visible_pages(),
            'info' => $this->info(),
            'is_first_page' => $this->is_first_page(),
            'is_last_page' => $this->is_last_page(),
        ];
    }

    //http://www.php.net/manual/en/function.parse-url.php#106731

    /**
     * @param mixed $parsed_url
     * @return mixed
     */
    protected function unParseUrl($parsed_url): string
    {
        $scheme = isset($parsed_url['scheme']) ? $parsed_url['scheme'] . '://' : '';
        $host = isset($parsed_url['host']) ? $parsed_url['host'] : '';
        $port = isset($parsed_url['port']) ? ':' . $parsed_url['port'] : '';
        $user = isset($parsed_url['user']) ? $parsed_url['user'] : '';
        $pass = isset($parsed_url['pass']) ? ':' . $parsed_url['pass'] : '';
        $pass = ($user || $pass) ? $pass . '@' : '';

        $path = isset($parsed_url['path']) ? $parsed_url['path'] : '';
        $query = isset($parsed_url['query']) ? '?' . $parsed_url['query'] : '';
        $fragment = isset($parsed_url['fragment']) ? '#' . $parsed_url['fragment'] : '';
        return $scheme . $user . $pass . $host . $port . $path . $query . $fragment;
    }

    /**
     * @param $url
     * @param string|array $param_to_remove
     * @return string
     */
    protected function removeQueryParam($url, $param_to_remove)
    {
        $parsed = parse_url($url);
        if ($parsed && isset($parsed['query'])) {
            $parsed['query'] = implode('&', array_filter(explode('&', $parsed['query']), function ($param) use ($param_to_remove): bool {
                return !in_array(Arr::first(explode('=', $param)), (array)$param_to_remove);
            }));

            if ($parsed['query'] === '') {
                unset($parsed['query']);
            }

            return $this->unParseUrl($parsed);
        } else {
            return $url;
        }
    }
}
