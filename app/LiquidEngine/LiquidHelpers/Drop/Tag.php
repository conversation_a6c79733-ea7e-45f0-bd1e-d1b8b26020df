<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.4.2019 г.
 * Time: 12:47 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Models\Product\Tag as ProductTagModel;
use App\Models\Blog\Tag as BlogTagModel;

/**
 * Tag Drop class for handling tag data in Liquid templates.
 * 
 * This class represents a tag (either product or blog) in the Liquid template system,
 * providing access to tag properties and methods for URL handling. It supports both
 * product tags and blog tags through a unified interface.
 * 
 * Properties:
 * - id: Tag ID (int)
 * - tag: Tag name (string)
 * - url_handle: URL-friendly version of the tag name (string)
 * - url: Full URL to the tag's page (string)
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class Tag extends AbstractDrop
{
    /**
     * The underlying tag model instance.
     * Can be either a product tag or a blog tag model.
     *
     * @var ProductTagModel|BlogTagModel
     */
    protected ProductTagModel|BlogTagModel $_tag;

    /**
     * Create a new Tag instance.
     * Initializes the drop with either a product tag or blog tag model.
     *
     * @param ProductTagModel|BlogTagModel $tag The tag model instance
     */
    public function __construct(ProductTagModel|BlogTagModel $tag)
    {
        $this->_tag = $tag;
    }

    /**
     * Get the unique identifier of the tag.
     * Returns the primary key of the underlying tag model.
     *
     * @return int The tag ID
     */
    public function id(): int
    {
        return (int) $this->_tag->id;
    }

    /**
     * Get the tag name.
     * Returns the human-readable name of the tag.
     *
     * @return string The tag name
     */
    public function tag(): string
    {
        return (string) $this->_tag->tag;
    }

    /**
     * Get the URL handle for the tag.
     * Returns a URL-friendly version of the tag name.
     *
     * @return string The URL handle
     */
    public function url_handle(): string
    {
        return (string) $this->_tag->url_handle;
    }

    /**
     * Get the URL for the tag.
     * Returns the full URL to the tag's page.
     *
     * @return string The tag URL
     */
    public function url(): string
    {
        return (string) $this->_tag->url;
    }

    /**
     * Get the collection of items as a plain array.
     * Returns an array representation of the tag with all its properties.
     * This method is used by Liquid templates to access the tag data.
     *
     * @return array{
     *     id: int,
     *     tag: string,
     *     url_handle: string,
     *     url: string
     * } The tag data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'tag' => $this->tag(),
            'url_handle' => $this->url_handle(),
            'url' => $this->url(),
        ];
    }
}
