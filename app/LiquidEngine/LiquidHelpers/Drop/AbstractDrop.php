<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Contracts\Support\Arrayable;
use JsonSerializable;
use Liquid\Drop;

/**
 * AbstractDrop Class
 * 
 * This abstract class serves as the base class for all Drop classes in the Liquid template system.
 * It provides common functionality for converting drops to arrays and JSON serialization.
 * 
 * All concrete Drop classes must implement:
 * - toArray(): array - Convert the drop to an array representation
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
abstract class AbstractDrop extends Drop implements Arrayable, JsonSerializable
{
    /**
     * Convert the drop to an array.
     * 
     * @return array<string, mixed> The drop data as an array
     */
    abstract public function toArray(): array;

    /**
     * Convert the drop to a JSON-serializable array.
     * 
     * @return array<string, mixed> The drop data as a JSON-serializable array
     */
    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
