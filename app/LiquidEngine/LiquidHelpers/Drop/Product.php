<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Exceptions\Error;
use App\Helper\Format;
use App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute;
use App\Helper\YesNo;
use App\Models\Category\PropertyOption as PropertyOptionModel;
use App\Models\Discount\ProductBanners;
use App\Models\Discount\ProductLabels;
use App\Models\Product\Image;
use App\Models\Product\Options;
use App\Models\Product\Tag as TagModel;
use App\Models\System\AppsManager;
use Carbon\Carbon;
use App\Models\Product\Product as ProductModel;
use App\LiquidEngine\LiquidHelpers\Drop\Category\PropertyOption;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Banner;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Discount;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Label;
use App\Models\Discount\Discount as DiscountModel;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Option;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Variant;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\LiquidHelpers\Drop\Category\Category as LiquidCategory;
use App\LiquidEngine\LiquidHelpers\Drop\Variants\Variant as Parameter;
use App\LiquidEngine\Traits\DropImageToArray;
use App\Models\Product\Variant as VariantModel;

/**
 * Class Product
 *
 * Represents a product in the Liquid template system.
 * Provides access to product data and functionality for templates.
 *
 * Properties:
 * @property-read ProductModel $_product The underlying product model
 * @property-read bool|int|null $_selected_variant The selected variant ID or false if none
 * @property-read Variant $_selected_or_first_available_variant The selected or first available variant
 * @property-read VariantModel $_variant The current variant model
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class Product extends AbstractDrop
{

    use DropImageToArray;

    /**
     * @var ProductModel $_product
     */
    protected $_product;

    /**
     * @var bool|int|null
     */
    protected $_selected_variant = false;

    /**
     * @var Variant
     */
    protected $_selected_or_first_available_variant;

    /**
     * @var VariantModel
     */
    protected $_variant;

    /**
     * Product constructor.
     *
     * @param ProductModel|null $product The product model instance
     * @throws Error If product formatting fails
     */
    public function __construct(ProductModel $product)
    {
        $this->_product = $product->format();
    }

    /**
     * Get the product ID.
     *
     * @return int
     */
    public function id(): int
    {
        return $this->_product->id;
    }

    /**
     * Get the product name.
     *
     * @return string
     */
    public function name(): string
    {
        return $this->_product->name;
    }

    /**
     * Get the product URL.
     *
     * @return string
     */
    public function url(): string
    {
        return $this->_product->url;
    }

    /**
     * Get the add to cart URL.
     *
     * @return string
     */
    public function add_to_cart(): string
    {
        return route('cart.add', ['variant_id' => $this->_product->default_variant_id]);
    }

    /**
     * Get the wishlist URL.
     *
     * @return string
     */
    public function wishlist_url(): string
    {
        return route('add-to-wishlist', ['id' => $this->id()]);
    }

    /**
     * Get the URL handle.
     *
     * @return string
     */
    public function url_handle(): string
    {
        return $this->_product->url_handle;
    }

    /**
     * Get the first parameter.
     *
     * @return string
     */
    public function p1(): string
    {
        return $this->_product->p1;
    }

    /**
     * Get the second parameter.
     *
     * @return string
     */
    public function p2(): string
    {
        return $this->_product->p2;
    }

    /**
     * Get the third parameter.
     *
     * @return string
     */
    public function p3(): string
    {
        return $this->_product->p3;
    }

    /**
     * Get the first parameter variant.
     *
     * @return Parameter|null
     */
    public function parameter1(): ?Parameter
    {
        return $this->p1() && $this->_product->relationLoaded('p1r') ? new Parameter($this->_product->p1r) : null;
    }

    /**
     * Get the second parameter variant.
     *
     * @return Parameter|null
     */
    public function parameter2(): ?Parameter
    {
        return $this->p2() && $this->_product->relationLoaded('p2r') ? new Parameter($this->_product->p2r) : null;
    }

    /**
     * Get the third parameter variant.
     *
     * @return Parameter|null
     */
    public function parameter3(): ?Parameter
    {
        return $this->p3() && $this->_product->relationLoaded('p3r') ? new Parameter($this->_product->p3r) : null;
    }

    /**
     * Get the total number of variants.
     *
     * @return int
     */
    public function total_variants(): int
    {
        return $this->_product->total_variants ?? 0;
    }

    /**
     * Check if the product has required data.
     *
     * @return bool
     */
    public function has_required_data(): bool
    {
        if ($this->total_variants()) {
            return true;
        }

        if (
            AppsManager::isInstalled('product_options') &&
            (
                $this->_product->getAttribute('required_fields_count') > 0 ||
                ($this->category() && $this->_product->category->getAttribute('required_fields_count') > 0) ||
                ($this->vendor() && $this->_product->vendor->getAttribute('required_fields_count') > 0)
            )
        ) {
            return true;
        }

        if (AppsManager::isInstalled('product_options') && $this->_product->fields->firstWhere('required', true)) {
            return true;
        }

        return false;
    }

    /**
     * Get the product category.
     *
     * @return LiquidCategory|null
     */
    public function category(): ?LiquidCategory
    {
        return $this->_product->category_id && $this->_product->relationLoaded('category') ? new LiquidCategory($this->_product->category) : null;
    }

    /**
     * Get the product vendor.
     *
     * @return Vendor|null
     */
    public function vendor(): ?Vendor
    {
        return $this->_product->vendor_id && $this->_product->relationLoaded('vendor') ? new Vendor($this->_product->vendor) : null;
    }

    /**
     * Get the number of views.
     *
     * @return int
     */
    public function views(): int
    {
        return $this->_product->views;
    }

    /**
     * Check if tracking is enabled.
     *
     * @return bool
     */
    public function tracking(): bool
    {
        return $this->_product->tracking == YesNo::True;
    }

    /**
     * Get the threshold value.
     *
     * @return int
     */
    public function threshold(): int
    {
        return $this->_product->threshold;
    }

    /**
     * Check if shipping is enabled.
     *
     * @return bool
     */
    public function shipping(): bool
    {
        return $this->_product->shipping == YesNo::True;
    }

    /**
     * Check if the product is digital.
     *
     * @return bool
     */
    public function digital(): bool
    {
        return $this->_product->digital == YesNo::True;
    }

    /**
     * Check if the product is on sale.
     *
     * @return bool
     */
    public function sale(): bool
    {
        return $this->_product->sale == YesNo::True;
    }

    /**
     * Check if the product is new.
     *
     * @return bool
     */
    public function new(): bool
    {
        return $this->_product->new == YesNo::True;
    }

    /**
     * Check if free shipping is available.
     *
     * @return bool
     */
    public function free_shipping(): bool
    {
        return $this->_product->free_shipping == YesNo::True;
    }

    /**
     * Check if the product is featured.
     *
     * @return bool
     */
    public function featured(): bool
    {
        return $this->_product->featured == YesNo::True;
    }

    /**
     * Check if the product has different prices.
     *
     * @return bool
     */
    public function diff_price(): bool
    {
        return $this->_product->diff_price == YesNo::True;
    }

    /**
     * Get the minimum price.
     *
     * @return float
     */
    public function price_from(): float
    {
        return (float) $this->_product->price_from;
    }

    /**
     * Get the raw minimum price.
     *
     * @return float
     */
    public function price_from_input(): float
    {
        return (float) $this->_product->price_from_input;
    }

    /**
     * Get the formatted minimum price.
     *
     * @return string
     */
    public function price_from_formatted(): string
    {
        return (string) $this->_product->price_from_formatted;
    }

    /**
     * Get the structured minimum price parts.
     *
     * @return array{amount: float, currency: string, formatted: string}
     */
    public function price_from_parts(): array
    {
        return PriceParts::format($this->price_from_input());
    }

    /**
     * Get the discounted minimum price.
     *
     * @return float|null
     */
    public function price_from_discounted(): ?float
    {
        return $this->_product->price_from_discounted;
    }

    /**
     * Get the raw discounted minimum price.
     *
     * @return float|null
     */
    public function price_from_discounted_input(): ?float
    {
        return $this->_product->price_from_discounted_input;
    }

    /**
     * Get the formatted discounted minimum price.
     *
     * @return string|null
     */
    public function price_from_discounted_formatted(): ?string
    {
        return $this->_product->price_from_discounted_formatted;
    }

    /**
     * Get the structured discounted minimum price parts.
     *
     * @return array{amount: float, currency: string, formatted: string}|null
     */
    public function price_from_discounted_parts(): ?array
    {
        return $this->price_from_discounted_input() ? PriceParts::format($this->price_from_discounted_input()) : null;
    }

    /**
     * Get the saved price amount.
     *
     * @return float|null
     */
    public function price_saved(): ?float
    {
        return $this->_product->price_saved;
    }

    /**
     * Get the formatted saved price.
     *
     * @return string|null
     */
    public function price_saved_formatted(): ?string
    {
        return $this->_product->price_saved_formatted;
    }

    /**
     * Get the raw saved price.
     *
     * @return float|null
     */
    public function price_saved_input(): ?float
    {
        return $this->_product->price_saved_input;
    }

    /**
     * Get the structured saved price parts.
     *
     * @return array{amount: float, currency: string, formatted: string}|null
     */
    public function price_saved_parts(): ?array
    {
        return $this->price_saved_input() ? PriceParts::format($this->price_saved_input()) : null;
    }

    /**
     * Get the maximum price.
     *
     * @return float
     */
    public function price_to(): float
    {
        return (float) $this->_product->price_to;
    }

    /**
     * Get the raw maximum price.
     *
     * @return float
     */
    public function price_to_input(): float
    {
        return (float) $this->_product->price_to_input;
    }

    /**
     * Get the structured maximum price parts.
     *
     * @return array{amount: float, currency: string, formatted: string}
     */
    public function price_to_parts(): array
    {
        return PriceParts::format($this->price_to_input());
    }

    /**
     * Get the formatted maximum price.
     *
     * @return string
     */
    public function price_to_formatted(): string
    {
        return (string) $this->_product->price_to_formatted;
    }

    /**
     * Get the price percentage.
     *
     * @return float
     */
    public function price_percent(): float
    {
        return (float) $this->_product->price_percent;
    }

    /**
     * Get the price type.
     *
     * @return string
     */
    public function price_type(): string
    {
        return (string) $this->_product->price_type;
    }

    /**
     * Check if continue selling is enabled.
     *
     * @return bool
     */
    public function continue_selling(): bool
    {
        return $this->_product->continue_selling == YesNo::True;
    }

    /**
     * Get the date added.
     *
     * @return Date
     */
    public function date_added(): Date
    {
        return new Date($this->_product->date_added);
    }

    /**
     * Get the date modified.
     *
     * @return Date
     */
    public function date_modified(): Date
    {
        return new Date($this->_product->date_modified);
    }

    /**
     * Get the active to date.
     *
     * @return Date|null
     */
    public function active_to(): ?Date
    {
        return $this->_product->active_to ? new Date($this->_product->active_to) : null;
    }

    /**
     * Get the short description.
     *
     * @return string
     */
    public function short_description(): string
    {
        return (string) $this->_product->short_description;
    }

    /**
     * Get the product type.
     *
     * @return string
     */
    public function type(): string
    {
        return (string) $this->_product->type;
    }

    /**
     * Check if the product is discounted.
     *
     * @return bool
     */
    public function is_discounted(): bool
    {
        return $this->_product->is_discounted == YesNo::True;
    }

    /**
     * Get the product to discount price.
     *
     * @return float
     */
    public function product_to_discount_price(): float
    {
        return (float) $this->_product->product_to_discount_price;
    }

    /**
     * Get the product to discount price to.
     *
     * @return float
     */
    public function product_to_discount_price_to(): float
    {
        return (float) $this->_product->product_to_discount_price_to;
    }

    /**
     * Get the minimum price with discount.
     *
     * @return float
     */
    public function min_price_with_discounted(): float
    {
        return (float) $this->_product->min_price_with_discounted;
    }

    /**
     * Get the maximum price with discount.
     *
     * @return float
     */
    public function max_price_with_discounted(): float
    {
        return (float) $this->_product->max_price_with_discounted;
    }

    /**
     * Get the quantity.
     *
     * @return int
     */
    public function quantity(): int
    {
        return (int) $this->_product->quantity;
    }

    /**
     * Check if online selling is enabled.
     *
     * @return bool
     */
    public function enable_online_selling(): bool
    {
        return $this->_product->enable_online_selling == YesNo::True;
    }

    /**
     * Get the overlay label.
     *
     * @return Label|false
     */
    public function overlay(): Label|false
    {
        return $this->_product->overlay ? new Label($this->_product->overlay) : false;
    }

    /**
     * Check if the product is a favorite.
     *
     * @return bool
     */
    public function favorite(): bool
    {
        return $this->_product->favorite == YesNo::True;
    }

    /**
     * Get the current variant.
     *
     * @return Variant
     */
    public function variant(): Variant
    {
        if (!$this->_variant) {
            $this->_variant = $this->selected_or_first_available_variant();
        }
        return $this->_variant;
    }

    /**
     * Get all variants.
     *
     * @return array<Variant>
     */
    public function variants(): array
    {
        $variants = [];
        if ($this->_product->relationLoaded('variants')) {
            $variants = $this->_product->variants->map(function (VariantModel $variant): Variant {
                return new Variant($variant, $this->_product);
            })->all();
        }
        return $variants;
    }

    /**
     * Get category properties.
     *
     * @return array<PropertyOption>
     */
    public function category_properties(): array
    {
        $properties = [];
        if ($this->_product->relationLoaded('category_properties')) {
            $properties = $this->_product->category_properties->map(function (PropertyOptionModel $property): PropertyOption {
                return new PropertyOption($property);
            })->all();
        }
        return $properties;
    }

    /**
     * Get category properties grouped by category.
     *
     * @return array<string, array<PropertyOption>>
     */
    public function category_properties_grouped(): array
    {
        $grouped = [];
        foreach ($this->category_properties() as $property) {
            $category = $property->category();
            if ($category) {
                $grouped[$category->name()][] = $property;
            }
        }
        return $grouped;
    }

    /**
     * Get the product image.
     *
     * @return Image|null
     */
    public function image(): ?Image
    {
        return $this->_product->image;
    }

    /**
     * Check if the product has an image.
     *
     * @return bool
     */
    public function has_image(): bool
    {
        return $this->_product->image !== null;
    }

    /**
     * Check if the product is hidden.
     *
     * @return bool
     */
    public function is_hidden(): bool
    {
        return $this->_product->is_hidden == YesNo::True;
    }

    /**
     * Get the first two images.
     *
     * @return array<Image>
     */
    public function first_two_images(): array
    {
        return $this->_product->images->take(2)->all();
    }

    /**
     * Get the product tags.
     *
     * @return array<TagModel>
     */
    public function tags(): array
    {
        return $this->_product->tags->all();
    }

    /**
     * Get the product banners.
     *
     * @return array<Banner>
     */
    public function banners(): array
    {
        $banners = [];
        if ($this->_product->relationLoaded('banners')) {
            $banners = $this->_product->banners->map(function (ProductBanners $banner): Banner {
                return new Banner($banner);
            })->all();
        }
        return $banners;
    }

    /**
     * Get the product labels.
     *
     * @return array<Label>
     */
    public function labels(): array
    {
        $labels = [];
        if ($this->_product->relationLoaded('labels')) {
            $labels = $this->_product->labels->map(function (ProductLabels $label): Label {
                return new Label($label);
            })->all();
        }
        return $labels;
    }

    /**
     * Get the product discount.
     *
     * @return Discount|null
     */
    public function discount(): ?Discount
    {
        if ($this->_product->relationLoaded('discount') && $this->_product->discount) {
            return new Discount($this->_product->discount);
        }
        return null;
    }

    /**
     * Get the product options.
     *
     * @return array<Option>
     */
    public function options(): array
    {
        $options = [];
        if ($this->_product->relationLoaded('options')) {
            $options = $this->_product->options->map(function (Options $option): Option {
                return new Option($option);
            })->all();
        }
        return $options;
    }

    /**
     * Get the total number of options.
     *
     * @return int
     */
    public function total_options(): int
    {
        return count($this->options());
    }

    /**
     * Get the selected variant.
     *
     * @return Variant|null
     */
    public function selected_variant(): ?Variant
    {
        if ($this->_selected_variant === false) {
            $this->_selected_variant = $this->_product->selected_variant_id;
        }

        if ($this->_selected_variant && $this->_product->relationLoaded('variants')) {
            $variant = $this->_product->variants->firstWhere('id', $this->_selected_variant);
            if ($variant) {
                return new Variant($variant, $this->_product);
            }
        }

        return null;
    }

    /**
     * Get the selected or first available variant.
     *
     * @return Variant
     */
    public function selected_or_first_available_variant(): Variant
    {
        if (!$this->_selected_or_first_available_variant) {
            $variant = $this->selected_variant();
            if (!$variant && $this->_product->relationLoaded('variants')) {
                $variant = $this->_product->variants->first();
                if ($variant) {
                    $variant = new Variant($variant, $this->_product);
                }
            }
            $this->_selected_or_first_available_variant = $variant;
        }
        return $this->_selected_or_first_available_variant;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     id: int,
     *     name: string,
     *     url: string,
     *     add_to_cart: string,
     *     wishlist_url: string,
     *     url_handle: string,
     *     p1: string,
     *     p2: string,
     *     p3: string,
     *     parameter1: Parameter|null,
     *     parameter2: Parameter|null,
     *     parameter3: Parameter|null,
     *     total_variants: int,
     *     has_required_data: bool,
     *     category: LiquidCategory|null,
     *     vendor: Vendor|null,
     *     views: int,
     *     tracking: bool,
     *     threshold: int,
     *     shipping: bool,
     *     digital: bool,
     *     sale: bool,
     *     new: bool,
     *     free_shipping: bool,
     *     featured: bool,
     *     diff_price: bool,
     *     price_from: float,
     *     price_from_input: float,
     *     price_from_formatted: string,
     *     price_from_parts: array,
     *     price_from_discounted: float|null,
     *     price_from_discounted_input: float|null,
     *     price_from_discounted_formatted: string|null,
     *     price_from_discounted_parts: array|null,
     *     price_saved: float|null,
     *     price_saved_formatted: string|null,
     *     price_saved_input: float|null,
     *     price_saved_parts: array|null,
     *     price_to: float,
     *     price_to_input: float,
     *     price_to_parts: array,
     *     price_to_formatted: string,
     *     price_percent: float,
     *     price_type: string,
     *     continue_selling: bool,
     *     date_added: Date,
     *     date_modified: Date,
     *     active_to: Date|null,
     *     short_description: string,
     *     type: string,
     *     is_discounted: bool,
     *     product_to_discount_price: float,
     *     product_to_discount_price_to: float,
     *     min_price_with_discounted: float,
     *     max_price_with_discounted: float,
     *     quantity: int,
     *     enable_online_selling: bool,
     *     overlay: Label|false,
     *     favorite: bool,
     *     variant: Variant,
     *     variants: array<Variant>,
     *     category_properties: array<PropertyOption>,
     *     category_properties_grouped: array<string, array<PropertyOption>>,
     *     image: Image|null,
     *     has_image: bool,
     *     is_hidden: bool,
     *     first_two_images: array<Image>,
     *     tags: array<TagModel>,
     *     banners: array<Banner>,
     *     labels: array<Label>,
     *     discount: Discount|null,
     *     options: array<Option>,
     *     total_options: int,
     *     selected_variant: Variant|null,
     *     selected_or_first_available_variant: Variant
     * }
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'url' => $this->url(),
            'add_to_cart' => $this->add_to_cart(),
            'wishlist_url' => $this->wishlist_url(),
            'url_handle' => $this->url_handle(),
            'p1' => $this->p1(),
            'p2' => $this->p2(),
            'p3' => $this->p3(),
            'parameter1' => $this->parameter1(),
            'parameter2' => $this->parameter2(),
            'parameter3' => $this->parameter3(),
            'total_variants' => $this->total_variants(),
            'has_required_data' => $this->has_required_data(),
            'category' => $this->category(),
            'vendor' => $this->vendor(),
            'views' => $this->views(),
            'tracking' => $this->tracking(),
            'threshold' => $this->threshold(),
            'shipping' => $this->shipping(),
            'digital' => $this->digital(),
            'sale' => $this->sale(),
            'new' => $this->new(),
            'free_shipping' => $this->free_shipping(),
            'featured' => $this->featured(),
            'diff_price' => $this->diff_price(),
            'price_from' => $this->price_from(),
            'price_from_input' => $this->price_from_input(),
            'price_from_formatted' => $this->price_from_formatted(),
            'price_from_parts' => $this->price_from_parts(),
            'price_from_discounted' => $this->price_from_discounted(),
            'price_from_discounted_input' => $this->price_from_discounted_input(),
            'price_from_discounted_formatted' => $this->price_from_discounted_formatted(),
            'price_from_discounted_parts' => $this->price_from_discounted_parts(),
            'price_saved' => $this->price_saved(),
            'price_saved_formatted' => $this->price_saved_formatted(),
            'price_saved_input' => $this->price_saved_input(),
            'price_saved_parts' => $this->price_saved_parts(),
            'price_to' => $this->price_to(),
            'price_to_input' => $this->price_to_input(),
            'price_to_parts' => $this->price_to_parts(),
            'price_to_formatted' => $this->price_to_formatted(),
            'price_percent' => $this->price_percent(),
            'price_type' => $this->price_type(),
            'continue_selling' => $this->continue_selling(),
            'date_added' => $this->date_added(),
            'date_modified' => $this->date_modified(),
            'active_to' => $this->active_to(),
            'short_description' => $this->short_description(),
            'type' => $this->type(),
            'is_discounted' => $this->is_discounted(),
            'product_to_discount_price' => $this->product_to_discount_price(),
            'product_to_discount_price_to' => $this->product_to_discount_price_to(),
            'min_price_with_discounted' => $this->min_price_with_discounted(),
            'max_price_with_discounted' => $this->max_price_with_discounted(),
            'quantity' => $this->quantity(),
            'enable_online_selling' => $this->enable_online_selling(),
            'overlay' => $this->overlay(),
            'favorite' => $this->favorite(),
            'variant' => $this->variant(),
            'variants' => $this->variants(),
            'category_properties' => $this->category_properties(),
            'category_properties_grouped' => $this->category_properties_grouped(),
            'image' => $this->image(),
            'has_image' => $this->has_image(),
            'is_hidden' => $this->is_hidden(),
            'first_two_images' => $this->first_two_images(),
            'tags' => $this->tags(),
            'banners' => $this->banners(),
            'labels' => $this->labels(),
            'discount' => $this->discount(),
            'options' => $this->options(),
            'total_options' => $this->total_options(),
            'selected_variant' => $this->selected_variant(),
            'selected_or_first_available_variant' => $this->selected_or_first_available_variant(),
        ];
    }

    /**
     * Get the product title.
     *
     * @return string
     */
    public function title(): string
    {
        return $this->name();
    }

    /**
     * Get the product handle.
     *
     * @return string
     */
    public function handle(): string
    {
        return $this->url_handle();
    }

    /**
     * Get the creation date.
     *
     * @return string
     */
    public function created_at(): string
    {
        return $this->date_added()->toDateString();
    }

    /**
     * Get the last update date.
     *
     * @return string
     */
    public function updated_at(): string
    {
        return $this->date_modified()->toDateString();
    }

    /**
     * Get the product description.
     *
     * @return string
     */
    public function description(): string
    {
        return (string) $this->_product->description;
    }

    /**
     * Check if the product is available.
     *
     * @return bool
     */
    public function available(): bool
    {
        if (!$this->enable_online_selling()) {
            return false;
        }

        if ($this->quantity() <= 0 && !$this->continue_selling()) {
            return false;
        }

        return true;
    }

    /**
     * Get the publication date.
     *
     * @return string|null
     */
    public function published_at(): ?string
    {
        return $this->date_added()->toDateString();
    }

    /**
     * Get the compare at price.
     *
     * @return float|null
     */
    public function compare_at_price(): ?float
    {
        return $this->price_from_discounted();
    }

    /**
     * Get the current price.
     *
     * @return float
     */
    public function price(): float
    {
        return $this->price_from();
    }
}
