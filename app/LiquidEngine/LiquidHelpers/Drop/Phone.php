<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Locale\Country;
use Exception;
use Illuminate\Support\Str;
use libphonenumber\PhoneNumber;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;

class Phone extends AbstractDrop
{

    /**
     * @var string $_phone
     */
    protected $_phone;

    /**
     * @var string $_phone_country
     */
    protected $_phone_country;

    /**
     * @var PhoneNumber
     */
    protected $_parsed;

    /**
     * @var PhoneNumberUtil
     */
    protected $_proto;

    protected $_valid = false;

    /**
     * @param mixed $phone
     * @param mixed $phone_country
     * @return mixed
     */
    public function __construct($phone, $phone_country)
    {
        $this->_phone = $phone;
        $this->_phone_country = Country::has($phone_country = Str::upper($phone_country)) ? $phone_country : null;
        $this->_proto = PhoneNumberUtil::getInstance();
        $this->_parse();
    }

    /**
     * @return string
     */
    public function phone()
    {
        return $this->_phone;
    }

    /**
     * @return string
     */
    public function country()
    {
        return $this->_phone_country;
    }

    /**
     * @return string
     */
    public function e164()
    {
        return $this->_format(PhoneNumberFormat::E164);
    }

    /**
     * @return string
     */
    public function national()
    {
        return $this->_format(PhoneNumberFormat::NATIONAL);
    }

    /**
     * @return string
     */
    public function international()
    {
        return $this->_format(PhoneNumberFormat::INTERNATIONAL);
    }

    /**
     * @return string
     */
    public function rfc3966()
    {
        return $this->_format(PhoneNumberFormat::RFC3966);
    }

    /**
     * Get phone number formatted for display
     * Shopify compatible: phone.formatted
     */
    public function formatted(): string
    {
        return $this->international() ?: $this->phone();
    }

    /**
     * Get area code from phone number
     * Shopify compatible: phone.area_code
     */
    public function area_code(): ?string
    {
        if (!$this->_valid || !$this->_parsed) {
            return null;
        }

        $national = $this->national();
        if ($national && preg_match('/\((\d{3})\)/', $national, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * Get country code from phone number
     * Shopify compatible: phone.country_code
     */
    public function country_code(): ?string
    {
        if (!$this->_valid || !$this->_parsed) {
            return null;
        }

        return (string) $this->_parsed->getCountryCode();
    }

    /**
     * Get extension from phone number (if any)
     * Shopify compatible: phone.extension
     */
    public function extension(): ?string
    {
        if (!$this->_valid || !$this->_parsed) {
            return null;
        }

        $extension = $this->_parsed->getExtension();
        return $extension ?: null;
    }

    /**
     * Get phone number without country code
     * Shopify compatible: phone.number
     */
    public function number(): string
    {
        $national = $this->national();
        if ($national) {
            // Remove common formatting characters but keep the number readable
            return preg_replace('/^\(?\d{1,4}\)?\s*/', '', $national);
        }

        return $this->phone();
    }

    /**
     * Get display name for phone number
     * Shopify compatible: phone.display_name
     */
    public function display_name(): string
    {
        return $this->formatted();
    }

    /**
     * Check if phone number is valid
     * Shopify compatible: phone.valid
     */
    public function valid(): bool
    {
        return $this->_valid;
    }

    /**
     * Get phone number type (mobile, fixed line, etc.)
     * Shopify compatible: phone.type
     */
    public function type(): ?string
    {
        if (!$this->_valid || !$this->_parsed || !$this->_proto) {
            return null;
        }

        try {
            $numberType = $this->_proto->getNumberType($this->_parsed);
            
            switch ($numberType) {
                case \libphonenumber\PhoneNumberType::MOBILE:
                    return 'mobile';
                case \libphonenumber\PhoneNumberType::FIXED_LINE:
                    return 'fixed_line';
                case \libphonenumber\PhoneNumberType::FIXED_LINE_OR_MOBILE:
                    return 'fixed_line_or_mobile';
                case \libphonenumber\PhoneNumberType::TOLL_FREE:
                    return 'toll_free';
                case \libphonenumber\PhoneNumberType::PREMIUM_RATE:
                    return 'premium_rate';
                case \libphonenumber\PhoneNumberType::SHARED_COST:
                    return 'shared_cost';
                case \libphonenumber\PhoneNumberType::VOIP:
                    return 'voip';
                case \libphonenumber\PhoneNumberType::PERSONAL_NUMBER:
                    return 'personal_number';
                case \libphonenumber\PhoneNumberType::PAGER:
                    return 'pager';
                case \libphonenumber\PhoneNumberType::UAN:
                    return 'uan';
                case \libphonenumber\PhoneNumberType::VOICEMAIL:
                    return 'voicemail';
                default:
                    return 'unknown';
            }
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Get phone number in tel: link format
     * Shopify compatible: phone.tel_link
     */
    public function tel_link(): string
    {
        $e164 = $this->e164();
        return $e164 ? 'tel:' . $e164 : 'tel:' . $this->phone();
    }

    /**
     * Get country name for phone number
     * Shopify compatible: phone.country_name
     */
    public function country_name(): ?string
    {
        if (!$this->_phone_country) {
            return null;
        }

        // Use CloudCart's Country helper if available, otherwise return country code
        if (class_exists('\App\Locale\Country')) {
            try {
                // Check if the country code exists in the Country helper
                if (Country::has($this->_phone_country)) {
                    return $this->_phone_country; // Return the country code as name for now
                }
            } catch (Exception $e) {
                // Fall through to default
            }
        }

        return $this->_phone_country;
    }

    /**
     * Get SMS link for phone number
     * Shopify compatible: phone.sms_link
     */
    public function sms_link(): string
    {
        $e164 = $this->e164();
        return $e164 ? 'sms:' . $e164 : 'sms:' . $this->phone();
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'phone' => $this->phone(),
            'country' => $this->country(),
            'e164' => $this->e164(),
            'national' => $this->national(),
            'international' => $this->international(),
            'rfc3966' => $this->rfc3966(),
            'formatted' => $this->formatted(),
            'area_code' => $this->area_code(),
            'country_code' => $this->country_code(),
            'extension' => $this->extension(),
            'number' => $this->number(),
            'display_name' => $this->display_name(),
            'valid' => $this->valid(),
            'type' => $this->type(),
            'tel_link' => $this->tel_link(),
            'country_name' => $this->country_name(),
            'sms_link' => $this->sms_link(),
        ];
    }

    /**
     * @return string
     */
    public function __toString(): string
    {
        return $this->e164() ?: '';
    }

    /**
     * @return bool
     */
    protected function _parse()
    {
        if (!$this->_phone || !$this->_phone_country) {
            return false;
        }

        try {
            $this->_parsed = $this->_proto->parse($this->_phone, $this->_phone_country);
            $this->_valid = $this->_proto->isValidNumber($this->_parsed);
        } catch (Exception $exception) {
        }

        return $this->_valid;
    }

    /**
     * @param $format
     * @return string|null
     */
    protected function _format($format)
    {
        if ($this->_valid && $this->_proto) {
            return $this->_proto->format($this->_parsed, $format);
        }

        return null;
    }

}
