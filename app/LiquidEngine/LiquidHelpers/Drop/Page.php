<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Models\Page\Page as PageModel;

class Page extends AbstractTextContent
{

    /**
     * @var PageModel $_page
     */
    protected \Illuminate\Database\Eloquent\Model $_page;

    /**
     * Page constructor.
     * @param PageModel $page
     */
    public function __construct(PageModel $page)
    {
        parent::__construct($page);
    }

    /**
     * Factory method to create a Page drop
     *
     * @param PageModel $page
     * @return self
     */
    public static function make(PageModel $page): self
    {
        return new self($page);
    }

    /**
     * Get the page's handle (URL handle)
     *
     * @return string
     */
    public function handle()
    {
        return $this->_page->url_handle;
    }

    /**
     * Get the page's title
     *
     * @return string
     */
    public function title()
    {
        return $this->_page->name;
    }

    /**
     * Get the page's author
     *
     * @return string
     */
    public function author()
    {
        // FIXED: No author field exists in database schema
        // Return empty string for now as there's no author field in pages table
        return '';
    }

    /**
     * Get the page's published date
     *
     * @return Date
     */
    public function published_at()
    {
        // VALID: date_added exists as accessor method, fallback to created_at
        $date = $this->_page->date_added ?? $this->_page->created_at;
        return new Date($date);
    }

    /**
     * Get the page's template suffix (if any)
     *
     * @return string
     */
    public function template_suffix()
    {
        // FIXED: No template field exists in database schema
        // Return empty string as there's no template field in pages table
        return '';
    }

    /**
     * Get the page's body (content)
     *
     * @return string
     */
    public function body()
    {
        return $this->_page->content;
    }

    /**
     * @return array
     */
    public function faq()
    {
        // FIXED: faq field doesn't exist in database schema
        // Return empty array as there's no faq field in pages table
        return [];
    }

    /**
     * @return string
     */
    public function type()
    {
        // FIXED: Use correct field name 'type' instead of incorrect field name
        return $this->_page->type ?? 'page';
    }

    /**
     * @return string
     */
    public function url()
    {
        return $this->_page->url();
    }

    /**
     * @return string
     */
    public function url_handle()
    {
        return $this->_page->url_handle;
    }

    /**
     * Shopify compatibility: created_at
     * Added to match Shopify's page.created_at specification
     *
     * @return Date
     */
    public function created_at()
    {
        return new Date($this->_page->created_at);
    }

    /**K
     * Shopify compatibility: updated_at
     * Added to match Shopify's page.updated_at specification
     *
     * @return Date
     */
    public function updated_at()
    {
        return new Date($this->_page->updated_at);
    }

    /**
     * Shopify compatibility: excerpt
     * Added to match Shopify's page.excerpt specification
     *
     * @return string
     */
    public function excerpt()
    {
        $content = $this->body();
        if (empty($content)) {
            return '';
        }

        // Create excerpt by taking first 150 characters, breaking at word boundary
        $excerpt = substr(strip_tags($content), 0, 150);
        $lastSpace = strrpos($excerpt, ' ');
        if ($lastSpace !== false && $lastSpace > 100) {
            $excerpt = substr($excerpt, 0, $lastSpace);
        }
        return $excerpt . (strlen($content) > 150 ? '...' : '');
    }

    /**
     * Shopify compatibility: content
     * Added to match Shopify's page.content specification
     *
     * @return string
     */
    public function content(): string
    {
        // Shopify uses "content" as alias for body
        return $this->body();
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            // EXISTING KEYS - PRESERVED EXACTLY:
            'faq' => $this->faq(),
            'type' => $this->type(),
            'url' => $this->url(),
            'url_handle' => $this->url_handle(),
            'handle' => $this->handle(),
            'title' => $this->title(),
            'author' => $this->author(),
            'published_at' => $this->published_at(),
            'template_suffix' => $this->template_suffix(),
            'body' => $this->body(),

            // NEW KEYS - ADDED FOR SHOPIFY COMPATIBILITY:
            'content' => $this->content(),
            'excerpt' => $this->excerpt(),
            'created_at' => $this->created_at(),
            'updated_at' => $this->updated_at(),
        ]);
    }
}
