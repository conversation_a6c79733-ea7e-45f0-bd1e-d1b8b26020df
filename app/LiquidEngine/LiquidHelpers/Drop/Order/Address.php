<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 20.3.2019 г.
 * Time: 16:33 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Order;

use App\Models\Order\ShippingAddress;
use App\LiquidEngine\LiquidHelpers\Drop\Customer\Address as CustomerAddress;

class Address extends CustomerAddress
{
    // Inherited functionality from Customer\Address and Helpers\Address:
    // - first_name(), last_name(), company(), address1(), address2()
    // - city(), province(), province_code(), zip(), country(), country_code()
    // - phone(), latitude(), longitude(), default(), id()
    // - All formatting and validation methods

    /**
     * @return string
     */
    public function type(): string
    {
        return $this->_address instanceof ShippingAddress ? 'shipping' : 'billing';
    }

    /**
     * Shopify Compatibility: name
     * Full name (uses existing full_name method)
     *
     * @return string
     */
    public function name(): string
    {
        return $this->full_name() ?? '';
    }

    /**
     * Shopify Compatibility: address1
     * Primary street address (uses existing street method)
     *
     * @return string
     */
    public function address1(): string
    {
        $street = $this->street();
        return $street ? $street->name() : '';
    }

    /**
     * Shopify Compatibility: address2
     * Secondary address line (street number)
     *
     * @return string
     */
    public function address2(): string
    {
        return $this->street_number();
    }

    /**
     * Shopify Compatibility: city
     * City name string
     *
     * @return string
     */
    public function city_name(): string
    {
        $city = $this->city();
        return $city ? $city->name() : '';
    }

    /**
     * Shopify Compatibility: province
     * Province/state name
     *
     * @return string
     */
    public function province(): string
    {
        $state = $this->state();
        return $state ? $state->name() : '';
    }

    /**
     * Shopify Compatibility: province_code
     * Province/state code
     *
     * @return string
     */
    public function province_code(): string
    {
        $state = $this->state();
        return $state ? $state->iso2() : '';
    }

    /**
     * Shopify Compatibility: zip
     * Postal code (alias for post_code)
     *
     * @return string
     */
    public function zip(): string
    {
        return $this->post_code() ?? '';
    }

    /**
     * Shopify Compatibility: country_name
     * Country name
     *
     * @return string
     */
    public function country_name(): string
    {
        $country = $this->country();
        return $country ? $country->name() : '';
    }

    /**
     * Shopify Compatibility: country_code
     * Country code
     *
     * @return string
     */
    public function country_code(): string
    {
        $country = $this->country();
        return $country ? $country->iso2() : '';
    }

    /**
     * Shopify Compatibility: company
     * Company name
     *
     * @return string
     */
    public function company(): string
    {
        return $this->company_name() ?? '';
    }

    /**
     * Shopify Compatibility: summary
     * Complete formatted address summary
     *
     * @return string
     */
    public function summary(): string
    {
        $parts = array_filter([
            $this->name(),
            $this->company(),
            $this->address1(),
            $this->address2(),
            $this->city_name(),
            $this->province(),
            $this->zip(),
            $this->country_name()
        ]);

        return implode(', ', $parts);
    }

    /**
     * Shopify Compatibility: url
     * Address URL (for maps or address management)
     *
     * @return null|string
     */
    public function url(): ?string
    {
        // Order addresses typically don't have management URLs
        // But we can provide a Google Maps URL if coordinates exist
        $lat = $this->latitude();
        $lng = $this->longitude();

        if ($lat !== null && $lng !== null) {
            return "https://maps.google.com/?q={$lat},{$lng}";
        }

        return null;
    }

    /**
     * Convert to array for Vue.js frontend and API responses
     * Includes all inherited methods plus new Shopify compatibility methods
     *
     * @return array
     */
    public function toArray(): array
    {
        $inherited = parent::toArray();

        $shopifyMethods = [
            'name' => $this->name(),
            'address1' => $this->address1(),
            'address2' => $this->address2(),
            'city_name' => $this->city_name(),
            'province' => $this->province(),
            'province_code' => $this->province_code(),
            'zip' => $this->zip(),
            'country_name' => $this->country_name(),
            'country_code' => $this->country_code(),
            'company' => $this->company(),
            'summary' => $this->summary(),
            'url' => $this->url(),
        ];

        return array_merge($inherited, $shopifyMethods);
    }
}
