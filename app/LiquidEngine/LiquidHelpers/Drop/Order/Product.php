<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Order;

use App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute;
use App\Helper\YesNo;
use App\Models\Order\OrderProduct;
use App\Models\Product\Image;
use App\Models\Product\ProductFiles;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Order\Product\Parameter;
use App\LiquidEngine\LiquidHelpers\Drop\Product\File;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;
use App\Helper\Format;
use App\LiquidEngine\LiquidHelpers\Drop\Product as ProductDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Variant;

/**
 * Product Drop Class
 *
 * This class represents order product information in Liquid templates, providing access to product properties
 * and methods for Shopify compatibility, accessibility, and SEO features.
 *
 * Properties:
 * - name: Product name
 * - sku: Stock keeping unit
 * - barcode: Product barcode
 * - url: Product URL
 * - url_handle: URL-friendly product handle
 * - p1, p2, p3: Product parameters
 * - v1, v2, v3: Product values
 * - digital: Whether product is digital
 * - weight: Weight in minor unit
 * - weight_input: Raw weight value
 * - weight_formatted: Formatted weight with unit
 * - price: Price in currency's minor unit
 * - price_input: Raw price value
 * - price_formatted: Formatted price with currency
 * - price_parts: Price components for formatting
 * - order_price: Order-specific price in minor unit
 * - order_price_input: Order-specific raw price
 * - order_price_formatted: Order-specific formatted price
 * - order_price_parts: Order-specific price components
 * - quantity: Ordered quantity
 * - image: Product image
 * - has_image: Whether product has an image
 * - parameters: Product parameters
 * - files: Product files
 * - line_price: Total price for line item
 * - original_line_price: Original total price
 * - final_line_price: Final total price
 * - unit_price: Price per unit
 * - original_price: Original unit price
 * - final_price: Final unit price
 * - line_price_with_currency: Formatted line price
 * - key: Unique identifier
 * - vendor: Product vendor
 * - product_type: Product type
 * - grams: Weight in grams
 * - gift_card: Whether is gift card
 * - product: Associated product
 * - variant: Product variant
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop\Order
 */
class Product extends AbstractDrop
{
    use DropImageToArray;

    /**
     * @var OrderProduct The product model
     */
    protected OrderProduct $_product;

    /**
     * @var string The locale for translations
     */
    protected string $_locale;

    /**
     * @var string The currency code
     */
    protected string $_currency;

    /**
     * Product constructor.
     *
     * @param OrderProduct $product The product model
     * @param string|null $locale The locale for translations
     * @param string|null $currency The currency code
     */
    public function __construct(OrderProduct $product, ?string $locale = null, ?string $currency = null)
    {
        $this->_product = $product;
        $this->_locale = $locale ?: setting('language');
        $this->_currency = $currency ?: site('currency');
    }

    /**
     * Get the product name
     *
     * @return string The product name
     */
    public function name(): string
    {
        return $this->_product->name;
    }

    /**
     * Get the stock keeping unit
     *
     * @return string The SKU
     */
    public function sku(): string
    {
        return $this->_product->sku;
    }

    /**
     * Get the product barcode
     *
     * @return string The barcode
     */
    public function barcode(): string
    {
        return $this->_product->barcode;
    }

    /**
     * Get the product URL
     *
     * @return string The product URL
     */
    public function url(): string
    {
        return $this->_product->url();
    }

    /**
     * Get the URL-friendly product handle
     *
     * @return string The URL handle
     */
    public function url_handle(): string
    {
        return $this->_product->url_handle;
    }

    /**
     * Get the first product parameter
     *
     * @return string|null The parameter value
     */
    public function p1(): ?string
    {
        return $this->_product->p1;
    }

    /**
     * Get the second product parameter
     *
     * @return string|null The parameter value
     */
    public function p2(): ?string
    {
        return $this->_product->p2;
    }

    /**
     * Get the third product parameter
     *
     * @return string|null The parameter value
     */
    public function p3(): ?string
    {
        return $this->_product->p3;
    }

    /**
     * Get the first product value
     *
     * @return string|null The value
     */
    public function v1(): ?string
    {
        return $this->_product->v1;
    }

    /**
     * Get the second product value
     *
     * @return string|null The value
     */
    public function v2(): ?string
    {
        return $this->_product->v2;
    }

    /**
     * Get the third product value
     *
     * @return string|null The value
     */
    public function v3(): ?string
    {
        return $this->_product->v3;
    }

    /**
     * Check if the product is digital
     *
     * @return bool Whether the product is digital
     */
    public function digital(): bool
    {
        return $this->_product->digital === YesNo::True;
    }

    /**
     * Get the weight in minor unit
     *
     * @return float|null The weight
     */
    public function weight(): ?float
    {
        return $this->_product->weight;
    }

    /**
     * Get the raw weight value
     *
     * @return float|null The raw weight
     */
    public function weight_input(): ?float
    {
        return $this->_product->weight_input;
    }

    /**
     * Get the formatted weight with unit
     *
     * @return string|null The formatted weight
     */
    public function weight_formatted(): ?string
    {
        return $this->_product->weight_formatted;
    }

    /**
     * Get the price in currency's minor unit
     *
     * @return float|null The price
     */
    public function price(): ?float
    {
        return $this->_product->price;
    }

    /**
     * Get the raw price value
     *
     * @return float|null The raw price
     */
    public function price_input(): ?float
    {
        return $this->_product->price_input;
    }

    /**
     * Get the formatted price with currency
     *
     * @return string|null The formatted price
     */
    public function price_formatted(): ?string
    {
        return $this->_product->price_formatted;
    }

    /**
     * Get the price components for formatting
     *
     * @return array{
     *     amount: string,
     *     currency: string,
     *     currency_symbol: string,
     *     decimal_separator: string,
     *     thousands_separator: string,
     *     decimal_places: int,
     *     formatted: string
     * } The price parts
     */
    public function price_parts(): array
    {
        return PriceParts::format($this->price_input(), $this->_currency, $this->_locale);
    }

    /**
     * Get the order-specific price in minor unit
     *
     * @return float|null The order price
     */
    public function order_price(): ?float
    {
        return $this->_product->order_price;
    }

    /**
     * Get the order-specific raw price
     *
     * @return float|null The raw order price
     */
    public function order_price_input(): ?float
    {
        return $this->_product->order_price_input;
    }

    /**
     * Get the order-specific formatted price
     *
     * @return string|null The formatted order price
     */
    public function order_price_formatted(): ?string
    {
        return $this->_product->order_price_formatted;
    }

    /**
     * Get the order-specific price components
     *
     * @return array{
     *     amount: string,
     *     currency: string,
     *     currency_symbol: string,
     *     decimal_separator: string,
     *     thousands_separator: string,
     *     decimal_places: int,
     *     formatted: string
     * } The order price parts
     */
    public function order_price_parts(): array
    {
        return PriceParts::format($this->order_price_input(), $this->_currency, $this->_locale);
    }

    /**
     * Get the ordered quantity
     *
     * @return int The quantity
     */
    public function quantity(): int
    {
        return $this->_product->quantity;
    }

    /**
     * Get the product image
     *
     * @return LiquidImageAttribute The image
     */
    public function image(): LiquidImageAttribute
    {
        $image = new Image();
        if ($this->_product->relationLoaded('product') && !empty($this->_product->product->image_id)) {
            $image = $this->_product->product->image_id ? $this->_product->product->image : $image;
        }

        $formatter = new UrlImageFormat($image);
        return $formatter->getLiquidImage();
    }

    /**
     * Check if the product has an image
     *
     * @return bool Whether the product has an image
     */
    public function has_image(): bool
    {
        return $this->_product->relationLoaded('product') && $this->_product->hasImage();
    }

    /**
     * Get the product parameters
     *
     * @return array<Parameter> The parameters
     */
    public function parameters(): array
    {
        return array_map(function ($parameter): Parameter {
            return new Parameter($parameter);
        }, $this->_product->parameters ?: []);
    }

    /**
     * Get the product files
     *
     * @return array<File> The files
     */
    public function files(): array
    {
        $files = [];
        if ($this->_product->relationLoaded('files') && $this->_product->files->count()) {
            $files = $this->_product->files->map(function (ProductFiles $file): File {
                return new File($file, $this->_product->url_handle);
            })->all();
        }

        return $files;
    }

    /**
     * Get the total price for this line item (quantity * unit_price)
     * Shopify compatibility: line_price
     *
     * @return float|null The line price
     */
    public function line_price(): ?float
    {
        if ($this->_product->price === null || $this->_product->quantity === null) {
            return null;
        }

        return (float) ($this->_product->price * $this->_product->quantity);
    }

    /**
     * Get the original total price before any discounts
     * Shopify compatibility: original_line_price
     *
     * @return float|null The original line price
     */
    public function original_line_price(): ?float
    {
        if ($this->_product->original_price === null || $this->_product->quantity === null) {
            return $this->line_price(); // Fallback to current price
        }

        return (float) ($this->_product->original_price * $this->_product->quantity);
    }

    /**
     * Get the final total price after all discounts
     * Shopify compatibility: final_line_price
     *
     * @return float|null The final line price
     */
    public function final_line_price(): ?float
    {
        if ($this->_product->final_price === null || $this->_product->quantity === null) {
            return $this->line_price(); // Fallback to current price
        }

        return (float) ($this->_product->final_price * $this->_product->quantity);
    }

    /**
     * Get the price per unit
     * Shopify compatibility: unit_price
     *
     * @return float|null The unit price
     */
    public function unit_price(): ?float
    {
        if ($this->_product->price === null) {
            return null;
        }

        return (float) $this->_product->price;
    }

    /**
     * Get the original unit price before any discounts
     * Shopify compatibility: original_price
     *
     * @return float|null The original price
     */
    public function original_price(): ?float
    {
        if ($this->_product->original_price === null) {
            return $this->unit_price(); // Fallback to current price
        }

        return (float) $this->_product->original_price;
    }

    /**
     * Get the final unit price after all discounts
     * Shopify compatibility: final_price
     *
     * @return float|null The final price
     */
    public function final_price(): ?float
    {
        if ($this->_product->final_price === null) {
            return $this->unit_price(); // Fallback to current price
        }

        return (float) $this->_product->final_price;
    }

    /**
     * Get the formatted line price with currency
     * Shopify compatibility: line_price_with_currency
     *
     * @return string The formatted line price
     */
    public function line_price_with_currency(): string
    {
        $line_price = $this->line_price();

        if ($line_price === null) {
            return '';
        }

        return Format::money($line_price);
    }

    /**
     * Get the unique identifier
     * Shopify compatibility: key
     *
     * @return string The key
     */
    public function key(): string
    {
        $variant_id = $this->_product->variant_id ?? 'no-variant';
        $product_id = $this->_product->product_id ?? 'no-product';

        return (string) "{$product_id}:{$variant_id}";
    }

    /**
     * Get the product vendor
     * Shopify compatibility: vendor
     *
     * @return null|string The vendor
     */
    public function vendor(): ?string
    {
        try {
            $product = $this->product();
            return $product ? $product->vendor() : null;
        } catch (\Throwable $e) {
            return null;
        }
    }

    /**
     * Get the product type
     * Shopify compatibility: product_type
     *
     * @return null|string The product type
     */
    public function product_type(): ?string
    {
        try {
            $product = $this->product();
            return $product ? $product->type() : null;
        } catch (\Throwable $e) {
            return null;
        }
    }

    /**
     * Get the weight in grams
     * Shopify compatibility: grams
     *
     * @return null|int The weight in grams
     */
    public function grams(): ?int
    {
        if ($this->_product->weight === null) {
            return null;
        }

        // Convert weight to grams (assuming weight is in kg)
        return (int) ($this->_product->weight * 1000);
    }

    /**
     * Check if the product is a gift card
     * Shopify compatibility: gift_card
     *
     * @return bool Whether the product is a gift card
     */
    public function gift_card(): bool
    {
        // Check if product type indicates gift card
        $product_type = $this->product_type();

        if ($product_type && stripos($product_type, 'gift') !== false) {
            return true;
        }

        // Check product title
        $title = $this->name();
        if ($title && stripos($title, 'gift card') !== false) {
            return true;
        }

        return false;
    }

    /**
     * Get the associated product
     *
     * @return null|ProductDrop The product
     */
    public function product(): ?ProductDrop
    {
        if ($this->_product->product_id === null) {
            return null;
        }

        try {
            $product = \App\Models\Product\Product::find($this->_product->product_id);
            if ($product) {
                return new ProductDrop($product);
            }
            return null;
        } catch (\Throwable $e) {
            return null;
        }
    }

    /**
     * Get the product variant
     *
     * @return null|Variant The variant
     */
    public function variant(): ?Variant
    {
        if ($this->_product->variant_id === null) {
            return null;
        }

        try {
            $variant = \App\Models\Product\Variant::find($this->_product->variant_id);
            if ($variant) {
                $product = \App\Models\Product\Product::find($this->_product->product_id);
                if ($product) {
                    return new Variant($variant, $product);
                }
            }
            return null;
        } catch (\Throwable $e) {
            return null;
        }
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     name: string,
     *     sku: string,
     *     barcode: string,
     *     url: string,
     *     url_handle: string,
     *     p1: string|null,
     *     p2: string|null,
     *     p3: string|null,
     *     v1: string|null,
     *     v2: string|null,
     *     v3: string|null,
     *     digital: bool,
     *     weight: float|null,
     *     weight_input: float|null,
     *     weight_formatted: string|null,
     *     price: float|null,
     *     price_input: float|null,
     *     price_formatted: string|null,
     *     price_parts: array,
     *     order_price: float|null,
     *     order_price_input: float|null,
     *     order_price_formatted: string|null,
     *     order_price_parts: array,
     *     quantity: int,
     *     image: LiquidImageAttribute,
     *     has_image: bool,
     *     parameters: array<Parameter>,
     *     files: array<File>,
     *     line_price: float|null,
     *     original_line_price: float|null,
     *     final_line_price: float|null,
     *     unit_price: float|null,
     *     original_price: float|null,
     *     final_price: float|null,
     *     line_price_with_currency: string,
     *     key: string,
     *     vendor: string|null,
     *     product_type: string|null,
     *     grams: int|null,
     *     gift_card: bool,
     *     product: ProductDrop|null,
     *     variant: Variant|null
     * } The product data
     */
    public function toArray(): array
    {
        $existing_methods = [
            'name' => $this->name(),
            'sku' => $this->sku(),
            'barcode' => $this->barcode(),
            'url' => $this->url(),
            'url_handle' => $this->url_handle(),
            'p1' => $this->p1(),
            'v1' => $this->v1(),
            'p2' => $this->p2(),
            'v2' => $this->v2(),
            'p3' => $this->p3(),
            'v3' => $this->v3(),
            'digital' => $this->digital(),
            'weight' => $this->weight(),
            'weight_input' => $this->weight_input(),
            'weight_formatted' => $this->weight_formatted(),
            'price' => $this->price(),
            'price_input' => $this->price_input(),
            'price_formatted' => $this->price_formatted(),
            'price_parts' => $this->price_parts(),
            'order_price' => $this->order_price(),
            'order_price_input' => $this->order_price_input(),
            'order_price_formatted' => $this->order_price_formatted(),
            'order_price_parts' => $this->order_price_parts(),
            'quantity' => $this->quantity(),
            'has_image' => $this->has_image(),
            'image' => $this->getImageArray(),
        ];

        return array_merge($existing_methods, [
            'line_price' => $this->line_price(),
            'original_line_price' => $this->original_line_price(),
            'final_line_price' => $this->final_line_price(),
            'unit_price' => $this->unit_price(),
            'original_price' => $this->original_price(),
            'final_price' => $this->final_price(),
            'line_price_with_currency' => $this->line_price_with_currency(),
            'key' => $this->key(),
            'vendor' => $this->vendor(),
            'product_type' => $this->product_type(),
            'grams' => $this->grams(),
            'gift_card' => $this->gift_card(),
            'product' => $this->product(),
            'variant' => $this->variant()
        ]);
    }
}
