<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Order;

use App\Models\Order\OrderTotals;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use App\Helper\Format;

/**
 * Total Drop Class
 * 
 * This class represents order total information in Liquid templates, providing access to total properties
 * and methods for Shopify compatibility, accessibility, and SEO features.
 * 
 * Properties:
 * - key: Unique identifier combining group and key
 * - prefix: Total group prefix
 * - suffix: Total key suffix
 * - name: Total name/description
 * - description: Detailed description
 * - price: Price in currency's minor unit
 * - price_input: Raw price value
 * - price_formatted: Formatted price with currency
 * - price_parts: Price components for formatting
 * - price_without_vat: Price excluding VAT in minor unit
 * - price_input_without_vat: Raw price excluding VAT
 * - price_formatted_without_vat: Formatted price excluding VAT
 * - currency: Currency code
 * - value: Additional value data
 * - invoice_name: Name used on invoice
 * - use_for_total: Whether included in total
 * - use_for_total_without_vat: Whether included in VAT-free total
 * - hide_in_invoice: Whether hidden in invoice
 * - is_vat: Whether represents VAT/tax
 * - vat_rate: VAT rate percentage
 * - sort_order: Display order
 * - group: Total group
 * - price_with_currency: Price with currency symbol
 * - order_id: Associated order ID
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Order
 */
class Total extends AbstractDrop
{
    /**
     * @var OrderTotals The total model
     */
    protected OrderTotals $_total;

    /**
     * @var string The locale for translations
     */
    protected string $_locale;

    /**
     * Total constructor.
     * 
     * @param OrderTotals $total The total model
     * @param string|null $locale The locale for translations
     */
    public function __construct(OrderTotals $total, ?string $locale = null)
    {
        $this->_total = $total;
        $this->_locale = $locale ?: setting('language');
    }

    /**
     * Get the unique identifier combining group and key
     * Enhanced to provide better Shopify compatibility
     * 
     * @return string The unique key
     */
    public function key(): string
    {
        return sprintf('%s.%s', $this->prefix(), $this->suffix());
    }

    /**
     * Get the total group prefix
     * 
     * @return string The group prefix
     */
    public function prefix(): string
    {
        return $this->_total->group;
    }

    /**
     * Get the total key suffix
     * 
     * @return string The key suffix
     */
    public function suffix(): string
    {
        return $this->_total->key;
    }

    /**
     * Get the total name/description
     * 
     * @return string The total name
     */
    public function name(): string
    {
        return $this->_total->name;
    }

    /**
     * Get the detailed description
     * 
     * @return string The description
     */
    public function description(): string
    {
        return $this->_total->description;
    }

    /**
     * Get the price in currency's minor unit
     * Enhanced to handle null values properly and provide proper type casting
     * 
     * @return int|null The price in minor unit
     */
    public function price(): ?int
    {
        return is_numeric($this->_total->price) ? (int)$this->_total->price : null;
    }

    /**
     * Get the raw price value
     * Enhanced to handle null values and provide proper type casting
     * 
     * @return float|null The raw price
     */
    public function price_input(): ?float
    {
        return $this->_total->price_input;
    }

    /**
     * Get the formatted price with currency
     * Enhanced to provide better formatting with fallback logic
     * 
     * @return string|null The formatted price
     */
    public function price_formatted(): ?string
    {
        if ($this->_total->price_formatted) {
            return $this->_total->price_formatted;
        }
        
        if ($this->price_input() !== null && $this->currency()) {
            return Format::money($this->price_input(), true, $this->currency());
        }
        
        return null;
    }

    /**
     * Get the price components for formatting
     * Enhanced to provide comprehensive price parts breakdown
     * 
     * @return array{
     *     amount: string,
     *     currency: string,
     *     currency_symbol: string,
     *     decimal_separator: string,
     *     thousands_separator: string,
     *     decimal_places: int,
     *     formatted: string
     * } The price parts
     */
    public function price_parts(): array
    {
        return PriceParts::format($this->price_input(), $this->_total->currency, $this->_locale);
    }

    /**
     * Get the price excluding VAT in minor unit
     * Shopify compatibility: price_without_vat
     * 
     * @return float|null The price without VAT
     */
    public function price_without_vat(): ?float
    {
        return $this->_total->price_without_vat;
    }

    /**
     * Get the raw price excluding VAT
     * Shopify compatibility: price_input_without_vat
     * 
     * @return float|null The raw price without VAT
     */
    public function price_input_without_vat(): ?float
    {
        return $this->_total->price_input_without_vat;
    }

    /**
     * Get the formatted price excluding VAT with currency
     * Shopify compatibility: price_formatted_without_vat
     * 
     * @return string|null The formatted price without VAT
     */
    public function price_formatted_without_vat(): ?string
    {
        if ($this->_total->price_formatted_without_vat) {
            return $this->_total->price_formatted_without_vat;
        }
        
        if ($this->price_input_without_vat() !== null && $this->currency()) {
            return Format::money($this->price_input_without_vat(), true, $this->currency());
        }
        
        return null;
    }

    /**
     * Get the currency code
     * Shopify compatibility: currency
     * 
     * @return string|null The currency code
     */
    public function currency(): ?string
    {
        return $this->_total->currency;
    }

    /**
     * Get additional value data
     * Shopify compatibility: value
     * 
     * @return array|null The additional value data
     */
    public function value(): ?array
    {
        return $this->_total->value;
    }

    /**
     * Get the name used on invoice
     * Shopify compatibility: invoice_name
     * 
     * @return string The invoice name
     */
    public function invoice_name(): string
    {
        return $this->_total->invoice_name ?: $this->name();
    }

    /**
     * Check if included in total calculation
     * Shopify compatibility: use_for_total
     * 
     * @return bool Whether included in total
     */
    public function use_for_total(): bool
    {
        return (bool) $this->_total->use_for_total;
    }

    /**
     * Check if included in VAT-free total calculation
     * Shopify compatibility: use_for_total_without_vat
     * 
     * @return bool Whether included in VAT-free total
     */
    public function use_for_total_without_vat(): bool
    {
        return (bool) $this->_total->use_for_total_without_vat;
    }

    /**
     * Check if hidden in invoice display
     * Shopify compatibility: hide_in_invoice
     * 
     * @return bool Whether hidden in invoice
     */
    public function hide_in_invoice(): bool
    {
        return (bool) $this->_total->hide_in_invoice;
    }

    /**
     * Check if represents VAT/tax
     * Shopify compatibility: is_vat
     * 
     * @return bool Whether represents VAT
     */
    public function is_vat(): bool
    {
        return (bool) $this->_total->is_vat;
    }

    /**
     * Get the VAT rate percentage
     * 
     * @return int|null The VAT rate
     */
    public function vat_rate(): ?int
    {
        return $this->_total->vat_rate;
    }

    /**
     * Get the display order
     * 
     * @return int The sort order
     */
    public function sort_order(): int
    {
        return $this->_total->sort_order ?? 0;
    }

    /**
     * Get the total group
     * 
     * @return string|null The group
     */
    public function group(): ?string
    {
        return $this->_total->group;
    }

    /**
     * Get the price with currency symbol
     * 
     * @return string The price with currency
     */
    public function price_with_currency(): string
    {
        if ($this->price_formatted()) {
            return $this->price_formatted();
        }

        $amount = $this->price_input();
        if ($amount === null) {
            return '';
        }

        return Format::money($amount, true, $this->currency());
    }

    /**
     * Get the price without VAT formatted
     * 
     * @return string|null The formatted price without VAT
     */
    public function price_without_vat_formatted(): ?string
    {
        if ($this->price_formatted_without_vat()) {
            return $this->price_formatted_without_vat();
        }

        $amount = $this->price_input_without_vat();
        if ($amount === null) {
            return null;
        }

        return Format::money($amount, true, $this->currency());
    }

    /**
     * Get the price parts without VAT
     * 
     * @return array{
     *     amount: string,
     *     currency: string,
     *     currency_symbol: string,
     *     decimal_separator: string,
     *     thousands_separator: string,
     *     decimal_places: int,
     *     formatted: string
     * } The price parts without VAT
     */
    public function price_parts_without_vat(): array
    {
        return PriceParts::format(
            $this->price_input_without_vat(),
            $this->_total->currency,
            $this->_locale
        );
    }

    /**
     * Get the associated order ID
     * 
     * @return int|null The order ID
     */
    public function order_id(): ?int
    {
        return $this->_total->order_id;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     key: string,
     *     prefix: string,
     *     suffix: string,
     *     name: string,
     *     description: string,
     *     price: int|null,
     *     price_input: float|null,
     *     price_formatted: string|null,
     *     price_parts: array,
     *     price_without_vat: float|null,
     *     price_input_without_vat: float|null,
     *     price_formatted_without_vat: string|null,
     *     currency: string|null,
     *     value: array|null,
     *     invoice_name: string,
     *     use_for_total: bool,
     *     use_for_total_without_vat: bool,
     *     hide_in_invoice: bool,
     *     is_vat: bool,
     *     vat_rate: int|null,
     *     sort_order: int,
     *     group: string|null,
     *     price_with_currency: string,
     *     price_without_vat_formatted: string|null,
     *     price_parts_without_vat: array,
     *     order_id: int|null
     * } The total data
     */
    public function toArray(): array
    {
        return [
            'key' => $this->key(),
            'prefix' => $this->prefix(),
            'suffix' => $this->suffix(),
            'name' => $this->name(),
            'description' => $this->description(),
            'price' => $this->price(),
            'price_input' => $this->price_input(),
            'price_formatted' => $this->price_formatted(),
            'price_parts' => $this->price_parts(),
            'price_without_vat' => $this->price_without_vat(),
            'price_input_without_vat' => $this->price_input_without_vat(),
            'price_formatted_without_vat' => $this->price_formatted_without_vat(),
            'currency' => $this->currency(),
            'value' => $this->value(),
            'invoice_name' => $this->invoice_name(),
            'use_for_total' => $this->use_for_total(),
            'use_for_total_without_vat' => $this->use_for_total_without_vat(),
            'hide_in_invoice' => $this->hide_in_invoice(),
            'is_vat' => $this->is_vat(),
            'vat_rate' => $this->vat_rate(),
            'sort_order' => $this->sort_order(),
            'group' => $this->group(),
            'price_with_currency' => $this->price_with_currency(),
            'price_without_vat_formatted' => $this->price_without_vat_formatted(),
            'price_parts_without_vat' => $this->price_parts_without_vat(),
            'order_id' => $this->order_id()
        ];
    }
}
