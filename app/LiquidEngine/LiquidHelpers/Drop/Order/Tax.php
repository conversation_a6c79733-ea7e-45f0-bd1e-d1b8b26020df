<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Order;

use App\Helper\YesNo;
use App\Models\Order\OrderTax;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;

/**
 * Tax Drop Class
 * 
 * This class represents tax information in Liquid templates, providing access to tax properties
 * and methods for Shopify compatibility, accessibility, and SEO features.
 * 
 * Properties:
 * - name: Tax name/description
 * - vat: Whether this is a VAT tax
 * - shipping: Whether this tax applies to shipping
 * - amount: Tax amount in cents
 * - amount_input: Raw tax amount
 * - amount_formatted: Formatted tax amount
 * - amount_parts: Tax amount parts
 * - title: Tax title (Shopify compatibility)
 * - price: Tax price in cents
 * - rate: Tax rate as decimal
 * - rate_percentage: Tax rate as percentage
 * - channel_liable: Whether channel is liable for tax
 * - compare_at: Original tax amount
 * - position: Tax line position
 * - source: Tax source/authority
 * - zone: Tax zone/jurisdiction
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Order
 */
class Tax extends AbstractDrop
{
    /**
     * @var OrderTax The tax model
     */
    protected OrderTax $_tax;

    /**
     * @var string The locale for translations
     */
    protected string $_locale;

    /**
     * @var string The currency code
     */
    protected string $_currency;

    /**
     * Tax constructor.
     * 
     * @param OrderTax $tax The tax model
     * @param string|null $locale The locale for translations
     * @param string|null $currency The currency code
     */
    public function __construct(
        OrderTax $tax,
        ?string $locale = null,
        ?string $currency = null
    ) {
        $this->_tax = $tax;
        $this->_locale = $locale ?: setting('language');
        $this->_currency = $currency ?: site('currency');
    }

    /**
     * Get the tax name/description
     * 
     * @return string The tax name
     */
    public function name(): string
    {
        return $this->_tax->tax_name;
    }

    /**
     * Check if this is a VAT tax
     * 
     * @return bool Whether this is a VAT tax
     */
    public function vat(): bool
    {
        return $this->_tax->tax_vat === YesNo::True;
    }

    /**
     * Check if this tax applies to shipping
     * 
     * @return bool Whether this tax applies to shipping
     */
    public function shipping(): bool
    {
        return $this->_tax->tax_shipping === YesNo::True;
    }

    /**
     * Get the tax amount in cents
     * 
     * @return int|null The tax amount
     */
    public function amount(): ?int
    {
        return is_numeric($this->_tax->order_amount) ? (int)$this->_tax->order_amount : null;
    }

    /**
     * Get the raw tax amount
     * 
     * @return float|null The raw amount
     */
    public function amount_input(): ?float
    {
        return $this->_tax->order_amount_input;
    }

    /**
     * Get the formatted tax amount
     * 
     * @return string|null The formatted amount
     */
    public function amount_formatted(): ?string
    {
        return $this->_tax->order_amount_formatted;
    }

    /**
     * Get the tax amount parts
     * 
     * @return array{
     *     amount: string,
     *     currency: string,
     *     currency_symbol: string,
     *     decimal_separator: string,
     *     thousands_separator: string,
     *     decimal_places: int,
     *     formatted: string
     * } The amount parts
     */
    public function amount_parts(): array
    {
        return PriceParts::format($this->amount_input(), $this->_currency, $this->_locale);
    }

    // ==============================================
    // SHOPIFY TAX_LINE COMPATIBILITY METHODS
    // ==============================================

    /**
     * Get the tax title (Shopify compatibility)
     * The name of the tax
     * 
     * @return string The tax title
     */
    public function title(): string
    {
        return $this->name();
    }

    /**
     * Get the tax price in cents (Shopify compatibility)
     * The amount of tax to be charged in the currency's subunit
     * 
     * @return int The tax price
     */
    public function price(): int
    {
        return $this->amount() ?? 0;
    }

    /**
     * Get the tax rate as decimal (Shopify compatibility)
     * The rate of tax to be applied as a decimal
     * 
     * @return float The tax rate
     */
    public function rate(): float
    {
        return $this->_tax->tax_rate ?? 0.0;
    }

    /**
     * Get the tax rate as percentage (Shopify compatibility)
     * The rate of tax to be applied as a percentage
     * 
     * @return float The tax rate percentage
     */
    public function rate_percentage(): float
    {
        return $this->rate() * 100;
    }

    /**
     * Check if channel is liable for tax (Shopify compatibility)
     * Whether the channel that submitted the tax line is liable for remitting
     * 
     * @return bool|null Whether channel is liable
     */
    public function channel_liable(): ?bool
    {
        return $this->_tax->channel_liable;
    }

    /**
     * Get the original tax amount (Shopify compatibility)
     * The tax amount before any discounts
     * 
     * @return int The original amount
     */
    public function compare_at(): int
    {
        return $this->_tax->original_amount ?? $this->amount() ?? 0;
    }

    /**
     * Get the tax line position (Shopify compatibility)
     * The position of this tax line in the collection
     * 
     * @return int The position
     */
    public function position(): int
    {
        return $this->_tax->sort_order ?? 1;
    }

    /**
     * Get the tax source (Shopify compatibility)
     * The source/authority of the tax (manual, automatic, etc.)
     * 
     * @return string The tax source
     */
    public function source(): string
    {
        return $this->_tax->source ?? 'automatic';
    }

    /**
     * Get the tax zone (Shopify compatibility)
     * The tax zone or jurisdiction name
     * 
     * @return string|null The tax zone
     */
    public function zone(): ?string
    {
        return $this->_tax->tax_zone;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     name: string,
     *     vat: bool,
     *     shipping: bool,
     *     amount: int|null,
     *     amount_input: float|null,
     *     amount_formatted: string|null,
     *     amount_parts: array,
     *     title: string,
     *     price: int,
     *     rate: float,
     *     rate_percentage: float,
     *     channel_liable: bool|null,
     *     compare_at: int,
     *     position: int,
     *     source: string,
     *     zone: string|null
     * } The tax data
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'vat' => $this->vat(),
            'shipping' => $this->shipping(),
            'amount' => $this->amount(),
            'amount_input' => $this->amount_input(),
            'amount_formatted' => $this->amount_formatted(),
            'amount_parts' => $this->amount_parts(),
            'title' => $this->title(),
            'price' => $this->price(),
            'rate' => $this->rate(),
            'rate_percentage' => $this->rate_percentage(),
            'channel_liable' => $this->channel_liable(),
            'compare_at' => $this->compare_at(),
            'position' => $this->position(),
            'source' => $this->source(),
            'zone' => $this->zone()
        ];
    }
}
