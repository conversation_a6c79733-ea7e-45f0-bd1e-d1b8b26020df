<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Order;

use App\Helper\Store\Abstraction\Invoice as InvoiceAbstraction;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

/**
 * Invoice Drop Class
 * 
 * This class represents an invoice in Liquid templates, providing access to invoice properties
 * and methods for Shopify compatibility, accessibility, and SEO features.
 * 
 * Properties:
 * - number: Invoice number
 * - date: Formatted invoice date
 * - order_id: Associated order ID
 * - currency: Invoice currency code
 * - id: Invoice ID (using number as ID)
 * - name: Invoice document name/title
 * - type: Invoice type (always 'invoice')
 * - status: Invoice status (issued/pending/paid)
 * - created_at: Invoice creation date
 * - created_time: Invoice creation time
 * - order_number: Formatted order number
 * - order_date: Order date
 * - order_time: Order time
 * - order_datetime: Order date and time combined
 * - customer_email: Customer email
 * - customer_first_name: Customer first name
 * - customer_last_name: Customer last name
 * - customer_name: Customer full name
 * - billing_address: Billing address data
 * - shipping_address: Shipping address data
 * - language: Invoice language
 * - line_items: Invoice line items
 * - totals: Invoice totals
 * - payments: Invoice payments
 * - company: Company information
 * - issuer: Invoice issuer details
 * - shipping_date: Shipping date information
 * - credit_number: Credit note number
 * - credit_date: Credit note date
 * - tax_details: Tax details
 * - settings: Invoice settings
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Order
 */
class Invoice extends AbstractDrop
{
    /**
     * @var InvoiceAbstraction The underlying invoice model
     */
    protected InvoiceAbstraction $_invoice;

    /**
     * @var string|null The locale for formatting
     */
    protected ?string $_locale;

    /**
     * Invoice constructor.
     * 
     * @param InvoiceAbstraction $invoice The invoice model
     * @param string|null $locale The locale for formatting
     */
    public function __construct(InvoiceAbstraction $invoice, ?string $locale = null)
    {
        $this->_invoice = $invoice;
        $this->_locale = $locale;
    }

    /**
     * Get invoice number
     * 
     * @return string|null The invoice number
     */
    public function number(): ?string
    {
        return $this->_invoice->getNumber();
    }

    /**
     * Get formatted invoice date
     * 
     * @return string|null The formatted invoice date
     */
    public function date(): ?string
    {
        return $this->_invoice->getDate();
    }

    /**
     * Get invoice order ID
     * 
     * @return int The order ID
     */
    public function order_id(): int
    {
        return $this->_invoice->getOrderId();
    }

    /**
     * Get invoice currency
     * 
     * @return string The currency code
     */
    public function currency(): string
    {
        return $this->_invoice->getCurrency();
    }

    /**
     * Get invoice ID (using number as ID)
     * 
     * @return string|null The invoice ID
     */
    public function id(): ?string
    {
        return $this->number();
    }

    /**
     * Get invoice document name/title
     * 
     * @return string The invoice name
     */
    public function name(): string
    {
        return __('Invoice');
    }

    /**
     * Get invoice type
     * 
     * @return string The invoice type
     */
    public function type(): string
    {
        return 'invoice';
    }

    /**
     * Get invoice status
     * 
     * @return string The invoice status
     */
    public function status(): string
    {
        if ($this->number()) {
            return 'issued';
        }
        return 'pending';
    }

    /**
     * Get invoice creation date
     * 
     * @return string|null The creation date
     */
    public function created_at(): ?string
    {
        return $this->_invoice->getInvoiceDate();
    }

    /**
     * Get invoice creation time
     * 
     * @return string|null The creation time
     */
    public function created_time(): ?string
    {
        return $this->_invoice->getInvoiceTime();
    }

    /**
     * Get formatted order number
     * 
     * @return int|string The order number
     */
    public function order_number(): int|string
    {
        return $this->_invoice->getOrderNumber();
    }

    /**
     * Get order date
     * 
     * @return string The order date
     */
    public function order_date(): string
    {
        return $this->_invoice->getOrderDate();
    }

    /**
     * Get order time
     * 
     * @return string The order time
     */
    public function order_time(): string
    {
        return $this->_invoice->getOrderTime();
    }

    /**
     * Get order date and time combined
     * 
     * @return string The order date and time
     */
    public function order_datetime(): string
    {
        return $this->_invoice->getOrderDateTime();
    }

    /**
     * Get customer email
     * 
     * @return string|null The customer email
     */
    public function customer_email(): ?string
    {
        return $this->_invoice->getCustomerEmail();
    }

    /**
     * Get customer first name
     * 
     * @return string|null The customer first name
     */
    public function customer_first_name(): ?string
    {
        return $this->_invoice->getCustomerFirstName();
    }

    /**
     * Get customer last name
     * 
     * @return string|null The customer last name
     */
    public function customer_last_name(): ?string
    {
        return $this->_invoice->getCustomerLastName();
    }

    /**
     * Get customer full name
     * 
     * @return string|null The customer full name
     */
    public function customer_name(): ?string
    {
        return $this->_invoice->getCustomerFullName();
    }

    /**
     * Get billing address
     * 
     * @return array<string, mixed>|null The billing address data
     */
    public function billing_address(): ?array
    {
        $address = $this->_invoice->getBillingAddress();
        return $address ? $address->returnArray() : null;
    }

    /**
     * Get shipping address
     * 
     * @return array<string, mixed>|null The shipping address data
     */
    public function shipping_address(): ?array
    {
        $address = $this->_invoice->getShippingAddress();
        return $address ? $address->returnArray() : null;
    }

    /**
     * Get invoice language
     * 
     * @return string The invoice language
     */
    public function language(): string
    {
        return $this->_locale ?? config('app.locale');
    }

    /**
     * Get invoice line items
     * 
     * @return array<int, array<string, mixed>> The line items
     */
    public function line_items(): array
    {
        return $this->_invoice->getRows()->toArray();
    }

    /**
     * Get invoice totals
     * 
     * @return array<string, mixed> The totals
     */
    public function totals(): array
    {
        return $this->_invoice->getTotals()->toArray();
    }

    /**
     * Get invoice payments
     * 
     * @return array<int, array<string, mixed>> The payments
     */
    public function payments(): array
    {
        return $this->_invoice->getPayments()->toArray();
    }

    /**
     * Get company information
     * 
     * @return array<string, mixed> The company data
     */
    public function company(): array
    {
        return (array)$this->_invoice->getStoreDetails();
    }

    /**
     * Get invoice issuer details
     * 
     * @return array<string, mixed>|null The issuer data
     */
    public function issuer(): ?array
    {
        $issuer = $this->_invoice->getIssuer();
        return $issuer ? (array)$issuer : null;
    }

    /**
     * Check if invoice has shipping date
     * 
     * @return bool Whether the invoice has shipping date
     */
    public function has_shipping_date(): bool
    {
        return $this->_invoice->getShippingDate() !== null;
    }

    /**
     * Get shipping date information
     * 
     * @return array<string, mixed>|null The shipping date data
     */
    public function shipping_date(): ?array
    {
        $date = $this->_invoice->getShippingDate();
        return $date ? (array)$date : null;
    }

    /**
     * Get credit note number
     * 
     * @return int|null The credit note number
     */
    public function credit_number(): ?int
    {
        return $this->_invoice->getCreditNumber();
    }

    /**
     * Get formatted credit note number
     * 
     * @return string|null The formatted credit note number
     */
    public function credit_number_formatted(): ?string
    {
        return $this->_invoice->getCreditNumberFormatted();
    }

    /**
     * Get credit note date
     * 
     * @return string|null The credit note date
     */
    public function credit_date(): ?string
    {
        return $this->_invoice->getCreditDate();
    }

    /**
     * Check if this is a credit note
     * 
     * @return bool Whether this is a credit note
     */
    public function is_credit(): bool
    {
        return $this->credit_number() !== null;
    }

    /**
     * Get tax details
     * 
     * @return array<string, mixed> The tax details
     */
    public function tax_details(): array
    {
        return [
            'without_vat_reasons' => $this->_invoice->getWithoutVatReasons(),
            'tax_column_list' => $this->_invoice->getTaxColumnList(),
        ];
    }

    /**
     * Get invoice settings
     * 
     * @return array<string, mixed> The invoice settings
     */
    public function settings(): array
    {
        return [
            'show_product_image_list' => $this->_invoice->getShowProductImageList(),
            'product_quantity_list' => $this->_invoice->getProductQuantityList(),
            'show_payment_details' => $this->_invoice->getShowPaymentDetails(),
            'custom_header_text' => $this->_invoice->getCustomHeaderText(),
            'footer_note' => $this->_invoice->getFooterNote(),
        ];
    }

    /**
     * Check if invoice is issued
     * 
     * @return bool Whether the invoice is issued
     */
    public function is_issued(): bool
    {
        return $this->status() === 'issued';
    }

    /**
     * Check if invoice is pending
     * 
     * @return bool Whether the invoice is pending
     */
    public function is_pending(): bool
    {
        return $this->status() === 'pending';
    }

    /**
     * Get invoice URL
     * 
     * @return string|null The invoice URL
     */
    public function url(): ?string
    {
        if ($this->number()) {
            return route('invoice.view', ['number' => $this->number()]);
        }
        return null;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array<string, mixed> The array representation
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'number' => $this->number(),
            'date' => $this->date(),
            'order_id' => $this->order_id(),
            'currency' => $this->currency(),
            'name' => $this->name(),
            'type' => $this->type(),
            'status' => $this->status(),
            'created_at' => $this->created_at(),
            'created_time' => $this->created_time(),
            'order_number' => $this->order_number(),
            'order_date' => $this->order_date(),
            'order_time' => $this->order_time(),
            'order_datetime' => $this->order_datetime(),
            'customer_email' => $this->customer_email(),
            'customer_first_name' => $this->customer_first_name(),
            'customer_last_name' => $this->customer_last_name(),
            'customer_name' => $this->customer_name(),
            'billing_address' => $this->billing_address(),
            'shipping_address' => $this->shipping_address(),
            'language' => $this->language(),
            'line_items' => $this->line_items(),
            'totals' => $this->totals(),
            'payments' => $this->payments(),
            'company' => $this->company(),
            'issuer' => $this->issuer(),
            'has_shipping_date' => $this->has_shipping_date(),
            'shipping_date' => $this->shipping_date(),
            'credit_number' => $this->credit_number(),
            'credit_number_formatted' => $this->credit_number_formatted(),
            'credit_date' => $this->credit_date(),
            'is_credit' => $this->is_credit(),
            'tax_details' => $this->tax_details(),
            'settings' => $this->settings(),
            'is_issued' => $this->is_issued(),
            'is_pending' => $this->is_pending(),
            'url' => $this->url()
        ];
    }

    /**
     * Get string representation of the invoice
     * 
     * @return string The invoice number
     */
    public function __toString(): string
    {
        return $this->number() ?? '';
    }
}
