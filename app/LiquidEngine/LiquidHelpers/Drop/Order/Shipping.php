<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Order;

use App\Models\Order\OrderFulfillment;
use App\Models\Shipping\ShippingProvider;
use App\Models\Order\OrderShipping;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Date;

/**
 * Shipping Drop Class
 * 
 * This class represents shipping information in Liquid templates, providing access to shipping properties
 * and methods for Shopify compatibility, accessibility, and SEO features.
 * 
 * Properties:
 * - name: Shipping provider name
 * - service_name: Shipping service name
 * - tracking_url: Tracking URL for the shipment
 * - tracking_number: Tracking number for the shipment
 * - date_delivery: Expected delivery date
 * - date_expedition: Expedition/shipping date
 * - status: Shipping status
 * - title: Shipping method title (Shopify compatibility)
 * - price: Shipping price in cents
 * - code: Shipping method code
 * - source: Shipping source/provider
 * - discounted_price: Discounted shipping price
 * - carrier_identifier: Carrier service identifier
 * - requested_fulfillment_service_id: Fulfillment service ID
 * - price_set: Price in shop and presentment currencies
 * - discounted_price_set: Discounted price in shop and presentment currencies
 * - tax_lines: Tax information for shipping
 * - discount_allocations: Discount information for shipping
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Order
 */
class Shipping extends AbstractDrop
{
    /**
     * @var OrderShipping The shipping model
     */
    protected OrderShipping $_shipping;

    /**
     * @var string The shipping status
     */
    protected string $_status;

    /**
     * @var string The locale for translations
     */
    protected string $_locale;

    /**
     * @var string|null The status color
     */
    protected ?string $_color;

    /**
     * @var OrderFulfillment|null The fulfillment information
     */
    protected ?OrderFulfillment $_fulfillment;

    /**
     * Shipping constructor.
     * 
     * @param OrderShipping $shipping The shipping model
     * @param string $status The shipping status
     * @param OrderFulfillment|null $fulfillment The fulfillment information
     * @param string|null $color The status color
     * @param string|null $locale The locale for translations
     */
    public function __construct(
        OrderShipping $shipping,
        string $status,
        ?OrderFulfillment $fulfillment = null,
        ?string $color = null,
        ?string $locale = null
    ) {
        $this->_shipping = $shipping;
        $this->_status = $status;
        $this->_locale = $locale ?: setting('language');
        $this->_color = $color;
        $this->_fulfillment = $fulfillment;
    }

    /**
     * Get the shipping provider name
     * 
     * @return string The provider name
     */
    public function name(): string
    {
        return $this->_shipping->provider_name;
    }

    /**
     * Get the shipping service name
     * 
     * @return string|null The service name
     */
    public function service_name(): ?string
    {
        return $this->_fulfillment?->shipping_provider;
    }

    /**
     * Get the tracking URL for the shipment
     * 
     * @return string|null The tracking URL
     */
    public function tracking_url(): ?string
    {
        if (!empty($this->_fulfillment?->shipping_tracking_url)) {
            return $this->_fulfillment->shipping_tracking_url;
        } elseif ($number = $this->tracking_number()) {
            return ShippingProvider::TRACK17 . $number;
        }

        return null;
    }

    /**
     * Get the tracking number for the shipment
     * 
     * @return string|null The tracking number
     */
    public function tracking_number(): ?string
    {
        return $this->_fulfillment?->shipping_tracking_number;
    }

    /**
     * Get the expected delivery date
     * 
     * @return Date|null The delivery date
     */
    public function date_delivery(): ?Date
    {
        return !empty($this->_fulfillment?->shipping_date_delivery) 
            ? new Date($this->_fulfillment->shipping_date_delivery) 
            : null;
    }

    /**
     * Get the expedition/shipping date
     * 
     * @return Date|null The expedition date
     */
    public function date_expedition(): ?Date
    {
        return !empty($this->_fulfillment?->shipping_date_expedition) 
            ? new Date($this->_fulfillment->shipping_date_expedition) 
            : null;
    }

    /**
     * Get the shipping status
     * 
     * @return Status The shipping status
     */
    public function status(): Status
    {
        return new Status(
            $this->_status,
            $this->_color,
            'shipping',
            $this->_locale
        );
    }

    // ===========================================
    // Shopify shipping_line Compatibility Methods
    // ===========================================

    /**
     * Get the shipping method title (Shopify compatibility)
     * Maps to CloudCart's provider name
     * 
     * @return string|null The shipping method title
     */
    public function title(): ?string
    {
        return $this->_shipping->provider_name;
    }

    /**
     * Get the shipping price in cents (Shopify compatibility)
     * Maps to CloudCart's shipping price
     * 
     * @return string|null The shipping price in cents
     */
    public function price(): ?string
    {
        return $this->_shipping->price 
            ? number_format((float)$this->_shipping->price * 100, 0, '', '') 
            : null;
    }

    /**
     * Get the shipping method code (Shopify compatibility)
     * Maps to CloudCart's provider code or ID
     * 
     * @return string|null The shipping method code
     */
    public function code(): ?string
    {
        return $this->_shipping->provider_code ?? (string)$this->_shipping->id;
    }

    /**
     * Get the shipping source/provider (Shopify compatibility)
     * Returns the shipping provider identifier
     * 
     * @return string|null The shipping source
     */
    public function source(): ?string
    {
        return $this->_shipping->provider_name ?? 'manual';
    }

    /**
     * Get the discounted shipping price (Shopify compatibility)
     * For CloudCart, typically same as price unless discounts applied
     * 
     * @return string|null The discounted price in cents
     */
    public function discounted_price(): ?string
    {
        $discountedPrice = $this->_shipping->discounted_price ?? $this->_shipping->price;
        return $discountedPrice 
            ? number_format((float)$discountedPrice * 100, 0, '', '') 
            : null;
    }

    /**
     * Get the carrier service identifier (Shopify compatibility)
     * Maps to CloudCart's shipping provider
     * 
     * @return string|null The carrier identifier
     */
    public function carrier_identifier(): ?string
    {
        return $this->_shipping->carrier_identifier ?? $this->_shipping->provider_name;
    }

    /**
     * Get the fulfillment service ID (Shopify compatibility)
     * Returns fulfillment service ID if third-party, null otherwise
     * 
     * @return string|null The fulfillment service ID
     */
    public function requested_fulfillment_service_id(): ?string
    {
        return $this->_fulfillment && $this->_fulfillment->fulfillment_service !== 'manual'
            ? $this->_fulfillment->fulfillment_service
            : null;
    }

    /**
     * Get the price in shop and presentment currencies (Shopify compatibility)
     * Returns structured price information
     * 
     * @return array{
     *     shop_money: array{amount: string, currency_code: string},
     *     presentment_money: array{amount: string, currency_code: string}
     * }|null The price set
     */
    public function price_set(): ?array
    {
        if (!$this->_shipping->price) {
            return null;
        }

        $amount = number_format((float)$this->_shipping->price, 2, '.', '');
        $currency = $this->_shipping->currency ?? 'USD';

        return [
            'shop_money' => [
                'amount' => $amount,
                'currency_code' => $currency
            ],
            'presentment_money' => [
                'amount' => $amount,
                'currency_code' => $currency
            ]
        ];
    }

    /**
     * Get the discounted price in shop and presentment currencies (Shopify compatibility)
     * Returns structured discounted price information
     * 
     * @return array{
     *     shop_money: array{amount: string, currency_code: string},
     *     presentment_money: array{amount: string, currency_code: string}
     * }|null The discounted price set
     */
    public function discounted_price_set(): ?array
    {
        $discountedPrice = $this->_shipping->discounted_price ?? $this->_shipping->price;
        if (!$discountedPrice) {
            return null;
        }

        $amount = number_format((float)$discountedPrice, 2, '.', '');
        $currency = $this->_shipping->currency ?? 'USD';

        return [
            'shop_money' => [
                'amount' => $amount,
                'currency_code' => $currency
            ],
            'presentment_money' => [
                'amount' => $amount,
                'currency_code' => $currency
            ]
        ];
    }

    /**
     * Get the tax information for shipping (Shopify compatibility)
     * 
     * @return array<int, array{
     *     price: string,
     *     rate: float,
     *     title: string,
     *     price_set: array{
     *         shop_money: array{amount: string, currency_code: string},
     *         presentment_money: array{amount: string, currency_code: string}
     *     }
     * }> The tax lines
     */
    public function tax_lines(): array
    {
        if (empty($this->_shipping->tax_lines)) {
            return [];
        }

        $taxLines = [];
        foreach ($this->_shipping->tax_lines as $taxLine) {
            $amount = number_format((float)$taxLine['price'], 2, '.', '');
            $currency = $this->_shipping->currency ?? 'USD';

            $taxLines[] = [
                'price' => number_format((float)$taxLine['price'] * 100, 0, '', ''),
                'rate' => (float)$taxLine['rate'],
                'title' => $taxLine['title'],
                'price_set' => [
                    'shop_money' => [
                        'amount' => $amount,
                        'currency_code' => $currency
                    ],
                    'presentment_money' => [
                        'amount' => $amount,
                        'currency_code' => $currency
                    ]
                ]
            ];
        }

        return $taxLines;
    }

    /**
     * Get the discount information for shipping (Shopify compatibility)
     * 
     * @return array<int, array{
     *     amount: string,
     *     discount_application_index: int,
     *     amount_set: array{
     *         shop_money: array{amount: string, currency_code: string},
     *         presentment_money: array{amount: string, currency_code: string}
     *     }
     * }> The discount allocations
     */
    public function discount_allocations(): array
    {
        if (empty($this->_shipping->discount_allocations)) {
            return [];
        }

        $discountAllocations = [];
        foreach ($this->_shipping->discount_allocations as $index => $allocation) {
            $amount = number_format((float)$allocation['amount'], 2, '.', '');
            $currency = $this->_shipping->currency ?? 'USD';

            $discountAllocations[] = [
                'amount' => number_format((float)$allocation['amount'] * 100, 0, '', ''),
                'discount_application_index' => $index,
                'amount_set' => [
                    'shop_money' => [
                        'amount' => $amount,
                        'currency_code' => $currency
                    ],
                    'presentment_money' => [
                        'amount' => $amount,
                        'currency_code' => $currency
                    ]
                ]
            ];
        }

        return $discountAllocations;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     name: string,
     *     service_name: string|null,
     *     tracking_url: string|null,
     *     tracking_number: string|null,
     *     date_delivery: Date|null,
     *     date_expedition: Date|null,
     *     status: Status,
     *     title: string|null,
     *     price: string|null,
     *     code: string|null,
     *     source: string|null,
     *     discounted_price: string|null,
     *     carrier_identifier: string|null,
     *     requested_fulfillment_service_id: string|null,
     *     price_set: array|null,
     *     discounted_price_set: array|null,
     *     tax_lines: array,
     *     discount_allocations: array
     * } The shipping data
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'service_name' => $this->service_name(),
            'tracking_url' => $this->tracking_url(),
            'tracking_number' => $this->tracking_number(),
            'date_delivery' => $this->date_delivery(),
            'date_expedition' => $this->date_expedition(),
            'status' => $this->status(),
            'title' => $this->title(),
            'price' => $this->price(),
            'code' => $this->code(),
            'source' => $this->source(),
            'discounted_price' => $this->discounted_price(),
            'carrier_identifier' => $this->carrier_identifier(),
            'requested_fulfillment_service_id' => $this->requested_fulfillment_service_id(),
            'price_set' => $this->price_set(),
            'discounted_price_set' => $this->discounted_price_set(),
            'tax_lines' => $this->tax_lines(),
            'discount_allocations' => $this->discount_allocations()
        ];
    }
}
