<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Order\Product;

use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

/**
 * Parameter Drop Class
 * 
 * This class represents product parameter information in Liquid templates, providing access to parameter properties
 * and methods for Shopify compatibility, accessibility, and SEO features.
 * 
 * Properties:
 * - name: Parameter name
 * - value: Parameter value
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Order\Product
 */
class Parameter extends AbstractDrop
{
    /**
     * @var array{
     *     name: string,
     *     value: string
     * } The parameter data
     */
    protected array $_parameter;

    /**
     * Parameter constructor.
     * 
     * @param array{
     *     name: string,
     *     value: string
     * } $parameter The parameter data
     */
    public function __construct(array $parameter)
    {
        $this->_parameter = $parameter;
    }

    /**
     * Get the parameter name
     * 
     * @return string The parameter name
     */
    public function name(): string
    {
        return $this->_parameter['name'];
    }

    /**
     * Get the parameter value
     * 
     * @return string The parameter value
     */
    public function value(): string
    {
        return $this->_parameter['value'];
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     name: string,
     *     value: string
     * } The parameter data
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'value' => $this->value()
        ];
    }
}
