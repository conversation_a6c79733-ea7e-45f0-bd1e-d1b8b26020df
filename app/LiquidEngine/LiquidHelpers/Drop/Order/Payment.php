<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Order;

use App\Models\Order\OrderPayment;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Date;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;

/**
 * Payment Drop Class
 * 
 * This class represents a payment in Liquid templates, providing access to payment properties
 * and methods for Shopify compatibility, accessibility, and SEO features.
 * 
 * Properties:
 * - id: Payment ID
 * - order_id: Associated order ID
 * - hash: Payment hash/identifier
 * - code: Payment provider code
 * - name: Payment provider name
 * - description: Payment description
 * - amount: Payment amount
 * - amount_input: Raw payment amount
 * - amount_formatted: Formatted payment amount
 * - amount_parts: Payment amount parts
 * - date: Payment date
 * - status: Payment status
 * - type: Payment type
 * - is_leasing: Whether payment is leasing
 * - is_offline: Whether payment is offline
 * - gateway: Payment gateway
 * - gateway_display_name: Gateway display name
 * - kind: Transaction kind
 * - transaction_status: Transaction status
 * - created_at: Creation timestamp
 * - currency: Currency code
 * - authorization: Authorization code
 * - receipt: Receipt number
 * - test: Test mode flag
 * - error_code: Error code
 * - message: Status message
 * - parent_id: Parent transaction ID
 * - gateway_amount: Gateway amount
 * - gateway_currency: Gateway currency
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Order
 */
class Payment extends AbstractDrop
{
    /**
     * @var OrderPayment The payment model
     */
    protected OrderPayment $_payment;

    /**
     * @var string The locale for translations
     */
    protected string $_locale;

    /**
     * @var string The currency code
     */
    protected string $_currency;

    /**
     * @const string Leasing provider type
     */
    const LEASING_PROVIDER_TYPE = 'credit';

    /**
     * @const string Offline provider type
     */
    const OFFLINE_PROVIDER_TYPE = 'offline';

    /**
     * Payment constructor.
     * 
     * @param OrderPayment $payment The payment model
     * @param string|null $locale The locale for translations
     * @param string|null $currency The currency code
     */
    public function __construct(OrderPayment $payment, ?string $locale = null, ?string $currency = null)
    {
        $this->_payment = $payment;
        $this->_locale = $locale ?: setting('language');
        $this->_currency = $currency ?: site('currency');
    }

    /**
     * Get the payment ID
     * 
     * @return int The payment ID
     */
    public function id(): int
    {
        return $this->_payment->id;
    }

    /**
     * Get the associated order ID
     * 
     * @return int The order ID
     */
    public function order_id(): int
    {
        return $this->_payment->order_id;
    }

    /**
     * Get the payment hash/identifier
     * 
     * @return string The payment hash
     */
    public function hash(): string
    {
        return $this->_payment->hash;
    }

    /**
     * Get the payment provider code
     * 
     * @return string The provider code
     */
    public function code(): string
    {
        return $this->_payment->provider;
    }

    /**
     * Get the payment provider name
     * 
     * @return string The provider name
     */
    public function name(): string
    {
        return $this->_payment->provider_name;
    }

    /**
     * Get the payment description
     * 
     * @return string|null The payment description
     */
    public function description(): ?string
    {
        if (!empty($this->_payment->payment_provider->configuration->configuration['description']) && 
            is_string($this->_payment->payment_provider->configuration->configuration['description'])) {
            return $this->_payment->payment_provider->configuration->configuration['description'];
        }

        return null;
    }

    /**
     * Get the payment amount
     * 
     * @return int The payment amount
     */
    public function amount(): int
    {
        return $this->_payment->amount;
    }

    /**
     * Get the raw payment amount
     * 
     * @return float The raw amount
     */
    public function amount_input(): float
    {
        return $this->_payment->amount_input;
    }

    /**
     * Get the formatted payment amount
     * 
     * @return string The formatted amount
     */
    public function amount_formatted(): string
    {
        return $this->_payment->amount_formatted;
    }

    /**
     * Get the payment date
     * 
     * @return Date The payment date
     */
    public function date(): Date
    {
        return new Date($this->_payment->date_added);
    }

    /**
     * Get the payment amount parts
     * 
     * @return array<string, mixed> The amount parts
     */
    public function amount_parts(): array
    {
        return PriceParts::format($this->amount_input(), $this->_currency, $this->_locale);
    }

    /**
     * Get the payment status
     * 
     * @return Status The payment status
     */
    public function status(): Status
    {
        return new Status(
            $this->_payment->status,
            $this->_payment->status_color,
            'payment',
            $this->_locale
        );
    }

    /**
     * Get the payment type
     * 
     * @return array<string> The payment types
     */
    public function type(): array
    {
        return $this->_payment->payment_provider->type ?? [];
    }

    /**
     * Check if payment is leasing
     * 
     * @return bool Whether payment is leasing
     */
    public function is_leasing(): bool
    {
        return in_array(static::LEASING_PROVIDER_TYPE, $this->type());
    }

    /**
     * Check if payment is offline
     * 
     * @return bool Whether payment is offline
     */
    public function is_offline(): bool
    {
        return in_array(static::OFFLINE_PROVIDER_TYPE, $this->type());
    }

    /**
     * Get the payment gateway
     * 
     * @return string The gateway code
     */
    public function gateway(): string
    {
        return $this->_payment->provider ?? 'unknown';
    }

    /**
     * Get the gateway display name
     * 
     * @return string The display name
     */
    public function gateway_display_name(): string
    {
        return $this->_payment->provider_name ?? $this->gateway();
    }

    /**
     * Get the transaction kind
     * 
     * @return string The transaction kind
     */
    public function kind(): string
    {
        switch ($this->_payment->status ?? 0) {
            case 1: // Paid
                return 'capture';
            case 2: // Pending
                return 'authorization';
            case 3: // Failed
                return 'void';
            case 4: // Refunded
                return 'refund';
            default:
                return 'authorization';
        }
    }

    /**
     * Get the transaction status
     * 
     * @return string The transaction status
     */
    public function transaction_status(): string
    {
        switch ($this->_payment->status ?? 0) {
            case 1: // Paid
                return 'success';
            case 2: // Pending
                return 'pending';
            case 3: // Failed
                return 'failure';
            case 4: // Refunded
                return 'success';
            default:
                return 'pending';
        }
    }

    /**
     * Get the creation timestamp
     * 
     * @return Date The creation date
     */
    public function created_at(): Date
    {
        return $this->date();
    }

    /**
     * Get the currency code
     * 
     * @return string The currency code
     */
    public function currency(): string
    {
        return $this->_currency;
    }

    /**
     * Get the authorization code
     * 
     * @return string|null The authorization code
     */
    public function authorization(): ?string
    {
        return $this->_payment->hash ?: null;
    }

    /**
     * Get the receipt number
     * 
     * @return string The receipt number
     */
    public function receipt(): string
    {
        return $this->_payment->hash ?: 'CC-' . $this->_payment->id;
    }

    /**
     * Check if in test mode
     * 
     * @return bool Whether in test mode
     */
    public function test(): bool
    {
        return false; // CloudCart production transactions
    }

    /**
     * Get the error code
     * 
     * @return string|null The error code
     */
    public function error_code(): ?string
    {
        if ($this->transaction_status() === 'failure') {
            return 'generic_error';
        }
        return null;
    }

    /**
     * Get the status message
     * 
     * @return string The status message
     */
    public function message(): string
    {
        switch ($this->_payment->status ?? 0) {
            case 1:
                return 'Transaction approved';
            case 2:
                return 'Transaction pending';
            case 3:
                return 'Transaction declined';
            case 4:
                return 'Transaction refunded';
            default:
                return 'Transaction processing';
        }
    }

    /**
     * Get the parent transaction ID
     * 
     * @return int|null The parent ID
     */
    public function parent_id(): ?int
    {
        return null; // CloudCart refunds are separate records
    }

    /**
     * Get the gateway amount
     * 
     * @return int The gateway amount
     */
    public function gateway_amount(): int
    {
        return $this->amount();
    }

    /**
     * Get the gateway currency
     * 
     * @return string The gateway currency
     */
    public function gateway_currency(): string
    {
        return $this->currency();
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array<string, mixed> The array representation
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'order_id' => $this->order_id(),
            'hash' => $this->hash(),
            'code' => $this->code(),
            'name' => $this->name(),
            'description' => $this->description(),
            'amount' => $this->amount(),
            'amount_input' => $this->amount_input(),
            'amount_formatted' => $this->amount_formatted(),
            'amount_parts' => $this->amount_parts(),
            'date' => $this->date(),
            'status' => $this->status(),
            'type' => $this->type(),
            'is_leasing' => $this->is_leasing(),
            'is_offline' => $this->is_offline(),
            'gateway' => $this->gateway(),
            'gateway_display_name' => $this->gateway_display_name(),
            'kind' => $this->kind(),
            'transaction_status' => $this->transaction_status(),
            'created_at' => $this->created_at(),
            'currency' => $this->currency(),
            'authorization' => $this->authorization(),
            'receipt' => $this->receipt(),
            'test' => $this->test(),
            'error_code' => $this->error_code(),
            'message' => $this->message(),
            'parent_id' => $this->parent_id(),
            'gateway_amount' => $this->gateway_amount(),
            'gateway_currency' => $this->gateway_currency()
        ];
    }
}
