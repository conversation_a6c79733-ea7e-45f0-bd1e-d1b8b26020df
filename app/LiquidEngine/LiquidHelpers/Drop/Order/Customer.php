<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Order;

use App\LiquidEngine\LiquidHelpers\Drop\Customer as BaseCustomer;
use App\Models\Customer\Customer as CustomerModel;
use App\Models\Order\Order as OrderModel;

/**
 * Order Customer Drop Class
 *
 * This class extends the base Customer class to provide order-specific customer data
 * and methods for Shopify compatibility, accessibility, and SEO features.
 *
 * Properties:
 * - first_name: Customer's first name from order context
 * - last_name: Customer's last name from order context
 * - name: Customer's full name
 * - display_name: Customer's display name
 * - email: Customer's email from order context
 * - phone: Customer's phone number
 * - mobile: Customer's mobile number
 * - order: Associated order model
 * - order_customer_id: Order-specific customer ID
 * - billing_address: Customer's billing address
 * - shipping_address: Customer's shipping address
 * - default_address: Customer's default address
 * - orders_count: Total number of orders
 * - total_spent: Total amount spent by customer
 * - accepts_marketing: Whether customer accepts marketing
 * - tags: Customer tags
 * - note: Customer note
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop\Order
 */
class Customer extends BaseCustomer
{
    /**
     * @var OrderModel Order model for context
     */
    protected OrderModel $_order;

    /**
     * Customer constructor.
     *
     * @param CustomerModel $customer The customer model
     * @param OrderModel $order Order context for customer data
     * @param string|null $locale The locale for formatting
     */
    public function __construct(CustomerModel $customer, OrderModel $order, ?string $locale = null)
    {
        parent::__construct($customer, $locale);
        $this->_order = $order;
    }

    /**
     * Get customer's first name from order context
     * Returns order-specific customer name if available, fallback to customer model
     *
     * @return string|null Customer first name
     */
    public function first_name(): ?string
    {
        return $this->_order->customer_first_name ?? parent::first_name();
    }

    /**
     * Get customer's last name from order context
     * Returns order-specific customer name if available, fallback to customer model
     *
     * @return string|null Customer last name
     */
    public function last_name(): ?string
    {
        return $this->_order->customer_last_name ?? parent::last_name();
    }

    /**
     * Get customer's full name from order context
     * Combines first and last name with proper spacing
     *
     * @return string Customer full name
     */
    public function name(): string
    {
        $firstName = $this->first_name();
        $lastName = $this->last_name();

        if ($firstName && $lastName) {
            return trim($firstName . ' ' . $lastName);
        }

        return $firstName ?: $lastName ?: '';
    }

    /**
     * Get customer's display name (same as name for order context)
     *
     * @return string Customer display name
     */
    public function display_name(): string
    {
        return $this->name();
    }

    /**
     * Get customer's email from order context
     * Returns order-specific customer email if available, fallback to customer model
     *
     * @return string|null Customer email
     */
    public function email(): ?string
    {
        return $this->_order->customer_email ?? parent::email();
    }

    /**
     * Get customer's phone from order context
     * Returns order-specific customer phone if available, fallback to customer model
     *
     * @return string Customer phone
     */
    public function phone(): string
    {
        return $this->_order->customer_phone ?? parent::phone();
    }

    /**
     * Get customer's mobile phone from order context
     *
     * @return string Customer mobile phone
     */
    public function mobile(): string
    {
        return $this->_order->customer_mobile ?? '';
    }

    /**
     * Get the order this customer is associated with
     *
     * @return OrderModel The associated order
     */
    public function order(): OrderModel
    {
        return $this->_order;
    }

    /**
     * Get order-specific customer ID
     *
     * @return int|null Order customer ID
     */
    public function order_customer_id(): ?int
    {
        return $this->_order->customer_id;
    }

    /**
     * Check if customer is the same as order customer
     *
     * @return bool True if customer matches order customer
     */
    public function is_order_customer(): bool
    {
        return $this->_customer->id === $this->_order->customer_id;
    }

    /**
     * Get customer's billing address from order context
     * Returns the order's billing address if available
     *
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Customer\Address|null Billing address data
     */
    public function billing_address(): ?\App\LiquidEngine\LiquidHelpers\Drop\Customer\Address
    {
        if (!$this->_order->billing_address) {
            return parent::billing_address();
        }

        return new \App\LiquidEngine\LiquidHelpers\Drop\Customer\Address((object)[
            'first_name' => $this->_order->billing_first_name,
            'last_name' => $this->_order->billing_last_name,
            'address' => $this->_order->billing_address,
            'address_2' => $this->_order->billing_address_2,
            'city' => $this->_order->billing_city,
            'state' => $this->_order->billing_state,
            'country' => $this->_order->billing_country,
            'postal_code' => $this->_order->billing_postal_code,
            'phone' => $this->_order->billing_phone,
            'company' => $this->_order->billing_company,
        ]);
    }

    /**
     * Get customer's shipping address from order context
     * Returns the order's shipping address if available
     *
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Customer\Address|null Shipping address data
     */
    public function shipping_address(): ?\App\LiquidEngine\LiquidHelpers\Drop\Customer\Address
    {
        if (!$this->_order->shipping_address) {
            return parent::shipping_address();
        }

        return new \App\LiquidEngine\LiquidHelpers\Drop\Customer\Address((object)[
            'first_name' => $this->_order->shipping_first_name,
            'last_name' => $this->_order->shipping_last_name,
            'address' => $this->_order->shipping_address,
            'address_2' => $this->_order->shipping_address_2,
            'city' => $this->_order->shipping_city,
            'state' => $this->_order->shipping_state,
            'country' => $this->_order->shipping_country,
            'postal_code' => $this->_order->shipping_postal_code,
            'phone' => $this->_order->shipping_phone,
            'company' => $this->_order->shipping_company,
        ]);
    }

    /**
     * Get customer's default address (billing address in order context)
     *
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Customer\Address|null Default address data
     */
    public function default_address(): ?\App\LiquidEngine\LiquidHelpers\Drop\Customer\Address
    {
        return $this->billing_address();
    }

    /**
     * Check if customer has a valid order
     *
     * @return bool True if customer has valid order
     */
    public function has_order(): bool
    {
        return !empty($this->_order->id);
    }

    /**
     * Get total orders count for this customer
     *
     * @return int Total orders count
     */
    public function orders_count(): int
    {
        if ($this->_customer->id) {
            return OrderModel::where('customer_id', $this->_customer->id)->count();
        }

        return 0;
    }

    /**
     * Get total amount spent by this customer
     *
     * @return string Total amount spent formatted
     */
    public function total_spent(): string
    {
        if ($this->_customer->id) {
            $total = OrderModel::where('customer_id', $this->_customer->id)
                ->where('status', '!=', 'cancelled')
                ->sum('total');
            return number_format($total, 2);
        }

        return '0.00';
    }

    /**
     * Check if this is the customer's first order
     *
     * @return bool True if this is the first order
     */
    public function first_order(): bool
    {
        if (!$this->_customer->id) {
            return true;
        }

        $orderCount = OrderModel::where('customer_id', $this->_customer->id)
            ->where('id', '!=', $this->_order->id)
            ->count();

        return $orderCount === 0;
    }

    /**
     * Check if customer accepts marketing
     *
     * @return bool True if customer accepts marketing
     */
    public function accepts_marketing(): bool
    {
        return (bool)($this->_order->customer_accepts_marketing ?? false);
    }

    /**
     * Get customer tags
     *
     * @return string Customer tags
     */
    public function tags(): string
    {
        if ($this->_customer->id) {
            return $this->_customer->tags->implode(',') ?? '';
        }

        return '';
    }

    /**
     * Get customer note
     *
     * @return string Customer note
     */
    public function note(): string
    {
        return $this->_order->customer_note ?? '';
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array<string, mixed> The array representation
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'first_name' => $this->first_name(),
            'last_name' => $this->last_name(),
            'name' => $this->name(),
            'display_name' => $this->display_name(),
            'email' => $this->email(),
            'phone' => $this->phone(),
            'mobile' => $this->mobile(),
            'order' => $this->order()->toArray(),
            'order_customer_id' => $this->order_customer_id(),
            'is_order_customer' => $this->is_order_customer(),
            'billing_address' => $this->billing_address()?->toArray(),
            'shipping_address' => $this->shipping_address()?->toArray(),
            'default_address' => $this->default_address()?->toArray(),
            'has_order' => $this->has_order(),
            'orders_count' => $this->orders_count(),
            'total_spent' => $this->total_spent(),
            'first_order' => $this->first_order(),
            'accepts_marketing' => $this->accepts_marketing(),
            'tags' => $this->tags(),
            'note' => $this->note()
        ];
    }

    /**
     * Get string representation of the customer
     *
     * @return string Customer name
     */
    public function __toString(): string
    {
        return $this->name();
    }
}
