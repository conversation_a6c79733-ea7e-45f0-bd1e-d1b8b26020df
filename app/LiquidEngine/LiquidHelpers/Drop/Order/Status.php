<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Order;

use App\Common\Status as CommonStatus;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\Models\Order\OrderStatus;

/**
 * Status Drop Class
 * 
 * This class represents an order status in Liquid templates, providing access to status properties
 * and methods for Shopify compatibility, accessibility, and SEO features.
 * 
 * Properties:
 * - key: Status key/value for identification
 * - name: Localized status name
 * - color: Status color for UI display
 * - type: Status type (order, payment, shipping)
 * - title: Display title (alias for name)
 * - value: Raw status value
 * - label: Human-readable label
 * - status_key: Alternative accessor for status key
 * - display_name: Display-friendly name
 * - is_positive: Whether status is positive/successful
 * - is_negative: Whether status is negative/problematic
 * - is_pending: Whether status is pending/in-progress
 * - is_final: Whether status represents a final state
 * - css_class: CSS class for styling
 * - icon: Icon identifier
 * - description: Status description
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Order
 */
class Status extends AbstractDrop
{
    /**
     * @var string The status key/value
     */
    protected string $_status;

    /**
     * @var string|null The locale for translations
     */
    protected ?string $_locale;

    /**
     * @var string The status type (order, payment, shipping)
     */
    protected string $_type;

    /**
     * @var string|null The status color
     */
    protected ?string $_color;

    /**
     * Status constructor.
     * 
     * @param string $status The status key/value
     * @param string|null $color The status color
     * @param string $type The status type
     * @param string|null $locale The locale for translations
     */
    public function __construct(string $status, ?string $color = null, string $type = 'order', ?string $locale = null)
    {
        $this->_status = $status;
        $this->_type = $type;
        $this->_color = $color;
        $this->_locale = $locale ?: setting('language');
    }

    /**
     * Get the status key/value used for identification
     * 
     * @return string The status key
     */
    public function key(): string
    {
        return $this->_status;
    }

    /**
     * Get the localized status name
     * 
     * @return string The status name
     */
    public function name(): string
    {
        if ($this->_type === 'order') {
            return CommonStatus::order($this->_status, $this->_locale);
        } elseif ($this->_type === 'shipping') {
            return CommonStatus::shipping($this->_status, $this->_locale);
        } elseif ($this->_type === 'payment') {
            return CommonStatus::payment($this->_status, $this->_locale);
        }

        return $this->_status;
    }

    /**
     * Get the status color for UI display
     * 
     * @return string|null The status color
     */
    public function color(): ?string
    {
        return $this->_color;
    }

    /**
     * Get the status title (alias for name)
     * 
     * @return string The status title
     */
    public function title(): string
    {
        return $this->name();
    }

    /**
     * Get the raw status value
     * 
     * @return string The status value
     */
    public function value(): string
    {
        return $this->_status;
    }

    /**
     * Get the human-readable label
     * 
     * @return string The status label
     */
    public function label(): string
    {
        return $this->name();
    }

    /**
     * Get the status type
     * 
     * @return string The status type
     */
    public function type(): string
    {
        return $this->_type;
    }

    /**
     * Get the status key (alternative accessor)
     * 
     * @return string The status key
     */
    public function status_key(): string
    {
        return $this->key();
    }

    /**
     * Get the display-friendly name
     * 
     * @return string The display name
     */
    public function display_name(): string
    {
        return $this->name();
    }

    /**
     * Check if this is a positive/successful status
     * 
     * @return bool Whether the status is positive
     */
    public function is_positive(): bool
    {
        $positiveStatuses = [
            // Order statuses
            'completed', 'paid', 'authorized', 'fulfilled',
            // Payment statuses  
            'paid', 'authorized', 'completed',
            // Shipping statuses
            'fulfilled', 'delivered', 'shipped'
        ];

        return in_array($this->_status, $positiveStatuses);
    }

    /**
     * Check if this is a negative/problematic status
     * 
     * @return bool Whether the status is negative
     */
    public function is_negative(): bool
    {
        $negativeStatuses = array_merge(
            OrderStatus::NEGATIVE_STATUS,
            ['failed', 'cancelled', 'refunded', 'voided', 'declined', 'expired']
        );

        return in_array($this->_status, $negativeStatuses);
    }

    /**
     * Check if this is a pending/in-progress status
     * 
     * @return bool Whether the status is pending
     */
    public function is_pending(): bool
    {
        $pendingStatuses = [
            'pending', 'processing', 'on_hold', 'scheduled', 
            'partially_paid', 'partially_fulfilled', 'partial'
        ];

        return in_array($this->_status, $pendingStatuses);
    }

    /**
     * Check if this status represents a final state
     * 
     * @return bool Whether the status is final
     */
    public function is_final(): bool
    {
        $finalStatuses = [
            'completed', 'cancelled', 'refunded', 'fulfilled', 
            'failed', 'voided', 'restocked', 'delivered'
        ];

        return in_array($this->_status, $finalStatuses);
    }

    /**
     * Get the CSS class for styling
     * 
     * @return string The CSS class
     */
    public function css_class(): string
    {
        if ($this->is_positive()) {
            return 'status-positive';
        } elseif ($this->is_negative()) {
            return 'status-negative';
        } elseif ($this->is_pending()) {
            return 'status-pending';
        }

        return 'status-neutral';
    }

    /**
     * Get the icon identifier
     * 
     * @return string The icon identifier
     */
    public function icon(): string
    {
        if ($this->is_positive()) {
            return 'check-circle';
        } elseif ($this->is_negative()) {
            return 'x-circle';
        } elseif ($this->is_pending()) {
            return 'clock';
        }

        return 'circle';
    }

    /**
     * Get the status description
     * 
     * @return string The status description
     */
    public function description(): string
    {
        $descriptions = [
            'completed' => 'Order has been completed successfully',
            'pending' => 'Order is pending processing',
            'processing' => 'Order is being processed',
            'cancelled' => 'Order has been cancelled',
            'refunded' => 'Order has been refunded',
            'failed' => 'Order processing has failed',
            'on_hold' => 'Order is on hold',
            'scheduled' => 'Order is scheduled for processing',
            'partially_paid' => 'Order is partially paid',
            'partially_fulfilled' => 'Order is partially fulfilled',
            'fulfilled' => 'Order has been fulfilled',
            'delivered' => 'Order has been delivered',
            'shipped' => 'Order has been shipped',
            'paid' => 'Order has been paid',
            'authorized' => 'Payment has been authorized',
            'voided' => 'Order has been voided',
            'restocked' => 'Items have been restocked',
            'expired' => 'Order has expired',
            'declined' => 'Payment has been declined'
        ];

        return $descriptions[$this->_status] ?? '';
    }

    /**
     * Check if status can transition to a new status
     * 
     * @param string $newStatus The new status to check
     * @return bool Whether the transition is allowed
     */
    public function can_transition_to(string $newStatus): bool
    {
        $allowedTransitions = [
            'pending' => ['processing', 'cancelled', 'failed'],
            'processing' => ['completed', 'cancelled', 'failed', 'on_hold'],
            'on_hold' => ['processing', 'cancelled'],
            'completed' => ['refunded'],
            'cancelled' => [],
            'refunded' => [],
            'failed' => ['processing', 'cancelled']
        ];

        return in_array($newStatus, $allowedTransitions[$this->_status] ?? []);
    }

    /**
     * Get the status formatted with icon
     * 
     * @return string The formatted status
     */
    public function formatted_with_icon(): string
    {
        return sprintf(
            '<span class="status %s"><i class="icon-%s"></i> %s</span>',
            $this->css_class(),
            $this->icon(),
            $this->name()
        );
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array<string, mixed> The array representation
     */
    public function toArray(): array
    {
        return [
            'key' => $this->key(),
            'name' => $this->name(),
            'color' => $this->color(),
            'type' => $this->type(),
            'title' => $this->title(),
            'value' => $this->value(),
            'label' => $this->label(),
            'status_key' => $this->status_key(),
            'display_name' => $this->display_name(),
            'is_positive' => $this->is_positive(),
            'is_negative' => $this->is_negative(),
            'is_pending' => $this->is_pending(),
            'is_final' => $this->is_final(),
            'css_class' => $this->css_class(),
            'icon' => $this->icon(),
            'description' => $this->description(),
            'formatted_with_icon' => $this->formatted_with_icon()
        ];
    }

    /**
     * Get string representation of the status
     * 
     * @return string The status name
     */
    public function __toString(): string
    {
        return $this->name();
    }
}
