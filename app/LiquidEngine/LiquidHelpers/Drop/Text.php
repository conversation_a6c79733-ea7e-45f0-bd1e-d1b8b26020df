<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Support\Str;

/**
 * Text Drop Class
 * 
 * This class provides a structured way to handle text content in Liquid templates.
 * It includes support for Shopify compatibility, accessibility, SEO, and analytics.
 * 
 * @property string $content The text content
 * @property string $format The content format (plain, html, markdown)
 * @property array $attributes Additional HTML attributes
 * @property array $analytics Analytics data for the text
 */
class Text extends AbstractDrop
{
    /**
     * @var string The text content
     */
    protected string $content;

    /**
     * @var string The content format (plain, html, markdown)
     */
    protected string $format;

    /**
     * @var array<string, string> Additional HTML attributes
     */
    protected array $attributes;

    /**
     * @var array<string, mixed> Analytics data for the text
     */
    protected array $analytics;

    /**
     * Create a new Text instance.
     *
     * @param string $content The text content
     * @param string $format The content format
     * @param array<string, string> $attributes Additional HTML attributes
     * @param array<string, mixed> $analytics Analytics data
     */
    public function __construct(
        string $content,
        string $format = 'plain',
        array $attributes = [],
        array $analytics = []
    ) {
        $this->content = $content;
        $this->format = $format;
        $this->attributes = $attributes;
        $this->analytics = $analytics;
    }

    /**
     * Get the text content.
     *
     * @return string
     */
    public function content(): string
    {
        return $this->content;
    }

    /**
     * Get the content format.
     *
     * @return string
     */
    public function format(): string
    {
        return $this->format;
    }

    /**
     * Get additional HTML attributes.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return $this->attributes;
    }

    /**
     * Get analytics data.
     *
     * @return array<string, mixed>
     */
    public function analytics(): array
    {
        return $this->analytics;
    }

    /**
     * Get the plain text content.
     *
     * @return string
     */
    public function plain(): string
    {
        return strip_tags($this->content);
    }

    /**
     * Get the HTML content.
     *
     * @return string
     */
    public function html(): string
    {
        if ($this->format === 'html') {
            return $this->content;
        }

        if ($this->format === 'markdown') {
            return Str::markdown($this->content);
        }

        return htmlspecialchars($this->content, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Get the markdown content.
     *
     * @return string
     */
    public function markdown(): string
    {
        if ($this->format === 'markdown') {
            return $this->content;
        }

        if ($this->format === 'html') {
            // Convert HTML to Markdown (simplified)
            return strip_tags($this->content);
        }

        return $this->content;
    }

    /**
     * Get a truncated version of the text.
     *
     * @param int $length Maximum length
     * @param string $append String to append if truncated
     * @return string
     */
    public function truncate(int $length = 100, string $append = '...'): string
    {
        return Str::limit($this->plain(), $length, $append);
    }

    /**
     * Get accessibility attributes for the text.
     *
     * @return array<string, string>
     */
    public function accessibility(): array
    {
        return [
            'role' => 'text',
            'aria-label' => $this->truncate(50),
        ];
    }

    /**
     * Get structured data for SEO.
     *
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'WebPage',
            'mainEntity' => [
                '@type' => 'WebPageElement',
                'text' => $this->plain(),
                'contentFormat' => $this->format,
            ],
        ];
    }

    /**
     * Convert the text to a Shopify-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toShopifyArray(): array
    {
        return [
            'content' => $this->content,
            'format' => $this->format,
            'attributes' => $this->attributes,
            'plain' => $this->plain(),
            'html' => $this->html(),
            'markdown' => $this->markdown(),
        ];
    }

    /**
     * Convert the text to an SEO-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toSeoArray(): array
    {
        return [
            'content' => $this->content,
            'format' => $this->format,
            'plain' => $this->plain(),
            'html' => $this->html(),
            'structured_data' => $this->structured_data(),
        ];
    }

    /**
     * Convert the text to an analytics-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toAnalyticsArray(): array
    {
        return array_merge($this->analytics, [
            'content_length' => strlen($this->content),
            'plain_length' => strlen($this->plain()),
            'format' => $this->format,
        ]);
    }

    /**
     * Convert the text to an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'content' => $this->content,
            'format' => $this->format,
            'attributes' => $this->attributes,
            'analytics' => $this->analytics,
            'plain' => $this->plain(),
            'html' => $this->html(),
            'markdown' => $this->markdown(),
            'accessibility' => $this->accessibility(),
            'structured_data' => $this->structured_data(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics_data' => $this->toAnalyticsArray(),
        ];
    }
} 