<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Shop;

use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Helpers\Address;
use App\Models\Store\Shop as ShopModel;

class Shop extends AbstractDrop
{
    /**
     * @var ShopModel $_shop
     */
    protected \App\Models\Store\Shop $_shop;

    /**
     * @param \App\Models\Store\Shop $shop
     * @return void
     */
    public function __construct(ShopModel $shop)
    {
        $this->_shop = $shop;
    }

    public function id()
    {
        return $this->_shop->id;
    }

    public function name()
    {
        return $this->_shop->title;
    }

    public function city()
    {
        return $this->_shop->city;
    }

    public function address(): ?\App\LiquidEngine\LiquidHelpers\Drop\Helpers\Address
    {
        if ($this->_shop->relationLoaded('address') && $this->_shop->address) {
            return new Address($this->_shop->address);
        }

        return null;
    }

    public function phone()
    {
        return $this->_shop->phone;
    }

    public function work_time()
    {
        return $this->_shop->worktime;
    }

    public function url_handle()
    {
        return $this->_shop->url_handle;
    }

    public function seo_title()
    {
        return $this->_shop->seo_title;
    }

    public function seo_description()
    {
        return $this->_shop->seo_description;
    }

    public function latitude()
    {
        return $this->_shop->gps_lt;
    }

    public function longitude()
    {
        return $this->_shop->gps_ll;
    }

    public function email()
    {
        return $this->_shop->email;
    }

    public function url()
    {
        return route('stores.view', ['slug' => $this->url_handle()]);
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'city' => $this->city(),
            'address' => $this->address(),
            'phone' => $this->phone(),
            'work_time' => $this->work_time(),
            'url_handle' => $this->url_handle(),
            'seo_title' => $this->seo_title(),
            'seo_description' => $this->seo_description(),
            'latitude' => $this->latitude(),
            'longitude' => $this->longitude(),
            'email' => $this->email(),
            'url' => $this->url(),
        ];
    }

}
