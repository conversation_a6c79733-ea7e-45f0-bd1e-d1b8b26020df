<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;

/**
 * Class Debug
 * 
 * Provides debugging functionality for Liquid templates.
 * Implements Shopify-compatible debug methods.
 * 
 * @property-read bool $enabled Whether debugging is enabled
 * @property-read array $config Debug configuration
 * @property-read array $environment Current environment information
 * @property-read array $performance Performance metrics
 * @property-read array $memory Memory usage information
 * @property-read array $queries Database query information
 * @property-read array $errors Error information
 * @property-read array $warnings Warning information
 */
class Debug extends AbstractDrop
{
    /**
     * Whether debugging is enabled.
     *
     * @var bool
     */
    protected bool $enabled;

    /**
     * Debug configuration.
     *
     * @var array{
     *     show_queries: bool,
     *     show_memory: bool,
     *     show_performance: bool,
     *     show_errors: bool,
     *     show_warnings: bool,
     *     show_environment: bool
     * }
     */
    protected array $config;

    /**
     * Create a new Debug instance.
     */
    public function __construct()
    {
        $this->enabled = Config::get('app.debug', false);
        $this->config = [
            'show_queries' => true,
            'show_memory' => true,
            'show_performance' => true,
            'show_errors' => true,
            'show_warnings' => true,
            'show_environment' => true
        ];
    }

    /**
     * Check if debugging is enabled.
     *
     * @return bool
     */
    public function enabled(): bool
    {
        return $this->enabled;
    }

    /**
     * Get debug configuration.
     *
     * @return array{
     *     show_queries: bool,
     *     show_memory: bool,
     *     show_performance: bool,
     *     show_errors: bool,
     *     show_warnings: bool,
     *     show_environment: bool
     * }
     */
    public function config(): array
    {
        return $this->config;
    }

    /**
     * Get current environment information.
     *
     * @return array{
     *     app_env: string,
     *     app_debug: bool,
     *     app_url: string,
     *     app_timezone: string,
     *     app_locale: string
     * }
     */
    public function environment(): array
    {
        return [
            'app_env' => App::environment(),
            'app_debug' => Config::get('app.debug', false),
            'app_url' => Config::get('app.url', ''),
            'app_timezone' => Config::get('app.timezone', 'UTC'),
            'app_locale' => App::getLocale()
        ];
    }

    /**
     * Get performance metrics.
     *
     * @return array{
     *     execution_time: float,
     *     memory_peak: int,
     *     memory_usage: int,
     *     query_count: int
     * }
     */
    public function performance(): array
    {
        return [
            'execution_time' => microtime(true) - LARAVEL_START,
            'memory_peak' => memory_get_peak_usage(true),
            'memory_usage' => memory_get_usage(true),
            'query_count' => $this->getQueryCount()
        ];
    }

    /**
     * Get memory usage information.
     *
     * @return array{
     *     peak: int,
     *     current: int,
     *     limit: int,
     *     percentage: float
     * }
     */
    public function memory(): array
    {
        $peak = memory_get_peak_usage(true);
        $current = memory_get_usage(true);
        $limit = ini_get('memory_limit');
        
        return [
            'peak' => $peak,
            'current' => $current,
            'limit' => $this->parseMemoryLimit($limit),
            'percentage' => $this->calculateMemoryPercentage($current, $limit)
        ];
    }

    /**
     * Get database query information.
     *
     * @return array{
     *     count: int,
     *     time: float,
     *     queries: array<int, array{query: string, time: float, bindings: array}>
     * }
     */
    public function queries(): array
    {
        if (!App::bound('db')) {
            return [
                'count' => 0,
                'time' => 0.0,
                'queries' => []
            ];
        }

        $queries = App::make('db')->getQueryLog();
        $time = array_sum(array_column($queries, 'time'));

        return [
            'count' => count($queries),
            'time' => $time,
            'queries' => $queries
        ];
    }

    /**
     * Get error information.
     *
     * @return array{
     *     count: int,
     *     errors: array<int, array{message: string, file: string, line: int, trace: array}>
     * }
     */
    public function errors(): array
    {
        $errors = error_get_last();
        
        if (!$errors) {
            return [
                'count' => 0,
                'errors' => []
            ];
        }

        return [
            'count' => 1,
            'errors' => [[
                'message' => $errors['message'],
                'file' => $errors['file'],
                'line' => $errors['line'],
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
            ]]
        ];
    }

    /**
     * Get warning information.
     *
     * @return array{
     *     count: int,
     *     warnings: array<int, array{message: string, file: string, line: int}>
     * }
     */
    public function warnings(): array
    {
        return [
            'count' => 0,
            'warnings' => []
        ];
    }

    /**
     * Get accessibility attributes.
     *
     * @return array{
     *     aria_label: string,
     *     role: string
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => 'Debug information',
            'role' => 'status'
        ];
    }

    /**
     * Get structured data for SEO.
     *
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'debug' => [
                'enabled' => $this->enabled(),
                'environment' => $this->environment(),
                'performance' => $this->performance()
            ]
        ];
    }

    /**
     * Convert to Shopify format.
     *
     * @return array{
     *     enabled: bool,
     *     config: array{show_queries: bool, show_memory: bool, show_performance: bool, show_errors: bool, show_warnings: bool, show_environment: bool},
     *     environment: array{app_env: string, app_debug: bool, app_url: string, app_timezone: string, app_locale: string},
     *     performance: array{execution_time: float, memory_peak: int, memory_usage: int, query_count: int},
     *     memory: array{peak: int, current: int, limit: int, percentage: float},
     *     queries: array{count: int, time: float, queries: array<int, array{query: string, time: float, bindings: array}>},
     *     errors: array{count: int, errors: array<int, array{message: string, file: string, line: int, trace: array}>},
     *     warnings: array{count: int, warnings: array<int, array{message: string, file: string, line: int}>},
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toShopifyArray(): array
    {
        return [
            'enabled' => $this->enabled(),
            'config' => $this->config(),
            'environment' => $this->environment(),
            'performance' => $this->performance(),
            'memory' => $this->memory(),
            'queries' => $this->queries(),
            'errors' => $this->errors(),
            'warnings' => $this->warnings(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to SEO array format.
     *
     * @return array{
     *     enabled: bool,
     *     config: array{show_queries: bool, show_memory: bool, show_performance: bool, show_errors: bool, show_warnings: bool, show_environment: bool},
     *     environment: array{app_env: string, app_debug: bool, app_url: string, app_timezone: string, app_locale: string},
     *     performance: array{execution_time: float, memory_peak: int, memory_usage: int, query_count: int},
     *     memory: array{peak: int, current: int, limit: int, percentage: float},
     *     queries: array{count: int, time: float, queries: array<int, array{query: string, time: float, bindings: array}>},
     *     errors: array{count: int, errors: array<int, array{message: string, file: string, line: int, trace: array}>},
     *     warnings: array{count: int, warnings: array<int, array{message: string, file: string, line: int}>},
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toSeoArray(): array
    {
        return [
            'enabled' => $this->enabled(),
            'config' => $this->config(),
            'environment' => $this->environment(),
            'performance' => $this->performance(),
            'memory' => $this->memory(),
            'queries' => $this->queries(),
            'errors' => $this->errors(),
            'warnings' => $this->warnings(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to analytics array format.
     *
     * @return array{
     *     enabled: bool,
     *     config: array{show_queries: bool, show_memory: bool, show_performance: bool, show_errors: bool, show_warnings: bool, show_environment: bool},
     *     environment: array{app_env: string, app_debug: bool, app_url: string, app_timezone: string, app_locale: string},
     *     performance: array{execution_time: float, memory_peak: int, memory_usage: int, query_count: int},
     *     memory: array{peak: int, current: int, limit: int, percentage: float},
     *     queries: array{count: int, time: float, queries: array<int, array{query: string, time: float, bindings: array}>},
     *     errors: array{count: int, errors: array<int, array{message: string, file: string, line: int, trace: array}>},
     *     warnings: array{count: int, warnings: array<int, array{message: string, file: string, line: int}>},
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toAnalyticsArray(): array
    {
        return [
            'enabled' => $this->enabled(),
            'config' => $this->config(),
            'environment' => $this->environment(),
            'performance' => $this->performance(),
            'memory' => $this->memory(),
            'queries' => $this->queries(),
            'errors' => $this->errors(),
            'warnings' => $this->warnings(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Get the collection as a plain array.
     *
     * @return array{
     *     enabled: bool,
     *     config: array{show_queries: bool, show_memory: bool, show_performance: bool, show_errors: bool, show_warnings: bool, show_environment: bool},
     *     environment: array{app_env: string, app_debug: bool, app_url: string, app_timezone: string, app_locale: string},
     *     performance: array{execution_time: float, memory_peak: int, memory_usage: int, query_count: int},
     *     memory: array{peak: int, current: int, limit: int, percentage: float},
     *     queries: array{count: int, time: float, queries: array<int, array{query: string, time: float, bindings: array}>},
     *     errors: array{count: int, errors: array<int, array{message: string, file: string, line: int, trace: array}>},
     *     warnings: array{count: int, warnings: array<int, array{message: string, file: string, line: int}>},
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string},
     *     shopify: array{enabled: bool, config: array{show_queries: bool, show_memory: bool, show_performance: bool, show_errors: bool, show_warnings: bool, show_environment: bool}, environment: array{app_env: string, app_debug: bool, app_url: string, app_timezone: string, app_locale: string}, performance: array{execution_time: float, memory_peak: int, memory_usage: int, query_count: int}, memory: array{peak: int, current: int, limit: int, percentage: float}, queries: array{count: int, time: float, queries: array<int, array{query: string, time: float, bindings: array}>}, errors: array{count: int, errors: array<int, array{message: string, file: string, line: int, trace: array}>}, warnings: array{count: int, warnings: array<int, array{message: string, file: string, line: int}>}, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     seo: array{enabled: bool, config: array{show_queries: bool, show_memory: bool, show_performance: bool, show_errors: bool, show_warnings: bool, show_environment: bool}, environment: array{app_env: string, app_debug: bool, app_url: string, app_timezone: string, app_locale: string}, performance: array{execution_time: float, memory_peak: int, memory_usage: int, query_count: int}, memory: array{peak: int, current: int, limit: int, percentage: float}, queries: array{count: int, time: float, queries: array<int, array{query: string, time: float, bindings: array}>}, errors: array{count: int, errors: array<int, array{message: string, file: string, line: int, trace: array}>}, warnings: array{count: int, warnings: array<int, array{message: string, file: string, line: int}>}, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     analytics: array{enabled: bool, config: array{show_queries: bool, show_memory: bool, show_performance: bool, show_errors: bool, show_warnings: bool, show_environment: bool}, environment: array{app_env: string, app_debug: bool, app_url: string, app_timezone: string, app_locale: string}, performance: array{execution_time: float, memory_peak: int, memory_usage: int, query_count: int}, memory: array{peak: int, current: int, limit: int, percentage: float}, queries: array{count: int, time: float, queries: array<int, array{query: string, time: float, bindings: array}>}, errors: array{count: int, errors: array<int, array{message: string, file: string, line: int, trace: array}>}, warnings: array{count: int, warnings: array<int, array{message: string, file: string, line: int}>}, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}}
     * }
     */
    public function toArray(): array
    {
        return [
            'enabled' => $this->enabled(),
            'config' => $this->config(),
            'environment' => $this->environment(),
            'performance' => $this->performance(),
            'memory' => $this->memory(),
            'queries' => $this->queries(),
            'errors' => $this->errors(),
            'warnings' => $this->warnings(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics' => $this->toAnalyticsArray()
        ];
    }

    /**
     * Get the number of database queries executed.
     *
     * @return int
     */
    protected function getQueryCount(): int
    {
        if (!App::bound('db')) {
            return 0;
        }

        return count(App::make('db')->getQueryLog());
    }

    /**
     * Parse memory limit string to bytes.
     *
     * @param string $limit
     * @return int
     */
    protected function parseMemoryLimit(string $limit): int
    {
        $unit = strtolower(substr($limit, -1));
        $value = (int) $limit;

        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Calculate memory usage percentage.
     *
     * @param int $current
     * @param string $limit
     * @return float
     */
    protected function calculateMemoryPercentage(int $current, string $limit): float
    {
        $limitBytes = $this->parseMemoryLimit($limit);
        return $limitBytes > 0 ? ($current / $limitBytes) * 100 : 0.0;
    }
} 