<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 5.9.2019 г.
 * Time: 09:10 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Models\Selection\ProductSelection;

/**
 * Selection Drop class for handling product selection data in Liquid templates.
 *
 * This class represents a product selection in the Liquid template system,
 * providing access to selection properties and SEO-related information. It wraps
 * the ProductSelection model to provide a clean interface for Liquid templates.
 *
 * Properties:
 * - id: Selection ID (int)
 * - name: Selection name (string)
 * - seo_title: SEO title for the selection (string)
 * - seo_description: SEO description for the selection (string)
 * - url_handle: URL-friendly version of the selection name (string)
 * - url: Full URL to the selection's page (string)
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class Selection extends AbstractDrop
{
    /**
     * The underlying product selection model instance.
     * Contains the raw selection data from the database.
     *
     * @var ProductSelection
     */
    protected ProductSelection $_selection;

    /**
     * Create a new Selection instance.
     * Initializes the drop with a ProductSelection model instance.
     *
     * @param ProductSelection $selection The product selection model instance
     */
    public function __construct(ProductSelection $selection)
    {
        $this->_selection = $selection;
    }

    /**
     * Get the unique identifier of the selection.
     * Returns the primary key of the underlying selection model.
     *
     * @return int The selection ID
     */
    public function id(): int
    {
        return (int) $this->_selection->id;
    }

    /**
     * Get the name of the selection.
     * Returns the human-readable name of the selection.
     *
     * @return string The selection name
     */
    public function name(): string
    {
        return (string) $this->_selection->name;
    }

    /**
     * Get the SEO title for the selection.
     * Returns the SEO title from the underlying selection model.
     *
     * @return string The SEO title
     */
    public function seo_title(): string
    {
        return (string) $this->_selection->seo_title;
    }

    /**
     * Get the SEO description for the selection.
     * Returns the SEO description from the underlying selection model.
     *
     * @return string The SEO description
     */
    public function seo_description(): string
    {
        return (string) $this->_selection->seo_description;
    }

    /**
     * Get the URL handle for the selection.
     * Returns a URL-friendly version of the selection name.
     *
     * @return string The URL handle
     */
    public function url_handle(): string
    {
        return (string) $this->_selection->url_handle;
    }

    /**
     * Get the URL for the selection.
     * Returns the full URL to the selection's page.
     *
     * @return string The selection URL
     */
    public function url(): string
    {
        return (string) $this->_selection->url;
    }

    /**
     * Get the collection of items as a plain array.
     * Returns an array representation of the selection with all its properties.
     * This method is used by Liquid templates to access the selection data.
     *
     * @return array{
     *     id: int,
     *     name: string,
     *     seo_title: string,
     *     seo_description: string,
     *     url_handle: string,
     *     url: string
     * } The selection data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'seo_title' => $this->seo_title(),
            'seo_description' => $this->seo_description(),
            'url_handle' => $this->url_handle(),
            'url' => $this->url(),
        ];
    }
}
