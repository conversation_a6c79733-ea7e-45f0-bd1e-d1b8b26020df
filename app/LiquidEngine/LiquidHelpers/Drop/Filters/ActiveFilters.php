<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.9.2019 г.
 * Time: 17:24 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters;

use ArrayIterator;
use Illuminate\Contracts\Support\Arrayable;
use IteratorAggregate;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

class ActiveFilters extends AbstractDrop implements IteratorAggregate
{

    protected array $_filters;

    /**
     * ActiveFilters constructor.
     * @param array $filters
     */
    public function __construct($filters)
    {
        if ($filters instanceof Arrayable) {
            $filters = $filters->toArray();
        }

        $this->_filters = is_array($filters) ? $filters : [];
    }

    public function size(): int
    {
        return count($this->_filters);
    }

    public function all(): array
    {
        return $this->_filters;
    }

    /**
     * @return \Traversable
     */
    public function getIterator(): \Traversable
    {
        return new ArrayIterator($this->_filters);
    }

    /**
     * Shopify compatibility: count
     * Returns the number of active filters
     * @return int
     */
    public function count(): int
    {
        return $this->size();
    }

    /**
     * Shopify compatibility: empty
     * Returns whether there are no active filters
     * @return bool
     */
    public function empty(): bool
    {
        return $this->size() === 0;
    }

    /**
     * Shopify compatibility: has_filters
     * Returns whether there are any active filters
     * @return bool
     */
    public function has_filters(): bool
    {
        return $this->size() > 0;
    }

    /**
     * Shopify compatibility: clear_url
     * Returns the URL to clear all active filters
     * @return string
     */
    public function clear_url(): string
    {
        $currentUrl = request()->url();
        $params = request()->query();

        // Remove filter parameters
        $filteredParams = array_filter($params, function ($key) {
            return !str_starts_with($key, 'filter.');
        }, ARRAY_FILTER_USE_KEY);

        return $currentUrl . (!empty($filteredParams) ? '?' . http_build_query($filteredParams) : '');
    }

    /**
     * Shopify compatibility: url_to_remove_all
     * Alias for clear_url
     * @return string
     */
    public function url_to_remove_all(): string
    {
        return $this->clear_url();
    }

    /**
     * Analytics: filter_types
     * Returns the types of active filters
     * @return array
     */
    public function filter_types(): array
    {
        $types = [];
        foreach ($this->_filters as $filter) {
            if (method_exists($filter, 'type')) {
                $types[] = $filter->type();
            }
        }
        return array_unique($types);
    }

    /**
     * Analytics: filter_counts
     * Returns the count of filters by type
     * @return array
     */
    public function filter_counts(): array
    {
        $counts = [];
        foreach ($this->filter_types() as $type) {
            $counts[$type] = count(array_filter($this->_filters, function ($filter) use ($type) {
                return method_exists($filter, 'type') && $filter->type() === $type;
            }));
        }
        return $counts;
    }

    /**
     * Analytics: summary
     * Returns a summary of active filters
     * @return array
     */
    public function summary(): array
    {
        return [
            'total_filters' => $this->size(),
            'filter_types' => $this->filter_types(),
            'filter_counts' => $this->filter_counts(),
            'clear_url' => $this->clear_url()
        ];
    }

    /**
     * SEO: structured_data
     * Returns the JSON-LD structured data for active filters
     * @return array
     */
    public function structured_data(): array
    {
        $data = [
            '@context' => 'https://schema.org',
            '@type' => 'ItemList',
            'itemListElement' => []
        ];

        foreach ($this->_filters as $index => $filter) {
            if (method_exists($filter, 'toArray')) {
                $data['itemListElement'][] = [
                    '@type' => 'ListItem',
                    'position' => $index + 1,
                    'item' => $filter->toArray()
                ];
            }
        }

        return $data;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'count' => $this->count(),
            'empty' => $this->empty(),
            'has_filters' => $this->has_filters(),
            'clear_url' => $this->clear_url(),
            'url_to_remove_all' => $this->url_to_remove_all(),
            'filter_types' => $this->filter_types(),
            'filter_counts' => $this->filter_counts(),
            'summary' => $this->summary(),
            'structured_data' => $this->structured_data()
        ];
    }
}
