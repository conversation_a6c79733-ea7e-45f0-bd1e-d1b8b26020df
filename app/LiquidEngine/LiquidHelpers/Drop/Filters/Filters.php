<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.9.2019 г.
 * Time: 17:24 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters;

use Illuminate\Config\Repository;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use Apps;
use App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\AbstractFilter;
use App\LiquidEngine\Models\Theme;

class Filters extends AbstractDrop
{
    /**
     * @var array $allowed_filters
     */
    protected $allowed_filters = [
        'categories', 'price_ranges', 'variants',
        'vendors', 'category_properties', 'other'
    ];

    const FILTER_NAMESPACE = '\\App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\%s';

    protected $_filters;

    protected \Illuminate\Config\Repository $config;

    protected $_global_filters = [];

    /**
     * Filters constructor.
     * @param $filters
     * @param $query
     * @param $makeFilters
     */
    public function __construct($filters = null, $query = null, $makeFilters = null)
    {
        $this->_global_filters = array_merge(is_array($filters) ? $filters : [], [
            '_model_query' => $query instanceof Builder ? $query : null,
            '_makeFilters' => is_array($makeFilters) ? $makeFilters : []
        ]);

        $settings = Theme::getThemeSystemConfigs()->get('@system.products.filters', []);
        $this->config = new Repository(is_array($settings) ? $settings : []);
    }

    /**
     * @return array
     */
    public function getAllowedFilters(): array
    {
        $filters = $this->allowed_filters;

        //brand model
        if (Apps::installed('brand_model')) {
            $filters[] = 'brand_model';
        }

        return $filters;
    }

    /**
     * @return array
     */
    public function all(): array
    {
        if (!is_null($this->_filters)) {
            return $this->_filters;
        }

        $results = [];
        foreach ($this->getAllowedFilters() as $filter) {
            /** @var null|AbstractFilter $init */
            if (($init = $this->initFilter($filter)) && $init->allowed()) {
                $results[$filter] = $init;
            }
        }

        $sorting = $this->config->get('sorting', []);
        $results = Arr::sort($results, function ($filter, $key) use ($sorting): int|string|false {
            return array_search($key, $sorting, true);
        });

        return $this->_filters = $results;
    }

    /**
     * Catch all method that is invoked before a specific method
     *
     * @param string $method
     *
     * @return null
     */
    protected function initFilter(string $method)
    {
        if (
            in_array($method, $this->getAllowedFilters()) &&
            class_exists($className = sprintf(static::FILTER_NAMESPACE, \Illuminate\Support\Str::studly($method))) &&
            ($allowed = $this->config->get('filters.' . $method))
        ) {
            if ($method == 'other') {
                $filter = app($className, array_merge($this->_global_filters, ['_allowed' => $allowed]));
            } else {
                $filter = app($className, array_merge($this->_global_filters));
            }

            $filter->setConfig($this->config);

            return $filter;
        }

        return null;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return $this->_dataToArray($this->all());
    }

    /**
     * @param $items
     * @return array
     */
    protected function _dataToArray($items)
    {
        if ($items instanceof Arrayable) {
            $items = $items->toArray();
        }

        if (!is_array($items)) {
            return $items;
        }

        return array_map([$this, '_dataToArray'], $items);
    }

    /**
     * Get all available filters
     * Shopify-compatible method
     */
    public function filters(): array
    {
        return $this->all();
    }

    /**
     * Get the total number of filters
     * Shopify-compatible method
     */
    public function size(): int
    {
        return count($this->all());
    }

    /**
     * Get the total number of filters
     * Shopify-compatible method (alias for size)
     */
    public function count(): int
    {
        return $this->size();
    }

    /**
     * Check if there are any filters available
     * Shopify-compatible method
     */
    public function has_filters(): bool
    {
        return $this->size() > 0;
    }

    /**
     * Check if filters are empty
     * Shopify-compatible method
     */
    public function empty(): bool
    {
        return $this->size() === 0;
    }

    /**
     * Get active filters only
     * Shopify-compatible method
     */
    public function active_filters(): array
    {
        $activeFilters = [];
        foreach ($this->all() as $filter) {
            if (method_exists($filter, 'active_values') && !empty($filter->active_values())) {
                $activeFilters[] = $filter;
            }
        }
        return $activeFilters;
    }

    /**
     * Get filters by type
     * Shopify-compatible method
     */
    public function by_type(string $type): array
    {
        $filters = [];
        foreach ($this->all() as $filter) {
            if (method_exists($filter, 'type') && $filter->type() === $type) {
                $filters[] = $filter;
            }
        }
        return $filters;
    }

    /**
     * Get filter by handle/key
     * Shopify-compatible method
     */
    public function by_handle(string $handle): ?AbstractFilter
    {
        foreach ($this->all() as $key => $filter) {
            if ($key === $handle || (method_exists($filter, 'handle') && $filter->handle() === $handle)) {
                return $filter;
            }
        }
        return null;
    }

    /**
     * Get filter by name/label
     * Shopify-compatible method
     */
    public function by_name(string $name): ?AbstractFilter
    {
        foreach ($this->all() as $filter) {
            if (method_exists($filter, 'name') && $filter->name() === $name) {
                return $filter;
            }
            if (method_exists($filter, 'label') && $filter->label() === $name) {
                return $filter;
            }
        }
        return null;
    }

    /**
     * Get available filter types
     * Shopify-compatible method
     */
    public function available_types(): array
    {
        $types = [];
        foreach ($this->all() as $filter) {
            if (method_exists($filter, 'type')) {
                $types[] = $filter->type();
            }
        }
        return array_unique($types);
    }

    /**
     * Get filters that have active values
     * Shopify-compatible method
     */
    public function with_active_values(): array
    {
        return $this->active_filters();
    }

    /**
     * Get price range filters
     * Shopify-compatible method
     */
    public function price_range_filters(): array
    {
        return $this->by_type('price_range');
    }

    /**
     * Get boolean filters
     * Shopify-compatible method
     */
    public function boolean_filters(): array
    {
        return $this->by_type('boolean');
    }

    /**
     * Get list filters
     * Shopify-compatible method
     */
    public function list_filters(): array
    {
        return $this->by_type('list');
    }

    /**
     * Generate clear all filters URL
     * Shopify-compatible method
     */
    public function clear_all_url(): string
    {
        $currentUrl = request()->url();
        $params = request()->query();

        // Remove filter parameters
        $filteredParams = array_filter($params, function ($key) {
            return !str_starts_with($key, 'filter.');
        }, ARRAY_FILTER_USE_KEY);

        return $currentUrl . (!empty($filteredParams) ? '?' . http_build_query($filteredParams) : '');
    }

    /**
     * Get URL to remove all filters
     * Shopify-compatible method (alias for clear_all_url)
     */
    public function url_to_remove_all(): string
    {
        return $this->clear_all_url();
    }

    /**
     * Check if any filters are currently applied
     * Shopify-compatible method
     */
    public function has_active_filters(): bool
    {
        return count($this->active_filters()) > 0;
    }

    /**
     * Get the total count of active filter values
     * Shopify-compatible method
     */
    public function active_values_count(): int
    {
        $count = 0;
        foreach ($this->active_filters() as $filter) {
            if (method_exists($filter, 'active_values')) {
                $count += count($filter->active_values());
            }
        }
        return $count;
    }

    /**
     * Format filters for form display
     * Shopify-compatible method
     */
    public function to_form(): array
    {
        $formData = [];
        foreach ($this->all() as $key => $filter) {
            if (method_exists($filter, 'to_form')) {
                $formData[$key] = $filter->to_form();
            } else {
                $formData[$key] = $filter;
            }
        }
        return $formData;
    }

    /**
     * Get filters summary for display
     * Shopify-compatible method
     */
    public function summary(): array
    {
        return [
            'total_filters' => $this->size(),
            'active_filters' => count($this->active_filters()),
            'active_values' => $this->active_values_count(),
            'available_types' => $this->available_types(),
            'has_active' => $this->has_active_filters()
        ];
    }

    /**
     * Get filters configuration
     * Shopify-compatible method
     */
    public function configuration(): array
    {
        return $this->config->all();
    }

    /**
     * Get filter labels/names
     * Shopify-compatible method
     */
    public function labels(): array
    {
        $labels = [];
        foreach ($this->all() as $filter) {
            if (method_exists($filter, 'label')) {
                $labels[] = $filter->label();
            } elseif (method_exists($filter, 'name')) {
                $labels[] = $filter->name();
            }
        }
        return $labels;
    }

    /**
     * Get filter handles/keys
     * Shopify-compatible method
     */
    public function handles(): array
    {
        $handles = [];
        foreach ($this->all() as $key => $filter) {
            if (method_exists($filter, 'handle')) {
                $handles[] = $filter->handle();
            } else {
                $handles[] = $key;
            }
        }
        return $handles;
    }

    /**
     * Get filters that allow multiple selection
     * Shopify-compatible method
     */
    public function multiple_selection(): array
    {
        $filters = [];
        foreach ($this->all() as $filter) {
            if (method_exists($filter, 'allows_multiple') && $filter->allows_multiple()) {
                $filters[] = $filter;
            }
        }
        return $filters;
    }

    /**
     * Get required filters
     * Shopify-compatible method
     */
    public function required(): array
    {
        $filters = [];
        foreach ($this->all() as $filter) {
            if (method_exists($filter, 'required') && $filter->required()) {
                $filters[] = $filter;
            }
        }
        return $filters;
    }

    /**
     * Get visible filters
     * Shopify-compatible method
     */
    public function visible(): array
    {
        $filters = [];
        foreach ($this->all() as $filter) {
            if (method_exists($filter, 'visible')) {
                if ($filter->visible()) {
                    $filters[] = $filter;
                }
            } else {
                // Default to visible if method doesn't exist
                $filters[] = $filter;
            }
        }
        return $filters;
    }

    /**
     * Get first filter
     * Shopify-compatible method
     */
    public function first(): ?AbstractFilter
    {
        $filters = $this->all();
        return !empty($filters) ? array_values($filters)[0] : null;
    }

    /**
     * Get last filter
     * Shopify-compatible method
     */
    public function last(): ?AbstractFilter
    {
        $filters = $this->all();
        return !empty($filters) ? array_values($filters)[count($filters) - 1] : null;
    }

    /**
     * Check if filters support the given feature
     * Shopify-compatible method
     */
    public function supports(string $feature): bool
    {
        return $this->config->get('features.' . $feature, false);
    }

    /**
     * Get filter parameters from current request
     * Shopify-compatible method
     */
    public function current_parameters(): array
    {
        $params = [];
        foreach (request()->query() as $key => $value) {
            if (str_starts_with($key, 'filter.')) {
                $params[$key] = $value;
            }
        }
        return $params;
    }

    /**
     * Check if filter system is enabled
     * Shopify-compatible method
     */
    public function enabled(): bool
    {
        return $this->config->get('enabled', true);
    }

    /**
     * Get filters context for templates
     * Shopify-compatible method
     */
    public function context(): array
    {
        return [
            'filters' => $this->visible(),
            'active_filters' => $this->active_filters(),
            'summary' => $this->summary(),
            'clear_url' => $this->clear_all_url(),
            'enabled' => $this->enabled()
        ];
    }
}
