<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.4.2019 г.
 * Time: 12:02 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType;

use App\Models\Product\Category as CategoryModel;
use App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Category;

class Categories extends AbstractFilter
{
    /**
     * The categories filter data.
     *
     * @var array<string, mixed>|null
     */
    protected ?array $_categories = null;

    /**
     * {@inheritDoc}
     * @return array<string, mixed>
     */
    protected function items(): array
    {
        if (!is_null($this->_categories)) {
            return $this->_categories;
        }

        /** @var \Illuminate\Database\Eloquent\Collection<int, CategoryModel> $categories */
        $categories = CategoryModel::whereParentId(null)
            ->with(['childrenRecursive'])
            ->get();

        return $this->_categories = $categories->map(function (CategoryModel $category): Category {
            return new Category($category);
        })->all();
    }

    /**
     * Get the filter name
     * @return string
     */
    public function name(): string
    {
        return __('item.catalog.filters.text.categories');
    }

    /**
     * Get the filter type
     * @return string
     */
    public function type(): string
    {
        return 'category';
    }

    /**
     * Get the filter description
     * @return string|null
     */
    public function description(): ?string
    {
        return __('item.catalog.filters.description.categories');
    }

    /**
     * Get active filter values
     * @return array<string, array{id: int, name: string, handle: string, url: string, level: int, parent_id: int|null}>
     */
    public function active_values(): array
    {
        $activeValues = [];
        foreach ($this->items() as $category) {
            if ($category->active()) {
                $activeValues[] = [
                    'id' => $category->id(),
                    'name' => $category->name(),
                    'handle' => $category->url_handle(),
                    'url' => $category->url(),
                    'level' => $category->level(),
                    'parent_id' => $category->parent_id()
                ];
            }
            foreach ($category->dropdown() as $child) {
                if ($child->active()) {
                    $activeValues[] = [
                        'id' => $child->id(),
                        'name' => $child->name(),
                        'handle' => $child->url_handle(),
                        'url' => $child->url(),
                        'level' => $child->level(),
                        'parent_id' => $child->parent_id()
                    ];
                }
            }
        }
        return $activeValues;
    }

    /**
     * Get filter usage count
     * @return int
     */
    public function usage_count(): int
    {
        $count = 0;
        foreach ($this->items() as $category) {
            $count += $category->total_products();
            foreach ($category->dropdown() as $child) {
                $count += $child->total_products();
            }
        }
        return $count;
    }

    /**
     * Get SEO meta title
     * @return string
     */
    public function meta_title(): string
    {
        $activeCategories = array_filter($this->items(), fn($category) => $category->active());
        if (empty($activeCategories)) {
            return $this->name();
        }
        return implode(' > ', array_map(fn($category) => $category->name(), $activeCategories));
    }

    /**
     * Get SEO meta description
     * @return string
     */
    public function meta_description(): string
    {
        $activeCategories = array_filter($this->items(), fn($category) => $category->active());
        if (empty($activeCategories)) {
            return $this->description() ?? '';
        }
        return sprintf(
            __('item.catalog.filters.meta_description.categories'),
            implode(' > ', array_map(fn($category) => $category->name(), $activeCategories))
        );
    }

    /**
     * Get canonical URL
     * @return string|null
     */
    public function canonical_url(): ?string
    {
        $activeCategories = array_filter($this->items(), fn($category) => $category->active());
        if (empty($activeCategories)) {
            return null;
        }
        $lastCategory = end($activeCategories);
        return $lastCategory->url();
    }

    /**
     * Get structured data for SEO
     * @return array{
     *     '@context': string,
     *     '@type': string,
     *     'itemListElement': array<array{
     *         '@type': string,
     *         'name': string,
     *         'url': string,
     *         'position': int,
     *         'hasPart': array<array{
     *             '@type': string,
     *             'name': string,
     *             'url': string,
     *             'position': int
     *         }>
     *     }>
     * }
     */
    public function structured_data(): array
    {
        $categories = array_map(function($category) {
            $children = array_map(function($child) {
                return [
                    '@type' => 'Category',
                    'name' => $child->name(),
                    'url' => $child->url(),
                    'position' => $child->sort_order()
                ];
            }, $category->dropdown());

            return [
                '@type' => 'Category',
                'name' => $category->name(),
                'url' => $category->url(),
                'position' => $category->sort_order(),
                'hasPart' => $children
            ];
        }, $this->items());

        return [
            '@context' => 'https://schema.org',
            '@type' => 'ItemList',
            'itemListElement' => $categories
        ];
    }

    /**
     * Check if the filter allows multiple selections
     * @return bool
     */
    public function allows_multiple(): bool
    {
        return true;
    }

    /**
     * Get filter configuration
     * @return array{
     *     multiple_selection: bool,
     *     display_type: string,
     *     show_counts: bool,
     *     max_depth: int,
     *     sort_by: string,
     *     sort_order: string
     * }
     */
    public function configuration(): array
    {
        $config = parent::configuration();
        return array_merge($config, [
            'multiple_selection' => true,
            'display_type' => 'tree',
            'show_counts' => true,
            'max_depth' => 3,
            'sort_by' => 'name',
            'sort_order' => 'asc'
        ]);
    }

    /**
     * Get filter context for templates
     * @return array{
     *     display_type: string,
     *     show_counts: bool,
     *     max_depth: int,
     *     sort_by: string,
     *     sort_order: string,
     *     categories: array<array{
     *         id: int,
     *         name: string,
     *         handle: string,
     *         url: string,
     *         active: bool,
     *         level: int,
     *         parent_id: int|null,
     *         children: array<array{
     *             id: int,
     *             name: string,
     *             handle: string,
     *             url: string,
     *             active: bool,
     *             level: int,
     *             parent_id: int|null
     *         }>
     *     }>
     * }
     */
    public function context(): array
    {
        $context = parent::context();
        return array_merge($context, [
            'display_type' => 'tree',
            'show_counts' => true,
            'max_depth' => 3,
            'sort_by' => 'name',
            'sort_order' => 'asc',
            'categories' => array_map(function($category) {
                return [
                    'id' => $category->id(),
                    'name' => $category->name(),
                    'handle' => $category->url_handle(),
                    'url' => $category->url(),
                    'active' => $category->active(),
                    'level' => $category->level(),
                    'parent_id' => $category->parent_id(),
                    'children' => array_map(function($child) {
                        return [
                            'id' => $child->id(),
                            'name' => $child->name(),
                            'handle' => $child->url_handle(),
                            'url' => $child->url(),
                            'active' => $child->active(),
                            'level' => $child->level(),
                            'parent_id' => $child->parent_id()
                        ];
                    }, $category->dropdown())
                ];
            }, $this->items())
        ]);
    }

}
