<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.4.2019 г.
 * Time: 12:02 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType;

use App\Models\Product\Product;
use App\Models\Product\Vendor as VendorModel;
use Illuminate\Database\Query\JoinClause;
use App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Vendor;

/**
 * Class Vendors
 * 
 * Represents a filter for product vendors/brands.
 * This class extends AbstractFilter to provide filtering functionality for vendor-based product filtering.
 * 
 * @property-read int|null $_vendor_id Current vendor ID if filtering by specific vendor
 * @property-read array<\App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Vendor>|null $_vendors Cached vendor items
 * @property-read string $handle Filter handle for Shopify compatibility
 * @property-read string $title Filter title for Shopify compatibility
 * @property-read string $type Filter type for Shopify compatibility
 * @property-read string|null $description Filter description for Shopify compatibility
 * @property-read int $sort_order Filter sort order for Shopify compatibility
 * @property-read bool $is_required Whether the filter is required
 * @property-read bool $allows_multiple Whether the filter allows multiple selections
 * @property-read bool $is_visible Whether the filter is visible
 * @property-read array<array{id: int, name: string, value: string, count: int, active: bool}> $active_values Active filter values
 * @property-read int $total_values Total number of filter values
 * @property-read int $usage_count Number of times the filter is used
 * @property-read array{multiple_selection: bool, display_type: string, show_counts: bool, sort_by: string, sort_order: string} $configuration Filter configuration
 * @property-read array{display_type: string, show_counts: bool, sort_by: string, sort_order: string, vendors: array<\App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Vendor>} $context Filter context
 * @property-read array{@type: string, name: string, description: string|null} $structured_data Structured data for SEO
 * @property-read array{category: string, action: string, label: string} $analytics Analytics data
 * @property-read array{aria_label: string, role: string} $accessibility Accessibility attributes
 */
class Vendors extends AbstractFilter
{

    /**
     * @var int|null
     */
    protected ?int $_vendor_id = null;

    /**
     * @var array<\App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Vendor>|null
     */
    protected ?array $_vendors = null;

    /**
     * @param array|null $_makeFilters
     * @param mixed $_model_query
     * @param int|null $vendor_id
     */
    public function __construct(?array $_makeFilters = null, $_model_query = null, ?int $vendor_id = null)
    {
        parent::__construct($_makeFilters, $_model_query);
        $this->_vendor_id = $vendor_id;
    }

    /**
     * Get filter items
     * @return array<\App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Vendor>
     */
    protected function items(): array
    {
        if (!is_null($this->_vendors)) {
            return $this->_vendors;
        }

        if ($this->_vendor_id || !$this->_model_query) {
            return [];
        }

        $query = $this->getProductQuery(['vendor'])->select('type__products_vendors.*')->setBindings([], 'select')->distinct()->join('type__products_vendors', function (JoinClause $join): void {
            $join->on('type__products_vendors.id', 'products.vendor_id');
        })->orderBy('type__products_vendors.name', 'asc');

        /** @var \Illuminate\Database\Eloquent\Collection<int, Product> $products */
        $products = $query->get();

        return $this->_vendors = $products->map(function (Product $product): \App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Vendor {
            return new Vendor((new VendorModel())->newFromBuilder($product->getAttributes()));
        })->all();
    }

    /**
     * Get filter handle
     * @return string
     */
    public function handle(): string
    {
        return 'vendors';
    }

    /**
     * Get filter title
     * @return string
     */
    public function title(): string
    {
        return __('item.catalog.filters.title.vendors');
    }

    /**
     * Get filter type
     * @return string
     */
    public function type(): string
    {
        return 'vendors';
    }

    /**
     * Get filter description
     * @return string|null
     */
    public function description(): ?string
    {
        return __('item.catalog.filters.description.vendors');
    }

    /**
     * Get filter sort order
     * @return int
     */
    public function sort_order(): int
    {
        return 2;
    }

    /**
     * Check if filter is required
     * @return bool
     */
    public function is_required(): bool
    {
        return false;
    }

    /**
     * Check if filter allows multiple selections
     * @return bool
     */
    public function allows_multiple(): bool
    {
        return true;
    }

    /**
     * Check if filter is visible
     * @return bool
     */
    public function is_visible(): bool
    {
        return true;
    }

    /**
     * Get active filter values
     * @return array<array{
     *     id: int,
     *     name: string,
     *     value: string,
     *     count: int,
     *     active: bool
     * }>
     */
    public function active_values(): array
    {
        $items = $this->items();
        $active = [];

        foreach ($items as $item) {
            if ($item->active()) {
                $active[] = [
                    'id' => $item->id(),
                    'name' => $item->name(),
                    'value' => (string) $item->id(),
                    'count' => $item->products_count(),
                    'active' => true
                ];
            }
        }

        return $active;
    }

    /**
     * Get total number of filter values
     * @return int
     */
    public function total_values(): int
    {
        return count($this->items());
    }

    /**
     * Get filter usage count
     * @return int
     */
    public function usage_count(): int
    {
        $active = $this->active_values();
        return count($active);
    }

    /**
     * Get filter configuration
     * @return array{
     *     multiple_selection: bool,
     *     display_type: string,
     *     show_counts: bool,
     *     sort_by: string,
     *     sort_order: string
     * }
     */
    public function configuration(): array
    {
        return [
            'multiple_selection' => true,
            'display_type' => 'list',
            'show_counts' => true,
            'sort_by' => 'name',
            'sort_order' => 'asc'
        ];
    }

    /**
     * Get filter context
     * @return array{
     *     display_type: string,
     *     show_counts: bool,
     *     sort_by: string,
     *     sort_order: string,
     *     vendors: array<\App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Vendor>
     * }
     */
    public function context(): array
    {
        return [
            'display_type' => 'list',
            'show_counts' => true,
            'sort_by' => 'name',
            'sort_order' => 'asc',
            'vendors' => $this->items()
        ];
    }

    /**
     * Get structured data for SEO
     * @return array{
     *     @type: string,
     *     name: string,
     *     description: string|null
     * }
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'FilterGroup',
            'name' => $this->title(),
            'description' => $this->description()
        ];
    }

    /**
     * Get analytics data
     * @return array{
     *     category: string,
     *     action: string,
     *     label: string
     * }
     */
    public function analytics(): array
    {
        return [
            'category' => 'Filter',
            'action' => 'Select',
            'label' => $this->title()
        ];
    }

    /**
     * Get accessibility attributes
     * @return array{
     *     aria_label: string,
     *     role: string
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => $this->title(),
            'role' => 'group'
        ];
    }

}
