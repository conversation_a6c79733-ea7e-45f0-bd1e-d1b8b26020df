<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.4.2019 г.
 * Time: 12:02 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType;

use App\Models\Product\ParameterOption;
use App\Models\Product\Product;
use App\Models\Product\ProductsParametersOptionsStat;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection as IlluminateCollection;
use App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Variant as Parameter;

/**
 * Class Variants
 * 
 * Represents a filter for product variants/parameters.
 * This class extends AbstractFilter to provide filtering functionality for variant-based product filtering.
 * 
 * @property-read array<\App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Variant>|null $_variants Cached variant items
 * @property-read string $handle Filter handle for Shopify compatibility
 * @property-read string $title Filter title for Shopify compatibility
 * @property-read string $type Filter type for Shopify compatibility
 * @property-read string|null $description Filter description for Shopify compatibility
 * @property-read int $sort_order Filter sort order for Shopify compatibility
 * @property-read bool $is_required Whether the filter is required
 * @property-read bool $allows_multiple Whether the filter allows multiple selections
 * @property-read bool $is_visible Whether the filter is visible
 * @property-read array<array{id: int, name: string, type: string, options: array<array{id: int, name: string, value: string, count: int, active: bool}>, active: bool}> $active_values Active filter values
 * @property-read int $total_values Total number of filter values
 * @property-read int $usage_count Number of times the filter is used
 * @property-read array{multiple_selection: bool, display_type: string, show_counts: bool, sort_by: string, sort_order: string} $configuration Filter configuration
 * @property-read array{display_type: string, show_counts: bool, sort_by: string, sort_order: string, variants: array<\App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Variant>} $context Filter context
 * @property-read array{@type: string, name: string, description: string|null} $structured_data Structured data for SEO
 * @property-read array{category: string, action: string, label: string} $analytics Analytics data
 * @property-read array{aria_label: string, role: string} $accessibility Accessibility attributes
 */
class Variants extends AbstractFilter
{
    /**
     * @var array<\App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Variant>|null
     */
    protected ?array $_variants = null;

    /**
     * Get filter items
     * @return array<\App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Variant>
     */
    protected function items(): array
    {
        if (!is_null($this->_variants)) {
            return $this->_variants;
        }

        //@todo optimaze this
        $model = $this->getProductQuery()->select('products.id')
            ->setBindings([], 'select');
        if (activeRoute('bundles.list.*')) {
            $model->join('products_bundle', function (JoinClause $join): void {
                $join->on('products_bundle.bundle_id', 'products.id');
            })->join('products AS p2', function (JoinClause $join): void {
                $join->on('products_bundle.product_id', 'p2.id');
            });

            /** @var Product $sql */
            $model->whereNotNull('p2.p1_id')->select('products_parameters_options_stat.*')
                ->join('products_parameters_options_stat', function (JoinClause $join): void {
                    $join->on('products_parameters_options_stat.product_id', 'p2.id');
                })->groupBy('parameter_option_id');
        } else {
            /** @var Product $sql */
            $model->whereNotNull('products.p1_id')->select('products_parameters_options_stat.*')
                ->join('products_parameters_options_stat', function (JoinClause $join): void {
                    $join->on('products_parameters_options_stat.product_id', 'products.id');
                })->groupBy('parameter_option_id');
        }

        $model = ProductsParametersOptionsStat::from(\Illuminate\Support\Facades\DB::raw('(' . $model->toFullSql() . ') AS tmp'))
            ->with(['parameter'])->select('tmp.*');

        foreach (['parameter_id', 'name', 'settings', 'sort', 'max_thumb_size', 'color', 'id', 'image'] as $col) {
            $model->addSelect('products_parameters_options.' . $col . ' AS parameter_option.' . $col);
        }

        $model->join('products_parameters_options', function (JoinClause $join): void {
            $join->on('parameter_option_id', 'products_parameters_options.id');
        });

        /** @var IlluminateCollection<int, ProductsParametersOptionsStat> $records */
        $records = $model->get()->map(function (ProductsParametersOptionsStat $rec) {
            $tmp = [];
            foreach ($rec->getAttributes() as $key => $data) {
                if (substr($key, 0, 17) == 'parameter_option.') {
                    $tmp[substr($key, 17)] = $data;
                    $rec->offsetUnset($key);
                }
            }

            return $rec->syncOriginal()
                ->setRelation('parameter_option', (new ParameterOption)->newFromBuilder($tmp));
        });

        /** @var IlluminateCollection<int, ParameterOption> $options */
        $options = $records->pluck('parameter_option');
        $options = $options->groupBy('parameter_id');

        /** @var IlluminateCollection<int, \App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Variant> $parameters */
        $parameters = $records->unique('parameter_id')->map(function ($option) use ($options): \App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Variant {
            /** @var ProductsParametersOptionsStat $option */
            $parameter = $option->parameter;

            $parameter->setRelation('options', $options->has($parameter->id) ? $options->get($parameter->id) : collect());
            return new Parameter($parameter);
        })->filter()->sortBy('sort', SORT_ASC);

        return $this->_variants = $parameters->values()->all();
    }

    /**
     * Get filter handle
     * @return string
     */
    public function handle(): string
    {
        return 'variants';
    }

    /**
     * Get filter title
     * @return string
     */
    public function title(): string
    {
        return __('item.catalog.filters.title.variants');
    }

    /**
     * Get filter type
     * @return string
     */
    public function type(): string
    {
        return 'variants';
    }

    /**
     * Get filter description
     * @return string|null
     */
    public function description(): ?string
    {
        return __('item.catalog.filters.description.variants');
    }

    /**
     * Get filter sort order
     * @return int
     */
    public function sort_order(): int
    {
        return 3;
    }

    /**
     * Check if filter is required
     * @return bool
     */
    public function is_required(): bool
    {
        return false;
    }

    /**
     * Check if filter allows multiple selections
     * @return bool
     */
    public function allows_multiple(): bool
    {
        return true;
    }

    /**
     * Check if filter is visible
     * @return bool
     */
    public function is_visible(): bool
    {
        return true;
    }

    /**
     * Get active filter values
     * @return array<array{
     *     id: int,
     *     name: string,
     *     type: string,
     *     options: array<array{
     *         id: int,
     *         name: string,
     *         value: string,
     *         count: int,
     *         active: bool
     *     }>,
     *     active: bool
     * }>
     */
    public function active_values(): array
    {
        $items = $this->items();
        $active = [];

        foreach ($items as $item) {
            $active[] = $item->toAnalyticsArray();
        }

        return $active;
    }

    /**
     * Get total number of filter values
     * @return int
     */
    public function total_values(): int
    {
        return count($this->items());
    }

    /**
     * Get filter usage count
     * @return int
     */
    public function usage_count(): int
    {
        $active = $this->active_values();
        return count($active);
    }

    /**
     * Get filter configuration
     * @return array{
     *     multiple_selection: bool,
     *     display_type: string,
     *     show_counts: bool,
     *     sort_by: string,
     *     sort_order: string
     * }
     */
    public function configuration(): array
    {
        return [
            'multiple_selection' => true,
            'display_type' => 'list',
            'show_counts' => true,
            'sort_by' => 'name',
            'sort_order' => 'asc'
        ];
    }

    /**
     * Get filter context
     * @return array{
     *     display_type: string,
     *     show_counts: bool,
     *     sort_by: string,
     *     sort_order: string,
     *     variants: array<\App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Variant>
     * }
     */
    public function context(): array
    {
        return [
            'display_type' => 'list',
            'show_counts' => true,
            'sort_by' => 'name',
            'sort_order' => 'asc',
            'variants' => $this->items()
        ];
    }

    /**
     * Get structured data for SEO
     * @return array{
     *     @type: string,
     *     name: string,
     *     description: string|null
     * }
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'FilterGroup',
            'name' => $this->title(),
            'description' => $this->description()
        ];
    }

    /**
     * Get analytics data
     * @return array{
     *     category: string,
     *     action: string,
     *     label: string
     * }
     */
    public function analytics(): array
    {
        return [
            'category' => 'Filter',
            'action' => 'Select',
            'label' => $this->title()
        ];
    }

    /**
     * Get accessibility attributes
     * @return array{
     *     aria_label: string,
     *     role: string
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => $this->title(),
            'role' => 'group'
        ];
    }
}
