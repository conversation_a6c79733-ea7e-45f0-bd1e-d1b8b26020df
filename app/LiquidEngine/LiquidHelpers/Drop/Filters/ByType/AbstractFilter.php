<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.4.2019 г.
 * Time: 12:05 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType;

use Illuminate\Config\Repository;
use Illuminate\Database\Eloquent\Builder;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

/**
 * Abstract base class for all filter types in the Liquid templating system.
 * Provides core functionality and Shopify compatibility for product filtering.
 *
 * @property-read Builder|null $_model_query The base query builder instance
 * @property-read array<string, mixed> $_makeFilters The filter configuration array
 * @property-read Repository|null $_configuration The configuration repository instance
 * @property-read array<string, mixed> $items The filter items
 * @property-read int $size The number of filter items
 * @property-read bool $allowed Whether the filter is allowed
 * @property-read string $handle The filter handle (URL-friendly identifier)
 * @property-read string $name The filter name
 * @property-read string $title The filter title
 * @property-read string $type The filter type
 * @property-read string|null $description The filter description
 * @property-read int $sort_order The filter sort order
 * @property-read bool $is_required Whether the filter is required
 * @property-read bool $allows_multiple Whether the filter allows multiple selections
 * @property-read bool $is_visible Whether the filter is visible
 * @property-read array<string, mixed> $active_values The active filter values
 * @property-read int $total_values The total number of filter values
 * @property-read int $usage_count The filter usage count
 * @property-read string|null $meta_title The filter meta title
 * @property-read string|null $meta_description The filter meta description
 * @property-read string|null $canonical_url The filter canonical URL
 * @property-read string $robots The filter robots meta tag
 * @property-read array<string, mixed> $structured_data The filter structured data
 * @property-read array<string, mixed> $configuration The filter configuration
 * @property-read array<string, mixed> $context The filter context
 * @property-read array{aria_label: string, role: string} $accessibility The filter accessibility attributes
 */
abstract class AbstractFilter extends AbstractDrop
{
    /**
     * The base query builder instance for the filter.
     *
     * @var Builder|null
     */
    protected ?Builder $_model_query = null;

    /**
     * The filter configuration array.
     *
     * @var array<string, mixed>
     */
    protected array $_makeFilters;

    /**
     * The configuration repository instance.
     *
     * @var Repository|null
     */
    protected ?Repository $_configuration = null;

    /**
     * Create a new filter instance.
     *
     * @param array<string, mixed> $_makeFilters The filter configuration
     * @param Builder $_model_query The base query builder
     */
    public function __construct(array $_makeFilters, Builder $_model_query)
    {
        $this->_model_query = $_model_query->cloneWithout([]);
        $this->_makeFilters = $_makeFilters;
    }

    /**
     * Set the configuration repository for the filter.
     *
     * @param Repository $config The configuration repository
     * @return self
     */
    public function setConfig(Repository $config): self
    {
        $this->_configuration = $config;
        return $this;
    }

    /**
     * Get the filter items.
     * This method must be implemented by concrete filter classes.
     *
     * @return array<string, mixed>
     */
    abstract protected function items(): array;

    /**
     * Get the number of filter items.
     *
     * @return int
     */
    public function size(): int
    {
        return count($this->items());
    }

    /**
     * Check if the filter is allowed to be used.
     *
     * @return bool
     */
    public function allowed(): bool
    {
        return true;
    }

    /**
     * Get accessibility attributes
     * @return array{
     *     aria_label: string,
     *     role: string
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => $this->title(),
            'role' => 'filter'
        ];
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     handle: string,
     *     title: string,
     *     type: string,
     *     description: string|null,
     *     sort_order: int,
     *     is_required: bool,
     *     allows_multiple: bool,
     *     is_visible: bool,
     *     active_values: array<string, mixed>,
     *     total_values: int,
     *     usage_count: int,
     *     meta_title: string|null,
     *     meta_description: string|null,
     *     canonical_url: string|null,
     *     robots: string,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toArray(): array
    {
        return array_merge($this->items(), [
            'handle' => $this->handle(),
            'title' => $this->title(),
            'type' => $this->type(),
            'description' => $this->description(),
            'sort_order' => $this->sort_order(),
            'is_required' => $this->is_required(),
            'allows_multiple' => $this->allows_multiple(),
            'is_visible' => $this->is_visible(),
            'active_values' => $this->active_values(),
            'total_values' => $this->total_values(),
            'usage_count' => $this->usage_count(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'canonical_url' => $this->canonical_url(),
            'robots' => $this->robots(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ]);
    }

    /**
     * Get the filter handle (URL-friendly identifier).
     * Shopify compatibility method.
     *
     * @return string
     */
    public function handle(): string
    {
        return strtolower(str_replace(' ', '-', $this->title()));
    }

    /**
     * Get the filter name.
     * Base method for filter identification.
     *
     * @return string
     */
    public function name(): string
    {
        return '';
    }

    /**
     * Get the filter title/name.
     * Shopify compatibility method.
     *
     * @return string
     */
    public function title(): string
    {
        return $this->name();
    }

    /**
     * Get the filter type.
     * Shopify compatibility method.
     *
     * @return string
     */
    public function type(): string
    {
        return 'filter';
    }

    /**
     * Get the filter description.
     * Shopify compatibility method.
     *
     * @return string|null
     */
    public function description(): ?string
    {
        return null;
    }

    /**
     * Get the filter sort order.
     * Shopify compatibility method.
     *
     * @return int
     */
    public function sort_order(): int
    {
        return 0;
    }

    /**
     * Check if the filter is required.
     * Shopify compatibility method.
     *
     * @return bool
     */
    public function is_required(): bool
    {
        return false;
    }

    /**
     * Check if the filter allows multiple selections.
     * Shopify compatibility method.
     *
     * @return bool
     */
    public function allows_multiple(): bool
    {
        return false;
    }

    /**
     * Check if the filter is visible.
     * Shopify compatibility method.
     *
     * @return bool
     */
    public function is_visible(): bool
    {
        return true;
    }

    /**
     * Get active filter values.
     * Shopify compatibility method.
     *
     * @return array<string, mixed>
     */
    public function active_values(): array
    {
        return [];
    }

    /**
     * Get total number of filter values.
     * Shopify compatibility method.
     *
     * @return int
     */
    public function total_values(): int
    {
        return count($this->items());
    }

    /**
     * Get filter usage count.
     * Analytics method.
     *
     * @return int
     */
    public function usage_count(): int
    {
        return 0;
    }

    /**
     * Get the filter meta title.
     * SEO method.
     *
     * @return string|null
     */
    public function meta_title(): ?string
    {
        return null;
    }

    /**
     * Get the filter meta description.
     * SEO method.
     *
     * @return string|null
     */
    public function meta_description(): ?string
    {
        return null;
    }

    /**
     * Get the filter canonical URL.
     * SEO method.
     *
     * @return string|null
     */
    public function canonical_url(): ?string
    {
        return null;
    }

    /**
     * Get the filter robots meta tag.
     * SEO method.
     *
     * @return string
     */
    public function robots(): string
    {
        return 'index, follow';
    }

    /**
     * Get the filter structured data.
     * SEO method.
     *
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [];
    }

    /**
     * Get the filter configuration.
     *
     * @return array<string, mixed>
     */
    public function configuration(): array
    {
        return $this->_makeFilters;
    }

    /**
     * Get the filter context.
     *
     * @return array<string, mixed>
     */
    public function context(): array
    {
        return [
            'filter' => $this->toArray(),
            'configuration' => $this->configuration(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Get the product query builder.
     *
     * @param array<string, mixed> $without The relations to exclude
     * @return Builder
     */
    protected function getProductQuery(array $without = []): Builder
    {
        return $this->_model_query->cloneWithout($without);
    }
}
