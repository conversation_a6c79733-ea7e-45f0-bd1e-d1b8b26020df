<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.4.2019 г.
 * Time: 12:02 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType;

use App\Exceptions\Error;
use App\Helper\Format;
use App\Helper\Validate;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;

class PriceRanges extends AbstractFilter
{
    /**
     * The price range data.
     *
     * @var array<string, mixed>|null
     */
    protected ?array $_price_range = null;

    /**
     * The price range settings.
     *
     * @var array<string, mixed>|null
     */
    protected ?array $_price_range_setting = null;

    /**
     * The filter settings.
     *
     * @var array<string, mixed>
     */
    protected array $_settings = [];

    /**
     * The price range query parameter keys.
     *
     * @var array<string>
     */
    protected static array $_price_range_keys = [
        'price_from', 'price_to'
    ];

    /**
     * {@inheritDoc}
     * @return array<string, mixed>
     */
    protected function items(): array
    {
        if (!is_null($this->_price_range)) {
            return $this->_price_range;
        }

        return $this->_price_range = array_merge($this->_getPriceSliderSettings(), ['currency' => PriceParts::format(0)['currency']]);
    }

    /**
     * Get the price slider settings.
     *
     * @return array<string, mixed>
     */
    protected function _getPriceSliderSettings(): array
    {
        if (!is_null($this->_price_range_setting)) {
            return $this->_price_range_setting;
        }

        $model = $this->getProductQuery(['price_to', 'price_from', 'price_range']);

        $model_min = clone $model;
        $first_min = $model_min->setBindings([], 'select')->getMinPrice()->first()->min ?? 0;
        $result_min = Format::toIntegerPrice(floor(Format::moneyInput($first_min)));
        $model_max = clone $model;
        $first_max = $model_max->setBindings([], 'select')->getMaxPrice()->first()->max ?? 0;
        $result_max = Format::toIntegerPrice(ceil(Format::moneyInput($first_max)));

        if ($first_min === $first_max) {
            return $this->_price_range_setting = [
                'min' => 0,
                'min_formatted' => Format::money(0),
                'min_parts' => PriceParts::format(0),
                'max' => 0,
                'max_formatted' => Format::money(0),
                'max_parts' => PriceParts::format(0),
                'step' => 0,
                'step_formatted' => Format::money(0),
                'step_parts' => PriceParts::format(0),
                'value_from' => 0,
                'value_from_formatted' => Format::money(0),
                'value_from_parts' => PriceParts::format(0),
                'value_to' => 0,
                'value_to_formatted' => Format::money(0),
                'value_to_parts' => PriceParts::format(0),
                'active' => false,
                'active_values' => null,
            ];
        }

        $step = 1;
        if (!empty($this->_settings['price_range_step'])) {
            $step = Format::moneyInput($this->_settings['price_range_step']);
        }

        $active = static::parseQueryPriceRange();

        return $this->_price_range_setting = [
            'min' => $value_from = Format::moneyInput($result_min),
            'min_formatted' => Format::money($result_min),
            'min_parts' => PriceParts::format($value_from),
            'max' => $value_to = Format::moneyInput($result_max),
            'max_formatted' => Format::money($result_max),
            'max_parts' => PriceParts::format($result_max),
            'step' => $step = number_format($step, 2, '.', ''),
            'step_formatted' => Format::money(Format::toIntegerPrice($step)),
            'step_parts' => PriceParts::format($step),
            'value_from' => $active_value_from = (isset($active['price_from']) && $active['price_from'] < $value_to && $value_to >= $value_from ? max($value_from, $active['price_from']) : $value_from),
            'value_from_formatted' => Format::money(Format::toIntegerPrice($active_value_from)),
            'value_from_parts' => PriceParts::format(number_format($active_value_from, 2, '.', '')),
            'value_to' => $active_value_to = (isset($active['price_to']) && $active['price_to'] >= $value_from && $value_to >= $value_from ? min($value_to, $active['price_to']) : $value_to),
            'value_to_formatted' => Format::money(Format::toIntegerPrice($active_value_to)),
            'value_to_parts' => PriceParts::format(number_format($active_value_to, 2, '.', '')),
            'active' => true,
            'active_values' => $active,
        ];
    }

    /**
     * Parse the price range from the query parameters.
     *
     * @return array<string, float>
     */
    public static function parseQueryPriceRange(): array
    {
        $ranges = [];
        foreach (self::$_price_range_keys as $key) {
            if (is_null($price = request()->query($key))) {
                continue;
            }

            try {
                $ranges[$key] = Format::moneyInput(Validate::currencyAmount($price, 10));
            } catch (Error $e) {
            }
        }

        return $ranges;
    }

    /**
     * Get the filter name
     * @return string
     */
    public function name(): string
    {
        return __('item.catalog.filters.text.price_range');
    }

    /**
     * Get the filter type
     * @return string
     */
    public function type(): string
    {
        return 'price_range';
    }

    /**
     * Get the filter description
     * @return string|null
     */
    public function description(): ?string
    {
        return __('item.catalog.filters.description.price_range');
    }

    /**
     * Get active filter values
     * @return array{
     *     min: float,
     *     max: float,
     *     currency: string,
     *     formatted: string
     * }
     */
    public function active_values(): array
    {
        $active = static::parseQueryPriceRange();
        if (empty($active)) {
            return [];
        }

        $items = $this->items();
        return [
            'min' => $active['price_from'] ?? $items['min'],
            'max' => $active['price_to'] ?? $items['max'],
            'currency' => $items['currency'],
            'formatted' => sprintf(
                '%s - %s',
                Format::money($active['price_from'] ?? $items['min']),
                Format::money($active['price_to'] ?? $items['max'])
            )
        ];
    }

    /**
     * Get filter usage count
     * @return int
     */
    public function usage_count(): int
    {
        $active = static::parseQueryPriceRange();
        if (empty($active)) {
            return 0;
        }

        $model = $this->getProductQuery(['price_to', 'price_from']);
        return $model->where(function($query) use ($active) {
            if (isset($active['price_from'])) {
                $query->where('price', '>=', $active['price_from']);
            }
            if (isset($active['price_to'])) {
                $query->where('price', '<=', $active['price_to']);
            }
        })->count();
    }

    /**
     * Get the SEO meta title
     * @return string
     */
    public function meta_title(): string
    {
        $active = static::parseQueryPriceRange();
        if (empty($active)) {
            return $this->name();
        }

        $items = $this->items();
        return sprintf(
            __('item.catalog.filters.meta_title.price_range'),
            Format::money($active['price_from'] ?? $items['min']),
            Format::money($active['price_to'] ?? $items['max'])
        );
    }

    /**
     * Get the SEO meta description
     * @return string
     */
    public function meta_description(): string
    {
        $active = static::parseQueryPriceRange();
        if (empty($active)) {
            return $this->description() ?? '';
        }

        $items = $this->items();
        return sprintf(
            __('item.catalog.filters.meta_description.price_range'),
            Format::money($active['price_from'] ?? $items['min']),
            Format::money($active['price_to'] ?? $items['max'])
        );
    }

    /**
     * Get the canonical URL
     * @return string|null
     */
    public function canonical_url(): ?string
    {
        $active = static::parseQueryPriceRange();
        if (empty($active)) {
            return null;
        }

        $query = [];
        if (isset($active['price_from'])) {
            $query['price_from'] = $active['price_from'];
        }
        if (isset($active['price_to'])) {
            $query['price_to'] = $active['price_to'];
        }

        return request()->url() . '?' . http_build_query($query);
    }

    /**
     * Get structured data for SEO
     * @return array{
     *     context: string,
     *     type: string,
     *     itemListElement: array<array{
     *         type: string,
     *         position: int,
     *         name: string,
     *         url: string,
     *         item: array{
     *             type: string,
     *             name: string,
     *             description: string,
     *             url: string
     *         }
     *     }>
     * }
     */
    public function structured_data(): array
    {
        $active = static::parseQueryPriceRange();
        if (empty($active)) {
            return [
                'context' => 'https://schema.org',
                'type' => 'ItemList',
                'itemListElement' => []
            ];
        }

        $items = $this->items();
        $min = $active['price_from'] ?? $items['min'];
        $max = $active['price_to'] ?? $items['max'];

        return [
            'context' => 'https://schema.org',
            'type' => 'ItemList',
            'itemListElement' => [
                [
                    'type' => 'ListItem',
                    'position' => 1,
                    'name' => sprintf(
                        __('item.catalog.filters.structured_data.price_range'),
                        Format::money($min),
                        Format::money($max)
                    ),
                    'url' => $this->canonical_url(),
                    'item' => [
                        'type' => 'PriceRange',
                        'name' => sprintf(
                            __('item.catalog.filters.structured_data.price_range'),
                            Format::money($min),
                            Format::money($max)
                        ),
                        'description' => sprintf(
                            __('item.catalog.filters.structured_data.price_range_description'),
                            Format::money($min),
                            Format::money($max)
                        ),
                        'url' => $this->canonical_url()
                    ]
                ]
            ]
        ];
    }

    /**
     * Check if the filter allows multiple selections
     * @return bool
     */
    public function allows_multiple(): bool
    {
        return false;
    }

    /**
     * Get filter configuration
     * @return array{
     *     multiple_selection: bool,
     *     display_type: string,
     *     show_counts: bool,
     *     min_price: float,
     *     max_price: float,
     *     step: float,
     *     currency: array{
     *         code: string,
     *         symbol: string,
     *         position: string
     *     }
     * }
     */
    public function configuration(): array
    {
        $items = $this->items();
        return [
            'multiple_selection' => false,
            'display_type' => 'slider',
            'show_counts' => false,
            'min_price' => $items['min'],
            'max_price' => $items['max'],
            'step' => $items['step'],
            'currency' => $items['currency']
        ];
    }

    /**
     * Get filter context
     * @return array{
     *     display_type: string,
     *     show_counts: bool,
     *     min_price: float,
     *     max_price: float,
     *     step: float,
     *     currency: array{
     *         code: string,
     *         symbol: string,
     *         position: string
     *     },
     *     active: array{
     *         min: float,
     *         max: float,
     *         currency: string,
     *         formatted: string
     *     }|null
     * }
     */
    public function context(): array
    {
        $items = $this->items();
        $active = $this->active_values();

        return [
            'display_type' => 'slider',
            'show_counts' => false,
            'min_price' => $items['min'],
            'max_price' => $items['max'],
            'step' => $items['step'],
            'currency' => $items['currency'],
            'active' => !empty($active) ? $active : null
        ];
    }

}
