<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\BrandModel;

use Modules\BrandModel\Models\Model as BrandModelModel;
use App\LiquidEngine\LiquidHelpers\Drop\BrandModel\Brand as BaseBrand;

class Brand extends BaseBrand
{

    protected $_models;

    public function models()
    {
        if (is_null($this->_models)) {
            $this->_models = [];
            if ($this->_brand->relationLoaded('models')) {
                $this->_models = $this->_brand->models->map(function (BrandModelModel $model): \App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\BrandModel\Model {
                    return new Model($model);
                })->values()->all();
            }
        }

        return $this->_models;
    }

    /**
     * @return bool
     */
    public function active(): bool
    {
        return request()->query('brand') == $this->url_handle();
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'url_handle' => $this->url_handle(),
            'url' => $this->url(),
            'models' => $this->models(),
            'active' => $this->active(),
        ];
    }

}
