<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper;

use App\Models\Product\ParameterOption as ParameterOptionModel;
use App\LiquidEngine\LiquidHelpers\Drop\Variants\Variant as BaseVariant;

/**
 * Class Variant
 * 
 * Represents a variant in the filter system.
 * This class extends BaseVariant to provide filtering functionality for variants.
 * 
 * @property-read int $id Variant ID
 * @property-read string $name Variant name
 * @property-read string $type Variant type
 * @property-read array<VariantOption> $options Variant options
 * @property-read bool $active Whether the variant is active
 * @property-read array{id: int, name: string, type: string, options: array} $shopify Shopify-compatible variant data
 * @property-read array{id: int, name: string, type: string, options: array} $seo SEO-compatible variant data
 * @property-read array{id: int, name: string, type: string, options: array, active: bool} $analytics Analytics-compatible variant data
 * @property-read array{aria_label: string, role: string} $accessibility Accessibility attributes
 */
class Variant extends BaseVariant
{
    /**
     * Get variant options
     * @return array<VariantOption>
     */
    public function options(): array
    {
        return $this->_variant->relationLoaded('options') 
            ? $this->_variant->options
                ->sortBy('sort', SORT_ASC)
                ->map(function (ParameterOptionModel $option): VariantOption {
                    $option->setRelation('parameter', $this->_variant);
                    return new VariantOption($option);
                })
                ->values()
                ->all() 
            : [];
    }

    /**
     * Check if the variant is active
     * @return bool
     */
    public function active(): bool
    {
        if (($variant = explode(',', request()->input('variant.' . $this->_variant->id))) && ($variant = array_filter($variant))) {
            if ($this->_variant->relationLoaded('options') && $this->_variant->options->whereIn('id', $variant)->isNotEmpty()) {
                return true;
            }

            return !($variant === []);
        }

        return false;
    }

    /**
     * Convert to Shopify option format
     * @return array{
     *     id: int,
     *     name: string,
     *     type: string,
     *     options: array
     * }
     */
    public function toShopifyOption(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'type' => $this->type(),
            'options' => array_map(
                fn($option) => method_exists($option, 'toShopifyOption') ? $option->toShopifyOption() : $option, 
                $this->options()
            ),
        ];
    }

    /**
     * Convert all options to Shopify format
     * @return array
     */
    public function toShopifyOptionsArray(): array
    {
        return array_map(
            fn($option) => method_exists($option, 'toShopifyOption') ? $option->toShopifyOption() : $option, 
            $this->options()
        );
    }

    /**
     * Convert to SEO array format
     * @return array{
     *     id: int,
     *     name: string,
     *     type: string,
     *     options: array
     * }
     */
    public function toSeoArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'type' => $this->type(),
            'options' => array_map(
                fn($option) => method_exists($option, 'toSeoArray') ? $option->toSeoArray() : $option, 
                $this->options()
            ),
        ];
    }

    /**
     * Convert to analytics array format
     * @return array{
     *     id: int,
     *     name: string,
     *     type: string,
     *     options: array,
     *     active: bool
     * }
     */
    public function toAnalyticsArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'type' => $this->type(),
            'options' => array_map(
                fn($option) => method_exists($option, 'toAnalyticsArray') ? $option->toAnalyticsArray() : $option, 
                $this->options()
            ),
            'active' => $this->active(),
        ];
    }

    /**
     * Get accessibility attributes
     * @return array{
     *     aria_label: string,
     *     role: string
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => $this->name(),
            'role' => 'group'
        ];
    }

    /**
     * {@inheritDoc}
     * @return array{
     *     id: int,
     *     name: string,
     *     type: string,
     *     options: array<VariantOption>,
     *     active: bool,
     *     shopify: array{id: int, name: string, type: string, options: array},
     *     seo: array{id: int, name: string, type: string, options: array},
     *     analytics: array{id: int, name: string, type: string, options: array, active: bool},
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'type' => $this->type(),
            'options' => $this->options(),
            'active' => $this->active(),
            'shopify' => $this->toShopifyOption(),
            'seo' => $this->toSeoArray(),
            'analytics' => $this->toAnalyticsArray(),
            'accessibility' => $this->accessibility(),
        ];
    }
}
