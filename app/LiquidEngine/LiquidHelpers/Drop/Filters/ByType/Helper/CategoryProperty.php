<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper;

use App\Exceptions\Error;
use App\Models\Category\PropertyOption as CategoryPropertyOptionModel;
use App\LiquidEngine\LiquidHelpers\Drop\Category\Property;

class CategoryProperty extends Property
{

    public function options(): array
    {
        return $this->_property->relationLoaded('options') && $this->type() != 'range' ? $this->_property->options->sortBy('sort', SORT_ASC)->map(function (CategoryPropertyOptionModel $option): \App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\CategoryPropertyOption {
            $option->setRelation('property', $this->_property);
            return new CategoryPropertyOption($option);
        })->values()->all() : [];
    }

    public function values(): ?array
    {
        if ($this->type() != 'range') {
            return null;
        }

        if ($values = $this->active_value()) {
            return explode(':', $values);
        }

        return parent::values();
    }

    public function slider_values()
    {
        if ($this->type() != 'range' || !$this->_property->relationLoaded('options')) {
            return [];
        }

        return $this->_property->options->pluck('value')->map(function ($value): string {
            return number_format($value, $this->decimal_points(), '.', '');
        })->unique()->sort()->values()->all();
    }

    public function active(): bool
    {
        return !empty(request()->input('property.' . $this->url_handle())) && $this->active_value();
    }

    public function active_value(): ?string
    {
        if (empty(request()->input('property.' . $this->url_handle())) || (($property_url_values = request()->input('property.' . $this->url_handle())) == sprintf('%s:%s', $this->min(), $this->max())) || !$property_url_values) {
            return null;
        }

        $parts = explode(':', $property_url_values);
        if (count($parts) == 1) {
            $parts[] = \Illuminate\Support\Arr::first($parts);
        }

        return implode(':', $parts);
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     * @throws Error
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'type' => $this->type(),
            'decimal_points' => $this->decimal_points(),
            'url_handle' => $this->url_handle(),
            'options' => $this->options(),
            //range
            'step' => $this->step(),
            'min' => $this->min(),
            'max' => $this->max(),
            'values' => $this->values(),
            'value_from' => $this->value_from(),
            'value_to' => $this->value_to(),
            'active' => $this->active(),
            'active_value' => $this->active_value(),
            'slider_values' => $this->slider_values(),
        ];
    }

}
