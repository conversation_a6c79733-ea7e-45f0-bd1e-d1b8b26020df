<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper;

use App\LiquidEngine\LiquidHelpers\Drop\Category\Category as BaseCategory;

/**
 * Class Category
 * 
 * Represents a category in the filter system.
 * This class extends BaseCategory to provide filtering functionality for categories.
 * 
 * @property-read int $id Category ID
 * @property-read string $name Category name
 * @property-read string $url_handle Category URL handle
 * @property-read string $url Category URL
 * @property-read array<Category> $dropdown Child categories
 * @property-read bool $active Whether the category is active
 * @property-read array{id: int, name: string, url_handle: string, url: string, dropdown: array<Category>, active: bool} $shopify Shopify-compatible category data
 * @property-read array{id: int, name: string, url_handle: string, url: string, dropdown: array<Category>, active: bool} $seo SEO-compatible category data
 * @property-read array{id: int, name: string, url_handle: string, url: string, dropdown: array<Category>, active: bool} $analytics Analytics-compatible category data
 * @property-read array{aria_label: string, role: string} $accessibility Accessibility attributes
 */
class Category extends BaseCategory
{
    /**
     * Check if the category is active
     * @return bool
     */
    public function active(): bool
    {
        if (activeRoute('category.view bundles.list.category') && routeParameter('slug') == $this->url_handle()) {
            return true;
        }

        if (($categories = explode(',', request()->query('category'))) && ($categories = array_filter($categories))) {
            return in_array($this->url_handle(), $categories);
        }

        return !(array_filter($this->dropdown(), function (self $category) {
            return $category->active();
        }) === []);
    }

    /**
     * Convert to Shopify format
     * @return array{
     *     id: int,
     *     name: string,
     *     url_handle: string,
     *     url: string,
     *     dropdown: array<Category>,
     *     active: bool
     * }
     */
    public function toShopifyArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'url_handle' => $this->url_handle(),
            'url' => $this->url(),
            'dropdown' => $this->dropdown(),
            'active' => $this->active()
        ];
    }

    /**
     * Convert to SEO array format
     * @return array{
     *     id: int,
     *     name: string,
     *     url_handle: string,
     *     url: string,
     *     dropdown: array<Category>,
     *     active: bool
     * }
     */
    public function toSeoArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'url_handle' => $this->url_handle(),
            'url' => $this->url(),
            'dropdown' => $this->dropdown(),
            'active' => $this->active()
        ];
    }

    /**
     * Convert to analytics array format
     * @return array{
     *     id: int,
     *     name: string,
     *     url_handle: string,
     *     url: string,
     *     dropdown: array<Category>,
     *     active: bool
     * }
     */
    public function toAnalyticsArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'url_handle' => $this->url_handle(),
            'url' => $this->url(),
            'dropdown' => $this->dropdown(),
            'active' => $this->active()
        ];
    }

    /**
     * Get accessibility attributes
     * @return array{
     *     aria_label: string,
     *     role: string
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => $this->name(),
            'role' => 'treeitem'
        ];
    }

    /**
     * {@inheritDoc}
     * @return array{
     *     id: int,
     *     name: string,
     *     url_handle: string,
     *     url: string,
     *     dropdown: array<Category>,
     *     active: bool,
     *     shopify: array{id: int, name: string, url_handle: string, url: string, dropdown: array<Category>, active: bool},
     *     seo: array{id: int, name: string, url_handle: string, url: string, dropdown: array<Category>, active: bool},
     *     analytics: array{id: int, name: string, url_handle: string, url: string, dropdown: array<Category>, active: bool},
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'url_handle' => $this->url_handle(),
            'url' => $this->url(),
            'dropdown' => $this->dropdown(),
            'active' => $this->active(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics' => $this->toAnalyticsArray(),
            'accessibility' => $this->accessibility(),
        ];
    }
}
