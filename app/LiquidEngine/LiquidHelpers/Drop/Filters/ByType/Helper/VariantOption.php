<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper;

use App\LiquidEngine\LiquidHelpers\Drop\Variants\Option;

/**
 * Class VariantOption
 * 
 * Represents a variant option in the filter system.
 * This class extends Option to provide filtering functionality for variant options.
 * 
 * @property-read int $id Option ID
 * @property-read string $name Option name
 * @property-read string|null $color Option color
 * @property-read bool $has_image Whether the option has an image
 * @property-read array{url: string, alt: string}|null $image Option image data
 * @property-read bool $active Whether the option is active
 * @property-read array{id: int, name: string, color: string|null, has_image: bool, image: array{url: string, alt: string}|null, active: bool} $shopify Shopify-compatible option data
 * @property-read array{id: int, name: string, color: string|null, has_image: bool, image: array{url: string, alt: string}|null, active: bool} $seo SEO-compatible option data
 * @property-read array{id: int, name: string, color: string|null, has_image: bool, image: array{url: string, alt: string}|null, active: bool} $analytics Analytics-compatible option data
 * @property-read array{aria_label: string, role: string} $accessibility Accessibility attributes
 */
class VariantOption extends Option
{
    /**
     * Check if the option is active
     * @return bool
     */
    public function active(): bool
    {
        if (($variant = explode(',', request()->input('variant.' . $this->_option->parameter_id))) && ($variant = array_filter($variant))) {
            return in_array($this->id(), $variant);
        }

        return false;
    }

    /**
     * Convert to Shopify option format
     * @return array{
     *     id: int,
     *     name: string,
     *     color: string|null,
     *     has_image: bool,
     *     image: array{url: string, alt: string}|null,
     *     active: bool
     * }
     */
    public function toShopifyOption(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'color' => $this->color(),
            'has_image' => $this->_option->parameter->type === 'image' ? $this->has_image() : false,
            'image' => $this->_option->parameter->type === 'image' ? $this->getImageArray() : null,
            'active' => $this->active(),
        ];
    }

    /**
     * Convert to SEO array format
     * @return array{
     *     id: int,
     *     name: string,
     *     color: string|null,
     *     has_image: bool,
     *     image: array{url: string, alt: string}|null,
     *     active: bool
     * }
     */
    public function toSeoArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'color' => $this->color(),
            'has_image' => $this->_option->parameter->type === 'image' ? $this->has_image() : false,
            'image' => $this->_option->parameter->type === 'image' ? $this->getImageArray() : null,
            'active' => $this->active(),
        ];
    }

    /**
     * Convert to analytics array format
     * @return array{
     *     id: int,
     *     name: string,
     *     color: string|null,
     *     has_image: bool,
     *     image: array{url: string, alt: string}|null,
     *     active: bool
     * }
     */
    public function toAnalyticsArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'color' => $this->color(),
            'has_image' => $this->_option->parameter->type === 'image' ? $this->has_image() : false,
            'image' => $this->_option->parameter->type === 'image' ? $this->getImageArray() : null,
            'active' => $this->active(),
        ];
    }

    /**
     * Get accessibility attributes
     * @return array{
     *     aria_label: string,
     *     role: string
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => $this->name(),
            'role' => 'option'
        ];
    }

    /**
     * {@inheritDoc}
     * @return array{
     *     id: int,
     *     name: string,
     *     color: string|null,
     *     has_image: bool,
     *     image: array{url: string, alt: string}|null,
     *     active: bool,
     *     shopify: array{id: int, name: string, color: string|null, has_image: bool, image: array{url: string, alt: string}|null, active: bool},
     *     seo: array{id: int, name: string, color: string|null, has_image: bool, image: array{url: string, alt: string}|null, active: bool},
     *     analytics: array{id: int, name: string, color: string|null, has_image: bool, image: array{url: string, alt: string}|null, active: bool},
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'color' => $this->color(),
            'has_image' => $this->_option->parameter->type === 'image' ? $this->has_image() : false,
            'image' => $this->_option->parameter->type === 'image' ? $this->getImageArray() : null,
            'active' => $this->active(),
            'shopify' => $this->toShopifyOption(),
            'seo' => $this->toSeoArray(),
            'analytics' => $this->toAnalyticsArray(),
            'accessibility' => $this->accessibility(),
        ];
    }
}
