<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.4.2019 г.
 * Time: 12:02 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType;

use App\Models\Category\Property;
use App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\CategoryProperty;
use App\Models\Product\Category;

class CategoryProperties extends AbstractFilter
{
    /**
     * The category ID for filtering properties.
     *
     * @var int|null
     */
    protected ?int $_category_id = null;

    /**
     * The category properties filter data.
     *
     * @var array<string, mixed>|null
     */
    protected ?array $_category_properties = null;

    /**
     * Create a new category properties filter instance.
     *
     * @param array<string, mixed> $_makeFilters The filter configuration
     * @param \Illuminate\Database\Eloquent\Builder $_model_query The base query builder
     * @param int|null $category_id The category ID to filter properties for
     */
    public function __construct(array $_makeFilters, $_model_query, ?int $category_id = null)
    {
        parent::__construct($_makeFilters, $_model_query);
        $this->_category_id = $category_id;
    }

    /**
     * {@inheritdoc}
     * @return array<string, mixed>
     */
    protected function items(): array
    {
        if (!$this->_category_id) {
            return [];
        }

        if (!is_null($this->_category_properties)) {
            return $this->_category_properties;
        }

        /** @var \App\Models\Product\Category $category */
        $category = Category::find($this->_category_id);
        if (!$category) {
            return [];
        }

        /** @var \Illuminate\Database\Eloquent\Collection<int, Property> $properties */
        $properties = Property::getForFilter(
            $category,
            $this->getProductQuery(['category_properties'])
        );

        return $this->_category_properties = $properties->map(function (Property $property): CategoryProperty {
            return new CategoryProperty($property);
        })->all();
    }

    /**
     * Get the filter name
     * @return string
     */
    public function name(): string
    {
        return __('item.catalog.filters.text.category_properties');
    }

    /**
     * Get the filter type
     * @return string
     */
    public function type(): string
    {
        return 'category_property';
    }

    /**
     * Get the filter description
     * @return string|null
     */
    public function description(): ?string
    {
        return __('item.catalog.filters.description.category_properties');
    }

    /**
     * Get active filter values
     * @return array<string, array{
     *     id: int,
     *     name: string,
     *     type: string,
     *     handle: string,
     *     active_value: mixed,
     *     values: array,
     *     options: array<array{
     *         id: int,
     *         value: string,
     *         description: string|null,
     *         url_handle: string,
     *         active: bool
     *     }>
     * }>
     */
    public function active_values(): array
    {
        $activeValues = [];
        foreach ($this->items() as $property) {
            if ($property->active()) {
                $activeValues[] = [
                    'id' => $property->id(),
                    'name' => $property->name(),
                    'type' => $property->type(),
                    'handle' => $property->url_handle(),
                    'active_value' => $property->active_value(),
                    'values' => $property->values(),
                    'options' => array_map(function($option) {
                        return [
                            'id' => $option->id(),
                            'value' => $option->value(),
                            'description' => $option->description(),
                            'url_handle' => $option->url_handle(),
                            'active' => $option->active()
                        ];
                    }, $property->options())
                ];
            }
        }
        return $activeValues;
    }

    /**
     * Get filter usage count
     * @return int
     */
    public function usage_count(): int
    {
        $count = 0;
        foreach ($this->items() as $property) {
            foreach ($property->options() as $option) {
                $count += $option->total_products();
            }
        }
        return $count;
    }

    /**
     * Get the SEO meta title
     * @return string
     */
    public function meta_title(): string
    {
        $activeValues = $this->active_values();
        if (empty($activeValues)) {
            return '';
        }

        $titles = [];
        foreach ($activeValues as $property) {
            $titles[] = $property['name'] . ': ' . implode(', ', array_map(function($option) {
                return $option['value'];
            }, $property['options']));
        }

        return implode(' | ', $titles);
    }

    /**
     * Get the SEO meta description
     * @return string
     */
    public function meta_description(): string
    {
        $activeValues = $this->active_values();
        if (empty($activeValues)) {
            return '';
        }

        $descriptions = [];
        foreach ($activeValues as $property) {
            $descriptions[] = $property['name'] . ': ' . implode(', ', array_map(function($option) {
                return $option['value'];
            }, $property['options']));
        }

        return implode('. ', $descriptions);
    }

    /**
     * Get the canonical URL
     * @return string|null
     */
    public function canonical_url(): ?string
    {
        $activeValues = $this->active_values();
        if (empty($activeValues)) {
            return null;
        }

        $lastProperty = end($activeValues);
        return $lastProperty['url_handle'] ?? null;
    }

    /**
     * Get structured data for SEO
     * @return array{
     *     '@context': string,
     *     '@type': string,
     *     'itemListElement': array<array{
     *         '@type': string,
     *         'position': int,
     *         'name': string,
     *         'url': string,
     *         'item': array{
     *             '@type': string,
     *             'name': string,
     *             'description': string|null,
     *             'url': string
     *         }
     *     }>
     * }
     */
    public function structured_data(): array
    {
        $activeValues = $this->active_values();
        if (empty($activeValues)) {
            return [
                '@context' => 'https://schema.org',
                '@type' => 'ItemList',
                'itemListElement' => []
            ];
        }

        $items = [];
        $position = 1;

        foreach ($activeValues as $property) {
            foreach ($property['options'] as $option) {
                $items[] = [
                    '@type' => 'ListItem',
                    'position' => $position++,
                    'name' => $property['name'] . ': ' . $option['value'],
                    'url' => $option['url_handle'],
                    'item' => [
                        '@type' => 'PropertyValue',
                        'name' => $option['value'],
                        'description' => $option['description'],
                        'url' => $option['url_handle']
                    ]
                ];
            }
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'ItemList',
            'itemListElement' => $items
        ];
    }

    /**
     * Check if multiple selection is allowed
     * @return bool
     */
    public function allows_multiple(): bool
    {
        return true;
    }

    /**
     * Get the filter configuration
     * @return array{
     *     multiple_selection: bool,
     *     display_type: string,
     *     show_counts: bool,
     *     max_depth: int,
     *     sort_by: string,
     *     sort_order: string
     * }
     */
    public function configuration(): array
    {
        return [
            'multiple_selection' => $this->allows_multiple(),
            'display_type' => 'checkbox',
            'show_counts' => true,
            'max_depth' => 1,
            'sort_by' => 'name',
            'sort_order' => 'asc'
        ];
    }

    /**
     * Get the filter context
     * @return array{
     *     display_type: string,
     *     show_counts: bool,
     *     max_depth: int,
     *     sort_by: string,
     *     sort_order: string,
     *     properties: array<array{
     *         id: int,
     *         name: string,
     *         type: string,
     *         handle: string,
     *         active_value: mixed,
     *         values: array,
     *         options: array<array{
     *             id: int,
     *             value: string,
     *             description: string|null,
     *             url_handle: string,
     *             active: bool
     *         }>
     *     }>
     * }
     */
    public function context(): array
    {
        return [
            'display_type' => $this->configuration()['display_type'],
            'show_counts' => $this->configuration()['show_counts'],
            'max_depth' => $this->configuration()['max_depth'],
            'sort_by' => $this->configuration()['sort_by'],
            'sort_order' => $this->configuration()['sort_order'],
            'properties' => $this->active_values()
        ];
    }

}
