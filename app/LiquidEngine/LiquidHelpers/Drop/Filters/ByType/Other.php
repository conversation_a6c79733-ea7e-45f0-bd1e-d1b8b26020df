<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.4.2019 г.
 * Time: 12:02 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType;

/**
 * Class Other
 * 
 * Represents a filter for other product attributes like new, sale, and featured.
 * This class extends AbstractFilter to provide filtering functionality for special product states.
 * 
 * @property-read array<string> $_types List of supported filter types
 * @property-read array<string, string|int> $_type_maps Mapping of filter types to their values
 * @property-read array<array{key: string, name: string, active: bool}>|null $_other_filters Cached filter items
 * @property-read array<string, bool> $_allowed Allowed filter types
 * @property-read string $handle Filter handle for Shopify compatibility
 * @property-read string $title Filter title for Shopify compatibility
 * @property-read string $type Filter type for Shopify compatibility
 * @property-read string|null $description Filter description for Shopify compatibility
 * @property-read int $sort_order Filter sort order for Shopify compatibility
 * @property-read bool $is_required Whether the filter is required
 * @property-read bool $allows_multiple Whether the filter allows multiple selections
 * @property-read bool $is_visible Whether the filter is visible
 * @property-read array<array{key: string, name: string, value: string, count: int, active: bool}> $active_values Active filter values
 * @property-read int $total_values Total number of filter values
 * @property-read int $usage_count Number of times the filter is used
 * @property-read array{multiple_selection: bool, display_type: string, show_counts: bool, sort_by: string, sort_order: string} $configuration Filter configuration
 * @property-read array{display_type: string, show_counts: bool, sort_by: string, sort_order: string, items: array<array{key: string, name: string, active: bool}>} $context Filter context
 * @property-read array{@type: string, name: string, description: string|null} $structured_data Structured data for SEO
 * @property-read array{category: string, action: string, label: string} $analytics Analytics data
 * @property-read array{aria_label: string, role: string} $accessibility Accessibility attributes
 */
class Other extends AbstractFilter
{
    /**
     * @var array<string>
     */
    protected array $_types = [
        'new', 'sale', 'featured'
    ];

    /**
     * @var array<string, string|int>
     */
    protected array $_type_maps = [
        'new' => 'yes',
        'sale' => 'yes',
        'featured' => 1
    ];

    /**
     * @var array<array{
     *     key: string,
     *     name: string,
     *     active: bool
     * }>|null
     */
    protected ?array $_other_filters = null;

    /**
     * @var array<string, bool>
     */
    protected array $_allowed;

    /**
     * @param array $_makeFilters
     * @param mixed $_model_query
     * @param array<string, bool> $_allowed
     */
    public function __construct(array $_makeFilters, $_model_query, array $_allowed = [])
    {
        $this->_allowed = $_allowed;
        parent::__construct($_makeFilters, $_model_query);
    }

    /**
     * Check if filter is allowed
     * @return bool
     */
    public function allowed(): bool
    {
        return $this->items() !== [];
    }

    /**
     * Get filter items
     * @return array<array{
     *     key: string,
     *     name: string,
     *     active: bool
     * }>
     */
    protected function items(): array
    {
        if (!is_null($this->_other_filters)) {
            return $this->_other_filters;
        }

        return array_filter(array_map(function (string $type): ?array {
            if (!($this->_allowed[$type] ?? false)) {
                return null;
            }

            if (activeRoute('bundles.list.list') && 'sale' === $type) {
                return null;
            }

            if (!$this->getProductQuery()->where($type, $this->_type_maps[$type])->value('id')) {
                return null;
            }

            return [
                'key' => $type,
                'name' => __('item.catalog.filters.text.' . $type),
                'active' => !!request()->query($type),
            ];
        }, $this->_types));
    }

    /**
     * Get filter handle
     * @return string
     */
    public function handle(): string
    {
        return 'other';
    }

    /**
     * Get filter title
     * @return string
     */
    public function title(): string
    {
        return __('item.catalog.filters.title.other');
    }

    /**
     * Get filter type
     * @return string
     */
    public function type(): string
    {
        return 'other';
    }

    /**
     * Get filter description
     * @return string|null
     */
    public function description(): ?string
    {
        return __('item.catalog.filters.description.other');
    }

    /**
     * Get filter sort order
     * @return int
     */
    public function sort_order(): int
    {
        return 4;
    }

    /**
     * Check if filter is required
     * @return bool
     */
    public function is_required(): bool
    {
        return false;
    }

    /**
     * Check if filter allows multiple selections
     * @return bool
     */
    public function allows_multiple(): bool
    {
        return true;
    }

    /**
     * Check if filter is visible
     * @return bool
     */
    public function is_visible(): bool
    {
        return $this->allowed();
    }

    /**
     * Get active filter values
     * @return array<array{
     *     key: string,
     *     name: string,
     *     value: string,
     *     count: int,
     *     active: bool
     * }>
     */
    public function active_values(): array
    {
        $items = $this->items();
        $active = [];

        foreach ($items as $item) {
            if ($item['active']) {
                $active[] = [
                    'key' => $item['key'],
                    'name' => $item['name'],
                    'value' => $item['key'],
                    'count' => 1,
                    'active' => true
                ];
            }
        }

        return $active;
    }

    /**
     * Get total number of filter values
     * @return int
     */
    public function total_values(): int
    {
        return count($this->items());
    }

    /**
     * Get filter usage count
     * @return int
     */
    public function usage_count(): int
    {
        $active = $this->active_values();
        return count($active);
    }

    /**
     * Get filter configuration
     * @return array{
     *     multiple_selection: bool,
     *     display_type: string,
     *     show_counts: bool,
     *     sort_by: string,
     *     sort_order: string
     * }
     */
    public function configuration(): array
    {
        return [
            'multiple_selection' => true,
            'display_type' => 'list',
            'show_counts' => false,
            'sort_by' => 'name',
            'sort_order' => 'asc'
        ];
    }

    /**
     * Get filter context
     * @return array{
     *     display_type: string,
     *     show_counts: bool,
     *     sort_by: string,
     *     sort_order: string,
     *     items: array<array{
     *         key: string,
     *         name: string,
     *         active: bool
     *     }>
     * }
     */
    public function context(): array
    {
        return [
            'display_type' => 'list',
            'show_counts' => false,
            'sort_by' => 'name',
            'sort_order' => 'asc',
            'items' => $this->items()
        ];
    }

    /**
     * Get structured data for SEO
     * @return array{
     *     @type: string,
     *     name: string,
     *     description: string|null
     * }
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'FilterGroup',
            'name' => $this->title(),
            'description' => $this->description()
        ];
    }

    /**
     * Get analytics data
     * @return array{
     *     category: string,
     *     action: string,
     *     label: string
     * }
     */
    public function analytics(): array
    {
        return [
            'category' => 'Filter',
            'action' => 'Select',
            'label' => $this->title()
        ];
    }

    /**
     * Get accessibility attributes
     * @return array{
     *     aria_label: string,
     *     role: string
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => $this->title(),
            'role' => 'group'
        ];
    }
}
