<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.4.2019 г.
 * Time: 12:02 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType;

use Apps;
use Illuminate\Support\Collection as IlluminateCollection;
use Illuminate\Database\Query\JoinClause;
use Modules\BrandModel\Models\Brand;
use Modules\BrandModel\Models\Model;
use App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\BrandModel\Brand as DropBrand;

class BrandModel extends AbstractFilter
{

    /**
     * The brand models filter data.
     *
     * @var array<string, mixed>|null
     */
    protected ?array $_brand_models_filter = null;

    /**
     * {@inheritdoc}
     * @return array<string, mixed>
     */
    protected function items(): array
    {
        //brand model
        if (!Apps::installed('brand_model')) {
            return [];
        }

        if (!is_null($this->_brand_models_filter)) {
            return $this->_brand_models_filter;
        }

        $query = $this->getProductQuery(['brand', 'model']);

        /** @var IlluminateCollection<int, int> $product_models_filter */
        $product_models_filter = $query->distinct()->join('@brand_model_product_to_model', function (JoinClause $join): void {
            $join->on('@brand_model_product_to_model.product_id', 'products.id');
        })->setEagerLoads([])->pluck('@brand_model_product_to_model.model_id');

        if ($product_models_filter->isEmpty()) {
            return $this->_brand_models_filter = [];
        }

        /** @var IlluminateCollection<int, Model> $product_models_data */
        $product_models_data = Model::active()->with('brand')->whereHas('brand', function ($query): void {
            /** @var Brand $query */
            $query->active()->select(['id', 'title', 'url_handle', 'max_thumb_size', 'image']);
        })->whereIn('id', $product_models_filter)->get(['id', 'brand_id', 'title', 'url_handle']);

        return $this->_brand_models_filter = $product_models_data->groupBy('brand_id')->map(function ($models, $brand_id) {
            $brand = $models->first()->brand;
            return new DropBrand($brand, $models);
        })->values()->all();
    }

    /**
     * Get the filter name
     * @return string
     */
    public function name(): string
    {
        return __('item.catalog.filters.text.brand_model');
    }

    /**
     * Get the filter type
     * @return string
     */
    public function type(): string
    {
        return 'brand_model';
    }

    /**
     * Get the filter description
     * @return string|null
     */
    public function description(): ?string
    {
        return __('item.catalog.filters.description.brand_model');
    }

    /**
     * Get active filter values
     * @return array<string, array{id: int, name: string, handle: string, url: string}>
     */
    public function active_values(): array
    {
        $activeValues = [];
        foreach ($this->items() as $brand) {
            if ($brand->active()) {
                $activeValues[] = [
                    'id' => $brand->id(),
                    'name' => $brand->name(),
                    'handle' => $brand->url_handle(),
                    'url' => $brand->url()
                ];
            }
            foreach ($brand->models() as $model) {
                if ($model->active()) {
                    $activeValues[] = [
                        'id' => $model->id(),
                        'name' => $model->name(),
                        'handle' => $model->url_handle(),
                        'url' => $model->url()
                    ];
                }
            }
        }
        return $activeValues;
    }

    /**
     * Get filter usage count
     * @return int
     */
    public function usage_count(): int
    {
        $count = 0;
        foreach ($this->items() as $brand) {
            $count += $brand->total_products();
            foreach ($brand->models() as $model) {
                $count += $model->total_products();
            }
        }
        return $count;
    }

    /**
     * Get SEO meta title
     * @return string
     */
    public function meta_title(): string
    {
        $activeBrands = array_filter($this->items(), fn($brand) => $brand->active());
        if (empty($activeBrands)) {
            return $this->name();
        }
        return implode(', ', array_map(fn($brand) => $brand->name(), $activeBrands));
    }

    /**
     * Get SEO meta description
     * @return string
     */
    public function meta_description(): string
    {
        $activeBrands = array_filter($this->items(), fn($brand) => $brand->active());
        if (empty($activeBrands)) {
            return $this->description() ?? '';
        }
        return sprintf(
            __('item.catalog.filters.meta_description.brand_model'),
            implode(', ', array_map(fn($brand) => $brand->name(), $activeBrands))
        );
    }

    /**
     * Get canonical URL
     * @return string|null
     */
    public function canonical_url(): ?string
    {
        $activeBrands = array_filter($this->items(), fn($brand) => $brand->active());
        if (empty($activeBrands)) {
            return null;
        }
        return route('category.view', [
            'slug' => 'brands',
            'brand' => implode(',', array_map(fn($brand) => $brand->url_handle(), $activeBrands))
        ]);
    }

    /**
     * Get structured data for SEO
     * @return array{
     *     '@context': string,
     *     '@type': string,
     *     'itemListElement': array<array{
     *         '@type': string,
     *         'name': string,
     *         'url': string,
     *         'model': array<array{
     *             '@type': string,
     *             'name': string,
     *             'url': string
     *         }>
     *     }>
     * }
     */
    public function structured_data(): array
    {
        $brands = array_map(function($brand) {
            $models = array_map(function($model) {
                return [
                    '@type' => 'ProductModel',
                    'name' => $model->name(),
                    'url' => $model->url()
                ];
            }, $brand->models());

            return [
                '@type' => 'Brand',
                'name' => $brand->name(),
                'url' => $brand->url(),
                'model' => $models
            ];
        }, $this->items());

        return [
            '@context' => 'https://schema.org',
            '@type' => 'ItemList',
            'itemListElement' => $brands
        ];
    }

    /**
     * Check if the filter allows multiple selections
     * @return bool
     */
    public function allows_multiple(): bool
    {
        return true;
    }

    /**
     * Get filter configuration.
     * Shopify compatibility method.
     *
     * @return array{
     *     multiple_selection: bool,
     *     display_type: string,
     *     show_counts: bool,
     *     sort_by: string,
     *     sort_order: string,
     *     required: bool,
     *     visible: bool,
     *     total_values: int,
     *     usage_count: int
     * }
     */
    public function configuration(): array
    {
        $config = parent::configuration();
        return array_merge($config, [
            'multiple_selection' => true,
            'display_type' => 'dropdown',
            'show_counts' => true,
            'sort_by' => 'name',
            'sort_order' => 'asc',
            'required' => $this->is_required(),
            'visible' => $this->is_visible(),
            'total_values' => $this->total_values(),
            'usage_count' => $this->usage_count()
        ]);
    }

    /**
     * Get filter context for templates
     * @return array{
     *     display_type: string,
     *     show_counts: bool,
     *     sort_by: string,
     *     sort_order: string,
     *     brands: array<array{
     *         id: int,
     *         name: string,
     *         handle: string,
     *         url: string,
     *         active: bool,
     *         models: array<array{
     *             id: int,
     *             name: string,
     *             handle: string,
     *             url: string,
     *             active: bool
     *         }>
     *     }>
     * }
     */
    public function context(): array
    {
        $context = parent::context();
        return array_merge($context, [
            'display_type' => 'dropdown',
            'show_counts' => true,
            'sort_by' => 'name',
            'sort_order' => 'asc',
            'brands' => array_map(function($brand) {
                return [
                    'id' => $brand->id(),
                    'name' => $brand->name(),
                    'handle' => $brand->url_handle(),
                    'url' => $brand->url(),
                    'active' => $brand->active(),
                    'models' => array_map(function($model) {
                        return [
                            'id' => $model->id(),
                            'name' => $model->name(),
                            'handle' => $model->url_handle(),
                            'url' => $model->url(),
                            'active' => $model->active()
                        ];
                    }, $brand->models())
                ];
            }, $this->items())
        ]);
    }

    /**
     * Get the filter handle (URL-friendly identifier).
     * Shopify compatibility method.
     *
     * @return string
     */
    public function handle(): string
    {
        return 'brand-model';
    }

    /**
     * Get the filter title/name.
     * Shopify compatibility method.
     *
     * @return string
     */
    public function title(): string
    {
        return $this->name();
    }

    /**
     * Get the filter sort order.
     * Shopify compatibility method.
     *
     * @return int
     */
    public function sort_order(): int
    {
        return 10; // Higher priority for brand/model filters
    }

    /**
     * Check if the filter is required.
     * Shopify compatibility method.
     *
     * @return bool
     */
    public function is_required(): bool
    {
        return false;
    }

    /**
     * Check if the filter is visible.
     * Shopify compatibility method.
     *
     * @return bool
     */
    public function is_visible(): bool
    {
        return !empty($this->items());
    }

    /**
     * Get total number of filter values.
     * Shopify compatibility method.
     *
     * @return int
     */
    public function total_values(): int
    {
        $count = 0;
        foreach ($this->items() as $brand) {
            $count++;
            $count += count($brand->models());
        }
        return $count;
    }

}
