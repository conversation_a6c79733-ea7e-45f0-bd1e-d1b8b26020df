<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\BrandModel;

use App\Exceptions\Error;
use Illuminate\Contracts\Support\Arrayable;

class BrandDetails extends Brand
{

    public function description()
    {
        return $this->_brand->description;
    }

    public function seo_title()
    {
        return $this->_brand->seo_title;
    }

    public function seo_description()
    {
        return $this->_brand->seo_description;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     * @throws Error
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'description' => $this->description(),
            'seo_title' => $this->seo_title(),
            'seo_description' => $this->seo_description(),
        ]);
    }

}
