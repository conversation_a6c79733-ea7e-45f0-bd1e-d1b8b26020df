<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Exceptions\Error;
use App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute;
use App\Helper\YesNo;
use App\Models\Discount\ProductBanners;
use App\Models\Discount\ProductLabels;
use App\Models\Product\Image;
use Illuminate\Config\Repository;
use App\Models\Product\Product as ProductModel;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Banner;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Label;
use App\Models\Discount\Discount as DiscountModel;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Variant;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use App\LiquidEngine\Traits\DropImageToArray;
/**
 * Bundle Drop Class
 *
 * This class represents bundle information in Liquid templates, providing access to bundle properties
 * and methods for price calculations and Shopify compatibility.
 *
 * Properties:
 * - id: Bundle ID (int)
 * - name: Bundle name (string)
 * - url: Bundle URL (string)
 * - url_handle: Bundle URL handle (string)
 * - views: Number of views (int)
 * - tracking: Whether tracking is enabled (bool)
 * - threshold: Stock threshold (int)
 * - shipping: Whether shipping is enabled (bool)
 * - digital: Whether it's a digital product (bool)
 * - sale: Whether it's on sale (bool)
 * - new: Whether it's new (bool)
 * - free_shipping: Whether it has free shipping (bool)
 * - featured: Whether it's featured (bool)
 * - price_from: Minimum price (float)
 * - price_to: Maximum price (float)
 * - price_percent: Price percentage (int)
 * - price_type: Price type (string)
 * - continue_selling: Whether to continue selling when out of stock (bool)
 * - date_added: Date added (Date)
 * - date_modified: Date modified (Date)
 * - active_to: Active until date (Date|false)
 * - short_description: Short description (string|null)
 * - type: Bundle type (string)
 * - per_row: Items per row (int)
 * - quantity: Available quantity (int)
 * - overlay: Overlay label (Label|false)
 * - favorite: Whether it's favorited (bool)
 * - variant: Default variant (Variant)
 * - image: Main image (LiquidImageAttribute|null)
 * - banners: Bundle banners (array<Banner>)
 * - labels: Bundle labels (array<Label>)
 * - products: Bundle products (array<BundleProduct>)
 * - total_variants: Total number of variants (int|false)
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class Bundle extends AbstractDrop
{

    use DropImageToArray;

    /**
     * @var ProductModel The bundle product model instance
     */
    protected ProductModel $_product;

    /**
     * @var Repository The configuration repository instance
     */
    protected Repository $config;

    /**
     * @var array|null The cached products array
     */
    protected ?array $products = null;

    /**
     * @var Variant|null The cached variant instance
     */
    protected ?Variant $_variant = null;

    /**
     * Bundle constructor.
     *
     * @param ProductModel $product The product model instance
     * @param array|null $config The configuration array
     * @throws Error
     */
    public function __construct(ProductModel $product, ?array $config = null)
    {
        $this->_product = $product->format();
        $this->config = new Repository($config ?: []);
    }

    /**
     * Get the bundle ID
     *
     * @return int The bundle ID
     */
    public function id(): int
    {
        return $this->_product->id;
    }

    /**
     * Get the bundle name
     *
     * @return string The bundle name
     */
    public function name(): string
    {
        return $this->_product->name;
    }

    /**
     * Get the bundle URL
     *
     * @return string The bundle URL
     */
    public function url(): string
    {
        return $this->_product->url;
    }

    /**
     * Get the add to cart URL
     *
     * @return string The add to cart URL
     */
    public function add_to_cart(): string
    {
        return route('cart.add', ['variant_id' => $this->_product->default_variant_id]);
    }

    /**
     * Get the wishlist URL
     *
     * @return string The wishlist URL
     */
    public function wishlist_url(): string
    {
        return route('add-to-wishlist', ['id' => $this->id()]);
    }

    /**
     * Get the bundle URL handle
     *
     * @return string The bundle URL handle
     */
    public function url_handle(): string
    {
        return $this->_product->url_handle;
    }

    /**
     * Get the number of views
     *
     * @return int The number of views
     */
    public function views(): int
    {
        return $this->_product->views;
    }

    /**
     * Check if tracking is enabled
     *
     * @return bool Whether tracking is enabled
     */
    public function tracking(): bool
    {
        return $this->_product->tracking == YesNo::True;
    }

    /**
     * Get the stock threshold
     *
     * @return int The stock threshold
     */
    public function threshold(): int
    {
        return $this->_product->threshold;
    }

    /**
     * Check if shipping is enabled
     *
     * @return bool Whether shipping is enabled
     */
    public function shipping(): bool
    {
        return $this->_product->shipping == YesNo::True;
    }

    /**
     * Check if it's a digital product
     *
     * @return bool Whether it's a digital product
     */
    public function digital(): bool
    {
        return $this->_product->digital == YesNo::Yes;
    }

    /**
     * Check if it's on sale
     *
     * @return bool Whether it's on sale
     */
    public function sale(): bool
    {
        return !!$this->_product->product_to_discount_id;
    }

    /**
     * Check if it's new
     *
     * @return bool Whether it's new
     */
    public function new(): bool
    {
        return $this->_product->new == YesNo::True;
    }

    /**
     * Check if it has free shipping
     *
     * @return bool Whether it has free shipping
     */
    public function free_shipping(): bool
    {
        return !!$this->_product->free_shipping;
    }

    /**
     * Check if it's featured
     *
     * @return bool Whether it's featured
     */
    public function featured(): bool
    {
        return !!$this->_product->featured;
    }

    /**
     * Get the minimum price
     *
     * @return float The minimum price
     */
    public function price_from(): float
    {
        return $this->_product->price_from;
    }

    /**
     * Get the raw minimum price
     *
     * @return float The raw minimum price
     */
    public function price_from_input(): float
    {
        return $this->_product->price_from_input;
    }

    /**
     * Get the formatted minimum price
     *
     * @return string The formatted minimum price
     */
    public function price_from_formatted(): string
    {
        return $this->_product->price_from_formatted;
    }

    /**
     * Get the minimum price parts
     *
     * @return array{
     *     amount: float,
     *     currency: string,
     *     formatted: string,
     *     symbol: string,
     *     position: string
     * } The minimum price parts
     */
    public function price_from_parts(): array
    {
        return PriceParts::format($this->price_from_input());
    }

    /**
     * Get the maximum price
     *
     * @return float The maximum price
     */
    public function price_to(): float
    {
        return $this->_product->price_to;
    }

    /**
     * Get the raw maximum price
     *
     * @return float The raw maximum price
     */
    public function price_to_input(): float
    {
        return $this->_product->price_to_input;
    }

    /**
     * Get the formatted maximum price
     *
     * @return string The formatted maximum price
     */
    public function price_to_formatted(): string
    {
        return $this->_product->price_to_formatted;
    }

    /**
     * Get the maximum price parts
     *
     * @return array{
     *     amount: float,
     *     currency: string,
     *     formatted: string,
     *     symbol: string,
     *     position: string
     * } The maximum price parts
     */
    public function price_to_parts(): array
    {
        return PriceParts::format($this->price_to_input());
    }

    /**
     * Get the price percentage
     *
     * @return int The price percentage
     */
    public function price_percent(): int
    {
        return $this->_product->price_percent;
    }

    /**
     * Get the price type
     *
     * @return string The price type
     */
    public function price_type(): string
    {
        return $this->_product->price_type;
    }

    /**
     * Check if to continue selling when out of stock
     *
     * @return bool Whether to continue selling when out of stock
     */
    public function continue_selling(): bool
    {
        return $this->_product->continue_selling == YesNo::Yes;
    }

    /**
     * Get the date added
     *
     * @return Date The date added
     */
    public function date_added(): Date
    {
        return new Date($this->_product->date_added);
    }

    /**
     * Get the date modified
     *
     * @return Date The date modified
     */
    public function date_modified(): Date
    {
        return new Date($this->_product->date_modified);
    }

    /**
     * Get the active until date
     *
     * @return Date|false The active until date or false if not set
     */
    public function active_to(): Date|false
    {
        if ($this->_product->active_to) {
            return new Date($this->_product->active_to);
        }

        return false;
    }

    /**
     * Get the short description
     *
     * @return string|null The short description
     */
    public function short_description(): ?string
    {
        return $this->_product->short_description;
    }

    /**
     * Get the bundle type
     *
     * @return string The bundle type
     */
    public function type(): string
    {
        return $this->_product->type;
    }

    /**
     * Get the items per row
     *
     * @return int The items per row
     */
    public function per_row(): int
    {
        return $this->_product->per_row;
    }

    /**
     * Get the available quantity
     *
     * @return int The available quantity
     */
    public function quantity(): int
    {
        return $this->_product->quantity;
    }

    /**
     * Get the overlay label
     *
     * @return Label|false The overlay label or false if not set
     */
    public function overlay(): Label|false
    {
        if ($this->_product->overlay) {
            return new Label($this->_product->overlay);
        }

        return false;
    }

    /**
     * Check if it's favorited
     *
     * @return bool Whether it's favorited
     */
    public function favorite(): bool
    {
        return !!$this->_product->favorite;
    }

    /**
     * Get the default variant
     *
     * @return Variant The default variant
     */
    public function variant(): Variant
    {
        if ($this->_variant) {
            return $this->_variant;
        }

        return $this->_variant = new Variant($this->_product->variant, $this->_product);
    }

    /**
     * Get the main image
     *
     * @return LiquidImageAttribute|null The main image
     */
    public function image(): ?LiquidImageAttribute
    {
        if ($this->_product->relationLoaded('image') && $this->_product->image instanceof Image) {
            return new LiquidImageAttribute($this->_product->image);
        }

        return null;
    }

    /**
     * Check if it has an image
     *
     * @return bool Whether it has an image
     */
    public function has_image(): bool
    {
        return $this->_product->relationLoaded('image') && $this->_product->image instanceof Image;
    }

    /**
     * Get the bundle banners
     *
     * @return array<Banner> The bundle banners
     */
    public function banners(): array
    {
        return $this->_product->banners->map(function ($banner): ?Banner {
            if ($banner instanceof ProductBanners) {
                return new Banner($banner->discount);
            } elseif ($banner instanceof DiscountModel) {
                return new Banner($banner);
            }

            return null;
        })->filter()->all();
    }

    /**
     * Get the bundle labels
     *
     * @return array<Label> The bundle labels
     */
    public function labels(): array
    {
        return $this->_product->labels->map(function ($label): ?Label {
            if ($label instanceof ProductLabels) {
                if (is_null($label->discount_id)) {
                    return new Label(new DiscountModel($label->toArray()));
                } else {
                    return new Label($label->discount);
                }
            } elseif ($label instanceof DiscountModel) {
                return new Label($label);
            }

            return null;
        })->filter()->all();
    }

    /**
     * Get the bundle products
     *
     * @return array<BundleProduct> The bundle products
     */
    public function products(): array
    {
        if (!is_null($this->products)) {
            return $this->products;
        }

        $products = [];
        if ($this->_product->relationLoaded('bundle_products')) {
            $products = $this->_product->bundle_products->map(function (ProductModel $product): BundleProduct {
                return new BundleProduct($product, $this->_product);
            })->all();
        }

        return $this->products = $products;
    }

    /**
     * Get the total number of variants
     *
     * @return int|false The total number of variants or false if not loaded
     */
    public function total_variants(): int|false
    {
        if ($this->_product->relationLoaded('bundle_products')) {
            return $this->_product->bundle_products->max('total_variants');
        }

        return false;
    }

    /**
     * Check if it has required data
     *
     * @return bool Whether it has required data
     */
    public function has_required_data(): bool
    {
        if ($this->total_variants()) {
            return true;
        }

        foreach ($this->products() as $product) {
            if ($product->has_required_data()) {
                return true;
            }
        }

        return false;
    }

    // START: Shopify-compatible methods for Product Bundle
    // These methods provide compatibility with Shopify's bundle/product patterns

    /**
     * Get bundle title (alias for name)
     * Shopify compatible: bundle.title
     *
     * @return string The bundle title
     */
    public function title(): string
    {
        return $this->name();
    }

    /**
     * Get bundle handle (alias for url_handle)
     * Shopify compatible: bundle.handle
     *
     * @return string The bundle handle
     */
    public function handle(): string
    {
        return $this->url_handle();
    }

    /**
     * Get bundle description
     * Shopify compatible: bundle.description
     *
     * @return string The bundle description
     */
    public function description(): string
    {
        return $this->short_description() ?? '';
    }

    /**
     * Get bundle vendor/brand
     * Shopify compatible: bundle.vendor
     *
     * @return string The bundle vendor
     */
    public function vendor(): string
    {
        // Default to store name or empty, could be enhanced with actual vendor data
        return '';
    }

    /**
     * Get bundle product type
     * Shopify compatible: bundle.product_type
     *
     * @return string The bundle product type
     */
    public function product_type(): string
    {
        return $this->type() ?? 'Bundle';
    }

    /**
     * Get bundle tags
     *
     * @return array<string> The bundle tags
     */
    public function tags(): array
    {
        return $this->_product->tags->pluck('name')->all();
    }

    /**
     * Check if bundle is available for purchase
     * Shopify compatible: bundle.available
     *
     * @return bool Whether the bundle is available
     */
    public function available(): bool
    {
        return $this->quantity() > 0 || $this->continue_selling();
    }

    /**
     * Get bundle created timestamp
     * Shopify compatible: bundle.created_at
     *
     * @return string|null The bundle created timestamp
     */
    public function created_at(): ?string
    {
        $dateAdded = $this->date_added();
        if ($dateAdded && method_exists($dateAdded, 'toArray')) {
            $dateArray = $dateAdded->toArray();
            if (isset($dateArray['date'])) {
                return $dateArray['date'] instanceof \Carbon\Carbon
                    ? $dateArray['date']->toISOString()
                    : \Carbon\Carbon::parse($dateArray['date'])->toISOString();
            }
        }

        return null;
    }

    /**
     * Get bundle updated timestamp
     * Shopify compatible: bundle.updated_at
     *
     * @return string|null The bundle updated timestamp
     */
    public function updated_at(): ?string
    {
        $dateModified = $this->date_modified();
        if ($dateModified && method_exists($dateModified, 'toArray')) {
            $dateArray = $dateModified->toArray();
            if (isset($dateArray['date'])) {
                return $dateArray['date'] instanceof \Carbon\Carbon
                    ? $dateArray['date']->toISOString()
                    : \Carbon\Carbon::parse($dateArray['date'])->toISOString();
            }
        }

        return null;
    }

    /**
     * Get bundle published timestamp
     * Shopify compatible: bundle.published_at
     *
     * @return string|null The bundle published timestamp
     */
    public function published_at(): ?string
    {
        // Default to created_at, could be enhanced with actual publish date
        return $this->created_at();
    }

    /**
     * Get bundle template suffix
     * Shopify compatible: bundle.template_suffix
     *
     * @return string|null The bundle template suffix
     */
    public function template_suffix(): ?string
    {
        return null; // Could be enhanced to support custom templates
    }

    /**
     * Get total bundle price (minimum price)
     * Shopify compatible: bundle.price
     *
     * @return int The total bundle price in cents
     */
    public function price(): int
    {
        return (int) round($this->price_from() * 100); // Return in cents
    }

    /**
     * Get bundle price range (formatted)
     * Shopify compatible: bundle.price_range
     *
     * @return string The bundle price range
     */
    public function price_range(): string
    {
        $priceFrom = $this->price_from_formatted();
        $priceTo = $this->price_to_formatted();

        if ($priceFrom === $priceTo) {
            return $priceFrom;
        }

        return $priceFrom . ' - ' . $priceTo;
    }

    /**
     * Get minimum price in bundle
     * Shopify compatible: bundle.price_min
     *
     * @return int The minimum price in cents
     */
    public function price_min(): int
    {
        return (int) round($this->price_from() * 100); // Return in cents
    }

    /**
     * Get maximum price in bundle
     * Shopify compatible: bundle.price_max
     *
     * @return int The maximum price in cents
     */
    public function price_max(): int
    {
        return (int) round($this->price_to() * 100); // Return in cents
    }

    /**
     * Check if bundle price varies
     * Shopify compatible: bundle.price_varies
     *
     * @return bool Whether the bundle price varies
     */
    public function price_varies(): bool
    {
        return $this->price_from() !== $this->price_to();
    }

    /**
     * Get compare at price (original price when on sale)
     * Shopify compatible: bundle.compare_at_price
     *
     * @return int|null The compare at price in cents
     */
    public function compare_at_price(): ?int
    {
        if (!$this->sale()) {
            return null;
        }

        // Calculate compare price based on discount percentage
        $currentPrice = $this->price_from();
        $discountPercent = $this->price_percent();

        if ($discountPercent > 0) {
            $originalPrice = $currentPrice / (1 - ($discountPercent / 100));
            return (int) round($originalPrice * 100); // Return in cents
        }

        return null;
    }

    /**
     * Get bundle images
     *
     * @return array<LiquidImageAttribute> The bundle images
     */
    public function images(): array
    {
        return $this->_product->images->map(function (Image $image): LiquidImageAttribute {
            return new LiquidImageAttribute($image);
        })->all();
    }

    /**
     * Get featured image (alias for image)
     * Shopify compatible: bundle.featured_image
     *
     * @return LiquidImageAttribute|null The featured image
     */
    public function featured_image(): ?LiquidImageAttribute
    {
        return $this->image();
    }

    /**
     * Get first available variant
     * Shopify compatible: bundle.first_available_variant
     *
     * @return Variant The first available variant
     */
    public function first_available_variant(): Variant
    {
        return $this->variant();
    }

    /**
     * Get bundle options
     *
     * @return array<array{
     *     name: string,
     *     position: int,
     *     values: array<string>
     * }> The bundle options
     */
    public function options(): array
    {
        return $this->_product->options->map(function ($option): array {
            return [
                'name' => $option->name,
                'position' => $option->position,
                'values' => $option->values->pluck('name')->all()
            ];
        })->all();
    }

    /**
     * Get bundle options with values
     *
     * @return array<array{
     *     name: string,
     *     position: int,
     *     values: array<array{
     *         name: string,
     *         position: int
     *     }>
     * }> The bundle options with values
     */
    public function options_with_values(): array
    {
        return $this->_product->options->map(function ($option): array {
            return [
                'name' => $option->name,
                'position' => $option->position,
                'values' => $option->values->map(function ($value): array {
                    return [
                        'name' => $value->name,
                        'position' => $value->position
                    ];
                })->all()
            ];
        })->all();
    }

    /**
     * Get selected or first variant
     * Shopify compatible: bundle.selected_or_first_available_variant
     *
     * @return Variant The selected or first variant
     */
    public function selected_or_first_available_variant(): Variant
    {
        return $this->variant();
    }

    /**
     * Get bundle variants
     *
     * @return array<Variant> The bundle variants
     */
    public function variants(): array
    {
        return $this->_product->variants->map(function ($variant): Variant {
            return new Variant($variant, $this->_product);
        })->all();
    }

    /**
     * Get bundle collections
     *
     * @return array<Collection> The bundle collections
     */
    public function collections(): array
    {
        return $this->_product->collections->map(function ($collection): Collection {
            return new Collection($collection);
        })->all();
    }

    /**
     * Check if bundle has only default variant
     * Shopify compatible: bundle.has_only_default_variant
     *
     * @return bool Whether the bundle has only default variant
     */
    public function has_only_default_variant(): bool
    {
        return true; // Bundles typically have one main variant
    }

    /**
     * Check if bundle requires shipping
     * Shopify compatible: bundle.requires_shipping
     *
     * @return bool Whether the bundle requires shipping
     */
    public function requires_shipping(): bool
    {
        return $this->shipping() && !$this->digital();
    }

    /**
     * Check if bundle is taxable
     * Shopify compatible: bundle.taxable
     *
     * @return bool Whether the bundle is taxable
     */
    public function taxable(): bool
    {
        return true; // Default to taxable, could be enhanced
    }

    /**
     * Get SEO title
     * Shopify compatible: bundle.seo.title
     *
     * @return string The SEO title
     */
    public function seo_title(): string
    {
        return $this->title();
    }

    /**
     * Get SEO description
     * Shopify compatible: bundle.seo.description
     *
     * @return string The SEO description
     */
    public function seo_description(): string
    {
        return $this->description();
    }

    /**
     * Get bundle metafields
     * Shopify compatible: bundle.metafields
     *
     * @return array<array{
     *     namespace: string,
     *     key: string,
     *     value: mixed,
     *     type: string
     * }> The bundle metafields
     */
    public function metafields(): array
    {
        return $this->_product->metafields->map(function ($metafield): array {
            return [
                'namespace' => $metafield->namespace,
                'key' => $metafield->key,
                'value' => $metafield->value,
                'type' => $metafield->type
            ];
        })->all();
    }

    // END: Shopify-compatible methods

    /**
     * Convert the bundle to an array
     *
     * @return array{
     *     id: int,
     *     name: string,
     *     url: string,
     *     url_handle: string,
     *     views: int,
     *     tracking: bool,
     *     threshold: int,
     *     shipping: bool,
     *     digital: bool,
     *     sale: bool,
     *     new: bool,
     *     free_shipping: bool,
     *     featured: bool,
     *     price_from: float,
     *     price_to: float,
     *     price_percent: int,
     *     price_type: string,
     *     continue_selling: bool,
     *     date_added: array<string, mixed>,
     *     date_modified: array<string, mixed>,
     *     active_to: array<string, mixed>|null,
     *     short_description: string|null,
     *     type: string,
     *     per_row: int,
     *     quantity: int,
     *     overlay: array|null,
     *     favorite: bool,
     *     variant: array,
     *     image: array|null,
     *     banners: array<array>,
     *     labels: array<array>,
     *     products: array<array>,
     *     total_variants: int|false
     * } The bundle data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'url' => $this->url(),
            'url_handle' => $this->url_handle(),
            'views' => $this->views(),
            'tracking' => $this->tracking(),
            'threshold' => $this->threshold(),
            'shipping' => $this->shipping(),
            'digital' => $this->digital(),
            'sale' => $this->sale(),
            'new' => $this->new(),
            'free_shipping' => $this->free_shipping(),
            'featured' => $this->featured(),
            'price_from' => $this->price_from(),
            'price_to' => $this->price_to(),
            'price_percent' => $this->price_percent(),
            'price_type' => $this->price_type(),
            'continue_selling' => $this->continue_selling(),
            'date_added' => $this->date_added()->toArray(),
            'date_modified' => $this->date_modified()->toArray(),
            'active_to' => $this->active_to() ? $this->active_to()->toArray() : null,
            'short_description' => $this->short_description(),
            'type' => $this->type(),
            'per_row' => $this->per_row(),
            'quantity' => $this->quantity(),
            'overlay' => $this->overlay() ? $this->overlay()->toArray() : null,
            'favorite' => $this->favorite(),
            'variant' => $this->variant()->toArray(),
            'image' => $this->image() ? $this->image()->toArray() : null,
            'banners' => array_map(fn(Banner $banner) => $banner->toArray(), $this->banners()),
            'labels' => array_map(fn(Label $label) => $label->toArray(), $this->labels()),
            'products' => array_map(fn(BundleProduct $product) => $product->toArray(), $this->products()),
            'total_variants' => $this->total_variants()
        ];
    }

}
