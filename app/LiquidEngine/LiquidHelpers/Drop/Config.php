<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Support\Facades\Config as LaravelConfig;

/**
 * Class Config
 * 
 * Provides access to application configuration values.
 * Implements Shopify-compatible configuration methods.
 * 
 * @property-read array<string, mixed> $all All configuration values
 * @property-read array<string, mixed> $shopify Shopify-compatible configuration
 * @property-read array<string, mixed> $seo SEO-related configuration
 * @property-read array<string, mixed> $analytics Analytics-related configuration
 */
class Config extends AbstractDrop
{
    /**
     * Get a configuration value.
     *
     * @param string $name The configuration key
     * @param array<int, mixed> $arguments Additional arguments
     * @return mixed
     */
    public function __call(string $name, array $arguments)
    {
        return LaravelConfig::get($name);
    }

    /**
     * Get all configuration values.
     *
     * @return array<string, mixed>
     */
    public function all(): array
    {
        return LaravelConfig::all();
    }

    /**
     * Get accessibility attributes
     * @return array{
     *     aria_label: string,
     *     role: string
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => 'Configuration settings',
            'role' => 'status'
        ];
    }

    /**
     * Get structured data for SEO
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'configuration' => [
                'environment' => LaravelConfig::get('app.env'),
                'debug' => LaravelConfig::get('app.debug'),
                'timezone' => LaravelConfig::get('app.timezone'),
                'locale' => LaravelConfig::get('app.locale')
            ]
        ];
    }

    /**
     * Convert to Shopify format
     * @return array{
     *     environment: string,
     *     debug: bool,
     *     timezone: string,
     *     locale: string,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toShopifyArray(): array
    {
        return [
            'environment' => LaravelConfig::get('app.env'),
            'debug' => LaravelConfig::get('app.debug'),
            'timezone' => LaravelConfig::get('app.timezone'),
            'locale' => LaravelConfig::get('app.locale'),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to SEO array format
     * @return array{
     *     environment: string,
     *     debug: bool,
     *     timezone: string,
     *     locale: string,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toSeoArray(): array
    {
        return [
            'environment' => LaravelConfig::get('app.env'),
            'debug' => LaravelConfig::get('app.debug'),
            'timezone' => LaravelConfig::get('app.timezone'),
            'locale' => LaravelConfig::get('app.locale'),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to analytics array format
     * @return array{
     *     environment: string,
     *     debug: bool,
     *     timezone: string,
     *     locale: string,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toAnalyticsArray(): array
    {
        return [
            'environment' => LaravelConfig::get('app.env'),
            'debug' => LaravelConfig::get('app.debug'),
            'timezone' => LaravelConfig::get('app.timezone'),
            'locale' => LaravelConfig::get('app.locale'),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Get the collection as a plain array.
     *
     * @return array{
     *     all: array<string, mixed>,
     *     environment: string,
     *     debug: bool,
     *     timezone: string,
     *     locale: string,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string},
     *     shopify: array{environment: string, debug: bool, timezone: string, locale: string, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     seo: array{environment: string, debug: bool, timezone: string, locale: string, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     analytics: array{environment: string, debug: bool, timezone: string, locale: string, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}}
     * }
     */
    public function toArray(): array
    {
        return [
            'all' => $this->all(),
            'environment' => LaravelConfig::get('app.env'),
            'debug' => LaravelConfig::get('app.debug'),
            'timezone' => LaravelConfig::get('app.timezone'),
            'locale' => LaravelConfig::get('app.locale'),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics' => $this->toAnalyticsArray()
        ];
    }
}
