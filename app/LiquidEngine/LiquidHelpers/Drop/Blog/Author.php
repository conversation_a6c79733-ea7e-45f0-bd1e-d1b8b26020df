<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Blog;

use App\Exceptions\Error;
use App\Models\Setting\Admin as AdminModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;
use App\LiquidEngine\LiquidHelpers\Drop\Date;

class Author extends AbstractDrop
{

    use DropImageToArray;

    // Author role constants
    public const ROLE_ADMIN = 'admin';
    public const ROLE_EDITOR = 'editor';
    public const ROLE_CONTRIBUTOR = 'contributor';
    public const ROLE_GUEST = 'guest';

    // Author status constants
    public const STATUS_ACTIVE = 'active';
    public const STATUS_INACTIVE = 'inactive';
    public const STATUS_SUSPENDED = 'suspended';

    protected \App\Models\Setting\Admin $_admin;

    /**
     * @param \App\Models\Setting\Admin $admin
     * @return mixed
     */
    public function __construct(AdminModel $admin)
    {
        $this->_admin = $admin;
    }

    public function username()
    {
        return $this->_admin->username;
    }

    public function full_name()
    {
        return $this->_admin->full_name;
    }

    public function first_name()
    {
        return $this->_admin->first_name;
    }

    public function last_name()
    {
        return $this->_admin->last_name;
    }

    public function items_count()
    {
        if (!$this->_admin->exists) {
            return 0;
        }

        return is_numeric($this->_admin->active_items_count) ? $this->_admin->active_items_count : $this->_admin->activeItems()->count();
    }

    public function image()
    {
        $formatter = new UrlImageFormat($this->_admin);
        return $formatter->getLiquidImage();
    }

    /**
     * @return bool
     */
    public function has_image(): bool
    {
        return !!$this->_admin->hasImage();
    }

    /**
     * Get the author's role
     *
     * @return string
     */
    public function role(): string
    {
        if ($this->_admin->is_admin) {
            return self::ROLE_ADMIN;
        }
        if ($this->_admin->is_editor) {
            return self::ROLE_EDITOR;
        }
        if ($this->_admin->is_contributor) {
            return self::ROLE_CONTRIBUTOR;
        }
        return self::ROLE_GUEST;
    }

    /**
     * Get the author's status
     *
     * @return string
     */
    public function status(): string
    {
        if (!$this->_admin->is_active) {
            return self::STATUS_INACTIVE;
        }
        if ($this->_admin->is_suspended) {
            return self::STATUS_SUSPENDED;
        }
        return self::STATUS_ACTIVE;
    }

    /**
     * Get the author's email
     *
     * @return string|null
     */
    public function email(): ?string
    {
        return $this->_admin->email;
    }

    /**
     * Get the author's bio
     *
     * @return string
     */
    public function bio(): string
    {
        return $this->_admin->bio ?? '';
    }

    /**
     * Get the author's website URL
     *
     * @return string|null
     */
    public function website(): ?string
    {
        return $this->_admin->website;
    }

    /**
     * Get the author's social media links
     *
     * @return array
     */
    public function social_media(): array
    {
        return [
            'facebook' => $this->_admin->facebook,
            'twitter' => $this->_admin->twitter,
            'linkedin' => $this->_admin->linkedin,
            'instagram' => $this->_admin->instagram,
            'youtube' => $this->_admin->youtube,
        ];
    }

    /**
     * Get the author's meta description
     *
     * @return string
     */
    public function meta_description(): string
    {
        return $this->_admin->seo_description ?? $this->bio();
    }

    /**
     * Get the author's meta title
     *
     * @return string
     */
    public function meta_title(): string
    {
        return $this->_admin->seo_title ?? $this->full_name();
    }

    /**
     * Get the author's SEO keywords
     *
     * @return array
     */
    public function seo_keywords(): array
    {
        if ($this->_admin->seo_keywords) {
            return array_map('trim', explode(',', $this->_admin->seo_keywords));
        }
        return [];
    }

    /**
     * Get the author's canonical URL
     *
     * @return string
     */
    public function canonical_url(): string
    {
        return $this->_admin->canonical_url ?? route('blog.author', ['author' => $this->username()]);
    }

    /**
     * Get the author's robots meta tag content
     *
     * @return string
     */
    public function robots(): string
    {
        $robots = [];

        // Indexing
        if ($this->_admin->no_index) {
            $robots[] = 'noindex';
        } else {
            $robots[] = 'index';
        }

        // Following
        if ($this->_admin->no_follow) {
            $robots[] = 'nofollow';
        } else {
            $robots[] = 'follow';
        }

        return implode(',', $robots);
    }

    /**
     * Get Open Graph meta tags
     *
     * @return array
     */
    public function open_graph(): array
    {
        return [
            'title' => $this->meta_title(),
            'description' => $this->meta_description(),
            'type' => 'profile',
            'url' => $this->canonical_url(),
            'image' => $this->image_url(),
            'site_name' => config('app.name'),
            'profile:first_name' => $this->first_name(),
            'profile:last_name' => $this->last_name(),
            'profile:username' => $this->username(),
        ];
    }

    /**
     * Get Twitter Card meta tags
     *
     * @return array
     */
    public function twitter_card(): array
    {
        return [
            'card' => 'summary_large_image',
            'title' => $this->meta_title(),
            'description' => $this->meta_description(),
            'image' => $this->image_url(),
            'site' => '@' . config('app.name'),
            'creator' => '@' . $this->username(),
        ];
    }

    /**
     * Get structured data for JSON-LD
     *
     * @return array
     */
    public function structured_data(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Person',
            'name' => $this->full_name(),
            'givenName' => $this->first_name(),
            'familyName' => $this->last_name(),
            'description' => $this->meta_description(),
            'image' => $this->image_url(),
            'url' => $this->canonical_url(),
            'jobTitle' => $this->role(),
            'worksFor' => [
                '@type' => 'Organization',
                'name' => config('app.name'),
            ],
            'sameAs' => array_filter($this->social_media()),
        ];
    }

    /**
     * Get the author's image URL
     *
     * @return string|null
     */
    public function image_url(): ?string
    {
        if ($this->has_image()) {
            return $this->_admin->image;
        }
        return null;
    }

    /**
     * Get the author's analytics data
     *
     * @return array
     */
    public function analytics(): array
    {
        return [
            'articles_count' => $this->items_count(),
            'comments_count' => $this->_admin->comments_count ?? 0,
            'views_count' => $this->_admin->views_count ?? 0,
            'last_updated' => $this->_admin->updated_at ? new Date($this->_admin->updated_at) : null,
            'created_at' => new Date($this->_admin->created_at),
        ];
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     * @throws Error
     */
    public function toArray(): array
    {
        return [
            'role' => $this->role(),
            'status' => $this->status(),
            'email' => $this->email(),
            'bio' => $this->bio(),
            'website' => $this->website(),
            'social_media' => $this->social_media(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'seo_keywords' => $this->seo_keywords(),
            'canonical_url' => $this->canonical_url(),
            'robots' => $this->robots(),
            'open_graph' => $this->open_graph(),
            'twitter_card' => $this->twitter_card(),
            'structured_data' => $this->structured_data(),
            'image_url' => $this->image_url(),
            'analytics' => $this->analytics(),
        ];
    }

}
