<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Blog;

use App\Exceptions\Error;
use App\Helper\YesNo;
use App\Models\Blog\Blog as BlogModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Date;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;

class Blog extends AbstractDrop
{

    use DropImageToArray;

    // Blog status constants
    public const STATUS_ACTIVE = 'active';
    public const STATUS_INACTIVE = 'inactive';
    public const STATUS_ARCHIVED = 'archived';

    // Blog type constants
    public const TYPE_STANDARD = 'standard';
    public const TYPE_FEATURED = 'featured';
    public const TYPE_PINNED = 'pinned';

    protected \App\Models\Blog\Blog $_blog;

    /**
     * @param \App\Models\Blog\Blog $blog
     * @return mixed
     */
    public function __construct(BlogModel $blog)
    {
        $this->_blog = $blog;
    }

    /**
     * Factory method to create a Blog drop
     *
     * @param BlogModel $blog
     * @return self
     */
    public static function make(BlogModel $blog): self
    {
        return new self($blog);
    }

    /**
     * Get the blog ID
     *
     * @return int
     */
    public function id()
    {
        return $this->_blog->id;
    }

    /**
     * Get the blog title (name)
     *
     * @return string
     */
    public function title()
    {
        return $this->_blog->name;
    }

    /**
     * Get the blog name (Shopify compatibility)
     *
     * @return string
     */
    public function name()
    {
        return $this->_blog->name;
    }

    /**
     * Get the blog URL
     *
     * @return string
     */
    public function url()
    {
        return $this->_blog->url;
    }

    /**
     * Get the blog handle
     *
     * @return string
     */
    public function handle()
    {
        return $this->_blog->url_handle;
    }

    /**
     * Get the blog URL handle (legacy method)
     *
     * @return string
     */
    public function url_handle()
    {
        return $this->_blog->url_handle;
    }

    /**
     * Check if the blog is currently selected
     *
     * @return bool
     */
    public function selected()
    {
        if (activeRoute('blog.view')) {
            return routeParameter('filter') == 'category' && routeParameter('slug') == $this->url_handle();
        }

        return false;
    }

    /**
     * Check if comments are enabled for the blog
     *
     * @return bool
     */
    public function comments_enabled(): bool
    {
        return $this->_blog->comments != YesNo::False;
    }

    /**
     * Get the default sort order for articles
     *
     * @return string
     */
    public function sort_option()
    {
        return $this->_blog->sort_by ?? 'created_at desc';
    }

    /**
     * Get the blog image
     *
     * @return mixed
     */
    public function image()
    {
        $formatter = new UrlImageFormat($this->_blog);
        return $formatter->getLiquidImage();
    }

    /**
     * Check if the blog has an image
     *
     * @return bool
     */
    public function has_image(): bool
    {
        return !!$this->_blog->hasImage();
    }

    /**
     * Count the active articles in the blog
     *
     * @return int
     */
    public function articles_count()
    {
        return is_numeric($this->_blog->active_items_count) ? $this->_blog->active_items_count : $this->_blog->activeItems()->count();
    }

    /**
     * Count the active items in the blog (legacy method)
     *
     * @return int
     */
    public function items_count()
    {
        return $this->articles_count();
    }

    /**
     * Get all articles in the blog
     *
     * @return mixed
     */
    public function articles()
    {
        return $this->_blog->items()
            ->active()->withCount('comments as comments_count')
            ->with(['author', 'tags', 'blog'])->orderBy('id', 'desc');
    }

    /**
     * Get all items in the blog (legacy method)
     *
     * @return mixed
     */
    public function items()
    {
        return $this->articles();
    }

    /**
     * Get all tags used in the blog's articles
     *
     * @return array
     */
    public function all_tags()
    {
        return $this->_blog->allTags()->pluck('name', 'url_handle')->toArray();
    }

    /**
     * Get the blog status
     *
     * @return string
     */
    public function status(): string
    {
        if (!$this->_blog->is_active) {
            return self::STATUS_INACTIVE;
        }
        if ($this->_blog->is_archived) {
            return self::STATUS_ARCHIVED;
        }
        return self::STATUS_ACTIVE;
    }

    /**
     * Get the blog type
     *
     * @return string
     */
    public function type(): string
    {
        if ($this->_blog->is_featured) {
            return self::TYPE_FEATURED;
        }
        if ($this->_blog->is_pinned) {
            return self::TYPE_PINNED;
        }
        return self::TYPE_STANDARD;
    }

    /**
     * Get the blog template suffix
     *
     * @return string
     */
    public function template_suffix(): string
    {
        return $this->_blog->template_suffix ?? '';
    }

    /**
     * Get the blog meta description
     *
     * @return string
     */
    public function meta_description(): string
    {
        return $this->_blog->seo_description ?? $this->description();
    }

    /**
     * Get the blog meta title
     *
     * @return string
     */
    public function meta_title(): string
    {
        return $this->_blog->seo_title ?? $this->title();
    }

    /**
     * Get the blog description
     *
     * @return string
     */
    public function description(): string
    {
        return $this->_blog->description ?? '';
    }

    /**
     * Get the blog SEO keywords
     *
     * @return array
     */
    public function seo_keywords(): array
    {
        if ($this->_blog->seo_keywords) {
            return array_map('trim', explode(',', $this->_blog->seo_keywords));
        }
        return [];
    }

    /**
     * Get the blog canonical URL
     *
     * @return string
     */
    public function canonical_url(): string
    {
        return $this->_blog->canonical_url ?? $this->url();
    }

    /**
     * Get the blog robots meta tag content
     *
     * @return string
     */
    public function robots(): string
    {
        $robots = [];

        // Indexing
        if ($this->_blog->no_index) {
            $robots[] = 'noindex';
        } else {
            $robots[] = 'index';
        }

        // Following
        if ($this->_blog->no_follow) {
            $robots[] = 'nofollow';
        } else {
            $robots[] = 'follow';
        }

        // Additional directives
        if ($this->_blog->no_archive) {
            $robots[] = 'noarchive';
        }
        if ($this->_blog->no_snippet) {
            $robots[] = 'nosnippet';
        }

        return implode(',', $robots);
    }

    /**
     * Get Open Graph meta tags
     *
     * @return array
     */
    public function open_graph(): array
    {
        return [
            'title' => $this->meta_title(),
            'description' => $this->meta_description(),
            'type' => 'website',
            'url' => $this->canonical_url(),
            'image' => $this->image_url(),
            'site_name' => config('app.name'),
        ];
    }

    /**
     * Get Twitter Card meta tags
     *
     * @return array
     */
    public function twitter_card(): array
    {
        return [
            'card' => 'summary_large_image',
            'title' => $this->meta_title(),
            'description' => $this->meta_description(),
            'image' => $this->image_url(),
            'site' => '@' . config('app.name'),
        ];
    }

    /**
     * Get structured data for JSON-LD
     *
     * @return array
     */
    public function structured_data(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Blog',
            'name' => $this->title(),
            'description' => $this->meta_description(),
            'image' => $this->image_url(),
            'url' => $this->canonical_url(),
            'publisher' => [
                '@type' => 'Organization',
                'name' => config('app.name'),
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => config('app.logo'),
                ],
            ],
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => $this->canonical_url(),
            ],
            'keywords' => implode(', ', $this->seo_keywords()),
            'articleCount' => $this->articles_count(),
        ];
    }

    /**
     * Get the blog image URL
     *
     * @return string|null
     */
    public function image_url(): ?string
    {
        if ($this->has_image()) {
            return $this->_blog->image;
        }
        return null;
    }

    /**
     * Get the blog analytics data
     *
     * @return array
     */
    public function analytics(): array
    {
        return [
            'articles_count' => $this->articles_count(),
            'comments_count' => $this->_blog->comments_count ?? 0,
            'views_count' => $this->_blog->views_count ?? 0,
            'last_updated' => $this->_blog->updated_at ? new Date($this->_blog->updated_at) : null,
            'created_at' => new Date($this->_blog->created_at),
        ];
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     * @throws Error
     */
    public function toArray(): array
    {
        return [
            'status' => $this->status(),
            'type' => $this->type(),
            'template_suffix' => $this->template_suffix(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'description' => $this->description(),
            'seo_keywords' => $this->seo_keywords(),
            'canonical_url' => $this->canonical_url(),
            'robots' => $this->robots(),
            'open_graph' => $this->open_graph(),
            'twitter_card' => $this->twitter_card(),
            'structured_data' => $this->structured_data(),
            'image_url' => $this->image_url(),
            'analytics' => $this->analytics(),
        ];
    }

}
