<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Blog;

use App\Exceptions\Error;
use App\Models\Setting\Admin;
use App\Models\Blog\Article as ArticleModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Date;
use App\LiquidEngine\LiquidHelpers\Drop\Tag;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;
use Illuminate\Support\Str;

class Article extends AbstractDrop
{

    use DropImageToArray;

    // Article status constants
    public const STATUS_DRAFT = 'draft';
    public const STATUS_PUBLISHED = 'published';
    public const STATUS_ARCHIVED = 'archived';
    public const STATUS_SCHEDULED = 'scheduled';

    // Article type constants
    public const TYPE_STANDARD = 'standard';
    public const TYPE_FEATURED = 'featured';
    public const TYPE_PINNED = 'pinned';
    public const TYPE_SPONSORED = 'sponsored';

    protected \App\Models\Blog\Article $_article;

    /**
     * Article constructor.
     * @param ArticleModel $article
     */
    public function __construct(ArticleModel $article)
    {
        $this->_article = $article;
    }

    /**
     * Factory method to create an Article drop
     *
     * @param ArticleModel $article
     * @return static
     */
    public static function make(ArticleModel $article): static
    {
        return new static($article);
    }

    /**
     * Get the article ID
     *
     * @return int
     */
    public function id()
    {
        return $this->_article->id;
    }

    /**
     * Get the article title (name)
     *
     * @return string
     */
    public function title()
    {
        return $this->_article->name;
    }

    /**
     * Get the article name (legacy method)
     *
     * @return string
     */
    public function name()
    {
        return $this->_article->name;
    }

    /**
     * Get the article URL
     *
     * @return string
     */
    public function url()
    {
        return $this->_article->url;
    }

    /**
     * Get the article handle
     *
     * @return string
     */
    public function handle()
    {
        return $this->_article->url_handle;
    }

    /**
     * Get the article URL handle (legacy method)
     *
     * @return string
     */
    public function url_handle()
    {
        return $this->_article->url_handle;
    }

    /**
     * Get the article content (body)
     *
     * @return string
     */
    public function content()
    {
        return $this->_article->content;
    }

    /**
     * Get the article body
     *
     * @return string
     */
    public function body()
    {
        return $this->_article->content;
    }

    /**
     * Get an excerpt from the article content
     *
     * @param int $words Number of words
     * @return string
     */
    public function excerpt(int $words = 50): string
    {
        $text = strip_tags($this->_article->content);
        return Str::words($text, $words);
    }

    /**
     * Get the summary of the article
     *
     * @return string
     */
    public function summary()
    {
        return $this->_article->summary ?? $this->excerpt(30);
    }

    /**
     * Get date information for the article
     *
     * @return array
     */
    public function date(): array
    {
        return [
            'created' => new Date($this->_article->date_added),
            'updated' => new Date($this->_article->date_modified)
        ];
    }

    /**
     * Get the published date of the article
     *
     * @return Date
     */
    public function published_at(): Date
    {
        return new Date($this->_article->date_added);
    }

    /**
     * Get the created date of the article
     *
     * @return Date
     */
    public function created_at(): Date
    {
        return new Date($this->_article->created_at);
    }

    /**
     * Get the updated date of the article
     *
     * @return Date
     */
    public function updated_at(): Date
    {
        return new Date($this->_article->updated_at ?? $this->_article->date_modified);
    }

    /**
     * Check if the article is published
     *
     * @return bool
     */
    public function published(): bool
    {
        return $this->_article->is_active == 1;
    }

    /**
     * Check if comments are moderated
     *
     * @return bool
     */
    public function moderated(): bool
    {
        return $this->_article->moderate_comments == 1;
    }

    /**
     * Get the count of comments on the article
     *
     * @return int
     */
    public function comments_count()
    {
        return $this->_article->comments_count;
    }

    /**
     * Get the article image
     *
     * @return mixed
     */
    public function image()
    {
        $formatter = new UrlImageFormat($this->_article);
        return $formatter->getLiquidImage();
    }

    /**
     * Get the article image URL
     *
     * @return string|null
     */
    public function image_url(): ?string
    {
        if ($this->has_image()) {
            return $this->_article->image;
        }
        return null;
    }

    /**
     * Check if the article has an image
     *
     * @return bool
     */
    public function has_image(): bool
    {
        return !!$this->_article->hasImage();
    }

    /**
     * Get the article author
     *
     * @return Author
     */
    public function author(): \App\LiquidEngine\LiquidHelpers\Drop\Blog\Author
    {
        return $this->_article->author ? new Author($this->_article->author) : new Author(new Admin([
            'username' => __('global.text.none'),
        ]));
    }

    /**
     * Get the blog this article belongs to
     *
     * @return Blog
     */
    public function blog(): \App\LiquidEngine\LiquidHelpers\Drop\Blog\Blog
    {
        return new Blog($this->_article->blog);
    }

    /**
     * Get the article tags
     *
     * @return array
     */
    public function tags()
    {
        return $this->_article->tags->map(function ($tag): \App\LiquidEngine\LiquidHelpers\Drop\Tag {
            return new Tag($tag);
        })->all();
    }

    /**
     * Get the article status
     *
     * @return string
     */
    public function status(): string
    {
        if (!$this->_article->is_active) {
            return self::STATUS_DRAFT;
        }
        if ($this->_article->date_added > now()) {
            return self::STATUS_SCHEDULED;
        }
        return self::STATUS_PUBLISHED;
    }

    /**
     * Get the article type
     *
     * @return string
     */
    public function type(): string
    {
        if ($this->_article->is_featured) {
            return self::TYPE_FEATURED;
        }
        if ($this->_article->is_pinned) {
            return self::TYPE_PINNED;
        }
        if ($this->_article->is_sponsored) {
            return self::TYPE_SPONSORED;
        }
        return self::TYPE_STANDARD;
    }

    /**
     * Get the article template suffix
     *
     * @return string
     */
    public function template_suffix(): string
    {
        return $this->_article->template_suffix ?? '';
    }

    /**
     * Get the article meta description
     *
     * @return string
     */
    public function meta_description(): string
    {
        return $this->_article->seo_description ?? $this->excerpt(25);
    }

    /**
     * Get the article meta title
     *
     * @return string
     */
    public function meta_title(): string
    {
        return $this->_article->seo_title ?? $this->title();
    }

    /**
     * Get the article reading time in minutes
     *
     * @return int
     */
    public function reading_time(): int
    {
        $words = str_word_count(strip_tags($this->_article->content));
        return max(1, ceil($words / 200)); // Assuming 200 words per minute
    }

    /**
     * Get the article word count
     *
     * @return int
     */
    public function word_count(): int
    {
        return str_word_count(strip_tags($this->_article->content));
    }

    /**
     * Get the article character count
     *
     * @return int
     */
    public function character_count(): int
    {
        return strlen(strip_tags($this->_article->content));
    }

    /**
     * Get social sharing URLs
     *
     * @return array
     */
    public function social_sharing(): array
    {
        $url = urlencode($this->url());
        $title = urlencode($this->title());
        $summary = urlencode($this->excerpt(20));

        return [
            'facebook' => "https://www.facebook.com/sharer/sharer.php?u={$url}",
            'twitter' => "https://twitter.com/intent/tweet?url={$url}&text={$title}",
            'linkedin' => "https://www.linkedin.com/shareArticle?mini=true&url={$url}&title={$title}&summary={$summary}",
            'pinterest' => "https://pinterest.com/pin/create/button/?url={$url}&media={$this->image_url()}&description={$title}",
            'email' => "mailto:?subject={$title}&body={$summary}%0A%0A{$url}"
        ];
    }

    /**
     * Get article analytics data
     *
     * @return array
     */
    public function analytics(): array
    {
        return [
            'views' => $this->_article->views_count ?? 0,
            'likes' => $this->_article->likes_count ?? 0,
            'shares' => $this->_article->shares_count ?? 0,
            'comments' => $this->comments_count(),
            'reading_time' => $this->reading_time(),
            'word_count' => $this->word_count(),
            'character_count' => $this->character_count(),
            'last_viewed_at' => $this->_article->last_viewed_at ? new Date($this->_article->last_viewed_at) : null,
            'published_days_ago' => $this->_article->date_added->diffInDays(now()),
        ];
    }

    /**
     * Get article engagement metrics
     *
     * @return array
     */
    public function engagement(): array
    {
        $views = $this->_article->views_count ?? 0;
        $comments = $this->comments_count();
        $likes = $this->_article->likes_count ?? 0;
        $shares = $this->_article->shares_count ?? 0;

        return [
            'total_engagement' => $views + $comments + $likes + $shares,
            'engagement_rate' => $views > 0 ? round(($comments + $likes + $shares) / $views * 100, 2) : 0,
            'comments_rate' => $views > 0 ? round($comments / $views * 100, 2) : 0,
            'likes_rate' => $views > 0 ? round($likes / $views * 100, 2) : 0,
            'shares_rate' => $views > 0 ? round($shares / $views * 100, 2) : 0,
        ];
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     * @throws Error
     */
    public function toArray(): array
    {
        return [
            'social_sharing' => $this->social_sharing(),
            'analytics' => $this->analytics(),
            'engagement' => $this->engagement(),
        ];
    }

}
