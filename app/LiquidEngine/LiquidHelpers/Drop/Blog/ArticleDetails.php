<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Blog;

use App\Exceptions\Error;
use Illuminate\Support\Str;

class ArticleDetails extends Article
{
    // SEO constants
    public const SEO_PRIORITY_HIGH = 'high';
    public const SEO_PRIORITY_MEDIUM = 'medium';
    public const SEO_PRIORITY_LOW = 'low';

    /**
     * Get the SEO title
     * 
     * @return string
     */
    public function seo_title(): string
    {
        return $this->_article->seo_title ?? $this->title();
    }

    /**
     * Get the SEO description
     * 
     * @return string
     */
    public function seo_description(): string
    {
        return $this->_article->seo_description ?? $this->excerpt(25);
    }

    /**
     * Get the SEO keywords
     * 
     * @return array
     */
    public function seo_keywords(): array
    {
        if ($this->_article->seo_keywords) {
            return array_map('trim', explode(',', $this->_article->seo_keywords));
        }
        
        // Generate keywords from tags and title
        $keywords = collect($this->tags())->map(fn($tag) => $tag->name())->toArray();
        $titleWords = array_filter(explode(' ', Str::lower($this->title())));
        return array_unique(array_merge($keywords, $titleWords));
    }

    /**
     * Get the SEO priority
     * 
     * @return string
     */
    public function seo_priority(): string
    {
        if ($this->_article->is_featured) {
            return self::SEO_PRIORITY_HIGH;
        }
        if ($this->_article->is_pinned) {
            return self::SEO_PRIORITY_HIGH;
        }
        return self::SEO_PRIORITY_MEDIUM;
    }

    /**
     * Get the canonical URL
     * 
     * @return string
     */
    public function canonical_url(): string
    {
        return $this->_article->canonical_url ?? $this->url();
    }

    /**
     * Get the robots meta tag content
     * 
     * @return string
     */
    public function robots(): string
    {
        $robots = [];
        
        // Indexing
        if ($this->_article->no_index) {
            $robots[] = 'noindex';
        } else {
            $robots[] = 'index';
        }
        
        // Following
        if ($this->_article->no_follow) {
            $robots[] = 'nofollow';
        } else {
            $robots[] = 'follow';
        }
        
        // Additional directives
        if ($this->_article->no_archive) {
            $robots[] = 'noarchive';
        }
        if ($this->_article->no_snippet) {
            $robots[] = 'nosnippet';
        }
        
        return implode(',', $robots);
    }

    /**
     * Get Open Graph meta tags
     * 
     * @return array
     */
    public function open_graph(): array
    {
        return [
            'title' => $this->seo_title(),
            'description' => $this->seo_description(),
            'type' => 'article',
            'url' => $this->canonical_url(),
            'image' => $this->image_url(),
            'site_name' => config('app.name'),
            'article:published_time' => $this->published_at()->datetime(),
            'article:modified_time' => $this->updated_at()->datetime(),
            'article:author' => $this->author()->full_name(),
            'article:section' => $this->blog()->name(),
            'article:tag' => $this->seo_keywords(),
        ];
    }

    /**
     * Get Twitter Card meta tags
     * 
     * @return array
     */
    public function twitter_card(): array
    {
        return [
            'card' => 'summary_large_image',
            'title' => $this->seo_title(),
            'description' => $this->seo_description(),
            'image' => $this->image_url(),
            'site' => '@' . config('app.name'),
            'creator' => '@' . $this->author()->username(),
        ];
    }

    /**
     * Get structured data for JSON-LD
     * 
     * @return array
     */
    public function structured_data(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => $this->title(),
            'description' => $this->seo_description(),
            'image' => $this->image_url(),
            'datePublished' => $this->published_at()->datetime(),
            'dateModified' => $this->updated_at()->datetime(),
            'author' => [
                '@type' => 'Person',
                'name' => $this->author()->full_name(),
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => config('app.name'),
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => config('app.logo'),
                ],
            ],
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => $this->canonical_url(),
            ],
            'keywords' => implode(', ', $this->seo_keywords()),
            'wordCount' => $this->word_count(),
            'articleSection' => $this->blog()->name(),
        ];
    }

    /**
     * Get article reading time in minutes
     * 
     * @return int
     */
    public function reading_time(): int
    {
        $words = str_word_count(strip_tags($this->content()));
        return max(1, ceil($words / 200)); // Assuming 200 words per minute reading speed
    }

    /**
     * Get article word count
     * 
     * @return int
     */
    public function word_count(): int
    {
        return str_word_count(strip_tags($this->content()));
    }

    /**
     * Get article character count
     * 
     * @return int
     */
    public function character_count(): int
    {
        return strlen(strip_tags($this->content()));
    }

    /**
     * Get article last viewed date
     * 
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Date|null
     */
    public function last_viewed(): ?\App\LiquidEngine\LiquidHelpers\Drop\Date
    {
        return $this->_article->last_viewed ? new \App\LiquidEngine\LiquidHelpers\Drop\Date($this->_article->last_viewed) : null;
    }

    /**
     * Get article view count
     * 
     * @return int
     */
    public function views(): int
    {
        return (int) $this->_article->views;
    }

    /**
     * Get article like count
     * 
     * @return int
     */
    public function likes(): int
    {
        return (int) $this->_article->likes;
    }

    /**
     * Get article share count
     * 
     * @return int
     */
    public function shares(): int
    {
        return (int) $this->_article->shares;
    }

    /**
     * Get article comment count
     * 
     * @return int
     */
    public function comments_count(): int
    {
        return (int) $this->_article->comments_count;
    }

    /**
     * Get article engagement metrics
     * 
     * @return array
     */
    public function engagement(): array
    {
        $views = $this->views();
        if ($views === 0) {
            return [
                'total' => 0,
                'rate' => 0,
                'comments_rate' => 0,
                'likes_rate' => 0,
                'shares_rate' => 0
            ];
        }

        return [
            'total' => $this->comments_count() + $this->likes() + $this->shares(),
            'rate' => round(($this->comments_count() + $this->likes() + $this->shares()) / $views * 100, 2),
            'comments_rate' => round($this->comments_count() / $views * 100, 2),
            'likes_rate' => round($this->likes() / $views * 100, 2),
            'shares_rate' => round($this->shares() / $views * 100, 2)
        ];
    }

    /**
     * Get article analytics data
     * 
     * @return array
     */
    public function analytics(): array
    {
        $publishedDate = $this->published_at();
        $now = new \Carbon\Carbon();
        $daysSincePublished = $publishedDate ? $now->diffInDays(\Carbon\Carbon::parse($this->_article->date_added)) : 0;

        return [
            'views' => $this->views(),
            'likes' => $this->likes(),
            'shares' => $this->shares(),
            'comments' => $this->comments_count(),
            'reading_time' => $this->reading_time(),
            'word_count' => $this->word_count(),
            'character_count' => $this->character_count(),
            'last_viewed' => $this->last_viewed(),
            'days_since_published' => $daysSincePublished
        ];
    }

    /**
     * Get social sharing URLs
     * 
     * @return array
     */
    public function social_sharing(): array
    {
        $url = urlencode($this->url());
        $title = urlencode($this->title());
        $excerpt = urlencode($this->excerpt());

        return [
            'facebook' => "https://www.facebook.com/sharer/sharer.php?u={$url}",
            'twitter' => "https://twitter.com/intent/tweet?url={$url}&text={$title}",
            'linkedin' => "https://www.linkedin.com/shareArticle?mini=true&url={$url}&title={$title}&summary={$excerpt}",
            'pinterest' => "https://pinterest.com/pin/create/button/?url={$url}&description={$title}",
            'email' => "mailto:?subject={$title}&body={$excerpt}%0A%0A{$url}"
        ];
    }

    /**
     * Shopify compatibility: excerpt
     * Returns a short excerpt of the article content
     * @param int $words
     * @return string
     */
    public function excerpt(int $words = 30): string
    {
        return Str::words(strip_tags($this->content()), $words);
    }

    /**
     * Shopify compatibility: created_at
     * Returns the article creation date as a Date Drop
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Date
     */
    public function created_at(): \App\LiquidEngine\LiquidHelpers\Drop\Date
    {
        return new \App\LiquidEngine\LiquidHelpers\Drop\Date($this->_article->created_at);
    }

    /**
     * Shopify compatibility: updated_at
     * Returns the article last update date as a Date Drop
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Date
     */
    public function updated_at(): \App\LiquidEngine\LiquidHelpers\Drop\Date
    {
        return new \App\LiquidEngine\LiquidHelpers\Drop\Date($this->_article->updated_at);
    }

    /**
     * Shopify compatibility: published_at
     * Returns the article published date as a Date Drop
     * @return \App\LiquidEngine\LiquidHelpers\Drop\Date
     */
    public function published_at(): \App\LiquidEngine\LiquidHelpers\Drop\Date
    {
        return new \App\LiquidEngine\LiquidHelpers\Drop\Date($this->_article->published_at ?? $this->_article->date_added);
    }

    /**
     * Shopify compatibility: author
     * Returns the article author Drop
     * @return Author
     */
    public function author(): Author
    {
        return new Author($this->_article->author);
    }

    /**
     * Shopify compatibility: tags
     * Returns the article tags as an array
     * @return array
     */
    public function tags(): array
    {
        return $this->_article->tags ?? [];
    }

    /**
     * Shopify compatibility: metafields
     * Returns the article metafields as an array
     * @return array
     */
    public function metafields(): array
    {
        return $this->_article->metafields ?? [];
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     * @throws Error
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'seo_title' => $this->seo_title(),
            'seo_description' => $this->seo_description(),
            'seo_keywords' => $this->seo_keywords(),
            'seo_priority' => $this->seo_priority(),
            'canonical_url' => $this->canonical_url(),
            'robots' => $this->robots(),
            'open_graph' => $this->open_graph(),
            'twitter_card' => $this->twitter_card(),
            'structured_data' => $this->structured_data(),
            'reading_time' => $this->reading_time(),
            'word_count' => $this->word_count(),
            'character_count' => $this->character_count(),
            'analytics' => $this->analytics(),
            'engagement' => $this->engagement(),
            'social_sharing' => $this->social_sharing(),
            'excerpt' => $this->excerpt(),
            'created_at' => $this->created_at(),
            'updated_at' => $this->updated_at(),
            'published_at' => $this->published_at(),
            'author' => $this->author(),
            'tags' => $this->tags(),
            'metafields' => $this->metafields()
        ]);
    }
}
