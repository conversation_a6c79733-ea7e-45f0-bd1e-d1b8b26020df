<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Exceptions\Error;
use App\Helper\Format;
use App\Models\Product\Product as ProductModel;
use App\LiquidEngine\LiquidHelpers\Drop\Product\BundleVariant;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;

/**
 * BundleProduct Drop Class
 * 
 * This class represents a product within a bundle in Liquid templates, providing access to product properties
 * and methods for price calculations and bundle-specific features.
 * 
 * Properties:
 * - hide_thumb: Whether to hide the product thumbnail in the bundle display
 * - visible: Whether the product is visible in product details view
 * - individual_price_enabled: Whether individual pricing is enabled for this bundle item
 * - individual_price: Individual price of the product when individual pricing is enabled
 * - individual_price_input: Raw individual price value for calculations
 * - individual_price_formatted: Formatted individual price for display
 * - individual_price_parts: Individual price components (currency, amount, etc.)
 * - individual_price_total: Total individual price including quantity
 * - individual_price_total_input: Raw total individual price for calculations
 * - individual_price_total_formatted: Formatted total individual price for display
 * - individual_price_total_parts: Total individual price components
 * - individual_quantity_enabled: Whether individual quantity selection is enabled
 * - individual_quantity: Selected quantity for this bundle item
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class BundleProduct extends ProductDetails
{
    /**
     * @var ProductModel $_bundle The parent bundle product
     */
    protected ProductModel $_bundle;

    /**
     * Create a new BundleProduct instance.
     *
     * @param ProductModel $product The product model instance
     * @param ProductModel $bundle The parent bundle product model
     */
    public function __construct(ProductModel $product, ProductModel $bundle)
    {
        parent::__construct($product);
        $this->_bundle = $bundle;
    }

    /**
     * Get the discount (always null for bundle products)
     * 
     * @return null Always returns null as bundle products don't support discounts
     */
    public function discount(): null
    {
        return null;
    }

    /**
     * Get the discounted price from (always null for bundle products)
     * 
     * @return null Always returns null as bundle products don't support discounts
     */
    public function price_from_discounted(): null
    {
        return null;
    }

    /**
     * Get the raw discounted price from (always null for bundle products)
     * 
     * @return null Always returns null as bundle products don't support discounts
     */
    public function price_from_discounted_input(): null
    {
        return null;
    }

    /**
     * Get the formatted discounted price from (always null for bundle products)
     * 
     * @return null Always returns null as bundle products don't support discounts
     */
    public function price_from_discounted_formatted(): null
    {
        return null;
    }

    /**
     * Get the discounted price from parts (always null for bundle products)
     * 
     * @return null Always returns null as bundle products don't support discounts
     */
    public function price_from_discounted_parts(): null
    {
        return null;
    }

    /**
     * Check if the product is discounted (always false for bundle products)
     * 
     * @return bool Always returns false as bundle products don't support discounts
     */
    public function is_discounted(): bool
    {
        return false;
    }

    /**
     * Get the product to discount price (always 0 for bundle products)
     * 
     * @return float Always returns 0 as bundle products don't support discounts
     */
    public function product_to_discount_price(): float
    {
        return 0.0;
    }

    /**
     * Get the product to discount price to (always 0 for bundle products)
     * 
     * @return float Always returns 0 as bundle products don't support discounts
     */
    public function product_to_discount_price_to(): float
    {
        return 0.0;
    }

    /**
     * Get the minimum price with discount (always 0 for bundle products)
     * 
     * @return float Always returns 0 as bundle products don't support discounts
     */
    public function min_price_with_discounted(): float
    {
        return 0.0;
    }

    /**
     * Get the maximum price with discount (always 0 for bundle products)
     * 
     * @return float Always returns 0 as bundle products don't support discounts
     */
    public function max_price_with_discounted(): float
    {
        return 0.0;
    }

    /**
     * Get the bundle variant
     * 
     * @return BundleVariant The bundle variant instance
     */
    public function variant(): BundleVariant
    {
        if ($this->_variant) {
            return new BundleVariant($this->_variant, $this->_product, $this->_bundle);
        }

        return new BundleVariant($this->_product->variant, $this->_product, $this->_bundle);
    }

    /**
     * Get all bundle variants
     * 
     * @return array<BundleVariant> Array of bundle variant instances
     */
    public function variants(): array
    {
        $variants = [];
        if ($this->total_variants() > 0 && $this->_product->relationLoaded('variants')) {
            $variants = $this->_product->variants->map(function ($variant): BundleVariant {
                return new BundleVariant($variant, $this->_product, $this->_bundle);
            })->all();
        }

        return $variants;
    }

    /**
     * Check if the product thumbnail should be hidden
     * 
     * @return bool Whether the thumbnail should be hidden in the bundle display
     */
    public function hide_thumb(): bool
    {
        return !!$this->_product->pivot->hide_thumb;
    }

    /**
     * Check if the product is visible in product details
     * 
     * @return bool Whether the product is visible in product details view
     */
    public function visible(): bool
    {
        return !!$this->_product->pivot->visible_product_details;
    }

    /**
     * Check if individual pricing is enabled
     * 
     * @return bool Whether individual pricing is enabled for this bundle item
     */
    public function individual_price_enabled(): bool
    {
        return !!$this->_product->pivot->individual_price_enabled;
    }

    /**
     * Get the individual price
     * 
     * @return int|null The individual price or null if not enabled
     */
    public function individual_price(): ?int
    {
        if ($this->individual_price_enabled()) {
            return $this->_product->pivot->individual_price;
        }

        return null;
    }

    /**
     * Get the raw individual price
     * 
     * @return string|int|null The raw individual price or null if not enabled
     */
    public function individual_price_input(): string|int|null
    {
        if ($this->individual_price_enabled()) {
            return Format::moneyInput($this->_product->pivot->individual_price);
        }

        return null;
    }

    /**
     * Get the formatted individual price
     * 
     * @return string|null The formatted individual price or null if not enabled
     */
    public function individual_price_formatted(): ?string
    {
        if ($this->individual_price_enabled()) {
            return Format::money($this->_product->pivot->individual_price);
        }

        return null;
    }

    /**
     * Get the individual price parts
     * 
     * @return array{currency: string, amount: float, formatted: string}|null The individual price parts or null if not enabled
     */
    public function individual_price_parts(): ?array
    {
        if ($this->individual_price_enabled()) {
            return PriceParts::format($this->individual_price_input());
        }

        return null;
    }

    /**
     * Get the total individual price
     * 
     * @return int|float|null The total individual price or null if not enabled
     */
    public function individual_price_total(): int|float|null
    {
        if ($this->individual_price_enabled()) {
            return $this->_product->pivot->individual_price * $this->individual_quantity_enabled();
        }

        return null;
    }

    /**
     * Get the raw total individual price
     * 
     * @return string|int|null The raw total individual price or null if not enabled
     */
    public function individual_price_total_input(): string|int|null
    {
        if ($this->individual_price_enabled()) {
            return Format::moneyInput($this->_product->pivot->individual_price * $this->individual_quantity_enabled());
        }

        return null;
    }

    /**
     * Get the formatted total individual price
     * 
     * @return string|null The formatted total individual price or null if not enabled
     */
    public function individual_price_total_formatted(): ?string
    {
        if ($this->individual_price_enabled()) {
            return Format::money($this->_product->pivot->individual_price * $this->individual_quantity_enabled());
        }

        return null;
    }

    /**
     * Get the total individual price parts
     * 
     * @return array{currency: string, amount: float, formatted: string}|null The total individual price parts or null if not enabled
     */
    public function individual_price_total_parts(): ?array
    {
        if ($this->individual_price_enabled()) {
            return PriceParts::format($this->individual_price_input());
        }

        return null;
    }

    /**
     * Check if individual quantity is enabled
     * 
     * @return bool Whether individual quantity selection is enabled for this bundle item
     */
    public function individual_quantity_enabled(): bool
    {
        return !!$this->_product->pivot->individual_qty_enabled;
    }

    /**
     * Get the individual quantity
     * 
     * @return int The individual quantity or 1 if not enabled
     */
    public function individual_quantity(): int
    {
        if ($this->individual_quantity_enabled()) {
            return $this->_product->pivot->qty;
        }

        return 1;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     hide_thumb: bool,
     *     visible: bool,
     *     individual_price_enabled: bool,
     *     individual_price: int|null,
     *     individual_price_input: string|int|null,
     *     individual_price_formatted: string|null,
     *     individual_price_parts: array{currency: string, amount: float, formatted: string}|null,
     *     individual_price_total: int|float|null,
     *     individual_price_total_input: string|int|null,
     *     individual_price_total_formatted: string|null,
     *     individual_price_total_parts: array{currency: string, amount: float, formatted: string}|null,
     *     individual_quantity_enabled: bool,
     *     individual_quantity: int
     * } The bundle product data
     * @throws Error
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'hide_thumb' => $this->hide_thumb(),
            'visible' => $this->visible(),
            'individual_price_enabled' => $this->individual_price_enabled(),
            'individual_price' => $this->individual_price(),
            'individual_price_input' => $this->individual_price_input(),
            'individual_price_formatted' => $this->individual_price_formatted(),
            'individual_price_parts' => $this->individual_price_parts(),
            'individual_price_total' => $this->individual_price_total(),
            'individual_price_total_input' => $this->individual_price_total_input(),
            'individual_price_total_formatted' => $this->individual_price_total_formatted(),
            'individual_price_total_parts' => $this->individual_price_total_parts(),
            'individual_quantity_enabled' => $this->individual_quantity_enabled(),
            'individual_quantity' => $this->individual_quantity(),
        ]);
    }
}
