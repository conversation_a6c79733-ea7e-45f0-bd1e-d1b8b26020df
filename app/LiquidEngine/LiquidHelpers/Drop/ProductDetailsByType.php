<?php

declare(strict_types=1);

/**
 * ProductDetailsByType Drop Class
 *
 * This class implements a factory pattern for returning the appropriate Drop class
 * (ProductDetails or BundleDetails) based on the product type. It is used to abstract
 * the instantiation logic for Liquid templates and ensure type safety.
 *
 * The class maintains a mapping of product types to their corresponding Drop classes,
 * defaulting to the simple product type if an unknown type is encountered.
 *
 * Usage:
 *   $drop = ProductDetailsByType::get($productModel);
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Models\Product\Product as ProductModel;

class ProductDetailsByType
{
    /**
     * Mapping of product types to Drop classes.
     * Maps product type constants to their corresponding Drop class implementations.
     * Unknown types will default to the simple product type.
     *
     * @var array<string, class-string<ProductDetails|BundleDetails>>
     */
    public const PRODUCT_TYPES = [
        ProductModel::TYPE_SIMPLE => ProductDetails::class,
        ProductModel::TYPE_DIGITAL => ProductDetails::class,
        ProductModel::TYPE_MULTIPLE => ProductDetails::class,
        ProductModel::TYPE_BUNDLE => BundleDetails::class,
    ];

    /**
     * ProductDetailsByType constructor (protected, static factory only).
     * Prevents direct instantiation of the factory class.
     */
    protected function __construct() {}

    /**
     * Get the appropriate Drop instance for the given product.
     * Returns a ProductDetails or BundleDetails instance based on the product type.
     * Falls back to the simple product type if the product type is unknown.
     *
     * @param ProductModel $product The product model instance
     * @return ProductDetails|BundleDetails The corresponding Drop instance
     * @throws \InvalidArgumentException If the product type is invalid
     */
    public static function get(ProductModel $product): ProductDetails|BundleDetails
    {
        $className = static::PRODUCT_TYPES[$product->type] ?? static::PRODUCT_TYPES[ProductModel::TYPE_SIMPLE];
        return new $className($product);
    }
}
