<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.4.2019 г.
 * Time: 12:02 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\LiquidEngine\LiquidHelpers\Drop\StoreFront\Breadcrumb;

/**
 * Breadcrumbs Drop class for handling breadcrumb navigation in Liquid templates.
 *
 * This class manages the breadcrumb navigation for the application, implementing
 * the Singleton pattern to maintain a single instance of breadcrumbs. It provides
 * methods for adding breadcrumb items, accessing them in various formats (Shopify,
 * SEO, Analytics), and generating structured data for search engines.
 *
 * Usage:
 *   {{ breadcrumbs.items }}
 *   {{ breadcrumbs.structured_data }}
 *   {{ breadcrumbs.shopify }}
 *   {{ breadcrumbs.seo }}
 *   {{ breadcrumbs.analytics }}
 *
 * Properties:
 * - items: array<Breadcrumb>
 * - structured_data: array<string, mixed>
 * - accessibility: array{aria_label: string, role: string}
 * - shopify: array{items: array<Breadcrumb>, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}}
 * - seo: array{items: array<Breadcrumb>, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}}
 * - analytics: array{items: array<Breadcrumb>, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}}
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class Breadcrumbs
{
    /**
     * The singleton instance.
     * Stores the single instance of the Breadcrumbs class.
     *
     * @var self|null
     */
    protected static ?self $instance = null;

    /**
     * The collection of breadcrumb items.
     * Stores the list of breadcrumb navigation items.
     *
     * @var array<Breadcrumb>
     */
    protected array $_items = [];

    /**
     * Private constructor to prevent direct instantiation.
     * Forces the use of the singleton pattern through the instance() method.
     */
    protected function __construct()
    {
    }

    /**
     * Get the singleton instance.
     * Creates a new instance if one doesn't exist.
     *
     * @return self The singleton instance
     */
    public static function instance(): self
    {
        if (is_null(static::$instance)) {
            static::$instance = new static();
        }

        return static::$instance;
    }

    /**
     * Add a new breadcrumb item.
     * Adds a breadcrumb to the navigation trail.
     *
     * @param string $text The breadcrumb text
     * @param string|null $link The breadcrumb link
     * @return self For method chaining
     */
    public function add(string $text, ?string $link = null): self
    {
        $this->_items[] = new Breadcrumb($text, $link);
        return $this;
    }

    /**
     * Reset the breadcrumbs collection.
     * Clears all breadcrumb items.
     *
     * @return self For method chaining
     */
    public function reset(): self
    {
        $this->_items = [];
        return $this;
    }

    /**
     * Get all breadcrumb items.
     * Returns the complete collection of breadcrumb items.
     *
     * @return array<Breadcrumb> The breadcrumb items
     */
    public function all(): array
    {
        return $this->_items;
    }

    /**
     * Get accessibility attributes.
     * Returns ARIA attributes for accessibility.
     *
     * @return array{
     *     aria_label: string,
     *     role: string
     * } The accessibility attributes
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => 'Breadcrumb navigation',
            'role' => 'navigation'
        ];
    }

    /**
     * Get structured data for SEO.
     * Returns Schema.org structured data for breadcrumbs.
     *
     * @return array<string, mixed> The structured data
     */
    public function structured_data(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => array_map(function(Breadcrumb $item, int $index) {
                return [
                    '@type' => 'ListItem',
                    'position' => $index + 1,
                    'name' => $item->text(),
                    'item' => $item->link()
                ];
            }, $this->_items, array_keys($this->_items))
        ];
    }

    /**
     * Convert to Shopify format.
     * Returns breadcrumbs in Shopify-compatible format.
     *
     * @return array{
     *     items: array<Breadcrumb>,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * } The Shopify-compatible data
     */
    public function toShopifyArray(): array
    {
        return [
            'items' => $this->all(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to SEO array format.
     * Returns breadcrumbs in SEO-compatible format.
     *
     * @return array{
     *     items: array<Breadcrumb>,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * } The SEO-compatible data
     */
    public function toSeoArray(): array
    {
        return [
            'items' => $this->all(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to analytics array format.
     * Returns breadcrumbs in analytics-compatible format.
     *
     * @return array{
     *     items: array<Breadcrumb>,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * } The analytics-compatible data
     */
    public function toAnalyticsArray(): array
    {
        return [
            'items' => $this->all(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Get the collection as a plain array.
     * Returns a complete array representation of the breadcrumbs,
     * including all formats (Shopify, SEO, Analytics).
     *
     * @return array{
     *     items: array<Breadcrumb>,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string},
     *     shopify: array{items: array<Breadcrumb>, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     seo: array{items: array<Breadcrumb>, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     analytics: array{items: array<Breadcrumb>, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}}
     * } The complete breadcrumbs data
     */
    public function toArray(): array
    {
        return [
            'items' => $this->all(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics' => $this->toAnalyticsArray()
        ];
    }
}
