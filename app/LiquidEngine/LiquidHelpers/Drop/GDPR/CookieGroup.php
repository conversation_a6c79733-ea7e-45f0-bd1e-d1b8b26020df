<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\GDPR;

use Modules\Apps\Others\Gdpr\Models\CookieGroup as CookieGroupModel;
use Modules\Apps\Others\Gdpr\Models\CookieProviders;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractTextContent;

class CookieGroup extends AbstractTextContent
{

    /**
     * @var \Illuminate\Database\Eloquent\Model|CookieGroupModel $_page
     */
    protected \Illuminate\Database\Eloquent\Model|CookieGroupModel $_page;

    /**
     * Page constructor.
     * @param \Illuminate\Database\Eloquent\Model|CookieGroupModel $page
     */
    public function __construct(\Illuminate\Database\Eloquent\Model|CookieGroupModel $page)
    {
        parent::__construct($page);
    }

    /**
     * @return bool
     */
    public function default(): bool
    {
        return !!$this->_page->getAttribute('default');
    }

    /**
     * @return string
     */
    public function mapping()
    {
        return $this->_page->getAttribute('mapping');
    }

    /**
     * @return string
     */
    public function content(): string
    {
        return $this->_page->getAttribute('description');
    }

    /**
     * @return array
     */
    public function providers()
    {
        $providers = [];
        if ($this->_page->relationLoaded('providers')) {
            $providers = $this->_page->providers->map(function (CookieProviders $provider): \App\LiquidEngine\LiquidHelpers\Drop\GDPR\CookieProvider {
                return new CookieProvider($provider);
            })->all();
        }

        return $providers;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'default' => $this->default(),
            'mapping' => $this->mapping(),
            'providers' => $this->providers(),
        ]);
    }
}
