<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\GDPR;

use Modules\Apps\Others\Gdpr\Models\CookieProviders;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractTextContent;

class <PERSON>ieProvider extends AbstractTextContent
{

    /**
     * @var \Illuminate\Database\Eloquent\Model|CookieProviders $_page
     */
    protected \Illuminate\Database\Eloquent\Model|CookieProviders $_page;

    /**
     * Page constructor.
     * @param \Illuminate\Database\Eloquent\Model|CookieProviders $page
     */
    public function __construct(\Illuminate\Database\Eloquent\Model|CookieProviders $page)
    {
        parent::__construct($page);
    }

    /**
     * @return string
     */
    public function mapping()
    {
        return $this->_page->getAttribute('mapping');
    }

    /**
     * @return string
     */
    public function content(): string
    {
        return $this->_page->getAttribute('description');
    }

    /**
     * @return array
     */
    public function cookies(): array
    {
        return array_filter(array_map('trim', explode(',', $this->_page->getAttribute('cookies'))));
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'mapping' => $this->mapping(),
            'cookies' => $this->cookies(),
        ]);
    }
}
