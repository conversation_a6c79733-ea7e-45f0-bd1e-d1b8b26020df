<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\GDPR;

use Modules\Apps\Others\Gdpr\Models\Policy as PolicyModel;
use App\LiquidEngine\LiquidHelpers\Drop\Page;

class Policy extends Page
{

    /**
     * @var \App\Models\Page\Page|\Illuminate\Database\Eloquent\Model|PolicyModel $_page
     */
    protected \App\Models\Page\Page|\Illuminate\Database\Eloquent\Model|PolicyModel $_page;

    /**
     * Page constructor.
     * @param \App\Models\Page\Page|\Illuminate\Database\Eloquent\Model|PolicyModel $page
     */
    public function __construct(\App\Models\Page\Page|\Illuminate\Database\Eloquent\Model|PolicyModel $page)
    {
        parent::__construct($page);
    }

    /**
     * @return bool
     */
    public function optional(): bool
    {
        return !!$this->_page->getAttribute('optional');
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'optional' => $this->optional()
        ]);
    }
}
