<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.4.2019 г.
 * Time: 12:47 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Product;

use App\Helper\Conversion\Unit;
use App\Models\Layout\FormFieldOptions;
use App\Models\Product\Options;
use App\LiquidEngine\LiquidHelpers\Drop\StoreFront\FormField;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use App\Models\Product\Options as OptionModel;
use Illuminate\Support\Str;

/**
 * Class Option
 *
 * Represents a product option in the Liquid template system.
 * Extends FormField to provide additional product-specific functionality.
 *
 * Properties:
 * @property-read OptionModel $_option The underlying option model
 * @property-read string $name_amount The name with amount
 * @property-read bool $multiple Whether multiple values are allowed
 * @property-read bool $allow_options Whether options are allowed
 * @property-read float $amount The option amount
 * @property-read float $amount_input The raw amount input
 * @property-read string $amount_formatted The formatted amount
 * @property-read array $amount_parts The structured amount parts
 * @property-read float $min The minimum value
 * @property-read float $min_square The minimum square value
 * @property-read float $step The step value
 * @property-read mixed $default_value The default value
 * @property-read string $symbol The option symbol
 * @property-read string $symbol_value The value symbol
 * @property-read string $symbol_product The product symbol
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop\Product
 */
class Option extends FormField
{
    protected OptionModel $_option;

    /**
     * Option constructor.
     *
     * @param Options $option The option model instance
     */
    public function __construct(Options $option)
    {
        $this->_option = $option;
        parent::__construct($option);
    }

    /**
     * Get the name with amount.
     *
     * @return string|null
     */
    public function name_amount(): ?string
    {
        return $this->field->name_amount;
    }

    /**
     * Check if multiple values are allowed.
     *
     * @return bool
     */
    public function multiple(): bool
    {
        return (bool) $this->field->multiple;
    }

    /**
     * Check if options are allowed.
     *
     * @return bool
     */
    public function allow_options(): bool
    {
        return (bool) $this->field->allow_options;
    }

    /**
     * Get the option amount.
     *
     * @return float
     */
    public function amount(): float
    {
        return (float) $this->field->amount;
    }

    /**
     * Get the raw amount input.
     *
     * @return float
     */
    public function amount_input(): float
    {
        return (float) $this->field->amount_input;
    }

    /**
     * Get the formatted amount.
     *
     * @return string
     */
    public function amount_formatted(): string
    {
        return (string) $this->field->amount_formatted;
    }

    /**
     * Get the structured amount parts.
     *
     * @return array{amount: float, currency: string, formatted: string}
     */
    public function amount_parts(): array
    {
        return PriceParts::format($this->amount_input());
    }

    /**
     * Get the minimum value.
     *
     * @return float
     */
    public function min(): float
    {
        return (float) $this->field->min;
    }

    /**
     * Get the minimum square value.
     *
     * @return float
     */
    public function min_square(): float
    {
        return (float) $this->field->min_square;
    }

    /**
     * Get the minimum square label.
     *
     * @return string|null
     */
    public function min_square_label(): ?string
    {
        if (in_array($this->type(), ['length', 'weight', 'square']) && $this->min_square() > 0) {
            return __('global.unit.min.' . $this->type() . '.' . Unit::convertLengthSymbol($this->field->product_symbol), ['min' => $this->min_square()]);
        }

        return null;
    }

    /**
     * Get the step value.
     *
     * @return float
     */
    public function step(): float
    {
        return (float) $this->field->step;
    }

    /**
     * Get the default value.
     *
     * @return mixed
     */
    public function default_value()
    {
        return $this->field->default_value;
    }

    /**
     * Get the option symbol.
     *
     * @return string
     */
    public function symbol(): string
    {
        return (string) $this->field->symbol;
    }

    /**
     * Get the value symbol.
     *
     * @return string
     */
    public function symbol_value(): string
    {
        return (string) $this->field->symbol_value;
    }

    /**
     * Get the product symbol.
     *
     * @return string
     */
    public function symbol_product(): string
    {
        return (string) $this->field->symbol_product;
    }

    /**
     * Get the product label.
     *
     * @return string|null
     */
    public function product_label(): ?string
    {
        if (!in_array($this->type(), ['length', 'weight', 'square'])) {
            return null;
        }

        return __('global.unit.product.' . $this->type() . '.' . Unit::convertLengthSymbol($this->field->product_symbol));
    }

    /**
     * Get the option values.
     *
     * @return array<OptionValue>
     */
    public function options(): array
    {
        $options = [];
        if ($this->field->relationLoaded('options')) {
            $options = $this->field->options->map(function (FormFieldOptions $option): OptionValue {
                $active = false;
                if (in_array($this->type(), ['checkbox', 'radio', 'select'])) {
                    $active = is_array($this->value) ? in_array($option->id, $this->value) : $this->value == $option->id;
                }

                return new OptionValue($option, $active);
            })->all();
        }

        return $options;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     name_amount: string|null,
     *     multiple: bool,
     *     allow_options: bool,
     *     amount: float,
     *     amount_input: float,
     *     amount_formatted: string,
     *     amount_parts: array,
     *     step: float,
     *     min: float,
     *     min_square: float,
     *     min_square_label: string|null,
     *     default_value: mixed,
     *     symbol: string,
     *     symbol_value: string,
     *     symbol_product: string,
     *     product_label: string|null
     * }
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'name_amount' => $this->name_amount(),
            'multiple' => $this->multiple(),
            'allow_options' => $this->allow_options(),
            'amount' => $this->amount(),
            'amount_input' => $this->amount_input(),
            'amount_formatted' => $this->amount_formatted(),
            'amount_parts' => $this->amount_parts(),
            'step' => $this->step(),
            'min' => $this->min(),
            'min_square' => $this->min_square(),
            'min_square_label' => $this->min_square_label(),
            'default_value' => $this->default_value(),
            'symbol' => $this->symbol(),
            'symbol_value' => $this->symbol_value(),
            'symbol_product' => $this->symbol_product(),
            'product_label' => $this->product_label(),
        ]);
    }

    /**
     * Get the option ID.
     *
     * @return int
     */
    public function option_id(): int
    {
        return $this->_option->id;
    }

    /**
     * Get the option values in Vue format.
     *
     * @return array
     */
    public function values(): array
    {
        return $this->_option->option_values->toVue();
    }

    /**
     * Get the option in Vue format.
     *
     * @return array
     */
    public function to_vue(): array
    {
        return $this->_option->toVue();
    }

    /**
     * Get the option type (color, size, material, etc.)
     * Shopify-compatible method.
     *
     * @return string
     */
    public function type(): string
    {
        return $this->_option->type ?? 'select';
    }

    /**
     * Get the option position/order.
     * Shopify-compatible method.
     *
     * @return int
     */
    public function position(): int
    {
        return $this->_option->sort_order ?? 0;
    }

    /**
     * Get the option handle (URL-safe identifier).
     * Shopify-compatible method.
     *
     * @return string
     */
    public function handle(): string
    {
        return Str::slug($this->name());
    }

    /**
     * Get all available option values.
     * Enhanced Shopify-compatible method.
     *
     * @return array
     */
    public function option_values(): array
    {
        return $this->values();
    }

    /**
     * Get the first available option value.
     * Shopify-compatible method.
     *
     * @return array|null
     */
    public function first_value(): ?array
    {
        $values = $this->values();
        return !empty($values) ? $values[0] : null;
    }

    /**
     * Get the selected option value.
     * Shopify-compatible method.
     *
     * @return array|null
     */
    public function selected_value(): ?array
    {
        $values = $this->values();
        foreach ($values as $value) {
            if (isset($value['selected']) && $value['selected']) {
                return $value;
            }
        }
        return null;
    }

    /**
     * Check if option has multiple values.
     *
     * @return bool
     */
    public function has_multiple_values(): bool
    {
        return count($this->values()) > 1;
    }

    /**
     * Get the number of values.
     *
     * @return int
     */
    public function values_count(): int
    {
        return count($this->values());
    }

    /**
     * Check if the option is required.
     *
     * @return bool
     */
    public function required(): bool
    {
        return (bool) $this->_option->required;
    }

    /**
     * Get the display name.
     *
     * @return string
     */
    public function display_name(): string
    {
        return (string) $this->_option->display_name;
    }

    /**
     * Get the option title.
     *
     * @return string
     */
    public function title(): string
    {
        return (string) $this->_option->title;
    }

    /**
     * Get the option label.
     *
     * @return string
     */
    public function label(): string
    {
        return (string) $this->_option->label;
    }

    /**
     * Check if the option is visible.
     *
     * @return bool
     */
    public function visible(): bool
    {
        return (bool) $this->_option->visible;
    }

    /**
     * Get the option description.
     *
     * @return string|null
     */
    public function description(): ?string
    {
        return $this->_option->description;
    }

    /**
     * Get the help text.
     *
     * @return string|null
     */
    public function help_text(): ?string
    {
        return $this->_option->help_text;
    }

    /**
     * Get the placeholder text.
     *
     * @return string|null
     */
    public function placeholder(): ?string
    {
        return $this->_option->placeholder;
    }

    /**
     * Check if the option has values.
     *
     * @return bool
     */
    public function has_values(): bool
    {
        return !empty($this->values());
    }

    /**
     * Get a value by name.
     *
     * @param string $name The value name
     * @return array|null
     */
    public function value_by_name(string $name): ?array
    {
        $values = $this->values();
        foreach ($values as $value) {
            if (isset($value['name']) && $value['name'] === $name) {
                return $value;
            }
        }
        return null;
    }

    /**
     * Get a value by handle.
     *
     * @param string $handle The value handle
     * @return array|null
     */
    public function value_by_handle(string $handle): ?array
    {
        $values = $this->values();
        foreach ($values as $value) {
            if (isset($value['handle']) && $value['handle'] === $handle) {
                return $value;
            }
        }
        return null;
    }

    /**
     * Get all value names.
     *
     * @return array<string>
     */
    public function value_names(): array
    {
        return array_map(function ($value) {
            return $value['name'] ?? '';
        }, $this->values());
    }

    /**
     * Get all value handles.
     *
     * @return array<string>
     */
    public function value_handles(): array
    {
        return array_map(function ($value) {
            return $value['handle'] ?? '';
        }, $this->values());
    }

    /**
     * Convert to select format.
     *
     * @return array
     */
    public function to_select(): array
    {
        return [
            'id' => $this->option_id(),
            'name' => $this->name(),
            'values' => $this->values(),
            'selected' => $this->selected_value(),
            'required' => $this->required(),
            'type' => $this->type(),
            'position' => $this->position(),
        ];
    }

    /**
     * Get the CSS class.
     *
     * @return string
     */
    public function css_class(): string
    {
        $classes = ['option', 'option-' . $this->type()];
        if ($this->required()) {
            $classes[] = 'required';
        }
        if ($this->multiple()) {
            $classes[] = 'multiple';
        }
        return implode(' ', $classes);
    }

    /**
     * Get the data attributes.
     *
     * @return array<string, string>
     */
    public function data_attributes(): array
    {
        return [
            'data-option-id' => (string) $this->option_id(),
            'data-option-type' => $this->type(),
            'data-option-position' => (string) $this->position(),
            'data-option-required' => $this->required() ? 'true' : 'false',
            'data-option-multiple' => $this->multiple() ? 'true' : 'false',
        ];
    }

    /**
     * Get the HTML attributes.
     *
     * @return string
     */
    public function html_attributes(): string
    {
        $attributes = $this->data_attributes();
        $html = [];
        foreach ($attributes as $key => $value) {
            $html[] = $key . '="' . htmlspecialchars($value) . '"';
        }
        return implode(' ', $html);
    }

    /**
     * Get the option weight.
     *
     * @return int
     */
    public function weight(): int
    {
        return (int) $this->_option->weight;
    }

    /**
     * Check if this is the first option.
     *
     * @return bool
     */
    public function first(): bool
    {
        return $this->position() === 1;
    }

    /**
     * Get the option context.
     *
     * @return array
     */
    public function context(): array
    {
        return [
            'id' => $this->option_id(),
            'name' => $this->name(),
            'type' => $this->type(),
            'position' => $this->position(),
            'required' => $this->required(),
            'multiple' => $this->multiple(),
            'values' => $this->values(),
            'selected' => $this->selected_value(),
            'css_class' => $this->css_class(),
            'html_attributes' => $this->html_attributes(),
        ];
    }
}
