<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.4.2019 г.
 * Time: 12:47 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Product;

use App\Models\Discount\Discount;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

/**
 * Label Drop Class
 * 
 * This class represents product label information in Liquid templates, providing access to label properties
 * and methods for Shopify compatibility and styling.
 * 
 * Properties:
 * - id: Label ID
 * - name: Label name
 * - color: Label color
 * - position: Label position
 * - style: Label style
 * - is_active: Whether the label is active
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Product
 */
class Label extends AbstractDrop
{
    /**
     * @var Discount The label model instance
     */
    protected Discount $_label;

    /**
     * Label constructor.
     * 
     * @param Discount $discount The label model instance
     */
    public function __construct(Discount $discount)
    {
        $this->_label = $discount;
    }

    /**
     * Get the label ID
     * 
     * @return int The label ID
     */
    public function id(): int
    {
        return $this->_label->id;
    }

    /**
     * Get the label name
     * 
     * @return string The label name
     */
    public function name(): string
    {
        return $this->_label->name;
    }

    /**
     * Get the label color
     * 
     * @return string|null The label color
     */
    public function color(): ?string
    {
        return $this->_label->color;
    }

    /**
     * Get the label position
     * 
     * @return string|null The label position
     */
    public function position(): ?string
    {
        return $this->_label->label_position;
    }

    /**
     * Get the label style
     * 
     * @return string|null The label style
     */
    public function style(): ?string
    {
        return $this->_label->label_style;
    }

    /**
     * Check if the label is active
     * 
     * @return bool Whether the label is active
     */
    public function is_active(): bool
    {
        return (bool) $this->_label->is_active;
    }

    /**
     * Get the label background color
     * 
     * @return string|null The label background color
     */
    public function background_color(): ?string
    {
        return $this->_label->background_color;
    }

    /**
     * Get the label text color
     * 
     * @return string|null The label text color
     */
    public function text_color(): ?string
    {
        return $this->_label->text_color;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     id: int,
     *     name: string,
     *     color: string|null,
     *     position: string|null,
     *     style: string|null,
     *     is_active: bool,
     *     background_color: string|null,
     *     text_color: string|null
     * } The label data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'color' => $this->color(),
            'position' => $this->position(),
            'style' => $this->style(),
            'is_active' => $this->is_active(),
            'background_color' => $this->background_color(),
            'text_color' => $this->text_color()
        ];
    }
}
