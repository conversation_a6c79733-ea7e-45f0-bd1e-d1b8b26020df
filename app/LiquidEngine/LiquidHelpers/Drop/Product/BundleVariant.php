<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Product;

use App\Helper\Format;
use App\Models\Product\Product as ProductModel;
use App\Models\Product\Variant as VariantModel;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;

/**
 * BundleVariant Drop Class
 * 
 * This class represents bundle variant information in Liquid templates, providing access to variant properties
 * and methods for price calculations and Shopify compatibility.
 * 
 * Properties:
 * - price: Bundle variant price
 * - price_input: Raw bundle variant price
 * - price_formatted: Formatted bundle variant price
 * - price_parts: Bundle variant price parts
 * - discounted: Whether the bundle variant is discounted
 * - price_discounted: Discounted price
 * - price_discounted_formatted: Formatted discounted price
 * - price_discounted_input: Raw discounted price
 * - price_discounted_parts: Discounted price parts
 * - discount_percent: Discount percentage
 * - discount_percent_formatted: Formatted discount percentage
 * - price_saved: Amount saved
 * - price_saved_formatted: Formatted amount saved
 * - price_saved_input: Raw amount saved
 * - price_saved_parts: Amount saved parts
 * - bundle_price_percent: Bundle price percentage
 * - bundle_price_percent_formatted: Formatted bundle price percentage
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Product
 */
class BundleVariant extends Variant
{
    /**
     * @var ProductModel The bundle product model instance
     */
    protected ProductModel $_bundle;

    /**
     * BundleVariant constructor.
     * 
     * @param VariantModel $variant The variant model instance
     * @param ProductModel $product The product model instance
     * @param ProductModel $bundle The bundle product model instance
     */
    public function __construct(VariantModel $variant, ProductModel $product, ProductModel $bundle)
    {
        parent::__construct($variant, $product);
        $this->_bundle = $bundle;
    }

    /**
     * Get the bundle variant price
     * 
     * @return int The bundle variant price
     */
    public function price(): int
    {
        if ($this->_bundle->price_percent > 0) {
            return (int) ($this->_variant->price - round($this->_variant->price * ($this->_bundle->price_percent / 10000)));
        }

        return parent::price();
    }

    /**
     * Get the raw bundle variant price
     * 
     * @return float The raw bundle variant price
     */
    public function price_input(): float
    {
        if ($this->_bundle->price_percent > 0) {
            if (is_null($this->_variant->price) || $this->_variant->price == '0') {
                return parent::price_input();
            }

            return Format::moneyInput($this->price());
        }

        return parent::price_input();
    }

    /**
     * Get the formatted bundle variant price
     * 
     * @return string The formatted bundle variant price
     */
    public function price_formatted(): string
    {
        if ($this->_bundle->price_percent > 0) {
            if (is_null($this->_variant->price) || $this->_variant->price == '0') {
                return parent::price_formatted();
            }

            return Format::money($this->price());
        }

        return parent::price_formatted();
    }

    /**
     * Get the bundle variant price parts
     * 
     * @return array<string, mixed> The bundle variant price parts
     */
    public function price_parts(): array
    {
        if ($this->_bundle->price_percent > 0) {
            return PriceParts::format($this->price_input());
        }

        return parent::price_parts();
    }

    /**
     * Check if the bundle variant is discounted
     * 
     * @return bool Whether the bundle variant is discounted
     */
    public function discounted(): bool
    {
        return false;
    }

    /**
     * Get the discounted price
     * 
     * @return null The discounted price (always null for bundle variants)
     */
    public function price_discounted(): ?int
    {
        return null;
    }

    /**
     * Get the formatted discounted price
     * 
     * @return null The formatted discounted price (always null for bundle variants)
     */
    public function price_discounted_formatted(): ?string
    {
        return null;
    }

    /**
     * Get the raw discounted price
     * 
     * @return null The raw discounted price (always null for bundle variants)
     */
    public function price_discounted_input(): ?float
    {
        return null;
    }

    /**
     * Get the discounted price parts
     * 
     * @return null The discounted price parts (always null for bundle variants)
     */
    public function price_discounted_parts(): ?array
    {
        return null;
    }

    /**
     * Get the discount percentage
     * 
     * @return int The discount percentage (always 0 for bundle variants)
     */
    public function discount_percent(): int
    {
        return 0;
    }

    /**
     * Get the formatted discount percentage
     * 
     * @return null The formatted discount percentage (always null for bundle variants)
     */
    public function discount_percent_formatted(): ?string
    {
        return null;
    }

    /**
     * Get the amount saved
     * 
     * @return null The amount saved (always null for bundle variants)
     */
    public function price_saved(): ?int
    {
        return null;
    }

    /**
     * Get the formatted amount saved
     * 
     * @return null The formatted amount saved (always null for bundle variants)
     */
    public function price_saved_formatted(): ?string
    {
        return null;
    }

    /**
     * Get the raw amount saved
     * 
     * @return null The raw amount saved (always null for bundle variants)
     */
    public function price_saved_input(): ?float
    {
        return null;
    }

    /**
     * Get the amount saved parts
     * 
     * @return null The amount saved parts (always null for bundle variants)
     */
    public function price_saved_parts(): ?array
    {
        return null;
    }

    /**
     * Get the bundle price percentage
     * 
     * @return int The bundle price percentage
     */
    public function bundle_price_percent(): int
    {
        return (int) $this->_bundle->price_percent;
    }

    /**
     * Get the bundle price percentage formatted
     * 
     * @return string The formatted bundle price percentage
     */
    public function bundle_price_percent_formatted(): string
    {
        return Format::percent($this->bundle_price_percent());
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     price: int,
     *     price_input: float,
     *     price_formatted: string,
     *     price_parts: array<string, mixed>,
     *     discounted: bool,
     *     price_discounted: int|null,
     *     price_discounted_formatted: string|null,
     *     price_discounted_input: float|null,
     *     price_discounted_parts: array<string, mixed>|null,
     *     discount_percent: int,
     *     discount_percent_formatted: string|null,
     *     price_saved: int|null,
     *     price_saved_formatted: string|null,
     *     price_saved_input: float|null,
     *     price_saved_parts: array<string, mixed>|null,
     *     bundle_price_percent: int,
     *     bundle_price_percent_formatted: string
     * } The bundle variant data
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'bundle_price_percent' => $this->bundle_price_percent(),
            'bundle_price_percent_formatted' => $this->bundle_price_percent_formatted(),
        ]);
    }
}
