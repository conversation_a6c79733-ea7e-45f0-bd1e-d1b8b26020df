<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Product;

use App\Exceptions\Error;
use App\LiquidEngine\LiquidHelpers\Drop\Product;

/**
 * Class Compare
 * 
 * Represents a product comparison in the Liquid template system.
 * Extends the Product class to add comparison functionality.
 * 
 * Properties:
 * @property-read array $_compares The array of products to compare
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Product
 */
class Compare extends Product
{
    /**
     * @var array The array of products to compare
     */
    protected array $_compares = [];

    /**
     * Set the comparison data.
     * 
     * @param array $compare The array of products to compare
     * @return void
     */
    public function setComparesData(array $compare): void
    {
        $this->_compares = $compare;
    }

    /**
     * Get the comparison data.
     * 
     * @return array The array of products to compare
     */
    public function compare(): array
    {
        return $this->_compares;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     compare: array
     * }
     * @throws Error
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'compare' => $this->compare(),
        ]);
    }
}
