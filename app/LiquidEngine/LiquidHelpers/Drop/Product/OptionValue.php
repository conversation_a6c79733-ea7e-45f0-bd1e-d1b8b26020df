<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.4.2019 г.
 * Time: 12:47 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Product;

use App\Exceptions\Error;
use App\LiquidEngine\LiquidHelpers\Drop\StoreFront\FormFieldOption;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use Illuminate\Support\Str;

/**
 * Option Value Drop Class
 * 
 * This class represents a product option value in Liquid templates, providing access to option value properties
 * and methods for price calculations, Shopify compatibility, and form field handling.
 * 
 * Properties:
 * - name_amount: Amount associated with the name
 * - amount: Raw amount value
 * - amount_input: Input amount value
 * - amount_formatted: Formatted amount value
 * - amount_parts: Structured amount components
 * - title: Option value title (Shopify compatibility)
 * - value: Option value content (Shopify compatibility)
 * - selected: Whether the option is selected (Shopify compatibility)
 * - available: Whether the option is available (Shopify compatibility)
 * - url: Option selection URL (Shopify compatibility)
 * - handle: URL handle for the option (Shopify compatibility)
 * - option: Parent option instance (Shopify compatibility)
 * - option_id: Parent option ID
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Product
 */
class OptionValue extends FormFieldOption
{
    /**
     * Get the amount associated with the name
     * 
     * @return int|null The name amount
     */
    public function name_amount(): ?int
    {
        return $this->field->name_amount;
    }

    /**
     * Get the raw amount value
     * 
     * @return int|null The raw amount
     */
    public function amount(): ?int
    {
        return $this->field->amount;
    }

    /**
     * Get the input amount value
     * 
     * @return float The input amount
     */
    public function amount_input(): float
    {
        return $this->field->amount_input;
    }

    /**
     * Get the formatted amount value
     * 
     * @return string The formatted amount
     */
    public function amount_formatted(): string
    {
        return $this->field->amount_formatted;
    }

    /**
     * Get the structured amount components
     * 
     * @return array<string, mixed> The amount components
     */
    public function amount_parts(): array
    {
        return PriceParts::format($this->amount_input());
    }

    // ========================================
    // SHOPIFY COMPATIBILITY METHODS
    // ========================================

    /**
     * Shopify compatibility: title
     * Alias for name() to match Shopify's option_value.title
     * 
     * @return string The option value title
     */
    public function title(): string
    {
        return $this->name() ?? '';
    }

    /**
     * Shopify compatibility: value
     * Returns the option value content
     * 
     * @return string The option value content
     */
    public function value(): string
    {
        return $this->name() ?? '';
    }

    /**
     * Shopify compatibility: selected
     * Returns whether this option value is currently selected
     * 
     * @return bool Whether the option is selected
     */
    public function selected(): bool
    {
        return $this->active();
    }

    /**
     * Shopify compatibility: available
     * Returns whether this option value is available for selection
     * 
     * @return bool Whether the option is available
     */
    public function available(): bool
    {
        // Option values are generally available unless specifically disabled
        // This can be enhanced based on inventory or other business rules
        return true;
    }

    /**
     * Shopify compatibility: url
     * Returns URL for selecting this option value
     * 
     * @return string The option selection URL
     */
    public function url(): string
    {
        // Generate URL with this option value selected
        // This should be enhanced to build proper product URL with option selection
        return '';
    }

    /**
     * Shopify compatibility: handle
     * Returns URL handle for this option value
     * 
     * @return string The URL handle
     */
    public function handle(): string
    {
        return Str::slug($this->name() ?? '') ?: '';
    }

    /**
     * Shopify compatibility: option
     * Returns the parent option this value belongs to
     * Note: This requires the parent option to be passed during construction
     * 
     * @return Option|null The parent option
     */
    public function option(): ?Option
    {
        // This would need the parent option passed in constructor
        // For now returning null, can be enhanced if parent option relationship is needed
        return null;
    }

    /**
     * Get parent option ID if available
     * 
     * @return int|null The parent option ID
     */
    public function option_id(): ?int
    {
        // This would come from the field relationship
        return $this->field->field_id ?? null;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     name_amount: int|null,
     *     amount: int|null,
     *     amount_input: float,
     *     amount_formatted: string,
     *     amount_parts: array<string, mixed>,
     *     title: string,
     *     value: string,
     *     selected: bool,
     *     available: bool,
     *     url: string,
     *     handle: string,
     *     option_id: int|null
     * } The option value data
     * @throws Error
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'name_amount' => $this->name_amount(),
            'amount' => $this->amount(),
            'amount_input' => $this->amount_input(),
            'amount_formatted' => $this->amount_formatted(),
            'amount_parts' => $this->amount_parts(),
            // Shopify compatibility methods
            'title' => $this->title(),
            'value' => $this->value(),
            'selected' => $this->selected(),
            'available' => $this->available(),
            'url' => $this->url(),
            'handle' => $this->handle(),
            'option_id' => $this->option_id(),
        ]);
    }
}
