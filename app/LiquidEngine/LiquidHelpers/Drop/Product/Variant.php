<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Product;

use App\Exceptions\Error;
use App\Helper\Catalog\Products;
use App\Helper\Format;
use App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute;
use App\Helper\YesNo;
use App\Models\Product\Image;
use App\Models\Product\Quantity;
use App\Models\Product\Status;
use App\Models\Product\Variant as VariantModel;
use App\Models\Product\Product as ProductModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Quantity as DropQuantity;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use App\LiquidEngine\LiquidHelpers\Drop\Variants\Variant as DropVariant;
use App\LiquidEngine\LiquidHelpers\Drop\Variants\Option as DropVariantOption;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;

/**
 * Variant Drop Class
 * 
 * This class represents product variant information in Liquid templates, providing access to variant properties
 * and methods for price calculations, inventory management, and Shopify compatibility.
 * 
 * Properties:
 * - id: Variant ID
 * - compare_key: Unique comparison key
 * - product_id: Associated product ID
 * - v1, v2, v3: Variant values
 * - p1, p2, p3: Variant parameters
 * - barcode: Product barcode
 * - barcode_formatted: Formatted barcode
 * - sku: Stock keeping unit
 * - sku_formatted: Formatted SKU
 * - quantity: Available quantity
 * - quantity_formatted: Formatted quantity
 * - weight: Weight in minor unit
 * - weight_formatted: Formatted weight
 * - weight_input: Raw weight value
 * - price: Price in currency's minor unit
 * - price_input: Raw price value
 * - price_formatted: Formatted price
 * - price_parts: Price components
 * - discounted: Whether variant is discounted
 * - price_discounted: Discounted price
 * - price_discounted_formatted: Formatted discounted price
 * - price_discounted_input: Raw discounted price
 * - price_discounted_parts: Discounted price components
 * - discount_percent: Discount percentage
 * - discount_percent_formatted: Formatted discount percentage
 * - price_saved: Amount saved
 * - price_saved_formatted: Formatted amount saved
 * - price_saved_input: Raw amount saved
 * - price_saved_parts: Amount saved components
 * - stock_status_key_label: Stock status label
 * - stock_status_key: Stock status key
 * - tracking: Whether inventory is tracked
 * - continue_selling: Whether to continue selling when out of stock
 * - image: Variant image
 * - images: Variant images
 * - has_image: Whether variant has an image
 * - quantity_by_store: Quantity by store
 * - unit_delta: Unit delta value
 * - unit: Unit information
 * - unit_value: Unit value
 * - unit_value_formatted: Formatted unit value
 * - title: Variant title
 * - option1, option2, option3: Option values
 * - inventory_quantity: Available inventory
 * - compare_at_price: Compare at price
 * - available: Whether variant is available
 * - created_at: Creation timestamp
 * - updated_at: Last update timestamp
 * - grams: Weight in grams
 * - requires_shipping: Whether shipping is required
 * - taxable: Whether variant is taxable
 * - inventory_policy: Inventory policy
 * - inventory_management: Inventory management type
 * - featured_image: Featured image
 * - position: Variant position
 * - unit_price: Price per unit
 * - unit_price_measurement: Unit price measurement
 * - url: Variant URL
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Product
 */
class Variant extends AbstractDrop
{

    use DropImageToArray;

    /**
     * @var VariantModel The variant model instance
     */
    protected VariantModel $_variant;

    /**
     * @var ProductModel The product model instance
     */
    protected ProductModel $_product;

    /**
     * @var array|null The quantity by store cache
     */
    protected ?array $quantity_by_store = null;

    /**
     * Product constructor.
     * @param VariantModel $variant
     * @param ProductModel $product
     */
    public function __construct(VariantModel $variant, ProductModel $product)
    {
        $this->_variant = $variant;
        $this->_product = $product;

        $this->_formatVariant();
    }

    /**
     * Get the variant ID
     * 
     * @return int The variant ID
     */
    public function id()
    {
        return $this->_variant->id;
    }

    /**
     * Get the unique comparison key
     * 
     * @return string The comparison key
     */
    public function compare_key()
    {
        return $this->_variant->compare_key;
    }

    /**
     * Get the associated product ID
     * 
     * @return int The product ID
     */
    public function product_id()
    {
        return $this->_variant->item_id;
    }

    /**
     * Get the first variant value
     * 
     * @return string|null The first variant value
     */
    public function v1()
    {
        return $this->_variant->v1;
    }

    /**
     * Get the second variant value
     * 
     * @return string|null The second variant value
     */
    public function v2()
    {
        return $this->_variant->v2;
    }

    /**
     * Get the third variant value
     * 
     * @return string|null The third variant value
     */
    public function v3()
    {
        return $this->_variant->v3;
    }

    /**
     * Get the first variant parameter
     * 
     * @return string|null The first variant parameter
     */
    public function p1()
    {
        return $this->_variant->getAttribute('p1');
    }

    /**
     * Get the second variant parameter
     * 
     * @return string|null The second variant parameter
     */
    public function p2()
    {
        return $this->_variant->getAttribute('p2');
    }

    /**
     * Get the third variant parameter
     * 
     * @return string|null The third variant parameter
     */
    public function p3()
    {
        return $this->_variant->getAttribute('p3');
    }

    /**
     * Get the first variant parameter object
     * 
     * @return DropVariant|null The first variant parameter object
     */
    public function v1_parameter(): ?\App\LiquidEngine\LiquidHelpers\Drop\Variants\Variant
    {
        if ($this->v1() && $this->_variant->relationLoaded('v1r') && $this->_variant->v1r) {
            return new DropVariant($this->_variant->v1r->parameter);
        }

        return null;
    }

    /**
     * Get the second variant parameter object
     * 
     * @return DropVariant|null The second variant parameter object
     */
    public function v2_parameter(): ?\App\LiquidEngine\LiquidHelpers\Drop\Variants\Variant
    {
        if ($this->v2() && $this->_variant->relationLoaded('v2r') && $this->_variant->v2r) {
            return new DropVariant($this->_variant->v2r->parameter);
        }

        return null;
    }

    /**
     * Get the third variant parameter object
     * 
     * @return DropVariant|null The third variant parameter object
     */
    public function v3_parameter(): ?\App\LiquidEngine\LiquidHelpers\Drop\Variants\Variant
    {
        if ($this->v3() && $this->_variant->relationLoaded('v3r') && $this->_variant->v3r) {
            return new DropVariant($this->_variant->v3r->parameter);
        }

        return null;
    }

    /**
     * Get the first variant option object
     * 
     * @return DropVariantOption|null The first variant option object
     */
    public function v1_option(): ?\App\LiquidEngine\LiquidHelpers\Drop\Variants\Option
    {
        if ($this->v1() && $this->_variant->relationLoaded('v1r') && $this->_variant->v1r) {
            return new DropVariantOption($this->_variant->v1r);
        }

        return null;
    }

    /**
     * Get the second variant option object
     * 
     * @return DropVariantOption|null The second variant option object
     */
    public function v2_option(): ?\App\LiquidEngine\LiquidHelpers\Drop\Variants\Option
    {
        if ($this->v2() && $this->_variant->relationLoaded('v2r') && $this->_variant->v2r) {
            return new DropVariantOption($this->_variant->v2r);
        }

        return null;
    }

    /**
     * Get the third variant option object
     * 
     * @return DropVariantOption|null The third variant option object
     */
    public function v3_option(): ?\App\LiquidEngine\LiquidHelpers\Drop\Variants\Option
    {
        if ($this->v3() && $this->_variant->relationLoaded('v3r') && $this->_variant->v3r) {
            return new DropVariantOption($this->_variant->v3r);
        }

        return null;
    }

    /**
     * Get the product barcode
     * 
     * @return string|null The product barcode
     */
    public function barcode()
    {
        return $this->_variant->barcode;
    }

    /**
     * Get the formatted product barcode
     * 
     * @return string|null The formatted barcode
     */
    public function barcode_formatted()
    {
        return $this->_variant->barcode_formatted;
    }

    /**
     * Get the stock keeping unit
     * 
     * @return string|null The SKU
     */
    public function sku()
    {
        return $this->_variant->sku;
    }

    /**
     * Get the formatted stock keeping unit
     * 
     * @return string|null The formatted SKU
     */
    public function sku_formatted()
    {
        return $this->_variant->sku_formatted;
    }

    /**
     * Get the available quantity
     * 
     * @return int The available quantity
     */
    public function quantity()
    {
        return $this->_variant->quantity;
    }

    /**
     * Get the quantity change value
     * 
     * @return int The quantity change value
     */
    public function quantity_change(): int
    {
        if ($this->continue_selling() || !$this->tracking()) {
            return 100;
        }

        return (int)$this->quantity();
    }

    /**
     * Get the formatted quantity
     * 
     * @return string The formatted quantity
     */
    public function quantity_formatted()
    {
        return $this->_variant->quantity_formatted;
    }

    /**
     * Get the weight in minor unit
     * 
     * @return float|null The weight
     */
    public function weight()
    {
        return $this->_variant->weight;
    }

    /**
     * Get the formatted weight
     * 
     * @return string|null The formatted weight
     */
    public function weight_formatted()
    {
        return $this->_variant->weight_formatted;
    }

    /**
     * Get the raw weight value
     * 
     * @return float|null The raw weight
     */
    public function weight_input()
    {
        return $this->_variant->weight_input;
    }

    /**
     * Get the price in currency's minor unit
     * 
     * @return int The price
     */
    public function price()
    {
        return $this->_variant->price;
    }

    /**
     * Get the raw price value
     * 
     * @return float The raw price
     */
    public function price_input()
    {
        return $this->_variant->price_input;
    }

    /**
     * Get the formatted price
     * 
     * @return string The formatted price
     */
    public function price_formatted()
    {
        return $this->_variant->price_formatted;
    }

    /**
     * Get the price components
     * 
     * @return array<string, mixed> The price components
     */
    public function price_parts(): array
    {
        return PriceParts::format($this->price_input());
    }

    /**
     * Check if the variant is discounted
     * 
     * @return bool Whether the variant is discounted
     */
    public function discounted(): bool
    {
        return !!$this->_variant->getAttribute('discounted');
    }

    /**
     * Get the discounted price
     * 
     * @return int|null The discounted price
     */
    public function price_discounted()
    {
        return $this->_variant->getAttribute('price_discounted');
    }

    /**
     * Get the formatted discounted price
     * 
     * @return string|null The formatted discounted price
     */
    public function price_discounted_formatted()
    {
        return $this->_variant->getAttribute('price_discounted_formatted');
    }

    /**
     * Get the raw discounted price
     * 
     * @return float|null The raw discounted price
     */
    public function price_discounted_input()
    {
        return $this->_variant->getAttribute('price_discounted_input');
    }

    /**
     * Get the discounted price components
     * 
     * @return array<string, mixed>|null The discounted price components
     */
    public function price_discounted_parts(): ?array
    {
        if ($price = $this->price_discounted_input()) {
            return PriceParts::format($price);
        }

        return null;
    }

    /**
     * Get the discount percentage
     * 
     * @return int The discount percentage
     */
    public function discount_percent()
    {
        return $this->_variant->getAttribute('discount_percent');
    }

    /**
     * Get the formatted discount percentage
     * 
     * @return string|null The formatted discount percentage
     */
    public function discount_percent_formatted(): ?string
    {
        if ($percent = $this->discount_percent()) {
            return Format::percent($percent * 100);
        }

        return null;
    }

    /**
     * Get the amount saved
     * 
     * @return int|null The amount saved
     */
    public function price_saved()
    {
        return $this->_variant->getAttribute('price_saved');
    }

    /**
     * Get the formatted amount saved
     * 
     * @return string|null The formatted amount saved
     */
    public function price_saved_formatted()
    {
        return $this->_variant->getAttribute('price_saved_formatted');
    }

    /**
     * Get the raw amount saved
     * 
     * @return float|null The raw amount saved
     */
    public function price_saved_input()
    {
        return $this->_variant->getAttribute('price_saved_input');
    }

    /**
     * Get the amount saved components
     * 
     * @return array<string, mixed>|null The amount saved components
     */
    public function price_saved_parts(): ?array
    {
        if ($price = $this->price_saved_input()) {
            return PriceParts::format($price);
        }

        return null;
    }

    /**
     * Get the stock status label
     * 
     * @return string|null The stock status label
     */
    public function stock_status_key_label()
    {
        return $this->_variant->getAttribute('stock_status_key_label');
    }

    /**
     * Get the stock status key
     * 
     * @return string|null The stock status key
     */
    public function stock_status_key()
    {
        return $this->_variant->getAttribute('stock_status_key');
    }

    /**
     * Check if inventory is tracked
     * 
     * @return bool Whether inventory is tracked
     */
    public function tracking(): bool
    {
        return $this->_variant->tracking == YesNo::True;
    }

    /**
     * Check if to continue selling when out of stock
     * 
     * @return bool Whether to continue selling
     */
    public function continue_selling(): bool
    {
        return $this->_variant->continue_selling == YesNo::True;
    }

    /**
     * Get the variant image
     * 
     * @return LiquidImageAttribute The variant image
     */
    public function image()
    {
        $formatter = new UrlImageFormat($this->has_image() ? $this->_variant->image->image : new Image());
        return $formatter->getLiquidImage();
    }

    public function images()
    {
        $images = [];
        if ($this->has_image() && $this->_variant->relationLoaded('images') && $this->_variant->images->isNotEmpty()) {
            $images = $this->_variant->images->map(function ($image) {
                $formatter = new UrlImageFormat($image->image);
                return $formatter->getLiquidImage();
            })->all();
        }

        return $images;
    }

    /**
     * @return bool
     */
    public function has_image(): bool
    {
        return $this->_variant->relationLoaded('image') && ($this->_variant->image->image ?? null) && $this->_variant->image->image->hasImage();
    }

    /**
     * Get the quantity by store
     * 
     * @return array<DropQuantity> The quantity by store
     */
    public function quantity_by_store()
    {
        if (!is_null($this->quantity_by_store)) {
            return $this->quantity_by_store;
        }

        $quantities = [];
        if ($this->_variant->relationLoaded('quantity_per_shops')) {
            $quantities = $this->_variant->quantity_per_shops->sortBy(function (Quantity $quantity): string {
                return sprintf('%s#%s', $quantity->shop->city, $quantity->shop->title);
            })->map(function (Quantity $quantity): \App\LiquidEngine\LiquidHelpers\Drop\Product\Quantity {
                return new DropQuantity($quantity);
            })->values()->toArray();
        }

        return $this->quantity_by_store = $quantities;
    }

    public function unit_delta(): float
    {
        return 1.0;
    }

    public function unit(): ?\App\LiquidEngine\LiquidHelpers\Drop\Product\Unit
    {
        return $this->_product->unit ? new Unit($this->_product->unit) : null;
    }

    public function unit_value()
    {
        return $this->_variant->unit ? $this->_variant->unit_value : null;
    }

    public function unit_value_formatted()
    {
        return $this->_variant->unit_value_formatted;
    }

    // START: Shopify-compatible methods for Product Variant
    // These methods provide compatibility with Shopify's variant object structure

    /**
     * Get variant title (combination of option values)
     * Shopify compatible: variant.title
     */
    public function title(): string
    {
        $title_parts = [];
        
        if ($this->v1()) {
            $title_parts[] = $this->v1();
        }
        if ($this->v2()) {
            $title_parts[] = $this->v2();
        }
        if ($this->v3()) {
            $title_parts[] = $this->v3();
        }
        
        return !empty($title_parts) ? implode(' / ', $title_parts) : 'Default Title';
    }

    /**
     * Get variant option1 value
     * Shopify compatible: variant.option1
     */
    public function option1(): ?string
    {
        return $this->v1();
    }

    /**
     * Get variant option2 value
     * Shopify compatible: variant.option2
     */
    public function option2(): ?string
    {
        return $this->v2();
    }

    /**
     * Get variant option3 value
     * Shopify compatible: variant.option3
     */
    public function option3(): ?string
    {
        return $this->v3();
    }

    /**
     * Get inventory quantity
     * Shopify compatible: variant.inventory_quantity
     */
    public function inventory_quantity(): int
    {
        return (int) $this->quantity();
    }

    /**
     * Get compare at price in currency subunit (cents)
     * Shopify compatible: variant.compare_at_price
     */
    public function compare_at_price()
    {
        // Return null if no discount/compare price exists
        if (!$this->discounted()) {
            return null;
        }
        
        // Return original price in subunit (cents) when discounted
        return (int) round($this->price() * 100);
    }

    /**
     * Check if variant is available for purchase
     * Shopify compatible: variant.available
     */
    public function available(): bool
    {
        // Based on CloudCart's enable_sell logic
        return (bool) $this->_variant->enable_sell;
    }

    /**
     * Get variant created timestamp
     * Shopify compatible: variant.created_at
     */
    public function created_at(): ?string
    {
        if (!$this->_variant->created_at) {
            return null;
        }
        
        return $this->_variant->created_at instanceof \Carbon\Carbon 
            ? $this->_variant->created_at->toISOString()
            : \Carbon\Carbon::parse($this->_variant->created_at)->toISOString();
    }

    /**
     * Get variant updated timestamp
     * Shopify compatible: variant.updated_at
     */
    public function updated_at(): ?string
    {
        if (!$this->_variant->updated_at) {
            return null;
        }
        
        return $this->_variant->updated_at instanceof \Carbon\Carbon 
            ? $this->_variant->updated_at->toISOString()
            : \Carbon\Carbon::parse($this->_variant->updated_at)->toISOString();
    }

    /**
     * Get variant grams (weight in grams)
     * Shopify compatible: variant.grams
     */
    public function grams(): float
    {
        // Convert weight to grams if needed
        $weight = $this->weight();
        if ($weight === null) {
            return 0.0;
        }
        
        // Assuming weight is already in grams, but could be enhanced based on unit system
        return (float) $weight;
    }

    /**
     * Check if variant requires shipping
     * Shopify compatible: variant.requires_shipping
     */
    public function requires_shipping(): bool
    {
        // Check if the product has physical attributes that require shipping
        return $this->grams() > 0 || $this->weight() > 0;
    }

    /**
     * Check if variant is taxable
     * Shopify compatible: variant.taxable
     */
    public function taxable(): bool
    {
        // Default to true for physical products, could be enhanced with tax settings
        return true;
    }

    /**
     * Get variant inventory policy
     * Shopify compatible: variant.inventory_policy
     */
    public function inventory_policy(): string
    {
        return $this->continue_selling() ? 'continue' : 'deny';
    }

    /**
     * Get variant inventory management
     * Shopify compatible: variant.inventory_management
     */
    public function inventory_management(): ?string
    {
        return $this->tracking() ? 'shopify' : null;
    }

    /**
     * Get variant featured image
     * Shopify compatible: variant.featured_image
     */
    public function featured_image()
    {
        return $this->image();
    }

    /**
     * Get variant position in product variants list
     * Shopify compatible: variant.position
     */
    public function position(): int
    {
        // Could be enhanced to track actual position if needed
        return 1;
    }

    /**
     * Get variant unit price in currency subunit (cents)
     * Shopify compatible: variant.unit_price
     */
    public function unit_price()
    {
        $unit_value = $this->unit_value();
        if ($unit_value === null) {
            return null;
        }
        
        return (int) round($unit_value * 100);
    }

    /**
     * Get variant unit price measurement
     * Shopify compatible: variant.unit_price_measurement
     */
    public function unit_price_measurement()
    {
        $unit = $this->unit();
        if (!$unit) {
            return null;
        }
        
        return [
            'measured_type' => 'weight', // or 'volume', 'length' based on unit type
            'quantity_unit' => $unit->toArray()['short_name'] ?? 'g',
            'quantity_value' => $this->unit_delta(),
            'reference_unit' => $unit->toArray()['short_name'] ?? 'g',
            'reference_value' => 1
        ];
    }

    /**
     * Get variant URL for deep linking
     * Shopify compatible: variant.url
     */
    public function url(): string
    {
        // Generate product URL with variant parameter
        $productUrl = route('product', ['product' => $this->_product->slug ?? $this->_product->id]);
        return $productUrl . '?variant=' . $this->id();
    }

    // END: Shopify-compatible methods

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     * @throws Error
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'compare_key' => $this->compare_key(),
            'barcode' => $this->barcode(),
            'barcode_formatted' => $this->barcode_formatted(),
            'continue_selling' => $this->continue_selling(),
            'discounted' => $this->discounted(),
            'has_image' => $this->has_image(),
            'image' => $this->getImageArray(),
            'images' => array_map(function ($image): array {
                $formatter = new UrlImageFormat($image);
                return $formatter->imagesFormatArray();
            }, $this->images()),
            'p1' => $this->p1(),
            'p2' => $this->p2(),
            'p3' => $this->p3(),
            'price' => $this->price(),
            'price_formatted' => $this->price_formatted(),
            'price_input' => $this->price_input(),
            'price_parts' => $this->price_parts(),
            'price_discounted' => $this->price_discounted(),
            'price_discounted_formatted' => $this->price_discounted_formatted(),
            'price_discounted_input' => $this->price_discounted_input(),
            'price_discounted_parts' => $this->price_discounted_parts(),
            'price_saved' => $this->price_saved(),
            'price_saved_formatted' => $this->price_saved_formatted(),
            'price_saved_input' => $this->price_saved_input(),
            'price_saved_parts' => $this->price_saved_parts(),
            'discount_percent' => $this->discount_percent(),
            'discount_percent_formatted' => $this->discount_percent_formatted(),
            'product_id' => $this->product_id(),
            'quantity' => $this->quantity(),
            'quantity_by_store' => $this->quantity_by_store(),
            'quantity_change' => $this->quantity_change(),
            'quantity_formatted' => $this->quantity_formatted(),
            'sku' => $this->sku(),
            'sku_formatted' => $this->sku_formatted(),
            'stock_status_key' => $this->stock_status_key(),
            'stock_status_key_label' => $this->stock_status_key_label(),
            'tracking' => $this->tracking(),
            'v1' => $this->v1(),
            'v1_option' => $this->v1_option(),
            'v1_parameter' => $this->v1_parameter(),
            'v2' => $this->v2(),
            'v2_option' => $this->v2_option(),
            'v2_parameter' => $this->v2_parameter(),
            'v3' => $this->v3(),
            'v3_option' => $this->v3_option(),
            'v3_parameter' => $this->v3_parameter(),
            'weight' => $this->weight(),
            'weight_formatted' => $this->weight_formatted(),
            'weight_input' => $this->weight_input(),
            'unit_delta' => $this->unit_delta(),
            'unit' => $this->unit(),
            'unit_value' => $this->unit_value(),
            'unit_value_formatted' => $this->unit_value_formatted(),
            
            // Shopify-compatible fields
            'title' => $this->title(),
            'option1' => $this->option1(),
            'option2' => $this->option2(),
            'option3' => $this->option3(),
            'inventory_quantity' => $this->inventory_quantity(),
            'compare_at_price' => $this->compare_at_price(),
            'available' => $this->available(),
            'created_at' => $this->created_at(),
            'updated_at' => $this->updated_at(),
            'grams' => $this->grams(),
            'requires_shipping' => $this->requires_shipping(),
            'taxable' => $this->taxable(),
            'inventory_policy' => $this->inventory_policy(),
            'inventory_management' => $this->inventory_management(),
            'featured_image' => $this->featured_image(),
            'position' => $this->position(),
            'unit_price' => $this->unit_price(),
            'unit_price_measurement' => $this->unit_price_measurement(),
            'url' => $this->url(),
        ];
    }

    /**
     * @return VariantModel
     */
    protected function _formatVariant(): \App\Models\Product\Variant
    {
        $is_bundle = $this->_product->type == ProductModel::TYPE_BUNDLE;

        if ($this->_variant->relationLoaded('images')) {
            $this->_variant->setAttribute('image_ids', $this->_variant->images->unique()->pluck('product_image_id'));
        }

        if ($this->_product->relationLoaded('pivot')) {
            $this->_variant->setAttribute('pivot', $this->_product->pivot);
        } else {
            $this->_variant->setAttribute('pivot', null);
        }

        for ($i = 1; $i <= 3; $i++) {
            $this->_variant->setAttribute('p' . $i, $this->_product->getAttribute('p' . $i));
        }

        if ($this->_product->discount && $this->_variant->detailed_discount) {
            $this->_variant->setAttribute('discounted', $this->_variant->detailed_discount->discount);
            $this->_variant->setAttribute('price_discounted', $this->_variant->detailed_discount->price);
            $this->_variant->setAttribute('price_discounted_formatted', Format::money($this->_variant->getAttribute('price_discounted')));
            $this->_variant->setAttribute('price_discounted_input', Format::moneyInput($this->_variant->getAttribute('price_discounted')));

            $this->_variant->setAttribute('price_saved', $this->_variant->price - $this->_variant->getAttribute('price_discounted'));
            $this->_variant->setAttribute('price_saved_formatted', Format::money($this->_variant->getAttribute('price_saved')));
            $this->_variant->setAttribute('price_saved_input', Format::moneyInput($this->_variant->getAttribute('price_saved')));

            $this->_variant->setAttribute('discount_percent', 100 - get_percentage($this->_variant->price, $this->_variant->getAttribute('price_discounted')));
        }

        if ($is_bundle) {
            $this->_variant->quantity = $this->_product->quantity;
        }

        $status = $this->getStatus();

        $this->_variant->setAttribute('stock_status_key_label', $status->name);
        $this->_variant->setAttribute('stock_status_key', $status->type);

        $this->_variant->enable_sell = Products::showBuyButton($status, $this->_variant, $this->_product);
        if (!$this->_variant->enable_sell) {
            $this->_variant->setAttribute('stock_status_key', $this->_variant->quantity > 0 ? $status->type : ($status ? $status->type : null));
            $this->_variant->setAttribute('stock_status_key_label', $this->_variant->quantity > 0 ? $status->name : ($status ? $status->name : null));
        }

//        if (CustomIntegration::supportVariantsFormat()) {
//            CustomIntegration::variantsFormat($variants, $product);
//        }

        return $this->_variant;
    }

    /**
     * @return Status
     */
    protected function getStatus()
    {

        if ($this->_product->tracking == YesNo::True && $this->_product->continue_selling == YesNo::True && !is_null($this->_variant->quantity) && ($status_continue_selling = Status::getMapped()->where('quantity_operator_id', Status::CONTINUE_SELLING_ID)->first()) && (int)$status_continue_selling->quantity == (int)$this->_variant->quantity) {
            return $status_continue_selling ?: new Status([
                'name' => __('product.stock.install.in_stock'),
                'type' => 'in_stock'
            ]);
        }

        if ($this->_product->out_of_stock_id && !is_null($this->_variant->quantity) && $this->_variant->quantity == 0) {
            return Status::getMapped()->get($this->_product->out_of_stock_id) ?: new Status([
                'name' => __('sf.global.label.out_of_stock'),
                'type' => 'out_stock'
            ]);
        }

        if ($this->_product->status_id && ($this->_product->tracking == YesNo::False || ($this->_product->tracking == YesNo::True && $this->_variant->quantity > 0))) {
            return Status::getMapped()->get($this->_product->status_id) ?: new Status([
                'name' => __('sf.global.label.out_of_stock'),
                'type' => 'out_stock'
            ]);
        } else {

            if ($this->_product->tracking == YesNo::False && $status_without_tracking = Status::getMapped()->where('quantity_operator_id', Status::NOT_TRACKING_OPERATOR_ID)->first()) {
                return $status_without_tracking ?: new Status([
                    'name' => __('sf.global.label.out_of_stock'),
                    'type' => 'out_stock'
                ]);
            }

            if ($status_with_same_quantity = Status::getMapped()->where('quantity', is_null($this->_variant->quantity) ? '===' : '==', $this->_variant->quantity)->where('quantity_operator_id', Status::EQUAL_OPERATOR_ID)->first()) {
                return $status_with_same_quantity ?: new Status([
                    'name' => __('sf.global.label.out_of_stock'),
                    'type' => 'out_stock'
                ]);
            }

            $all_statuses = Status::getMapped();
            if ($all_statuses) {
                foreach ($all_statuses as $status) {
                    if (Products::hasValidStatus($this->_variant, $status)) {
                        return $status;
                    }
                }
            }
        }

        if ($this->_variant->enable_sell) {
            return new Status([
                'name' => __('product.stock.install.in_stock'),
                'type' => 'in_stock'
            ]);
        }

        return new Status([
            'name' => __('sf.global.label.out_of_stock'),
            'type' => 'out_stock'
        ]);
    }

}
