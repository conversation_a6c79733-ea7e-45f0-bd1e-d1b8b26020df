<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Product;

use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use Modules\Apps\Administration\GroceryStore\Models\Units;

/**
 * Class Unit
 * 
 * Represents a product unit in the Liquid template system.
 * This class provides access to unit information and conversion factors.
 * 
 * @property-read Units $_unit The underlying unit model
 */
class Unit extends AbstractDrop
{
    /**
     * The underlying unit model instance.
     *
     * @var Units
     */
    protected Units $_unit;

    /**
     * Create a new Unit instance.
     *
     * @param Units $unit The unit model instance
     */
    public function __construct(Units $unit)
    {
        $this->_unit = $unit;
    }

    /**
     * Get the unit name.
     *
     * @return string The short name of the unit
     */
    public function name(): string
    {
        return (string) $this->_unit->short_name;
    }

    /**
     * Get the unit conversion factor (delta).
     *
     * @return float The conversion factor for the unit
     */
    public function delta(): float
    {
        return 1.0;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     name: string,
     *     delta: float
     * } The array representation of the unit
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'delta' => $this->delta(),
        ];
    }
}
