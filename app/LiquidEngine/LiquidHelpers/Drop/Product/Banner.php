<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.4.2019 г.
 * Time: 12:47 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Product;

use App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute;
use App\Models\Discount\Discount;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;
use App\Exceptions\Error;

/**
 * Banner Drop Class
 * 
 * This class represents product banner information in Liquid templates, providing access to banner properties
 * and methods for image handling and Shopify compatibility.
 * 
 * Properties:
 * - id: Banner ID
 * - name: Banner name
 * - banner_position: Banner position
 * - image: Banner image
 * - has_image: Whether the banner has an image
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Product
 */
class Banner extends AbstractDrop
{

    use DropImageToArray;

    /**
     * @var Discount The banner model instance
     */
    protected Discount $_banner;

    /**
     * Banner constructor.
     * 
     * @param Discount $discount The banner model instance
     */
    public function __construct(Discount $discount)
    {
        $this->_banner = $discount;
    }

    /**
     * Get the banner ID
     * 
     * @return int The banner ID
     */
    public function id(): int
    {
        return $this->_banner->id;
    }

    /**
     * Get the banner name
     * 
     * @return string The banner name
     */
    public function name(): string
    {
        return $this->_banner->name;
    }

    /**
     * Get the banner position
     * 
     * @return string The banner position
     */
    public function banner_position(): string
    {
        return $this->_banner->banner_position_new;
    }

    /**
     * Get the banner image
     * 
     * @return LiquidImageAttribute The banner image
     */
    public function image(): LiquidImageAttribute
    {
        $formatter = new UrlImageFormat($this->_banner);
        return $formatter->getLiquidImage();
    }

    /**
     * Check if the banner has an image
     * 
     * @return bool Whether the banner has an image
     */
    public function has_image(): bool
    {
        return (bool) $this->_banner->hasImage();
    }

    /**
     * Get the banner URL
     * 
     * @return string|null The banner URL
     */
    public function url(): ?string
    {
        return $this->_banner->url;
    }

    /**
     * Get the banner target
     * 
     * @return string|null The banner target
     */
    public function target(): ?string
    {
        return $this->_banner->target;
    }

    /**
     * Get the banner alt text
     * 
     * @return string|null The banner alt text
     */
    public function alt(): ?string
    {
        return $this->_banner->alt;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     id: int,
     *     name: string,
     *     banner_position: string,
     *     has_image: bool,
     *     image: array<string, mixed>,
     *     url: string|null,
     *     target: string|null,
     *     alt: string|null
     * } The banner data
     * @throws Error
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'banner_position' => $this->banner_position(),
            'has_image' => $this->has_image(),
            'image' => $this->getImageArray(),
            'url' => $this->url(),
            'target' => $this->target(),
            'alt' => $this->alt()
        ];
    }

}
