<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.4.2019 г.
 * Time: 12:47 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Product;

use App\Models\Discount\Discount as DiscountModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use DateTimeInterface;

/**
 * Discount Drop Class
 *
 * This class represents product discount information in Liquid templates, providing access to discount properties
 * and methods for Shopify compatibility, accessibility, and SEO features.
 *
 * Properties:
 * - id: Discount ID
 * - name: Discount name
 * - type_value: Discount value
 * - type_value_formatted: Formatted discount value
 * - type: Discount type (flat, percent, shipping, fixed, etc.)
 * - color: Discount color
 * - timer_list: List of timer information
 * - timer_details: Detailed timer information
 * - date_start: Start date of the discount
 * - date_end: End date of the discount
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop\Product
 */
class Discount extends AbstractDrop
{
    /**
     * @var DiscountModel The discount model instance
     */
    protected DiscountModel $_discount;

    /**
     * Discount constructor.
     *
     * @param DiscountModel $discount The discount model instance
     */
    public function __construct(DiscountModel $discount)
    {
        $this->_discount = $discount;
    }

    /**
     * Get the discount ID
     *
     * @return int The discount ID
     */
    public function id(): int
    {
        return $this->_discount->id;
    }

    /**
     * Get the discount name
     *
     * @return string The discount name
     */
    public function name(): string
    {
        return $this->_discount->name;
    }

    /**
     * Get the discount value
     *
     * @return float The discount value
     */
    public function type_value(): float
    {
        return (float) $this->_discount->type_value;
    }

    /**
     * Get the formatted discount value
     *
     * @return string The formatted discount value
     */
    public function type_value_formatted(): string
    {
        return $this->_discount->type_value_formatted;
    }

    /**
     * Get the discount type
     *
     * @return string The discount type (flat, percent, shipping, fixed, etc.)
     */
    public function type(): string
    {
        return $this->_discount->type;
    }

    /**
     * Get the discount color
     *
     * @return string|null The discount color
     */
    public function color(): ?string
    {
        return $this->_discount->color;
    }

    /**
     * Get the timer list information
     *
     * @return array<int|string, mixed> The timer list information
     */
    public function timer_list(): array
    {
        return (array) $this->_discount->timer_list;
    }

    /**
     * Get the timer details information
     *
     * @return array<int|string, mixed> The timer details information
     */
    public function timer_details(): array
    {
        return (array) $this->_discount->timer_details;
    }

    /**
     * Get the discount start date
     *
     * @return DateTimeInterface|null The discount start date
     */
    public function date_start(): ?DateTimeInterface
    {
        if (!empty($this->_discount->date_start)) {
            return $this->_discount->date_start;
        }

        return null;
    }

    /**
     * Get the discount end date
     *
     * @return DateTimeInterface|null The discount end date
     */
    public function date_end(): ?DateTimeInterface
    {
        if (!empty($this->_discount->date_end)) {
            return $this->_discount->date_end;
        }

        return null;
    }

    /**
     * Check if the discount is active
     *
     * @return bool Whether the discount is active
     */
    public function is_active(): bool
    {
        $now = now();
        $start = $this->date_start();
        $end = $this->date_end();

        if ($start && $now->lt($start)) {
            return false;
        }

        if ($end && $now->gt($end)) {
            return false;
        }

        return true;
    }

    /**
     * Check if the discount is percentage-based
     *
     * @return bool Whether the discount is percentage-based
     */
    public function is_percentage(): bool
    {
        return $this->type() === 'percent';
    }

    /**
     * Check if the discount is fixed amount
     *
     * @return bool Whether the discount is fixed amount
     */
    public function is_fixed(): bool
    {
        return $this->type() === 'fixed';
    }

    /**
     * Check if the discount is for shipping
     *
     * @return bool Whether the discount is for shipping
     */
    public function is_shipping(): bool
    {
        return $this->type() === 'shipping';
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     id: int,
     *     name: string,
     *     type_value: float,
     *     type_value_formatted: string,
     *     type: string,
     *     color: string|null,
     *     timer_list: array<int|string, mixed>,
     *     timer_details: array<int|string, mixed>,
     *     date_start: DateTimeInterface|null,
     *     date_end: DateTimeInterface|null,
     *     is_active: bool,
     *     is_percentage: bool,
     *     is_fixed: bool,
     *     is_shipping: bool
     * } The discount data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'type_value' => $this->type_value(),
            'type_value_formatted' => $this->type_value_formatted(),
            'type' => $this->type(),
            'color' => $this->color(),
            'timer_list' => $this->timer_list(),
            'timer_details' => $this->timer_details(),
            'date_start' => $this->date_start(),
            'date_end' => $this->date_end(),
            'is_active' => $this->is_active(),
            'is_percentage' => $this->is_percentage(),
            'is_fixed' => $this->is_fixed(),
            'is_shipping' => $this->is_shipping()
        ];
    }
}
