<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Product;

use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Shop\Shop;
use App\Models\Product\Quantity as QuantityModel;

/**
 * Class Quantity
 * 
 * Represents a product quantity in the Liquid template system.
 * This class provides access to quantity information and related store data.
 * 
 * @property-read QuantityModel $_quantity The underlying quantity model
 * @property-read mixed $_product The associated product
 * @property-read mixed $config The configuration data
 */
class Quantity extends AbstractDrop
{
    /**
     * The underlying quantity model instance.
     *
     * @var QuantityModel
     */
    protected QuantityModel $_quantity;

    /**
     * The associated product instance.
     *
     * @var mixed
     */
    protected $_product;

    /**
     * The configuration data.
     *
     * @var mixed
     */
    protected $config;

    /**
     * Create a new Quantity instance.
     *
     * @param QuantityModel $quantity The quantity model instance
     */
    public function __construct(QuantityModel $quantity)
    {
        $this->_quantity = $quantity;
    }

    /**
     * Get the quantity value.
     *
     * @return int The quantity value
     */
    public function quantity(): int
    {
        return (int) $this->_quantity->qty;
    }

    /**
     * Get the associated store.
     *
     * @return Shop|null The store instance or null if not available
     */
    public function store(): ?Shop
    {
        if ($this->_quantity->relationLoaded('shop') && $this->_quantity->shop) {
            return new Shop($this->_quantity->shop);
        }

        return null;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     quantity: int,
     *     store: Shop|null
     * } The array representation of the quantity
     */
    public function toArray(): array
    {
        return [
            'quantity' => $this->quantity(),
            'store' => $this->store(),
        ];
    }
}
