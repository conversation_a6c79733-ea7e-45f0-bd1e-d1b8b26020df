<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Product;

use App\Models\Product\ProductFiles;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

/**
 * Class File
 * 
 * Represents a product file in the Liquid template system.
 * Provides access to file data and functionality for templates.
 * 
 * Properties:
 * @property-read ProductFiles $_file The underlying file model
 * @property-read string $_product_url_handle The URL handle of the associated product
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Product
 */
class File extends AbstractDrop
{
    /**
     * @var ProductFiles The underlying file model
     */
    protected ProductFiles $_file;

    /**
     * @var string The URL handle of the associated product
     */
    protected string $_product_url_handle;

    /**
     * File constructor.
     * 
     * @param ProductFiles $file The file model instance
     * @param string $product_url_handle The URL handle of the associated product
     */
    public function __construct(ProductFiles $file, string $product_url_handle)
    {
        $this->_file = $file;
        $this->_product_url_handle = $product_url_handle;
    }

    /**
     * Get the file name.
     * 
     * @return string
     */
    public function name(): string
    {
        return (string) $this->_file->name;
    }

    /**
     * Check if the file is public.
     * 
     * @return bool
     */
    public function public(): bool
    {
        return (bool) $this->_file->public;
    }

    /**
     * Get the file download URL.
     * 
     * @return string
     */
    public function url(): string
    {
        return route('site.download.' . $this->_file->getDownloadType(), [
            'url_handle' => $this->_product_url_handle,
            'mask' => $this->_file->mask
        ]);
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     name: string,
     *     public: bool,
     *     url: string
     * }
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'public' => $this->public(),
            'url' => $this->url(),
        ];
    }
}
