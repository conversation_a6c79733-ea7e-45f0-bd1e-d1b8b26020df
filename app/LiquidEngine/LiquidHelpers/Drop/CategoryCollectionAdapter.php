<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\LiquidEngine\Traits\DropImageToArray;
use App\Models\Product\Category;

/**
 * CategoryCollectionAdapter Drop class for handling category collections in Liquid templates.
 *
 * This class adapts a Category model to work with Collection templates, providing
 * Shopify-compatible methods and properties for category collections. It handles
 * product filtering, sorting, pagination, and various collection-related operations.
 *
 * Usage:
 *   {{ collection.id }}
 *   {{ collection.name }}
 *   {{ collection.products }}
 *   {{ collection.products_count }}
 *   {{ collection.all_tags }}
 *
 * Properties:
 * - id: int
 * - name: string
 * - title: string
 * - description: string
 * - url: string
 * - image: LiquidImageAttribute|null
 * - products: array<Product>
 * - products_count: int
 * - all_tags: array<string>
 * - all_types: array<string>
 * - all_vendors: array<string>
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class CategoryCollectionAdapter extends Collection
{
    use DropImageToArray;

    /**
     * The underlying category model.
     * Stores the Category instance being adapted.
     *
     * @var Category
     */
    protected Category $_category;

    /**
     * The current page number.
     * Used for pagination of products.
     *
     * @var int
     */
    protected int $_page;

    /**
     * Whether to mute category-specific functionality.
     * When true, certain category-specific features are disabled.
     *
     * @var bool
     */
    protected bool $_mute_category;

    /**
     * The maximum number of products to return.
     * Null means no limit.
     *
     * @var int|null
     */
    protected ?int $_limit;

    /**
     * The field to sort products by.
     * Null means use default sorting.
     *
     * @var string|null
     */
    protected ?string $_sort_by;

    /**
     * Whether to reverse the sort order.
     *
     * @var bool
     */
    protected bool $_reverse;

    /**
     * Whether to include tags in the results.
     *
     * @var bool
     */
    protected bool $_with_tags;

    /**
     * The vendor to filter products by.
     * Null means no vendor filter.
     *
     * @var string|null
     */
    protected ?string $_vendor;

    /**
     * The product type to filter by.
     * Null means no type filter.
     *
     * @var int|null
     */
    protected ?int $_product_type;

    /**
     * Store original methods preserved.
     * Used for caching and optimization.
     *
     * @var array|null
     */
    protected ?array $original_products = null;

    /**
     * Last call tracker for liquid engine.
     * Used for debugging and performance tracking.
     *
     * @var string|null
     */
    protected ?string $_last_call = null;

    /**
     * Create a new CategoryCollectionAdapter instance.
     * Initializes the adapter with a category and configuration options.
     *
     * @param Category $category The category to adapt
     * @param int $page The page number for pagination
     * @param bool $mute_category Whether to mute category-specific functionality
     * @param int|null $limit The maximum number of products to return
     * @param string|null $sort_by The field to sort products by
     * @param bool $reverse Whether to reverse the sort order
     * @param bool $with_tags Whether to include tags in the results
     * @param string|null $vendor The vendor to filter products by
     * @param int|null $product_type The product type to filter by
     */
    public function __construct(
        Category $category,
        int $page = 1,
        bool $mute_category = false,
        ?int $limit = null,
        ?string $sort_by = null,
        bool $reverse = false,
        bool $with_tags = false,
        ?string $vendor = null,
        ?int $product_type = null
    ) {
        $this->_category = $category;
        $this->_page = $page;
        $this->_mute_category = $mute_category;
        $this->_limit = $limit;
        $this->_sort_by = $sort_by;
        $this->_reverse = $reverse;
        $this->_with_tags = $with_tags;
        $this->_vendor = $vendor;
        $this->_product_type = $product_type;
    }

    /**
     * Create a CategoryCollectionAdapter from a Category.
     * Factory method to create an adapter instance.
     *
     * @param Category $category The category to adapt
     * @param int $page The page number for pagination
     * @param bool $mute_category Whether to mute category-specific functionality
     * @param int|null $limit The maximum number of products to return
     * @param string|null $sort_by The field to sort products by
     * @param bool $reverse Whether to reverse the sort order
     * @param bool $with_tags Whether to include tags in the results
     * @param string|null $vendor The vendor to filter products by
     * @param int|null $product_type The product type to filter by
     * @return self The created adapter instance
     */
    public static function fromCategory(
        Category $category,
        int $page = 1,
        bool $mute_category = false,
        ?int $limit = null,
        ?string $sort_by = null,
        bool $reverse = false,
        bool $with_tags = false,
        ?string $vendor = null,
        ?int $product_type = null
    ): self {
        return new self($category, $page, $mute_category, $limit, $sort_by, $reverse, $with_tags, $vendor, $product_type);
    }

    /**
     * Get the collection ID.
     * Returns the ID of the underlying category.
     *
     * @return int The category ID
     */
    public function id(): int
    {
        return $this->_category->id;
    }

    /**
     * Get the collection name.
     * Returns the name of the underlying category.
     *
     * @return string The category name
     */
    public function name(): string
    {
        return $this->_category->name;
    }

    /**
     * Get the collection title (Shopify compatibility).
     * Returns the name of the underlying category.
     *
     * @return string The category name
     */
    public function title(): string
    {
        return $this->_category->name;
    }

    /**
     * Get the collection SEO title.
     * Returns the SEO title of the underlying category.
     *
     * @return string The SEO title
     */
    public function seo_title(): string
    {
        return $this->_category->seo_title;
    }

    /**
     * Get the collection SEO description.
     * Returns the SEO description of the underlying category.
     *
     * @return string The SEO description
     */
    public function seo_description(): string
    {
        return $this->_category->seo_description;
    }

    /**
     * Get the collection description.
     * Returns the description or SEO description of the underlying category.
     *
     * @return string The category description
     */
    public function description(): string
    {
        return $this->_category->description ?? $this->_category->seo_description ?? '';
    }

    /**
     * Get the collection URL handle.
     * Returns the URL handle of the underlying category.
     *
     * @return string The URL handle
     */
    public function url_handle(): string
    {
        return $this->_category->url_handle;
    }

    /**
     * Get the collection handle (Shopify compatibility).
     * Returns the URL handle of the underlying category.
     *
     * @return string The URL handle
     */
    public function handle(): string
    {
        return $this->_category->url_handle;
    }

    /**
     * Get the collection URL.
     * Returns the URL of the underlying category.
     *
     * @return string The category URL
     */
    public function url(): string
    {
        return $this->_category->url;
    }

    /**
     * Get the collection image.
     * Returns a LiquidImageWrapper for the category image if it exists.
     *
     * @return LiquidImageAttribute|null The category image or null
     */
    public function image(): ?LiquidImageAttribute
    {
        if ($this->has_image()) {
            return new LiquidImageWrapper($this->_category);
        }
        return null;
    }

    /**
     * Check if the collection has an image.
     * Returns whether the underlying category has an image.
     *
     * @return bool Whether the category has an image
     */
    public function has_image(): bool
    {
        return $this->_category->hasImage();
    }

    /**
     * Get the collection's default sort order.
     * Returns the default sorting method for the collection.
     *
     * @return string The default sort order
     */
    public function default_sort_by(): string
    {
        return 'manual';
    }

    /**
     * Get all tags used in the collection's products.
     * Returns an array of unique tags from the collection's products.
     *
     * @return array<string> The unique tags
     */
    public function all_tags(): array
    {
        return [];
    }

    /**
     * Get the number of products in the collection.
     * Returns the actual count of products in the category.
     *
     * @return int The number of products
     */
    public function products_count(): int
    {
        return $this->_category->real_products_count ?? 0;
    }

    /**
     * Get all products count.
     * Returns the total number of products in the category.
     *
     * @return int The total number of products
     */
    public function all_products_count(): int
    {
        return $this->products_count();
    }

    /**
     * Get the collection as a plain array.
     * Returns a complete array representation of the collection,
     * including all properties and nested data.
     *
     * @return array{
     *     id: int,
     *     name: string,
     *     title: string,
     *     description: string,
     *     url: string,
     *     url_handle: string,
     *     handle: string,
     *     image: array<string, mixed>|null,
     *     products: array<array<string, mixed>>,
     *     products_count: int,
     *     all_tags: array<string>,
     *     all_types: array<string>,
     *     all_vendors: array<string>,
     *     current_vendor: string|null,
     *     current_product_type: int|null,
     *     sort_by: string,
     *     reverse: bool,
     *     has_products: bool,
     *     empty: bool,
     *     size: int,
     *     length: int,
     *     count: int,
     *     template_suffix: string,
     *     has_only_default_variant: bool,
     *     parent_collection: array<string, mixed>|null,
     *     child_collections: array<array<string, mixed>>,
     *     has_children: bool,
     *     depth: int,
     *     is_root: bool,
     *     breadcrumbs: array<array<string, mixed>>
     * } The complete collection data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'title' => $this->title(),
            'description' => $this->description(),
            'url' => $this->url(),
            'url_handle' => $this->url_handle(),
            'handle' => $this->handle(),
            'image' => $this->image()?->toArray(),
            'products' => array_map(fn($product) => $product->toArray(), $this->products()),
            'products_count' => $this->products_count(),
            'all_tags' => $this->all_tags(),
            'all_types' => $this->all_types(),
            'all_vendors' => $this->all_vendors(),
            'current_vendor' => $this->current_vendor(),
            'current_product_type' => $this->current_product_type(),
            'sort_by' => $this->sort_by(),
            'reverse' => $this->reverse(),
            'has_products' => $this->has_products(),
            'empty' => $this->empty(),
            'size' => $this->size(),
            'length' => $this->length(),
            'count' => $this->count(),
            'template_suffix' => $this->template_suffix(),
            'has_only_default_variant' => $this->has_only_default_variant(),
            'parent_collection' => $this->parent_collection()?->toArray(),
            'child_collections' => array_map(fn($collection) => $collection->toArray(), $this->child_collections()),
            'has_children' => $this->has_children(),
            'depth' => $this->depth(),
            'is_root' => $this->is_root(),
            'breadcrumbs' => $this->breadcrumbs()
        ];
    }

    /**
     * Get CSS dimensions string for the image
     *
     * @param string|null $size
     * @return string|null
     */
    public function getCssDimensions($size = null): ?string
    {
        $width = $this->_category->width ?? 0;
        $height = $this->_category->height ?? 0;

        if ($width > 0 && $height > 0) {
            return sprintf('width: %dpx; height: %dpx;', $width, $height);
        }

        // Default dimensions if not available
        return 'width: 100px; height: 100px;';
    }

    /**
     * Get HTML dimensions string for the image
     *
     * @param string|null $size
     * @return string|null
     */
    public function getHtmlDimensions($size = null): ?string
    {
        $width = $this->_category->width ?? 0;
        $height = $this->_category->height ?? 0;

        if ($width > 0 && $height > 0) {
            return sprintf('width="%d" height="%d"', $width, $height);
        }

        // Default dimensions if not available
        return 'width="100" height="100"';
    }

    /**
     * Get image dimensions as an object
     *
     * @param string|null $size
     * @return object|null
     */
    public function getDimensions($size = null): ?object
    {
        $width = $this->_category->width ?? 0;
        $height = $this->_category->height ?? 0;

        if ($width > 0 && $height > 0) {
            return (object)[
                'width' => $width,
                'height' => $height
            ];
        }

        return (object)[
            'width' => 100,
            'height' => 100
        ];
    }

    /**
     * Get collection title (alias for title method)
     * Shopify-compatible method
     */
    public function collection_title(): string
    {
        return $this->_category->title ?? '';
    }

    /**
     * Get collection ID
     * Shopify-compatible method
     */
    public function collection_id(): int
    {
        return $this->_category->id ?? 0;
    }

    /**
     * Get collection content (alias for description)
     * Shopify-compatible method
     */
    public function content(): string
    {
        return $this->description();
    }

    /**
     * Get collection excerpt
     * Shopify-compatible method
     */
    public function excerpt(?int $limit = 100): string
    {
        $description = $this->description();
        if (strlen($description) <= $limit) {
            return $description;
        }
        return substr($description, 0, $limit) . '...';
    }

    /**
     * Get collection summary (short description)
     * Shopify-compatible method
     */
    public function summary(?int $limit = 50): string
    {
        return $this->excerpt($limit);
    }

    /**
     * Get collection link (formatted HTML link)
     * Shopify-compatible method
     */
    public function link(?string $text = null): string
    {
        $title = $text ?? $this->title();
        $url = $this->url();
        return "<a href=\"{$url}\">{$title}</a>";
    }

    /**
     * Get collection link with custom attributes
     * Shopify-compatible method
     */
    public function link_with_attributes(array $attributes = []): string
    {
        $url = $this->url();
        $title = $this->title();

        $attr_string = '';
        foreach ($attributes as $key => $value) {
            $attr_string .= " {$key}=\"{$value}\"";
        }

        return "<a href=\"{$url}\"{$attr_string}>{$title}</a>";
    }

    /**
     * Get all products in collection
     * Shopify-compatible method
     */
    public function all_products()
    {
        // Get all products without pagination
        $products = $this->_category->productsListing(1, $this->_mute_category, null, $this->_sort_by, $this->_reverse, $this->_with_tags, $this->_vendor, $this->_product_type);

        $result = [];
        foreach ($products as $product) {
            $result[] = new Product($product);
        }

        return $result;
    }

    /**
     * Check if collection has products
     * Shopify-compatible method
     */
    public function has_products(): bool
    {
        return $this->products_count() > 0;
    }

    /**
     * Check if collection is empty
     * Shopify-compatible method
     */
    public function empty(): bool
    {
        return !$this->has_products();
    }

    /**
     * Get collection size (number of products)
     * Shopify-compatible method
     */
    public function size(): int
    {
        return $this->products_count();
    }

    /**
     * Get collection length (alias for size)
     * Shopify-compatible method
     */
    public function length(): int
    {
        return $this->size();
    }

    /**
     * Get collection count (alias for size)
     * Shopify-compatible method
     */
    public function count(): int
    {
        return $this->size();
    }

    /**
     * Get collection sort order
     * Shopify-compatible method
     */
    public function sort_by(): string
    {
        return $this->_sort_by ?? 'default';
    }

    /**
     * Get current sort type
     * Shopify-compatible method
     */
    public function current_type(): string
    {
        return $this->sort_by();
    }

    /**
     * Check if collection is sorted in reverse
     * Shopify-compatible method
     */
    public function reverse(): bool
    {
        return $this->_reverse;
    }

    /**
     * Get collection tags
     * Shopify-compatible method
     */
    public function tags(): array
    {
        $products = $this->products();
        $tags = [];

        foreach ($products as $product) {
            if (method_exists($product, 'tags') && $product->tags()) {
                $tags = array_merge($tags, $product->tags());
            }
        }

        return array_unique($tags);
    }

    /**
     * Get all product types in collection
     * Shopify-compatible method
     */
    public function all_types(): array
    {
        $products = $this->products();
        $types = [];

        foreach ($products as $product) {
            if (method_exists($product, 'type') && $product->type()) {
                $types[] = $product->type();
            }
        }

        return array_unique($types);
    }

    /**
     * Get all vendors in collection
     * Shopify-compatible method
     */
    public function all_vendors(): array
    {
        $products = $this->products();
        $vendors = [];

        foreach ($products as $product) {
            if (method_exists($product, 'vendor') && $product->vendor()) {
                $vendors[] = $product->vendor();
            }
        }

        return array_unique($vendors);
    }

    /**
     * Get current vendor filter
     * Shopify-compatible method
     */
    public function current_vendor(): ?string
    {
        return $this->_vendor;
    }

    /**
     * Get current product type filter
     * Shopify-compatible method
     */
    public function current_product_type(): ?int
    {
        return $this->_product_type;
    }

    /**
     * Get products filtered by tag
     * Shopify-compatible method
     */
    public function products_by_tag(string $tag): array
    {
        $products = $this->products();
        $filtered = [];

        foreach ($products as $product) {
            if (method_exists($product, 'tags') && $product->tags()) {
                if (in_array($tag, $product->tags())) {
                    $filtered[] = $product;
                }
            }
        }

        return $filtered;
    }

    /**
     * Get products filtered by vendor
     * Shopify-compatible method
     */
    public function products_by_vendor(string $vendor): array
    {
        $products = $this->products();
        $filtered = [];

        foreach ($products as $product) {
            if (method_exists($product, 'vendor') && $product->vendor() === $vendor) {
                $filtered[] = $product;
            }
        }

        return $filtered;
    }

    /**
     * Get products filtered by type
     * Shopify-compatible method
     */
    public function products_by_type(string $type): array
    {
        $products = $this->products();
        $filtered = [];

        foreach ($products as $product) {
            if (method_exists($product, 'type') && $product->type() === $type) {
                $filtered[] = $product;
            }
        }

        return $filtered;
    }

    /**
     * Get first product in collection
     * Shopify-compatible method
     */
    public function first_product()
    {
        $products = $this->products();
        return !empty($products) ? $products[0] : null;
    }

    /**
     * Get last product in collection
     * Shopify-compatible method
     */
    public function last_product()
    {
        $products = $this->products();
        return !empty($products) ? end($products) : null;
    }

    /**
     * Get random product from collection
     * Shopify-compatible method
     */
    public function random_product()
    {
        $products = $this->products();
        if (empty($products)) {
            return null;
        }

        $random_index = array_rand($products);
        return $products[$random_index];
    }

    /**
     * Get collection metadata
     * Shopify-compatible method
     */
    public function metafields(): array
    {
        // Return custom fields or metadata if available
        return $this->_category->metafields ?? [];
    }

    /**
     * Get specific metafield value
     * Shopify-compatible method
     */
    public function metafield(string $namespace, string $key)
    {
        $metafields = $this->metafields();
        return $metafields[$namespace][$key] ?? null;
    }

    /**
     * Check if collection has metafields
     * Shopify-compatible method
     */
    public function has_metafields(): bool
    {
        $metafields = $this->metafields();
        return !empty($metafields);
    }

    /**
     * Get collection template suffix
     * Shopify-compatible method
     */
    public function template_suffix(): string
    {
        return $this->_category->template_suffix ?? '';
    }

    /**
     * Check if collection has only one product
     * Shopify-compatible method
     */
    public function has_only_default_variant(): bool
    {
        $products = $this->products();
        if (count($products) !== 1) {
            return false;
        }

        $product = $products[0];
        if (method_exists($product, 'has_only_default_variant')) {
            return $product->has_only_default_variant();
        }

        return false;
    }

    /**
     * Get collection as array for Vue components
     * Shopify-compatible method
     */
    public function to_vue(): array
    {
        return [
            'id' => $this->collection_id(),
            'title' => $this->title(),
            'handle' => $this->handle(),
            'description' => $this->description(),
            'url' => $this->url(),
            'image' => $this->image(),
            'has_image' => $this->has_image(),
            'products_count' => $this->products_count(),
            'has_products' => $this->has_products(),
            'sort_by' => $this->sort_by(),
            'reverse' => $this->reverse(),
            'tags' => $this->tags(),
            'all_vendors' => $this->all_vendors(),
            'all_types' => $this->all_types(),
            'template_suffix' => $this->template_suffix()
        ];
    }

    /**
     * Get collection formatted for JSON API
     * Shopify-compatible method
     */
    public function to_json(): string
    {
        return json_encode($this->to_vue());
    }

    /**
     * Get collection display name
     * Shopify-compatible method
     */
    public function display_name(): string
    {
        return $this->title();
    }

    /**
     * Get collection parent category
     * Shopify-compatible method
     */
    public function parent_collection()
    {
        if ($this->_category->parent_id) {
            $parent = Category::find($this->_category->parent_id);
            if ($parent) {
                return new static($parent);
            }
        }
        return null;
    }

    /**
     * Get collection children categories
     * Shopify-compatible method
     */
    public function child_collections(): array
    {
        $children = $this->_category->children ?? [];
        $result = [];

        foreach ($children as $child) {
            $result[] = new static($child);
        }

        return $result;
    }

    /**
     * Check if collection has children
     * Shopify-compatible method
     */
    public function has_children(): bool
    {
        return count($this->child_collections()) > 0;
    }

    /**
     * Get collection depth level
     * Shopify-compatible method
     */
    public function depth(): int
    {
        return $this->_category->depth ?? 0;
    }

    /**
     * Check if collection is root level
     * Shopify-compatible method
     */
    public function is_root(): bool
    {
        return $this->depth() === 0;
    }

    /**
     * Get collection breadcrumbs
     * Shopify-compatible method
     */
    public function breadcrumbs(): array
    {
        $breadcrumbs = [];
        $current = $this->_category;

        while ($current && $current->parent_id) {
            $parent = Category::find($current->parent_id);
            if ($parent) {
                array_unshift($breadcrumbs, new static($parent));
                $current = $parent;
            } else {
                break;
            }
        }

        return $breadcrumbs;
    }
}

/**
 * LiquidImageWrapper class for handling category images in Liquid templates.
 *
 * This class wraps a Category model to provide image-related functionality
 * in a format compatible with Liquid templates.
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class LiquidImageWrapper extends \App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute
{
    /**
     * The underlying category model.
     * Stores the Category instance being wrapped.
     *
     * @var Category
     */
    protected Category $_category;

    /**
     * Create a new LiquidImageWrapper instance.
     * Initializes the wrapper with a category.
     *
     * @param Category $category The category to wrap
     */
    public function __construct(Category $category)
    {
        $this->_category = $category;
        parent::__construct($category->image);
    }

    /**
     * Get CSS dimensions for the image.
     * Returns a CSS string with width and height.
     *
     * @param string|null $size The size to get dimensions for
     * @return string|null The CSS dimensions or null
     */
    public function getCssDimensions(?string $size = null): ?string
    {
        $dimensions = $this->getDimensions($size);
        if (!$dimensions) {
            return null;
        }
        return "width: {$dimensions->width}px; height: {$dimensions->height}px;";
    }

    /**
     * Get HTML dimensions for the image.
     * Returns HTML width and height attributes.
     *
     * @param string|null $size The size to get dimensions for
     * @return string|null The HTML dimensions or null
     */
    public function getHtmlDimensions(?string $size = null): ?string
    {
        $dimensions = $this->getDimensions($size);
        if (!$dimensions) {
            return null;
        }
        return "width=\"{$dimensions->width}\" height=\"{$dimensions->height}\"";
    }

    /**
     * Get dimensions for the image.
     * Returns an object with width and height.
     *
     * @param string|null $size The size to get dimensions for
     * @return object{width: int, height: int}|null The dimensions or null
     */
    public function getDimensions(?string $size = null): ?object
    {
        if (!$this->_category->hasImage()) {
            return null;
        }

        $sizes = $this->getMaxThumbSizes();
        $size = $size ?: 'original';

        return (object) [
            'width' => $sizes[$size]['width'] ?? 0,
            'height' => $sizes[$size]['height'] ?? 0
        ];
    }

    /**
     * Get maximum thumbnail sizes.
     * Returns an array of available thumbnail sizes.
     *
     * @return array<string, array{width: int, height: int}> The thumbnail sizes
     */
    public function getMaxThumbSizes(): array
    {
        return [
            'original' => [
                'width' => $this->_category->image->width,
                'height' => $this->_category->image->height
            ],
            'thumb' => [
                'width' => 100,
                'height' => 100
            ],
            'small' => [
                'width' => 300,
                'height' => 300
            ],
            'medium' => [
                'width' => 600,
                'height' => 600
            ],
            'large' => [
                'width' => 900,
                'height' => 900
            ]
        ];
    }
}
