<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Support\Facades\Config;

/**
 * Money Drop Class
 * 
 * This class provides a structured way to handle monetary values in Liquid templates.
 * It includes support for Shopify compatibility, accessibility, SEO, and analytics.
 * 
 * @property float $amount The monetary amount in the smallest currency unit
 * @property string $currency The currency code (e.g., USD, EUR)
 * @property string $currency_symbol The currency symbol (e.g., $, €)
 * @property int $decimal_places The number of decimal places for the currency
 * @property string $decimal_separator The decimal separator character
 * @property string $thousands_separator The thousands separator character
 * @property array $format_options Additional formatting options
 */
class Money extends AbstractDrop
{
    /**
     * @var float The monetary amount in the smallest currency unit
     */
    protected float $amount;

    /**
     * @var string The currency code (e.g., USD, EUR)
     */
    protected string $currency;

    /**
     * @var string The currency symbol (e.g., $, €)
     */
    protected string $currency_symbol;

    /**
     * @var int The number of decimal places for the currency
     */
    protected int $decimal_places;

    /**
     * @var string The decimal separator character
     */
    protected string $decimal_separator;

    /**
     * @var string The thousands separator character
     */
    protected string $thousands_separator;

    /**
     * @var array<string, mixed> Additional formatting options
     */
    protected array $format_options;

    /**
     * Create a new Money instance.
     *
     * @param float $amount The monetary amount in the smallest currency unit
     * @param string $currency The currency code (e.g., USD, EUR)
     * @param array<string, mixed> $format_options Additional formatting options
     */
    public function __construct(
        float $amount,
        string $currency = 'USD',
        array $format_options = []
    ) {
        $this->amount = $amount;
        $this->currency = strtoupper($currency);
        $this->format_options = $format_options;

        // Load currency configuration
        $currency_config = Config::get('currencies.' . $this->currency, [
            'symbol' => '$',
            'decimal_places' => 2,
            'decimal_separator' => '.',
            'thousands_separator' => ',',
        ]);

        $this->currency_symbol = $currency_config['symbol'];
        $this->decimal_places = $currency_config['decimal_places'];
        $this->decimal_separator = $currency_config['decimal_separator'];
        $this->thousands_separator = $currency_config['thousands_separator'];
    }

    /**
     * Get the monetary amount.
     *
     * @return float
     */
    public function amount(): float
    {
        return $this->amount;
    }

    /**
     * Get the currency code.
     *
     * @return string
     */
    public function currency(): string
    {
        return $this->currency;
    }

    /**
     * Get the currency symbol.
     *
     * @return string
     */
    public function currency_symbol(): string
    {
        return $this->currency_symbol;
    }

    /**
     * Get the number of decimal places.
     *
     * @return int
     */
    public function decimal_places(): int
    {
        return $this->decimal_places;
    }

    /**
     * Get the decimal separator.
     *
     * @return string
     */
    public function decimal_separator(): string
    {
        return $this->decimal_separator;
    }

    /**
     * Get the thousands separator.
     *
     * @return string
     */
    public function thousands_separator(): string
    {
        return $this->thousands_separator;
    }

    /**
     * Format the monetary amount with currency symbol.
     *
     * @return string
     */
    public function format(): string
    {
        $formatted = number_format(
            $this->amount / pow(10, $this->decimal_places),
            $this->decimal_places,
            $this->decimal_separator,
            $this->thousands_separator
        );

        return $this->currency_symbol . $formatted;
    }

    /**
     * Format the monetary amount without currency symbol.
     *
     * @return string
     */
    public function format_without_currency(): string
    {
        return number_format(
            $this->amount / pow(10, $this->decimal_places),
            $this->decimal_places,
            $this->decimal_separator,
            $this->thousands_separator
        );
    }

    /**
     * Get accessibility attributes for the monetary value.
     *
     * @return array<string, string>
     */
    public function accessibility(): array
    {
        return [
            'role' => 'text',
            'aria-label' => $this->format() . ' ' . $this->currency,
        ];
    }

    /**
     * Get structured data for SEO.
     *
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'PriceSpecification',
            'price' => $this->amount / pow(10, $this->decimal_places),
            'priceCurrency' => $this->currency,
        ];
    }

    /**
     * Convert the monetary value to a Shopify-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toShopifyArray(): array
    {
        return [
            'amount' => $this->amount,
            'currency' => $this->currency,
            'currency_symbol' => $this->currency_symbol,
            'formatted' => $this->format(),
            'formatted_without_currency' => $this->format_without_currency(),
        ];
    }

    /**
     * Convert the monetary value to an SEO-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toSeoArray(): array
    {
        return [
            'amount' => $this->amount / pow(10, $this->decimal_places),
            'currency' => $this->currency,
            'formatted' => $this->format(),
            'structured_data' => $this->structured_data(),
        ];
    }

    /**
     * Convert the monetary value to an analytics-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toAnalyticsArray(): array
    {
        return [
            'amount' => $this->amount / pow(10, $this->decimal_places),
            'currency' => $this->currency,
            'formatted' => $this->format(),
            'decimal_places' => $this->decimal_places,
        ];
    }

    /**
     * Convert the monetary value to an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'amount' => $this->amount,
            'currency' => $this->currency,
            'currency_symbol' => $this->currency_symbol,
            'decimal_places' => $this->decimal_places,
            'decimal_separator' => $this->decimal_separator,
            'thousands_separator' => $this->thousands_separator,
            'format_options' => $this->format_options,
            'formatted' => $this->format(),
            'formatted_without_currency' => $this->format_without_currency(),
            'accessibility' => $this->accessibility(),
            'structured_data' => $this->structured_data(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics' => $this->toAnalyticsArray(),
        ];
    }
} 