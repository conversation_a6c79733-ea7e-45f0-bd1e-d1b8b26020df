<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Support\Facades\Config;

/**
 * Meta Drop Class
 * 
 * This class provides a structured way to handle meta data in Liquid templates.
 * It includes support for Shopify compatibility, accessibility, SEO, and analytics.
 * 
 * @property array $title The page title and related meta data
 * @property array $description The meta description and related data
 * @property array $keywords The meta keywords and related data
 * @property array $robots The robots meta directives
 * @property array $open_graph The Open Graph meta data
 * @property array $twitter The Twitter Card meta data
 * @property array $canonical The canonical URL data
 * @property array $alternate The alternate language URLs
 * @property array $structured_data The structured data for SEO
 */
class Meta extends AbstractDrop
{
    /**
     * @var array<string, mixed> The page title and related meta data
     */
    protected array $title;

    /**
     * @var array<string, mixed> The meta description and related data
     */
    protected array $description;

    /**
     * @var array<string, mixed> The meta keywords and related data
     */
    protected array $keywords;

    /**
     * @var array<string, mixed> The robots meta directives
     */
    protected array $robots;

    /**
     * @var array<string, mixed> The Open Graph meta data
     */
    protected array $open_graph;

    /**
     * @var array<string, mixed> The Twitter Card meta data
     */
    protected array $twitter;

    /**
     * @var array<string, mixed> The canonical URL data
     */
    protected array $canonical;

    /**
     * @var array<string, mixed> The alternate language URLs
     */
    protected array $alternate;

    /**
     * @var array<string, mixed> The structured data for SEO
     */
    protected array $structured_data;

    /**
     * Create a new Meta instance.
     *
     * @param array<string, mixed> $title The page title and related meta data
     * @param array<string, mixed> $description The meta description and related data
     * @param array<string, mixed> $keywords The meta keywords and related data
     * @param array<string, mixed> $robots The robots meta directives
     * @param array<string, mixed> $open_graph The Open Graph meta data
     * @param array<string, mixed> $twitter The Twitter Card meta data
     * @param array<string, mixed> $canonical The canonical URL data
     * @param array<string, mixed> $alternate The alternate language URLs
     * @param array<string, mixed> $structured_data The structured data for SEO
     */
    public function __construct(
        array $title = [],
        array $description = [],
        array $keywords = [],
        array $robots = [],
        array $open_graph = [],
        array $twitter = [],
        array $canonical = [],
        array $alternate = [],
        array $structured_data = []
    ) {
        $this->title = $title;
        $this->description = $description;
        $this->keywords = $keywords;
        $this->robots = $robots;
        $this->open_graph = $open_graph;
        $this->twitter = $twitter;
        $this->canonical = $canonical;
        $this->alternate = $alternate;
        $this->structured_data = $structured_data;
    }

    /**
     * Get the page title and related meta data.
     *
     * @return array<string, mixed>
     */
    public function title(): array
    {
        return $this->title;
    }

    /**
     * Get the meta description and related data.
     *
     * @return array<string, mixed>
     */
    public function description(): array
    {
        return $this->description;
    }

    /**
     * Get the meta keywords and related data.
     *
     * @return array<string, mixed>
     */
    public function keywords(): array
    {
        return $this->keywords;
    }

    /**
     * Get the robots meta directives.
     *
     * @return array<string, mixed>
     */
    public function robots(): array
    {
        return $this->robots;
    }

    /**
     * Get the Open Graph meta data.
     *
     * @return array<string, mixed>
     */
    public function open_graph(): array
    {
        return $this->open_graph;
    }

    /**
     * Get the Twitter Card meta data.
     *
     * @return array<string, mixed>
     */
    public function twitter(): array
    {
        return $this->twitter;
    }

    /**
     * Get the canonical URL data.
     *
     * @return array<string, mixed>
     */
    public function canonical(): array
    {
        return $this->canonical;
    }

    /**
     * Get the alternate language URLs.
     *
     * @return array<string, mixed>
     */
    public function alternate(): array
    {
        return $this->alternate;
    }

    /**
     * Get the structured data for SEO.
     *
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return $this->structured_data;
    }

    /**
     * Get accessibility attributes for meta data.
     *
     * @return array<string, string>
     */
    public function accessibility(): array
    {
        return [
            'role' => 'contentinfo',
            'aria-label' => 'Page meta information',
        ];
    }

    /**
     * Convert the meta data to a Shopify-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toShopifyArray(): array
    {
        return [
            'title' => $this->title['main'] ?? '',
            'description' => $this->description['main'] ?? '',
            'keywords' => $this->keywords['main'] ?? '',
            'robots' => $this->robots['main'] ?? '',
            'canonical_url' => $this->canonical['url'] ?? '',
            'alternate_urls' => $this->alternate['urls'] ?? [],
            'open_graph' => $this->open_graph,
            'twitter' => $this->twitter,
        ];
    }

    /**
     * Convert the meta data to an SEO-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toSeoArray(): array
    {
        return [
            'title' => $this->title,
            'description' => $this->description,
            'keywords' => $this->keywords,
            'robots' => $this->robots,
            'canonical' => $this->canonical,
            'alternate' => $this->alternate,
            'open_graph' => $this->open_graph,
            'twitter' => $this->twitter,
            'structured_data' => $this->structured_data,
        ];
    }

    /**
     * Convert the meta data to an analytics-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toAnalyticsArray(): array
    {
        return [
            'page_title' => $this->title['main'] ?? '',
            'page_description' => $this->description['main'] ?? '',
            'page_keywords' => $this->keywords['main'] ?? '',
            'canonical_url' => $this->canonical['url'] ?? '',
            'alternate_languages' => count($this->alternate['urls'] ?? []),
            'has_structured_data' => !empty($this->structured_data),
        ];
    }

    /**
     * Convert the meta data to an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'title' => $this->title,
            'description' => $this->description,
            'keywords' => $this->keywords,
            'robots' => $this->robots,
            'open_graph' => $this->open_graph,
            'twitter' => $this->twitter,
            'canonical' => $this->canonical,
            'alternate' => $this->alternate,
            'structured_data' => $this->structured_data,
            'accessibility' => $this->accessibility(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics' => $this->toAnalyticsArray(),
        ];
    }
} 