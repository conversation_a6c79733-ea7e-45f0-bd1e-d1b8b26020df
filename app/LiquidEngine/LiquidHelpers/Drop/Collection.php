<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 5.9.2019 г.
 * Time: 09:10 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\LiquidEngine\Helpers\Catalog\Product\Sorting;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;
use App\Models\Collections\Collections;

class Collection extends AbstractDrop
{
    use DropImageToArray;

    protected \App\Models\Collections\Collections $_collection;

    /**
     * Collection constructor
     *
     * @param Collections $collection
     */
    public function __construct(Collections $collection)
    {
        $this->_collection = $collection;
    }

    /**
     * Factory method to create a Collection drop
     *
     * @param Collections $collection
     * @return self
     */
    public static function make(Collections $collection): self
    {
        return new self($collection);
    }

    /**
     * Get the collection ID
     *
     * @return int
     */
    public function id()
    {
        return $this->_collection->id;
    }

    /**
     * Get the collection name
     *
     * @return string
     */
    public function name()
    {
        return $this->_collection->name;
    }

    /**
     * Get the collection title (Shopify compatibility)
     *
     * @return string
     */
    public function title()
    {
        return $this->_collection->name;
    }

    /**
     * Get the collection SEO title
     *
     * @return string
     */
    public function seo_title()
    {
        return $this->_collection->seo_title;
    }

    /**
     * Get the collection SEO description
     *
     * @return string
     */
    public function seo_description()
    {
        return $this->_collection->seo_description;
    }

    /**
     * Get the collection description
     *
     * @return string
     */
    public function description()
    {
        return $this->_collection->description ?? $this->_collection->seo_description ?? '';
    }

    /**
     * Get the collection URL handle
     *
     * @return string
     */
    public function url_handle()
    {
        return $this->_collection->url_handle;
    }

    /**
     * Get the collection handle (Shopify compatibility)
     *
     * @return string
     */
    public function handle()
    {
        return $this->_collection->url_handle;
    }

    /**
     * Get the collection URL
     *
     * @return string
     */
    public function url()
    {
        return $this->_collection->url;
    }

    /**
     * Get the collection image
     *
     * @return mixed
     */
    public function image()
    {
        if ($this->_collection->hasImage()) {
            $formatter = new UrlImageFormat($this->_collection);
            return $formatter->getLiquidImage();
        }
        return null;
    }

    /**
     * Check if the collection has an image
     *
     * @return bool
     */
    public function has_image(): bool
    {
        return $this->_collection->hasImage();
    }

    /**
     * Get the collection's default sort order
     *
     * @return string
     */
    public function default_sort_by()
    {
        return $this->_collection->default_sort_by ?? 'manual';
    }

    /**
     * Get all tags used in the collection's products
     *
     * @return array
     */
    public function all_tags()
    {
        if (method_exists($this->_collection, 'allTags')) {
            return $this->_collection->allTags()->pluck('name', 'url_handle')->toArray();
        }
        return [];
    }

    /**
     * Get the number of products in the collection
     *
     * @return int
     */
    public function products_count()
    {
        return $this->_collection->products_count;
    }

    /**
     * Get all products count (removes debug statement)
     *
     * @return int
     */
    public function all_products_count()
    {
        return $this->products_count();
    }

    /**
     * Get the products in the collection
     *
     * @return mixed
     */
    public function products()
    {
        list($sort, $direction) = Sorting::parse($this->_collection->default_sort_by);
        return Sorting::sort($sort, $direction, $this->_collection->products()->listing());
    }

    /**
     * Get featured image URL
     *
     * @return string|null
     */
    public function featured_image()
    {
        if ($this->has_image()) {
            return $this->_collection->image;
        }
        return null;
    }

    /**
     * Get the published date of the collection
     *
     * @return Date
     */
    public function published_at()
    {
        return new Date($this->_collection->created_at);
    }

    /**
     * Convert the collection to array with enhanced Shopify compatibility
     *
     * @return array
     */
    public function toArray(): array
    {
        $array = [
            // Core collection data
            'id' => $this->id(),
            'name' => $this->name(),
            'title' => $this->title(),
            'description' => $this->description(),
            'handle' => $this->handle(),
            'url' => $this->url(),
            'url_handle' => $this->url_handle(),

            // SEO data
            'seo_title' => $this->seo_title(),
            'seo_description' => $this->seo_description(),

            // Image data
            'has_image' => $this->has_image(),
            'image' => $this->getImageArray(),
            'featured_image' => $this->featured_image(),

            // Product data
            'products_count' => $this->products_count(),
            'all_products_count' => $this->all_products_count(),

            // Sorting and filtering
            'default_sort_by' => $this->default_sort_by(),
            'sort_by' => $this->sort_by(),
            'all_tags' => $this->all_tags(),
            'tags' => $this->tags(),
            'all_types' => $this->all_types(),
            'all_vendors' => $this->all_vendors(),
            'current_type' => $this->current_type(),
            'current_vendor' => $this->current_vendor(),
            'filters' => $this->filters(),
            'sort_options' => $this->sort_options(),

            // Timestamps (Shopify compatibility)
            'created_at' => $this->created_at(),
            'updated_at' => $this->updated_at(),
            'published_at' => $this->published_at(),

            // Template
            'template_suffix' => $this->template_suffix()
        ];

        if ($this->has_image()) {
            $array = array_merge($array, $this->imageToArray());
        }

        return $array;
    }

    /**
     * Get the collection creation timestamp (Shopify compatibility)
     *
     * @return string|null
     */
    public function created_at(): ?string
    {
        return $this->_collection->created_at ?
            $this->_collection->created_at->toISOString() :
            null;
    }

    /**
     * Get the collection update timestamp (Shopify compatibility)
     *
     * @return string|null
     */
    public function updated_at(): ?string
    {
        return $this->_collection->updated_at ?
            $this->_collection->updated_at->toISOString() :
            null;
    }

    /**
     * Get the current sort order applied to the collection (Shopify compatibility)
     *
     * @return string|null
     */
    public function sort_by(): ?string
    {
        // This would typically come from request parameters
        return request('sort_by') ?? $this->default_sort_by();
    }

    /**
     * Get all product types in the collection (Shopify compatibility)
     *
     * @return array
     */
    public function all_types(): array
    {
        if (method_exists($this->_collection, 'allTypes')) {
            return $this->_collection->allTypes()->pluck('name')->toArray();
        }
        return [];
    }

    /**
     * Get all product vendors in the collection (Shopify compatibility)
     *
     * @return array
     */
    public function all_vendors(): array
    {
        if (method_exists($this->_collection, 'allVendors')) {
            return $this->_collection->allVendors()->pluck('name')->toArray();
        }
        return [];
    }

    /**
     * Get the current product type filter (Shopify compatibility)
     *
     * @return string|null
     */
    public function current_type(): ?string
    {
        return request('type');
    }

    /**
     * Get the current vendor filter (Shopify compatibility)
     *
     * @return string|null
     */
    public function current_vendor(): ?string
    {
        return request('vendor');
    }

    /**
     * Get available filters for the collection (Shopify compatibility)
     *
     * @return array
     */
    public function filters(): array
    {
        $filters = [];

        // Add type filter if collection has multiple types
        $types = $this->all_types();
        if (count($types) > 1) {
            $filters[] = [
                'type' => 'list',
                'label' => 'Type',
                'param_name' => 'type',
                'values' => $types
            ];
        }

        // Add vendor filter if collection has multiple vendors
        $vendors = $this->all_vendors();
        if (count($vendors) > 1) {
            $filters[] = [
                'type' => 'list',
                'label' => 'Vendor',
                'param_name' => 'vendor',
                'values' => $vendors
            ];
        }

        return $filters;
    }

    /**
     * Get available sort options for the collection (Shopify compatibility)
     *
     * @return array
     */
    public function sort_options(): array
    {
        return [
            ['name' => 'Manual', 'value' => 'manual'],
            ['name' => 'Best selling', 'value' => 'best-selling'],
            ['name' => 'Alphabetically, A-Z', 'value' => 'title-ascending'],
            ['name' => 'Alphabetically, Z-A', 'value' => 'title-descending'],
            ['name' => 'Price, low to high', 'value' => 'price-ascending'],
            ['name' => 'Price, high to low', 'value' => 'price-descending'],
            ['name' => 'Date, old to new', 'value' => 'created-ascending'],
            ['name' => 'Date, new to old', 'value' => 'created-descending']
        ];
    }

    /**
     * Get currently applied tags to the collection (Shopify compatibility)
     *
     * @return array
     */
    public function tags(): array
    {
        $currentTags = request('tag');
        if ($currentTags) {
            return is_array($currentTags) ? $currentTags : [$currentTags];
        }
        return [];
    }

    /**
     * Get the template suffix for custom templates (Shopify compatibility)
     *
     * @return string|null
     */
    public function template_suffix(): ?string
    {
        return $this->_collection->template ?? null;
    }

}
