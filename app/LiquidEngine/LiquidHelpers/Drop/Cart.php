<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Models\Store\Cart as CartModel;
use App\Models\Store\CartItem as CartItemModel;
use Liquid\Drop;

/**
 * Cart Drop
 *
 * Provides access to the current cart information in Liquid templates.
 * This follows Shopify's cart object pattern.
 */
class Cart extends Drop
{
    /**
     * @var CartModel|null
     */
    protected ?CartModel $cart;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->cart = CartModel::getCurrent();
    }

    /**
     * Get all cart items
     *
     * @return array
     */
    public function items(): array
    {
        if (!$this->cart) {
            return [];
        }

        return $this->cart->items->map(function (CartItemModel $item) {
            return new CartItem($item);
        })->all();
    }

    /**
     * Get the total number of items in the cart
     *
     * @return int
     */
    public function item_count(): int
    {
        if (!$this->cart) {
            return 0;
        }

        return $this->cart->items->sum('quantity');
    }

    /**
     * Get the cart's total price
     *
     * @return float
     */
    public function total_price(): float
    {
        if (!$this->cart) {
            return 0.0;
        }

        return (float) $this->cart->total;
    }

    /**
     * Get the cart's subtotal price (before discounts)
     *
     * @return float
     */
    public function subtotal_price(): float
    {
        if (!$this->cart) {
            return 0.0;
        }

        return (float) $this->cart->subtotal;
    }

    /**
     * Get the cart's total discount amount
     *
     * @return float
     */
    public function total_discount(): float
    {
        if (!$this->cart) {
            return 0.0;
        }

        return (float) ($this->cart->subtotal - $this->cart->total);
    }

    /**
     * Get the cart's currency code
     *
     * @return string
     */
    public function currency(): string
    {
        return site('currency');
    }

    /**
     * Check if the cart is empty
     *
     * @return bool
     */
    public function is_empty(): bool
    {
        return !$this->cart || $this->cart->items->isEmpty();
    }

    /**
     * Get the cart's note
     *
     * @return string|null
     */
    public function note(): ?string
    {
        if (!$this->cart) {
            return null;
        }

        return $this->cart->note;
    }

    /**
     * Get the cart's attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        if (!$this->cart) {
            return [];
        }

        return $this->cart->attributes ?? [];
    }

    /**
     * Get the cart's original total price
     *
     * @return float
     */
    public function original_total_price(): float
    {
        return $this->subtotal_price();
    }

    /**
     * Get the cart's total tax amount
     *
     * @return float
     */
    public function total_tax(): float
    {
        if (!$this->cart) {
            return 0.0;
        }

        return (float) $this->cart->tax_total;
    }

    /**
     * Get the cart's total weight
     *
     * @return float
     */
    public function total_weight(): float
    {
        if (!$this->cart) {
            return 0.0;
        }

        return (float) $this->cart->items->sum(function (CartItemModel $item) {
            return $item->quantity * ($item->product->weight ?? 0);
        });
    }

    /**
     * Get the cart's discount codes
     *
     * @return array
     */
    public function discount_codes(): array
    {
        if (!$this->cart) {
            return [];
        }

        $discounts = [];

        if ($this->cart->discount_code) {
            $discounts[] = [
                'code' => $this->cart->discount_code,
                'amount' => $this->cart->discount_amount,
                'type' => $this->cart->discount_type
            ];
        }

        return $discounts;
    }

    /**
     * Returns true if the cart is empty, false otherwise.
     * Shopify compatibility method (alias for is_empty)
     *
     * @return bool
     */
    public function empty(): bool
    {
        return $this->is_empty();
    }

    /**
     * The subtotal price of all items in the cart as string
     * Shopify compatibility method
     *
     * @return string
     */
    public function items_subtotal_price(): string
    {
        return number_format($this->subtotal_price(), 2, '.', '');
    }

    /**
     * Whether taxes are included in the cart total
     * Shopify compatibility method
     *
     * @return bool
     */
    public function taxes_included(): bool
    {
        // Based on CloudCart tax settings
        return (bool) \get_option('tax_default_customer_group_display_prices_in_catalog');
    }

    /**
     * Whether duties are included in the cart total
     * Shopify compatibility method
     *
     * @return bool
     */
    public function duties_included(): bool
    {
        // CloudCart doesn't have separate duties, they're included in tax calculation
        return $this->taxes_included();
    }

    /**
     * Returns true if the cart requires shipping
     * Shopify compatibility method
     *
     * @return bool
     */
    public function requires_shipping(): bool
    {
        foreach ($this->items() as $item) {
            if (method_exists($item, 'requires_shipping') && $item->requires_shipping()) {
                return true;
            }
        }
        return false;
    }

    /**
     * Convert the cart to array with enhanced Shopify compatibility
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            // Core cart data
            'note' => $this->note(),
            'attributes' => $this->attributes(),
            'currency' => $this->currency(),

            // Item data
            'items' => $this->items(),
            'item_count' => $this->item_count(),

            // Pricing
            'total_price' => $this->total_price(),
            'subtotal_price' => $this->subtotal_price(),
            'items_subtotal_price' => $this->items_subtotal_price(),
            'original_total_price' => $this->original_total_price(),
            'total_discount' => $this->total_discount(),
            'total_tax' => $this->total_tax(),

            // Tax and duties
            'taxes_included' => $this->taxes_included(),
            'duties_included' => $this->duties_included(),
            'total_weight' => $this->total_weight(),

            // State
            'empty' => $this->empty(),
            'is_empty' => $this->is_empty(),
            'requires_shipping' => $this->requires_shipping(),

            // Discounts
            'discount_codes' => $this->discount_codes(),
            'cart_level_discount_applications' => $this->cart_level_discount_applications(),

            // Checkout
            'checkout_url' => $this->checkout_url(),
            'checkout_charge_amount' => $this->checkout_charge_amount(),
        ];
    }

    /**
     * Cart-level discount applications
     * Shopify compatibility method
     *
     * @return array
     */
    public function cart_level_discount_applications(): array
    {
        if (!$this->cart || !$this->cart->discount_code) {
            return [];
        }

        return [
            [
                'type' => 'discount_code',
                'title' => $this->cart->discount_code,
                'description' => $this->cart->discount_code,
                'value' => $this->cart->discount_amount,
                'value_type' => $this->cart->discount_type,
                'allocation_method' => 'across',
                'target_selection' => 'all',
                'target_type' => 'line_item'
            ]
        ];
    }

    /**
     * URL to proceed to checkout
     * Shopify compatibility method
     *
     * @return string
     */
    public function checkout_url(): string
    {
        return url('checkout');
    }

    /**
     * Additional checkout charge amount (e.g., payment fees)
     * Shopify compatibility method
     *
     * @return float
     */
    public function checkout_charge_amount(): float
    {
        // CloudCart might have payment fees or additional charges
        if (!$this->cart) {
            return 0.0;
        }

        // Return any additional fees (payment fees, handling fees, etc.)
        return (float) ($this->cart->payment_fee ?? 0);
    }
}
