<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Helper\Format;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Variant;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use App\Models\Store\CartItem as CartItemModel;
use Liquid\Drop;

/**
 * CartItem Drop
 *
 * Provides access to cart item information in Liquid templates.
 * This follows Shopify's line_item object pattern.
 */
class CartItem extends Drop
{
    /**
     * @var CartItemModel
     */
    protected $_cartItem;

    /**
     * Constructor
     *
     * @param CartItemModel $_cartItem
     */
    public function __construct(CartItemModel $_cartItem)
    {
        $this->_cartItem = $_cartItem;
    }

    /**
     * Get the cart item ID
     *
     * @return int
     */
    public function id(): int
    {
        return (int) $this->_cartItem->id;
    }

    /**
     * Get the cart item product ID
     *
     * @return int
     */
    public function product_id(): int
    {
        return $this->_cartItem->product_id;
    }

    /**
     * Get the cart item variant ID
     *
     * @return int|null
     */
    public function variant_id(): ?int
    {
        return $this->_cartItem->variant_id;
    }

    /**
     * Get the cart item quantity
     *
     * @return int
     */
    public function quantity(): int
    {
        return (int) $this->_cartItem->quantity;
    }

    /**
     * Get the cart item title
     *
     * @return string
     */
    public function title(): string
    {
        return $this->_cartItem->title ?? '';
    }

    /**
     * Get the cart item variant title
     *
     * @return string|null
     */
    public function variant_title(): ?string
    {
        if (!$this->_cartItem->variant_id) {
            return 'Default Title';
        }

        return $this->_cartItem->variant->title ?? null;
    }

    /**
     * Get the cart item price
     *
     * @return float
     */
    public function price(): float
    {
        return (float) $this->_cartItem->price;
    }

    /**
     * Get the cart item line price (price * quantity)
     *
     * @return float
     */
    public function line_price(): float
    {
        return (float) ($this->_cartItem->price * $this->_cartItem->quantity);
    }

    /**
     * Get the cart item final line price (after discounts)
     *
     * @return float
     */
    public function final_line_price(): float
    {
        return (float) ($this->_cartItem->final_price * $this->_cartItem->quantity);
    }

    /**
     * Get the cart item final price (after discounts)
     *
     * @return float
     */
    public function final_price(): float
    {
        return (float) $this->_cartItem->final_price;
    }

    /**
     * Get the cart item URL
     *
     * @return string|null
     */
    public function url(): ?string
    {
        return $this->_cartItem->product->url ?? null;
    }

    /**
     * Get the cart item image
     *
     * @return string|null
     */
    public function image(): ?string
    {
        return $this->_cartItem->product->featured_image ?? null;
    }

    /**
     * Get the cart item product type
     *
     * @return string|null
     */
    public function product_type(): ?string
    {
        return $this->_cartItem->product->type ?? null;
    }

    /**
     * Get the cart item vendor
     *
     * @return string|null
     */
    public function vendor(): ?string
    {
        return $this->_cartItem->product->vendor ?? null;
    }

    /**
     * Get the cart item properties
     *
     * @return array
     */
    public function properties(): array
    {
        return $this->_cartItem->properties ?? [];
    }

    /**
     * Get the cart item options
     *
     * @return array
     */
    public function options(): array
    {
        $options = [];

        if ($this->_cartItem->options) {
            foreach ($this->_cartItem->options as $option) {
                $options[] = [
                    'name' => $option->name,
                    'value' => $option->value
                ];
            }
        }

        return $options;
    }

    /**
     * @return null|Product
     */
    public function product()
    {
        if (!$this->_cartItem->relationLoaded('product') || !$this->_cartItem->product) {
            return null;
        }

        return new Product($this->_cartItem->product);
    }

    /**
     * @return null|Variant
     */
    public function variant()
    {
        if (!$this->_cartItem->relationLoaded('variant') || !$this->_cartItem->variant) {
            return null;
        }

        return new Variant($this->_cartItem->variant);
    }

    /**
     * @return null|float
     */
    public function total_price(): ?float
    {
        if ($this->_cartItem->price === null) {
            return null;
        }

        return (float) ($this->_cartItem->price * $this->_cartItem->quantity);
    }

    /**
     * @return null|float
     */
    public function discount(): ?float
    {
        return $this->_cartItem->discount !== null ? (float) $this->_cartItem->discount : null;
    }

    /**
     * @return null|float
     */
    public function total_discount(): ?float
    {
        if ($this->_cartItem->discount === null) {
            return null;
        }

        return (float) ($this->_cartItem->discount * $this->_cartItem->quantity);
    }

    /**
     * @return null|float
     */
    public function original_line_price(): ?float
    {
        return $this->total_price();
    }

    /**
     * @return null|float
     */
    public function original_price(): ?float
    {
        return $this->price();
    }

    /**
     * @return null|float
     */
    public function unit_price(): ?float
    {
        return $this->price();
    }

    /**
     * @return null|float
     */
    public function total_final_price(): ?float
    {
        $final_price = $this->final_price();

        if ($final_price === null) {
            return null;
        }

        return $final_price * $this->_cartItem->quantity;
    }

    /**
     * @return string
     */
    public function total_final_price_formatted(): string
    {
        $total_final = $this->total_final_price();

        if ($total_final === null) {
            return '';
        }

        return Format::money($total_final, true, site('currency'));
    }

    /**
     * @return string
     */
    public function price_formatted(): string
    {
        if ($this->_cartItem->price === null) {
            return '';
        }

        return Format::money((float) $this->_cartItem->price, true, site('currency'));
    }

    /**
     * @return string
     */
    public function total_price_formatted(): string
    {
        $total = $this->total_price();

        if ($total === null) {
            return '';
        }

        return Format::money($total, true, site('currency'));
    }

    /**
     * @return string
     */
    public function discount_formatted(): string
    {
        if ($this->_cartItem->discount === null) {
            return '';
        }

        return Format::money((float) $this->_cartItem->discount, true, site('currency'));
    }

    /**
     * @return string
     */
    public function total_discount_formatted(): string
    {
        $total_discount = $this->total_discount();

        if ($total_discount === null) {
            return '';
        }

        return Format::money($total_discount, true, site('currency'));
    }

    /**
     * @return string
     */
    public function final_price_formatted(): string
    {
        $final_price = $this->final_price();

        if ($final_price === null) {
            return '';
        }

        return Format::money($final_price, true, site('currency'));
    }

    /**
     * @return array
     */
    public function price_parts(): array
    {
        if ($this->_cartItem->price === null) {
            return [];
        }

        return PriceParts::format((float) $this->_cartItem->price);
    }

    /**
     * @return array
     */
    public function total_price_parts(): array
    {
        $total = $this->total_price();

        if ($total === null) {
            return [];
        }

        return PriceParts::format($total);
    }

    /**
     * @return array
     */
    public function final_price_parts(): array
    {
        $final_price = $this->final_price();

        if ($final_price === null) {
            return [];
        }

        return PriceParts::format($final_price);
    }

    /**
     * @return array
     */
    public function total_final_price_parts(): array
    {
        $total_final = $this->total_final_price();

        if ($total_final === null) {
            return [];
        }

        return PriceParts::format($total_final);
    }

    /**
     * @return string
     */
    public function line_price_with_currency(): string
    {
        return $this->total_price_formatted();
    }

    /**
     * @return string
     */
    public function key(): string
    {
        if ($this->_cartItem->variant_id) {
            return $this->_cartItem->product_id . ':' . $this->_cartItem->variant_id;
        }

        return (string) $this->_cartItem->product_id;
    }

    /**
     * @return string|null
     */
    public function sku(): ?string
    {
        $variant = $this->variant();
        if ($variant && $variant->sku()) {
            return $variant->sku();
        }

        $product = $this->product();
        return $product ? $product->sku() : null;
    }

    /**
     * @return int|null
     */
    public function grams(): ?int
    {
        $variant = $this->variant();
        if ($variant && method_exists($variant, 'grams')) {
            return $variant->grams();
        }

        $product = $this->product();
        if ($product && method_exists($product, 'grams')) {
            return $product->grams();
        }

        return null;
    }

    /**
     * @return bool
     */
    public function gift_card(): bool
    {
        $product = $this->product();
        return $product ? $product->gift_card() : false;
    }

    /**
     * @return array|null
     */
    public function selling_plan_allocation(): ?array
    {
        return null;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'product' => $this->product(),
            'variant' => $this->variant(),
            'title' => $this->title(),
            'quantity' => $this->quantity(),
            'price' => $this->price(),
            'total_price' => $this->total_price(),
            'discount' => $this->discount(),
            'total_discount' => $this->total_discount(),
            'final_price' => $this->final_price(),
            'total_final_price' => $this->total_final_price(),
            'price_formatted' => $this->price_formatted(),
            'total_price_formatted' => $this->total_price_formatted(),
            'discount_formatted' => $this->discount_formatted(),
            'total_discount_formatted' => $this->total_discount_formatted(),
            'final_price_formatted' => $this->final_price_formatted(),
            'total_final_price_formatted' => $this->total_final_price_formatted(),
            'price_parts' => $this->price_parts(),
            'total_price_parts' => $this->total_price_parts(),
            'final_price_parts' => $this->final_price_parts(),
            'total_final_price_parts' => $this->total_final_price_parts(),

            'line_price' => $this->line_price(),
            'original_line_price' => $this->original_line_price(),
            'final_line_price' => $this->final_line_price(),
            'unit_price' => $this->unit_price(),
            'original_price' => $this->original_price(),
            'line_price_with_currency' => $this->line_price_with_currency(),
            'url' => $this->url(),
            'key' => $this->key(),
            'properties' => $this->properties(),
            'sku' => $this->sku(),
            'grams' => $this->grams(),
            'vendor' => $this->vendor(),
            'product_type' => $this->product_type(),
            'gift_card' => $this->gift_card(),
            'selling_plan_allocation' => $this->selling_plan_allocation(),
        ];
    }
}
