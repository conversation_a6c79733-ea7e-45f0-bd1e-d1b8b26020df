<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.4.2019 г.
 * Time: 12:47 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use ArrayIterator;
use IteratorAggregate;
use Liquid\Drop;
use App\LiquidEngine\LiquidHelpers\Drop\Tag;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class Tags extends AbstractDrop implements IteratorAggregate
{

    protected array $_tags;

    protected $_last_call = null;

    /**
     * Product constructor.
     * @param array $tags
     */
    public function __construct(array $tags)
    {
        $this->_tags = $tags;
    }

    public function size(): int
    {
        $this->_last_call = null;
        return count($this->_tags);
    }

    /**
     * @return ArrayIterator|\Traversable
     */
    public function getIterator(): \Traversable
    {
        $this->_last_call = null;
        return new ArrayIterator($this->_tags);
    }

    /**
     * @param mixed $name
     * @param mixed $arguments
     * @return mixed
     */
    public function __call($name, $arguments)
    {
        if (is_numeric($name)) {
            $this->_last_call = null;
            return $this->_tags[$name] ?? null;
        } elseif (in_array($name, ['implode', 'pluck'])) {
            $this->_last_call = $name;
            return $this;
        }

        if ($this->_last_call && method_exists($this, $method = '_' . \Illuminate\Support\Str::camel('get_' . $this->_last_call))) {
            $this->_last_call = null;
            return $this->$method($name);
        }

        return $this;
    }

    public function toArray(): array
    {
        return $this->_tags;
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    protected function _getPluck($name): array
    {
        return array_filter(array_map(function (Tag $tag) use ($name) {
            if (method_exists($tag, $name)) {
                return $tag->$name();
            }

            return null;
        }, $this->_tags));
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    protected function _getImplode($name): string
    {
        return implode(', ', $this->_getPluck($name));
    }

    public function tags(): array
    {
        return $this->_tags;
    }

    public function all(): array
    {
        return $this->_tags;
    }

    public function first(): string
    {
        if (count($this->_tags) > 0) {
            return $this->_tags[0];
        }

        return '';
    }

    public function last(): string
    {
        if (count($this->_tags) > 0) {
            return $this->_tags[count($this->_tags) - 1];
        }

        return '';
    }

    /**
     * Shopify-compatible method: Get tag collection as Tag Drop objects
     * @return array
     */
    public function collection(): array
    {
        $tagObjects = [];
        foreach ($this->_tags as $tag) {
            $tagObjects[] = new Tag($tag);
        }
        return $tagObjects;
    }

    /**
     * Shopify-compatible method: Get handles for all tags
     * @return array
     */
    public function handles(): array
    {
        $handles = [];
        foreach ($this->_tags as $tag) {
            $handles[] = Str::slug($tag, '-');
        }
        return $handles;
    }

    /**
     * Shopify-compatible method: Get tag by handle
     * @param string $handle
     * @return string|null
     */
    public function by_handle(string $handle): ?string
    {
        foreach ($this->_tags as $tag) {
            if (Str::slug($tag, '-') === $handle) {
                return $tag;
            }
        }
        return null;
    }

    /**
     * Shopify-compatible method: Check if tags contain specific tag
     * @param string $tag
     * @return bool
     */
    public function contains(string $tag): bool
    {
        return in_array($tag, $this->_tags, true);
    }

    /**
     * Shopify-compatible method: Check if tags contain tag by handle
     * @param string $handle
     * @return bool
     */
    public function contains_handle(string $handle): bool
    {
        return $this->by_handle($handle) !== null;
    }

    /**
     * Shopify-compatible method: Filter tags by prefix
     * @param string $prefix
     * @return array
     */
    public function filter_by_prefix(string $prefix): array
    {
        $filtered = [];
        foreach ($this->_tags as $tag) {
            if (Str::startsWith(strtolower($tag), strtolower($prefix))) {
                $filtered[] = $tag;
            }
        }
        return $filtered;
    }

    /**
     * Shopify-compatible method: Filter tags by suffix
     * @param string $suffix
     * @return array
     */
    public function filter_by_suffix(string $suffix): array
    {
        $filtered = [];
        foreach ($this->_tags as $tag) {
            if (Str::endsWith(strtolower($tag), strtolower($suffix))) {
                $filtered[] = $tag;
            }
        }
        return $filtered;
    }

    /**
     * Shopify-compatible method: Search tags containing string
     * @param string $search
     * @return array
     */
    public function search(string $search): array
    {
        $results = [];
        foreach ($this->_tags as $tag) {
            if (Str::contains(strtolower($tag), strtolower($search))) {
                $results[] = $tag;
            }
        }
        return $results;
    }

    /**
     * Shopify-compatible method: Get sorted tags
     * @param string $direction
     * @return array
     */
    public function sorted(string $direction = 'asc'): array
    {
        $sorted = $this->_tags;
        if ($direction === 'desc') {
            rsort($sorted);
        } else {
            sort($sorted);
        }
        return $sorted;
    }

    /**
     * Shopify-compatible method: Get unique tags
     * @return array
     */
    public function unique(): array
    {
        return array_unique($this->_tags);
    }

    /**
     * Shopify-compatible method: Get tags as comma-separated string
     * @param string $separator
     * @return string
     */
    public function join(string $separator = ', '): string
    {
        return implode($separator, $this->_tags);
    }

    /**
     * Shopify-compatible method: Get random tag
     * @return string|null
     */
    public function random(): ?string
    {
        if (empty($this->_tags)) {
            return null;
        }
        return $this->_tags[array_rand($this->_tags)];
    }

    /**
     * Shopify-compatible method: Get first N tags
     * @param int $count
     * @return array
     */
    public function limit(int $count): array
    {
        return array_slice($this->_tags, 0, $count);
    }

    /**
     * Shopify-compatible method: Check if tags collection is empty
     * @return bool
     */
    public function empty(): bool
    {
        return empty($this->_tags);
    }

    /**
     * Shopify-compatible method: Check if tags collection has any items
     * @return bool
     */
    public function any(): bool
    {
        return !empty($this->_tags);
    }

    /**
     * Shopify-compatible method: Get tag at specific index
     * @param int $index
     * @return string|null
     */
    public function at(int $index): ?string
    {
        return $this->_tags[$index] ?? null;
    }

    /**
     * Shopify-compatible method: Convert to JSON
     * @return string
     */
    public function json(): string
    {
        return json_encode($this->_tags, JSON_THROW_ON_ERROR);
    }

}
