<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Config\Repository;

/**
 * Setting Drop class for handling application settings in Liquid templates.
 *
 * This class provides access to application settings through a type-safe interface,
 * supporting various data types and default values. It wraps the configuration repository
 * and exposes allowed settings as dynamic methods for use in Liquid templates.
 *
 * Usage:
 *   {{ settings.add_to_cart_action }}
 *   {{ settings.compact_cart_panel }}
 *
 * Supported types: string, bool, int (with default values).
 *
 * Properties:
 * - add_to_cart_action: string
 * - compact_cart_panel: bool|null
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class Setting extends AbstractDrop
{
    /**
     * The configuration repository instance.
     * Contains all application settings loaded from the system.
     *
     * @var Repository
     */
    protected Repository $settings;

    /**
     * Allowed settings configuration with their types and default values.
     * Maps setting method names to their config keys, default values, and types.
     *
     * @var array<string, array{
     *     key: string,
     *     default: mixed,
     *     type: string
     * }>
     */
    protected array $allowed = [
        'add_to_cart_action' => [
            'key' => 'action_after_add_to_cart',
            'default' => 'show_popup',
            'type' => 'string'
        ],
        'compact_cart_panel' => [
            'key' => 'compact_cart_panel',
            'default' => null,
            'type' => 'bool'
        ]
    ];

    /**
     * Create a new Setting instance.
     * Initializes the configuration repository with all current settings.
     */
    public function __construct()
    {
        $this->settings = new Repository();
        $this->settings->set(setting()->all());
    }

    /**
     * Handle dynamic method calls into the class.
     * Returns the value of the allowed setting, transformed to its configured type.
     *
     * @param string $name The method name (setting key)
     * @param array $arguments The method arguments (unused)
     * @return mixed The transformed setting value, or null if not allowed
     */
    public function __call(string $name, array $arguments)
    {
        if (array_key_exists($name, $this->allowed)) {
            return $this->_transform(
                $this->settings->get($this->allowed[$name]['key']),
                $this->allowed[$name]
            );
        }

        return null;
    }

    /**
     * Get the collection of allowed settings as a plain array.
     * Returns an array of all allowed settings and their current values.
     *
     * @return array{
     *     add_to_cart_action: string,
     *     compact_cart_panel: bool|null
     * } The allowed settings and their values
     */
    public function toArray(): array
    {
        return [
            'add_to_cart_action' => $this->__call('add_to_cart_action', []),
            'compact_cart_panel' => $this->__call('compact_cart_panel', []),
        ];
    }

    /**
     * Transform a setting value based on its type configuration.
     * Applies type casting and default value logic for the setting.
     *
     * @param mixed $value The value to transform
     * @param array{
     *     key: string,
     *     default: mixed,
     *     type: string
     * } $data The setting configuration
     * @return mixed The transformed value
     */
    protected function _transform($value, array $data)
    {
        if (is_null($value) && !is_null($data['default'] ?? null)) {
            $value = $data['default'];
        }

        return match ($data['type'] ?? null) {
            'string' => is_scalar($value) ? (string)$value : '',
            'bool' => (bool)$value,
            'int' => (int)$value,
            default => $value
        };
    }
}
