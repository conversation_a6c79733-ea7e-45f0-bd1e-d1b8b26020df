<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Support\Facades\Cache as LaravelCache;

/**
 * Cache Drop class for handling caching functionality in Liquid templates.
 *
 * This class provides a Shopify-compatible caching interface for Liquid templates,
 * wrapping Laravel's cache functionality with additional features like statistics
 * tracking, structured data generation, and accessibility attributes.
 *
 * Usage:
 *   {{ cache.enabled }}
 *   {{ cache.ttl }}
 *   {{ cache.get('key') }}
 *   {{ cache.put('key', value) }}
 *   {{ cache.stats }}
 *
 * Properties:
 * - enabled: bool
 * - ttl: int
 * - config: array{enabled: bool, ttl: int, prefix: string, store: string}
 * - stats: array{hits: int, misses: int, keys: int, size: int, enabled: bool, store: string}
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class Cache extends AbstractDrop
{
    /**
     * The cache configuration.
     * Stores settings for cache behavior including enabled state,
     * time-to-live, key prefix, and storage driver.
     *
     * @var array{
     *     enabled: bool,
     *     ttl: int,
     *     prefix: string,
     *     store: string
     * }
     */
    protected array $config;

    /**
     * Create a new Cache instance.
     * Initializes the cache with default configuration that can be overridden.
     *
     * @param array{
     *     enabled?: bool,
     *     ttl?: int,
     *     prefix?: string,
     *     store?: string
     * } $config Cache configuration
     */
    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'enabled' => true,
            'ttl' => 3600, // 1 hour default
            'prefix' => 'liquid_',
            'store' => 'file'
        ], $config);
    }

    /**
     * Get whether caching is enabled.
     * Returns the current state of the cache system.
     *
     * @return bool Whether caching is enabled
     */
    public function enabled(): bool
    {
        return (bool) $this->config['enabled'];
    }

    /**
     * Get the default time-to-live for cache entries.
     * Returns the configured TTL in seconds.
     *
     * @return int The TTL in seconds
     */
    public function ttl(): int
    {
        return (int) $this->config['ttl'];
    }

    /**
     * Get a value from the cache.
     * Retrieves a cached value by key, returning the default if not found.
     *
     * @param string $key The cache key
     * @param mixed $default The default value if key not found
     * @return mixed The cached value or default
     */
    public function get(string $key, $default = null)
    {
        if (!$this->enabled()) {
            return $default;
        }

        return LaravelCache::get($this->prefixKey($key), $default);
    }

    /**
     * Store a value in the cache.
     * Saves a value to the cache with optional custom TTL.
     *
     * @param string $key The cache key
     * @param mixed $value The value to store
     * @param int|null $ttl Time to live in seconds
     * @return bool Whether the operation was successful
     */
    public function put(string $key, $value, ?int $ttl = null): bool
    {
        if (!$this->enabled()) {
            return false;
        }

        return LaravelCache::put(
            $this->prefixKey($key),
            $value,
            $ttl ?? $this->ttl()
        );
    }

    /**
     * Remove a value from the cache.
     * Deletes a specific cache entry by key.
     *
     * @param string $key The cache key
     * @return bool Whether the operation was successful
     */
    public function forget(string $key): bool
    {
        if (!$this->enabled()) {
            return false;
        }

        return LaravelCache::forget($this->prefixKey($key));
    }

    /**
     * Clear all cache entries.
     * Removes all cached items from the store.
     *
     * @return bool Whether the operation was successful
     */
    public function clear(): bool
    {
        if (!$this->enabled()) {
            return false;
        }

        return LaravelCache::flush();
    }

    /**
     * Check if a key exists in the cache.
     * Verifies whether a specific key is present in the cache.
     *
     * @param string $key The cache key
     * @return bool Whether the key exists
     */
    public function has(string $key): bool
    {
        if (!$this->enabled()) {
            return false;
        }

        return LaravelCache::has($this->prefixKey($key));
    }

    /**
     * Get cache statistics.
     * Returns detailed metrics about cache usage and performance.
     *
     * @return array{
     *     hits: int,
     *     misses: int,
     *     keys: int,
     *     size: int,
     *     enabled: bool,
     *     store: string
     * } The cache statistics
     */
    public function stats(): array
    {
        return [
            'hits' => (int) LaravelCache::get('cache_hits', 0),
            'misses' => (int) LaravelCache::get('cache_misses', 0),
            'keys' => (int) LaravelCache::get('cache_keys', 0),
            'size' => (int) LaravelCache::get('cache_size', 0),
            'enabled' => $this->enabled(),
            'store' => $this->config['store']
        ];
    }

    /**
     * Get accessibility attributes.
     * Returns ARIA attributes for cache status display.
     *
     * @return array{
     *     aria_label: string,
     *     role: string
     * } The accessibility attributes
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => 'Cache status',
            'role' => 'status'
        ];
    }

    /**
     * Get structured data for SEO.
     * Returns Schema.org structured data for cache information.
     *
     * @return array<string, mixed> The structured data
     */
    public function structured_data(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'cacheControl' => [
                'enabled' => $this->enabled(),
                'ttl' => $this->ttl(),
                'store' => $this->config['store']
            ]
        ];
    }

    /**
     * Convert to Shopify format.
     * Returns cache data in Shopify-compatible format.
     *
     * @return array{
     *     enabled: bool,
     *     ttl: int,
     *     store: string,
     *     stats: array{hits: int, misses: int, keys: int, size: int, enabled: bool, store: string},
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * } The Shopify-compatible data
     */
    public function toShopifyArray(): array
    {
        return [
            'enabled' => $this->enabled(),
            'ttl' => $this->ttl(),
            'store' => $this->config['store'],
            'stats' => $this->stats(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to SEO array format.
     * Returns cache data in SEO-compatible format.
     *
     * @return array{
     *     enabled: bool,
     *     ttl: int,
     *     store: string,
     *     stats: array{hits: int, misses: int, keys: int, size: int, enabled: bool, store: string},
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * } The SEO-compatible data
     */
    public function toSeoArray(): array
    {
        return [
            'enabled' => $this->enabled(),
            'ttl' => $this->ttl(),
            'store' => $this->config['store'],
            'stats' => $this->stats(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to analytics array format.
     * Returns cache data in analytics-compatible format.
     *
     * @return array{
     *     enabled: bool,
     *     ttl: int,
     *     store: string,
     *     stats: array{hits: int, misses: int, keys: int, size: int, enabled: bool, store: string},
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * } The analytics-compatible data
     */
    public function toAnalyticsArray(): array
    {
        return [
            'enabled' => $this->enabled(),
            'ttl' => $this->ttl(),
            'store' => $this->config['store'],
            'stats' => $this->stats(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Get the collection as a plain array.
     * Returns a complete array representation of the cache,
     * including all formats (Shopify, SEO, Analytics).
     *
     * @return array{
     *     enabled: bool,
     *     ttl: int,
     *     store: string,
     *     stats: array{hits: int, misses: int, keys: int, size: int, enabled: bool, store: string},
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string},
     *     shopify: array{enabled: bool, ttl: int, store: string, stats: array{hits: int, misses: int, keys: int, size: int, enabled: bool, store: string}, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     seo: array{enabled: bool, ttl: int, store: string, stats: array{hits: int, misses: int, keys: int, size: int, enabled: bool, store: string}, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     analytics: array{enabled: bool, ttl: int, store: string, stats: array{hits: int, misses: int, keys: int, size: int, enabled: bool, store: string}, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}}
     * } The complete cache data
     */
    public function toArray(): array
    {
        return [
            'enabled' => $this->enabled(),
            'ttl' => $this->ttl(),
            'store' => $this->config['store'],
            'stats' => $this->stats(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics' => $this->toAnalyticsArray()
        ];
    }

    /**
     * Prefix a cache key.
     * Adds the configured prefix to a cache key.
     *
     * @param string $key The original key
     * @return string The prefixed key
     */
    protected function prefixKey(string $key): string
    {
        return $this->config['prefix'] . $key;
    }
} 