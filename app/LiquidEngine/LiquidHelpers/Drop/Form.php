<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Support\Facades\Session;

/**
 * Class Form
 *
 * Provides form handling functionality for Liquid templates.
 * Implements Shopify-compatible form methods.
 *
 * @property-read string $id Form identifier
 * @property-read string $action Form action URL
 * @property-read string $method Form submission method
 * @property-read array $fields Form fields
 * @property-read array $errors Form validation errors
 * @property-read array $values Form field values
 * @property-read array $attributes Form HTML attributes
 */
class Form extends AbstractDrop
{
    /**
     * Form identifier.
     *
     * @var string
     */
    protected string $id;

    /**
     * Form action URL.
     *
     * @var string
     */
    protected string $action;

    /**
     * Form submission method.
     *
     * @var string
     */
    protected string $method;

    /**
     * Form fields.
     *
     * @var array<int, array{
     *     name: string,
     *     type: string,
     *     label: string,
     *     value: mixed,
     *     required: bool,
     *     placeholder: string,
     *     options: array<int, array{value: string, label: string}>,
     *     attributes: array<string, string>
     * }>
     */
    protected array $fields;

    /**
     * Form validation errors.
     *
     * @var array<string, array<int, string>>
     */
    protected array $errors;

    /**
     * Form field values.
     *
     * @var array<string, mixed>
     */
    protected array $values;

    /**
     * Form HTML attributes.
     *
     * @var array<string, string>
     */
    protected array $attributes;

    /**
     * Create a new Form instance.
     *
     * @param string $id Form identifier
     * @param string $action Form action URL
     * @param string $method Form submission method
     * @param array<int, array{name: string, type: string, label: string, value: mixed, required: bool, placeholder: string, options: array<int, array{value: string, label: string}>, attributes: array<string, string>}> $fields Form fields
     * @param array<string, array<int, string>> $errors Form validation errors
     * @param array<string, mixed> $values Form field values
     * @param array<string, string> $attributes Form HTML attributes
     */
    public function __construct(
        string $id,
        string $action,
        string $method = 'POST',
        array $fields = [],
        array $errors = [],
        array $values = [],
        array $attributes = []
    ) {
        $this->id = $id;
        $this->action = $action;
        $this->method = strtoupper($method);
        $this->fields = $fields;
        $this->errors = $errors;
        $this->values = $values;
        $this->attributes = array_merge([
            'id' => $id,
            'action' => $action,
            'method' => $method,
            'class' => 'form',
            'novalidate' => 'novalidate'
        ], $attributes);
    }

    /**
     * Get form identifier.
     *
     * @return string
     */
    public function id(): string
    {
        return $this->id;
    }

    /**
     * Get form action URL.
     *
     * @return string
     */
    public function action(): string
    {
        return $this->action;
    }

    /**
     * Get form submission method.
     *
     * @return string
     */
    public function method(): string
    {
        return $this->method;
    }

    /**
     * Get form fields.
     *
     * @return array<int, array{
     *     name: string,
     *     type: string,
     *     label: string,
     *     value: mixed,
     *     required: bool,
     *     placeholder: string,
     *     options: array<int, array{value: string, label: string}>,
     *     attributes: array<string, string>
     * }>
     */
    public function fields(): array
    {
        return $this->fields;
    }

    /**
     * Get form validation errors.
     *
     * @return array<string, array<int, string>>
     */
    public function errors(): array
    {
        return $this->errors;
    }

    /**
     * Get form field values.
     *
     * @return array<string, mixed>
     */
    public function values(): array
    {
        return $this->values;
    }

    /**
     * Get form HTML attributes.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return $this->attributes;
    }

    /**
     * Get CSRF token.
     *
     * @return string
     */
    public function csrf_token(): string
    {
        return Session::token();
    }

    /**
     * Get form field value.
     *
     * @param string $name Field name
     * @return mixed
     */
    public function value(string $name)
    {
        return $this->values[$name] ?? null;
    }

    /**
     * Get form field errors.
     *
     * @param string $name Field name
     * @return array<int, string>
     */
    public function field_errors(string $name): array
    {
        return $this->errors[$name] ?? [];
    }

    /**
     * Check if form has errors.
     *
     * @return bool
     */
    public function has_errors(): bool
    {
        return !empty($this->errors);
    }

    /**
     * Get form field HTML attributes.
     *
     * @param string $name Field name
     * @return array<string, string>
     */
    public function field_attributes(string $name): array
    {
        $field = collect($this->fields)->firstWhere('name', $name);

        if (!$field) {
            return [];
        }

        $attributes = $field['attributes'] ?? [];
        $attributes['name'] = $name;
        $attributes['id'] = $this->id . '_' . $name;
        $attributes['class'] = trim(($attributes['class'] ?? '') . ' form-control');

        if ($field['required'] ?? false) {
            $attributes['required'] = 'required';
        }

        if (isset($field['placeholder'])) {
            $attributes['placeholder'] = $field['placeholder'];
        }

        if (isset($this->values[$name])) {
            $attributes['value'] = $this->values[$name];
        }

        if (isset($this->errors[$name])) {
            $attributes['class'] .= ' is-invalid';
        }

        return $attributes;
    }

    /**
     * Get accessibility attributes.
     *
     * @return array{
     *     aria_label: string,
     *     role: string
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => 'Form',
            'role' => 'form'
        ];
    }

    /**
     * Get structured data for SEO.
     *
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'form' => [
                'id' => $this->id,
                'action' => $this->action,
                'method' => $this->method,
                'fields' => array_map(function ($field) {
                    return [
                        'name' => $field['name'],
                        'type' => $field['type'],
                        'label' => $field['label'],
                        'required' => $field['required'] ?? false
                    ];
                }, $this->fields)
            ]
        ];
    }

    /**
     * Convert to Shopify format.
     *
     * @return array{
     *     id: string,
     *     action: string,
     *     method: string,
     *     fields: array<int, array{name: string, type: string, label: string, value: mixed, required: bool, placeholder: string, options: array<int, array{value: string, label: string}>, attributes: array<string, string>}>,
     *     errors: array<string, array<int, string>>,
     *     values: array<string, mixed>,
     *     attributes: array<string, string>,
     *     csrf_token: string,
     *     has_errors: bool,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toShopifyArray(): array
    {
        return [
            'id' => $this->id(),
            'action' => $this->action(),
            'method' => $this->method(),
            'fields' => $this->fields(),
            'errors' => $this->errors(),
            'values' => $this->values(),
            'attributes' => $this->attributes(),
            'csrf_token' => $this->csrf_token(),
            'has_errors' => $this->has_errors(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to SEO array format.
     *
     * @return array{
     *     id: string,
     *     action: string,
     *     method: string,
     *     fields: array<int, array{name: string, type: string, label: string, value: mixed, required: bool, placeholder: string, options: array<int, array{value: string, label: string}>, attributes: array<string, string>}>,
     *     errors: array<string, array<int, string>>,
     *     values: array<string, mixed>,
     *     attributes: array<string, string>,
     *     csrf_token: string,
     *     has_errors: bool,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toSeoArray(): array
    {
        return [
            'id' => $this->id(),
            'action' => $this->action(),
            'method' => $this->method(),
            'fields' => $this->fields(),
            'errors' => $this->errors(),
            'values' => $this->values(),
            'attributes' => $this->attributes(),
            'csrf_token' => $this->csrf_token(),
            'has_errors' => $this->has_errors(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to analytics array format.
     *
     * @return array{
     *     id: string,
     *     action: string,
     *     method: string,
     *     fields: array<int, array{name: string, type: string, label: string, value: mixed, required: bool, placeholder: string, options: array<int, array{value: string, label: string}>, attributes: array<string, string>}>,
     *     errors: array<string, array<int, string>>,
     *     values: array<string, mixed>,
     *     attributes: array<string, string>,
     *     csrf_token: string,
     *     has_errors: bool,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toAnalyticsArray(): array
    {
        return [
            'id' => $this->id(),
            'action' => $this->action(),
            'method' => $this->method(),
            'fields' => $this->fields(),
            'errors' => $this->errors(),
            'values' => $this->values(),
            'attributes' => $this->attributes(),
            'csrf_token' => $this->csrf_token(),
            'has_errors' => $this->has_errors(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Get the collection as a plain array.
     *
     * @return array{
     *     id: string,
     *     action: string,
     *     method: string,
     *     fields: array<int, array{name: string, type: string, label: string, value: mixed, required: bool, placeholder: string, options: array<int, array{value: string, label: string}>, attributes: array<string, string>}>,
     *     errors: array<string, array<int, string>>,
     *     values: array<string, mixed>,
     *     attributes: array<string, string>,
     *     csrf_token: string,
     *     has_errors: bool,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string},
     *     shopify: array{id: string, action: string, method: string, fields: array<int, array{name: string, type: string, label: string, value: mixed, required: bool, placeholder: string, options: array<int, array{value: string, label: string}>, attributes: array<string, string>}>, errors: array<string, array<int, string>>, values: array<string, mixed>, attributes: array<string, string>, csrf_token: string, has_errors: bool, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     seo: array{id: string, action: string, method: string, fields: array<int, array{name: string, type: string, label: string, value: mixed, required: bool, placeholder: string, options: array<int, array{value: string, label: string}>, attributes: array<string, string>}>, errors: array<string, array<int, string>>, values: array<string, mixed>, attributes: array<string, string>, csrf_token: string, has_errors: bool, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     analytics: array{id: string, action: string, method: string, fields: array<int, array{name: string, type: string, label: string, value: mixed, required: bool, placeholder: string, options: array<int, array{value: string, label: string}>, attributes: array<string, string>}>, errors: array<string, array<int, string>>, values: array<string, mixed>, attributes: array<string, string>, csrf_token: string, has_errors: bool, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}>
     * }
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'action' => $this->action(),
            'method' => $this->method(),
            'fields' => $this->fields(),
            'errors' => $this->errors(),
            'values' => $this->values(),
            'attributes' => $this->attributes(),
            'csrf_token' => $this->csrf_token(),
            'has_errors' => $this->has_errors(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics' => $this->toAnalyticsArray()
        ];
    }
}
