<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Pagination Drop Class
 * 
 * This class provides a structured way to handle pagination in Liquid templates.
 * It includes support for Shopify compatibility, accessibility, SEO, and analytics.
 * 
 * @property int $current_page The current page number
 * @property int $total_pages The total number of pages
 * @property int $total_items The total number of items
 * @property int $per_page The number of items per page
 * @property array $pages Array of page numbers
 * @property bool $has_previous Whether there is a previous page
 * @property bool $has_next Whether there is a next page
 * @property string $previous_url The URL for the previous page
 * @property string $next_url The URL for the next page
 * @property array $parts Array of pagination parts (previous, pages, next)
 */
class Pagination extends AbstractDrop
{
    /**
     * @var int The current page number
     */
    protected int $current_page;

    /**
     * @var int The total number of pages
     */
    protected int $total_pages;

    /**
     * @var int The total number of items
     */
    protected int $total_items;

    /**
     * @var int The number of items per page
     */
    protected int $per_page;

    /**
     * @var array<int> Array of page numbers
     */
    protected array $pages;

    /**
     * @var bool Whether there is a previous page
     */
    protected bool $has_previous;

    /**
     * @var bool Whether there is a next page
     */
    protected bool $has_next;

    /**
     * @var string The URL for the previous page
     */
    protected string $previous_url;

    /**
     * @var string The URL for the next page
     */
    protected string $next_url;

    /**
     * @var array<string, mixed> Array of pagination parts
     */
    protected array $parts;

    /**
     * Create a new Pagination instance.
     *
     * @param LengthAwarePaginator $paginator The Laravel paginator instance
     * @param string $base_url The base URL for pagination links
     * @param string $page_param The query parameter name for the page number
     */
    public function __construct(
        LengthAwarePaginator $paginator,
        string $base_url = '',
        string $page_param = 'page'
    ) {
        $this->current_page = $paginator->currentPage();
        $this->total_pages = $paginator->lastPage();
        $this->total_items = $paginator->total();
        $this->per_page = $paginator->perPage();
        $this->pages = range(1, $this->total_pages);
        $this->has_previous = $paginator->hasPages() && $paginator->currentPage() > 1;
        $this->has_next = $paginator->hasPages() && $paginator->currentPage() < $this->total_pages;

        // Build URLs
        $this->previous_url = $this->buildPageUrl($base_url, $this->current_page - 1, $page_param);
        $this->next_url = $this->buildPageUrl($base_url, $this->current_page + 1, $page_param);

        // Build parts
        $this->parts = [
            'previous' => [
                'title' => 'Previous',
                'url' => $this->previous_url,
                'is_current' => false,
                'is_disabled' => !$this->has_previous,
            ],
            'pages' => array_map(
                fn($page) => [
                    'title' => (string) $page,
                    'url' => $this->buildPageUrl($base_url, $page, $page_param),
                    'is_current' => $page === $this->current_page,
                    'is_disabled' => false,
                ],
                $this->pages
            ),
            'next' => [
                'title' => 'Next',
                'url' => $this->next_url,
                'is_current' => false,
                'is_disabled' => !$this->has_next,
            ],
        ];
    }

    /**
     * Build a page URL.
     *
     * @param string $base_url The base URL
     * @param int $page The page number
     * @param string $page_param The query parameter name
     * @return string
     */
    protected function buildPageUrl(string $base_url, int $page, string $page_param): string
    {
        $url = parse_url($base_url);
        $query = [];
        
        if (isset($url['query'])) {
            parse_str($url['query'], $query);
        }
        
        $query[$page_param] = $page;
        
        $url['query'] = http_build_query($query);
        
        return $this->buildUrl($url);
    }

    /**
     * Build a URL from components.
     *
     * @param array<string, mixed> $parts URL components
     * @return string
     */
    protected function buildUrl(array $parts): string
    {
        $scheme = isset($parts['scheme']) ? $parts['scheme'] . '://' : '';
        $host = $parts['host'] ?? '';
        $port = isset($parts['port']) ? ':' . $parts['port'] : '';
        $path = $parts['path'] ?? '';
        $query = isset($parts['query']) ? '?' . $parts['query'] : '';
        $fragment = isset($parts['fragment']) ? '#' . $parts['fragment'] : '';

        return $scheme . $host . $port . $path . $query . $fragment;
    }

    /**
     * Get the current page number.
     *
     * @return int
     */
    public function current_page(): int
    {
        return $this->current_page;
    }

    /**
     * Get the total number of pages.
     *
     * @return int
     */
    public function total_pages(): int
    {
        return $this->total_pages;
    }

    /**
     * Get the total number of items.
     *
     * @return int
     */
    public function total_items(): int
    {
        return $this->total_items;
    }

    /**
     * Get the number of items per page.
     *
     * @return int
     */
    public function per_page(): int
    {
        return $this->per_page;
    }

    /**
     * Get the array of page numbers.
     *
     * @return array<int>
     */
    public function pages(): array
    {
        return $this->pages;
    }

    /**
     * Check if there is a previous page.
     *
     * @return bool
     */
    public function has_previous(): bool
    {
        return $this->has_previous;
    }

    /**
     * Check if there is a next page.
     *
     * @return bool
     */
    public function has_next(): bool
    {
        return $this->has_next;
    }

    /**
     * Get the URL for the previous page.
     *
     * @return string
     */
    public function previous_url(): string
    {
        return $this->previous_url;
    }

    /**
     * Get the URL for the next page.
     *
     * @return string
     */
    public function next_url(): string
    {
        return $this->next_url;
    }

    /**
     * Get the pagination parts.
     *
     * @return array<string, mixed>
     */
    public function parts(): array
    {
        return $this->parts;
    }

    /**
     * Get accessibility attributes for the pagination.
     *
     * @return array<string, string>
     */
    public function accessibility(): array
    {
        return [
            'role' => 'navigation',
            'aria-label' => 'Pagination',
            'aria-current' => 'page',
        ];
    }

    /**
     * Get structured data for SEO.
     *
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'CollectionPage',
            'pageNumber' => $this->current_page,
            'numberOfPages' => $this->total_pages,
            'numberOfItems' => $this->total_items,
            'itemsPerPage' => $this->per_page,
        ];
    }

    /**
     * Convert the pagination to a Shopify-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toShopifyArray(): array
    {
        return [
            'current_page' => $this->current_page,
            'total_pages' => $this->total_pages,
            'total_items' => $this->total_items,
            'per_page' => $this->per_page,
            'has_previous' => $this->has_previous,
            'has_next' => $this->has_next,
            'previous_url' => $this->previous_url,
            'next_url' => $this->next_url,
            'parts' => $this->parts,
        ];
    }

    /**
     * Convert the pagination to an SEO-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toSeoArray(): array
    {
        return [
            'current_page' => $this->current_page,
            'total_pages' => $this->total_pages,
            'total_items' => $this->total_items,
            'per_page' => $this->per_page,
            'previous_url' => $this->previous_url,
            'next_url' => $this->next_url,
            'structured_data' => $this->structured_data(),
        ];
    }

    /**
     * Convert the pagination to an analytics-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toAnalyticsArray(): array
    {
        return [
            'current_page' => $this->current_page,
            'total_pages' => $this->total_pages,
            'total_items' => $this->total_items,
            'per_page' => $this->per_page,
            'has_previous' => $this->has_previous,
            'has_next' => $this->has_next,
            'page_numbers' => count($this->pages),
        ];
    }

    /**
     * Convert the pagination to an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'current_page' => $this->current_page,
            'total_pages' => $this->total_pages,
            'total_items' => $this->total_items,
            'per_page' => $this->per_page,
            'pages' => $this->pages,
            'has_previous' => $this->has_previous,
            'has_next' => $this->has_next,
            'previous_url' => $this->previous_url,
            'next_url' => $this->next_url,
            'parts' => $this->parts,
            'accessibility' => $this->accessibility(),
            'structured_data' => $this->structured_data(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics' => $this->toAnalyticsArray(),
        ];
    }
} 