<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Store;

use App\Helper\Store\CartTotal;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Store\CartItem;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Customer;
use App\LiquidEngine\LiquidHelpers\Drop\Store\Cart\DiscountCode;
use App\LiquidEngine\LiquidHelpers\Drop\Store\Cart\FreeShipping;
use App\LiquidEngine\LiquidHelpers\Drop\Store\Cart\Product;
use App\LiquidEngine\LiquidHelpers\Drop\Store\Cart\Total;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use App\Models\Store\Cart as CartModel;
use Throwable;

/**
 * Cart Drop class for handling shopping cart data in Liquid templates
 * 
 * @property-read int $id The cart ID
 * @property-read bool $exists Whether the cart exists
 * @property-read string $key The cart key
 * @property-read DiscountCode|null $discount_code The applied discount code
 * @property-read Product[] $products The cart products
 * @property-read int $subtotal The cart subtotal
 * @property-read float $subtotal_input The raw cart subtotal
 * @property-read string $subtotal_formatted The formatted cart subtotal
 * @property-read array $subtotal_parts The cart subtotal parts
 * @property-read int $quantity The total quantity of items
 * @property-read int $products_count The number of unique products
 * @property-read Customer|null $customer The cart customer
 * @property-read bool $has_customer Whether the cart has a customer
 * @property-read bool $is_logged Whether the customer is logged in
 * @property-read bool $is_guest Whether the customer is a guest
 * @property-read int $weight The cart weight
 * @property-read float $weight_input The raw cart weight
 * @property-read string $weight_formatted The formatted cart weight
 * @property-read string $step The current cart step
 * @property-read Total[] $totals The cart totals
 * @property-read Total $total The cart total
 * @property-read PaymentProvider[] $payments Available payment providers
 * @property-read PaymentProvider[] $payments_allowed Allowed payment providers
 * @property-read FreeShipping|null $free_shipping Free shipping information
 * @property-read string $url The cart URL
 */
class Cart extends AbstractDrop
{
    /**
     * @var CartModel
     */
    protected CartModel $_cart;

    /**
     * @var array<Total>|null
     */
    protected ?array $_totals = null;

    /**
     * Cart constructor.
     * 
     * @param CartModel $cart
     */
    public function __construct(CartModel $cart)
    {
        $this->_cart = $cart;
    }

    /**
     * Get the cart ID
     * 
     * @return int
     */
    public function id(): int
    {
        return $this->_cart->id;
    }

    /**
     * Check if the cart exists
     * 
     * @return bool
     */
    public function exists(): bool
    {
        return $this->_cart->exists;
    }

    /**
     * Get the cart key
     * 
     * @return string
     */
    public function key(): string
    {
        return $this->_cart->key;
    }

    /**
     * Get the applied discount code
     * 
     * @return DiscountCode|null
     * @throws Throwable
     */
    public function discount_code(): ?DiscountCode
    {
        if ($discount = $this->_cart->getDiscountCodeNew()) {
            return new DiscountCode($discount);
        }

        return null;
    }

    /**
     * Get the cart products
     * 
     * @return array<Product>
     */
    public function products(): array
    {
        return $this->_cart->products->map(function (CartItem $item): Product {
            return new Product($item);
        })->all();
    }

    /**
     * Get the cart subtotal
     * 
     * @return int
     */
    public function subtotal(): int
    {
        return $this->_cart->subtotal;
    }

    /**
     * Get the raw cart subtotal
     * 
     * @return float
     */
    public function subtotal_input(): float
    {
        return $this->_cart->subtotal_input;
    }

    /**
     * Get the formatted cart subtotal
     * 
     * @return string
     */
    public function subtotal_formatted(): string
    {
        return $this->_cart->subtotal_formatted;
    }

    /**
     * Get the cart subtotal parts
     * 
     * @return array
     */
    public function subtotal_parts(): array
    {
        return PriceParts::format($this->subtotal_input());
    }

    /**
     * Get the total quantity of items
     * 
     * @return int
     */
    public function quantity(): int
    {
        return $this->_cart->quantity;
    }

    /**
     * Get the number of unique products
     * 
     * @return int
     */
    public function products_count(): int
    {
        return $this->_cart->products_count;
    }

    /**
     * Get the cart customer
     * 
     * @return Customer|null
     */
    public function customer(): ?Customer
    {
        return $this->_cart && $this->_cart->exists && $this->_cart->customer ? new Customer($this->_cart->customer) : null;
    }

    /**
     * Check if the cart has a customer
     * 
     * @return bool
     */
    public function has_customer(): bool
    {
        return $this->_cart->has_customer;
    }

    /**
     * Check if the customer is logged in
     * 
     * @return bool
     */
    public function is_logged(): bool
    {
        return $this->_cart->is_logged;
    }

    /**
     * Check if the customer is a guest
     * 
     * @return bool
     */
    public function is_guest(): bool
    {
        return $this->_cart->is_guest;
    }

    /**
     * Get the cart weight
     * 
     * @return int
     */
    public function weight(): int
    {
        return $this->_cart->weight;
    }

    /**
     * Get the raw cart weight
     * 
     * @return float
     */
    public function weight_input(): float
    {
        return $this->_cart->weight_input;
    }

    /**
     * Get the formatted cart weight
     * 
     * @return string
     */
    public function weight_formatted(): string
    {
        return $this->_cart->weight_formatted;
    }

    /**
     * Get the current cart step
     * 
     * @return string
     * @throws Throwable
     */
    public function step(): string
    {
        return $this->_cart->getStep();
    }

    /**
     * Get the cart totals
     * 
     * @return array<Total>
     * @throws Throwable
     */
    public function totals(): array
    {
        if (!is_null($this->_totals)) {
            return $this->_totals;
        }

        return $this->_totals = $this->_cart && $this->_cart->exists ? $this->_cart->getTotalsSimple()->map(function (CartTotal $total): Total {
            return new Total($total);
        })->all() : [];
    }

    /**
     * Get the cart total
     * 
     * @return Total|null
     * @throws Throwable
     */
    public function total(): ?Total
    {
        return $this->totals()['total.total'] ?? null;
    }

    /**
     * Get available payment providers
     * 
     * @return array<PaymentProvider>
     */
    public function payments(): array
    {
        return PaymentProviderConfiguration::getConfigurations()->map(function (PaymentProviderConfiguration $config): PaymentProvider {
            return new PaymentProvider($config);
        })->all();
    }

    /**
     * Get allowed payment providers
     * 
     * @return array<PaymentProvider>
     * @throws Throwable
     */
    public function payments_allowed(): array
    {
        $providers = [];
        foreach ($this->payments() as $provider) {
            if (!$this->total() || !is_null($provider->minimum_amount()) && $provider->minimum_amount() > $this->total()->amount()) {
                continue;
            }

            $providers[] = $provider;
        }

        return $providers;
    }

    /**
     * Get free shipping information
     * 
     * @return FreeShipping|null
     * @throws Throwable
     */
    public function free_shipping(): ?FreeShipping
    {
        if ($freeShipping = $this->_cart->getFreeShipping()) {
            return new FreeShipping($freeShipping);
        }

        return null;
    }

    /**
     * Get the cart URL
     * 
     * @return string
     */
    public function url(): string
    {
        return $this->_cart->url;
    }

    /**
     * Get the cart as a plain array
     * 
     * @return array{
     *     id: int,
     *     exists: bool,
     *     key: string,
     *     discount_code: DiscountCode|null,
     *     products: array<Product>,
     *     subtotal: int,
     *     subtotal_input: float,
     *     subtotal_formatted: string,
     *     subtotal_parts: array,
     *     quantity: int,
     *     products_count: int,
     *     customer: Customer|null,
     *     has_customer: bool,
     *     is_logged: bool,
     *     is_guest: bool,
     *     weight: int,
     *     weight_input: float,
     *     weight_formatted: string,
     *     step: string,
     *     totals: array<Total>,
     *     total: Total|null,
     *     payments: array<PaymentProvider>,
     *     payments_allowed: array<PaymentProvider>,
     *     free_shipping: FreeShipping|null,
     *     url: string
     * }
     * @throws Throwable
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'exists' => $this->exists(),
            'key' => $this->key(),
            'discount_code' => $this->discount_code(),
            'products' => $this->products(),
            'subtotal' => $this->subtotal(),
            'subtotal_input' => $this->subtotal_input(),
            'subtotal_formatted' => $this->subtotal_formatted(),
            'subtotal_parts' => $this->subtotal_parts(),
            'quantity' => $this->quantity(),
            'products_count' => $this->products_count(),
            'customer' => $this->customer(),
            'has_customer' => $this->has_customer(),
            'is_logged' => $this->is_logged(),
            'is_guest' => $this->is_guest(),
            'weight' => $this->weight(),
            'weight_input' => $this->weight_input(),
            'weight_formatted' => $this->weight_formatted(),
            'step' => $this->step(),
            'totals' => $this->totals(),
            'total' => $this->total(),
            'payments' => $this->payments(),
            'payments_allowed' => $this->payments_allowed(),
            'free_shipping' => $this->free_shipping(),
            'url' => $this->url(),
        ];
    }
}
