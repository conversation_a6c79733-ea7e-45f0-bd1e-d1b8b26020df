<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Store;

use App\Helper\Format;
use App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Currency;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;

/**
 * PaymentProvider Drop class for handling payment provider data in Liquid templates
 * 
 * @property-read string $key The payment provider key
 * @property-read string $name The payment provider name
 * @property-read string $description The payment provider description
 * @property-read string $type The payment provider type
 * @property-read int|null $minimum_amount The minimum amount required
 * @property-read float|null $minimum_amount_input The raw minimum amount
 * @property-read string|null $minimum_amount_formatted The formatted minimum amount
 * @property-read array|null $minimum_amount_parts The minimum amount parts
 * @property-read Currency $currency The currency object
 * @property-read LiquidImageAttribute $image The payment provider image
 * @property-read bool $is_active Whether the payment provider is active
 * @property-read bool $is_credit_card Whether this is a credit card provider
 * @property-read bool $is_paypal Whether this is a PayPal provider
 * @property-read bool $is_bank_transfer Whether this is a bank transfer provider
 * @property-read bool $is_cash_on_delivery Whether this is a cash on delivery provider
 */
class PaymentProvider extends AbstractDrop
{
    /**
     * @var PaymentProviderConfiguration
     */
    protected PaymentProviderConfiguration $provider;

    /**
     * PaymentProvider constructor.
     * 
     * @param PaymentProviderConfiguration $provider
     */
    public function __construct(PaymentProviderConfiguration $provider)
    {
        $this->provider = $provider;
    }

    /**
     * Get the payment provider key
     * 
     * @return string
     */
    public function key(): string
    {
        return $this->provider->provider;
    }

    /**
     * Get the payment provider name
     * 
     * @return string
     */
    public function name(): string
    {
        return $this->provider->storefront_name;
    }

    /**
     * Get the payment provider description
     * 
     * @return string
     */
    public function description(): string
    {
        return $this->provider->configuration['description'] ?? '';
    }

    /**
     * Get the payment provider type
     * 
     * @return string
     */
    public function type(): string
    {
        return $this->provider->payment_provider->type;
    }

    /**
     * Get the minimum amount required
     * 
     * @return int|null
     */
    public function minimum_amount(): ?int
    {
        return is_numeric($this->provider->min_price) ? Format::toIntegerPrice($this->provider->min_price) : null;
    }

    /**
     * Get the raw minimum amount
     * 
     * @return float|null
     */
    public function minimum_amount_input(): ?float
    {
        if ($min = $this->minimum_amount()) {
            return Format::moneyInput($min);
        }

        return null;
    }

    /**
     * Get the formatted minimum amount
     * 
     * @return string|null
     */
    public function minimum_amount_formatted(): ?string
    {
        if ($min = $this->minimum_amount()) {
            return Format::money($min);
        }

        return null;
    }

    /**
     * Get the minimum amount parts
     * 
     * @return array|null
     */
    public function minimum_amount_parts(): ?array
    {
        if ($min = $this->minimum_amount_input()) {
            return PriceParts::format($min, $this->currency()->code());
        }

        return null;
    }

    /**
     * Get the currency object
     * 
     * @return Currency
     */
    public function currency(): Currency
    {
        return new Currency();
    }

    /**
     * Get the payment provider image
     * 
     * @return LiquidImageAttribute
     */
    public function image(): LiquidImageAttribute
    {
        if ($this->provider->hasImage()) {
            $formatter = new UrlImageFormat($this->provider);
            return $formatter->getLiquidImage();
        }

        $default = config('url.img') . 'sitecp/img/payment_providers/' . $this->provider->provider . '.png?' . app('last_build');

        $formatter = new UrlImageFormat($default);
        return $formatter->getLiquidImage();
    }

    /**
     * Check if the payment provider is active
     * 
     * @return bool
     */
    public function is_active(): bool
    {
        return (bool) $this->provider->active;
    }

    /**
     * Check if this is a credit card provider
     * 
     * @return bool
     */
    public function is_credit_card(): bool
    {
        return $this->type() === 'credit_card';
    }

    /**
     * Check if this is a PayPal provider
     * 
     * @return bool
     */
    public function is_paypal(): bool
    {
        return $this->type() === 'paypal';
    }

    /**
     * Check if this is a bank transfer provider
     * 
     * @return bool
     */
    public function is_bank_transfer(): bool
    {
        return $this->type() === 'bank_transfer';
    }

    /**
     * Check if this is a cash on delivery provider
     * 
     * @return bool
     */
    public function is_cash_on_delivery(): bool
    {
        return $this->type() === 'cash_on_delivery';
    }

    /**
     * Get the payment provider data as a plain array
     * 
     * @return array{
     *     key: string,
     *     name: string,
     *     description: string,
     *     type: string,
     *     minimum_amount: int|null,
     *     minimum_amount_input: float|null,
     *     minimum_amount_formatted: string|null,
     *     minimum_amount_parts: array|null,
     *     currency: Currency,
     *     image: LiquidImageAttribute,
     *     is_active: bool,
     *     is_credit_card: bool,
     *     is_paypal: bool,
     *     is_bank_transfer: bool,
     *     is_cash_on_delivery: bool
     * }
     */
    public function toArray(): array
    {
        return [
            'key' => $this->key(),
            'name' => $this->name(),
            'description' => $this->description(),
            'type' => $this->type(),
            'minimum_amount' => $this->minimum_amount(),
            'minimum_amount_input' => $this->minimum_amount_input(),
            'minimum_amount_formatted' => $this->minimum_amount_formatted(),
            'minimum_amount_parts' => $this->minimum_amount_parts(),
            'currency' => $this->currency(),
            'image' => $this->image(),
            'is_active' => $this->is_active(),
            'is_credit_card' => $this->is_credit_card(),
            'is_paypal' => $this->is_paypal(),
            'is_bank_transfer' => $this->is_bank_transfer(),
            'is_cash_on_delivery' => $this->is_cash_on_delivery(),
        ];
    }
}
