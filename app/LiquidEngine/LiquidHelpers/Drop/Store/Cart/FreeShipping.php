<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Store\Cart;

use App\Helper\Format;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Currency;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use Throwable;

/**
 * FreeShipping Drop class for handling free shipping data in Liquid templates
 * 
 * @property-read int $amount The free shipping amount
 * @property-read float $amount_input The raw free shipping amount
 * @property-read string $amount_formatted The formatted free shipping amount
 * @property-read array $amount_parts The free shipping amount parts
 * @property-read Currency $currency The currency object
 * @property-read bool $is_active Whether free shipping is active
 * @property-read int $minimum_amount The minimum amount required for free shipping
 * @property-read int $remaining_amount The remaining amount needed for free shipping
 * @property-read string $remaining_amount_formatted The formatted remaining amount
 */
class FreeShipping extends AbstractDrop
{
    /**
     * @var int
     */
    protected int $total;

    /**
     * FreeShipping constructor.
     * 
     * @param int $total
     */
    public function __construct(int $total)
    {
        $this->total = $total;
    }

    /**
     * Get the currency object
     * 
     * @return Currency
     */
    public function currency(): Currency
    {
        return new Currency();
    }

    /**
     * Get the free shipping amount
     * 
     * @return int
     */
    public function amount(): int
    {
        return $this->total;
    }

    /**
     * Get the raw free shipping amount
     * 
     * @return float
     */
    public function amount_input(): float
    {
        return Format::moneyInput($this->total);
    }

    /**
     * Get the formatted free shipping amount
     * 
     * @return string
     */
    public function amount_formatted(): string
    {
        return Format::money($this->total);
    }

    /**
     * Get the free shipping amount parts
     * 
     * @return array
     */
    public function amount_parts(): array
    {
        return PriceParts::format($this->amount_input(), $this->currency()->code());
    }

    /**
     * Check if free shipping is active
     * 
     * @return bool
     */
    public function is_active(): bool
    {
        return $this->total <= 0;
    }

    /**
     * Get the minimum amount required for free shipping
     * 
     * @return int
     */
    public function minimum_amount(): int
    {
        return $this->total > 0 ? $this->total : 0;
    }

    /**
     * Get the remaining amount needed for free shipping
     * 
     * @return int
     */
    public function remaining_amount(): int
    {
        return max(0, $this->total);
    }

    /**
     * Get the formatted remaining amount needed for free shipping
     * 
     * @return string
     */
    public function remaining_amount_formatted(): string
    {
        return Format::money($this->remaining_amount());
    }

    /**
     * Get the free shipping data as a plain array
     * 
     * @return array{
     *     amount: int,
     *     amount_input: float,
     *     amount_formatted: string,
     *     amount_parts: array,
     *     currency: Currency,
     *     is_active: bool,
     *     minimum_amount: int,
     *     remaining_amount: int,
     *     remaining_amount_formatted: string
     * }
     * @throws Throwable
     */
    public function toArray(): array
    {
        return [
            'amount' => $this->amount(),
            'amount_input' => $this->amount_input(),
            'amount_formatted' => $this->amount_formatted(),
            'amount_parts' => $this->amount_parts(),
            'currency' => $this->currency(),
            'is_active' => $this->is_active(),
            'minimum_amount' => $this->minimum_amount(),
            'remaining_amount' => $this->remaining_amount(),
            'remaining_amount_formatted' => $this->remaining_amount_formatted(),
        ];
    }
}
