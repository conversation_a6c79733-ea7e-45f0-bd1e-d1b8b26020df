<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Store\Cart;

use App\Helper\Store\CartTotal;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Currency;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use Throwable;

/**
 * Total Drop class for handling cart total data in Liquid templates
 * 
 * @property-read string $key The total key
 * @property-read string $name The total name
 * @property-read string|null $description The total description
 * @property-read Currency $currency The currency object
 * @property-read int $amount The total amount
 * @property-read float $amount_input The raw total amount
 * @property-read string $amount_formatted The formatted total amount
 * @property-read array $amount_parts The total amount parts
 * @property-read bool $is_tax Whether this is a tax total
 * @property-read bool $is_shipping Whether this is a shipping total
 * @property-read bool $is_discount Whether this is a discount total
 * @property-read bool $is_subtotal Whether this is a subtotal
 * @property-read bool $is_total Whether this is the final total
 */
class Total extends AbstractDrop
{
    /**
     * @var CartTotal
     */
    protected CartTotal $total;

    /**
     * Total constructor.
     * 
     * @param CartTotal $total
     */
    public function __construct(CartTotal $total)
    {
        $this->total = $total;
    }

    /**
     * Get the total key
     * 
     * @return string
     */
    public function key(): string
    {
        return $this->total->getKey();
    }

    /**
     * Get the total name
     * 
     * @return string
     */
    public function name(): string
    {
        return $this->total->getName();
    }

    /**
     * Get the total description
     * 
     * @return string|null
     */
    public function description(): ?string
    {
        return $this->total->getDescription();
    }

    /**
     * Get the currency object
     * 
     * @return Currency
     */
    public function currency(): Currency
    {
        return new Currency($this->total->getCurrency());
    }

    /**
     * Get the total amount
     * 
     * @return int
     */
    public function amount(): int
    {
        return $this->total->getPrice();
    }

    /**
     * Get the raw total amount
     * 
     * @return float
     */
    public function amount_input(): float
    {
        return $this->total->getPriceInput();
    }

    /**
     * Get the formatted total amount
     * 
     * @return string
     */
    public function amount_formatted(): string
    {
        return $this->total->getPriceFormatted();
    }

    /**
     * Get the total amount parts
     * 
     * @return array
     */
    public function amount_parts(): array
    {
        return PriceParts::format($this->amount_input(), $this->currency()->code());
    }

    /**
     * Check if this is a tax total
     * 
     * @return bool
     */
    public function is_tax(): bool
    {
        return str_starts_with($this->key(), 'tax.');
    }

    /**
     * Check if this is a shipping total
     * 
     * @return bool
     */
    public function is_shipping(): bool
    {
        return str_starts_with($this->key(), 'shipping.');
    }

    /**
     * Check if this is a discount total
     * 
     * @return bool
     */
    public function is_discount(): bool
    {
        return str_starts_with($this->key(), 'discount.');
    }

    /**
     * Check if this is a subtotal
     * 
     * @return bool
     */
    public function is_subtotal(): bool
    {
        return $this->key() === 'subtotal';
    }

    /**
     * Check if this is the final total
     * 
     * @return bool
     */
    public function is_total(): bool
    {
        return $this->key() === 'total.total';
    }

    /**
     * Get the total data as a plain array
     * 
     * @return array{
     *     key: string,
     *     name: string,
     *     description: string|null,
     *     amount: int,
     *     amount_input: float,
     *     amount_formatted: string,
     *     amount_parts: array,
     *     currency: Currency,
     *     is_tax: bool,
     *     is_shipping: bool,
     *     is_discount: bool,
     *     is_subtotal: bool,
     *     is_total: bool
     * }
     * @throws Throwable
     */
    public function toArray(): array
    {
        return [
            'key' => $this->key(),
            'name' => $this->name(),
            'description' => $this->description(),
            'amount' => $this->amount(),
            'amount_input' => $this->amount_input(),
            'amount_formatted' => $this->amount_formatted(),
            'amount_parts' => $this->amount_parts(),
            'currency' => $this->currency(),
            'is_tax' => $this->is_tax(),
            'is_shipping' => $this->is_shipping(),
            'is_discount' => $this->is_discount(),
            'is_subtotal' => $this->is_subtotal(),
            'is_total' => $this->is_total(),
        ];
    }
}
