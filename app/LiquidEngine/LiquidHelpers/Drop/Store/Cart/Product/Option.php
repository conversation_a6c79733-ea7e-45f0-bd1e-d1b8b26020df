<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Store\Cart\Product;

use App\Models\Store\CartItemOption;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use Throwable;

/**
 * Option Drop class for handling cart item option data in Liquid templates
 * 
 * @property-read string $name The option name
 * @property-read string $type The option type
 * @property-read string $value The option value
 * @property-read string|null $url The option file URL (for file type)
 * @property-read int|null $amount The option amount
 * @property-read float|null $amount_input The raw option amount
 * @property-read string|null $amount_formatted The formatted option amount
 * @property-read array $amount_parts The option amount parts
 * @property-read bool $is_file Whether the option is a file type
 * @property-read bool $is_text Whether the option is a text type
 * @property-read bool $is_select Whether the option is a select type
 * @property-read bool $is_radio Whether the option is a radio type
 * @property-read bool $is_checkbox Whether the option is a checkbox type
 */
class Option extends AbstractDrop
{
    /**
     * @var CartItemOption
     */
    protected CartItemOption $option;

    /**
     * Option constructor.
     * 
     * @param CartItemOption $option
     */
    public function __construct(CartItemOption $option)
    {
        $this->option = $option;
    }

    /**
     * Get the option name
     * 
     * @return string
     */
    public function name(): string
    {
        return $this->option->name;
    }

    /**
     * Get the option type
     * 
     * @return string
     */
    public function type(): string
    {
        return $this->option->type;
    }

    /**
     * Get the option value
     * 
     * @return string
     */
    public function value(): string
    {
        if ($this->is_file()) {
            return $this->option->file_name;
        } elseif ($this->option->option) {
            return $this->option->option;
        } else {
            return $this->option->value_formatted;
        }
    }

    /**
     * Get the option file URL
     * 
     * @return string|null
     */
    public function url(): ?string
    {
        if ($this->is_file()) {
            return $this->option->file_url;
        }

        return null;
    }

    /**
     * Get the option amount
     * 
     * @return int|null
     */
    public function amount(): ?int
    {
        return $this->option->field->amount ?? null;
    }

    /**
     * Get the raw option amount
     * 
     * @return float|null
     */
    public function amount_input(): ?float
    {
        return $this->amount() > 0 ? $this->option->field->amount_input : null;
    }

    /**
     * Get the formatted option amount
     * 
     * @return string|null
     */
    public function amount_formatted(): ?string
    {
        return $this->amount() > 0 ? $this->option->field->amount_formatted : null;
    }

    /**
     * Get the option amount parts
     * 
     * @return array
     */
    public function amount_parts(): array
    {
        return PriceParts::format($this->amount_input() > 0 ? $this->amount_input() : 0);
    }

    /**
     * Check if the option is a file type
     * 
     * @return bool
     */
    public function is_file(): bool
    {
        return $this->type() === 'file';
    }

    /**
     * Check if the option is a text type
     * 
     * @return bool
     */
    public function is_text(): bool
    {
        return $this->type() === 'text';
    }

    /**
     * Check if the option is a select type
     * 
     * @return bool
     */
    public function is_select(): bool
    {
        return $this->type() === 'select';
    }

    /**
     * Check if the option is a radio type
     * 
     * @return bool
     */
    public function is_radio(): bool
    {
        return $this->type() === 'radio';
    }

    /**
     * Check if the option is a checkbox type
     * 
     * @return bool
     */
    public function is_checkbox(): bool
    {
        return $this->type() === 'checkbox';
    }

    /**
     * Get the option data as a plain array
     * 
     * @return array{
     *     name: string,
     *     type: string,
     *     value: string,
     *     url: string|null,
     *     amount: int|null,
     *     amount_input: float|null,
     *     amount_formatted: string|null,
     *     amount_parts: array,
     *     is_file: bool,
     *     is_text: bool,
     *     is_select: bool,
     *     is_radio: bool,
     *     is_checkbox: bool
     * }
     * @throws Throwable
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'type' => $this->type(),
            'value' => $this->value(),
            'url' => $this->url(),
            'amount' => $this->amount(),
            'amount_input' => $this->amount_input(),
            'amount_formatted' => $this->amount_formatted(),
            'amount_parts' => $this->amount_parts(),
            'is_file' => $this->is_file(),
            'is_text' => $this->is_text(),
            'is_select' => $this->is_select(),
            'is_radio' => $this->is_radio(),
            'is_checkbox' => $this->is_checkbox(),
        ];
    }
}
