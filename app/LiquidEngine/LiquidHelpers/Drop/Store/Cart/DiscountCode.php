<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Store\Cart;

use App\Models\Discount\Discount;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

/**
 * DiscountCode Drop class for handling discount code data in Liquid templates
 * 
 * @property-read string $name The discount name
 * @property-read string $code The discount code
 * @property-read array $codes All available discount codes
 * @property-read string $type The discount type
 * @property-read float $amount The discount amount
 * @property-read string $amount_formatted The formatted discount amount
 * @property-read bool $is_percentage Whether the discount is percentage-based
 * @property-read bool $is_fixed Whether the discount is fixed amount
 * @property-read bool $is_free_shipping Whether the discount provides free shipping
 * @property-read bool $is_active Whether the discount is active
 * @property-read string|null $minimum_requirement The minimum requirement for the discount
 * @property-read string|null $maximum_requirement The maximum requirement for the discount
 */
class DiscountCode extends AbstractDrop
{
    /**
     * @var Discount
     */
    protected Discount $discount;

    /**
     * DiscountCode constructor.
     * 
     * @param Discount $discount
     */
    public function __construct(Discount $discount)
    {
        $this->discount = $discount;
    }

    /**
     * Get the discount name
     * 
     * @return string
     */
    public function name(): string
    {
        return $this->discount->name;
    }

    /**
     * Get the discount code
     * 
     * @return string
     */
    public function code(): string
    {
        return $this->discount->code_prefix ? $this->discount->barcode : $this->discount->code;
    }

    /**
     * Get all available discount codes
     * 
     * @return array
     */
    public function codes(): array
    {
        return $this->discount->codes;
    }

    /**
     * Get the discount type
     * 
     * @return string
     */
    public function type(): string
    {
        return $this->discount->type;
    }

    /**
     * Get the discount amount
     * 
     * @return float
     */
    public function amount(): float
    {
        return (float) $this->discount->amount;
    }

    /**
     * Get the formatted discount amount
     * 
     * @return string
     */
    public function amount_formatted(): string
    {
        return $this->discount->amount_formatted;
    }

    /**
     * Check if the discount is percentage-based
     * 
     * @return bool
     */
    public function is_percentage(): bool
    {
        return $this->discount->type === 'percentage';
    }

    /**
     * Check if the discount is fixed amount
     * 
     * @return bool
     */
    public function is_fixed(): bool
    {
        return $this->discount->type === 'fixed';
    }

    /**
     * Check if the discount provides free shipping
     * 
     * @return bool
     */
    public function is_free_shipping(): bool
    {
        return $this->discount->type === 'free_shipping';
    }

    /**
     * Check if the discount is active
     * 
     * @return bool
     */
    public function is_active(): bool
    {
        return (bool) $this->discount->active;
    }

    /**
     * Get the minimum requirement for the discount
     * 
     * @return string|null
     */
    public function minimum_requirement(): ?string
    {
        return $this->discount->minimum_requirement;
    }

    /**
     * Get the maximum requirement for the discount
     * 
     * @return string|null
     */
    public function maximum_requirement(): ?string
    {
        return $this->discount->maximum_requirement;
    }

    /**
     * Get the discount as a plain array
     * 
     * @return array{
     *     name: string,
     *     code: string,
     *     codes: array,
     *     type: string,
     *     amount: float,
     *     amount_formatted: string,
     *     is_percentage: bool,
     *     is_fixed: bool,
     *     is_free_shipping: bool,
     *     is_active: bool,
     *     minimum_requirement: string|null,
     *     maximum_requirement: string|null
     * }
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'code' => $this->code(),
            'codes' => $this->codes(),
            'type' => $this->type(),
            'amount' => $this->amount(),
            'amount_formatted' => $this->amount_formatted(),
            'is_percentage' => $this->is_percentage(),
            'is_fixed' => $this->is_fixed(),
            'is_free_shipping' => $this->is_free_shipping(),
            'is_active' => $this->is_active(),
            'minimum_requirement' => $this->minimum_requirement(),
            'maximum_requirement' => $this->maximum_requirement(),
        ];
    }
}
