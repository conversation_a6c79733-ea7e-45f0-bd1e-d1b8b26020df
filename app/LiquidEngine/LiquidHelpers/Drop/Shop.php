<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Exceptions\Error;
use App\Helper\YesNo;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Router\Host;
use Illuminate\Support\Str;
use Liquid\Drop;
use App\Models\Product\Product as ProductModel;
use App\Models\Product\Vendor as VendorModel;
use Liquid\LiquidException;
use App\LiquidEngine\LiquidHelpers\Drop\AllGlobals\Products;
use App\LiquidEngine\LiquidHelpers\Drop\Settings\Logo;
use ReflectionClass;
use ReflectionException;
use ReflectionMethod;
use Carbon\Carbon;

class Shop extends Drop
{
    /**
     * @var array
     */
    protected $_data = [];

    /**
     * @return string
     */
    public function logo(): \App\LiquidEngine\LiquidHelpers\Drop\Settings\Logo
    {
        return new Logo();
    }

    /**
     * @return string
     * @throws Error
     */
    public function favicon()
    {
        return favicon();
    }

    public function address()
    {

    }

    /**
     * @return int
     */
    public function collections_count(): int
    {
        return 0;
    }

    /**
     * @return Currency
     */
    public function currency(): \App\LiquidEngine\LiquidHelpers\Drop\Currency
    {
        return new Currency();
    }

    /**
     * @return string
     */
    public function description(): string
    {
        return setting('site_meta_description') ?? '';
    }

    /**
     * @return string|null
     */
    public function domain()
    {
        return site()->getPrimaryHost()->host ?? null;
    }

    /**
     * @return string
     */
    public function email()
    {
        return setting('site_email');
    }

    /**
     * @return array
     */
    public function enabled_currencies(): array
    {
        return [
            $this->currency()
        ];
    }

    /**
     * @return array
     */
    public function enabled_payment_types()
    {
        return $this->_data[__FUNCTION__] ?? [];
    }

    /**
     * @return string
     */
    public function locale()
    {
        return app()->getLocale();
    }

    /**
     * @return array
     */
    public function metafields(): array
    {
        return [];
    }

    public function money_format()
    {

    }

    public function money_with_currency_format()
    {

    }

    /**
     * @return string
     */
    public function name()
    {
        return setting('site_name');
    }

    public function password_message()
    {

    }

    /**
     * @return string|null
     */
    public function permanent_domain()
    {
        return $this->_data[__FUNCTION__] ?? null;
    }

    /**
     * @return string
     */
    public function phone()
    {
        return setting('site_phone');
    }

    /**
     * @return array
     */
    public function policies()
    {
        // Standard policy types
        $policyTypes = [
            'refund-policy',
            'privacy-policy',
            'terms-of-service',
            'shipping-policy',
            'legal-notice',
            'contact-information',
        ];
        
        // Get policies from the database
        $policies = \App\Models\Page\Page::whereIn('system_page', $policyTypes)
            ->orWhere('page_type', 'policy')
            ->active()
            ->get()
            ->map(function ($policy) {
                return [
                    'title' => $policy->name,
                    'handle' => $policy->url_handle,
                    'body' => $policy->content,
                    'url' => url("/policies/{$policy->url_handle}"),
                ];
            })
            ->keyBy('handle')
            ->toArray();
        
        // Add placeholder policies for standard types if they don't exist
        foreach ($policyTypes as $type) {
            if (!isset($policies[$type])) {
                $title = Str::title(str_replace('-', ' ', $type));
                $policies[$type] = [
                    'title' => $title,
                    'handle' => $type,
                    'body' => '',
                    'url' => url("/policies/{$type}"),
                ];
            }
        }
        
        return $policies;
    }

    /**
     * @return int
     * @throws LiquidException
     */
    public function products_count()
    {
        if (($all_products = $this->context->get('all_products')) instanceof Products) {
            return $all_products->size();
        }

        return 0;
    }

    /**
     * @return string
     */
    public function secure_url()
    {
        return site()->getSiteUrl('primary');
    }

    /**
     * @return bool
     */
    public function taxes_included(): bool
    {
        return setting('vat_included') == YesNo::True;
    }

    /**
     * @return array
     */
    public function types(): array
    {
        return [
            "simple", "variant",
            "multiple", "digital",
            "bundle",
        ];
    }

    /**
     * @return string
     */
    public function url()
    {
        return $this->secure_url();
    }

    /**
     * @return array|Vendor[]
     */
    public function vendors()
    {
        return $this->_data[__FUNCTION__] ?? [];
    }

    /**
     * @param string $method
     * @return mixed|null
     */
    protected function beforeMethod($method)
    {
        switch (true) {
            case $method === 'vendors' && !array_key_exists($method, $this->_data);
                $this->_data[$method] = VendorModel::all()->map(function (VendorModel $vendor): array {
                    return [
                        'handler' => $vendor->url_handle,
                        'brand' => new Vendor($vendor)
                    ];
                })->pluck('brand', 'handler')->all();
                break;
            case $method === 'enabled_payment_types' && !array_key_exists($method, $this->_data);
                $this->_data[$method] = PaymentProviderConfiguration::getConfigurations()->keys()->all();
                break;
            case $method === 'permanent_domain' && !array_key_exists($method, $this->_data);
                $this->_data[$method] = site()->hosts->filter(function (Host $host) {
                    return Str::endsWith($host->host, '.' . config('url.sites'));
                })->first()->host ?? null;
                break;
        }

        return $this->_data[$method] ?? null;
    }

    /**
     * Get the shop's customer accounts mode
     * 
     * @return string
     */
    public function customer_accounts_enabled()
    {
        return setting('customer_accounts_enabled') ? 'required' : 'disabled';
    }
    
    /**
     * Get the shop's customer accounts optional setting
     * 
     * @return bool
     */
    public function customer_accounts_optional()
    {
        return setting('customer_accounts_enabled') && !setting('customer_accounts_required');
    }
    
    /**
     * Get the shop's features image
     * 
     * @return array|null
     */
    public function features_image()
    {
        $image = setting('features_image');
        if ($image) {
            return [
                'src' => $image,
                'alt' => $this->name(),
            ];
        }
        return null;
    }
    
    /**
     * Get the shop's meta title
     * 
     * @return string
     */
    public function metafields_global_title_tag()
    {
        return setting('site_meta_title') ?? $this->name();
    }
    
    /**
     * Get the shop's meta description
     * 
     * @return string
     */
    public function metafields_global_description_tag()
    {
        return setting('site_meta_description') ?? '';
    }
    
    /**
     * Get the shop's search enabled setting
     * 
     * @return bool
     */
    public function search_enabled()
    {
        return setting('search_enabled') !== 'disabled';
    }
    
    /**
     * Get all pages from the shop
     * 
     * @return array
     */
    public function pages()
    {
        $pages = \App\Models\Page\Page::active()->get();
        return $pages->map(function(\App\Models\Page\Page $page) {
            return new Page($page);
        })->all();
    }
    
    /**
     * Get all collections from the shop
     * 
     * @return array
     */
    public function collections()
    {
        $collections = \App\Models\Collections\Collections::active()->get();
        return $collections->map(function(\App\Models\Collections\Collections $collection) {
            return new Collection($collection);
        })->all();
    }
    
    /**
     * Get all blogs from the shop
     * 
     * @return array
     */
    public function blogs()
    {
        $blogs = \App\Models\Blog\Blog::active()->get();
        return $blogs->map(function($blog) {
            return new \App\LiquidEngine\LiquidHelpers\Drop\Blog\Blog($blog);
        })->all();
    }

    /**
     * Get shop creation date in Shopify-compatible format
     * 
     * @return string
     */
    public function created_at(): string
    {
        $site = site();
        $createdAt = $site->created_at ?? $site->date_added;
        return $createdAt ? Carbon::parse($createdAt)->toISOString() : '';
    }

    /**
     * Get shop last update date in Shopify-compatible format
     * 
     * @return string
     */
    public function updated_at(): string
    {
        $site = site();
        $updatedAt = $site->updated_at ?? $site->date_modified;
        return $updatedAt ? Carbon::parse($updatedAt)->toISOString() : '';
    }

    /**
     * Get shop ID
     * 
     * @return int
     */
    public function id(): int
    {
        return (int) site()->getId();
    }

    /**
     * Check if checkout API is supported
     * 
     * @return bool
     */
    public function checkout_api_supported(): bool
    {
        return true; // CloudCart supports checkout API
    }

    /**
     * Get shop plan name
     * 
     * @return string
     */
    public function plan_name(): string
    {
        $site = site();
        return $site->plan->name ?? 'free';
    }

    /**
     * Get shop plan display name
     * 
     * @return string
     */
    public function plan_display_name(): string
    {
        $site = site();
        return $site->plan->display_name ?? ucfirst($this->plan_name());
    }

    /**
     * Check if shop is enabled
     * 
     * @return bool
     */
    public function enabled(): bool
    {
        return site()->status === 1;
    }

    /**
     * Get shop primary locale
     * 
     * @return string
     */
    public function primary_locale(): string
    {
        return site()->getLanguage();
    }

    /**
     * Check if shop is password protected
     * 
     * @return bool
     */
    public function password_enabled(): bool
    {
        return !empty(site()->password);
    }

    /**
     * Get shop weight unit
     * 
     * @return string
     */
    public function weight_unit(): string
    {
        return \get_option('weight_unit', 'kg');
    }

    /**
     * Get collection of items as a plain array for Shopify compatibility
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'email' => $this->email(),
            'domain' => $this->domain(),
            'url' => $this->url(),
            'permanent_domain' => $this->permanent_domain(),
            'currency' => $this->currency(),
            'phone' => $this->phone(),
            'address' => $this->address(),
            'city' => $this->city(),
            'source' => $this->source(),
            'google_analytics_id' => $this->google_analytics_id(),
            'latitude' => $this->latitude(),
            'longitude' => $this->longitude(),
            'primary_location_id' => $this->primary_location_id(),
            'primary_locale' => $this->primary_locale(),
            'address1' => $this->address1(),
            'address2' => $this->address2(),
            'created_at' => $this->created_at(),
            'updated_at' => $this->updated_at(),
            'checkout_api_supported' => $this->checkout_api_supported(),
            'plan_name' => $this->plan_name(),
            'plan_display_name' => $this->plan_display_name(),
            'enabled' => $this->enabled(),
            'password_message' => $this->password_message(),
            'password_enabled' => $this->password_enabled(),
            'taxes_included' => $this->taxes_included(),
            'weight_unit' => $this->weight_unit(),
            'money_format' => $this->money_format(),
            'money_with_currency_format' => $this->money_with_currency_format(),
        ];
    }

}
