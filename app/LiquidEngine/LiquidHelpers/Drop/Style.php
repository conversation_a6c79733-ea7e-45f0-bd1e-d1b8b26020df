<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Support\Facades\Config;

/**
 * Style Drop Class
 * 
 * This class provides a structured way to handle CSS styles in Liquid templates.
 * It includes support for Shopify compatibility, accessibility, SEO, and analytics.
 * 
 * @property string $href The source URL of the stylesheet
 * @property string $media The media query for the stylesheet
 * @property string $rel The relationship type (e.g., stylesheet)
 * @property string $type The MIME type of the stylesheet
 * @property array $attributes Additional HTML attributes
 * @property array $dependencies Style dependencies
 * @property array $analytics Analytics data for the stylesheet
 */
class Style extends AbstractDrop
{
    /**
     * @var string The source URL of the stylesheet
     */
    protected string $href;

    /**
     * @var string The media query for the stylesheet
     */
    protected string $media;

    /**
     * @var string The relationship type (e.g., stylesheet)
     */
    protected string $rel;

    /**
     * @var string The MIME type of the stylesheet
     */
    protected string $type;

    /**
     * @var array<string, string> Additional HTML attributes
     */
    protected array $attributes;

    /**
     * @var array<string> Style dependencies
     */
    protected array $dependencies;

    /**
     * @var array<string, mixed> Analytics data for the stylesheet
     */
    protected array $analytics;

    /**
     * Create a new Style instance.
     *
     * @param string $href The source URL of the stylesheet
     * @param string $media The media query for the stylesheet
     * @param string $rel The relationship type
     * @param string $type The MIME type
     * @param array<string, string> $attributes Additional HTML attributes
     * @param array<string> $dependencies Style dependencies
     * @param array<string, mixed> $analytics Analytics data
     */
    public function __construct(
        string $href,
        string $media = 'all',
        string $rel = 'stylesheet',
        string $type = 'text/css',
        array $attributes = [],
        array $dependencies = [],
        array $analytics = []
    ) {
        $this->href = $href;
        $this->media = $media;
        $this->rel = $rel;
        $this->type = $type;
        $this->attributes = $attributes;
        $this->dependencies = $dependencies;
        $this->analytics = $analytics;
    }

    /**
     * Get the source URL of the stylesheet.
     *
     * @return string
     */
    public function href(): string
    {
        return $this->href;
    }

    /**
     * Get the media query for the stylesheet.
     *
     * @return string
     */
    public function media(): string
    {
        return $this->media;
    }

    /**
     * Get the relationship type.
     *
     * @return string
     */
    public function rel(): string
    {
        return $this->rel;
    }

    /**
     * Get the MIME type of the stylesheet.
     *
     * @return string
     */
    public function type(): string
    {
        return $this->type;
    }

    /**
     * Get additional HTML attributes.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return $this->attributes;
    }

    /**
     * Get style dependencies.
     *
     * @return array<string>
     */
    public function dependencies(): array
    {
        return $this->dependencies;
    }

    /**
     * Get analytics data.
     *
     * @return array<string, mixed>
     */
    public function analytics(): array
    {
        return $this->analytics;
    }

    /**
     * Get accessibility attributes for the stylesheet.
     *
     * @return array<string, string>
     */
    public function accessibility(): array
    {
        return [
            'role' => 'stylesheet',
            'aria-label' => 'CSS stylesheet',
        ];
    }

    /**
     * Get structured data for SEO.
     *
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'WebPage',
            'mainEntity' => [
                '@type' => 'WebPageElement',
                'name' => basename($this->href),
                'contentType' => 'text/css',
                'mediaType' => $this->media,
            ],
        ];
    }

    /**
     * Convert the stylesheet to a Shopify-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toShopifyArray(): array
    {
        return [
            'href' => $this->href,
            'media' => $this->media,
            'rel' => $this->rel,
            'type' => $this->type,
            'attributes' => $this->attributes,
            'dependencies' => $this->dependencies,
        ];
    }

    /**
     * Convert the stylesheet to an SEO-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toSeoArray(): array
    {
        return [
            'href' => $this->href,
            'media' => $this->media,
            'rel' => $this->rel,
            'type' => $this->type,
            'structured_data' => $this->structured_data(),
        ];
    }

    /**
     * Convert the stylesheet to an analytics-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toAnalyticsArray(): array
    {
        return array_merge($this->analytics, [
            'href' => $this->href,
            'media' => $this->media,
            'type' => $this->type,
            'dependencies_count' => count($this->dependencies),
        ]);
    }

    /**
     * Convert the stylesheet to an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'href' => $this->href,
            'media' => $this->media,
            'rel' => $this->rel,
            'type' => $this->type,
            'attributes' => $this->attributes,
            'dependencies' => $this->dependencies,
            'analytics' => $this->analytics,
            'accessibility' => $this->accessibility(),
            'structured_data' => $this->structured_data(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics_data' => $this->toAnalyticsArray(),
        ];
    }
} 