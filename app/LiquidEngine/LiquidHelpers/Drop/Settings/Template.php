<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Settings;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\View;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

class Template extends AbstractDrop
{

    /**
     * @var string $_template
     */
    protected $_template;

    /**
     * @var string $_directory
     */
    protected $_directory;

    /**
     * @var string $_name
     */
    protected $_name;

    /**
     * @var string $_suffix
     */
    protected $_suffix;

    /**
     * Template constructor.
     * @param $template
     */
    public function __construct($template)
    {
        $this->_parse($template);
        $this->_template = $template;
        if (!empty($view = request()->query('view')) && is_scalar($view)) {
            $this->_suffix = $view;
            $this->_template = [
                $this->_template . '@' . $view,
                $this->_template
            ];
        }
    }

    /**
     * @return string
     */
    public function key(): string
    {
        return implode('.', array_filter([$this->name(), $this->suffix()]));
    }

    /**
     * @return string
     */
    public function directory()
    {
        return $this->_directory;
    }

    /**
     * @return string
     */
    public function name()
    {
        return $this->_name;
    }

    /**
     * @return string
     */
    public function suffix()
    {
        return $this->_suffix;
    }

    /**
     * @return string
     */
    public function data_key()
    {
        if ($key = config('_tmp.template.data_key')) {
            return $key;
        }

        if ($template = $this->getTemplate()) {
            if (strpos($template, 'templates.') === 0) {
                $template = substr($template, 10);
            }

            return str_replace(['.', '@'], '_', $template);
        }

        return null;
    }

    /**
     * @return array
     */
    public function getTemplates(): array
    {
        return is_array($this->_template) ? $this->_template : [$this->_template];
    }

    /**
     * @return string
     */
    public function getTemplate()
    {
        return is_array($this->_template) ? Arr::last($this->_template) : $this->_template;
    }

    /**
     * Check if the template exists in any form (in current theme or global theme)
     * 
     * @return bool
     */
    public function exists(): bool
    {
        $debugInfo = [];
        $found = false;
        
        foreach ($this->getTemplates() as $template) {
            // Try with dot notation
            $dotExists = View::exists($template);
            $debugInfo[$template] = ['dot_notation' => $dotExists];
            if ($dotExists) {
                $found = true;
                \Illuminate\Support\Facades\Log::debug("Template found with dot notation: {$template}");
            }
            
            // Try with slash notation
            $slashTemplate = str_replace('.', '/', $template);
            $slashExists = View::exists($slashTemplate);
            $debugInfo[$template]['slash_notation'] = $slashExists;
            if ($slashExists) {
                $found = true;
                \Illuminate\Support\Facades\Log::debug("Template found with slash notation: {$slashTemplate}");
            }
            
            // Try with .liquid extension
            $liquidExists = View::exists($template . '.liquid');
            $debugInfo[$template]['dot_liquid'] = $liquidExists;
            if ($liquidExists) {
                $found = true;
                \Illuminate\Support\Facades\Log::debug("Template found with .liquid extension: {$template}.liquid");
            }
            
            // Try with slash notation and .liquid extension
            $slashLiquidExists = View::exists($slashTemplate . '.liquid');
            $debugInfo[$template]['slash_liquid'] = $slashLiquidExists;
            if ($slashLiquidExists) {
                $found = true;
                \Illuminate\Support\Facades\Log::debug("Template found with slash notation and .liquid extension: {$slashTemplate}.liquid");
            }
            
            // Try with .json extension
            $jsonExists = View::exists($template . '.json');
            $debugInfo[$template]['json'] = $jsonExists;
            if ($jsonExists) {
                $found = true;
                \Illuminate\Support\Facades\Log::debug("Template found with .json extension: {$template}.json");
            }
            
            // Try with slash notation and .json extension
            $slashJsonExists = View::exists($slashTemplate . '.json');
            $debugInfo[$template]['slash_json'] = $slashJsonExists;
            if ($slashJsonExists) {
                $found = true;
                \Illuminate\Support\Facades\Log::debug("Template found with slash notation and .json extension: {$slashTemplate}.json");
            }
            
            // Check direct file existence for Shopify-style templates in current theme
            $templateName = $this->name();
            $activeTheme = site('template');
            $formats = ['.json', '.liquid'];
            
            foreach ($formats as $format) {
                $themePath = resource_path("themes/{$activeTheme}/templates/{$templateName}{$format}");
                $globalPath = resource_path("themes/_global/global-theme/templates/{$templateName}{$format}");
                
                if (file_exists($themePath)) {
                    $found = true;
                    \Illuminate\Support\Facades\Log::debug("Template found as file in theme: {$themePath}");
                    $debugInfo['file_exists'][$themePath] = true;
                }
                
                if (file_exists($globalPath)) {
                    $found = true;
                    \Illuminate\Support\Facades\Log::debug("Template found as file in global theme: {$globalPath}");
                    $debugInfo['file_exists'][$globalPath] = true;
                }
            }
        }
        
        // If not found, log all attempts
        if (!$found) {
            \Illuminate\Support\Facades\Log::error('Template not found after all attempts', ['debug_info' => $debugInfo]);
        }
        
        return $found;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'directory' => $this->directory(),
            'suffix' => $this->suffix(),
            'key' => $this->key(),
            'data_key' => $this->data_key(),
        ];
    }

    /**
     * @param $input
     */
    protected function _parse($input)
    {
        if (empty($parts = explode('.', $input))) {
            return;
        }

        array_shift($parts);

        if (count($parts) == 1) {
            $this->_name = array_shift($parts);
            return;
        }

        $this->_directory = array_shift($parts);
        if (count($parts) > 1) {
            $this->_name = implode('.', $parts);
        } else {
            $this->_name = array_shift($parts);
        }

        return;
    }

}
