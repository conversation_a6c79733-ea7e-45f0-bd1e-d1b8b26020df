<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Helper\YesNo;
use App\Models\Customer\CustomerBillingAddress;
use App\Models\Customer\CustomerShippingAddress;
use App\Models\Customer\Customer as CustomerModel;
use Illuminate\Support\Str;
use App\LiquidEngine\LiquidHelpers\Drop\Customer\Address;
use Carbon\Carbon;

class Customer extends AbstractDrop
{

    /**
     * @var CustomerModel $_customer
     */
    protected \App\Models\Customer\Customer $_customer;

    /**
     * @param \App\Models\Customer\Customer $customer
     * @return mixed
     */
    public function __construct(CustomerModel $customer)
    {
        $this->_customer = $customer;
    }

    /**
     * @return integer
     */
    public function id()
    {
        return $this->_customer->id;
    }

    /**
     * @return string
     */
    public function first_name()
    {
        return $this->_customer->first_name;
    }

    /**
     * @return string
     */
    public function last_name()
    {
        return $this->_customer->last_name;
    }

    /**
     * @return string
     */
    public function full_name()
    {
        return $this->_customer->full_name;
    }

    /**
     * @return string
     */
    public function initials()
    {
        if ($this->first_name() || $this->last_name()) {
            $initials = implode('', array_map(function ($name) {
                return Str::upper(Str::substr($name, 0, 1));
            }, array_filter(explode(' ', $this->full_name()))));
        } else {
            $initials = Str::upper(Str::substr(preg_replace('/[^a-z0-9]/i', '', $this->email()), 0, 2));
        }

        return $initials;
    }

    /**
     * @return string
     */
    public function email()
    {
        return $this->_customer->email;
    }

    /**
     * @return boolean
     */
    public function marketing(): bool
    {
        return $this->_customer->marketing === YesNo::True;
    }

    /**
     * @return boolean
     */
    public function banned(): bool
    {
        return $this->_customer->banned === YesNo::True;
    }

    /**
     * @return boolean
     */
    public function email_confirmed(): bool
    {
        return $this->_customer->email_confirmed === YesNo::True;
    }

    /**
     * @return null|Address
     */
    public function shipping_address(): ?\App\LiquidEngine\LiquidHelpers\Drop\Customer\Address
    {
        if ($this->_customer->relationLoaded('shipping_address')) {
            return new Address($this->_customer->shipping_address);
        }

        return null;
    }

    /**
     * @return array|Address[]
     */
    public function shipping_addresses()
    {
        $addresses = [];
        if ($this->_customer->relationLoaded('shipping_addresses')) {
            $addresses = $this->_customer->shipping_addresses->map(function (CustomerShippingAddress $address): \App\LiquidEngine\LiquidHelpers\Drop\Customer\Address {
                return new Address($address);
            })->all();
        }

        return $addresses;
    }

    /**
     * @return null|Address
     */
    public function billing_address(): ?\App\LiquidEngine\LiquidHelpers\Drop\Customer\Address
    {
        if ($this->_customer->relationLoaded('billing_address')) {
            return new Address($this->_customer->billing_address);
        }

        return null;
    }

    /**
     * @return array|Address[]
     */
    public function billing_addresses()
    {
        $addresses = [];
        if ($this->_customer->relationLoaded('billing_addresses')) {
            $addresses = $this->_customer->billing_addresses->map(function (CustomerBillingAddress $address): \App\LiquidEngine\LiquidHelpers\Drop\Customer\Address {
                return new Address($address);
            })->all();
        }

        return $addresses;
    }

    /**
     * @return boolean
     */
    public function has_password(): bool
    {
        return !empty($this->_customer->password);
    }

    /**
     * Get customer's phone number
     *
     * @return string
     */
    public function phone(): string
    {
        return $this->_customer->phone ?? '';
    }

    /**
     * Get customer creation date in Shopify-compatible format
     *
     * @return string
     */
    public function created_at(): string
    {
        return $this->_customer->created_at ?
            Carbon::parse($this->_customer->created_at)->toISOString() : '';
    }

    /**
     * Get customer last update date in Shopify-compatible format
     *
     * @return string
     */
    public function updated_at(): string
    {
        return $this->_customer->updated_at ?
            Carbon::parse($this->_customer->updated_at)->toISOString() : '';
    }

    /**
     * Get the number of orders this customer has placed
     *
     * @return int
     */
    public function orders_count(): int
    {
        return $this->_customer->orders()->count();
    }

    /**
     * Get the total amount the customer has spent
     *
     * @return string
     */
    public function total_spent(): string
    {
        $total = $this->_customer->orders()
            ->where('status', '!=', 'cancelled')
            ->sum('total');
        return number_format($total, 2);
    }

    /**
     * Check if customer accepts marketing emails
     *
     * @return bool
     */
    public function accepts_marketing(): bool
    {
        return (bool) ($this->_customer->newsletter ?? false);
    }

    /**
     * Get customer's email marketing state
     *
     * @return string
     */
    public function email_marketing_consent(): string
    {
        return $this->accepts_marketing() ? 'subscribed' : 'not_subscribed';
    }

    /**
     * Get customer's state (enabled/disabled)
     *
     * @return string
     */
    public function state(): string
    {
        return $this->_customer->active ? 'enabled' : 'disabled';
    }

    /**
     * Check if customer account is verified
     *
     * @return bool
     */
    public function verified_email(): bool
    {
        return !empty($this->_customer->email_verified_at);
    }

    /**
     * Get customer's tax exempt status
     *
     * @return bool
     */
    public function tax_exempt(): bool
    {
        return (bool) ($this->_customer->tax_exempt ?? false);
    }

    /**
     * Get customer's currency (defaults to shop currency)
     *
     * @return string
     */
    public function currency(): string
    {
        return $this->_customer->currency ?? site()->currency ?? 'USD';
    }

    /**
     * Get customer's addresses
     *
     * @return array
     */
    public function addresses(): array
    {
        // Return both shipping and billing addresses
        $addresses = [];
        if ($shipping = $this->shipping_address()) {
            $addresses[] = $shipping;
        }
        if ($billing = $this->billing_address()) {
            $addresses[] = $billing;
        }
        return $addresses;
    }

    /**
     * Get customer's tags
     *
     * @return string
     */
    public function tags(): string
    {
        return $this->_customer->tags->implode(',') ?? '';
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'email' => $this->email(),
            'first_name' => $this->first_name(),
            'last_name' => $this->last_name(),
            'name' => $this->full_name(),
            'phone' => $this->phone(),
            'created_at' => $this->created_at(),
            'updated_at' => $this->updated_at(),
            'orders_count' => $this->orders_count(),
            'total_spent' => $this->total_spent(),
            'accepts_marketing' => $this->accepts_marketing(),
            'email_marketing_consent' => $this->email_marketing_consent(),
            'state' => $this->state(),
            'verified_email' => $this->verified_email(),
            'tax_exempt' => $this->tax_exempt(),
            'currency' => $this->currency(),
            'default_address' => $this->shipping_address(),
            'addresses' => $this->addresses(),
            'tags' => $this->tags(),
        ];
    }
}
