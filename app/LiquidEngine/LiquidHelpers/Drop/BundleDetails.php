<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Exceptions\Error;
use App\Models\Collections\Collections;

/**
 * BundleDetails Drop class for handling detailed bundle information in Liquid templates.
 *
 * This class extends the Bundle class to provide additional bundle details including
 * descriptions, SEO information, and associated collections. It wraps the product model
 * and exposes bundle-specific details for use in Liquid templates.
 *
 * Usage:
 *   {{ bundle.description_name }}
 *   {{ bundle.seo_title }}
 *   {{ bundle.collections }}
 *
 * Properties:
 * - description_name: string
 * - description: string
 * - seo_title: string
 * - seo_description: string
 * - collections: array<Collection>
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class BundleDetails extends Bundle
{
    /**
     * Cached collection instances.
     * Stores the loaded collections for the bundle, or null if not loaded.
     *
     * @var array<Collection>|null
     */
    protected ?array $collections = null;

    /**
     * Get the description title of the bundle.
     * Returns the title for the bundle's description section.
     *
     * @return string The description title
     */
    public function description_name(): string
    {
        return (string) $this->_product->description_title;
    }

    /**
     * Get the description of the bundle.
     * Overrides the parent class method to provide the full description.
     *
     * @return string The bundle description
     */
    public function description(): string
    {
        return (string) $this->_product->description;
    }

    /**
     * Get the SEO title of the bundle.
     * Falls back to the bundle name if no SEO title is set.
     *
     * @return string The SEO title
     */
    public function seo_title(): string
    {
        return $this->_product->seo_title ?: strip_tags($this->name());
    }

    /**
     * Get the SEO description of the bundle.
     * Returns the SEO description from the product model.
     *
     * @return string The SEO description
     */
    public function seo_description(): string
    {
        return (string) $this->_product->seo_description;
    }

    /**
     * Get the collections associated with the bundle.
     * Returns cached collections if available, otherwise loads them from the product.
     *
     * @return array<Collection> The associated collections
     */
    public function collections(): array
    {
        if (!is_null($this->collections)) {
            return $this->collections;
        }

        $records = [];
        if ($this->_product->relationLoaded('collections') && $this->_product->collections->count()) {
            $records = $this->_product->collections->map(
                fn(Collections $collection): Collection => new Collection($collection)
            )->all();
        }

        return $this->collections = $records;
    }

    /**
     * Get the collection of items as a plain array.
     * Returns an array representation of the bundle details with all its properties.
     * This method is used by Liquid templates to access the bundle data.
     *
     * @return array{
     *     description_name: string,
     *     description: string,
     *     seo_title: string,
     *     seo_description: string,
     *     collections: array<Collection>
     * } The bundle details data
     * @throws Error
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'description_name' => $this->description_name(),
            'description' => $this->description(),
            'seo_title' => $this->seo_title(),
            'seo_description' => $this->seo_description(),
            'collections' => $this->collections(),
        ]);
    }
}
