<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Variants;

use App\Exceptions\Error;
use App\Models\Product\ParameterOption as ParameterOptionModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;

/**
 * Variant Option Drop Class
 * 
 * This class represents a variant option in Liquid templates, providing access to option properties
 * and methods for handling variant options in product configurations.
 * 
 * Properties:
 * - id: Option ID
 * - name: Option name
 * - sort: Sort order
 * - color: Option color
 * - image: Option image
 * - has_image: Whether option has an image
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Variants
 */
class Option extends AbstractDrop
{

    use DropImageToArray;

    /**
     * @var ParameterOptionModel The option model instance
     */
    protected ParameterOptionModel $_option;

    /**
     * @var array|null The configuration cache
     */
    protected ?array $config = null;

    /**
     * Option constructor.
     * 
     * @param ParameterOptionModel $option The option model instance
     */
    public function __construct(ParameterOptionModel $option)
    {
        $this->_option = $option;
    }

    /**
     * Get the option ID
     * 
     * @return int The option ID
     */
    public function id(): int
    {
        return $this->_option->id;
    }

    /**
     * Get the option name
     * 
     * @return string The option name
     */
    public function name(): string
    {
        return $this->_option->name;
    }

    /**
     * Get the sort order
     * 
     * @return int The sort order
     */
    public function sort(): int
    {
        return $this->_option->sort;
    }

    /**
     * Get the option color
     * 
     * @return string|null The option color
     */
    public function color(): ?string
    {
        return $this->_option->color;
    }

    /**
     * Get the option image
     * 
     * @return \App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute The option image
     */
    public function image(): \App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute
    {
        $formatter = new UrlImageFormat($this->_option);
        return $formatter->getLiquidImage();
    }

    /**
     * Check if the option has an image
     * 
     * @return bool Whether the option has an image
     */
    public function has_image(): bool
    {
        return !!$this->_option->hasImage();
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     id: int,
     *     name: string,
     *     sort: int,
     *     color: string|null,
     *     has_image: bool,
     *     image: array<string, mixed>
     * } The option data
     * @throws Error
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'sort' => $this->sort(),
            'color' => $this->color(),
            'has_image' => $this->has_image(),
            'image' => $this->getImageArray(),
        ];
    }

}
