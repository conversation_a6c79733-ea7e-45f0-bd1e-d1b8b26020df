<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Variants;

use App\Models\Product\Parameter as ParameterModel;
use App\Models\Product\ParameterOption as ParameterOptionModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

/**
 * Variant Parameter Drop Class
 * 
 * This class represents a variant parameter in Liquid templates, providing access to parameter properties
 * and methods for handling variant parameters in product configurations.
 * 
 * Properties:
 * - id: Parameter ID
 * - name: Parameter name
 * - description: Parameter description
 * - type: Parameter type
 * - sort: Sort order
 * - options: Available options for this parameter
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop\Variants
 */
class Variant extends AbstractDrop
{
    /**
     * @var ParameterModel The parameter model instance
     */
    protected ParameterModel $_variant;

    /**
     * Variant constructor.
     * 
     * @param ParameterModel $variant The parameter model instance
     */
    public function __construct(ParameterModel $variant)
    {
        $this->_variant = $variant;
    }

    /**
     * Get the parameter ID
     * 
     * @return int The parameter ID
     */
    public function id(): int
    {
        return $this->_variant->id;
    }

    /**
     * Get the parameter name
     * 
     * @return string The parameter name
     */
    public function name(): string
    {
        return $this->_variant->name;
    }

    /**
     * Get the parameter description
     * 
     * @return string|null The parameter description
     */
    public function description(): ?string
    {
        return $this->_variant->description;
    }

    /**
     * Get the parameter type
     * 
     * @return string The parameter type
     */
    public function type(): string
    {
        return $this->_variant->type;
    }

    /**
     * Get the sort order
     * 
     * @return int The sort order
     */
    public function sort(): int
    {
        return $this->_variant->sort;
    }

    /**
     * Get the available options for this parameter
     * 
     * @return object[] The available options
     */
    public function options(): array
    {
        if (!$this->_variant->relationLoaded('options')) {
            return [];
        }

        return $this->_variant->options
            ->sortBy('sort', SORT_ASC)
            ->map(function (ParameterOptionModel $option): Option {
                $option->setRelation('parameter', $this->_variant);
                return new Option($option);
            })
            ->values()
            ->all();
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     id: int,
     *     name: string,
     *     description: string|null,
     *     type: string,
     *     sort: int,
     *     options: object[]
     * } The parameter data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'description' => $this->description(),
            'type' => $this->type(),
            'sort' => $this->sort(),
            'options' => $this->options(),
        ];
    }
}
