<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

/**
 * Link Drop Class
 * 
 * This class provides a structured way to handle link data in Liquid templates.
 * It includes support for Shopify compatibility, accessibility, SEO, and analytics.
 * 
 * @property string $url The URL of the link
 * @property string $title The title/display text of the link
 * @property string $type The type of link (internal, external, etc.)
 * @property array $attributes Additional HTML attributes for the link
 * @property bool $active Whether the link is currently active
 * @property array $children Child links (for navigation menus)
 * @property array $analytics Analytics data for the link
 */
class Link extends AbstractDrop
{
    /**
     * @var string The URL of the link
     */
    protected string $url;

    /**
     * @var string The title/display text of the link
     */
    protected string $title;

    /**
     * @var string The type of link (internal, external, etc.)
     */
    protected string $type;

    /**
     * @var array<string, string> Additional HTML attributes for the link
     */
    protected array $attributes;

    /**
     * @var bool Whether the link is currently active
     */
    protected bool $active;

    /**
     * @var array<Link> Child links (for navigation menus)
     */
    protected array $children;

    /**
     * @var array<string, mixed> Analytics data for the link
     */
    protected array $analytics;

    /**
     * Create a new Link instance.
     *
     * @param string $url The URL of the link
     * @param string $title The title/display text of the link
     * @param string $type The type of link (internal, external, etc.)
     * @param array<string, string> $attributes Additional HTML attributes
     * @param bool $active Whether the link is currently active
     * @param array<Link> $children Child links
     * @param array<string, mixed> $analytics Analytics data
     */
    public function __construct(
        string $url,
        string $title,
        string $type = 'internal',
        array $attributes = [],
        bool $active = false,
        array $children = [],
        array $analytics = []
    ) {
        $this->url = $url;
        $this->title = $title;
        $this->type = $type;
        $this->attributes = $attributes;
        $this->active = $active;
        $this->children = $children;
        $this->analytics = $analytics;
    }

    /**
     * Get the URL of the link.
     *
     * @return string
     */
    public function url(): string
    {
        return $this->url;
    }

    /**
     * Get the title/display text of the link.
     *
     * @return string
     */
    public function title(): string
    {
        return $this->title;
    }

    /**
     * Get the type of link.
     *
     * @return string
     */
    public function type(): string
    {
        return $this->type;
    }

    /**
     * Get additional HTML attributes.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return $this->attributes;
    }

    /**
     * Check if the link is currently active.
     *
     * @return bool
     */
    public function active(): bool
    {
        return $this->active;
    }

    /**
     * Get child links.
     *
     * @return array<Link>
     */
    public function children(): array
    {
        return $this->children;
    }

    /**
     * Get analytics data.
     *
     * @return array<string, mixed>
     */
    public function analytics(): array
    {
        return $this->analytics;
    }

    /**
     * Get accessibility attributes for the link.
     *
     * @return array<string, string>
     */
    public function accessibility(): array
    {
        return [
            'role' => 'link',
            'aria-label' => $this->title,
            'aria-current' => $this->active ? 'page' : null,
        ];
    }

    /**
     * Get structured data for SEO.
     *
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'WebPage',
            'url' => $this->url,
            'name' => $this->title,
            'isPartOf' => [
                '@type' => 'WebSite',
                'url' => config('app.url'),
            ],
        ];
    }

    /**
     * Convert the link to a Shopify-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toShopifyArray(): array
    {
        return [
            'url' => $this->url,
            'title' => $this->title,
            'active' => $this->active,
            'current' => $this->active,
            'handle' => str_slug($this->title),
            'type' => $this->type,
            'links' => array_map(fn($child) => $child->toShopifyArray(), $this->children),
        ];
    }

    /**
     * Convert the link to an SEO-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toSeoArray(): array
    {
        return [
            'url' => $this->url,
            'title' => $this->title,
            'type' => $this->type,
            'active' => $this->active,
            'structured_data' => $this->structured_data(),
        ];
    }

    /**
     * Convert the link to an analytics-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toAnalyticsArray(): array
    {
        return array_merge($this->analytics, [
            'url' => $this->url,
            'title' => $this->title,
            'type' => $this->type,
            'active' => $this->active,
            'children_count' => count($this->children),
        ]);
    }

    /**
     * Convert the link to an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'url' => $this->url,
            'title' => $this->title,
            'type' => $this->type,
            'attributes' => $this->attributes,
            'active' => $this->active,
            'children' => array_map(fn($child) => $child->toArray(), $this->children),
            'analytics' => $this->analytics,
            'accessibility' => $this->accessibility(),
            'structured_data' => $this->structured_data(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics_data' => $this->toAnalyticsArray(),
        ];
    }
} 