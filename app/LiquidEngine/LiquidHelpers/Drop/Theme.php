<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Models\Gate\Template;
use Illuminate\Support\Facades\Config;

/**
 * Theme Drop Class
 * 
 * This class provides a structured way to handle theme data in Liquid templates.
 * It includes support for Shopify compatibility, accessibility, SEO, and analytics.
 * 
 * @property int|null $id The theme ID
 * @property string|null $name The theme name
 * @property string|null $handle The theme handle/identifier
 * @property string|null $role The theme role (main, unpublished)
 * @property bool $previewable Whether the theme is previewable
 * @property array $settings Theme settings
 * @property array $analytics Analytics data for the theme
 */
class Theme extends AbstractDrop
{
    /**
     * @var array<string, mixed> Theme data
     */
    protected array $_data = [];

    /**
     * @var Template|null The template model
     */
    protected ?Template $_template = null;

    /**
     * Get the theme ID.
     *
     * @return int|null
     */
    public function id(): ?int
    {
        return $this->getTemplate()?->id;
    }

    /**
     * Get the theme name.
     *
     * @return string|null
     */
    public function name(): ?string
    {
        return $this->getTemplate()?->name;
    }

    /**
     * Get the theme handle/identifier.
     *
     * @return string|null
     */
    public function handle(): ?string
    {
        return $this->getTemplate()?->mapping;
    }

    /**
     * Get the theme role.
     *
     * @return string|null
     */
    public function role(): ?string
    {
        return $this->getTemplate()?->is_active ? 'main' : 'unpublished';
    }

    /**
     * Check if the theme is previewable.
     *
     * @return bool
     */
    public function previewable(): bool
    {
        return (bool) $this->getTemplate()?->is_active;
    }

    /**
     * Get theme settings.
     *
     * @return array<string, mixed>
     */
    public function settings(): array
    {
        return $this->getTemplate()?->settings ?? [];
    }

    /**
     * Get analytics data.
     *
     * @return array<string, mixed>
     */
    public function analytics(): array
    {
        return $this->_data['analytics'] ?? [];
    }

    /**
     * Get accessibility attributes for the theme.
     *
     * @return array<string, string>
     */
    public function accessibility(): array
    {
        return [
            'role' => 'theme',
            'aria-label' => $this->name() ?? 'Theme',
        ];
    }

    /**
     * Get structured data for SEO.
     *
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'WebPage',
            'mainEntity' => [
                '@type' => 'WebSite',
                'name' => $this->name(),
                'url' => Config::get('app.url'),
                'description' => 'Theme: ' . ($this->name() ?? ''),
            ],
        ];
    }

    /**
     * Convert the theme to a Shopify-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toShopifyArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'handle' => $this->handle(),
            'role' => $this->role(),
            'previewable' => $this->previewable(),
            'settings' => $this->settings(),
        ];
    }

    /**
     * Convert the theme to an SEO-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toSeoArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'handle' => $this->handle(),
            'role' => $this->role(),
            'structured_data' => $this->structured_data(),
        ];
    }

    /**
     * Convert the theme to an analytics-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toAnalyticsArray(): array
    {
        return array_merge($this->analytics(), [
            'id' => $this->id(),
            'name' => $this->name(),
            'handle' => $this->handle(),
            'role' => $this->role(),
            'previewable' => $this->previewable(),
            'settings_count' => count($this->settings()),
        ]);
    }

    /**
     * Convert the theme to an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'handle' => $this->handle(),
            'role' => $this->role(),
            'previewable' => $this->previewable(),
            'settings' => $this->settings(),
            'analytics' => $this->analytics(),
            'accessibility' => $this->accessibility(),
            'structured_data' => $this->structured_data(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics_data' => $this->toAnalyticsArray(),
        ];
    }

    /**
     * Get the template model.
     *
     * @return Template|null
     */
    protected function getTemplate(): ?Template
    {
        if (is_null($this->_template)) {
            $this->_template = Template::whereMapping(site('template'))->first();
        }

        return $this->_template;
    }
}
