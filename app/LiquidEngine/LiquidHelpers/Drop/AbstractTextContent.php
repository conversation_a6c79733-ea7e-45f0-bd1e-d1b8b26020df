<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Database\Eloquent\Model;

/**
 * AbstractTextContent Drop Class
 * 
 * This abstract class provides a base implementation for text content drops in Liquid templates.
 * It handles common functionality for pages and other text-based content, providing access to
 * basic properties like ID, name, and content. Classes extending this abstract class must
 * implement their own specific functionality while inheriting these common text content features.
 * 
 * The class wraps an Eloquent Model instance to provide a clean interface for Liquid templates,
 * abstracting away the database layer and providing consistent access to text content properties.
 * 
 * Properties:
 * - id: Content ID (int)
 * - name: Content name (string)
 * - content: Content text (string)
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
abstract class AbstractTextContent extends AbstractDrop
{
    /**
     * The underlying page model instance.
     * Contains the raw content data from the database.
     * Must be an Eloquent Model with 'name' and 'content' attributes.
     * 
     * @var Model
     */
    protected Model $_page;

    /**
     * AbstractTextContent constructor.
     * Initializes the drop with a Model instance that contains text content.
     * 
     * @param Model $page The page model instance
     */
    public function __construct(Model $page)
    {
        $this->_page = $page;
    }

    /**
     * Get the content ID.
     * Returns the primary key of the underlying model.
     * 
     * @return int The content ID
     */
    public function id(): int
    {
        return $this->_page->getKey();
    }

    /**
     * Get the content name.
     * Returns the name attribute from the underlying model.
     * 
     * @return string The content name
     */
    public function name(): string
    {
        return $this->_page->getAttribute('name');
    }

    /**
     * Get the content text.
     * Returns the content attribute from the underlying model.
     * 
     * @return string The content text
     */
    public function content(): string
    {
        return $this->_page->getAttribute('content');
    }

    /**
     * Convert the content to an array.
     * Returns an array representation of the content with all its properties.
     * This method is used by Liquid templates to access the content data.
     * 
     * @return array{
     *     id: int,
     *     name: string,
     *     content: string
     * } The content data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'content' => $this->content(),
        ];
    }
}
