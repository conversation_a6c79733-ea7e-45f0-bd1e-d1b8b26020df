<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Exceptions\Error;
use App\Models\Product\ParameterOption;
use App\Models\Product\ProductFiles;
use App\Models\Product\ProductTabs;
use App\Models\Product\Quantity;
use App\Models\Collections\Collections;
use Illuminate\Support\Collection as CollectionAlias;
use App\LiquidEngine\LiquidHelpers\Drop\Product\File;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Variant;
use App\LiquidEngine\LiquidHelpers\Drop\Variants\Option;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Quantity as DropQuantity;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class ProductDetails extends Product
{

    protected $variants_groups;

    protected $quantity_by_store;

    protected $collections;

    /**
     * @return string
     */
    public function description_name()
    {
        return $this->_product->description_title;
    }

    /**
     * @return string
     */
    public function description(): string
    {
        return $this->_product->description;
    }

    /**
     * @return string
     */
    public function seo_title()
    {
        return $this->_product->seo_title ?: strip_tags($this->name());
    }

    /**
     * @return string
     */
    public function seo_description()
    {
        return $this->_product->seo_description;
    }

    /**
     * @return array
     */
    public function variants_groups()
    {
        return $this->getParametersFromVariantsMultiple();
    }

    /**
     * @return Variant|null
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function selected_variant(): ?Variant
    {
        if ($this->_selected_variant !== false) {
            return $this->_selected_variant;
        }

        if (is_numeric($variant_id = request()->get('variant')) && $this->_product->variants->firstWhere('id', $variant_id)) {
            return $this->_selected_variant = $variant_id;
        }

        return $this->_selected_variant = null;
    }

    /**
     * @return array
     */
    public function tabs()
    {
        $records = [];
        if ($this->_product->relationLoaded('tabs')) {
            $records = $this->_product->tabs->map(function (ProductTabs $tab): array {
                return [
                    'name' => $tab->name,
                    'content' => $tab->description,
                ];
            })->all();
        }

        return $records;
    }

    /**
     * @return array
     */
    public function collections()
    {
        if (!is_null($this->collections)) {
            return $this->collections;
        }

        $records = [];
        if ($this->_product->relationLoaded('collections') && $this->_product->collections->count()) {
            $records = $this->_product->collections->map(function (Collections $collection): \App\LiquidEngine\LiquidHelpers\Drop\Collection {
                return new Collection($collection);
            })->all();
        }

        return $this->collections = $records;
    }

    /**
     * @return array
     */
    public function files()
    {
        $files = [];
        if ($this->_product->relationLoaded('publicFiles') && $this->_product->publicFiles->count()) {
            $files = $this->_product->publicFiles->map(function (ProductFiles $file): \App\LiquidEngine\LiquidHelpers\Drop\Product\File {
                return new File($file, $this->_product->url_handle);
            })->all();
        }

        return $files;
    }

    public function quantity_by_store()
    {
        if (!is_null($this->quantity_by_store)) {
            return $this->quantity_by_store;
        }

        $quantities = [];
        if ($this->_product->relationLoaded('quantity_per_shops')) {
            $quantities = $this->_product->quantity_per_shops->sortBy(function (Quantity $quantity): string {
                return sprintf('%s#%s', $quantity->shop->city, $quantity->shop->title);
            })->groupBy('shop_id')->map(function (CollectionAlias $collection): \App\LiquidEngine\LiquidHelpers\Drop\Product\Quantity {
                $qty = new Quantity([
                    'qty' => $collection->sum('qty')
                ]);
                $qty->setRelation('shop', $collection->first()->shop);

                return new DropQuantity($qty);
            })->values()->all();
        }

        return $this->quantity_by_store = $quantities;
    }

    public function images()
    {
        $images = [];
        if ($this->_product->relationLoaded('images')) {
            $images = $this->_product->images->map(function ($image) {
                $formatter = new UrlImageFormat($image);
                return $formatter->getLiquidImage();
            })->all();
        }

        return $images;
    }

    // START: Shopify-compatible methods for Product Details
    // These methods provide compatibility with Shopify's product detail patterns

    /**
     * Get media items for the product (alias for images)
     * Shopify compatible: product.media
     */
    public function media(): array
    {
        return $this->images();
    }

    /**
     * Get featured media for the product (first image)
     * Shopify compatible: product.featured_media
     */
    public function featured_media()
    {
        $images = $this->images();
        return !empty($images) ? $images[0] : null;
    }

    /**
     * Get product options array
     * Shopify compatible: product.options
     */
    public function options(): array
    {
        $options = [];
        $variantGroups = $this->variants_groups();

        foreach ($variantGroups as $group) {
            $options[] = [
                'name' => $group['group'],
                'type' => $group['type'],
                'values' => array_column($group['values'], 'name')
            ];
        }

        return $options;
    }

    /**
     * Get options by name
     * Shopify compatible: product.options_by_name
     */
    public function options_by_name(): array
    {
        $optionsByName = [];
        $options = $this->options();

        foreach ($options as $option) {
            $optionsByName[$option['name']] = [
                'name' => $option['name'],
                'values' => $option['values']
            ];
        }

        return $optionsByName;
    }

    /**
     * Get options with values
     * Shopify compatible: product.options_with_values
     */
    public function options_with_values(): array
    {
        return $this->options();
    }

    /**
     * Get product form id
     * Shopify compatible: product.form_id
     */
    public function form_id(): string
    {
        return 'product-form-' . $this->id();
    }

    /**
     * Get selected or first available variant
     * Shopify compatible: product.selected_or_first_available_variant
     */
    public function selected_or_first_available_variant(): Variant
    {
        $selectedVariant = $this->selected_variant();
        if ($selectedVariant) {
            $variants = $this->variants();
            foreach ($variants as $variant) {
                if ($variant->id() == $selectedVariant) {
                    return $variant;
                }
            }
        }

        // Return first available variant
        $variants = $this->variants();
        foreach ($variants as $variant) {
            if ($variant->available()) {
                return $variant;
            }
        }

        // Return first variant if no available variants
        return !empty($variants) ? $variants[0] : $this->variant();
    }

    /**
     * Get first available variant
     * Shopify compatible: product.first_available_variant
     */
    public function first_available_variant()
    {
        $variants = $this->variants();
        foreach ($variants as $variant) {
            if ($variant->available()) {
                return $variant;
            }
        }

        return null;
    }

    /**
     * Check if product has only default variant
     * Shopify compatible: product.has_only_default_variant
     */
    public function has_only_default_variant(): bool
    {
        $variants = $this->variants();
        return count($variants) === 1;
    }

    /**
     * Get product attachments/files
     * Shopify compatible: product.attachments
     */
    public function attachments(): array
    {
        return $this->files();
    }

    /**
     * Get product content (alias for description)
     * Shopify compatible: product.content
     */
    public function content(): string
    {
        return $this->description() ?? '';
    }

    /**
     * Get product excerpt (truncated description)
     * Shopify compatible: product.excerpt
     */
    public function excerpt(): string
    {
        $description = $this->description();
        if (strlen($description) > 150) {
            return substr($description, 0, 150) . '...';
        }
        return $description;
    }

    /**
     * Get SEO meta description
     * Shopify compatible: product.meta_description
     */
    public function meta_description(): ?string
    {
        return $this->seo_description();
    }

    /**
     * Get product price range formatted
     * Shopify compatible: product.price_range
     */
    public function price_range(): string
    {
        $minPrice = $this->min_price_with_discounted();
        $maxPrice = $this->max_price_with_discounted();

        if ($minPrice === $maxPrice) {
            return money($minPrice);
        }

        return money($minPrice) . ' - ' . money($maxPrice);
    }

    /**
     * Get variant options (all option combinations)
     * Shopify compatible: product.variant_options
     */
    public function variant_options(): array
    {
        $options = [];
        $variants = $this->variants();

        foreach ($variants as $variant) {
            $options[] = [
                'option1' => $variant->v1(),
                'option2' => $variant->v2(),
                'option3' => $variant->v3(),
            ];
        }

        return array_unique($options, SORT_REGULAR);
    }

    /**
     * Get current variant (alias for selected_variant)
     * Shopify compatible: product.current_variant
     */
    public function current_variant()
    {
        return $this->selected_or_first_available_variant();
    }

    /**
     * Get product inventory tracking
     * Shopify compatible: product.requires_selling_plan
     */
    public function requires_selling_plan(): bool
    {
        return false; // CloudCart doesn't have selling plans by default
    }

    /**
     * Get product gift card status
     * Shopify compatible: product.gift_card
     */
    public function gift_card(): bool
    {
        return $this->type() === 'gift_card' || $this->type() === 'gift-card';
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     * @throws Error
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'description_name' => $this->description_name(),
            'description' => $this->description(),
            'seo_title' => $this->seo_title(),
            'seo_description' => $this->seo_description(),
            'variants_groups' => $this->variants_groups(),
            'tabs' => $this->tabs(),
            'collections' => $this->collections(),
            'files' => $this->files(),
            'quantity_by_store' => $this->quantity_by_store(),
            'images' => array_map(function ($image): array {
                $formatter = new UrlImageFormat($image);
                return $formatter->imagesFormatArray();
            }, $this->images()),
            // Shopify-compatible fields
            'media' => $this->media(),
            'featured_media' => $this->featured_media(),
            'options' => $this->options(),
            'options_by_name' => $this->options_by_name(),
            'options_with_values' => $this->options_with_values(),
            'form_id' => $this->form_id(),
            'selected_or_first_available_variant' => $this->selected_or_first_available_variant(),
            'first_available_variant' => $this->first_available_variant(),
            'has_only_default_variant' => $this->has_only_default_variant(),
            'attachments' => $this->attachments(),
            'content' => $this->content(),
            'excerpt' => $this->excerpt(),
            'meta_description' => $this->meta_description(),
            'price_range' => $this->price_range(),
            'variant_options' => $this->variant_options(),
            'current_variant' => $this->current_variant(),
            'requires_selling_plan' => $this->requires_selling_plan(),
            'gift_card' => $this->gift_card(),
        ]);
    }

    /**
     * @return array
     */
    protected function getParametersFromVariantsMultiple()
    {
        if (!($total = $this->total_variants()) || !$this->parameter1()) {
            return [];
        }

        if (!is_null($this->variants_groups)) {
            return $this->variants_groups;
        }

        /**
         * @todo remove all string keys for parameters (leave only id keys)
         */

        $parameters_sort = [];
        $sort_order = [];
        $sort_order_options = [];

        for ($i = 1; $i <= $total; $i++) {
            $key = call_user_func([$this, 'p' . $i]);
            /** @var Variants\Variant $parameter */
            $parameter = call_user_func([$this, 'parameter' . $i]);
            $parameters_sort[$key] = [
                'group' => $key,
                'type' => $parameter->type(),
                'key' => 'p' . $i,
                'values' => [],
                'sort' => [],
            ];
            $sort_order[$key] = $parameter->sort();
        }

        $no_image = new Option(new ParameterOption());
        /** @var Variant[] $variants */
        $variants = $this->variants();
        foreach ($variants as $variant) {
            for ($i = 1; $i <= $total; $i++) {
                $key = call_user_func([$variant, 'p' . $i]);
                $subkey = call_user_func([$variant, 'v' . $i]);
                /** @var Variants\Variant $parameter */
                if (empty($parameter = call_user_func([$variant, 'v' . $i . '_parameter']))) {
                    continue;
                }

                /** @var Option $option */
                $option = call_user_func([$variant, 'v' . $i . '_option']);
                if ($parameter->type() === 'image' && empty($parameters_sort[$key]['values'][$subkey])) {
                    if ($option->has_image()) {
                        $image = $option->image();
                    } elseif ($variant->has_image()) {
                        $image = $variant->image();
                    } else {
                        $image = $no_image->image();
                    }
                }

                $parameters_sort[$key]['values'][$subkey] = [
                    'name' => $option->name(),
                    'image' => $image ?? $no_image->image(),
                    'color' => $option->color(),
                    'key' => 'v' . $i,
                ];
                $parameters_sort[$key]['sort'][$subkey] = $option->sort();
                $sort_order_options[$key][$subkey] = $option->sort();
            }
        }

        array_multisort($sort_order, $parameters_sort, SORT_ASC, SORT_NUMERIC);
        $parameters = [];
        foreach ($parameters_sort as $group => $options) {
            $values = $options['values'];
            if (!empty($sort_order_options[$group]) && is_array($sort_order_options[$group])) {
                array_multisort($sort_order_options[$group], $values, SORT_ASC, SORT_NUMERIC);
            }

            $parameters[$group]['values'] = array_values($values);
            $parameters[$group]['type'] = $options['type'];
            $parameters[$group]['key'] = $options['key'];
            $parameters[$group]['group'] = $options['group'];
            asort($options['sort'], SORT_NUMERIC);
            $newSort = 0;
            $parameters[$group]['sort'] = array_map(function () use (&$newSort): int {
                return $newSort++;
            }, $options['sort']);
        }

        $schema_2d = null;
        $schema_2d_groups = null;
        $first_schema = null;
        foreach ($parameters as $key => $parameter) {
            if ($parameter['type'] == '2d') {
                if (!isset($schema_2d['X'])) {
                    $schema_2d['X'] = $parameter;
                    $schema_2d_groups['X'] = $first_schema = $key;
                } elseif (!isset($schema_2d['Y'])) {
                    $schema_2d['Y'] = $parameter;
                    $schema_2d_groups['Y'] = $key;
                    unset($parameters[$key]);
                }
            }
        }

        if ($first_schema) {
            if (!empty($schema_2d['Y'])) {
                unset($parameters[$first_schema]);
                $parameters[$first_schema] = [
                    'values' => $schema_2d,
                    'group' => $first_schema,
                    'type' => '2d',
                    'sort' => ['X' => $schema_2d['X']['sort'], 'Y' => $schema_2d['Y']['sort']],
                    'keys' => ['X' => $schema_2d['X']['key'], 'Y' => $schema_2d['Y']['key']],
                    'groups' => $schema_2d_groups
                ];
                unset($schema_2d, $first_schema, $schema_2d_groups);
            } else {
                $parameters[$first_schema]['type'] = 'select';
            }
        }

        foreach ($parameters as $key => $parameter) {
            if ($parameter['type'] == 'numeric_alpha') {
                $new_parameter = $parameter;
                $new_values = [];
                $sort = $parameter['sort'];
                foreach ($parameter['values'] as $value) {
                    if (preg_match('~^([0-9]{1,})([a-z]{1,})$~i', $value['name'], $match)) {
                        $new_values['Y'][(string)$match[1]] = [
                            'name' => $match[1],
                            'key' => $value['key'],
                            'image' => null,
                            'color' => null,
                            'sort' => $sort[$match[0]]
                        ];
                        $new_values['X'][(string)$match[2]] = [
                            'name' => $match[2],
                            'key' => $value['key'],
                            'image' => null,
                            'color' => null,
                            'sort' => $sort[$match[0]]
                        ];
                    }
                }

                if ($new_values) {
                    $new_values['X'] = collect($new_values['X'])->sortBy('name', SORT_NATURAL);
                    $new_values['Y'] = collect($new_values['Y'])->sortBy('name', SORT_NUMERIC);

                    $new_sort['X'] = $new_values['X']->pluck('sort', 'name')->all();
                    $new_sort['Y'] = $new_values['Y']->pluck('sort', 'name')->all();
                    unset($parameters[$key]);
                    $new_parameter['sort'] = $new_sort;
                    $new_parameter['values'] = collect($new_values)->toArray();
                    $parameters[$key] = $new_parameter;
                    unset($new_parameter);
                } else {
                    unset($parameters[$key]);
                }
            }
        }

        return $this->variants_groups = $parameters;
    }

}
