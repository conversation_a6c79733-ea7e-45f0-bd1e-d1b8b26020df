<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use DateTime;
use DateTimeZone;
use Exception;

/**
 * Timezone Drop Class
 * 
 * This class provides a structured way to handle timezone data in Liquid templates.
 * It includes support for Shopify compatibility, accessibility, SEO, and analytics.
 * 
 * @property string $timezone The timezone name
 * @property int $utc_offset The UTC offset in seconds
 * @property int|null $type The timezone type
 * @property array $analytics Analytics data for the timezone
 */
class Timezone extends AbstractDrop
{
    /**
     * @var DateTimeZone The timezone instance
     */
    protected DateTimeZone $_timezone;

    /**
     * @var array<string, mixed> Analytics data
     */
    protected array $_analytics = [];

    /**
     * Create a new Timezone instance.
     *
     * @param DateTimeZone $timezone The timezone instance
     * @param array<string, mixed> $analytics Analytics data
     */
    public function __construct(DateTimeZone $timezone, array $analytics = [])
    {
        $this->_timezone = $timezone;
        $this->_analytics = $analytics;
    }

    /**
     * Get the timezone name.
     *
     * @return string
     */
    public function timezone(): string
    {
        return $this->_timezone->getName();
    }

    /**
     * Get the UTC offset in seconds.
     *
     * @return int
     * @throws Exception
     */
    public function utc_offset(): int
    {
        return $this->_timezone->getOffset(new DateTime("now", new DateTimeZone("UTC")));
    }

    /**
     * Get the timezone type.
     *
     * @return int|null
     */
    public function type(): ?int
    {
        return ((array)$this->_timezone)['timezone_type'] ?? null;
    }

    /**
     * Get the timezone abbreviation.
     *
     * @return string
     * @throws Exception
     */
    public function abbreviation(): string
    {
        $date = new DateTime("now", $this->_timezone);
        return $date->format('T');
    }

    /**
     * Get the timezone location.
     *
     * @return array<string, float>|null
     */
    public function location(): ?array
    {
        $location = $this->_timezone->getLocation();
        if ($location === false) {
            return null;
        }

        return [
            'latitude' => $location['latitude'],
            'longitude' => $location['longitude'],
            'country_code' => $location['country_code'],
            'comments' => $location['comments'],
        ];
    }

    /**
     * Get analytics data.
     *
     * @return array<string, mixed>
     */
    public function analytics(): array
    {
        return $this->_analytics;
    }

    /**
     * Get accessibility attributes for the timezone.
     *
     * @return array<string, string>
     */
    public function accessibility(): array
    {
        return [
            'role' => 'timezone',
            'aria-label' => $this->timezone(),
        ];
    }

    /**
     * Get structured data for SEO.
     *
     * @return array<string, mixed>
     * @throws Exception
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'WebPage',
            'mainEntity' => [
                '@type' => 'TimeZone',
                'name' => $this->timezone(),
                'utcOffset' => $this->utc_offset(),
                'abbreviation' => $this->abbreviation(),
                'location' => $this->location(),
            ],
        ];
    }

    /**
     * Convert the timezone to a Shopify-compatible array.
     *
     * @return array<string, mixed>
     * @throws Exception
     */
    public function toShopifyArray(): array
    {
        return [
            'timezone' => $this->timezone(),
            'utc_offset' => $this->utc_offset(),
            'type' => $this->type(),
            'abbreviation' => $this->abbreviation(),
            'location' => $this->location(),
        ];
    }

    /**
     * Convert the timezone to an SEO-compatible array.
     *
     * @return array<string, mixed>
     * @throws Exception
     */
    public function toSeoArray(): array
    {
        return [
            'timezone' => $this->timezone(),
            'utc_offset' => $this->utc_offset(),
            'abbreviation' => $this->abbreviation(),
            'location' => $this->location(),
            'structured_data' => $this->structured_data(),
        ];
    }

    /**
     * Convert the timezone to an analytics-compatible array.
     *
     * @return array<string, mixed>
     * @throws Exception
     */
    public function toAnalyticsArray(): array
    {
        return array_merge($this->_analytics, [
            'timezone' => $this->timezone(),
            'utc_offset' => $this->utc_offset(),
            'abbreviation' => $this->abbreviation(),
            'has_location' => $this->location() !== null,
        ]);
    }

    /**
     * Convert the timezone to an array.
     *
     * @return array<string, mixed>
     * @throws Exception
     */
    public function toArray(): array
    {
        return [
            'timezone' => $this->timezone(),
            'utc_offset' => $this->utc_offset(),
            'type' => $this->type(),
            'abbreviation' => $this->abbreviation(),
            'location' => $this->location(),
            'analytics' => $this->_analytics,
            'accessibility' => $this->accessibility(),
            'structured_data' => $this->structured_data(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics_data' => $this->toAnalyticsArray(),
        ];
    }
}
