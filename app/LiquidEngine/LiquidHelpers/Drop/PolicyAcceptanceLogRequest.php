<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Modules\Apps\Others\Gdpr\Models\Requests;

/**
 * PolicyAcceptanceLogRequest Drop Class
 * 
 * This class represents a policy acceptance log request in Liquid templates,
 * providing access to request details such as ID, name, creation date,
 * processing date, and request type. It wraps the underlying Requests model
 * to provide a clean interface for Liquid templates.
 * 
 * Properties:
 * - id: Request ID (int)
 * - name: Request name (string)
 * - created: Creation date (Date|null)
 * - processed: Processing date (Date|null)
 * - type: Request type (string)
 * 
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class PolicyAcceptanceLogRequest extends AbstractDrop
{
    /**
     * The underlying request model instance.
     * Contains the raw request data from the database.
     * 
     * @var Requests
     */
    protected Requests $_request;

    /**
     * PolicyAcceptanceLogRequest constructor.
     * Initializes the drop with a Requests model instance.
     * 
     * @param Requests $request The request model instance
     */
    public function __construct(Requests $request)
    {
        $this->_request = $request;
    }

    /**
     * Get the request ID.
     * Returns the unique identifier for this request.
     * 
     * @return int The request ID
     */
    public function id(): int
    {
        return $this->_request->id;
    }

    /**
     * Get the request name.
     * Returns the human-readable name of the request.
     * 
     * @return string The request name
     */
    public function name(): string
    {
        return $this->_request->name;
    }

    /**
     * Get the creation date.
     * Returns a Date object representing when the request was created,
     * or null if the creation date is not set.
     * 
     * @return Date|null The creation date or null if not set
     */
    public function created(): ?Date
    {
        return $this->_request->created_at ? new Date($this->_request->created_at) : null;
    }

    /**
     * Get the processing date.
     * Returns a Date object representing when the request was processed,
     * or null if the request has not been processed yet.
     * 
     * @return Date|null The processing date or null if not set
     */
    public function processed(): ?Date
    {
        return $this->_request->processed_at ? new Date($this->_request->processed_at) : null;
    }

    /**
     * Get the formatted request type.
     * Returns a human-readable string representing the type of request.
     * 
     * @return string The formatted request type
     */
    public function type(): string
    {
        return $this->_request->type_formatted;
    }

    /**
     * Convert the request to an array.
     * Returns an array representation of the request with all its properties.
     * Date objects are converted to their array representation.
     * 
     * @return array{
     *     id: int,
     *     name: string,
     *     created: array{
     *         timestamp: int,
     *         formatted: string,
     *         timezone: string
     *     }|null,
     *     processed: array{
     *         timestamp: int,
     *         formatted: string,
     *         timezone: string
     *     }|null,
     *     type: string
     * } The request data
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'created' => $this->created()?->toArray(),
            'processed' => $this->processed()?->toArray(),
            'type' => $this->type(),
        ];
    }
}
