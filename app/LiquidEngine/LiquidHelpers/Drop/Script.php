<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Support\Facades\Config;

/**
 * Script Drop Class
 * 
 * This class provides a structured way to handle JavaScript scripts in Liquid templates.
 * It includes support for Shopify compatibility, accessibility, SEO, and analytics.
 * 
 * @property string $src The source URL of the script
 * @property string $type The script type (e.g., text/javascript)
 * @property bool $async Whether the script should be loaded asynchronously
 * @property bool $defer Whether the script should be deferred
 * @property string $crossorigin The crossorigin attribute value
 * @property array $attributes Additional HTML attributes
 * @property array $dependencies Script dependencies
 * @property array $analytics Analytics data for the script
 */
class Script extends AbstractDrop
{
    /**
     * @var string The source URL of the script
     */
    protected string $src;

    /**
     * @var string The script type (e.g., text/javascript)
     */
    protected string $type;

    /**
     * @var bool Whether the script should be loaded asynchronously
     */
    protected bool $async;

    /**
     * @var bool Whether the script should be deferred
     */
    protected bool $defer;

    /**
     * @var string The crossorigin attribute value
     */
    protected string $crossorigin;

    /**
     * @var array<string, string> Additional HTML attributes
     */
    protected array $attributes;

    /**
     * @var array<string> Script dependencies
     */
    protected array $dependencies;

    /**
     * @var array<string, mixed> Analytics data for the script
     */
    protected array $analytics;

    /**
     * Create a new Script instance.
     *
     * @param string $src The source URL of the script
     * @param string $type The script type
     * @param bool $async Whether the script should be loaded asynchronously
     * @param bool $defer Whether the script should be deferred
     * @param string $crossorigin The crossorigin attribute value
     * @param array<string, string> $attributes Additional HTML attributes
     * @param array<string> $dependencies Script dependencies
     * @param array<string, mixed> $analytics Analytics data
     */
    public function __construct(
        string $src,
        string $type = 'text/javascript',
        bool $async = false,
        bool $defer = false,
        string $crossorigin = '',
        array $attributes = [],
        array $dependencies = [],
        array $analytics = []
    ) {
        $this->src = $src;
        $this->type = $type;
        $this->async = $async;
        $this->defer = $defer;
        $this->crossorigin = $crossorigin;
        $this->attributes = $attributes;
        $this->dependencies = $dependencies;
        $this->analytics = $analytics;
    }

    /**
     * Get the source URL of the script.
     *
     * @return string
     */
    public function src(): string
    {
        return $this->src;
    }

    /**
     * Get the script type.
     *
     * @return string
     */
    public function type(): string
    {
        return $this->type;
    }

    /**
     * Check if the script should be loaded asynchronously.
     *
     * @return bool
     */
    public function async(): bool
    {
        return $this->async;
    }

    /**
     * Check if the script should be deferred.
     *
     * @return bool
     */
    public function defer(): bool
    {
        return $this->defer;
    }

    /**
     * Get the crossorigin attribute value.
     *
     * @return string
     */
    public function crossorigin(): string
    {
        return $this->crossorigin;
    }

    /**
     * Get additional HTML attributes.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return $this->attributes;
    }

    /**
     * Get script dependencies.
     *
     * @return array<string>
     */
    public function dependencies(): array
    {
        return $this->dependencies;
    }

    /**
     * Get analytics data.
     *
     * @return array<string, mixed>
     */
    public function analytics(): array
    {
        return $this->analytics;
    }

    /**
     * Get accessibility attributes for the script.
     *
     * @return array<string, string>
     */
    public function accessibility(): array
    {
        return [
            'role' => 'script',
            'aria-label' => 'JavaScript resource',
        ];
    }

    /**
     * Get structured data for SEO.
     *
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'WebPage',
            'mainEntity' => [
                '@type' => 'SoftwareApplication',
                'name' => basename($this->src),
                'applicationCategory' => 'JavaScript',
                'operatingSystem' => 'Any',
            ],
        ];
    }

    /**
     * Convert the script to a Shopify-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toShopifyArray(): array
    {
        return [
            'src' => $this->src,
            'type' => $this->type,
            'async' => $this->async,
            'defer' => $this->defer,
            'crossorigin' => $this->crossorigin,
            'attributes' => $this->attributes,
            'dependencies' => $this->dependencies,
        ];
    }

    /**
     * Convert the script to an SEO-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toSeoArray(): array
    {
        return [
            'src' => $this->src,
            'type' => $this->type,
            'async' => $this->async,
            'defer' => $this->defer,
            'crossorigin' => $this->crossorigin,
            'structured_data' => $this->structured_data(),
        ];
    }

    /**
     * Convert the script to an analytics-compatible array.
     *
     * @return array<string, mixed>
     */
    public function toAnalyticsArray(): array
    {
        return array_merge($this->analytics, [
            'src' => $this->src,
            'type' => $this->type,
            'async' => $this->async,
            'defer' => $this->defer,
            'dependencies_count' => count($this->dependencies),
        ]);
    }

    /**
     * Convert the script to an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'src' => $this->src,
            'type' => $this->type,
            'async' => $this->async,
            'defer' => $this->defer,
            'crossorigin' => $this->crossorigin,
            'attributes' => $this->attributes,
            'dependencies' => $this->dependencies,
            'analytics' => $this->analytics,
            'accessibility' => $this->accessibility(),
            'structured_data' => $this->structured_data(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics_data' => $this->toAnalyticsArray(),
        ];
    }
} 