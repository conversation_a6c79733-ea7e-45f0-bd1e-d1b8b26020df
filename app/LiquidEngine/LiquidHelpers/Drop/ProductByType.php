<?php

declare(strict_types=1);

/**
 * ProductByType Drop Class
 *
 * This class implements a factory pattern for returning the appropriate Drop class
 * (Product or Bundle) based on the product type. It is used to abstract
 * the instantiation logic for Liquid templates and ensure type safety.
 *
 * The class maintains a mapping of product types to their corresponding Drop classes,
 * defaulting to the simple product type if an unknown type is encountered.
 *
 * Usage:
 *   $drop = ProductByType::get($productModel);
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Models\Product\Product as ProductModel;

class ProductByType
{
    /**
     * Mapping of product types to Drop classes.
     * Maps product type constants to their corresponding Drop class implementations.
     * Unknown types will default to the simple product type.
     *
     * @var array<string, class-string<Product|Bundle>>
     */
    public const PRODUCT_TYPES = [
        ProductModel::TYPE_SIMPLE => Product::class,
        ProductModel::TYPE_DIGITAL => Product::class,
        ProductModel::TYPE_MULTIPLE => Product::class,
        ProductModel::TYPE_BUNDLE => Bundle::class,
    ];

    /**
     * ProductByType constructor (protected, static factory only).
     * Prevents direct instantiation of the factory class.
     */
    protected function __construct() {}

    /**
     * Get the appropriate Drop instance for the given product.
     * Returns a Product or Bundle instance based on the product type.
     * Falls back to the simple product type if the product type is unknown.
     *
     * @param ProductModel $product The product model instance
     * @return Product|Bundle The corresponding Drop instance
     * @throws \InvalidArgumentException If the product type is invalid
     */
    public static function get(ProductModel $product): Product|Bundle
    {
        $className = static::PRODUCT_TYPES[$product->type] ?? static::PRODUCT_TYPES[ProductModel::TYPE_SIMPLE];
        return new $className($product);
    }
}
