<?php

declare(strict_types=1);

/**
 * NavigationGroup Drop Class
 *
 * Represents a navigation group in the Liquid template system, providing access to navigation group properties,
 * links, SEO/analytics/accessibility data, and Shopify compatibility methods.
 *
 * @property-read \App\Models\StoreFront\NavigationGroups $navigation The underlying navigation group model
 */
namespace App\LiquidEngine\LiquidHelpers\Drop\StoreFront;

use App\Models\StoreFront\NavigationGroups;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

class NavigationGroup extends AbstractDrop
{
    /**
     * The underlying navigation group model instance.
     *
     * @var NavigationGroups
     */
    protected NavigationGroups $navigation;

    /**
     * Create a new NavigationGroup instance.
     *
     * @param NavigationGroups $navigation The navigation group model instance
     */
    public function __construct(NavigationGroups $navigation)
    {
        $this->navigation = $navigation;
    }

    /**
     * Get the navigation group name.
     *
     * @return string
     */
    public function name(): string
    {
        return (string) $this->navigation->name;
    }

    /**
     * Get the navigation group handle (mapping).
     *
     * @return string
     */
    public function handle(): string
    {
        return (string) $this->navigation->mapping;
    }

    /**
     * Get the navigation links as Navigation drop instances.
     *
     * @return array<Navigation>
     */
    public function links(): array
    {
        $links = [];
        if ($this->navigation->relationLoaded('links')) {
            $links = $this->navigation->links->mapInto(Navigation::class)->all();
        }
        return $links;
    }

    /**
     * Get the navigation group title (alias for name).
     *
     * @return string
     */
    public function title(): string
    {
        return $this->name();
    }

    /**
     * Get the navigation items (alias for links).
     *
     * @return array<Navigation>
     */
    public function items(): array
    {
        return $this->links();
    }

    /**
     * Get the meta title for SEO.
     *
     * @return string|null
     */
    public function meta_title(): ?string
    {
        return $this->navigation->meta_title ?? $this->name();
    }

    /**
     * Get the meta description for SEO.
     *
     * @return string|null
     */
    public function meta_description(): ?string
    {
        return $this->navigation->meta_description;
    }

    /**
     * Get structured data for SEO (JSON-LD).
     *
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'SiteNavigationElement',
            'name' => $this->name(),
            'handle' => $this->handle(),
            'items' => array_map(function($item) {
                return $item->structured_data();
            }, $this->items()),
        ];
    }

    /**
     * Get analytics data for the navigation group.
     *
     * @return array<string, mixed>
     */
    public function analytics(): array
    {
        return [
            'id' => $this->navigation->id,
            'name' => $this->name(),
            'type' => 'navigation_group',
            'items_count' => count($this->items()),
            'clicks' => $this->navigation->clicks ?? 0,
            'impressions' => $this->navigation->impressions ?? 0,
        ];
    }

    /**
     * Get accessibility attributes for the navigation group.
     *
     * @return array<string, mixed>
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => $this->navigation->aria_label ?? $this->name(),
            'role' => 'navigation',
            'aria_orientation' => $this->navigation->orientation ?? 'horizontal',
        ];
    }

    /**
     * Whether this navigation group is for mobile.
     *
     * @return bool
     */
    public function is_mobile(): bool
    {
        return (bool)($this->navigation->mobile ?? false);
    }

    /**
     * Whether this navigation group is for desktop.
     *
     * @return bool
     */
    public function is_desktop(): bool
    {
        return (bool)($this->navigation->desktop ?? true);
    }

    /**
     * Get the orientation of the navigation group.
     *
     * @return string|null
     */
    public function orientation(): ?string
    {
        return $this->navigation->orientation ?? 'horizontal';
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'handle' => $this->handle(),
            'links' => $this->links(),
            'title' => $this->title(),
            'items' => $this->items(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'structured_data' => $this->structured_data(),
            'analytics' => $this->analytics(),
            'accessibility' => $this->accessibility(),
            'is_mobile' => $this->is_mobile(),
            'is_desktop' => $this->is_desktop(),
            'orientation' => $this->orientation(),
        ];
    }
}
