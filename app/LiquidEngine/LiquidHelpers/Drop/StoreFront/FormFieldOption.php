<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\StoreFront;

use App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute;
use App\Models\Layout\FormFieldOptions;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;
use Illuminate\Support\Str;

/**
 * FormFieldOption Drop class for handling form field option data in Liquid templates.
 *
 * @property-read int $id The unique identifier of the option
 * @property-read string $name The display name of the option
 * @property-read bool $active Whether the option is currently active
 * @property-read LiquidImageAttribute $image The option's image
 * @property-read bool $has_image Whether the option has an image
 * @property-read string $value The value of the option
 * @property-read bool $selected Whether the option is selected
 * @property-read bool $disabled Whether the option is disabled
 * @property-read string $handle URL-friendly handle for the option
 * @property-read string|null $meta_title SEO meta title
 * @property-read string|null $meta_description SEO meta description
 * @property-read array $structured_data JSON-LD structured data
 * @property-read array $analytics Analytics data
 * @property-read array $accessibility Accessibility attributes
 * @property-read bool $is_mobile Whether the option is mobile-friendly
 * @property-read bool $is_desktop Whether the option is desktop-friendly
 * @property-read string $orientation Option orientation (vertical/horizontal)
 */
class FormFieldOption extends AbstractDrop
{
    use DropImageToArray;

    protected FormFieldOptions $field;

    protected bool $active;

    /**
     * FormFieldOption constructor.
     *
     * @param FormFieldOptions $field The form field option model instance
     * @param bool $active Whether the option is currently active
     */
    public function __construct(FormFieldOptions $field, bool $active = false)
    {
        $this->field = $field;
        $this->active = $active;
    }

    /**
     * Get the unique identifier of the option.
     *
     * @return int
     */
    public function id(): int
    {
        return $this->field->id;
    }

    /**
     * Get the display name of the option.
     *
     * @return string
     */
    public function name(): string
    {
        return $this->field->name;
    }

    /**
     * Check if the option is currently active.
     *
     * @return bool
     */
    public function active(): bool
    {
        return $this->active;
    }

    /**
     * Get the option's image.
     *
     * @return LiquidImageAttribute
     */
    public function image(): LiquidImageAttribute
    {
        $formatter = new UrlImageFormat($this->field);
        return $formatter->getLiquidImage();
    }

    /**
     * Check if the option has an image.
     *
     * @return bool
     */
    public function has_image(): bool
    {
        return (bool) $this->field->hasImage();
    }

    /**
     * Get the value of the option.
     *
     * @return string
     */
    public function value(): string
    {
        return $this->field->value ?? '';
    }

    /**
     * Check if the option is selected.
     *
     * @return bool
     */
    public function selected(): bool
    {
        return (bool) ($this->field->selected ?? false);
    }

    /**
     * Check if the option is disabled.
     *
     * @return bool
     */
    public function disabled(): bool
    {
        return (bool) ($this->field->disabled ?? false);
    }

    /**
     * Get the URL handle for the option.
     *
     * @return string
     */
    public function handle(): string
    {
        return Str::slug($this->name());
    }

    /**
     * Get the meta title for SEO.
     *
     * @return string|null
     */
    public function meta_title(): ?string
    {
        return $this->name();
    }

    /**
     * Get the meta description for SEO.
     *
     * @return string|null
     */
    public function meta_description(): ?string
    {
        return null;
    }

    /**
     * Get structured data in JSON-LD format.
     *
     * @return array{
     *     '@type': string,
     *     'name': string,
     *     'value': string
     * }
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'PropertyValue',
            'name' => $this->name(),
            'value' => $this->value()
        ];
    }

    /**
     * Get analytics data.
     *
     * @return array{
     *     'type': string,
     *     'name': string,
     *     'value': string
     * }
     */
    public function analytics(): array
    {
        return [
            'type' => 'form_field_option',
            'name' => $this->name(),
            'value' => $this->value()
        ];
    }

    /**
     * Get accessibility attributes.
     *
     * @return array{
     *     'role': string,
     *     'aria-label': string,
     *     'aria-selected': bool,
     *     'aria-disabled': bool
     * }
     */
    public function accessibility(): array
    {
        return [
            'role' => 'option',
            'aria-label' => $this->name(),
            'aria-selected' => $this->selected(),
            'aria-disabled' => $this->disabled()
        ];
    }

    /**
     * Check if the option is mobile-friendly.
     *
     * @return bool
     */
    public function is_mobile(): bool
    {
        return true;
    }

    /**
     * Check if the option is desktop-friendly.
     *
     * @return bool
     */
    public function is_desktop(): bool
    {
        return true;
    }

    /**
     * Get the orientation of the option.
     *
     * @return string
     */
    public function orientation(): string
    {
        return 'vertical';
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     'id': int,
     *     'name': string,
     *     'value': string,
     *     'selected': bool,
     *     'disabled': bool,
     *     'title': string,
     *     'handle': string,
     *     'meta_title': string|null,
     *     'meta_description': string|null,
     *     'structured_data': array,
     *     'analytics': array,
     *     'accessibility': array,
     *     'is_mobile': bool,
     *     'is_desktop': bool,
     *     'orientation': string
     * }
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'value' => $this->value(),
            'selected' => $this->selected(),
            'disabled' => $this->disabled(),
            'title' => $this->name(),
            'handle' => $this->handle(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'structured_data' => $this->structured_data(),
            'analytics' => $this->analytics(),
            'accessibility' => $this->accessibility(),
            'is_mobile' => $this->is_mobile(),
            'is_desktop' => $this->is_desktop(),
            'orientation' => $this->orientation()
        ];
    }
}
