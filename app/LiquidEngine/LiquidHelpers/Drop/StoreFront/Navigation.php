<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 9.4.2019 г.
 * Time: 16:24 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\StoreFront;

use App\Models\StoreFront\Navigations;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;
use Illuminate\Support\Str;

/**
 * Navigation Drop class for handling navigation menu data in Liquid templates.
 *
 * @property-read string $name The display name of the navigation item
 * @property-read string $tooltip The tooltip text for the navigation item
 * @property-read array $dropdown Child navigation items
 * @property-read string|null $addon Icon class for the navigation item
 * @property-read string|null $suffix Icon class after the navigation item
 * @property-read bool $active Whether the navigation item is active
 * @property-read string $href The URL of the navigation item
 * @property-read bool $divider Whether the navigation item is a divider
 * @property-read string $handle URL-friendly handle for the navigation item
 * @property-read string $title The title of the navigation item
 * @property-read string $url The URL of the navigation item
 * @property-read bool $current Whether the navigation item is current
 * @property-read array $items Child navigation items
 * @property-read string|null $icon Icon class for the navigation item
 * @property-read string|null $icon_after Icon class after the navigation item
 * @property-read bool $is_divider Whether the navigation item is a divider
 * @property-read string|null $meta_title SEO meta title
 * @property-read string|null $meta_description SEO meta description
 * @property-read array $structured_data JSON-LD structured data
 * @property-read array $analytics Analytics data
 * @property-read array $accessibility Accessibility attributes
 * @property-read bool $is_mobile Whether the navigation item is mobile-friendly
 * @property-read bool $is_desktop Whether the navigation item is desktop-friendly
 */
class Navigation extends AbstractDrop
{

    use DropImageToArray;

    protected Navigations $navigation;

    /**
     * Navigation constructor.
     *
     * @param Navigations $navigation The navigation model instance
     */
    public function __construct(Navigations $navigation)
    {
        $this->navigation = $navigation;
    }

    /**
     * Get the display name of the navigation item.
     *
     * @return string
     */
    public function name(): string
    {
        return $this->navigation->name;
    }

    /**
     * Get the tooltip text for the navigation item.
     *
     * @return string
     */
    public function tooltip(): string
    {
        return $this->navigation->title;
    }

    /**
     * Get child navigation items.
     *
     * @return array<self>
     */
    public function dropdown(): array
    {
        $children = [];
        if ($this->navigation->relationLoaded('children')) {
            $children = $this->navigation->children->map(function ($nav): self {
                return new self($nav);
            })->all();
        }

        return $children;
    }

    /**
     * Get the icon class for the navigation item.
     *
     * @return string|null
     */
    public function addon(): ?string
    {
        if ($this->navigation->addon) {
            return sprintf('<i class="%s"></i>', $this->navigation->addon);
        }

        return null;
    }

    /**
     * Get the icon class after the navigation item.
     *
     * @return string|null
     */
    public function suffix(): ?string
    {
        if ($this->navigation->suffix) {
            return sprintf('<i class="%s"></i>', $this->navigation->suffix);
        }

        return null;
    }

    /**
     * Check if the navigation item is active.
     *
     * @return bool
     */
    public function active(): bool
    {
        return (bool) $this->navigation->active;
    }

    /**
     * Get the URL of the navigation item.
     *
     * @return string
     */
    public function href(): string
    {
        return $this->navigation->link_formatted;
    }

    /**
     * Check if the navigation item is a divider.
     *
     * @return bool
     */
    public function divider(): bool
    {
        return (bool) $this->navigation->divider;
    }

    /**
     * Get the navigation item's image.
     *
     * @return \App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute
     */
    public function image()
    {
        $formatter = new UrlImageFormat($this->navigation);
        return $formatter->getLiquidImage();
    }

    /**
     * Check if the navigation item has an image.
     *
     * @return bool
     */
    public function has_image(): bool
    {
        return (bool) $this->navigation->hasImage();
    }

    /**
     * Get the URL handle for the navigation item.
     *
     * @return string
     */
    public function handle(): string
    {
        return $this->navigation->url_handle ?? Str::slug($this->name());
    }

    /**
     * Get the title of the navigation item.
     *
     * @return string
     */
    public function title(): string
    {
        return $this->tooltip();
    }

    /**
     * Get the URL of the navigation item.
     *
     * @return string
     */
    public function url(): string
    {
        return $this->href();
    }

    /**
     * Check if the navigation item is current.
     *
     * @return bool
     */
    public function current(): bool
    {
        return $this->active();
    }

    /**
     * Get child navigation items.
     *
     * @return array<self>
     */
    public function items(): array
    {
        return $this->dropdown();
    }

    /**
     * Get the icon class for the navigation item.
     *
     * @return string|null
     */
    public function icon(): ?string
    {
        return $this->addon();
    }

    /**
     * Get the icon class after the navigation item.
     *
     * @return string|null
     */
    public function icon_after(): ?string
    {
        return $this->suffix();
    }

    /**
     * Check if the navigation item is a divider.
     *
     * @return bool
     */
    public function is_divider(): bool
    {
        return $this->divider();
    }

    /**
     * Get the meta title for SEO.
     *
     * @return string|null
     */
    public function meta_title(): ?string
    {
        return $this->navigation->meta_title ?? $this->name();
    }

    /**
     * Get the meta description for SEO.
     *
     * @return string|null
     */
    public function meta_description(): ?string
    {
        return $this->navigation->meta_description;
    }

    /**
     * Get structured data in JSON-LD format.
     *
     * @return array{
     *     '@type': string,
     *     'name': string,
     *     'url': string,
     *     'position': int
     * }
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'SiteNavigationElement',
            'name' => $this->name(),
            'url' => $this->url(),
            'position' => $this->navigation->position ?? 1,
        ];
    }

    /**
     * Get analytics data.
     *
     * @return array{
     *     'id': int,
     *     'name': string,
     *     'type': string,
     *     'position': int,
     *     'clicks': int,
     *     'impressions': int
     * }
     */
    public function analytics(): array
    {
        return [
            'id' => $this->navigation->id,
            'name' => $this->name(),
            'type' => 'navigation',
            'position' => $this->navigation->position ?? 1,
            'clicks' => $this->navigation->clicks ?? 0,
            'impressions' => $this->navigation->impressions ?? 0,
        ];
    }

    /**
     * Get accessibility attributes.
     *
     * @return array{
     *     'aria_label': string,
     *     'aria_current': string|null,
     *     'aria_expanded': string|null,
     *     'aria_controls': string|null,
     *     'role': string
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => $this->navigation->aria_label ?? $this->name(),
            'aria_current' => $this->current() ? 'page' : null,
            'aria_expanded' => $this->has_children() ? 'false' : null,
            'aria_controls' => $this->has_children() ? 'nav-' . $this->handle() : null,
            'role' => 'menuitem',
        ];
    }

    /**
     * Check if the navigation item has children.
     *
     * @return bool
     */
    public function has_children(): bool
    {
        return $this->navigation->relationLoaded('children') && $this->navigation->children->isNotEmpty();
    }

    /**
     * Check if the navigation item is mobile-friendly.
     *
     * @return bool
     */
    public function is_mobile(): bool
    {
        return true;
    }

    /**
     * Check if the navigation item is desktop-friendly.
     *
     * @return bool
     */
    public function is_desktop(): bool
    {
        return true;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     'name': string,
     *     'tooltip': string,
     *     'dropdown': array<self>,
     *     'addon': string|null,
     *     'suffix': string|null,
     *     'active': bool,
     *     'href': string,
     *     'divider': bool,
     *     'handle': string,
     *     'title': string,
     *     'url': string,
     *     'current': bool,
     *     'items': array<self>,
     *     'icon': string|null,
     *     'icon_after': string|null,
     *     'is_divider': bool,
     *     'meta_title': string|null,
     *     'meta_description': string|null,
     *     'structured_data': array,
     *     'analytics': array,
     *     'accessibility': array,
     *     'is_mobile': bool,
     *     'is_desktop': bool
     * }
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'tooltip' => $this->tooltip(),
            'dropdown' => $this->dropdown(),
            'addon' => $this->addon(),
            'suffix' => $this->suffix(),
            'active' => $this->active(),
            'href' => $this->href(),
            'divider' => $this->divider(),
            'handle' => $this->handle(),
            'title' => $this->title(),
            'url' => $this->url(),
            'current' => $this->current(),
            'items' => $this->items(),
            'icon' => $this->icon(),
            'icon_after' => $this->icon_after(),
            'is_divider' => $this->is_divider(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'structured_data' => $this->structured_data(),
            'analytics' => $this->analytics(),
            'accessibility' => $this->accessibility(),
            'is_mobile' => $this->is_mobile(),
            'is_desktop' => $this->is_desktop()
        ];
    }

}
