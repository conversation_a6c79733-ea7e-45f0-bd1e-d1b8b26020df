<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 9.4.2019 г.
 * Time: 16:24 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\StoreFront;

use App\Exceptions\Error;
use App\Helper\StorageUrl;
use App\Models\Banner\Banners as BannerModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;

/**
 * Banner Drop class for handling banner data in Liquid templates
 *
 * @property-read string|null $name The banner name
 * @property-read string $type The banner type (image/script)
 * @property-read string|null $script The banner script content
 * @property-read string|null $image The banner image URL
 * @property-read string|null $link The banner link URL
 * @property-read string|null $title The banner title (Shopify compatibility)
 * @property-read string|null $handle The banner handle (Shopify compatibility)
 * @property-read string|null $alt_text The banner alt text (Shopify compatibility)
 * @property-read int $position The banner position (Shopify compatibility)
 * @property-read string $target The banner target attribute (Shopify compatibility)
 * @property-read string|null $meta_title The banner meta title (Shopify compatibility)
 * @property-read string|null $meta_description The banner meta description (Shopify compatibility)
 * @property-read array $structured_data The banner structured data (Shopify compatibility)
 * @property-read array $analytics The banner analytics data (Shopify compatibility)
 * @property-read array $accessibility The banner accessibility data (Shopify compatibility)
 * @property-read bool $is_mobile Whether the banner is mobile-only
 * @property-read bool $is_desktop Whether the banner is desktop-only
 */
class Banner extends AbstractDrop
{

    use DropImageToArray;

    /**
     * @var BannerModel
     */
    protected BannerModel $banner;

    /**
     * Navigation constructor.
     * @param BannerModel $banner
     */
    public function __construct(BannerModel $banner)
    {
        $this->banner = $banner;
    }

    /**
     * Get the banner name
     *
     * @return string|null
     */
    public function name(): ?string
    {
        if ($this->type() !== 'image') {
            return null;
        }

        return $this->banner->name;
    }

    /**
     * Get the banner type
     *
     * @return string
     */
    public function type(): string
    {
        return $this->banner->type;
    }

    /**
     * Get the banner script content
     *
     * @return string|null
     */
    public function script(): ?string
    {
        if ($this->type() !== 'script') {
            return null;
        }

        return $this->banner->script;
    }

    /**
     * Get the banner image URL
     *
     * @return string|null
     */
    public function image(): ?string
    {
        if ($this->type() !== 'image') {
            return null;
        }

        return StorageUrl::read($this->banner->src);
    }

    /**
     * Get the banner link URL
     *
     * @return string|null
     */
    public function link(): ?string
    {
        if ($this->type() !== 'image') {
            return null;
        }

        return $this->banner->link_formatted;
    }

    /**
     * Get the banner title (Shopify compatibility)
     *
     * @return string|null
     */
    public function title(): ?string
    {
        return $this->name();
    }

    /**
     * Get the banner handle (Shopify compatibility)
     *
     * @return string|null
     */
    public function handle(): ?string
    {
        return $this->banner->url_handle ?? null;
    }

    /**
     * Get the banner alt text (Shopify compatibility)
     *
     * @return string|null
     */
    public function alt_text(): ?string
    {
        return $this->banner->alt_text ?? $this->name();
    }

    /**
     * Get the banner position (Shopify compatibility)
     *
     * @return int
     */
    public function position(): int
    {
        return $this->banner->position ?? 1;
    }

    /**
     * Get the banner target attribute (Shopify compatibility)
     *
     * @return string|int
     */
    public function target(): string|int
    {
        return $this->banner->target ?? '_self';
    }

    /**
     * Get the banner meta title (Shopify compatibility)
     *
     * @return string|null
     */
    public function meta_title(): ?string
    {
        return $this->banner->meta_title ?? $this->name();
    }

    /**
     * Get the banner meta description (Shopify compatibility)
     *
     * @return string|null
     */
    public function meta_description(): ?string
    {
        return $this->banner->meta_description;
    }

    /**
     * Get the banner structured data (Shopify compatibility)
     *
     * @return array{
     *     @type: string,
     *     name: string|null,
     *     url: string|null,
     *     contentUrl: string|null,
     *     caption: string|null,
     *     position: int
     * }
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'ImageObject',
            'name' => $this->name(),
            'url' => $this->image(),
            'contentUrl' => $this->image(),
            'caption' => $this->alt_text(),
            'position' => $this->position(),
        ];
    }

    /**
     * Get the banner analytics data (Shopify compatibility)
     *
     * @return array{
     *     id: int,
     *     name: string|null,
     *     type: string,
     *     position: int,
     *     clicks: int,
     *     impressions: int
     * }
     */
    public function analytics(): array
    {
        return [
            'id' => $this->banner->id,
            'name' => $this->name(),
            'type' => $this->type(),
            'position' => $this->position(),
            'clicks' => $this->banner->clicks ?? 0,
            'impressions' => $this->banner->impressions ?? 0,
        ];
    }

    /**
     * Get the banner accessibility data (Shopify compatibility)
     *
     * @return array{
     *     aria_label: string|null,
     *     role: string,
     *     aria_hidden: string|null
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => $this->banner->aria_label ?? $this->name(),
            'role' => $this->type() === 'image' ? 'img' : 'presentation',
            'aria_hidden' => $this->type() === 'script' ? 'true' : null,
        ];
    }

    /**
     * Check if the banner is mobile-only
     *
     * @return bool
     */
    public function is_mobile(): bool
    {
        return (bool)($this->banner->mobile ?? false);
    }

    /**
     * Check if the banner is desktop-only
     *
     * @return bool
     */
    public function is_desktop(): bool
    {
        return (bool)($this->banner->desktop ?? true);
    }

    /**
     * Get the banner data as a plain array
     *
     * @return array{
     *     name: string|null,
     *     type: string,
     *     script: string|null,
     *     image: string|null,
     *     link: string|null,
     *     title: string|null,
     *     handle: string|null,
     *     alt_text: string|null,
     *     position: int,
     *     target: string,
     *     meta_title: string|null,
     *     meta_description: string|null,
     *     structured_data: array,
     *     analytics: array,
     *     accessibility: array,
     *     is_mobile: bool,
     *     is_desktop: bool
     * }
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'type' => $this->type(),
            'script' => $this->script(),
            'image' => $this->image(),
            'link' => $this->link(),
            'title' => $this->title(),
            'handle' => $this->handle(),
            'alt_text' => $this->alt_text(),
            'position' => $this->position(),
            'target' => $this->target(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'structured_data' => $this->structured_data(),
            'analytics' => $this->analytics(),
            'accessibility' => $this->accessibility(),
            'is_mobile' => $this->is_mobile(),
            'is_desktop' => $this->is_desktop(),
        ];
    }

}
