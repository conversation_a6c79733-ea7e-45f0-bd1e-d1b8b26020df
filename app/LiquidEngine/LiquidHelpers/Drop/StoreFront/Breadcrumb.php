<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.4.2019 г.
 * Time: 12:02 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\StoreFront;

use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use Illuminate\Support\Str;

/**
 * Breadcrumb Drop class for handling breadcrumb navigation in Liquid templates
 *
 * @property-read string $text The breadcrumb text
 * @property-read string|null $link The breadcrumb link URL
 * @property-read bool $active Whether the breadcrumb is active
 * @property-read string $name The breadcrumb name
 * @property-read string|null $url The breadcrumb URL
 * @property-read int $position The breadcrumb position
 * @property-read string $handle The breadcrumb handle
 * @property-read string|null $meta_title The breadcrumb meta title
 * @property-read string|null $meta_description The breadcrumb meta description
 * @property-read array $structured_data The breadcrumb structured data
 * @property-read array $analytics The breadcrumb analytics data
 * @property-read array $accessibility The breadcrumb accessibility data
 * @property-read bool $is_mobile Whether the breadcrumb is mobile-friendly
 * @property-read bool $is_desktop Whether the breadcrumb is desktop-friendly
 * @property-read string $orientation The breadcrumb orientation
 */
class Breadcrumb extends AbstractDrop
{
    /**
     * @var string
     */
    protected string $text;

    /**
     * @var string|null
     */
    protected ?string $link;

    /**
     * Breadcrumb constructor.
     *
     * @param string $text The breadcrumb text
     * @param string|null $link The breadcrumb link URL
     */
    public function __construct(string $text, ?string $link = null)
    {
        $this->text = $text;
        $this->link = $link;
    }

    /**
     * Get the breadcrumb text
     *
     * @return string
     */
    public function text(): string
    {
        return $this->text;
    }

    /**
     * Get the breadcrumb link URL
     *
     * @return string|null
     */
    public function link(): ?string
    {
        return $this->link;
    }

    /**
     * Check if the breadcrumb is active
     *
     * @return bool
     */
    public function active(): bool
    {
        return false;
    }

    /**
     * Get the name of the breadcrumb
     *
     * @return string
     */
    public function name(): string
    {
        return $this->text();
    }

    /**
     * Get the URL of the breadcrumb
     *
     * @return string|null
     */
    public function url(): ?string
    {
        return $this->link();
    }

    /**
     * Get the position of the breadcrumb
     *
     * @return int
     */
    public function position(): int
    {
        return $this->breadcrumb?->position ?? 0;
    }

    /**
     * Get the URL handle for the breadcrumb
     *
     * @return string
     */
    public function handle(): string
    {
        return Str::slug($this->name());
    }

    /**
     * Get the meta title for SEO
     *
     * @return string|null
     */
    public function meta_title(): ?string
    {
        return $this->name();
    }

    /**
     * Get the meta description for SEO
     *
     * @return string|null
     */
    public function meta_description(): ?string
    {
        return null;
    }

    /**
     * Get structured data in JSON-LD format
     *
     * @return array{
     *     @type: string,
     *     position: int,
     *     name: string,
     *     item: string|null
     * }
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'BreadcrumbList',
            'position' => $this->position(),
            'name' => $this->name(),
            'item' => $this->url()
        ];
    }

    /**
     * Get analytics data
     *
     * @return array{
     *     type: string,
     *     name: string,
     *     position: int
     * }
     */
    public function analytics(): array
    {
        return [
            'type' => 'breadcrumb',
            'name' => $this->name(),
            'position' => $this->position()
        ];
    }

    /**
     * Get accessibility attributes
     *
     * @return array{
     *     role: string,
     *     aria-label: string,
     *     aria-current: string|null
     * }
     */
    public function accessibility(): array
    {
        return [
            'role' => 'link',
            'aria-label' => $this->name(),
            'aria-current' => $this->active() ? 'page' : null
        ];
    }

    /**
     * Check if the breadcrumb is mobile-friendly
     *
     * @return bool
     */
    public function is_mobile(): bool
    {
        return true;
    }

    /**
     * Check if the breadcrumb is desktop-friendly
     *
     * @return bool
     */
    public function is_desktop(): bool
    {
        return true;
    }

    /**
     * Get the orientation of the breadcrumb
     *
     * @return string
     */
    public function orientation(): string
    {
        return 'horizontal';
    }

    /**
     * Get the breadcrumb data as a plain array
     *
     * @return array{
     *     name: string,
     *     url: string|null,
     *     active: bool,
     *     position: int,
     *     title: string,
     *     handle: string,
     *     meta_title: string|null,
     *     meta_description: string|null,
     *     structured_data: array,
     *     analytics: array,
     *     accessibility: array,
     *     is_mobile: bool,
     *     is_desktop: bool,
     *     orientation: string
     * }
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'url' => $this->url(),
            'active' => $this->active(),
            'position' => $this->position(),
            'title' => $this->name(),
            'handle' => $this->handle(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'structured_data' => $this->structured_data(),
            'analytics' => $this->analytics(),
            'accessibility' => $this->accessibility(),
            'is_mobile' => $this->is_mobile(),
            'is_desktop' => $this->is_desktop(),
            'orientation' => $this->orientation()
        ];
    }
}
