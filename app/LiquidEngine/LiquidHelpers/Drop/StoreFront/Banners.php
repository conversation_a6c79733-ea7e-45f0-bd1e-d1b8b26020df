<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 9.4.2019 г.
 * Time: 16:24 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\StoreFront;

use App\Models\Banner\BannersGroups;
use App\Models\Banner\Banners as BannerModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

/**
 * Banners Drop class for handling banner collections in Liquid templates
 *
 * @property-read string $name The banner collection name
 * @property-read string|null $handle The banner collection handle
 * @property-read array<Banner> $banners The collection of banners
 * @property-read string $title The banner collection title (Shopify compatibility)
 * @property-read array<Banner> $items The collection of banners (Shopify compatibility)
 * @property-read string|null $meta_title The banner collection meta title
 * @property-read string|null $meta_description The banner collection meta description
 * @property-read array $structured_data The banner collection structured data
 * @property-read array $analytics The banner collection analytics data
 * @property-read array $accessibility The banner collection accessibility data
 * @property-read bool $is_mobile Whether the banner collection is mobile-friendly
 * @property-read bool $is_desktop Whether the banner collection is desktop-friendly
 * @property-read string $orientation The banner collection orientation
 * @property-read string|null $description The banner collection description
 */
class Banners extends AbstractDrop
{

    /**
     * @var BannersGroups
     */
    protected BannersGroups $bannersGroup;

    /**
     * Navigation constructor.
     * @param BannersGroups $bannersGroup
     */
    public function __construct(BannersGroups $bannersGroup)
    {
        $this->bannersGroup = $bannersGroup;
    }

    /**
     * Get the banner collection name
     *
     * @return string
     */
    public function name(): string
    {
        return $this->bannersGroup->name;
    }

    /**
     * Get the banner collection handle
     *
     * @return string|null
     */
    public function handle(): ?string
    {
        return $this->bannersGroup->item->url_handle ?? null;
    }

    /**
     * Get the collection of banners
     *
     * @return array<Banner>
     */
    public function banners(): array
    {
        $banners = [];
        if ($this->bannersGroup->relationLoaded('banners')) {
            $banners = $this->bannersGroup->banners->map(function (BannerModel $banner): Banner {
                return new Banner($banner);
            })->all();
        }

        return $banners;
    }

    /**
     * Get the banner collection title (Shopify compatibility)
     *
     * @return string
     */
    public function title(): string
    {
        return $this->name();
    }

    /**
     * Get the collection of banners (Shopify compatibility)
     *
     * @return array<Banner>
     */
    public function items(): array
    {
        return $this->banners();
    }

    /**
     * Get the meta title for SEO
     *
     * @return string|null
     */
    public function meta_title(): ?string
    {
        return $this->name();
    }

    /**
     * Get the meta description for SEO
     *
     * @return string|null
     */
    public function meta_description(): ?string
    {
        return $this->description();
    }

    /**
     * Get structured data in JSON-LD format
     *
     * @return array{
     *     @type: string,
     *     name: string,
     *     description: string|null,
     *     numberOfItems: int
     * }
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'CollectionPage',
            'name' => $this->name(),
            'description' => $this->description(),
            'numberOfItems' => count($this->banners())
        ];
    }

    /**
     * Get analytics data
     *
     * @return array{
     *     type: string,
     *     name: string,
     *     count: int
     * }
     */
    public function analytics(): array
    {
        return [
            'type' => 'banner_collection',
            'name' => $this->name(),
            'count' => count($this->banners())
        ];
    }

    /**
     * Get accessibility attributes
     *
     * @return array{
     *     role: string,
     *     aria-label: string
     * }
     */
    public function accessibility(): array
    {
        return [
            'role' => 'banner',
            'aria-label' => $this->name()
        ];
    }

    /**
     * Check if the banner collection is mobile-friendly
     *
     * @return bool
     */
    public function is_mobile(): bool
    {
        return true;
    }

    /**
     * Check if the banner collection is desktop-friendly
     *
     * @return bool
     */
    public function is_desktop(): bool
    {
        return true;
    }

    /**
     * Get the orientation of the banner collection
     *
     * @return string
     */
    public function orientation(): string
    {
        return 'horizontal';
    }

    /**
     * Get the description of the banner collection
     *
     * @return string|null
     */
    public function description(): ?string
    {
        return $this->bannersGroup?->description;
    }

    /**
     * Get the banner collection data as a plain array
     *
     * @return array{
     *     name: string,
     *     handle: string|null,
     *     banners: array<Banner>,
     *     title: string,
     *     items: array<Banner>,
     *     meta_title: string|null,
     *     meta_description: string|null,
     *     structured_data: array,
     *     analytics: array,
     *     accessibility: array,
     *     is_mobile: bool,
     *     is_desktop: bool,
     *     orientation: string
     * }
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'handle' => $this->handle(),
            'banners' => $this->banners(),
            'title' => $this->title(),
            'items' => $this->items(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'structured_data' => $this->structured_data(),
            'analytics' => $this->analytics(),
            'accessibility' => $this->accessibility(),
            'is_mobile' => $this->is_mobile(),
            'is_desktop' => $this->is_desktop(),
            'orientation' => $this->orientation()
        ];
    }

}
