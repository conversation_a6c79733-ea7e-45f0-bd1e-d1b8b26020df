<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\StoreFront;

use App\Models\Layout\FormFieldOptions;
use App\Models\Layout\FormFields;
use Illuminate\Support\Arr;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Phone;
use Illuminate\Support\Str;

/**
 * FormField Drop class for handling form field data in Liquid templates.
 *
 * @property-read int $id The unique identifier of the form field
 * @property-read string $name The display name of the form field
 * @property-read string $type The type of the form field (text, email, phone, etc.)
 * @property-read bool $required Whether the field is required
 * @property-read array|Phone|string|null $value The current value of the field
 * @property-read array|FormFieldOption[] $options Available options for select/radio/checkbox fields
 * @property-read string|null $placeholder Placeholder text for the field
 * @property-read string|null $help_text Help text for the field
 * @property-read array $validation Validation rules for the field
 * @property-read string $handle URL-friendly handle for the field
 * @property-read string|null $meta_title SEO meta title
 * @property-read string|null $meta_description SEO meta description
 * @property-read array $structured_data JSON-LD structured data
 * @property-read array $analytics Analytics data
 * @property-read array $accessibility Accessibility attributes
 * @property-read bool $is_mobile Whether the field is mobile-friendly
 * @property-read bool $is_desktop Whether the field is desktop-friendly
 * @property-read string $orientation Field orientation (vertical/horizontal)
 */
class FormField extends AbstractDrop
{
    protected FormFields $field;

    /**
     * @var null|string|array $value
     */
    protected $value;

    /**
     * FormField constructor.
     *
     * @param FormFields $field The form field model instance
     * @param null|string|array $value The current value of the field
     */
    public function __construct(FormFields $field, $value = null)
    {
        $this->field = $field;
        $this->value = $value;
    }

    /**
     * Get the unique identifier of the form field.
     *
     * @return int
     */
    public function id(): int
    {
        return $this->field->id;
    }

    /**
     * Get the display name of the form field.
     *
     * @return string
     */
    public function name(): string
    {
        return $this->field->storefront_name ?: $this->field->name;
    }

    /**
     * Get the type of the form field.
     *
     * @return string
     */
    public function type(): string
    {
        return $this->field->type;
    }

    /**
     * Check if the field is required.
     *
     * @return bool
     */
    public function required(): bool
    {
        return (bool) $this->field->required;
    }

    /**
     * Get the current value of the field.
     *
     * @return array|Phone|string|null
     */
    public function value()
    {
        if (in_array($this->type(), ['checkbox', 'radio', 'select'])) {
            return null;
        }

        if ($this->type() === 'phone' && is_array($this->value) && Arr::has($this->value, ['phone', 'country'])) {
            return new Phone($this->value['phone'], $this->value['country']);
        }

        return $this->value;
    }

    /**
     * Get available options for select/radio/checkbox fields.
     *
     * @return array|FormFieldOption[]
     */
    public function options(): array
    {
        $options = [];
        if ($this->field->relationLoaded('options')) {
            $options = $this->field->options->map(function (FormFieldOptions $option): FormFieldOption {
                $active = false;
                if (in_array($this->type(), ['checkbox', 'radio', 'select'])) {
                    $active = is_array($this->value) ? in_array($option->id, $this->value) : $this->value == $option->id;
                }

                return new FormFieldOption($option, $active);
            })->all();
        }

        return $options;
    }

    /**
     * Get the placeholder text for the field.
     *
     * @return string|null
     */
    public function placeholder(): ?string
    {
        return $this->field?->placeholder;
    }

    /**
     * Get the help text for the field.
     *
     * @return string|null
     */
    public function help_text(): ?string
    {
        return $this->field?->help_text;
    }

    /**
     * Get the validation rules for the field.
     *
     * @return array<string, mixed>
     */
    public function validation(): array
    {
        return $this->field?->validation ?? [];
    }

    /**
     * Get the URL handle for the field.
     *
     * @return string
     */
    public function handle(): string
    {
        return Str::slug($this->name());
    }

    /**
     * Get the meta title for SEO.
     *
     * @return string|null
     */
    public function meta_title(): ?string
    {
        return $this->name();
    }

    /**
     * Get the meta description for SEO.
     *
     * @return string|null
     */
    public function meta_description(): ?string
    {
        return $this->help_text();
    }

    /**
     * Get structured data in JSON-LD format.
     *
     * @return array{
     *     '@type': string,
     *     'name': string,
     *     'description': string|null,
     *     'valueRequired': bool
     * }
     */
    public function structured_data(): array
    {
        return [
            '@type' => 'PropertyValue',
            'name' => $this->name(),
            'description' => $this->help_text(),
            'valueRequired' => $this->required()
        ];
    }

    /**
     * Get analytics data.
     *
     * @return array{
     *     'type': string,
     *     'name': string,
     *     'field_type': string
     * }
     */
    public function analytics(): array
    {
        return [
            'type' => 'form_field',
            'name' => $this->name(),
            'field_type' => $this->type()
        ];
    }

    /**
     * Get accessibility attributes.
     *
     * @return array{
     *     'role': string,
     *     'aria-label': string,
     *     'aria-required': bool,
     *     'aria-describedby': string|null
     * }
     */
    public function accessibility(): array
    {
        return [
            'role' => 'textbox',
            'aria-label' => $this->name(),
            'aria-required' => $this->required(),
            'aria-describedby' => $this->help_text() ? 'help-' . $this->handle() : null
        ];
    }

    /**
     * Check if the field is mobile-friendly.
     *
     * @return bool
     */
    public function is_mobile(): bool
    {
        return true;
    }

    /**
     * Check if the field is desktop-friendly.
     *
     * @return bool
     */
    public function is_desktop(): bool
    {
        return true;
    }

    /**
     * Get the orientation of the field.
     *
     * @return string
     */
    public function orientation(): string
    {
        return 'vertical';
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     'id': int,
     *     'name': string,
     *     'type': string,
     *     'required': bool,
     *     'options': array|FormFieldOption[],
     *     'value': array|Phone|string|null,
     *     'placeholder': string|null,
     *     'help_text': string|null,
     *     'validation': array<string, mixed>,
     *     'handle': string,
     *     'meta_title': string|null,
     *     'meta_description': string|null,
     *     'structured_data': array,
     *     'analytics': array,
     *     'accessibility': array,
     *     'is_mobile': bool,
     *     'is_desktop': bool,
     *     'orientation': string
     * }
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'type' => $this->type(),
            'required' => $this->required(),
            'options' => $this->options(),
            'value' => $this->value(),
            'placeholder' => $this->placeholder(),
            'help_text' => $this->help_text(),
            'validation' => $this->validation(),
            'handle' => $this->handle(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'structured_data' => $this->structured_data(),
            'analytics' => $this->analytics(),
            'accessibility' => $this->accessibility(),
            'is_mobile' => $this->is_mobile(),
            'is_desktop' => $this->is_desktop(),
            'orientation' => $this->orientation()
        ];
    }
}
