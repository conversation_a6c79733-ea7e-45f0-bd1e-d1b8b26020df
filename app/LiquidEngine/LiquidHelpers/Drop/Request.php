<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Support\Facades\Request as RequestFacade;
use Liquid\Drop;

/**
 * Request Drop
 * 
 * Provides access to the current request information in Liquid templates.
 * This follows Shopify's request object pattern.
 */
class Request extends Drop
{
    /**
     * Get the current page URL
     *
     * @return string
     */
    public function url(): string
    {
        return RequestFacade::url();
    }
    
    /**
     * Get the current page path
     *
     * @return string
     */
    public function path(): string
    {
        return RequestFacade::path();
    }
    
    /**
     * Get the request host
     *
     * @return string
     */
    public function host(): string
    {
        return RequestFacade::getHost();
    }
    
    /**
     * Get the request HTTP method
     *
     * @return string
     */
    public function method(): string
    {
        return RequestFacade::method();
    }
    
    /**
     * Get the locale of the current request
     *
     * @return string
     */
    public function locale(): string
    {
        return app()->getLocale();
    }
    
    /**
     * Check if the current page is the home page
     *
     * @return bool
     */
    public function is_home_page(): bool
    {
        return RequestFacade::path() === '/' || RequestFacade::path() === '';
    }
    
    /**
     * Check if the current page is the cart page
     *
     * @return bool
     */
    public function is_cart(): bool
    {
        return RequestFacade::path() === 'cart';
    }
    
    /**
     * Check if the current page is a product page
     *
     * @return bool
     */
    public function is_product(): bool
    {
        return str_starts_with(RequestFacade::path(), 'products/');
    }
    
    /**
     * Check if the current page is a collection page
     *
     * @return bool
     */
    public function is_collection(): bool
    {
        return str_starts_with(RequestFacade::path(), 'collections/');
    }
    
    /**
     * Check if the current page is a blog page
     *
     * @return bool
     */
    public function is_blog(): bool
    {
        return str_starts_with(RequestFacade::path(), 'blogs/');
    }
    
    /**
     * Check if the current page is a search page
     *
     * @return bool
     */
    public function is_search(): bool
    {
        return RequestFacade::path() === 'search';
    }
    
    /**
     * Check if the current page is an account page
     *
     * @return bool
     */
    public function is_account(): bool
    {
        return str_starts_with(RequestFacade::path(), 'account');
    }
    
    /**
     * Check if the current page is a checkout page
     *
     * @return bool
     */
    public function is_checkout(): bool
    {
        return str_starts_with(RequestFacade::path(), 'checkout');
    }
    
    /**
     * Get all query parameters
     *
     * @return array
     */
    public function query_parameters(): array
    {
        return RequestFacade::query();
    }
    
    /**
     * Get a specific query parameter
     *
     * @param string $key
     * @return mixed
     */
    public function param($key)
    {
        return RequestFacade::query($key);
    }
    
    /**
     * Check if the request is AJAX
     *
     * @return bool
     */
    public function is_ajax(): bool
    {
        return RequestFacade::ajax();
    }
    
    /**
     * Check if the request is for a JSON response
     *
     * @return bool
     */
    public function is_json(): bool
    {
        return RequestFacade::wantsJson() || 
               str_ends_with(RequestFacade::path(), '.json');
    }
    
    /**
     * Get the design mode status
     *
     * @return bool
     */
    public function design_mode(): bool
    {
        return RequestFacade::has('design_mode') && 
               RequestFacade::query('design_mode') === 'true';
    }

    /**
     * Get the page type of the current request
     * Shopify compatible: request.page_type
     */
    public function page_type(): string
    {
        // Determine page type based on current route/path
        $path = RequestFacade::path();
        
        if (strpos($path, 'products/') !== false) {
            return 'product';
        } elseif (strpos($path, 'collections/') !== false) {
            return 'collection';
        } elseif (strpos($path, 'pages/') !== false) {
            return 'page';
        } elseif (strpos($path, 'cart') !== false) {
            return 'cart';
        } elseif (strpos($path, 'checkout') !== false) {
            return 'checkout';
        } elseif (strpos($path, 'search') !== false) {
            return 'search';
        } elseif ($path === '/' || $path === '') {
            return 'index';
        } else {
            return 'page';
        }
    }

    /**
     * Get the origin of the request
     * Shopify compatible: request.origin
     */
    public function origin(): string
    {
        return RequestFacade::getScheme() . '://' . $this->host();
    }

    /**
     * Get locale information as array (Shopify format)
     * Shopify compatible: request.locale_info
     */
    public function locale_info(): array
    {
        $locale = app()->getLocale();
        return [
            'iso_code' => $locale,
            'name' => $locale,
            'primary' => true,
            'shop_configured' => true
        ];
    }

    /**
     * Get page size for pagination
     * Shopify compatible: request.page_size
     */
    public function page_size(): int
    {
        return (int) RequestFacade::query('per_page', 20);
    }

    /**
     * Get current page number
     * Shopify compatible: request.page_number
     */
    public function page_number(): int
    {
        return (int) RequestFacade::query('page', 1);
    }

    /**
     * Get request timestamp
     * Shopify compatible: request.timestamp
     */
    public function timestamp(): string
    {
        return \Carbon\Carbon::now()->toISOString();
    }

    /**
     * Get user agent information
     * Shopify compatible: request.user_agent
     */
    public function user_agent(): string
    {
        return RequestFacade::header('User-Agent') ?? '';
    }

    /**
     * Get request IP address
     * Shopify compatible: request.ip
     */
    public function ip(): string
    {
        return RequestFacade::ip() ?? '';
    }

    /**
     * Check if request is HTTPS
     * Shopify compatible: request.is_secure
     */
    public function is_secure(): bool
    {
        return RequestFacade::secure();
    }

    /**
     * Get request headers
     * Shopify compatible: request.headers
     */
    public function headers(): array
    {
        return RequestFacade::header();
    }

    /**
     * Get specific header value
     * Shopify compatible: request.header
     */
    public function header(string $name): ?string
    {
        return RequestFacade::header($name);
    }

    /**
     * Get request cookies
     * Shopify compatible: request.cookies
     */
    public function cookies(): array
    {
        return RequestFacade::cookie();
    }

    public function toArray(): array
    {
        return [
            // Original fields
            'host' => $this->host(),
            'url' => $this->url(),
            'path' => $this->path(),
            'query_parameters' => $this->query_parameters(),
            'method' => $this->method(),
            'is_ajax' => $this->is_ajax(),
            // New Shopify-compatible fields
            'page_type' => $this->page_type(),
            'origin' => $this->origin(),
            'locale' => $this->locale(),
            'locale_info' => $this->locale_info(),
            'design_mode' => $this->design_mode(),
            'page_size' => $this->page_size(),
            'page_number' => $this->page_number(),
            'timestamp' => $this->timestamp(),
            'user_agent' => $this->user_agent(),
            'ip' => $this->ip(),
            'is_secure' => $this->is_secure(),
            'headers' => $this->headers(),
            'cookies' => $this->cookies(),
        ];
    }
} 