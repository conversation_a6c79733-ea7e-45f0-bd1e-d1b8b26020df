<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Exceptions\Error;
use App\Models\Product\Vendor as VendorModel;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;

class Vendor extends AbstractDrop
{

    use DropImageToArray;

    protected ?\App\Models\Product\Vendor $_vendor;

    /**
     * Product constructor.
     * @param VendorModel|null $vendor
     */
    public function __construct(?VendorModel $vendor = null)
    {
        $this->_vendor = $vendor;
    }

    public function id()
    {
        return $this->_vendor->id ?? null;
    }

    public function name()
    {
        return $this->_vendor->name ?? null;
    }

    public function letter()
    {
        return $this->_vendor->letter ?? null;
    }

    public function url()
    {
        return $this->_vendor->url ?? null;
    }

    public function url_handle()
    {
        return $this->_vendor->url_handle ?? null;
    }

    public function description()
    {
        return $this->_vendor->description ?? null;
    }

    public function seo_title()
    {
        return $this->_vendor->seo_title ?? null;
    }

    public function seo_description()
    {
        return $this->_vendor->seo_description ?? null;
    }

    public function image()
    {
        $formatter = new UrlImageFormat($this->_vendor);
        return $formatter->getLiquidImage();
    }

    /**
     * @return bool
     */
    public function has_image(): bool
    {
        return !!$this->_vendor->hasImage();
    }

    // === NEW SHOPIFY-COMPATIBLE METHODS ===

    /**
     * Shopify-compatible handle (sanitized name)
     * @return string|null
     */
    public function handle(): ?string
    {
        if (!$this->_vendor || !$this->_vendor->name) {
            return null;
        }
        
        return strtolower(str_replace(' ', '-', preg_replace('/[^\w\s-]/', '', $this->_vendor->name)));
    }

    /**
     * Title (alias for name)
     * @return string|null
     */
    public function title(): ?string
    {
        return $this->name();
    }

    /**
     * Display name for frontend
     * @return string|null
     */
    public function display_name(): ?string
    {
        return $this->name();
    }

    /**
     * Number of products for this vendor
     * @return int
     */
    public function products_count(): int
    {
        if (!$this->_vendor) {
            return 0;
        }
        
        // Get count of products for this vendor
        return $this->_vendor->products()->count();
    }

    /**
     * Collection URL for this vendor
     * @return string|null
     */
    public function collection_url(): ?string
    {
        if (!$this->_vendor || !$this->_vendor->name) {
            return null;
        }
        
        return '/collections/vendors?q=' . urlencode($this->_vendor->name);
    }

    /**
     * Link to vendor collection
     * @return string|null
     */
    public function link(): ?string
    {
        return $this->collection_url();
    }

    /**
     * Check if vendor has products
     * @return bool
     */
    public function has_products(): bool
    {
        return $this->products_count() > 0;
    }

    /**
     * Check if vendor is active/published
     * @return bool
     */
    public function published(): bool
    {
        if (!$this->_vendor) {
            return false;
        }
        
        return $this->_vendor->status === 'active' || $this->_vendor->published ?? true;
    }

    /**
     * Meta title for SEO
     * @return string|null
     */
    public function meta_title(): ?string
    {
        return $this->seo_title() ?: $this->name();
    }

    /**
     * Meta description for SEO
     * @return string|null
     */
    public function meta_description(): ?string
    {
        return $this->seo_description() ?: $this->description();
    }

    /**
     * Formatted display for templates
     * @return string|null
     */
    public function formatted(): ?string
    {
        if (!$this->_vendor || !$this->_vendor->name) {
            return null;
        }
        
        return ucwords($this->_vendor->name);
    }

    /**
     * Check if vendor has description
     * @return bool
     */
    public function has_description(): bool
    {
        return !empty($this->description());
    }

    /**
     * Featured image (alias for image)
     * @return mixed
     */
    public function featured_image()
    {
        return $this->image();
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     * @throws Error
     */
    public function toArray(): array
    {
        if ($this->_vendor === null) {
            return [];
        }

        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'title' => $this->title(),
            'handle' => $this->handle(),
            'letter' => $this->letter(),
            'url' => $this->url(),
            'url_handle' => $this->url_handle(),
            'collection_url' => $this->collection_url(),
            'link' => $this->link(),
            'description' => $this->description(),
            'seo_title' => $this->seo_title(),
            'seo_description' => $this->seo_description(),
            'meta_title' => $this->meta_title(),
            'meta_description' => $this->meta_description(),
            'display_name' => $this->display_name(),
            'formatted' => $this->formatted(),
            'products_count' => $this->products_count(),
            'has_products' => $this->has_products(),
            'published' => $this->published(),
            'has_image' => $this->has_image(),
            'has_description' => $this->has_description(),
            'image' => $this->getImageArray(),
            'featured_image' => $this->featured_image(),
        ];
    }

}
