<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Helpers;

use App\Models\Customer\CustomerShippingAddress;
use App\Models\Customer\CustomerBillingAddress;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Customer\Address\City;
use App\LiquidEngine\LiquidHelpers\Drop\Customer\Address\Country;
use App\LiquidEngine\LiquidHelpers\Drop\Customer\Address\Formatted;
use App\LiquidEngine\LiquidHelpers\Drop\Customer\Address\State;
use App\LiquidEngine\LiquidHelpers\Drop\Customer\Address\Street;

/**
 * Address Drop class for handling address data in Liquid templates
 * 
 * @property-read int $id
 * @property-read string $integration
 * @property-read string $type
 * @property-read Country|null $country
 * @property-read State|null $state
 * @property-read City|null $city
 * @property-read string $post_code
 * @property-read Street|null $street
 * @property-read string $street_number
 * @property-read float|null $latitude
 * @property-read float|null $longitude
 * @property-read string|null $text
 * @property-read Formatted $formatted
 * @property-read string|null $company_name
 * @property-read string|null $company_bulstat
 * @property-read string|null $company_vat
 * @property-read string|null $company_owner
 */
class Address extends AbstractDrop
{
    /**
     * @var CustomerShippingAddress|CustomerBillingAddress
     */
    protected $_address;

    /**
     * Address constructor.
     * @param CustomerShippingAddress|CustomerBillingAddress $address
     */
    public function __construct($address)
    {
        $this->_address = $address;
    }

    /**
     * Get the address ID
     * 
     * @return int
     */
    public function id(): int
    {
        return $this->_address->id;
    }

    /**
     * Get the integration type
     * 
     * @return string
     */
    public function integration(): string
    {
        return $this->_address->integration;
    }

    /**
     * Get the address type
     * 
     * @return string
     */
    public function type(): string
    {
        return 'marketplace';
    }

    /**
     * Get the country object
     * 
     * @return Country|null
     */
    public function country(): ?Country
    {
        if ($country = $this->_address->address->getCountry()) {
            return new Country($country);
        }

        return null;
    }

    /**
     * Get the state/province object
     * 
     * @return State|null
     */
    public function state(): ?State
    {
        if ($state = $this->_address->address->getState()) {
            return new State($state);
        }

        return null;
    }

    /**
     * Get the city object
     * 
     * @return City|null
     */
    public function city(): ?City
    {
        if ($city = $this->_address->address->getCity()) {
            return new City($city);
        }

        return null;
    }

    /**
     * Get the postal code
     * 
     * @return string
     */
    public function post_code(): string
    {
        return $this->_address->address->getPostCode();
    }

    /**
     * Get the street object
     * 
     * @return Street|null
     */
    public function street(): ?Street
    {
        if ($street = $this->_address->address->getStreet()) {
            return new Street($street);
        }

        return null;
    }

    /**
     * Get the street number
     * 
     * @return string
     */
    public function street_number(): string
    {
        return $this->_address->address->getStreetNumber();
    }

    /**
     * Get the latitude coordinate
     * 
     * @return float|null
     */
    public function latitude(): ?float
    {
        return $this->_address->address->getLatitude();
    }

    /**
     * Get the longitude coordinate
     * 
     * @return float|null
     */
    public function longitude(): ?float
    {
        return $this->_address->address->getLongitude();
    }

    /**
     * Get the address text
     * 
     * @return string|null
     */
    public function text(): ?string
    {
        return $this->_address->address->getText();
    }

    /**
     * Get the formatted address
     * 
     * @return Formatted
     */
    public function formatted(): Formatted
    {
        return new Formatted($this->_address->address);
    }

    /**
     * Get the company name
     * 
     * @return string|null
     */
    public function company_name(): ?string
    {
        return $this->_address->company_name;
    }

    /**
     * Get the company bulstat (Bulgarian company registration number)
     * 
     * @return string|null
     */
    public function company_bulstat(): ?string
    {
        return $this->_address->company_bulstat;
    }

    /**
     * Get the company VAT number
     * 
     * @return string|null
     */
    public function company_vat(): ?string
    {
        return $this->_address->company_vat;
    }

    /**
     * Get the company owner/manager
     * 
     * @return string|null
     */
    public function company_owner(): ?string
    {
        return $this->_address->company_mol;
    }

    /**
     * Get the address as a plain array
     * 
     * @return array{
     *     id: int,
     *     integration: string,
     *     type: string,
     *     country: Country|null,
     *     state: State|null,
     *     city: City|null,
     *     post_code: string,
     *     street: Street|null,
     *     street_number: string,
     *     latitude: float|null,
     *     longitude: float|null,
     *     text: string|null,
     *     formatted: Formatted,
     *     company_name: string|null,
     *     company_bulstat: string|null,
     *     company_vat: string|null,
     *     company_owner: string|null
     * }
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'integration' => $this->integration(),
            'type' => $this->type(),
            'country' => $this->country(),
            'state' => $this->state(),
            'city' => $this->city(),
            'post_code' => $this->post_code(),
            'street' => $this->street(),
            'street_number' => $this->street_number(),
            'latitude' => $this->latitude(),
            'longitude' => $this->longitude(),
            'text' => $this->text(),
            'formatted' => $this->formatted(),
            'company_name' => $this->company_name(),
            'company_bulstat' => $this->company_bulstat(),
            'company_vat' => $this->company_vat(),
            'company_owner' => $this->company_owner(),
        ];
    }
}
