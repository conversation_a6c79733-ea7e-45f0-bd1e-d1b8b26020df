<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Helpers;

use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

/**
 * Banner Drop class for handling banner data in Liquid templates
 * 
 * @property-read string|null $type The type of banner (image or script)
 * @property-read string|null $src The source URL for image banners
 * @property-read string|null $caption The caption text for image banners
 * @property-read string|null $target The target attribute for image banners (_blank or _self)
 * @property-read string|null $url The URL for image banners
 * @property-read string|null $script The script content for script banners
 */
class Banner extends AbstractDrop
{
    /**
     * @var array{
     *     type?: string,
     *     src?: string,
     *     caption?: string,
     *     target?: string,
     *     url?: string,
     *     script?: string
     * }
     */
    protected array $_banner;

    /**
     * Banner constructor.
     * 
     * @param array{
     *     type?: string,
     *     src?: string,
     *     caption?: string,
     *     target?: string,
     *     url?: string,
     *     script?: string
     * } $banner
     */
    public function __construct(array $banner)
    {
        $this->_banner = $banner;
    }

    /**
     * Get the banner type
     * 
     * @return string|null
     */
    public function type(): ?string
    {
        return in_array($this->_banner['type'] ?? null, ['image', 'script']) ? $this->_banner['type'] : null;
    }

    /**
     * Get the source URL for image banners
     * 
     * @return string|null
     */
    public function src(): ?string
    {
        if ($this->type() === 'image') {
            return $this->_banner['src'] ?? null;
        }

        return null;
    }

    /**
     * Get the caption text for image banners
     * 
     * @return string|null
     */
    public function caption(): ?string
    {
        if ($this->type() === 'image') {
            return $this->_banner['caption'] ?? null;
        }

        return null;
    }

    /**
     * Get the target attribute for image banners
     * 
     * @return string|null
     */
    public function target(): ?string
    {
        if ($this->type() === 'image') {
            return ($this->_banner['target'] ?? null) === '_blank' ? '_blank' : '_self';
        }

        return null;
    }

    /**
     * Get the URL for image banners
     * 
     * @return string|null
     */
    public function url(): ?string
    {
        if ($this->type() === 'image') {
            return $this->_banner['url'] ?? null;
        }

        return null;
    }

    /**
     * Get the script content for script banners
     * 
     * @return string|null
     */
    public function script(): ?string
    {
        if ($this->type() === 'script') {
            return $this->_banner['script'] ?? null;
        }

        return null;
    }

    /**
     * Get the banner as a plain array
     * 
     * @return array{
     *     type: string|null,
     *     src: string|null,
     *     caption: string|null,
     *     target: string|null,
     *     url: string|null,
     *     script: string|null
     * }
     */
    public function toArray(): array
    {
        return [
            'type' => $this->type(),
            'src' => $this->src(),
            'caption' => $this->caption(),
            'target' => $this->target(),
            'url' => $this->url(),
            'script' => $this->script(),
        ];
    }
}
