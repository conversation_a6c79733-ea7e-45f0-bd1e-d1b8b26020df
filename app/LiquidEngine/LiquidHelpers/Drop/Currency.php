<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Helper\ArrayCache;
use App\Locale\Currency as LocaleCurrency;
use JsonSerializable;

class Currency extends AbstractDrop implements JsonSerializable
{

    /**
     * @var array $_currency
     */
    protected $_currency;

    /**
     * @param mixed $currency
     * @param mixed $locale
     * @return mixed
     */
    public function __construct($currency = null, $locale = null)
    {
        $locale = $locale ?: setting('language');
        $currency = $currency ?: site('currency');

        if (is_null($currency) && is_null($locale)) {
            $locale = site()->language;
            $currency = site()->currency;
        }

        $this->_currency = ArrayCache::remember(sprintf('currency.active.%s.%s', $currency, $locale), function () use ($currency, $locale) {
            return LocaleCurrency::locale($locale)->get($currency);
        });
    }

    /**
     * @return string
     */
    public function name()
    {
        return $this->_currency['name'];
    }

    /**
     * @return string
     */
    public function pattern()
    {
        return $this->_currency['pattern'];
    }

    /**
     * @return string
     */
    public function code()
    {
        return $this->_currency['code'];
    }

    /**
     * @return string
     */
    public function decimal_point()
    {
        return $this->_currency['dec_point'];
    }

    /**
     * @return string
     */
    public function thousands_separator()
    {
        return $this->_currency['thousands_sep'];
    }

    /**
     * @return int
     */
    public function coins_pad()
    {
        return $this->_currency['coins_pad'];
    }

    /**
     * @return string
     */
    public function sign_left()
    {
        return $this->_currency['sign_left'];
    }

    /**
     * @return string
     */
    public function sign_right()
    {
        return $this->_currency['sign_right'];
    }

    // START: Shopify-compatible methods for Currency
    // These methods provide compatibility with Shopify's currency object structure

    /**
     * Get the ISO code of the currency
     * Shopify compatible: currency.iso_code
     */
    public function iso_code(): string
    {
        return $this->code();
    }

    /**
     * Get the symbol of the currency
     * Shopify compatible: currency.symbol
     */
    public function symbol(): string
    {
        // Return the left sign if it exists, otherwise return the right sign
        $leftSign = $this->sign_left();
        if (!empty($leftSign)) {
            return $leftSign;
        }
        
        return $this->sign_right();
    }

    /**
     * Get currency symbol for display (alias for symbol)
     * Shopify compatible: currency.symbol
     */
    public function currency_symbol(): string
    {
        return $this->symbol();
    }

    /**
     * Check if currency uses symbol before amount
     * Shopify compatible pattern
     */
    public function symbol_first(): bool
    {
        return !empty($this->sign_left());
    }

    /**
     * Get the decimal separator
     * Shopify compatible: currency.decimal_mark
     */
    public function decimal_mark(): string
    {
        return $this->decimal_point();
    }

    /**
     * Get the thousands separator
     * Shopify compatible: currency.thousands_separator
     */
    public function thousands_sep(): string
    {
        return $this->thousands_separator();
    }

    /**
     * Get currency format for money display
     * Shopify compatible: currency.format
     */
    public function format(): string
    {
        return $this->pattern();
    }

    /**
     * Get money format without currency symbol
     * Shopify compatible pattern
     */
    public function money_without_currency_format(): string
    {
        $pattern = $this->pattern();
        // Remove currency symbols from pattern
        $pattern = str_replace($this->sign_left(), '', $pattern);
        $pattern = str_replace($this->sign_right(), '', $pattern);
        return trim($pattern);
    }

    /**
     * Get money format with currency symbol
     * Shopify compatible: currency.money_format
     */
    public function money_format(): string
    {
        return $this->pattern();
    }

    /**
     * Get money format with currency code instead of symbol
     * Shopify compatible pattern
     */
    public function money_with_currency_format(): string
    {
        $pattern = $this->pattern();
        // Replace symbols with currency code
        if (!empty($this->sign_left())) {
            $pattern = str_replace($this->sign_left(), $this->code() . ' ', $pattern);
        }
        if (!empty($this->sign_right())) {
            $pattern = str_replace($this->sign_right(), ' ' . $this->code(), $pattern);
        }
        return $pattern;
    }

    /**
     * Get number of decimal places (coins padding)
     * Shopify compatible pattern
     */
    public function decimal_places(): int
    {
        return $this->coins_pad();
    }

    // END: Shopify-compatible methods

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {

        return [
            'name' => $this->name(),
            'code' => $this->code(),
            'iso_code' => $this->iso_code(),
            'symbol' => $this->symbol(),
            'decimal_point' => $this->decimal_point(),
            'decimal_mark' => $this->decimal_mark(),
            'thousands_separator' => $this->thousands_separator(),
            'thousands_sep' => $this->thousands_sep(),
            'coins_pad' => $this->coins_pad(),
            'decimal_places' => $this->decimal_places(),
            'sign_left' => $this->sign_left(),
            'sign_right' => $this->sign_right(),
            'pattern' => $this->pattern(),
            'format' => $this->format(),
            'money_format' => $this->money_format(),
            'money_without_currency_format' => $this->money_without_currency_format(),
            'money_with_currency_format' => $this->money_with_currency_format(),
            'symbol_first' => $this->symbol_first(),
        ];
    }

    /**
     * {@inheritDoc}
     */
    public function __toString(): string
    {
        return $this->code();
    }
}
