<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Support\Facades\Storage;

/**
 * Class Image
 *
 * Provides image handling functionality for Liquid templates.
 * Implements Shopify-compatible image methods.
 *
 * @property-read string $src Image source URL
 * @property-read string $alt Image alt text
 * @property-read int $width Image width
 * @property-read int $height Image height
 * @property-read string $format Image format
 * @property-read array $sizes Available image sizes
 * @property-read array $attributes Image HTML attributes
 */
class Image extends AbstractDrop
{
    /**
     * Image source URL.
     *
     * @var string
     */
    protected string $src;

    /**
     * Image alt text.
     *
     * @var string
     */
    protected string $alt;

    /**
     * Image width.
     *
     * @var int
     */
    protected int $width;

    /**
     * Image height.
     *
     * @var int
     */
    protected int $height;

    /**
     * Image format.
     *
     * @var string
     */
    protected string $format;

    /**
     * Available image sizes.
     *
     * @var array<int, array{
     *     width: int,
     *     height: int,
     *     url: string,
     *     format: string
     * }>
     */
    protected array $sizes;

    /**
     * Image HTML attributes.
     *
     * @var array<string, string>
     */
    protected array $attributes;

    /**
     * Create a new Image instance.
     *
     * @param string $src Image source URL
     * @param string $alt Image alt text
     * @param int $width Image width
     * @param int $height Image height
     * @param string $format Image format
     * @param array<int, array{width: int, height: int, url: string, format: string}> $sizes Available image sizes
     * @param array<string, string> $attributes Image HTML attributes
     */
    public function __construct(
        string $src,
        string $alt = '',
        int $width = 0,
        int $height = 0,
        string $format = 'jpg',
        array $sizes = [],
        array $attributes = []
    ) {
        $this->src = $src;
        $this->alt = $alt;
        $this->width = $width;
        $this->height = $height;
        $this->format = $format;
        $this->sizes = $sizes;
        $this->attributes = array_merge([
            'src' => $src,
            'alt' => $alt,
            'width' => (string) $width,
            'height' => (string) $height,
            'loading' => 'lazy',
            'class' => 'img-fluid'
        ], $attributes);
    }

    /**
     * Get image source URL.
     *
     * @return string
     */
    public function src(): string
    {
        return $this->src;
    }

    /**
     * Get image alt text.
     *
     * @return string
     */
    public function alt(): string
    {
        return $this->alt;
    }

    /**
     * Get image width.
     *
     * @return int
     */
    public function width(): int
    {
        return $this->width;
    }

    /**
     * Get image height.
     *
     * @return int
     */
    public function height(): int
    {
        return $this->height;
    }

    /**
     * Get image format.
     *
     * @return string
     */
    public function format(): string
    {
        return $this->format;
    }

    /**
     * Get available image sizes.
     *
     * @return array<int, array{
     *     width: int,
     *     height: int,
     *     url: string,
     *     format: string
     * }>
     */
    public function sizes(): array
    {
        return $this->sizes;
    }

    /**
     * Get image HTML attributes.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return $this->attributes;
    }

    /**
     * Get image URL for specific size.
     *
     * @param int $width Desired width
     * @param int $height Desired height
     * @return string
     */
    public function url(int $width = 0, int $height = 0): string
    {
        if ($width === 0 && $height === 0) {
            return $this->src;
        }

        $size = collect($this->sizes)->first(function ($size) use ($width, $height) {
            return $size['width'] === $width && $size['height'] === $height;
        });

        return $size ? (string)$size['url'] : $this->src;
    }

    /**
     * Get image URL for specific format.
     *
     * @param string $format Desired format
     * @return string
     */
    public function format_url(string $format): string
    {
        $size = collect($this->sizes)->first(function ($size) use ($format) {
            return $size['format'] === $format;
        });

        return $size ? (string)$size['url'] : $this->src;
    }

    /**
     * Get image URL for specific size and format.
     *
     * @param int $width Desired width
     * @param int $height Desired height
     * @param string $format Desired format
     * @return string
     */
    public function size_url(int $width, int $height, string $format): string
    {
        $size = collect($this->sizes)->first(function ($size) use ($width, $height, $format) {
            return $size['width'] === $width && $size['height'] === $height && $size['format'] === $format;
        });

        return $size ? (string)$size['url'] : $this->src;
    }

    /**
     * Get image aspect ratio.
     *
     * @return float
     */
    public function aspect_ratio(): float
    {
        if ($this->height === 0) {
            return 0.0;
        }

        return $this->width / $this->height;
    }

    /**
     * Get image file size.
     *
     * @return int
     */
    public function file_size(): int
    {
        if (!Storage::exists($this->src)) {
            return 0;
        }

        return Storage::size($this->src);
    }

    /**
     * Get image file name.
     *
     * @return string
     */
    public function file_name(): string
    {
        return basename($this->src);
    }

    /**
     * Get image file extension.
     *
     * @return string
     */
    public function file_extension(): string
    {
        return pathinfo($this->src, PATHINFO_EXTENSION);
    }

    /**
     * Get accessibility attributes.
     *
     * @return array{
     *     aria_label: string,
     *     role: string
     * }
     */
    public function accessibility(): array
    {
        return [
            'aria_label' => $this->alt ?: 'Image',
            'role' => 'img'
        ];
    }

    /**
     * Get structured data for SEO.
     *
     * @return array<string, mixed>
     */
    public function structured_data(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'ImageObject',
            'contentUrl' => $this->src,
            'width' => $this->width,
            'height' => $this->height,
            'caption' => $this->alt,
            'encodingFormat' => $this->format,
            'sizes' => array_map(function ($size) {
                return [
                    'width' => $size['width'],
                    'height' => $size['height'],
                    'url' => $size['url'],
                    'format' => $size['format']
                ];
            }, $this->sizes)
        ];
    }

    /**
     * Convert to Shopify format.
     *
     * @return array{
     *     src: string,
     *     alt: string,
     *     width: int,
     *     height: int,
     *     format: string,
     *     sizes: array<int, array{width: int, height: int, url: string, format: string}>,
     *     attributes: array<string, string>,
     *     aspect_ratio: float,
     *     file_size: int,
     *     file_name: string,
     *     file_extension: string,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toShopifyArray(): array
    {
        return [
            'src' => $this->src(),
            'alt' => $this->alt(),
            'width' => $this->width(),
            'height' => $this->height(),
            'format' => $this->format(),
            'sizes' => $this->sizes(),
            'attributes' => $this->attributes(),
            'aspect_ratio' => $this->aspect_ratio(),
            'file_size' => $this->file_size(),
            'file_name' => $this->file_name(),
            'file_extension' => $this->file_extension(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to SEO array format.
     *
     * @return array{
     *     src: string,
     *     alt: string,
     *     width: int,
     *     height: int,
     *     format: string,
     *     sizes: array<int, array{width: int, height: int, url: string, format: string}>,
     *     attributes: array<string, string>,
     *     aspect_ratio: float,
     *     file_size: int,
     *     file_name: string,
     *     file_extension: string,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toSeoArray(): array
    {
        return [
            'src' => $this->src(),
            'alt' => $this->alt(),
            'width' => $this->width(),
            'height' => $this->height(),
            'format' => $this->format(),
            'sizes' => $this->sizes(),
            'attributes' => $this->attributes(),
            'aspect_ratio' => $this->aspect_ratio(),
            'file_size' => $this->file_size(),
            'file_name' => $this->file_name(),
            'file_extension' => $this->file_extension(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Convert to analytics array format.
     *
     * @return array{
     *     src: string,
     *     alt: string,
     *     width: int,
     *     height: int,
     *     format: string,
     *     sizes: array<int, array{width: int, height: int, url: string, format: string}>,
     *     attributes: array<string, string>,
     *     aspect_ratio: float,
     *     file_size: int,
     *     file_name: string,
     *     file_extension: string,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string}
     * }
     */
    public function toAnalyticsArray(): array
    {
        return [
            'src' => $this->src(),
            'alt' => $this->alt(),
            'width' => $this->width(),
            'height' => $this->height(),
            'format' => $this->format(),
            'sizes' => $this->sizes(),
            'attributes' => $this->attributes(),
            'aspect_ratio' => $this->aspect_ratio(),
            'file_size' => $this->file_size(),
            'file_name' => $this->file_name(),
            'file_extension' => $this->file_extension(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility()
        ];
    }

    /**
     * Get the collection as a plain array.
     *
     * @return array{
     *     src: string,
     *     alt: string,
     *     width: int,
     *     height: int,
     *     format: string,
     *     sizes: array<int, array{width: int, height: int, url: string, format: string}>,
     *     attributes: array<string, string>,
     *     aspect_ratio: float,
     *     file_size: int,
     *     file_name: string,
     *     file_extension: string,
     *     structured_data: array<string, mixed>,
     *     accessibility: array{aria_label: string, role: string},
     *     shopify: array{src: string, alt: string, width: int, height: int, format: string, sizes: array<int, array{width: int, height: int, url: string, format: string}>, attributes: array<string, string>, aspect_ratio: float, file_size: int, file_name: string, file_extension: string, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     seo: array{src: string, alt: string, width: int, height: int, format: string, sizes: array<int, array{width: int, height: int, url: string, format: string}>, attributes: array<string, string>, aspect_ratio: float, file_size: int, file_name: string, file_extension: string, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}},
     *     analytics: array{src: string, alt: string, width: int, height: int, format: string, sizes: array<int, array{width: int, height: int, url: string, format: string}>, attributes: array<string, string>, aspect_ratio: float, file_size: int, file_name: string, file_extension: string, structured_data: array<string, mixed>, accessibility: array{aria_label: string, role: string}>
     * }
     */
    public function toArray(): array
    {
        return [
            'src' => $this->src(),
            'alt' => $this->alt(),
            'width' => $this->width(),
            'height' => $this->height(),
            'format' => $this->format(),
            'sizes' => $this->sizes(),
            'attributes' => $this->attributes(),
            'aspect_ratio' => $this->aspect_ratio(),
            'file_size' => $this->file_size(),
            'file_name' => $this->file_name(),
            'file_extension' => $this->file_extension(),
            'structured_data' => $this->structured_data(),
            'accessibility' => $this->accessibility(),
            'shopify' => $this->toShopifyArray(),
            'seo' => $this->toSeoArray(),
            'analytics' => $this->toAnalyticsArray()
        ];
    }
}
