<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Helper\Invoice as HelperInvoiceNumber;
use App\Helper\YesNo;
use App\Locale\Language;
use App\Models\Order\OrderProduct;
use App\Models\Order\OrderTax;
use App\Models\Order\OrderTotals;
use App\Models\Order\Order as OrderModel;
use App\LiquidEngine\LiquidHelpers\Drop\Order\Address;
use App\LiquidEngine\LiquidHelpers\Drop\Order\Invoice;
use App\LiquidEngine\LiquidHelpers\Drop\Order\Payment;
use App\LiquidEngine\LiquidHelpers\Drop\Order\Shipping;
use App\LiquidEngine\LiquidHelpers\Drop\Order\Status;
use App\LiquidEngine\LiquidHelpers\Drop\Order\Tax;
use App\LiquidEngine\LiquidHelpers\Drop\Order\Total;
use App\LiquidEngine\LiquidHelpers\Drop\Order\Product as DropOrderProduct;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use Throwable;

/**
 * Order Drop Class
 *
 * This class represents an order in Liquid templates, providing access to order details,
 * customer information, shipping/billing addresses, payment information, and various
 * Shopify-compatible methods for order management. It wraps the OrderModel and exposes
 * order-specific details for use in Liquid templates.
 *
 * Usage:
 *   {{ order.id }}
 *   {{ order.order_number }}
 *   {{ order.customer }}
 *   {{ order.shipping_address }}
 *   {{ order.billing_address }}
 *   {{ order.totals }}
 *   {{ order.taxes }}
 *   {{ order.products }}
 *   {{ order.payment }}
 *   {{ order.shipping }}
 *   {{ order.status }}
 *   {{ order.invoice }}
 *   {{ order.url }}
 *
 * Properties:
 * - id: int
 * - order_number: string|int
 * - title: string|null
 * - sub_title: string|null
 * - customer: Customer|null
 * - shipping_address: Address|null
 * - billing_address: Address|null
 * - allow_invoicing: bool
 * - totals: Total[]
 * - taxes: Tax[]
 * - products: Product[]
 * - digital_products_url: string|null
 * - payment: Payment|null
 * - shipping: Shipping|null
 * - weight: float
 * - weight_input: float
 * - weight_formatted: string
 * - price: float
 * - price_input: float
 * - price_formatted: string
 * - price_parts: array
 * - status: Status
 * - invoice: Invoice|null
 * - locale: string
 * - currency: Currency
 * - date: Date
 * - is_fulfilled: bool
 * - download_invoice: string|null
 * - print_invoice: string|null
 * - url: string
 * - cancel_reason: string|null
 * - cancel_reason_label: string|null
 * - cancelled: bool
 * - cancelled_at: Date|null
 * - confirmation_number: string|null
 * - created_at: Date
 * - customer_url: string|null
 * - customer_order_url: string|null
 * - financial_status_label: string
 * - fulfillment_status_label: string
 * - order_status_url: string
 * - pickup_in_store: bool
 * - pickup_in_store_question: bool
 * - subtotal_line_items: array
 * - subtotal_price: float
 * - total_duties: float
 * - total_net_amount: float
 * - total_refunded_amount: float
 * - updated_at: Date
 *
 * @package App\LiquidEngine\LiquidHelpers\Drop
 */
class Order extends AbstractDrop
{
    /**
     * The underlying order model instance.
     * Contains the raw order data from the database.
     *
     * @var OrderModel
     */
    protected OrderModel $_order;

    /**
     * Create a new Order instance.
     * Initializes the drop with an OrderModel instance.
     *
     * @param OrderModel $order The order model instance
     */
    public function __construct(OrderModel $order)
    {
        $this->_order = $order;
    }

    /**
     * Get the order ID.
     * Returns the primary key of the underlying order model.
     *
     * @return int The order ID
     */
    public function id(): int
    {
        return $this->_order->id;
    }

    /**
     * Get the order number.
     * Returns the order number from the underlying order model.
     *
     * @return int|string The order number
     */
    public function order_number(): int|string
    {
        return $this->_order->order_number;
    }

    /**
     * Get the order title based on payment status.
     * Returns a localized title based on the payment status.
     *
     * @return string|null The order title
     */
    public function title(): ?string
    {
        if ($this->payment() === null) {
            return __('order.text.title_by_status.requested');
        }

        $status = $this->payment()->status()->key();
        $title = __('order.text.title_by_status.' . $status);

        if ('requested' === $status) {
            if (!in_array($this->payment()->code(), ['cod', 'bwt', 'pop']) && !$this->payment()->is_leasing()) {
                $title = __('order.text.title_by_status.' . $status);
            } else {
                $title = __('order.text.title_by_status.pending');
            }
        }

        return $title;
    }

    /**
     * Get the order subtitle based on payment status.
     * Returns a localized subtitle based on the payment status.
     *
     * @return string|null The order subtitle
     */
    public function sub_title(): ?string
    {
        if ($this->payment() === null) {
            return __('order.text.sub_title_by_status.requested');
        }

        $status = $this->payment()->status()->key();
        $title = __('order.text.sub_title_by_status.' . $status);

        if ('requested' === $status) {
            if (!in_array($this->payment()->code(), ['cod', 'bwt', 'pop']) && !$this->payment()->is_leasing()) {
                $title = __('order.text.sub_title_by_status.processed');
            }
        }

        return $title;
    }

    /**
     * Get the customer associated with the order.
     * Returns a Customer drop instance if the customer relation is loaded.
     *
     * @return Customer|null The customer drop instance
     */
    public function customer(): ?Customer
    {
        if ($this->_order->relationLoaded('customer') && $this->_order->customer) {
            return new Customer($this->_order->customer);
        }

        return null;
    }

    /**
     * Get the shipping address.
     * Returns an Address drop instance if the shipping address relation is loaded.
     *
     * @return Address|null The shipping address drop instance
     */
    public function shipping_address(): ?Address
    {
        if ($this->_order->relationLoaded('shippingAddress') && $this->_order->shippingAddress) {
            return new Address($this->_order->shippingAddress);
        }

        return null;
    }

    /**
     * Get the billing address.
     * Returns an Address drop instance if the billing address relation is loaded.
     *
     * @return Address|null The billing address drop instance
     */
    public function billing_address(): ?Address
    {
        if ($this->_order->relationLoaded('billingAddress') && $this->_order->billingAddress) {
            return new Address($this->_order->billingAddress);
        }

        return null;
    }

    /**
     * Check if invoicing is allowed.
     * Returns whether invoicing is allowed for the order.
     *
     * @return bool Whether invoicing is allowed
     */
    public function allow_invoicing(): bool
    {
        return $this->_order->allow_invoicing;
    }

    /**
     * Get the order totals.
     * Returns an array of Total drop instances if the totals relation is loaded.
     *
     * @return Total[] The order totals
     */
    public function totals(): array
    {
        $totals = [];
        if ($this->_order->relationLoaded('totals') && $this->_order->totals) {
            $totals = $this->_order->totals->map(function (OrderTotals $total): Total {
                return new Total($total, $this->_order->locale);
            })->all();
        }

        return $totals;
    }

    /**
     * Get the order taxes.
     * Returns an array of Tax drop instances if the taxes relation is loaded.
     *
     * @return Tax[] The order taxes
     */
    public function taxes(): array
    {
        $taxes = [];
        if ($this->_order->relationLoaded('taxes') && $this->_order->taxes) {
            $taxes = $this->_order->taxes->map(function (OrderTax $tax): Tax {
                return new Tax($tax, $this->_order->locale, $this->_order->currency);
            })->all();
        }

        return $taxes;
    }

    /**
     * Get the order products.
     * Returns an array of Product drop instances if the products relation is loaded.
     *
     * @return DropOrderProduct[] The order products
     */
    public function products(): array
    {
        $products = [];
        if ($this->_order->relationLoaded('products') && $this->_order->products) {
            $products = $this->_order->products->map(function (OrderProduct $product): DropOrderProduct {
                return new DropOrderProduct($product, $this->_order->locale, $this->_order->currency);
            })->all();
        }

        return $products;
    }

    /**
     * Get the URL for digital products.
     * Returns the URL for accessing digital products associated with the order.
     *
     * @return string|null The digital products URL
     */
    public function digital_products_url(): ?string
    {
        if ($this->status()->key() !== 'completed' || !$this->_order->relationLoaded('products')) {
            return null;
        }

        return route('site.account.files', ['order_product_ids' => $this->_order->products->where('digital', YesNo::True)->pluck('id')->all()]);
    }

    /**
     * Get the payment information.
     * Returns a Payment drop instance if the payment relation is loaded.
     *
     * @return Payment|null The payment drop instance
     */
    public function payment(): ?Payment
    {
        if ($this->_order->relationLoaded('payment') && $this->_order->payment) {
            return new Payment($this->_order->payment, $this->_order->locale, $this->_order->currency);
        }

        return null;
    }

    /**
     * Get the shipping information.
     * Returns a Shipping drop instance if the shipping relation is loaded.
     *
     * @return Shipping|null The shipping drop instance
     */
    public function shipping(): ?Shipping
    {
        if ($this->_order->relationLoaded('shipping') && $this->_order->shipping) {
            return new Shipping(
                $this->_order->shipping,
                $this->_order->status_fulfillment,
                $this->_order->relationLoaded('fulfillment') ? $this->_order->fulfillment : null,
                $this->_order->status_fulfillment_color,
                $this->_order->locale
            );
        }

        return null;
    }

    /**
     * Get the order weight.
     * Returns the total weight of the order.
     *
     * @return float The order weight
     */
    public function weight(): float
    {
        return $this->_order->weight;
    }

    /**
     * Get the raw weight input.
     * Returns the raw weight value from the order.
     *
     * @return float The raw weight input
     */
    public function weight_input(): float
    {
        return $this->_order->weight_input;
    }

    /**
     * Get the formatted weight.
     * Returns the weight formatted with the appropriate unit.
     *
     * @return string The formatted weight
     */
    public function weight_formatted(): string
    {
        return $this->_order->weight_formatted;
    }

    /**
     * Get the total price.
     * Returns the total price of the order.
     *
     * @return float The total price
     */
    public function price(): float
    {
        return $this->_order->price_total;
    }

    /**
     * Get the raw price input.
     * Returns the raw price value from the order.
     *
     * @return float The raw price input
     */
    public function price_input(): float
    {
        return $this->_order->price_total_input;
    }

    /**
     * Get the formatted price.
     * Returns the price formatted with the appropriate currency.
     *
     * @return string The formatted price
     */
    public function price_formatted(): string
    {
        return $this->_order->price_total_formatted;
    }

    /**
     * Get the price parts.
     * Returns an array of price components (currency, amount, etc.).
     *
     * @return array The price parts
     */
    public function price_parts(): array
    {
        return PriceParts::format($this->price_input(), $this->_order->currency, $this->_order->locale);
    }

    /**
     * Get the order status.
     * Returns a Status drop instance representing the order status.
     *
     * @return Status The order status
     */
    public function status(): Status
    {
        return new Status(
            $this->_order->status,
            $this->_order->status_color,
            'order',
            $this->_order->locale
        );
    }

    /**
     * Get the order invoice.
     * Returns an Invoice drop instance if the invoice relation is loaded.
     *
     * @return Invoice|null The invoice drop instance
     * @throws Throwable
     */
    public function invoice(): ?Invoice
    {
        if (!$this->_order->invoice_number) {
            return null;
        }

        return new Invoice(
            $this->_order->invoice_number,
            (new HelperInvoiceNumber($this->_order))->formatNumber(),
            $this->_order->invoice_date
        );
    }

    /**
     * Get the order locale.
     * Returns the locale code for the order.
     *
     * @return string The order locale
     */
    public function locale(): string
    {
        return Language::get($this->_order->locale);
    }

    /**
     * Get the order currency.
     * Returns a Currency drop instance representing the order currency.
     *
     * @return Currency The order currency
     */
    public function currency(): Currency
    {
        return new Currency($this->_order->currency, $this->_order->locale);
    }

    /**
     * Get the order date.
     * Returns a Date drop instance representing when the order was created.
     *
     * @return Date The order date
     */
    public function date(): Date
    {
        return new Date($this->_order->date_added);
    }

    /**
     * Check if the order is fulfilled.
     * Returns whether the order has been fulfilled.
     *
     * @return bool Whether the order is fulfilled
     */
    public function is_fulfilled(): bool
    {
        return $this->_order->status_fulfillment === 'fulfilled';
    }

    /**
     * Get the URL to download the invoice.
     * Returns the URL for downloading the order invoice.
     *
     * @return string|null The download invoice URL
     */
    public function download_invoice(): ?string
    {
        if ($this->_order->invoice_number && $this->_order->allow_invoicing) {
            return route('invoice.download', ['hash' => encrypt($this->id())]);
        }

        return null;
    }

    /**
     * Get the URL to print the invoice.
     * Returns the URL for printing the order invoice.
     *
     * @return string|null The print invoice URL
     */
    public function print_invoice(): ?string
    {
        if ($this->_order->invoice_number && $this->_order->allow_invoicing) {
            return route('invoice.display', ['hash' => encrypt($this->id())]);
        }

        return null;
    }

    /**
     * Get the order URL.
     *
     * @return string
     */
    public function url(): string
    {
        return route('site.account.order', ['order_id' => $this->id()]);
    }

    /**
     * Get the order cancellation reason.
     *
     * @return string|null
     */
    public function cancel_reason(): ?string
    {
        return $this->_order->cancel_reason;
    }

    /**
     * Get the formatted cancellation reason.
     *
     * @return string|null
     */
    public function cancel_reason_label(): ?string
    {
        $labels = [
            'customer' => 'Customer',
            'declined' => 'Declined',
            'fraud' => 'Fraud',
            'inventory' => 'Inventory',
            'staff' => 'Staff',
            'other' => 'Other'
        ];

        return $this->_order->cancel_reason ?
            ($labels[$this->_order->cancel_reason] ?? ucfirst($this->_order->cancel_reason)) :
            null;
    }

    /**
     * Check if the order is cancelled.
     *
     * @return bool
     */
    public function cancelled(): bool
    {
        return (bool) $this->_order->cancelled;
    }

    /**
     * Get when the order was cancelled.
     *
     * @return Date|null
     */
    public function cancelled_at(): ?Date
    {
        return $this->_order->cancelled_at ? new Date($this->_order->cancelled_at) : null;
    }

    /**
     * Get the order confirmation number.
     *
     * @return string|null
     */
    public function confirmation_number(): ?string
    {
        return $this->_order->confirmation_number;
    }

    /**
     * Get when the order was created.
     *
     * @return Date
     */
    public function created_at(): Date
    {
        return new Date($this->_order->created_at);
    }

    /**
     * Get the customer's order URL.
     *
     * @return string|null
     */
    public function customer_url(): ?string
    {
        if ($this->_order->customer) {
            return "/account/orders/{$this->_order->id}";
        }
        return null;
    }

    /**
     * Get the customer's order details URL.
     *
     * @return string|null
     */
    public function customer_order_url(): ?string
    {
        if ($this->_order->customer) {
            return "/account/orders/{$this->_order->id}";
        }
        return null;
    }

    /**
     * Get the financial status label.
     *
     * @return string
     */
    public function financial_status_label(): string
    {
        $labels = [
            'authorized' => 'Authorized',
            'expired' => 'Expired',
            'paid' => 'Paid',
            'partially_paid' => 'Partially Paid',
            'partially_refunded' => 'Partially Refunded',
            'pending' => 'Pending',
            'refunded' => 'Refunded',
            'unpaid' => 'Unpaid',
            'voided' => 'Voided'
        ];

        return $labels[$this->_order->financial_status] ?? ucfirst($this->_order->financial_status);
    }

    /**
     * Get the fulfillment status label.
     *
     * @return string
     */
    public function fulfillment_status_label(): string
    {
        $labels = [
            'complete' => 'Complete',
            'fulfilled' => 'Fulfilled',
            'partial' => 'Partial',
            'restocked' => 'Restocked',
            'unfulfilled' => 'Unfulfilled'
        ];

        return $labels[$this->_order->fulfillment_status] ?? ucfirst($this->_order->fulfillment_status);
    }

    /**
     * Get the order status URL.
     *
     * @return string
     */
    public function order_status_url(): string
    {
        return "/orders/{$this->_order->order_number}/status";
    }

    /**
     * Check if it's a store pickup order.
     *
     * @return bool
     */
    public function pickup_in_store(): bool
    {
        return $this->_order->pickup_in_store ?? false;
    }

    /**
     * Alias for pickup_in_store().
     *
     * @return bool
     */
    public function pickup_in_store_question(): bool
    {
        return $this->pickup_in_store();
    }

    /**
     * Get the non-tip line items.
     *
     * @return array
     */
    public function subtotal_line_items(): array
    {
        return array_filter($this->products(), function($lineItem) {
            return !($lineItem->model->is_tip ?? false);
        });
    }

    /**
     * Get the subtotal price.
     *
     * @return float
     */
    public function subtotal_price(): float
    {
        $subtotalItems = $this->subtotal_line_items();
        $total = array_reduce($subtotalItems, function($sum, $item) {
            return $sum + $item->price();
        }, 0.0);

        return $total;
    }

    /**
     * Get the total duties.
     *
     * @return float
     */
    public function total_duties(): float
    {
        return (float) ($this->_order->total_duties ?? 0);
    }

    /**
     * Get the net amount.
     *
     * @return float
     */
    public function total_net_amount(): float
    {
        return (float) ($this->_order->total_net_amount ?? $this->price());
    }

    /**
     * Get the refunded amount.
     *
     * @return float
     */
    public function total_refunded_amount(): float
    {
        return (float) ($this->_order->total_refunded_amount ?? 0);
    }

    /**
     * Get when the order was last updated.
     *
     * @return Date
     */
    public function updated_at(): Date
    {
        return new Date($this->_order->updated_at);
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array{
     *     id: int,
     *     name: string,
     *     order_number: int|string,
     *     created_at: string,
     *     updated_at: string,
     *     cancelled_at: string|null,
     *     financial_status: string,
     *     financial_status_label: string,
     *     fulfillment_status: string,
     *     fulfillment_status_label: string,
     *     cancelled: bool,
     *     customer: array|null,
     *     email: string|null,
     *     phone: string|null,
     *     billing_address: array|null,
     *     shipping_address: array|null,
     *     total_price: float,
     *     subtotal_price: float,
     *     total_tax: float,
     *     shipping_price: float|null,
     *     total_discounts: float,
     *     total_duties: float,
     *     total_net_amount: float,
     *     total_refunded_amount: float,
     *     line_items: array,
     *     item_count: int,
     *     note: string|null,
     *     confirmation_number: string|null,
     *     cancel_reason: string|null,
     *     cancel_reason_label: string|null,
     *     pickup_in_store: bool,
     *     customer_url: string|null,
     *     customer_order_url: string|null,
     *     order_status_url: string,
     *     attributes: array,
     *     tags: array
     * }
     * @throws Throwable
     */
    public function toArray(): array
    {
        return [
            // Core identifiers
            'id' => $this->id(),
            'name' => $this->title(),
            'order_number' => $this->order_number(),

            // Timestamps
            'created_at' => $this->created_at()->toISOString(),
            'updated_at' => $this->updated_at()->toISOString(),
            'cancelled_at' => $this->cancelled_at()?->toISOString(),

            // Status fields
            'financial_status' => $this->status()->key(),
            'financial_status_label' => $this->financial_status_label(),
            'fulfillment_status' => $this->is_fulfilled() ? 'fulfilled' : 'unfulfilled',
            'fulfillment_status_label' => $this->fulfillment_status_label(),
            'cancelled' => $this->cancelled(),

            // Customer information
            'customer' => $this->customer()?->toArray(),
            'email' => $this->customer()?->email(),
            'phone' => $this->customer()?->phone(),

            // Addresses
            'billing_address' => $this->billing_address()?->toArray(),
            'shipping_address' => $this->shipping_address()?->toArray(),

            // Pricing
            'total_price' => $this->price(),
            'subtotal_price' => $this->subtotal_price(),
            'total_tax' => $this->taxes()[0]->amount(),
            'shipping_price' => $this->shipping()?->price(),
            'total_discounts' => $this->totals()[0]->discount(),
            'total_duties' => $this->total_duties(),
            'total_net_amount' => $this->total_net_amount(),
            'total_refunded_amount' => $this->total_refunded_amount(),

            // Items and counts
            'line_items' => array_map(fn($item) => $item->toArray(), $this->products()),
            'item_count' => count($this->products()),

            // Additional information
            'note' => $this->_order->note,
            'confirmation_number' => $this->confirmation_number(),
            'cancel_reason' => $this->cancel_reason(),
            'cancel_reason_label' => $this->cancel_reason_label(),
            'pickup_in_store' => $this->pickup_in_store(),

            // URLs
            'customer_url' => $this->customer_url(),
            'customer_order_url' => $this->customer_order_url(),
            'order_status_url' => $this->order_status_url(),

            // Attributes and tags
            'attributes' => $this->_order->attributes,
            'tags' => $this->_order->tags,
        ];
    }
}
