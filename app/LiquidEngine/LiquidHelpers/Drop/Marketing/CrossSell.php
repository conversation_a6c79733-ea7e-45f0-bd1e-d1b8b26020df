<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Marketing;

use App\Models\Marketing\CrossSell\CrossSell as CrossSellModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

/**
 * CrossSell Drop class for handling cross-sell offers in Liquid templates
 * 
 * @property-read string $name The title of the cross-sell offer
 * @property-read string $type The type of offer (always 'cross_sell')
 * @property-read string $description The description of the cross-sell offer
 * @property-read CrossSell|UpSell|null $accept The offer shown when accepted
 * @property-read CrossSell|UpSell|null $decline The offer shown when declined
 */
class CrossSell extends AbstractDrop
{
    /**
     * @var CrossSellModel
     */
    protected CrossSellModel $_crossSell;

    /**
     * CrossSell constructor.
     * 
     * @param CrossSellModel $crossSell
     */
    public function __construct(CrossSellModel $crossSell)
    {
        $this->_crossSell = $crossSell;
    }

    /**
     * Get the name/title of the cross-sell offer
     * 
     * @return string
     */
    public function name(): string
    {
        return $this->_crossSell->offer_title;
    }

    /**
     * Get the type of offer
     * 
     * @return string
     */
    public function type(): string
    {
        return 'cross_sell';
    }

    /**
     * Get the description of the cross-sell offer
     * 
     * @return string
     */
    public function description(): string
    {
        return $this->_crossSell->description;
    }

    /**
     * Get the offer shown when declined
     * 
     * @return self|UpSell|null
     */
    public function decline(): self|UpSell|null
    {
        if ($this->_crossSell->relationLoaded('child_no_detailed') && $this->_crossSell->child_no_detailed) {
            if ($this->_crossSell->child_no_detailed instanceof CrossSellModel) {
                return new static($this->_crossSell->child_no_detailed);
            } else {
                return new UpSell($this->_crossSell->child_no_detailed);
            }
        }

        return null;
    }

    /**
     * Get the offer shown when accepted
     * 
     * @return self|UpSell|null
     */
    public function accept(): self|UpSell|null
    {
        if ($this->_crossSell->relationLoaded('child_yes_detailed') && $this->_crossSell->child_yes_detailed) {
            if ($this->_crossSell->child_yes_detailed instanceof CrossSellModel) {
                return new static($this->_crossSell->child_yes_detailed);
            } else {
                return new UpSell($this->_crossSell->child_yes_detailed);
            }
        }

        return null;
    }

    /**
     * Get the cross-sell offer as a plain array
     * 
     * @return array{
     *     type: string,
     *     name: string,
     *     description: string,
     *     accept: self|UpSell|null,
     *     decline: self|UpSell|null
     * }
     */
    public function toArray(): array
    {
        return [
            'type' => $this->type(),
            'name' => $this->name(),
            'description' => $this->description(),
            'accept' => $this->accept(),
            'decline' => $this->decline(),
        ];
    }
}
