<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\Marketing;

use App\Exceptions\Error;
use App\Models\Marketing\UpSell\UpSell as UpSellModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Variant;
use App\LiquidEngine\LiquidHelpers\Drop\ProductDetails;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;

/**
 * UpSell Drop class for handling up-sell offers in Liquid templates
 * 
 * @property-read int $id The ID of the up-sell offer
 * @property-read string $name The title of the up-sell offer
 * @property-read string $type The type of offer (always 'up_sell')
 * @property-read string $description The description of the up-sell offer
 * @property-read ProductDetails|null $trigger_product The product that triggers this offer
 * @property-read Variant|null $trigger_variant The variant that triggers this offer
 * @property-read ProductDetails|null $offer_product The product being offered
 * @property-read Variant|null $offer_variant The variant being offered
 * @property-read bool $only_additional_cost Whether only additional cost is shown
 * @property-read float $only The price difference
 * @property-read float $only_input The raw price difference
 * @property-read string $only_formatted The formatted price difference
 * @property-read array $only_parts The price difference parts
 * @property-read bool $offer_override_price Whether the offer price is overridden
 * @property-read float|null $offer_price The overridden offer price
 * @property-read float|null $offer_price_input The raw overridden offer price
 * @property-read string|null $offer_price_formatted The formatted overridden offer price
 * @property-read array|null $offer_price_parts The overridden offer price parts
 * @property-read float|null $trigger_price The trigger product price
 * @property-read float|null $trigger_price_input The raw trigger product price
 * @property-read string|null $trigger_price_formatted The formatted trigger product price
 * @property-read array|null $trigger_price_parts The trigger product price parts
 * @property-read bool $match_quantity Whether to match quantities
 * @property-read UpSell|CrossSell|null $decline The offer shown when declined
 * @property-read UpSell|CrossSell|null $accept The offer shown when accepted
 */
class UpSell extends AbstractDrop
{
    /**
     * @var UpSellModel
     */
    protected UpSellModel $_upSell;

    /**
     * UpSell constructor.
     * 
     * @param UpSellModel $upSell
     */
    public function __construct(UpSellModel $upSell)
    {
        $this->_upSell = $upSell->format();
    }

    /**
     * Get the ID of the up-sell offer
     * 
     * @return int
     */
    public function id(): int
    {
        return $this->_upSell->id;
    }

    /**
     * Get the name/title of the up-sell offer
     * 
     * @return string
     */
    public function name(): string
    {
        return $this->_upSell->offer_title;
    }

    /**
     * Get the type of offer
     * 
     * @return string
     */
    public function type(): string
    {
        return 'up_sell';
    }

    /**
     * Get the description of the up-sell offer
     * 
     * @return string
     */
    public function description(): string
    {
        return $this->_upSell->offer_description_formatted;
    }

    /**
     * Get the product that triggers this offer
     * 
     * @return ProductDetails|null
     * @throws Error
     */
    public function trigger_product(): ?ProductDetails
    {
        if ($this->_upSell->relationLoaded('trigger_variant') && $this->_upSell->trigger_variant && $this->_upSell->trigger_variant->relationLoaded('item') && $this->_upSell->trigger_variant->item) {
            $this->_upSell->trigger_variant->item->default_variant_id = $this->_upSell->trigger_variant->id;
            $this->_upSell->trigger_variant->setRelation('variant', $this->_upSell->trigger_variant->item);
            return new ProductDetails($this->_upSell->trigger_variant->item);
        }

        return null;
    }

    /**
     * Get the variant that triggers this offer
     * 
     * @return Variant|null
     */
    public function trigger_variant(): ?Variant
    {
        if ($this->_upSell->relationLoaded('trigger_variant') && $this->_upSell->trigger_variant && $this->_upSell->trigger_variant->relationLoaded('item') && $this->_upSell->trigger_variant->item) {
            return new Variant($this->_upSell->trigger_variant, $this->_upSell->trigger_product);
        }

        return null;
    }

    /**
     * Get the product being offered
     * 
     * @return ProductDetails|null
     * @throws Error
     */
    public function offer_product(): ?ProductDetails
    {
        if ($this->_upSell->relationLoaded('offer_variant') && $this->_upSell->offer_variant && $this->_upSell->offer_variant->relationLoaded('item') && $this->_upSell->offer_variant->item) {
            $this->_upSell->offer_variant->item->default_variant_id = $this->_upSell->offer_variant->id;
            $this->_upSell->offer_variant->setRelation('variant', $this->_upSell->offer_variant->item);
            return new ProductDetails($this->_upSell->offer_variant->item);
        }

        return null;
    }

    /**
     * Get the variant being offered
     * 
     * @return Variant|null
     */
    public function offer_variant(): ?Variant
    {
        if ($this->_upSell->relationLoaded('offer_variant') && $this->_upSell->offer_variant && $this->_upSell->offer_variant->relationLoaded('item') && $this->_upSell->offer_variant->item) {
            return new Variant($this->_upSell->offer_variant, $this->_upSell->offer_product);
        }

        return null;
    }

    /**
     * Whether only additional cost is shown
     * 
     * @return bool
     */
    public function only_additional_cost(): bool
    {
        return (bool) $this->_upSell->only_additional_cost;
    }

    /**
     * Get the price difference
     * 
     * @return float
     */
    public function only(): float
    {
        return $this->_upSell->only;
    }

    /**
     * Get the raw price difference
     * 
     * @return float
     */
    public function only_input(): float
    {
        return $this->_upSell->only_input;
    }

    /**
     * Get the formatted price difference
     * 
     * @return string
     */
    public function only_formatted(): string
    {
        return $this->_upSell->only_formatted;
    }

    /**
     * Get the price difference parts
     * 
     * @return array
     */
    public function only_parts(): array
    {
        return PriceParts::format($this->only_input());
    }

    /**
     * Whether the offer price is overridden
     * 
     * @return bool
     */
    public function offer_override_price(): bool
    {
        return (bool) $this->_upSell->offer_override_price;
    }

    /**
     * Get the overridden offer price
     * 
     * @return float|null
     */
    public function offer_price(): ?float
    {
        if ($this->offer_override_price()) {
            return $this->_upSell->offer_price;
        }

        return null;
    }

    /**
     * Get the raw overridden offer price
     * 
     * @return float|null
     */
    public function offer_price_input(): ?float
    {
        if ($this->offer_override_price()) {
            return $this->_upSell->offer_price_input;
        }

        return null;
    }

    /**
     * Get the formatted overridden offer price
     * 
     * @return string|null
     */
    public function offer_price_formatted(): ?string
    {
        if ($this->offer_override_price()) {
            return $this->_upSell->offer_price_formatted;
        }

        return null;
    }

    /**
     * Get the overridden offer price parts
     * 
     * @return array|null
     */
    public function offer_price_parts(): ?array
    {
        if ($this->offer_override_price()) {
            return PriceParts::format($this->offer_price_input());
        }

        return null;
    }

    /**
     * Get the trigger product price
     * 
     * @return float|null
     */
    public function trigger_price(): ?float
    {
        if ($this->offer_override_price()) {
            return $this->_upSell->trigger_price;
        }

        return null;
    }

    /**
     * Get the raw trigger product price
     * 
     * @return float|null
     */
    public function trigger_price_input(): ?float
    {
        if ($this->offer_override_price()) {
            return $this->_upSell->trigger_price_input;
        }

        return null;
    }

    /**
     * Get the formatted trigger product price
     * 
     * @return string|null
     */
    public function trigger_price_formatted(): ?string
    {
        if ($this->offer_override_price()) {
            return $this->_upSell->trigger_price_formatted;
        }

        return null;
    }

    /**
     * Get the trigger product price parts
     * 
     * @return array|null
     */
    public function trigger_price_parts(): ?array
    {
        if ($this->offer_override_price()) {
            return PriceParts::format($this->trigger_price_input());
        }

        return null;
    }

    /**
     * Whether to match quantities
     * 
     * @return bool
     */
    public function match_quantity(): bool
    {
        return (bool) $this->_upSell->match_quantity;
    }

    /**
     * Get the offer shown when declined
     * 
     * @return self|CrossSell|null
     */
    public function decline(): self|CrossSell|null
    {
        if ($this->_upSell->relationLoaded('child_no_detailed') && $this->_upSell->child_no_detailed) {
            if ($this->_upSell->child_no_detailed instanceof UpSellModel) {
                return new static($this->_upSell->child_no_detailed);
            } else {
                return new CrossSell($this->_upSell->child_no_detailed);
            }
        }

        return null;
    }

    /**
     * Get the offer shown when accepted
     * 
     * @return self|CrossSell|null
     */
    public function accept(): self|CrossSell|null
    {
        if ($this->_upSell->relationLoaded('child_yes_detailed') && $this->_upSell->child_yes_detailed) {
            if ($this->_upSell->child_yes_detailed instanceof UpSellModel) {
                return new static($this->_upSell->child_yes_detailed);
            } else {
                return new CrossSell($this->_upSell->child_yes_detailed);
            }
        }

        return null;
    }

    /**
     * Get the up-sell offer as a plain array
     * 
     * @return array{
     *     id: int,
     *     name: string,
     *     type: string,
     *     description: string,
     *     trigger_product: ProductDetails|null,
     *     trigger_variant: Variant|null,
     *     offer_product: ProductDetails|null,
     *     offer_variant: Variant|null,
     *     only_additional_cost: bool,
     *     only: float,
     *     only_input: float,
     *     only_formatted: string,
     *     only_parts: array,
     *     offer_override_price: bool,
     *     offer_price: float|null,
     *     offer_price_input: float|null,
     *     offer_price_formatted: string|null,
     *     offer_price_parts: array|null,
     *     trigger_price: float|null,
     *     trigger_price_input: float|null,
     *     trigger_price_formatted: string|null,
     *     trigger_price_parts: array|null,
     *     match_quantity: bool,
     *     decline: self|CrossSell|null,
     *     accept: self|CrossSell|null
     * }
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'type' => $this->type(),
            'description' => $this->description(),
            'trigger_product' => $this->trigger_product(),
            'trigger_variant' => $this->trigger_variant(),
            'offer_product' => $this->offer_product(),
            'offer_variant' => $this->offer_variant(),
            'only_additional_cost' => $this->only_additional_cost(),
            'only' => $this->only(),
            'only_input' => $this->only_input(),
            'only_formatted' => $this->only_formatted(),
            'only_parts' => $this->only_parts(),
            'offer_override_price' => $this->offer_override_price(),
            'offer_price' => $this->offer_price(),
            'offer_price_input' => $this->offer_price_input(),
            'offer_price_formatted' => $this->offer_price_formatted(),
            'offer_price_parts' => $this->offer_price_parts(),
            'trigger_price' => $this->trigger_price(),
            'trigger_price_input' => $this->trigger_price_input(),
            'trigger_price_formatted' => $this->trigger_price_formatted(),
            'trigger_price_parts' => $this->trigger_price_parts(),
            'match_quantity' => $this->match_quantity(),
            'decline' => $this->decline(),
            'accept' => $this->accept(),
        ];
    }
}
