<?php

declare(strict_types=1);

namespace App\LiquidEngine\Traits;

use App\Models\StoreFront\FrontWidget;
use Illuminate\Config\Repository;
use Illuminate\Support\Collection;

trait OldWidgetsSettings
{
    /**
     * @var null|Collection
     */
    private $___configs;

    /**
     * @var array|Collection[]
     */
    private $___mapping = [];

    /**
     * @return Collection
     */
    protected function findThemesConfigs() : Collection
    {
        if(!is_null($this->___configs)) {
            return $this->___configs;
        }

        $files = array_merge(
            glob(base_path('themes/*/config/theme.json')),
            glob(protected_path('templates/site/layouts/*/config.json'))
        );

        return $this->___configs = Collection::make($files)->map(function($path): ?array {
            if(preg_match('~(themes\/([^\/]*)|layouts\/([^\/]*))~', $path, $match)) {
                $themeName = $match[3] ?? $match[2];

                if(!$json = file_get_contents($path)) {
                    return null;
                }

                $json = str_replace('{$img_url}/', config('url.img'), $json);
                if(!is_array($json = (json_decode($json, true)['widgets'] ?? null))) {
                    return null;
                }

                return [
                    'theme' => $themeName,
                    'json' => $json
                ];
            }

            return null;
        })->pluck('json', 'theme');
    }

    /**
     * @param $key
     * @return Collection
     */
    protected function findMappingNames($key) : Collection
    {
        if(array_key_exists($key, $this->___mapping)) {
            return $this->___mapping[$key];
        }

        return $this->___mapping[$key] = $this->findThemesConfigs()->map(function($data) use($key) {
            if(!is_array($data)) {
                return false;
            }

            $data = array_filter($data, function($data) use($key): bool {
                return ($data['map'] ?? null) == $key;
            });

            return array_keys($data);
        })->collapse()->unique();
    }

    /**
     * @param $key
     * @return Repository
     */
    protected function getOldWidgetConfig($key) : Repository
    {
        $mappings = $this->findMappingNames($key)
            ->push($key);

        if($settings = (FrontWidget::whereIn('mapping', $mappings)->first()->settings ?? null)) {
            $config = $this->_transformJson($settings);
        }

        return new Repository($config ?? []);
    }

    /**
     * @param $data
     * @return array|mixed
     */
    private function _transformJson($data)
    {
        if(is_array($data)) {
            $tmp = [];
            foreach($data AS $k=>$v) {
                $tmp[$k] = $this->_transformJson($v);
            }

            return $tmp;
        }

        if(is_string($data) && is_array($test = @json_decode($data, true))) {
            $data = $test;
        }

        return $data;
    }

}
