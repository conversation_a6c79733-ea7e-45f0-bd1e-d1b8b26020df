<?php

declare(strict_types=1);

namespace App\LiquidEngine\Traits\Http\Checkout;

use App\Helper\Format;
use App\Helper\Store\CartSession;
use App\LiquidEngine\Assetics\Subscribers\Checkout\InitCheckout;
use App\Models\Store\Cart;
use App\Models\Store\Cart as CartModel;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Throwable;

/**
 * @property Cart $cart
 */
trait Steps
{

    /**
     * @param Request $request
     * @return View|RedirectResponse|Response
     * @throws Throwable
     */
    public function index(Request $request)
    {
        if (!$this->cart) {
            return redirect()->route('cart.list', [CartModel::getNewCartKey()]);
        }

        $steps = $this->_getSteps();

        if (($min_price = setting('checkout_min_price', 0)) > 0 && $this->cart->getCheckout()->getSubTotal('input') < $min_price) {
            $steps = [
                'min_price' => view('checkout::include.checkout_min_price', [
                    'message' => sprintf(__('sf.checkout.err.checkout_min_price_%1$s_allowed_checkout'),
                        Format::money(Format::toIntegerPrice($min_price)))
                ])->render()
            ];
        }

        if (!$this->cart->has_products) {
            return redirect()->route('cart.list', [CartModel::getNewCartKey()]);
        }

        $this->breadcrumbs()->add(
            __('pages.home'),
            route('site.home')
        );

        $this->breadcrumbs()->add(
            __('pages.cart'),
            route('cart.list', ['cart_key' => CartModel::getNewCartKey()])
        );

        $data = [
            'content_for_layout' => view('checkout::checkout.content_for_layout', [
                'steps' => $steps
            ])
        ];

//        if($request->ajax()) {
//            return $data['content_for_layout'];
//        }

        event('controller:checkout', new InitCheckout($this->cart));

        return $this->template($this->template, $data, $this->getShared('checkout'));
    }

    /**
     * @param null $seo
     * @return array
     */
    protected function getShared($seo = null): array
    {
        return array_merge([
            'order_summary_toggle' => view('checkout::checkout.order_summary_toggle'),
            'content_for_order_summary' => view('checkout::checkout.content_for_order_summary')
        ], $seo ? $this->getSeoData($seo) : []);
    }

    /**
     * @return array
     * @throws Throwable
     */
    private function _getSteps(): array
    {
        $steps_array = array_keys(CartSession::STEP_CLEAR_SESSION);
        $steps = [];
        $empty = null;
        foreach ($steps_array AS $step) {
            //remove shipping step if no shippable products
            if (!$this->cart->hasShippable() && in_array($step, ['shippingAddress', 'shipping'])) {
                continue;
            }

            if ($this->cart->hasGuestEmailInShippingForm() && $step == 'authorize') {
                $steps[$step] = $this->authorizeEmpty();
                continue;
            }

            //new engine fix
            if($step == 'authorize') {
                $step = 'checkoutAuthorize';
            }

            $steps[$step] = $this->{$step . ($empty !== true ? $empty : '')}(true);
            if ($empty === true) {
                $empty = 'Empty';
                if ($step == 'billingAddress' && !$this->cart->hasBillingAddress()) {
                    $empty = true;
                }
            }

            if ($step == 'authorize' && $step == $this->cart->getStep() && !$this->cart->customer) {
                $empty = 'Empty';
            }

            if ($empty != 'Empty' && $step == $this->cart->getStep()) {
                $empty = true;
            }

            if ($step == 'billingAddress' && $step == $this->cart->getStep() && $this->cart->hasBillingAddress()) {
                $empty = true;
            } elseif (!$this->cart->hasBillingAddress() && $step == $this->cart->getStep() && $step == 'shippingAddress') {
                $empty = true;
            } elseif ($this->cart->hasBillingAddress() && $this->cart->getBillingAddress() && $step == $this->cart->getStep() && $step == 'shippingAddress') {
                $empty = null;
            }
        }

        return $steps;
    }
}
