<?php

declare(strict_types=1);

namespace App\LiquidEngine\Traits\Http\Checkout;

use App\Events\OrderCreated;
use App\Exceptions\Error;
use App\Exceptions\Fault;
use App\Helper\Format;
use App\Helper\Mail\QueueNotifyAdmin;
use App\Helper\Mail\SendCustomerNotification;
use App\Helper\Store\CartTotal;
use App\Helper\Store\CheckoutSystemPages;
use App\Helper\YesNo;
use App\Http\Request\SiteV2\Checkout\PaymentRequest;
use Modules\Apps\Shippings\Omniship\AbstractManager;
use App\Models\Customer\Customer;
use App\Models\Customer\CustomerShippingAddress;
use App\Models\Discount\Discount;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\PaymentProviders;
use App\Models\Gateway\Payments;
use App\Models\Layout\FormFields;
use App\Models\Order\Order;
use App\Models\Order\OrderDiscount;
use App\Models\Order\OrderFulfillment;
use App\LiquidEngine\Assetics\Subscribers\Checkout\ReturnPageOrder;
use App\LiquidEngine\Assetics\Subscribers\Checkout\SubmitOrder;
use App\Models\Order\OrderPayment;
use App\Models\Order\OrderProduct;
use App\Models\Order\OrderShipping;
use App\Models\Router\Exceptions;
use App\Models\Store\Cart;
use Apps;
use Carbon\Carbon;
use Exception;
use GDPR;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\View\View;
use lib\widget\store\Leasing;
use App\LiquidEngine\LiquidHelpers\Drop\Order as DropOrder;
use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;
use Modules\GroceryStore\GroceryStoreManager;
use Modules\Apps\Administration\GroceryStore\Models\Units;
use Mpdf\MpdfException;
use Omniship\Consts;
use PaymentGateway;
use SmartyException;
use Throwable;
use Widget;
use Auth;

/**
 * @property Cart $cart
 */
trait Payment
{

    /**
     * @return Factory|View
     * @throws Error
     * @throws Throwable
     */
    public function payment()
    {
        return $this->changePayment();
    }

    /**
     * @return Factory|View
     * @throws Error
     * @throws Throwable
     */
    public function changePayment()
    {
        if ($this->cart->hasBillingAddress() && !$this->cart->getBillingAddress()) {
            return $this->paymentEmpty();
        }

        if ($this->cart->hasShippable()) {
            /** @var AbstractManager $shipping_manager */
            $shipping = $this->cart->getCheckout()->getShipping();
            if (!$shipping || !$shipping->service_id) {
                return $this->paymentEmpty();
            }

            $shipping_manager = $shipping && $shipping->manager ? $shipping->manager->setOrder($this->cart->getCheckout()) : null;
        } else {
            $shipping_manager = null;
            $shipping = null;
            if (!$this->cart->getBillingAddress()) {
                return $this->paymentEmpty();
            }
        }

        $managers = PaymentProviders::getConfigurations();
        $managers->map(function (PaymentProviderConfiguration $manager, $key) use ($managers): \App\Models\Gateway\PaymentProviderConfiguration {
            $minPrice = $manager->min_price;
            $total = $this->cart->getCheckout()->getTotal('input');

            if ($minPrice && $minPrice > $total) {
                $managers->offsetUnset($key);
            }

            return $manager;
        });

        if ($shipping_manager) {
            $map_managers = function (PaymentProviderConfiguration $manager) use ($shipping_manager): \App\Models\Gateway\PaymentProviderConfiguration {
                if ('credit' == $manager->payment_provider->group) {
                    /** @var Collection $products */
                    $products = $this->cart->products->pluck('product_id', 'product_id');
                    /** @var Leasing $leasing */
                    $leasing = Widget::get('store.leasing');
                    $active_manager = $this->cart->getPayment();

                    if ($active_manager) {
                        $details = $leasing->renderCheckoutDetailedForm($manager->provider, $this->cart->getCheckout()->getTotal('input'), $products->all(), ($manager->provider == $active_manager->provider ? $active_manager->payment_variant_id : 0), $active_manager->initial, $active_manager->installment);
                    } else {
                        $details = $leasing->renderCheckoutDetailedForm($manager->provider, $this->cart->getCheckout()->getTotal('input'), $products->all(), 0, 0, 0);
                    }

                    $manager->setAttribute('html', $details);
                }

                $manager->setAttribute('support_recalculate_shipping', $shipping_manager->supportsRecalculateShippingOnPaymentChange());
                return $manager;
            };

            $quote = $this->cart->getQuote($shipping_manager->getKey(), $shipping->service_id);
            if (!$shipping_manager->supportsCashOnDelivery() || !$quote || !$quote->getAllowanceCashOnDelivery()) {
                /** @var Collection $managers */
                $managers = $managers->forget('cod')->map($map_managers);
            }

            if (!$shipping_manager->supportsPayOnPlace() || !$quote) {
                /** @var Collection $managers */
                $managers = $managers->forget('pop')->map($map_managers);
            }
        } elseif (!$this->cart->hasShippable()) {
            /** @var Collection $managers */
            $managers = $managers->forget('cod')
                ->forget('pop');
        }

        $managers_groups = $managers->groupBy('payment_provider.group', true);

        return view('checkout::steps.payment', [
            'managers' => $managers_groups,
            'total_managers' => $managers->count(),
            'manager' => $this->cart->getPayment()
        ]);
    }

    /**
     * @param PaymentRequest $request
     * @return ResponseFactory|RedirectResponse|Response
     * @throws Fault
     * @throws Throwable
     */
    public function submitPayment(PaymentRequest $request)
    {
        if ($this->cart->hasShippable() && !$this->cart->getShippingAddress()) {
            $this->cart->setStep('authorize');
            return redirect()
                ->route('checkout');
        }

        $this->cart->setStep('payment');

        /** @var PaymentProviders $payment_provider */
        $payment_provider = PaymentProviders::whereProvider($request->input('checkout.payment.provider'))->firstOrFail();

        $this->cart->setPayment($payment_provider->fillFromCart($this->cart->getPayment())->toArray());

        try {
            \Illuminate\Support\Facades\DB::beginTransaction();
            if (setting('guest_to_customer') && Auth::getCustomerAccess() == 'both') {
                $this->cart->setRelation('customer_get', $this->cart->customer->convertGuestToCustomer(true));
                $this->cart->update(['guest_id' => null]);
            }

            $update = [];
//            $update['marketing'] = $marketing;
//            $this->cart->customer->marketing = $update['marketing'];
            $first_name = $this->cart->customer->first_name;
            $last_name = $this->cart->customer->last_name;
            if (empty($this->cart->customer->first_name)) {
                if (!empty($shipping_address = $this->cart->getShippingAddress()) && !empty($shipping_address->first_name) && trim($shipping_address->first_name)) {
                    $first_name = $shipping_address->first_name;
                    $update['first_name'] = $first_name;
                } elseif (!empty($billing_address = $this->cart->getBillingAddress()) && !empty($billing_address->first_name) && trim($billing_address->first_name)) {
                    $first_name = $billing_address->first_name;
                    $update['first_name'] = $first_name;
                }
            }

            if (empty($this->cart->customer->last_name)) {
                if (!empty($shipping_address = $this->cart->getShippingAddress()) && !empty($shipping_address->last_name) && trim($shipping_address->last_name)) {
                    $last_name = $shipping_address->last_name;
                    $update['last_name'] = $last_name;
                } elseif (!empty($billing_address = $this->cart->getBillingAddress()) && !empty($billing_address->last_name) && trim($billing_address->last_name)) {
                    $last_name = $billing_address->last_name;
                    $update['last_name'] = $last_name;
                }
            }

//            $update['marketing'] = $marketing;
            if ($update && $this->cart->customer->id) {
                $customer = Customer::find($this->cart->customer->id);
                $customer->update($update);
                $this->cart->setRelation('customer_get', $customer);
                if (empty($first_name)) {
                    $first_name = $customer->first_name;
                }

                if (empty($last_name)) {
                    $last_name = $customer->last_name;
                }
            }

            $order = Order::create([
                'customer_id' => $this->cart->customer->id,
                'customer_group_id' => $this->cart->customer->group_id,
                'customer_first_name' => $first_name,
                'customer_last_name' => $last_name,
                'customer_email' => $this->cart->customer->email,
                'note_customer' => htmlspecialchars($request->input('checkout.note'), ENT_QUOTES, 'utf-8'),
                'price_products_subtotal' => $this->cart->getCheckout()->getSubTotal(),
                'price_subtotal' => $this->cart->getCheckout()->getSubTotal(),
                'price_total' => $this->cart->getCheckout()->getTotal(),
                'quantity' => $this->cart->quantity,
                'weight' => $this->cart->weight,
                'vat_included' => setting('vat_included') == YesNo::True ? YesNo::True : YesNo::False,
                'status' => 'pending',
                'status_fulfillment' => 'not_fulfilled',
                'abandoned' => $this->cart->getAbandoned() ? 1 : 0,
            ]);

            if ($order->customer && is_array($customData = $this->cart->getMeta('custom', []))) {
                $order->customer->custom_data()->whereHas('field', function ($q): void {
                    /** @var FormFields $q */
                    $q->where('customer_modify', 1);
                })->delete();
                foreach ($customData AS $field_id => $value) {
                    if (!is_null($field = FormFields::find($field_id))) {
                        $order->customer->custom_data()->create([
                            'field_id' => $field->id,
                            'value' => $value
                        ]);
                    }
                }
            }

            if ($source_data = $this->cart->getRestoreSource()) {
                $order->setMeta(['restore_source' => $source_data]);
            }

            if ($utmData = Arr::only((array)$request->session()->get('utmData'), ['utm_source', 'utm_medium', 'utm_campaign', 'referer'])) {
                $order->setMeta($utmData);
            }

//            if ($campaignData = $request->session()->get('campaignData', null)) {
//                event(new OrderFromCampaignCreated($order->customer_id, $campaignData));
//            }

            $order->setMeta([
                'checkout_digital_shipping' => setting('checkout_digital_shipping'),
                'status_for_quantity_decrease' => $order::getOrderStatusDecrementProducts()
            ]);

            $total_row = 0;
            foreach ($this->cart->getCheckout()->getTotals() AS $group => $totals) {
                /** @var CartTotal $total */
                foreach ($totals AS $key => $total) {
                    $order->totals()->create([
                        'group' => $group,
                        'key' => $key,
                        'name' => $total->getName(),
                        'description' => $total->getDescription(),
                        'price' => $total->getPrice(),
                        'price_input' => $total->getPriceInput(),
                        'price_formatted' => $total->getPriceFormatted(),
                        'use_for_total' => $total->getUseForTotal(),
                        'price_without_vat' => $total->getPriceWithoutVat(),
                        'price_input_without_vat' => $total->getPriceInputWithoutVat(),
                        'price_formatted_without_vat' => $total->getPriceFormattedWithoutVat(),
                        'use_for_total_without_vat' => $total->getUseForTotalWithoutVat(),
                        'currency' => $total->getCurrency(),
                        'value' => $total->getValue(),
                        'sort_order' => $total_row++,
                        'hide_in_invoice' => $total->getHideInInvoice(),
                        'invoice_name' => $total->getInvoiceName(),
                    ]);
                }
            }

            foreach ($this->cart->getCheckout()->getDiscounts() AS $discount) {
                $order->discounts()->create([
                    'discount_id' => $discount->id,
                    'name' => $discount->name,
                    'code' => $discount->code,
                    'type' => $discount->type,
                    'type_value' => $discount->type_value,
                    'order_over' => $discount->order_over,
                    'target_customer_group_id' => $discount->customer_group_id,
                ]);

                // update discount uses
                if (!empty($discount->max_uses) && $discount->uses >= $discount->max_uses) {
                    throw new Error(__('sf.widget.checkout.err.discount_not_enough_uses_left'));
                }

//                $discount->increment('uses');
//                $discount->save();
                $discount->incrementUses();
            }

            if (($discount = $this->cart->getCheckout()->getDiscountCode()) && ($discount->order_over > 0 || $discount->validateDiscountCode($this->cart->products))) {
                $order_discount = $order->discounts()->create([
                    'discount_id' => $discount->id,
                    'name' => $discount->name,
                    'code' => $discount->code_prefix ? $discount->barcode : $discount->code,
                    'code_apply' => $discount->code_apply,
                    'type' => $discount->type,
                    'type_value' => $discount->type_value,
                    'order_over' => $discount->order_over,
                    'target_customer_group_id' => $discount->customer_group_id,
                    'is_container' => $discount->is_container,
                    'codes' => $discount->is_container ? $discount->codes : []
                ]);

                // update discount uses
                if (!empty($discount->max_uses) && $discount->uses >= $discount->max_uses) {
                    throw new Error(__('sf.widget.checkout.err.discount_not_enough_uses_left'));
                }

//                $discount->increment('uses');
//                $discount->save();
                $discount->incrementUses();

                $targets = $discount->targetsWithParameters($order->customer, $this->cart->getZoneInformationShipping())->get();
                foreach ($targets AS $target) {
                    $order_discount->targets()->create([
                        'type' => $target->type,
                        'item_id' => $target->item_id,
                        'sub_item_id' => $target->sub_item_id
                    ]);
                }
            }

            $this->cart->getCheckout()->setCountdownDiscountToOrderDependingOnType($order);

            /** @var OrderShipping $order_shipping */
            $order_shipping = null;
            if (!is_null($shipping = $this->cart->getCheckout()->getShipping())) {
                $insurance_enable = $shipping->manager->isExternal() ? false : !is_null($shipping->insurance);
                $order_shipping = $order->shipping()->create([
                    'provider_id' => $shipping->id,
                    'provider_name' => $shipping->name,
                    'provider_type' => $shipping->type,
                    'provider_amount' => $shipping->price,
                    'order_amount' => $shipping->price,
                    'provider_from' => $shipping->rate ? $shipping->rate->from : $shipping->price,
                    'provider_to' => $shipping->rate ? $shipping->rate->to : $shipping->price,
                    'provider_insurance' => $insurance_enable ? $shipping->insurance : null,
                    'order_has_insurance' => $insurance_enable ? YesNo::True : YesNo::False,
                    'order_insurance' => $insurance_enable && $shipping->quotes && $shipping->quotes->has($shipping->service_id) ? $shipping->quotes->get($shipping->service_id)->getInsurance() : null,
                    'service_id' => $shipping->service_id,
                    'service_name' => $shipping->service_name,
                ]);

                /** @var Discount $discount */
                foreach ($this->cart->getCheckout()->getDiscountShippings() AS $discount) {
                    $order->discounts()->create([
                        'discount_id' => $discount->id,
                        'name' => $discount->name,
                        'code' => $discount->code,
                        'type' => $discount->type,
                        'type_value' => $discount->type_value,
                        'order_over' => $discount->order_over,
                        'target_customer_group_id' => $discount->customer_group_id,
                    ]);

                    if ($discount->cross_sell) {
                        $order->crossSell()->create([
                            'cross_sell_id' => $discount->cross_sell->id,
                            'price' => 0,
                            'real_price' => $shipping->price,
                            'name' => $discount->cross_sell->name,
                            'type' => 'shipping',
                            'discount_type' => 'shipping',
                            'free_shipping' => 1,
                        ]);
                    } elseif ($discount->exists) {
                        // update discount uses
                        if (!empty($discount->max_uses) && $discount->uses >= $discount->max_uses) {
                            throw new Error(__('sf.widget.checkout.err.discount_not_enough_uses_left'));
                        }

//                        $discount->increment('uses');
                        $discount->incrementUses();
                    }
                }
            }

            foreach ($this->cart->getCheckout()->getTaxes() AS $tax) {
                $order->taxes()->create([
                    'tax_id' => $tax->id,
                    'tax_name' => $tax->name,
                    'tax_type' => $tax->type,
                    'tax_shipping' => $tax->shipping,
                    'tax_tax' => $tax->tax,
                    'tax_vat' => $tax->vat,
                    'order_amount' => $tax->calculated
                ]);
            }

            foreach ($this->cart->products AS $product) {
                $product->validateOrderQuantity();

                $order_product_discount = null;
                if ($product->discount && $product->discount->discount) {
                    /** @var OrderDiscount $order_product_discount */
                    if ($product->discount->discount->type == 'fixed') {
                        $order_product_discount = $order->discounts()->create([
                            'discount_id' => $product->discount->discount_id,
                            'name' => $product->discount->discount->name,
                            'code' => null,
                            'type' => 'fixed',
                            'type_value' => $product->discount->price,
                            'order_over' => null,
                            'target_product_id' => $product->discount->product_id,
                            'target_product_category_id' => null,
                            'target_customer_group_id' => $product->discount->customer_group_id,
                        ]);
                    } else {
                        $order_product_discount = $order->discounts()->create([
                            'discount_id' => $product->discount->discount_id,
                            'name' => $product->discount->discount->name,
                            'code' => $product->discount->discount->code,
                            'type' => $product->discount->discount->type,
                            'type_value' => $product->discount->discount->type_value,
                            'order_over' => $product->discount->discount->order_over,
                            'target_product_id' => $product->discount->product_id,
                            'target_product_category_id' => null,
                            'target_customer_group_id' => $product->discount->customer_group_id,
                        ]);
                    }

                    if ($product->discount->discount->exists && !empty($product->discount->discount->max_uses)) {
                        // update discount uses
                        if ($product->discount->discount->uses >= $product->discount->discount->max_uses) {
                            throw new Error(__('sf.widget.checkout.err.discount_not_enough_uses_left'));
                        }

                        //$product->discount->discount->increment('uses');
                        $product->discount->discount->incrementUses();
                    }
                }

                /** @var null|OrderFulfillment $order_product_fulfillment */
                $order_product_fulfillment = null;
                if ($product->is_digital) {
                    $order_product_fulfillment = $order->fulfillments()->create([
                        'date_fulfilled' => Carbon::now('UTC'),
                    ]);
                }

                $order_bundle = null;
                if ($product->bundle_id && !empty($product->bundle->bundle_id)) {
                    $order_bundle = $order->bundles()->firstOrCreate([
                        'bundle_id' => $product->bundle->bundle_id,
                        'cart_bundle_id' => $product->bundle_id
                    ]);
                }

                $data = [
                    'quantity' => $product->quantity,
                    'product_id' => $product->product_id,
                    'variant_id' => $product->variant_id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'barcode' => $product->barcode,
                    'weight' => $product->weight,
                    'price' => (int)$product->price + $product->options_price,
                    'order_price' => !is_null($product->discount_price) ? $product->discount_price : $product->price + $product->options_price,
                    'order_discount_id' => $order_product_discount ? $order_product_discount->id : null,
                    'order_fulfillment_id' => $order_product_fulfillment ? $order_product_fulfillment->id : null,
                    'p1' => $product->p1,
                    'p2' => $product->p2,
                    'p3' => $product->p3,
                    'v1' => $product->v1,
                    'v2' => $product->v2,
                    'v3' => $product->v3,
                    'vendor_id' => $product->vendor_id,
                    'vendor_name' => $product->vendor_name,
                    'category_id' => $product->category_id,
                    'category_name' => $product->category_name,
                    'sale' => $product->discount ? YesNo::True : YesNo::False,
                    'digital' => $product->is_digital ? YesNo::True : YesNo::False,
                    'tracked' => YesNo::False,
                    'order_bundle_id' => $order_bundle->id ?? null
                ];

                if(Apps::installed(GroceryStoreManager::APP_KEY)) {
                    $data = array_merge($data, [
                        'unit_id' => $product->unit_id,
                        'unit_value' => $product->unit_value,
                        'units' => $product->units->map(function(Units $unit) {
                            return $unit->getAttributes();
                        })->all(),
                    ]);
                }

                /** @var OrderProduct $order_product */
                $order_product = $order->products()->create($data);

                $order_product->setQuantityBeforeOrder($product->product_quantity);
                $order_product->setContinueSellingBeforeOrder($product->continue_selling == YesNo::True);

//                $product_meta = $product->product->meta;
//                if(count($product_meta)){
//                    $order_product->setMeta($product_meta);
//                }

                foreach ($product->options AS $option) {
                    $order_product->options()->create([
                        'field_id' => $option->field_id > 0 ? $option->field_id : null,
                        'field_option_id' => $option->field_option_id,
                        'type' => $option->type,
                        'name' => $option->name,
                        'option' => $option->option,
                        'value' => $option->value,
                        'amount' => $option->field_value ? ($option->field_value->amount ?? null) : ($option->field->amount ?? null),
                        'value_symbol' => $option->field->symbol_value ?? null
                    ]);
                }

                if ($product->cross_sell_product || $product->cross_sell_target) {
                    $order_product->crossSell()->create([
                        'order_id' => $order->id,
                        'cross_sell_id' => $product->cross_sell_product->id ?? $product->cross_sell_target->id ?? null,
                        'price' => !is_null($product->discount_price) ? $product->discount_price : $product->price + $product->options_price,
                        'real_price' => (int)$product->price + $product->options_price,
                        'name' => $product->cross_sell_product->name ?? $product->cross_sell_target->name ?? 'No name',
                        'type' => $product->cross_sell_product ? 'action' : 'target',
                        'discount_type' => $product->cross_sell_product->discount_type ?? $product->cross_sell_target->discount_type ?? null,
                        'free_products' => $product->cross_sell_product->free_products ?? $product->cross_sell_target->free_products ?? 0,
                        'discount_percent' => $product->cross_sell_product->discount_percent ?? $product->cross_sell_target->discount_percent ?? null,
                    ]);
                }

                if ($product->up_sell_for_create) {
                    $order_product->upSell()->create($product->up_sell_for_create);
                }
            }

            if ($this->cart->digital_count > 0 && $this->cart->quantity == $this->cart->digital_count) {
                $order->update([
                    'status_fulfillment' => 'fulfilled'
                ]);
            }

            if (!is_null($address = $this->cart->getBillingAddress())) {
                $order->billingAddress()->create(array_merge($address->toArray(), [
                    'customer_id' => $order->customer_id,
                    'address_id' => $address->id
                ]));
            }

            /** @var CustomerShippingAddress $address */
            if ($order_shipping && !is_null($address = $this->cart->getShippingAddressForProvider($order_shipping->provider->manager->getKey()))) {
                $order->shippingAddress()->create(array_merge($address->toArray(), [
                    'integration' => $order_shipping->provider->manager->getKey(),
                    'customer_id' => $order->customer_id,
                    'address_id' => $address->id
                ]));
            }

            /** @var OrderPayment $order_payment */
            $order_payment = $order->payment()->create([
                'provider' => $payment_provider->provider,
                'amount' => $this->cart->getCheckout()->getTotal(),
                'status' => 'initiated'
            ]);

            $paymentRequest = $order_payment->paymentServerRequest($this->cart->getCheckout());
            $payment = Payments::createFromRequest($paymentRequest);

            $gateway = PaymentGateway::provider($payment_provider->provider);
            $response = $gateway->purchase($payment);

            //set order meta
            if ($order_shipping && $shipping && !is_null($quote = $this->cart->getQuote($shipping->manager->getKey(), $shipping->service_id))) {
                $manager = $shipping->manager->setOrder($this->cart->getCheckout());

                $quotes = $this->cart->getQuotes($manager->getKey());
                $order->setMeta([
                    'integration' => $shipping->manager->getKey(),
                    'service_id' => $shipping->service_id,
                    'service_name' => $quote->getName(),
                    'service_quote_error' => $quote->getErrorMessage(),
                    'total' => $shipping->price,
                    'free_shipping_total' => $manager->getSetting('free_shipping_total') > 0 ? Format::toIntegerPrice($manager->getSetting('free_shipping_total')) : null,
//                        'original_total' => Format::toIntegerPrice($quote->getPrice()),
                    'currency' => $quote->getCurrency(),
                    'insurance' => $manager->supportsInsurance(),
                    'delivery_hour' => null, //speedy, econt, rapido
                    'delivery_time' => null, //speedy, rapido
                    'side' => in_array($order->status, ['completed', 'paid']) ? Consts::PAYER_SENDER : $manager->getPayerSide(),
                    'quotes' => json_encode($quotes ? $quotes->toArray() : null),
                    'boxes' => json_encode($this->cart->getBoxes($manager->getKey())),
                    'default_weight' => Format::toIntegerWeight($manager->getDefaultWeight()),
                    'weight' => Format::toIntegerWeight($manager->getWeightFromItems($this->cart->getItemsBag(), $manager->getDefaultWeight())),
                    'rate' => in_array($manager->getProvider([])->type, ['price', 'weight']) && $shipping->rate ? $shipping->rate->toJson() : null,
                    'rates' => in_array($manager->getProvider([])->type, ['price', 'weight']) && $shipping->rates ? $shipping->rates->toJson() : null,
                    'fixed_price' => $manager->getSetting('fixed_price'),

                    //speedy
                    'special_delivery_requirements' => $manager->getSetting('special_delivery_requirements'),
                    'option_before_payment' => $manager->getOptionBeforePayment(),
                    'fragile' => $manager->getSetting('fragile'),
                    'back_documents' => $manager->getSetting('back_documents'),
                    'back_receipt' => $manager->getSetting('back_receipt'),
                    'documents' => $manager->getSetting('documents'),
                    'saturday_delivery' => $manager->getSetting('saturday_delivery'),
                    //econt
                    'priority_type' => null,
                    //all
                    'pricing' => $manager->getSetting('pricing'),
                ]);

                if (!empty($sender_address = $this->cart->getMeta('sender_address'))) {
                    $order->updateMeta([$shipping->manager->getKey() . '.sender_address' => json_encode($sender_address)]);
                }

                if (!empty($quotes_request = $this->cart->getQuotesRequest($shipping->manager->getKey()))) {
                    $order->updateMeta([$shipping->manager->getKey() . '.quotes_request' => $quotes_request]);
                }

                if (!empty($quotes_response = $this->cart->getQuotesResponse($shipping->manager->getKey()))) {
                    $order->updateMeta([$shipping->manager->getKey() . '.quotes_response' => $quotes_response]);
                }
            }

            if (!empty($cart_source_meta = $this->cart->getMeta('cart.source'))) {
                $order->setMeta(['cart.source' => $cart_source_meta]);
            }

            $order->setMeta(array_filter([
                'utm_source' => $this->cart->getMeta('utm_source') ?? null,
                'utm_medium' => $this->cart->getMeta('utm_medium') ?? null,
                'utm_campaign' => $this->cart->getMeta('utm_campaign') ?? null,
                'referer' => $this->cart->getMeta('referer') ?? null,
            ]));

            if (!empty($cart_cross_sell = $this->cart->getMeta('cross_sell'))) {
                $order->setMeta(['cross_sell' => json_encode($cart_cross_sell)]);
            }

            if ($response instanceof Response) {
                return $response;
            }

            if (empty($response)) {
                throw new Error(__('sf.checkout.gateway.no_response'));
            }

            if (is_array($response)) {
                $order_payment->update([
                    'payment_id' => $response['payment']['payment_id'],
                    'status' => $response['status'],
                ]);
            }
        } catch (Error $e) {
            \Illuminate\Support\Facades\DB::rollBack();
            return response([
                'status' => 'error',
                'msg' => $e->getMessage(),
                'field' => $e->getField()
            ]);
        } catch (Exception $e) {
            \Illuminate\Support\Facades\DB::rollBack();
            Exceptions::create([
                'class' => get_class($e),
                'site_id' => site('site_id'),
                'created_at' => Carbon::now('UTC'),
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            if (inDevelopment() !== false) {
                throw $e;
            }

            return response([
                'status' => 'error',
                'msg' => $e->getMessage()
            ]);
        }

        if (!setting('hide_marketing') && !GDPR::isActive()) {
            $marketing = strtolower($request->input('marketing', 'no'));
            if ($marketing == YesNo::True) {
                $order->customer->update([
                    'marketing' => $marketing
                ]);
            }
        }

        \Illuminate\Support\Facades\DB::commit();

        if ($response instanceof Response) {
            return $response;
        }

        //session()->push('orders', $order->id);

        QueueNotifyAdmin::sendCreateNewOrder($order->id);

        event(new OrderCreated($order));

        event('controller:submitOrder', new SubmitOrder($order));

        if ($response['iframe'] ?? false) {
            return new Response([
                'status' => 'success',
                'js' => $response['iframe'],
            ]);
        }

        //@todo if $response['action'] === false return error in this check
        if (($response['action']['type'] ?? null) === 'redirect') {
            return redirect($response['action']['url']);
        } elseif ($response['action'] === false) {
            return redirect(route('checkout.return', [$response['status'], $order_payment->hash]));
        }

        throw new Fault('Could not resolve checkout action from payment server! Response: ' . var_export($response,
                true));
    }

    /**
     * @return Factory|View
     */
    public function paymentEmpty()
    {
        return view('checkout::steps.empty.payment');
    }

    /**
     * @param string $status
     * @param $payment_hash
     * @return \Illuminate\Contracts\View\View|RedirectResponse|Response
     * @throws Error
     * @throws Throwable
     * @throws MpdfException
     * @throws SmartyException
     * @todo get from widget checkout emails to send on return page!
     */
    public function returnPage($status, $payment_hash)
    {
        $order_payment = OrderPayment::getForReturnPage($payment_hash);

        if ($order_payment === null) {
            app()->abort(404, __('sf.global.err.order_no_longer_exists'));
        }

        if ($status == 'cancel') {
            session()->flash('payment_canceled', __('sf.checkout.header.status_cancelled'));
            return redirect()->route('checkout');
        }

        if ($this->cart) {
            $this->cart->delete();
        }

        $order = $order_payment->order;

        $order::clearCache();

        if (YesNo::False == $order->email_sent) {
            (new SendCustomerNotification)->sendCreateNewOrder($order);
            if ($order_payment->status == 'completed') {
                (new SendCustomerNotification)->sendCreateNewOrderCompleted($order);
                /** @var Collection $digital */
                if (($digital = $order->products->where('digital', YesNo::True)) && $digital->count()) {
                    (new SendCustomerNotification)->sendOrderFilesDownloadLink($order);
                }
            }

            if ($order_payment->status != 'completed' && !in_array($order_payment->provider, ['bwt', 'cod', 'pop']) && $order_payment->payment_provider->group != 'credit') {
                (new SendCustomerNotification)->sendCreateNewOrderPaymentError($order);
            }

            $order->update(['email_sent' => YesNo::True]);
        }

        if ($page = CheckoutSystemPages::getThankYouPage($order)) {

            event('controller:returnPageOrder', new ReturnPageOrder($order, $this->cart));

            return $page;
        }

        $template = new Template('templates.checkout-return');

        $dropOrder = new DropOrder($order);

        $this->breadcrumbs()->add(
            __('pages.home'),
            route('site.home')
        );

        $this->breadcrumbs()->add(
            $dropOrder->title(),
            route('checkout.return', ['status' => $status, 'payment_hash' => $payment_hash])
        );

        event('controller:returnPageOrder', new ReturnPageOrder($order, $this->cart));

        return $this->templateJson($template, [
            'order' => $dropOrder
        ], $this->getSeoData('checkoutReturn'));
    }
}
