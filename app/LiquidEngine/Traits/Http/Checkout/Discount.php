<?php

declare(strict_types=1);

namespace App\LiquidEngine\Traits\Http\Checkout;

use App\Http\Request\SiteV2\Checkout\DiscountCodeRequest;
use App\Models\Discount\Discount as DiscountModel;
use App\Models\Store\Cart;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\LiquidEngine\LiquidHelpers\Drop\Store\Cart as DropCart;
use Throwable;

/**
 * @property Cart $cart
 */
trait Discount
{

    /**
     * @param DiscountCodeRequest $request
     * @return Response
     * @throws Throwable
     */
    public function submitDiscountCode(DiscountCodeRequest $request)
    {
        $code = $request->input('discount_code');
        /** @var DiscountModel $discount */
        if (!is_null($discount = DiscountModel::findByCode($code, $this->cart))) {
            $this->cart->setDiscountCode($code);
            $this->cart->setDiscountContainerCode(null);
        } elseif (!is_null($discount = DiscountModel::findByBarCode($code, $this->cart))) {
            $this->cart->setDiscountCode($code);
            $this->cart->setDiscountContainerCode(null);
        } else {
            $this->cart->setDiscountCode(null);
            $this->cart->setDiscountContainerCode($code);
        }

//        if ($this->cart->getStep() != 'authorize' && setting('checkout_hide_single_shipping') && !is_null($single_check = $this->_checkSingleQuoteShippingManager())) {
//            return $single_check;
//        }

        $response = [
            'cart' => new DropCart($this->cart),
            'status' => 'success',
            'events' => ['cc.cart.product.addDiscountCode'],
        ];

        return response($response);
    }

    /**
     * @param Request $request
     * @param null|string $code
     * @return Response
     * @throws Throwable
     */
    public function removeDiscountCode(/** @noinspection PhpUnusedParameterInspection */
        Request $request, $code = null)
    {
        if (!$this->cart) {
            return null;
        }

        if (is_null($code)) {
            $this->cart->setDiscountCode(null);
        } else {
            $this->cart->removeDiscountContainerCode($code);
        }

        $response = [
            'cart' => new DropCart($this->cart),
            'status' => 'success',
            'events' => ['cc.cart.product.removeDiscountCode'],
        ];

//        if ($this->cart->getStep() != 'authorize' && setting('checkout_hide_single_shipping') && !is_null($single_check = $this->_checkSingleQuoteShippingManager())) {
//            return $single_check;
//        }

        return response($response);
    }

    /**
     * @return Response
     * @throws Throwable
     */
    public function countdownDiscountPopup()
    {
        /**@var DiscountModel $countdownDiscount */
        $countdownDiscount = $this->cart->getCheckout()->getCountdownDiscount();
        if ($countdownDiscount && !$this->cart->getMeta('countdown_discount_popup_was_shown')) {
            $this->cart->setMeta('countdown_discount_popup_was_shown', 1);
            $this->cart->setMeta('countdown_popup_first_showing', Carbon::now('UTC')->toDateTimeString());

            return LView::modal('discounts.countdown.checkout_popup', [
                'message' => $countdownDiscount->meta->get('countdown_description'),
                'popup_effect' => $countdownDiscount->meta->get('countdown_popup_effect'),
            ], null, false);
        }

        return null;
    }

}
