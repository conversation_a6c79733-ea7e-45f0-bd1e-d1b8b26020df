<?php

declare(strict_types=1);

namespace App\LiquidEngine\Traits\Http\Checkout;

use App\Exceptions\Error;
use App\Helper\Format;
use App\Http\Request\Site\Checkout\RecalculateShippingRequest;
use App\Http\Request\SiteV2\Checkout\ShippingRequest;
use Modules\Apps\Shippings\Omniship\AbstractManager;
use App\Models\Customer\CustomerShippingAddress;
use App\Models\Gateway\PaymentProviders;
use App\Models\Router\Logs;
use App\Models\Store\Cart;
use Apps;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Illuminate\View\View;
use Modules\ShippingHours\ShippingHoursManager;
use OmniShip;
use Omniship\Common\ShippingQuote;
use Omniship\Common\ShippingQuoteBag;
use Throwable;
use stdClass;
use Exception;

/**
 * @property Cart $cart
 */
trait Shipping
{

    /**
     * @return Factory|View
     * @throws Throwable
     */
    public function shipping()
    {
        if ($this->cart->hasShippable() && ($this->cart->hasBillingAddress() ? $this->cart->getBillingAddress() : true)) {
            if (!is_null($shipping = $this->cart->getCheckout()->getShipping()) && !empty($shipping->service_id) && OmniShip::has($shipping->omniship_key)) {
                $shipping_price = Format::money(0);
                if ($this->cart->total('shipping.shipping') > 0) {
                    $shipping_price = $this->cart->total('shipping.shipping', 'formatted');
                }

                return view('checkout::steps.edit.shipping', [
                    'shipping' => $shipping,
                    'shipping_price' => $shipping_price
                ]);
            } elseif ($this->cart->getShippingAddress()) {
                return $this->changeShippingHelper();
            }
        }

        return $this->shippingEmpty();
    }

    /**
     * @return Factory|View
     * @throws Error
     * @throws Throwable
     */
    public function changeShipping()
    {
        $data = [
            'content_for_layout' => $this->changeShippingHelper()
        ];

        if(request()->ajax()) {
            return $data['content_for_layout'];
        }

        return $this->template($this->template, $data, $this->getShared('checkoutShipping'));
    }

    /**
     * @param $provider
     * @return Factory|View
     * @throws Error
     * @throws Throwable
     */
    public function quotes($provider)
    {
        $managers = OmniShip::getManagersGroupByType($this->cart->getZoneInformationShipping());
        if ($this->cart->getCheckout()->getShippingType() && $managers->has($this->cart->getCheckout()->getShippingType())) {
            $managers = $managers->get($this->cart->getCheckout()->getShippingType())->each(function (AbstractManager $manager): void {
                $manager->setOrder($this->cart->getCheckout());
            });
        } else {
            $managers = new Collection();
        }

        //@todo check payments providers if is only COD active check shippings where support COD
        if (($payments_managers = PaymentProviders::getConfigurations()) && $payments_managers->count() == 1 && $payments_managers->has('cod')) {
            $managers = $managers->filter(function (AbstractManager $manager) {
                return $manager->supportsCashOnDelivery();
            });
        }

        /** @var CustomerShippingAddress $address */
        if (!is_null($address = $this->cart->getShippingAddress()) && !is_null($office = $address->office_id)) {
            $managers = $managers->filter(function (AbstractManager $manager) use ($address): bool {
                return $manager->getKey() == $address->integration;
            });
        }

        if (!$managers->has($provider)) {
            app()->abort(404);
        }

        /** @var AbstractManager $manager */
        $manager = $managers->get($provider);
        $this->cart->setQuotes($manager->getKey(), null);

        try {
            if (($quotes = $this->cart->getQuotes($manager->getKey())) && $quotes->count()) {
                if ($quotes) {
                    $quotes = $manager->formatCalculations($this->cart->getCheckout()->getTotalBeforeShipping('input'), $quotes, null, $this->cart->getShippableProductsCategoryIds());
                }

                $manager->setCheckoutQuotes($quotes);
            } else {
                $request_response = new stdClass;
                $quotes = $manager->getQuotes($manager->getCheckoutCalculationParameters(), $request_response, $this->cart->getShippableProductsCategoryIds());
                if ($manager->supportFallBackByPriceQuote() && (!$quotes || !$quotes->count())) {
                    $quotes = $manager->getFallBackByPriceQuotes(new Exception(), $manager->getCheckoutCalculationParameters(), $this->cart->getShippableProductsCategoryIds());
                }

                $this->cart->setQuotes($manager->getKey(), $quotes ? $quotes->keyBy('id') : null);
                if (!empty($request_response->request)) {
                    $this->cart->setQuotesRequest($manager->getKey(), $request_response->request);
                }

                if (!empty($request_response->response)) {
                    $this->cart->setQuotesResponse($manager->getKey(), $request_response->response);
                }

                if ($quotes) {
                    $quotes = $manager->formatCalculations($this->cart->getCheckout()->getTotalBeforeShipping('input'), $quotes, null, $this->cart->getShippableProductsCategoryIds());
                    $manager->setCheckoutQuotes($quotes);
                }
            }
        } catch (Exception $exception) {
            if ($manager->supportFallBackQuote()) {
                try {
                    $quotes = $manager->getFallBackQuotes($exception, $manager->getCheckoutCalculationParameters(), $this->cart->getShippableProductsCategoryIds());
                    $this->cart->setQuotes($manager->getKey(), $quotes ? $quotes->keyBy('id') : null);
                    if ($quotes) {
                        $quotes = $manager->formatCalculations($this->cart->getCheckout()->getTotalBeforeShipping('input'), $quotes, null, $this->cart->getShippableProductsCategoryIds());
                        $manager->setCheckoutQuotes($quotes);
                    }
                } catch (Exception $exception) {
                }
            } else {
                Logs::create([
                    'message' => 'Shipping Quotes Error: ' . $manager->getKey() . ' - ' . $exception->getMessage(),
                    'file' => $exception->getFile(),
                    'line' => $exception->getLine(),
                    'trace' => $exception->getTraceAsString(),
                    'data' => $manager->getCheckoutCalculationParameters()
                ]);
            }
        }

        return view('checkout::include._shipping_provider_quotes', [
            'm' => $manager,
            'loaded' => [
                $manager->getKey() => true
            ],
            'manager' => $this->cart->getShipping(),
            'total_managers' => $managers->count(),
            'provider' => $manager->getProvider([]),
        ]);
    }

    /**
     * @param RecalculateShippingRequest $request
     * @return Response|array
     * @throws Error
     * @throws Throwable
     */
    public function recalculateShipping(RecalculateShippingRequest $request)
    {
        /** @var AbstractManager $manager */
        if (!$this->cart->hasShippable() || !($manager = $this->cart->getCheckout()->getShippingManager())) {
            return []; //error
        }

        $manager->setOrder($this->cart->getCheckout());
        $payment_provider = PaymentProviders::whereProvider($request->input('checkout.payment.provider'))->firstOrFail();

        $parameters = $manager->getCheckoutCalculationParameters(false);

        if ($request->input('checkout.payment.provider') == 'cod' && $manager->supportsCashOnDelivery()) {
            if (!is_null($total = $this->cart->getCheckout()->getTotalWithoutShipping('input'))) {
                $parameters['cash_on_delivery_amount'] = $total;
            }
        }

        $shipping = $this->cart->getShipping();
        try {
            $request_response = new stdClass();
            $quotes = $manager->getQuotes($parameters, $request_response, $this->cart->getShippableProductsCategoryIds());
            $this->cart->setQuotes($manager->getKey(), $quotes ? $quotes->keyBy('id') : null);
            if (!empty($request_response->request)) {
                $this->cart->setQuotesRequest($manager->getKey(), $request_response->request);
            }

            if (!empty($request_response->response)) {
                $this->cart->setQuotesResponse($manager->getKey(), $request_response->response);
            }
        } catch (Exception $exception) {
            throw new Error('Shipping quote service is invalid!');
        }

        /** @var ShippingQuoteBag $quotes */
        $quotes = $quotes ? $quotes->keyBy('id') : null;
        if (!$quotes || !$quotes->has($shipping->service_id)) {
            throw new Error('Shipping quote service is invalid!');
        }

        $old_quotes = $this->cart->getQuotes($manager->getKey());
        $this->cart->setQuotes($manager->getKey(), $quotes);
        if ($quotes) {
            $quotes = $manager->formatCalculations($this->cart->getCheckout()->getTotalBeforeShipping('input'), $quotes, null, $this->cart->getShippableProductsCategoryIds());
            $manager->setCheckoutQuotes($quotes);
        }

        /** @var ShippingQuote $quote */
        $this->cart->setShippingQuote($quotes->keyBy('id')->get($shipping->service_id));

        $this->cart->setPayment($payment_provider->fillFromCart($this->cart->getPayment())->toArray());

        $response = [
            'status' => 'success',
        ];

        if (!$old_quotes || $old_quotes->toArray() != $quotes->toArray()) {
            $response = array_merge($response, [
                'reload' => [
                    '.js-checkout-shipping',
                    '.js-checkout-summary'
                ],
            ]);
        }

        return response($response);
    }

    /**
     * @param ShippingRequest $request
     * @return array|RedirectResponse|Response
     * @throws Throwable
     */
    public function submitShipping(/** @noinspection PhpUnusedParameterInspection */
        ShippingRequest $request)
    {
        if ($this->cart->hasShippable() && !$this->cart->getShippingAddress()) {
            $this->cart->setStep('authorize');
            return redirect()->route('checkout');
        }

        $this->cart->setStep('shipping');

        return redirect()
            ->route('checkout');

        $response = [
            'status' => 'success',
            'replace' => [
                'selector' => '.js-checkout-shipping',
                'html' => $this->shipping(),
            ],
            'reload' => [
//                '.js-checkout-shipping',
                '.js-checkout-summary-totals',
                '.js-checkout-payment',
            ],
            'events' => ['cc.checkout.step'],
            'step' => $this->cart->getStep(),
        ];

        return response($response);
    }

    /**
     * @return Factory|View
     */
    public function shippingEmpty()
    {
        return view('checkout::steps.empty.shipping');
    }

    /**
     * @return Response|null
     * @throws Error
     * @throws Throwable
     */
    private function _checkSingleQuoteShippingManager()
    {
        $managers = OmniShip::getManagersGroupByType($this->cart->getZoneInformationShipping());
        if ($this->cart->getCheckout()->getShippingType() && $managers->has($this->cart->getCheckout()->getShippingType())) {
            $managers = $managers->get($this->cart->getCheckout()->getShippingType())->each(function (AbstractManager $manager): void {
                $manager->setOrder($this->cart->getCheckout());
            });
        } else {
            $managers = new Collection();
        }

        //@todo check payments providers if is only COD active check shippings where support COD
        if (($payments_managers = PaymentProviders::getConfigurations()) && $payments_managers->count() == 1 && $payments_managers->has('cod')) {
            $managers = $managers->filter(function (AbstractManager $manager) {
                return $manager->supportsCashOnDelivery();
            });
        }

        /** @var CustomerShippingAddress $address */
        if (!is_null($address = $this->cart->getShippingAddress()) && !is_null($office = $address->office_id)) {
            $managers = $managers->filter(function (AbstractManager $manager) use ($address): bool {
                return $manager->getKey() == $address->integration;
            });
        }

        /** @var Collection $managers */
        $managers = $managers->map(function (AbstractManager $manager) use (&$loaded): \App\Integration\OmniShip\AbstractManager {
            if (($quotes = $this->cart->getQuotes($manager->getKey())) && $quotes->count()) {
                $manager->setCheckoutQuotes($quotes);
            }

            return $manager;
        });

        if ($managers->count() != 1) {
            return null;
        }

        /** @var AbstractManager $manager */
        $manager = $managers->first();

        if (Apps::installed(ShippingHoursManager::APP_KEY)) {
            $provider = $manager->getProvider([]);
            if ($provider->has_delivery_dates) {
                return null;
            }
        }

        $quotes = null;
        if ($manager->hasCheckoutQuotes()) {
            $quotes = $manager->getCheckoutQuotes();
        } else {
            try {
                $request_response = new stdClass;
                $quotes = $manager->getQuotes($manager->getCheckoutCalculationParameters(), $request_response, $this->cart->getShippableProductsCategoryIds());
                if (!empty($request_response->request)) {
                    $this->cart->setQuotesRequest($manager->getKey(), $request_response->request);
                }

                if (!empty($request_response->response)) {
                    $this->cart->setQuotesResponse($manager->getKey(), $request_response->response);
                }
            } catch (Exception $e) {
            }
        }

        if (!$quotes || $quotes->count() != 1) {
            return null;
        }

        $this->cart->setQuotes($manager->getKey(), $quotes);

        if ($quotes) {
            $quotes = $manager->formatCalculations($this->cart->getCheckout()->getTotalBeforeShipping('input'), $quotes, $manager->getQuotesRequest($manager->getCheckoutCalculationParameters()), $this->cart->getShippableProductsCategoryIds());
        }

        $this->cart->setShipping($manager->getProvider([])->toArray())->setShippingQuote($quotes->first(), $quotes);

        $this->cart->setStep('shipping');

        return redirect()
            ->route('checkout');

        $response = [
            'status' => 'success',
            'reload' => [
                '.js-checkout-shipping-address',
                '.js-checkout-billing-address',
                '.js-checkout-shipping',
                '.js-checkout-summary',
                '.js-checkout-payment',
            ],
            'events' => ['cc.checkout.step', 'cc.overlay.hide'],
            'step' => $this->cart->getStep(),
        ];

        return response($response);
    }

    ////////////////////////////// HELPERS ////////////////////////////////////////


    /**
     * @return Factory|View
     * @throws Error
     * @throws Throwable
     */
    public function changeShippingHelper()
    {
        $managers = OmniShip::getManagersGroupByType($this->cart->getZoneInformationShipping());
        if ($this->cart->getCheckout()->getShippingType() && $managers->has($this->cart->getCheckout()->getShippingType())) {
            $managers = $managers->get($this->cart->getCheckout()->getShippingType())->each(function (AbstractManager $manager): void {
                $manager->setOrder($this->cart->getCheckout());
            });
        } else {
            $managers = new Collection();
        }

        //@todo check payments providers if is only COD active check shippings where support COD
        if (($payments_managers = PaymentProviders::getConfigurations()) && $payments_managers->count() == 1 && $payments_managers->has('cod')) {
            $managers = $managers->filter(function (AbstractManager $manager) {
                return $manager->supportsCashOnDelivery();
            });
        }

        /** @var CustomerShippingAddress $address */
        if (!is_null($address = $this->cart->getShippingAddress()) && !is_null($office = $address->office_id)) {
            $managers = $managers->filter(function (AbstractManager $manager) use ($address): bool {
                return $manager->getKey() == $address->integration;
            });
        }

        $loaded = [];
        /** @var Collection $managers */
        $managers = $managers->map(function (AbstractManager $manager) use (&$loaded): \App\Integration\OmniShip\AbstractManager {
            if (($quotes = $this->cart->getQuotes($manager->getKey())) && $quotes->count()) {
                $quotes = $manager->formatCalculations($this->cart->getCheckout()->getTotalBeforeShipping('input'), $quotes, $manager->getQuotesRequest($manager->getCheckoutCalculationParameters()), $this->cart->getShippableProductsCategoryIds());
                $manager->setCheckoutQuotes($quotes);
            }

            $loaded[$manager->getKey()] = $manager->hasCheckoutQuotes();
            return $manager;
        });

        if ($managers->count() == 1 && !activeRoute('checkout.shipping.shipping.change')) {
            $this->cart->setShipping($managers->first()->getProvider([])->toArray());
        }

        return view('checkout::steps.shipping', [
            'managers' => $managers,
            'loaded' => $loaded,
            'manager' => $this->cart->getShipping(),
        ]);
    }

}
