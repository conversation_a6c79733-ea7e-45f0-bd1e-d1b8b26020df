<?php

declare(strict_types=1);

namespace App\LiquidEngine\Traits\Http\Customer;

use App\Events\CustomerBillingAddressAdd;
use App\Events\CustomerBillingAddressEdit;
use App\Http\Request\SiteV2\Customer\AddressRequest;
use App\Models\Customer\CustomerBillingAddress as CustomerBillingAddressModel;
use Exception;
use Illuminate\Contracts\View\Factory;
use Illuminate\Foundation\Application;
use Illuminate\View\View;
use Throwable;

trait CustomerBillingAddress
{

    protected $_billing_address_error;

    /**
     * @return Factory|Application|View
     * @throws Throwable
     */
    protected function getBillingAddressCreate()
    {
        $this->checkEnabledBillingAddress();

        $customer = $this->getCustomer();

        return $this->getBillingAddressForm($customer->billing_addresses()->getModel());
    }

    /**
     * @param AddressRequest $request
     * @return bool|CustomerBillingAddressModel
     * @throws Throwable
     */
    protected function postBillingAddressCreate(AddressRequest $request)
    {
        $this->checkEnabledBillingAddress();

        $customer = $this->getCustomer();
        try {
            $address = \Illuminate\Support\Facades\DB::transaction(function () use ($customer, $request) {
                $address = $customer->billing_addresses()->create($request->input());
                if($request->has('set_as_default')) {
                    $customer->update([
                        'default_billing_address_id' => $address->id
                    ]);
                }

                return $address;
            });

            event(new CustomerBillingAddressAdd($address));

            return $address;
        } catch (Exception $exception) {
            return !($this->_billing_address_error = $exception);
        }
    }

    /**
     * @param $address_id
     * @return Factory|Application|View
     * @throws Throwable
     */
    protected function getBillingAddressUpdate($address_id)
    {
        $this->checkEnabledBillingAddress();

        $customer = $this->getCustomer();

        /** @var CustomerBillingAddressModel $address */
        $address = $customer->billing_addresses()->find($address_id);
        if (!$address) {
            app()->abort(404, __('sf.account.details.warn.address_no_longer_exists'));
        }

        if ($address->customer_id != $customer->id) {
            app()->abort(404, __('global.err.this_address_is_not_your'));
        }

        return $this->getBillingAddressForm($address);
    }

    /**
     * @param AddressRequest $request
     * @param $address_id
     * @return bool|CustomerBillingAddressModel
     * @throws Throwable
     */
    protected function postBillingAddressUpdate(AddressRequest $request, $address_id)
    {
        $this->checkEnabledBillingAddress();

        $customer = $this->getCustomer();

        /** @var CustomerBillingAddressModel $address */
        $address = $customer->billing_addresses()->find($address_id);
        if (!$address) {
            app()->abort(404, __('sf.account.details.warn.address_no_longer_exists'));
        }

        if ($address->customer_id != $customer->id) {
            app()->abort(404, __('global.err.this_address_is_not_your'));
        }

        try {
            $address = \Illuminate\Support\Facades\DB::transaction(function () use ($address, $request, $address_id) {
                $address->update($request->input());
                return $address;
            });

            event(new CustomerBillingAddressEdit($address));

            return $address;
        } catch (Exception $exception) {
            return !($this->_billing_address_error = $exception);
        }
    }

    /**
     * @param CustomerBillingAddressModel $address
     * @param array $data
     * @return Factory|Application|View
     */
    protected function getBillingAddressForm(CustomerBillingAddressModel $address, array $data = [])
    {
        $this->checkEnabledBillingAddress();

        return view('checkout::customer.address.form', [
            'total_addresses' => $this->getCustomer()->billing_addresses()->count(),
            'type' => 'billing',
            'address' => $address,
            'title' => $address->exists ? __('customer.text.edit_address') : __('customer.text.create_address'),
        ], $data);
    }

    /**
     * @return null|Exception
     */
    protected function getBillingAddressPostError()
    {
        return $this->_billing_address_error;
    }

    /**
     *
     */
    protected function checkEnabledBillingAddress()
    {
        if (setting('checkout_hide_billing_address')) {
            app()->abort(404);
        }
    }

}
