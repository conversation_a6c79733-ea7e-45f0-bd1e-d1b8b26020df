<?php

declare(strict_types=1);

namespace App\LiquidEngine\Traits;

use App\LiquidEngine\Helpers\RenderSection;
use App\LiquidEngine\Services\TemplateLoader;
use App\LiquidEngine\Services\TemplateResolver;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\View\Factory;
use Illuminate\View\View;
use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;

trait ThemeResponse
{
    /**
     * @var TemplateResolver
     */
    protected $templateResolver;

    /**
     * @var TemplateLoader
     */
    protected $templateLoader;

    /**
     * Get the template resolver instance
     *
     * @return TemplateResolver
     */
    protected function getTemplateResolver(): TemplateResolver
    {
        if (!isset($this->templateResolver)) {
            $this->templateResolver = App::make(TemplateResolver::class);
        }

        return $this->templateResolver;
    }

    /**
     * Get the template loader instance
     *
     * @return TemplateLoader
     */
    protected function getTemplateLoader(): TemplateLoader
    {
        if (!isset($this->templateLoader)) {
            $this->templateLoader = App::make(TemplateLoader::class);
        }

        return $this->templateLoader;
    }

    /**
     * @param array $data
     * @param int $status
     * @param array $headers
     * @return ResponseFactory|Response
     */
    public function response(array $data = [], $status = 200, array $headers = [])
    {
        $request = request();
        if ($request->isMethod('get') && !empty($callback = $request->query('callback')) && !$request->expectsJson()) {
            return response()->jsonp($callback, $data, $status, $headers);
        }

        if (!empty($output = $request->input('_outputFormat')) && method_exists($this, $method = sprintf('_outputFormat%s', \Illuminate\Support\Str::studly($output)))) {
            $data = $this->$method($data, $status, $headers);
        }

        return response($data, $status, $headers);
    }

    /**
     * @param Template $template
     * @param array $data
     * @param array $mergeData
     * @return ResponseFactory|Response|View
     */
    public function templateJson(Template $template, $data = [], $mergeData = [])
    {
        $request = request();
        if($request->ajax()) {
            return $this->responseWithSettings($template, $data);
        }

        return $this->template($template, $data, $mergeData);
    }

    /**
     * @param Template $template
     * @param array $data
     * @param array $mergeData
     * @return ResponseFactory|Response|View
     */
    public function responseWithSettings(Template $template, $data = [], $mergeData = [])
    {
        $data = array_merge(
            $data,
            $mergeData,
            array_filter([
                'template' => $template,
                'breadcrumbs' => method_exists($this, 'breadcrumbs') ? $this->breadcrumbs() : []
            ])
        );

        return $this->response($data);
    }

    /**
     * Render a template with data
     *
     * If no template is provided, it will be automatically resolved based on the controller and action
     *
     * @param Template|string|null $template The template to render (optional)
     * @param array $data The data to pass to the template
     * @param array $mergeData Additional data to merge with the template data
     * @return RenderSection|\Liquid\Factory
     */
    public function template($template = null, $data = [], $mergeData = [])
    {
        // old return
//        if(($section = request()->input('section_id'))) {
//            return (new RenderSection($section))
//                ->with(array_merge($data, $mergeData))
//                ->with(['template', $template]);
//        }
//
//        /** @var \Liquid\Factory $factory */
//        $factory = liquid()->first($template->getTemplates(), array_merge($data, $mergeData));
//
//        return $factory
//            ->with('template', $template);

        // new return

        // If no template is provided, resolve it automatically
        if ($template === null) {
            // Get the called class and method
            $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
            $caller = $trace[1] ?? null;

            if (!$caller || !isset($caller['function'])) {
                throw new \RuntimeException('Could not determine caller method');
            }

            // Resolve template based on controller and action
            $template = $this->getTemplateResolver()->resolveTemplate($this, $caller['function']);
        } elseif (is_string($template)) {
            $template = new Template($template);
        }

//        dd($template);

        // Handle section rendering if requested
        $sectionParam = Config::get('liquid.section_param', 'section_id');
        if (($section = request()->input($sectionParam))) {
            return (new RenderSection($section))
                ->with(array_merge($data, $mergeData))
                ->with(['template', $template]);
        }

        // Use the template loader service for rendering
        return $this->getTemplateLoader()->load($template, $data, $mergeData);
    }

    /**
     * Render a template using automatic template resolution
     *
     * @param array $data The data to pass to the template
     * @param array $mergeData Additional data to merge with the template data
     * @return \Liquid\Factory
     */
    public function renderTemplate(array $data = [], array $mergeData = [])
    {
        // Get the called class and method
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
        $caller = $trace[1] ?? null;

        if (!$caller || !isset($caller['function'])) {
            throw new \RuntimeException('Could not determine caller method');
        }

        // Resolve template based on controller and action
        $template = $this->getTemplateResolver()->resolveTemplate($this, $caller['function']);

        // Use the template method to render the template
        return $this->template($template, $data, $mergeData);
    }

    /**
     * Render a specific template
     *
     * @param string $templatePath The specific template path to render
     * @param array $data The data to pass to the template
     * @param array $mergeData Additional data to merge with the template data
     * @return \Liquid\Factory
     */
    public function renderSpecificTemplate(string $templatePath, array $data = [], array $mergeData = [])
    {
        // Create a Template object from the path
        $template = new Template($templatePath);

        // Use the template method to render the template
        return $this->template($template, $data, $mergeData);
    }

    /**
     * @param Template $template
     * @param array $data
     * @param int $status
     * @param array $headers
     * @return ResponseFactory|Response
     */
    public function templateResponse(Template $template, $data = [], $status = 200, array $headers = [])
    {
        return response($this->template($template, $data), $status, $headers);
    }

    /**
     * @param array $data
     * @return mixed|null
     */
    public function templateShare($data = [])
    {
        /** @var View|Factory $factory */
        $factory = liquid();
        if(is_string($data)) {
            return $factory->shared($data);
        } elseif(is_array($data)) {
            return $factory->share($data);
        }

        return null;
    }
}
