<?php

declare(strict_types=1);

namespace App\LiquidEngine\Traits\Request;

use App\Models\Layout\FormFields;
use Illuminate\Support\Collection;

trait CustomFields
{

    /**
     * @var Collection|FormFields[]
     */
    protected $custom_fields;

    /**
     * @return array
     */
    protected function getCustomRules(): array {
        $rules = [];
        if(($customs = $this->getCustomFields()) && $customs->count()) {
            foreach($customs AS $custom) {
                $suffix = $custom->type == 'phone' ? '.phone' : '';
                $rules[$this->getPrefix() . 'custom.' . $custom->id . $suffix] = null;
                if(in_array($custom->type, ['checkbox'])) {
                    $rules[$this->getPrefix() . 'custom.' . $custom->id . $suffix] = 'array';
                }

                if($custom->required) {
                    $rules[$this->getPrefix() . 'custom.' . $custom->id . $suffix] .= '|required';
                }

                if($custom->type == 'phone') {
                    $rules[$this->getPrefix() . 'custom.' . $custom->id . $suffix] = 'phone_number:' . $this->getPrefix() . 'custom.' . $custom->id . '.country';
                }
            }
        }

        return array_filter($rules);
    }

    /**
     * @return array
     */
    protected function getCustomAttributes(): array
    {
        $attributes = [];
        if(($customs = $this->getCustomFields()) && $customs->count()) {
            foreach($customs AS $custom) {
                if(in_array($custom->type, ['checkbox'])) {
                    $attributes[$this->getPrefix() . 'custom.' . $custom->id] = $custom->storefront_name;
                } else {
                    $suffix = $custom->type == 'phone' ? '.phone' : '';
                    $attributes[$this->getPrefix() . 'custom.' . $custom->id . $suffix] = $custom->storefront_name;
                }
            }
        }

        return $attributes;
    }

    /**
     * @return \App\Models\Layout\FormFields[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection
     */
    protected function getCustomFields()
    {
        if(!is_null($this->custom_fields)) {
            return $this->custom_fields;
        }

        return $this->custom_fields = FormFields::getByForm('register');
    }

    protected function getPrefix(): string
    {
        return activeRoute('checkout.shipping.address') ? sprintf('%s.', request()->input('checkout.shipping.type')) : '';
    }
}
