<?php

declare(strict_types=1);

namespace App\LiquidEngine\Traits\Request;

use App\Helper\ArrayCache;
use App\Models\Product\Options;
use App\Models\Product\BundleProduct;
use App\Models\Store\CartItem;
use App\Models\System\AppsManager;
use App\Models\Product\Variant;
use App\Models\Product\Product;
use Illuminate\Support\Collection;

trait AddToCartProduct
{
    /**
     * @var CartItem
     */
    protected $_cart_item;

    /**
     * @return null|Product
     */
    protected function getProduct()
    {
        $request = request();
        if (!empty($variant_id = $request->input('variant_id')) && !empty($variant = $this->getVariant())) {
            return ArrayCache::remember('product-add-cart.product_by_variant_id.' . $variant_id, function () use ($variant) {
                $product = Product::withHidden()->listing()->find($variant->item_id);
                if ($product && $product->type == Product::TYPE_BUNDLE) {
                    return null;
                }

                if ($product) {
                    $product->format();
                    $product->setAttribute('cart_variant', $variant);
                }

                return $product;
            });
        } elseif (!empty($product_id = $request->input('product_id'))) {
            return ArrayCache::remember('product-add-cart.product.' . $product_id, function () use ($product_id) {
                $product = Product::withHidden()->listing()->find($product_id);
                if ($product && $product->type == Product::TYPE_BUNDLE) {
                    return null;
                }

                if ($product) {
                    $product->format();
                    $product->setAttribute('cart_variant', $product->variant);
                }

                return $product;
            });
        }

        return null;
    }

    /**
     * @return null|Variant
     */
    protected function getVariant()
    {
        if (!empty($variant_id = request()->input('variant_id'))) {
            return ArrayCache::remember('product-add-cart.variant.' . $variant_id, function () use ($variant_id) {
                return Variant::find($variant_id);
            });
        }

        return null;
    }

    /**
     * @return null|Product
     */
    protected function getBundle()
    {
        $request = request();
        if (empty($bundle_id = $request->input('bundle_id'))) {
            if (!empty($variant_id = $request->input('variant_id'))) {
                return ArrayCache::remember('bundle-add-cart.variant.' . $variant_id, function () use ($variant_id) {
                    return Product::withHidden()->whereTypeBundle()->listing()->with(['bundle_products' => function ($q): void {
                        /** @var BundleProduct $q */
                        $q->listing();
                    }])->whereHas('variants', function ($query) use ($variant_id): void {
                        /** @var Variant $query */
                        $query->whereId($variant_id);
                    })->first();
                });
            }

            return null;
        }

        return ArrayCache::remember('bundle-add-cart.' . $bundle_id, function () use ($bundle_id) {
            return Product::withHidden()->whereTypeBundle()->listing()->with(['bundle_products' => function ($q): void {
                /** @var BundleProduct $q */
                $q->listing();
            }])->find($bundle_id);
        });
    }

    /**
     * @return CartItem
     */
    protected function getCartItem()
    {
        if (!is_null($this->_cart_item)) {
            return $this->_cart_item;
        }

        /** @var null|\App\Models\Product\Variant $cart_variant */
        if (empty($product = $this->getProduct()) || empty($cart_variant = $product->getAttribute('cart_variant'))) {
            return null;
        }

        $request = request();
        $quantity = (int)$request->input('quantity', 1);
        if ($quantity < 1) {
            $quantity = 1;
        }

        return $this->_cart_item = new CartItem([
            'variant_id' => (int)$cart_variant->id,
            'product_id' => (int)$product->id,
            'quantity' => (int)$quantity,
            'up_sell_trigger_variant_id' => $request->input('up_sell_trigger_variant_id')
        ]);
    }

    /**
     * @param Product|null $product
     * @return Collection|Options[]
     */
    public function getProductFields(Product $product = null)
    {
        if (!$product || !$product->exists || !AppsManager::isInstalled('product_options')) {
            return collect();
        }

        return ArrayCache::remember('product.fields.' . $product->id, function () use ($product) {
            $fields = $product->fields()->get();
            if ($product->category) {
                $fields = $fields->merge($product->category->categoryPathWithFields->pluck('path.fields')->collapse());
            }

            if ($product->vendor) {
                $fields = $fields->merge($product->vendor->fields);
            }

            return $fields->sortBy('sort_order', SORT_NUMERIC)->values();
        });
    }
}
