<?php

declare(strict_types=1);

namespace App\LiquidEngine\ViewFinder;

use App\Models\Themes\Themes;
use Liquid\TemplateContent;
use Liquid\ViewFinders\DatabaseViewFinder;

class CustomDatabaseViewFinder extends DatabaseViewFinder
{
    /**
     * @param mixed $name
     * @param mixed $tables
     * @return mixed
     */
    protected function findInDriver($name, $tables): \Liquid\TemplateContent
    {
        foreach ((array) $tables as $table) {
            foreach($this->extensions ? : ['liquid'] as $extension) {
                if ($template = $this->findOverrideTemplate($table . '/' . $name . '.' . $extension, $table)) {
                    return new TemplateContent(
                        $template->content,
                        strtotime($template->updated_at),
                        $table . '::' . $name . '.' . $extension,
                        $name,
                        $extension
                    );
                }

                if ($template = $this->findDefaultTemplate($table . '/' . $name . '.' . $extension, $table)) {
                    return new TemplateContent(
                        $template->content,
                        strtotime($template->updated_at),
                        $table . '::' . $name . '.' . $extension,
                        $name,
                        $extension
                    );
                }
            }
        }

        throw new \InvalidArgumentException(sprintf('View [%s] not found.', $name));
    }

    /**
     * @param mixed $file
     * @param mixed $table
     * @return mixed
     */
    protected function findDefaultTemplate($file, $table)
    {
        $key = sprintf('template_%d_%s__%s', $this->getTheme()->id, $this->getThemeLastVersion(), str_replace(['/', '.'], '_', $file));
        return \Cache::remember($key, config('cache.ttl_1h'), function() use($table, $file) {
            return $this->table($table)->where('version_id', $this->getThemeLastVersion())
                ->where('path', $file)->first();
        });
    }

    /**
     * @param mixed $file
     * @param mixed $table
     * @return mixed
     */
    protected function findOverrideTemplate($file, $table): null
    {
        return null;
    }

    /**
     * @return Themes
     */
    protected function getTheme()
    {
        return site('theme');
    }

    /**
     * @return null|int
     */
    protected function getThemeLastVersion()
    {
        return $this->getTheme()->latest_version->id ?? null;
    }

}
