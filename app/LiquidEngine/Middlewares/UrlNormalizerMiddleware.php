<?php

declare(strict_types=1);

namespace App\LiquidEngine\Middlewares;

use App\LiquidEngine\Services\UrlNormalizer;
use Closure;
use Illuminate\Http\Request;

/**
 * URL Normalizer Middleware
 * 
 * Normalizes URLs and handles redirects for canonical paths.
 */
class UrlNormalizerMiddleware
{
    /**
     * @var UrlNormalizer
     */
    protected UrlNormalizer $normalizer;
    
    /**
     * Constructor
     *
     * @param UrlNormalizer $normalizer
     */
    public function __construct(UrlNormalizer $normalizer)
    {
        $this->normalizer = $normalizer;
    }
    
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip normalization for API requests and admin panel
        if ($request->is('api/*') || $request->is('sitecp/*')) {
            return $next($request);
        }
        
        // Check if URL needs normalization
        $redirectPath = $this->normalizer->normalizeCurrentUrl();
        
        // If a redirect is needed, return a 301 redirect
        if ($redirectPath !== null) {
            return redirect($redirectPath, 301);
        }
        
        return $next($request);
    }
} 