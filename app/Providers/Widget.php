<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 15.6.2016 г.
 * Time: 16:48 ч.
 */

namespace App\Providers;

use App\Helper\Widget as WidgetBuilder;
use Illuminate\Support\ServiceProvider;

class Widget extends ServiceProvider
{
    #[\Override]
    public function register(): void
    {
        $this->app->singleton('widget', fn(): \App\Helper\Widget => new WidgetBuilder());
        
        // Ensure W class is available by loading it
        if (!class_exists(\W::class)) {
            class_alias(\App\Helper\Widgets\W::class, 'W');
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    #[\Override]
    public function provides()
    {
        return ['widget'];
    }

}
