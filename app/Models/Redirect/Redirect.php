<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 27.5.2016 г.
 * Time: 12:01 ч.
 */

namespace App\Models\Redirect;

use App\Common\Section;
use App\Helper\Cache\CcCache;
use App\Models\Base\AbstractRedirects;
use App\Models\Blog\Article;
use App\Models\Blog\Blog;
use App\Models\Page\Page;
use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Models\Product\Vendor;
use App\Models\Router\Host;
use App\Scopes\HiddenProduct;
use App\Traits\Crudling;

/**
 * @property Product|Category|Vendor|Page|Blog|Article $item
 */
class Redirect extends AbstractRedirects
{
    use Crudling;

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'value',
        'link_formatted',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $data = [];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    #[\Override]
    protected static function boot()
    {
        parent::boot();

        static::saving(function (Redirect $redirect): void {
            if (in_array($redirect->location, ['product', 'category', 'vendor', 'blog', 'article', 'page'])) {
                $redirect->item_type = $redirect->location;
            } else {
                $redirect->item_type = null;
            }

            if ($redirect->location == 'section') {
                $redirect->new_url = Section::toRoute($redirect->new_url);
            } elseif ($redirect->location == 'manual') {
                $redirect->new_url = $redirect->parseNewUrl($redirect->new_url);
            } elseif ($redirect->location == 'external') {
                if (!preg_match('~^https?:\/\/~i', $redirect->new_url)) {
                    $redirect->new_url = 'http://' . $redirect->new_url;
                }
            }

            CcCache::flush(['redirects301']);
        });

        static::creating(function (Redirect $redirect) {
            if (in_array($redirect->location, ['product', 'category', 'vendor'])) {
                $url = match ($redirect->location) {
                    'product' => '/product/' . Product::where('id', $redirect->item_id)->value('url_handle'),
                    'category' => '/category/' . Category::where('id', $redirect->item_id)->value('url_handle'),
                    'vendor' => '/vendor/' . Vendor::where('id', $redirect->item_id)->value('url_handle'),
                    default => null,
                };
                if ($redirect->old_url == $url) {
                    return false;
                }

                CcCache::flush(['redirects301']);
            }
        });

        static::saved(function (): void {
            CcCache::flush(['redirects301']);
        });

        static::deleted(function (): void {
            CcCache::flush(['redirects301']);

            setting()->set('has_301_redirects', Redirect::exists())->save();
        });
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function item()
    {
        /** @var \Illuminate\Database\Eloquent\Relations\MorphTo|Product|Category|Vendor|Page|Blog|Article $query */
        $query = $this->morphTo('item');
        $query->withoutGlobalScope(new HiddenProduct());
        return $query;
    }

    /**
     * @param null $url
     * @return mixed|string
     */
    public function parseOldUrl($url = null)
    {
        if (!$url) {
            $url = $this->old_url;
        }

        $url = urldecode($url);
        $url_parts = parse_url($url);
        $url = $url_parts['path'] ?? $url;
        if (!empty($url_parts['query'])) {
            $url .= '?' . $url_parts['query'];
        }

        return $url;
    }

    /**
     * @param $url
     * @return mixed|string
     */
    private function parseNewUrl($url): string|array
    {
        $new_url = '';
        $url = urldecode((string)$url);
        $url_parts = parse_url($url);
        if ($url_parts) {
            if (isset($url_parts['host'])) {
                $site_hosts = Host::getHostsBySiteId(site('site_id'));
                if ($site_hosts->where('host', $url_parts['host'])->count()) {
                    $strip = $url_parts['host'];
                    if (isset($url_parts['scheme'])) {
                        $strip = $url_parts['scheme'] . '://' . $strip;
                    }

                    $new_url = str_replace($strip, '', $url);
                } else {
                    $new_url = $url;
                }
            } else {
                $new_url = $url;
            }
        }

        return $new_url;
    }

    /**
     * @return array
     */
    public static function getTypes(): array
    {
        return [
            'manual' => __('redirects.type.manual'),
            'external' => __('redirects.type.external'),
            'product' => __('widget.extra.banner.product'),
            'category' => __('widget.extra.banner.product_category'),
            'vendor' => __('widget.extra.banner.vendor'),
            'blog' => __('widget.extra.banner.blog'),
            'article' => __('widget.extra.banner.blog_article'),
            'page' => __('widget.extra.banner.page'),
            'section' => __('widget.extra.banner.section'),
        ];
    }

    public static function getTypesKeys()
    {
        return array_keys(self::getTypes());
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getValueAttribute($value)
    {
        if (in_array($this->location, ['category', 'page'])) {
            return $this->item_id;
        } elseif ($this->location == 'section') {
            return Section::toPath($this->new_url);
        } elseif (in_array($this->location, ['manual', 'external'])) {
            return $this->new_url;
        } elseif (in_array($this->location, ['product', 'category', 'vendor', 'blog', 'article', 'page']) && $this->item) {
            return json_encode(['id' => $this->item_id, 'name' => $this->item->name]);
        }
        return;
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getLinkFormattedAttribute($value)
    {
        if (!empty($this->data['link_formatted'])) {
            return $this->data['link_formatted'];
        }
        if ($this->location == 'section' && routeExists($this->new_url)) {
            return $this->data['link_formatted'] = route($this->new_url);
        } elseif ($this->item_id && $this->item_type && ($this->item && method_exists($this->item, 'urlWithHidden') || $this->item && method_exists($this->item, 'url'))) {
            if (method_exists($this->item, 'urlWithHidden')) {
                return $this->data['link_formatted'] = $this->item->urlWithHidden();
            } else {
                return $this->data['link_formatted'] = $this->item->url();
            }
        } elseif ($this->location == 'external') {
            return $this->data['link_formatted'] = $this->new_url;
        } elseif ($this->location == 'manual') {
            $url_parts = parse_url($this->new_url);
            foreach (['scheme', 'host'] as $key) {
                if (!empty($url_parts[$key])) {
                    unset($url_parts[$key]);
                }
            }

            return $this->data['link_formatted'] = site()->getSiteUrl('primary') . build_url($url_parts);
        }
        return;
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getRedirectTypeAttribute($value)
    {
        return $this->location;
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getFullNewUrlAttribute($value)
    {
        return $this->link_formatted;
    }

    /**
     * @return int|string|null
     */
    public function getNewUrlFormatted()
    {
        if ($this->location == 'section') {
            return Section::fromRoute($this->new_url);
        }
        return $this->new_url;
    }

}
