<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 2.8.2016 г.
 * Time: 15:20 ч.
 */

namespace App\Models\Gateway;

use App\Common\Media;
use App\Contracts\AdditionalAllowByProductId;
use App\Helper\Cache\CcCache;
use App\Models\Router\SiteInstalledApps;
use Illuminate\Database\Query\Builder;
use App\Integration\Payment\PaymentGatewayManager;
use App\Traits\DbTimezone;
use Illuminate\Database\Eloquent\Collection;
use App\Helper\YesNo;
use App\Traits\Image;
use App\Helper\AES;
use Modules\CcSegments\Models\SiteEventLog;
use PaymentGateway;
use Illuminate\Database\Eloquent\Model as Eloquent;

/**
 * @property  array $configuration
 * @property mixed $provider
 * @property mixed $title
 * @property string $active
 * @property PaymentProviders $payment_provider
 * @property string $storefront_name
 * @property null|string $type
 * @property string $min_price
 * @property int $site_id
 * @method  PaymentProviderConfiguration|Builder site()
 * @method  Builder active()
 * @method  Builder inactive()
 * @method  static Builder join($table, $one, $operator = null, $two = null, $type = 'inner', $where = false)
 */
class PaymentProviderConfiguration extends Eloquent
{
    use DbTimezone;

    use Image {
        Image::getImage as traitGetImage;
    }

    protected $connection = 'payments';

    /**
     * @var string
     */
    protected $table = 'cc_gateway.payment_configrations';

    /**
     * @var array
     */
    protected $appends = [
        'storefront_name'
    ];

    /**
     * @var array
     */
    protected $fillable = [
        'title',
        'provider',
        'site_id',
        'active',
        'created_at',
    ];

    /**
     *
     */
    #[\Override]
    protected static function boot()
    {
        parent::boot();

        static::saving(function (PaymentProviderConfiguration $record): void {
            if (is_array($record->configuration)) {
                $record->configuration = AES::crypt(json_encode($record->configuration));
            }
        });
        self::saved(function (self $record): void {
            SiteInstalledApps::updateOrCreate([
                'site_id' => $record->site_id,
                'mapping' => $record->provider,
            ], [
                'installed' => 1,
                'active' => YesNo::toBool($record->active),
            ]);
            static::invalidateCache();
        });
        self::created(function (self $record): void {
            SiteEventLog::add($record->site_id, SiteEventLog::TYPE_PAYMENT_INSTALLED, $record);
        });
        self::deleted(function (self $record): void {
            SiteEventLog::add($record->site_id, SiteEventLog::TYPE_PAYMENT_UNINSTALLED, $record);
            static::invalidateCache();
        });
    }

    private static function invalidateCache(): void
    {
        CcCache::forget(config('cache.router.payments-key'));
    }

    /**
     * @param $query
     */
    public function scopeSite($query): void
    {
        /** @var \Illuminate\Database\Eloquent\Builder $query */
        $query->where('site_id', site('site_id'));
    }

    /**
     * @param $query
     */
    public function scopeActive($query): void
    {
        /** @var \Illuminate\Database\Eloquent\Builder $query */
        $query->where($this->table . '.active', YesNo::True);
    }

    /**
     * @param $query
     */
    public function scopeInactive($query): void
    {
        /** @var \Illuminate\Database\Eloquent\Builder $query */
        $query->where($this->table . '.active', YesNo::False);
    }

    /**
     * @param $provider
     * @return PaymentProviderConfiguration
     */
    public static function createNewConfiguration($provider): \App\Models\Gateway\PaymentProviderConfiguration
    {
        $settings = new PaymentProviderConfiguration([
            'provider' => $provider,
            'site_id' => site('site_id'),
            'active' => YesNo::False,
        ]);

        $settings->configuration = [
            'amount_from' => 0,
            'amount_to' => null,
            'discount_amount' => null,
            'discount_type' => null,
        ];

        $settings->load('payment_provider');
        $settings->fill(['title' => $settings->storefront_name]);
        $settings->offsetUnset('storefront_name');

        $settings->save();

        return $settings;
    }

    /**
     * @param bool|null $active
     *
     * @return Collection|PaymentProviderConfiguration[]
     */
    public static function getConfigurations(?bool $active = true)
    {
        $configurations = CcCache::remember(config('cache.router.payments-key'), config('cache.ttl_1d'), fn() => static::join(
            'cc_gateway.[payment_providers] as pp',
            'pp.provider',
            'payment_configrations.provider'
        )
            ->orderBy('pp.sort_order')
            ->select('payment_configrations.*')
            ->with('payment_provider')
            ->site()
            ->get()
            ->keyBy('provider'));

        $configurations = $configurations
            ->whereNotIn('provider', PaymentProviders::$deprecated)
            ->filter(fn($configuration): bool => (bool) $configuration->payment_provider->active);

        $configurations->transform(function (PaymentProviderConfiguration $configuration) {
            $configuration->payment_provider->setRelation('configuration', (clone $configuration)->unsetRelation('payment_provider'));
            return $configuration;
        });

        if ($active) {
            return $configurations->where('active', YesNo::True);
        } elseif (false === $active) {
            return $configurations->where('active', YesNo::False);
        }

        return $configurations;
    }

    public function payment_provider()
    {
        return $this->hasOne(PaymentProviders::class, 'provider', 'provider');
    }

    /**
     * @return mixed|null
     */
    public function getAmountTo()
    {
        return $this->configuration['amount_to'] ?? null;

    }

    /**
     * @return mixed|null
     */
    public function getAmountFrom()
    {
        return $this->configuration['amount_from'] ?? null;
    }

    /**
     * @param mixed $thumb_size
     * @return mixed
     */
    public function getImage($thumb_size = null)
    {
        if (!$this->hasImage()) {
            return Media::payment_provider($this->provider);
        }

        return $this->traitGetImage($thumb_size);
    }

    public function deleteImage()
    {
        return $this->forceFill([
            'image' => null,
            'max_thumb_size' => null,
        ])->save();
    }

    /**
     * Execute a query for a single record by ID.
     *
     * @param int $id
     * @param array $columns
     *
     * @return mixed|static
     */
    public static function find($id, $columns = ['*'])
    {
        return static::where('id', $id)->site()->first($columns);
    }

    /**
     * @param $provider
     * @return static
     */
    public static function findByProvider($provider)
    {
        $configurations = static::getConfigurations(null);

        return $configurations->where('provider', $provider)->first();

        //        if ($active === true) {
        //            return static::where('provider', $provider)->active()->site()->first();
        //        }
        //
        //        if ($active === false) {
        //            return static::where('provider', $provider)->inactive()->site()->first();
        //        }
        //
        //        return static::where('provider', $provider)->site()->first();
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public static function remove($id)
    {
        return (optional(static::find($id)))->delete();
    }

    /**
     * @param mixed $id
     * @param mixed $active
     * @return mixed
     */
    public static function activity($id, $active)
    {
        $record = static::findOrFail($id);
        $record->update(['active' => $active]);
        SiteEventLog::add(
            $record->site_id,
            $record->active == YesNo::True ? SiteEventLog::TYPE_PAYMENT_ACTIVATED : SiteEventLog::TYPE_PAYMENT_DEACTIVATED,
            $record
        );

        return $record;
    }

    /**
     * @return bool
     */
    public function supportCustomerDetails()
    {
        if (!$this->exists) {
            return false;
        }

        return PaymentGateway::supports($this->provider, 'customerDetails');
    }

    /**
     * @return bool
     */
    public function renderCustomerDetails()
    {
        return PaymentGateway::customerDetails($this->provider, \Auth::customer());
    }

    /**
     * @param mixed $amount
     * @return mixed
     */
    public function isAllowedByOrderAmount($amount): bool
    {
        if (is_numeric($from = ($this->configuration['amount_from'] ?? null)) && $from > $amount) {
            return false;
        }

        if (is_numeric($to = ($this->configuration['amount_to'] ?? null)) && $to < $amount) {
            return false;
        }

        return true;
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public function allowByProductId($id): bool
    {
        $class = PaymentGatewayManager::INTEGRATIONS_NAMESPACE . \Illuminate\Support\Str::studly($this->provider) . '\\AllowByProductId';
        if (!class_exists($class)) {
            return true;
        }

        /** @var AdditionalAllowByProductId $instance */
        $instance = new $class(is_array($id) ? $id : [$id]);
        return $instance->allowedProvider();
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getConfigurationAttribute($value)
    {
        if ($this->exists && !is_array($this->attributes['configuration'])) {
            $this->attributes['configuration'] = json_decode(AES::decrypt($this->attributes['configuration']), true);
        }
        return !empty($this->attributes['configuration']) ? $this->attributes['configuration'] : [];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getTypeAttribute($value)
    {
        return $this->payment_provider->type;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getMinPriceAttribute($value)
    {
        return $this->configuration['min_price'] ?? null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getStorefrontNameAttribute($value)
    {
        if (empty($this->attributes['storefront_name'])) {
            if ((in_array($this->provider, ['pop']) ? true : !empty($this->attributes['configuration'])) && !empty($this->title)) {
                $this->attributes['storefront_name'] = $this->title;
            } elseif (/*$this->relationLoaded('provider') && */
                $this->provider && 'sf.payment_provider.name_' . $this->provider != __('sf.payment_provider.name_' . $this->provider)
            ) {
                $this->attributes['storefront_name'] = __('sf.payment_provider.name_' . $this->provider);
            } elseif ($this->relationLoaded('payment_provider')) {
                $this->attributes['storefront_name'] = $this->payment_provider->getAttribute('name');
            } else {
                $this->attributes['storefront_name'] = __('sf.payment_provider.name_' . $this->provider);
            }
        }
        return $this->attributes['storefront_name'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getImageUrlAttribute($value)
    {
        return $this->getImage();
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getTermsAttribute($value)
    {
        if (!$this->exists) {
            return;
        }
        return PaymentGateway::resolve($this->provider)->getTerms();
    }
}
