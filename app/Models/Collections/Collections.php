<?php

namespace App\Models\Collections;

use App\Helper\SiteCp\Tools;
use App\Models\Base\AbstractProductsCollections;
use App\LiquidEngine\Helpers\Collections\Filters;
use App\Models\Product\Product;
use App\Traits\Crudling;
use App\Traits\Image;
use App\Traits\PlanUsage;
use App\Traits\UrlHandle;
use Closure;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Linker;
use Cache;

class Collections extends AbstractProductsCollections
{

    use Image, Crudling, PlanUsage, UrlHandle;

    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub

        static::deleting(function (Collections $collection) {
            $collection->products()->detach();
            $collection->rules()->delete();
        });

        static::saving(function (Collections $collection) {
            if (!$collection->manual) {
                Cache::forget('collections.notManual.*');
            }
        });
    }

    /**
     * @return HasMany|CollectionsRules
     */
    public function rules()
    {
        return $this->hasMany(CollectionsRules::class, 'collection_id')
            ->orderBy('id', 'desc');
    }

    /**
     * @return BelongsToMany|Product
     */
    public function products()
    {
        return $this->belongsToMany(
            Product::class,
            'products_to_collections',
            'collection_id'
        );
    }

    /**
     * Selection link generate.
     *
     * @return string
     */
    public function url()
    {
        return Linker::fullLink("/collection/" . $this->url_handle);
    }

    public function scopeActive($query)
    {

    }

    public function scopeWithActiveProductsCount($query)
    {
        /** @var Collections $query */
        $query->withCount(['products' => function ($query) {
            /** @var Product $query */
            $query->listing()->select([])->setEagerLoads([])
                ->selectRaw('COUNT(*)')->setBindings([], 'select');
        }]);
    }

    public function scopeManual($query)
    {
        /** @var Collections $query */
        $query->where('manual', 1);
    }

    public function scopeNotManual($query)
    {
        /** @var Collections $query */
        $query->where('manual', 0);
    }

    /**
     * @return string
     */
    protected function getUrlAttribute()
    {
        return $this->url();
    }

    /**
     * @return Closure|null
     */
    protected function makeFilters()
    {
        if (!$this->exists) {
            return null;
        }

        $make = new Filters();
        $filters = [];
        $types = static::getRulesTypes();
        foreach ($this->rules as $rule) {
            if (!array_key_exists($rule->type, $types) || !is_array($comparisons = $types[$rule->type]) || !in_array($rule->comparison, $comparisons)) {
                continue;
            }

            if ($filter = $make->render($rule->type, $rule->comparison, $rule->value)) {
                $filters[] = $filter;
            }
        }

        if (empty($filters)) {
            return null;
        }

        return function ($query) use ($filters) {
            $method = $this->operation == 'and' ? 'where' : 'orWhere';
            foreach ($filters as $filter) {
                $query->$method($filter);
            }
        };
    }

    /**
     * @return bool
     */
    public function syncProducts()
    {
        if (!$this->exists) {
            return false;
        }

        if ($this->manual) {
            return true;
        }

        if (empty($filter = $this->makeFilters())) {
            return !!$this->products()->detach();
        } else {
            $this->products()->detach();
            $model = Product::withoutGlobalScopes()->where($filter)
                ->select('id')->selectRaw('?', [$this->id]);
            return !!$this->products()->newPivotStatement()->insertUsing(['product_id', 'collection_id'], $model);
        }
    }

    /**
     * @param int|array $ids
     * @param array|null $checkKeys
     * @return bool
     */
    public static function syncProduct($ids, array $checkKeys = null)
    {
        if (!site()->engineGe('2.0.0')) {
            return true;
        }

        if ($ids instanceof Arrayable) {
            $ids = $ids->toArray();
        }
        if (!is_array($ids)) {
            $ids = [$ids];
        }

        CollectionsToProduct::whereIn('product_id', $ids)->whereHas('collection', function ($query) {
            /** @var Collections $query */
            $query->notManual();
        })->delete();

        /** @var Collections[] $collections */
        $collections = Cache::remember('collections.notManual.' . md5(json_encode($checkKeys)), 60, function () use ($checkKeys) {
            return static::active()->notManual()->with('rules')->whereHas('rules', function ($query) use ($checkKeys) {
                /** @var CollectionsRules $query */
                if ($checkKeys) {
                    $query->whereIn('type', $checkKeys);
                }
            })->get();
        });

        if ($collections->isEmpty()) {
            return true;
        }

        $model = null;
        foreach ($collections as $collection) {
            if ($filter = $collection->makeFilters()) {
                if (!empty($model)) {
                    /** @var Product $model */
                    $model->union(
                        Product::withoutGlobalScopes()->whereIn('products.id', $ids)->select('id')->selectRaw('? as collection_id', [$collection->id])->where($filter)
                    );
                } else {
                    $model = Product::withoutGlobalScopes()->whereIn('products.id', $ids)->select('id')->selectRaw('? as collection_id', [$collection->id])->where($filter);
                }
            }
        }

        if (is_null($model)) {
            return false;
        }

        return !!CollectionsToProduct::insertUsing(['product_id', 'collection_id'], $model);
    }

    /**
     * @return $this
     */
    public function formatForSiteCp()
    {
        if (!$this->exists) {
            return $this;
        }

        if ($this->products_count) {
            $this->setAttribute('products_count_formatted', Tools::dataHolder('x fal fa-truck-loading', trans_choice('global.products_choice', $this->products_count),
                false, route('admin.products.list', ['filters[collection]' => $this->id]),
                '_blank'));
        } else {
            $this->setAttribute('products_count_formatted', Tools::dataHolder('x fal fa-truck-loading', trans_choice('global.products_choice', $this->products_count)));
        }

        $this->setAttribute('name_formatted', view('collections.list.name', [
            'collection' => $this
        ])->render());

        $this->setAttribute('url_formatted', view('collections.list.url', [
            'collection' => $this
        ])->render());

        return $this;
    }

    /**
     * @return array
     */
    public static function getRulesTypes()
    {
        return [
            'product_id' => ['equal', 'not_equal'],
            'product_title' => [/*'equal', 'not_equal',*/ 'start_with', 'end_with', 'contains', 'not_contains'],
            'product_type' => ['equal', 'not_equal'], //(simple,multiple,digital,bundle)
            'product_price' => ['equal', 'not_equal', /*'between', 'not_between',*/ 'lower', 'greater'],
            'product_weight' => ['equal', 'not_equal', /*'between', 'not_between',*/ 'lower', 'greater'],
            'product_stock' => ['equal', 'not_equal', /*'between', 'not_between',*/ 'lower', 'greater'],
            'product_new' => ['bool'],
            'product_featured' => ['bool'],
            'product_tag_id' => ['equal'],
            'vendor_id' => ['equal', 'not_equal'],
            'vendor_title' => [/*'equal', 'not_equal',*/ 'start_with', 'end_with', 'contains', 'not_contains'],
            'category_id' => ['equal', 'not_equal'],
//            'category_title' => [/*'equal', 'not_equal',*/ 'start_with', 'end_with', 'contains', 'not_contains'],
            'variant_title' => ['equal', 'not_equal', 'start_with', 'end_with', 'contains', 'not_contains'],
        ];
    }

    /**
     * @return array
     */
    public static function getRulesTypesOperations()
    {
        return collect(static::getRulesTypes())->collapse()->unique()->values()->all();
    }
}
