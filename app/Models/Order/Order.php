<?php

declare(strict_types=1);

namespace App\Models\Order;

use App\Common\DateTimeFormat;
use App\Common\Media;
use App\Common\Status as CommonStatus;
use App\Contracts\ApiModelContract;
use App\Contracts\PreventEventContract;
use App\Contracts\WebHookContract;
use App\Events\HookEvent;
use App\Events\OrderArchiveToggle;
use App\Events\OrderNoteEdit;
use App\Events\OrderPaymentUpdated;
use App\Events\OrderShippingChange;
use App\Events\OrderStatusChange;
use App\Exceptions\Error;
use App\Exceptions\SiteCpAccessDenied;
use App\Helper\ArrayCache;
use App\Helper\Format;
use App\Helper\Mail\QueueNotifyAdmin;
use App\Helper\OmniShip\Address;
use App\Helper\Shipping\QuotesFormat;
use App\Helper\SiteCp\Order\OrderPrint;
use App\Helper\SiteCp\Tools;
use App\Helper\Store\Abstraction\Order\OrderStoreMethods;
use App\Helper\Store\CartTotal;
use App\Helper\Store\Contracts\Concerns\Discount\DiscountContract;
use App\Helper\Store\Contracts\Concerns\Item\Options\ItemOptionContract;
use App\Helper\Store\Contracts\Concerns\TaxContract;
use App\Helper\Store\Contracts\ItemContract;
use App\Helper\Store\Contracts\OrderContract;
use App\Helper\Temp\ProductTemp;
use App\Helper\Text;
use App\Helper\WebHooks\OrderWebHook;
use App\Helper\YesNo;
use App\Integration\Custom\Facades\CustomIntegration;
use App\Integration\Zora\Models\OrderSendLog;
use App\Jobs\Orders\Events\HookEventJob;
use App\Locale\Country;
use App\Locale\Weight;
use App\Models\Base\AbstractOrders;
use App\Models\Customer\Customer;
use App\Models\Customer\Customer as CustomerModel;
use App\Models\Customer\Group;
use App\Models\Gateway\Currency;
use App\Models\Gateway\PaymentProviders;
use App\Models\Gateway\Payments;
use App\Models\Layout\FormFieldOptions;
use App\Models\Mongo\LogShippingQuotes;
use App\Models\Mongo\LogWaybill;
use App\Models\Product\ImageVariant;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Setting\Admin;
use App\Models\Setting\Setting;
use App\Models\Shipping\ShippingProvider;
use App\Models\Shipping\ShippingRate;
use App\Models\Store\AbandonedCart;
use App\Models\Store\CartBundle;
use App\Models\Store\CartItem;
use App\Models\Tax\Tax;
use App\Traits\Api as ApiTrait;
use App\Traits\Crudling;
use App\Traits\DateTemplate;
use App\Traits\OrderRelations;
use App\Traits\PreventEvent;
use Apps;
use Auth;
use Carbon\Carbon;
use DNS1D;
use DNS2D;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection as BaseCollection;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use MaxMind;
use Modules\Apps\Administration\GroceryStore\GroceryStoreManager;
use Modules\Apps\Administration\GroceryStore\Models\Units;
use Modules\Apps\Administration\N18\Audit\AuditManager;
use Modules\Apps\Shippings\ShippingHours\ShippingHoursManager;
use Modules\CustomIntegrations\Facades\CustomIntegrationModule;
use Modules\Marketing\Campaign\Core\Models\Campaign;
use Modules\Marketing\Segments\Core\Models\Subscriber;
use Modules\Marketing\Segments\Core\Traits\SubscriberNotificationsRelations;
use Mpdf\Mpdf;
use Mpdf\MpdfException;
use Omniship\Common\Bill\Create;
use Omniship\Common\ShippingQuote;
use Omniship\Common\ShippingQuoteBag;
use Omniship\Consts;
use ReflectionException;
use SmartyException;
use Throwable;
use Modules\Core\Invoicing\Facades\Invoicing;

/**
 * Class Order
 *
 * @package App\Models\Order
 *
 * @property Collection $payments
 * @property Collection|OrderProduct[] $products
 * @property OrderShipping $shipping
 * @property OrderFulfillment $fulfillment
 * @property OrderFulfillmentReturn $fulfillment_return
 * @property OrderPayment $payment
 * @property Create|null $integration_waybill
 * @property Address|null $integration_address
 * @property ShippingAddress|null $shippingAddress
 * @property BillingAddress|null $billingAddress
 * @property CustomerModel|null $customer
 * @property Collection|OrderMeta[] $meta
 * @property Collection $meta_pluck
 * @property OrderTax[]|Collection $taxes
 * @property OrderTotals[]|Collection $totals
 * @property OrderDiscount[]|Collection $discounts
 * @property string $customer_ip_country
 * @property string $usn
 */
class Order extends AbstractOrders implements PreventEventContract, ApiModelContract, OrderContract, WebHookContract
{
    use PreventEvent;
    use DateTemplate;
    use Crudling;
    use OrderRelations;
    use ApiTrait {
        ApiTrait::api as traitApi;
    }

    //new abstraction for cart & order
    use OrderStoreMethods;

    //campaigns
    use SubscriberNotificationsRelations;

    /**
     * @var
     */
    private static $next_invoice_number;

    private array $_low_stock_products = [];

    private array $_out_of_stock_products = [];

    private int $_max_try_generate_invoice_number = 0;

    private int $_max_try_generate_receipt_number = 0;

    private int $_max_try_generate_id = 0;

    private int $_max_try_generate_hash = 0;

    protected $appends = [
        'meta_pluck', 'omniship_provider', 'date_template',
        'date_added_formatted', 'price_total_formatted', 'sender_address',
        'price_total_input', 'price_subtotal_input', 'has_free_shipping',
        'status_formatted', 'status_fulfillment_formatted',
        'price_subtotal_formatted', 'price_products_subtotal_formatted',
        'price_products_subtotal_input',
        'status_color', 'status_fulfillment_color', 'content', 'content_key',
    ];

    protected $data = [];

    public const API_KEYS = [
        'customer_id', 'customer_group_id', 'customer_first_name',
        'customer_last_name', 'customer_email', 'customer_ip',
        'note_customer', 'note_administrator',
        'price_products_subtotal', 'price_subtotal', 'price_total',
        'quantity', 'weight', 'vat_included',
        'email_sent', /*'date_archived',*/
        'status', 'customer_geoip',
        'status_fulfillment', 'invoice_number',
        'invoice_date', 'id', /*'customer',*/
        'discounts', 'products',
        'shipping', 'payments', 'billing_address', 'shipping_address',
        'taxes', 'date_added', 'abandoned'
    ];

    /**
     *
     */
    #[\Override]
    protected static function boot()
    {
        parent::boot();

        static::creating(function (Order $order): void {
            if (empty($order->customer_geoip)) {
                $order->customer_geoip = [
                    'city' => MaxMind::getCity() ? MaxMind::getCity()->getName() : null,
                    'state' => MaxMind::getSubdivision() ? MaxMind::getSubdivision()->getName() : null,
                    'country' => MaxMind::getCountry() ? MaxMind::getCountry()->getName() : null,
                    'country_iso' => MaxMind::getCountry() ? MaxMind::getCountry()->getIsoCode() : null,
                    'timeZone' => MaxMind::getLocation() ? MaxMind::getLocation()->getTimeZone() : null,
                    'lon' => MaxMind::getLocation() ? MaxMind::getLocation()->getLatitude() : null,
                    'lat' => MaxMind::getLocation() ? MaxMind::getLocation()->getLongitude() : null,
                ];
            }

            if (empty($order->customer_ip)) {
                $order->customer_ip = sprintf("%u", ip2long(request()->ip()));
            }

            if (empty($order->locale)) {
                $order->locale = site('language');
            }

            if (empty($order->currency)) {
                $order->currency = site('currency');
            }

            if (empty($order->unit_system) || !in_array($order->unit_system, ['metric', 'imperial'])) {
                $order->fill([
                    'unit_system' => site('unit_system')
                ]);
            }
        });

        static::created(function (Order $order): void {
            $order->updateIncerementHash();
        });

        static::deleted(function (self $order): void {
            event(new HookEvent(HookEvent::OrderDeleted, $order));
        });

        static::saving(function (Order $order): void {
            if (empty($order->invoice_number)) {
                $order->invoice_number = null;
            }

            if ($order->status == 'paid' && $order->status_fulfillment == 'fulfilled' && \setting('order_complete', 1)) {
                $order->status = 'completed';
            }
        });
    }

    /**
     * @return HasMany
     */
    public function modifications(): HasMany
    {
        return $this->hasMany(OrdersModification::class, 'order_id', 'id')
            ->whereNull('order_product_id')->where('group', 'cart');
    }

    /**
     * @return HasOne
     */
    public function lockFrom()
    {
        return $this->hasOne(Admin::class, 'id', 'moderator_id');
    }

    /**
     * @return HasMany|OrderBundle
     */
    public function bundles()
    {
        return $this->hasMany(OrderBundle::class, 'order_id', 'id');
    }

    /**
     * @return HasMany|OrderMeta
     */
    public function meta()
    {
        return $this->hasMany(OrderMeta::class);
    }

    /**
     * @return HasOne|OrderMeta
     */
    public function meta_single()
    {
        return $this->hasOne(OrderMeta::class);
    }

    /**
     * @return HasMany
     */
    public function totals(): HasMany
    {
        return $this->hasMany(OrderTotals::class)
            ->orderBy('sort_order', 'asc')
            ->orderBy('id', 'asc');
    }

    /**
     * @return HasMany
     */
    public function payments()
    {
        return $this->hasMany(OrderPayment::class)
            ->orderBy('id', 'asc');
    }

    /**
     * @return HasOne
     */
    public function payment()
    {
        /** @var $payment HasOne|OrderPayment */
        $payment = $this->hasOne(OrderPayment::class);
        return $payment->orderBy('id', 'desc');
    }

    /**
     * @return HasMany|OrderTax
     */
    public function taxes()
    {
        return $this->hasMany(OrderTax::class)->orderBy('tax_vat', 'desc');
    }

    /**
     * @return HasMany|OrderDiscount
     */
    public function discounts()
    {
        return $this->hasMany(OrderDiscount::class);
    }

    /**
     * @return HasMany|OrderFulfillment
     */
    public function fulfillments()
    {
        return $this->hasMany(OrderFulfillment::class);
    }

    /**
     * @return HasOne|OrderFulfillment
     */
    public function fulfillment()
    {
        return $this->hasOne(OrderFulfillment::class)
            ->orderBy('id', 'desc');
    }

    /**
     * @return HasMany|OrderStatusHistory
     */
    public function status_history()
    {
        return $this->hasMany(OrderStatusHistory::class);
    }

    /**
     * @return HasMany|OrderHistory
     */
    public function history()
    {
        return $this->hasMany(OrderHistory::class);
    }

    /**
     * @return HasOne|OrderHistory
     */
    public function last_history()
    {
        return $this->hasOne(OrderHistory::class)
            ->latest('id');
    }

    /**
     * @return HasMany|OrderProduct
     */
    public function orderProducts()
    {
        return $this->products();
    }

    /**
     * @return HasOne|OrderShipping
     */
    public function shipping()
    {
        return $this->hasOne(OrderShipping::class);
    }

    /**
     * @return HasOne|CustomerModel
     */
    public function customer()
    {
        return $this->hasOne(CustomerModel::class, 'id', 'customer_id');
    }

    public function customer_group()
    {
        return $this->belongsTo(Group::class, 'customer_group_id');
    }

    /**
     * @return HasOne|Subscriber
     */
    public function subscriber()
    {
        return $this->hasOne(Subscriber::class, 'id', 'subscriber_id');
    }

    /**
     * @return HasOne|ShippingAddress
     */
    public function shippingAddress()
    {
        return $this->hasOne(ShippingAddress::class, 'order_id', 'id');
    }

    /**
     * @return HasOne|BillingAddress
     */
    public function billingAddress()
    {
        return $this->hasOne(BillingAddress::class, 'order_id', 'id');
    }

    /**
     * @return BelongsTo|CustomerModel
     */
    public function client()
    {
        return $this->belongsTo(CustomerModel::class, 'customer_id');
    }

    /**
     * @return HasMany|Order
     */
    public function customer_orders()
    {
        return $this->hasMany(Order::class, 'customer_id', 'customer_id');
    }

    /**
     * @return HasMany|OrderProduct
     */
    public function products()
    {
        return $this->hasMany(OrderProduct::class, 'order_id', 'id');
    }

    /**
     * @return HasMany|OrderCrossSell
     */
    public function crossSell()
    {
        return $this->hasMany(OrderCrossSell::class, 'order_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function campaign()
    {
        return $this->hasManyThrough(Campaign::class, OrderMeta::class, 'order_id', 'id', 'id', 'value')
            ->where('parameter', 'cc_campaign_id');
    }

    /**
     * @return BaseCollection|OrderProduct[]
     */
    public function shippable_products()
    {
        return $this->products->where('digital', YesNo::False);
    }

    /**
     * @return array
     */
    public static function statusCache(): array
    {
        $statuses = ArrayCache::remember('statuses.all', fn () => OrderStatus::get());
        $result = [];

        foreach ($statuses as $status) {
            $result[$status->type][$status->status] = $status->name;
        }

        return $result;
    }

    /**
     * @param      $status
     * @param bool $manual
     * @param array $data
     *
     * @return Order
     * @throws Error
     */
    public function changeStatus($status, $manual = false, $data = [])
    {
        if (in_array($status, OrderFulfillment::$statuses_fulfillment)) {
            if ($status == 'not_fulfilled') {
                return OrderFulfillment::removeByOrderId($this->id);
            } else {
                return OrderFulfillment::add(array_merge($data, [
                    'order' => $this,
                    'order_id' => $this->id,
                    'products_ids' => $this->products->where('digital', 'no')->pluck('id')->all()
                ]))->order;
            }
        }

        $previous = $this->status;

        if ($previous == $status) {
            return $this;
        }

        if ($status == OrderStatus::PAID && $this->status_fulfillment != 'fulfilled' && static::isDigital($this->id)) {
            OrderFulfillment::add(array_merge($data, [
                'order' => $this,
                'order_id' => $this->id,
                'products_ids' => $this->products->pluck('id')->all()
            ]), false);
        }

        $this->status = $status;
        if (in_array($status, OrderStatus::$statuses)) {
            $this->manual = (int)$manual;
        }

        if (!in_array($status, [OrderStatus::CANCELLED, OrderStatus::REFUNDED]) && !is_null($this->credit_number)) {
            $this->credit_number = null;
            $this->credit_date = null;
        }

        if ($this->meta_pluck->has('is_draft')) {
            $this->meta()->where('parameter', 'is_draft')->delete();
        }

        $this->save();
        //comment to allow sending hook for custom statuses
        //if (in_array($status, OrderStatus::$statuses)) {
        event(new OrderStatusChange($this, $previous));
        //}

        return $this;
    }

    /**
     * @param $status
     * @throws Error
     */
    public function validateChangeStatus($status): void
    {
        if ($status == 'completed' && $this->status != 'paid' && $this->status_fulfillment != 'fulfilled') {
            throw new Error(__('order.err.only_paid_and_fulfilled_orders_can_be_completed'));
            //        } elseif ($status == 'cancelled' && $this->status != 'pending') {
        } elseif ($status == 'cancelled' && (in_array($this->status, ['paid', 'completed']) /*|| $this->status_fulfillment == 'fulfilled'*/)) {
            throw new Error(__('order.err.only_pending_orders_can_be_canceled'));
        } elseif ($status == 'abandoned' && $this->status != 'pending') {
            throw new Error(__('order.err.only_pending_orders_can_be_abandoned'));
        }

        if ($this->isArchived()) {
            throw new Error(__('order.err.cannot_change_status_of_archived_order_unarchive_first'));
        }
    }

    /**
     * @param $status
     * @throws Error
     */
    public function validateChangeStatusSelect($status): void
    {
        if (in_array($status, OrderFulfillment::$statuses_fulfillment) && ($shipping_manager = $this->getShippingManager()) && $shipping_manager->isExternal()) {
            throw new Error(__('order.err.for_change_fulfillment_status_use_button'));
        }
    }

    /**
     * @param string|array $status
     * @param string|null $status_fulfillment
     *
     * @return mixed
     */
    public static function getCountByStatus($status, $status_fulfillment = null)
    {
        $query = static::whereIn('status', (array)$status);
        if ($status_fulfillment) {
            $query->where('status_fulfillment', $status_fulfillment);
        }

        return $query->count();
    }

    /**
     * @param string|array $status
     * @param string|null $status_fulfillment
     *
     * @return mixed
     */
    public static function existsByStatus($status, $status_fulfillment = null)
    {
        $query = static::whereIn('status', (array)$status);
        if ($status_fulfillment) {
            $query->where('status_fulfillment', $status_fulfillment);
        }

        return $query->exists();
    }

    /**
     * @return int
     */
    public static function abandonedCount()
    {
        return AbandonedCart::whereAbandoned()->count();
    }

    /**
     * @param $query
     * @param bool $completed Whether to return invoices only for completed orders (paid or completed).
     *
     * @return \Illuminate\Database\Query\Builder
     */
    public function scopeInvoiced($query, $completed = false)
    {
        /** @var \Illuminate\Database\Query\Builder $query */
        $query->whereNotNull('invoice_number');

        if ($completed) {
            $query->whereIn('status', ['paid', 'completed']);
        }

        return $query;
    }

    /**
     * @param $query
     * @param $bol_id
     */
    public function scopeWhereBolId($query, $bol_id): void
    {
        /** @var Order $query */
        $query->whereHas('meta', function ($query) use ($bol_id): void {
            /** @var OrderMeta $query */
            $query->where('parameter', 'bol_id')
                ->where('value', $bol_id);
        });
    }

    /**
     * @param $query
     * @throws Exception
     */
    public function scopeWhereNotDigitalOrder($query): void
    {
        $query->where(function ($query): void {
            $allProducts = OrderProduct::whereColumn('order_id', 'orders.id')
                ->selectRaw('COUNT(*)');
            $digitalProducts = clone $allProducts;
            $query->whereRaw(sprintf('(%s) <> (%s)', $allProducts->toSql(), $digitalProducts->where('digital', YesNo::True)->toFullSql()));
            $query->whereDoesntHave('meta', function ($query): void {
                $query->where('parameter', 'fast_order');
            });
        });
    }

    /**
     * @param $query
     * @throws Exception
     */
    public function scopeWhereDigitalOrder($query): void
    {
        $query->where(function ($query): void {
            $allProducts = OrderProduct::whereColumn('order_id', 'orders.id')
                ->selectRaw('COUNT(*)');
            $digitalProducts = clone $allProducts;
            $query->whereRaw(sprintf('(%s) = (%s)', $allProducts->toSql(), $digitalProducts->where('digital', YesNo::True)->toFullSql()));
            $query->whereNotFastOrder();
        });
    }

    /**
     * @param $query
     */
    public function scopeWhereFastOrder($query): void
    {
        $query->where(function ($query): void {
            $query->whereHas('meta', function ($query): void {
                $query->where('parameter', 'fast_order');
            });
        });
    }

    /**
     * @param $query
     */
    public function scopeWhereNotFastOrder($query): void
    {
        $query->where(function ($query): void {
            $query->whereDoesntHave('meta', function ($query): void {
                $query->where('parameter', 'fast_order');
            });
        });
    }

    /**
     * @param Builder $query
     * @param mixed $name
     *
     * @return mixed
     */
    public function scopeGeoZoneName($query, $name)
    {
        return $query->whereHas('shipping', function (Builder $q) use ($name): void {
            $q->whereHas('provider', function (Builder $q) use ($name): void {
                $q->whereHas('geo_zone', function (Builder $q) use ($name): void {
                    $q->where('name', $name);
                });
            });
        });
    }

    /**
     * @param Builder $query
     * @param mixed $id
     *
     * @return mixed
     */
    public function scopeGeoZoneId($query, $id)
    {
        return $query->whereHas('shipping', function (Builder $q) use ($id): void {
            $q->whereHas('provider', function (Builder $q) use ($id): void {
                $q->where('geo_zone_id', $id);
            });
        });
    }

    /**
     * @param $query
     */
    public function scopeForUpdate($query): void
    {
        /** @var Order $query */
        $query->with([
            'modifications.modification',
            'products' => function ($query): void {
                /** @var OrderProduct $query */
                $query->with([
                    'bundle.bundle',
                    'smart_collection_ids',
                    'modifications.modification',
                    'discounts' => function ($query): void {
                        $query->with(['discount.meta_data']);
                    }, 'tax', 'options',
                    'crossSell.cross_sell', 'upSell.up_sell', 'meta_data']);
            },
            'discounts' => function ($query): void {
                $query->with(['targets', 'codes_pro']);
            },
            'shipping.provider',
        ]);
    }

    /**
     * @param $query
     */
    public function scopeForDetails($query): void
    {
        /** @var Order $query */
        $query->with([
            'modifications.modification',
            'products' => function ($query): void {
                /** @var OrderProduct $query */
                $query->with([
                    'bundle.bundle',
                    'smart_collection_ids',
                    'modifications.modification',
                    'discounts' => function ($query): void {
                        $query->with(['discount.meta_data']);
                    }, 'tax', 'options',
                    'crossSell.cross_sell', 'upSell.up_sell', 'meta_data']);

                $query->with([
                    'product' => function($query) {
                        $query->withoutGlobalScopes();

                        $query->with([
                            'category.path', 'image',
                        ]);
                    },
                    'variant' => function ($query): void {
                        /** @var Variant $query */
                        $query->withoutGlobalScopes();
                        $query->with('image.image');
                    }
                ]);
            },
            'discounts' => function ($query): void {
                $query->with(['targets', 'codes_pro']);
            },
            'shipping.provider',
        ]);
    }

    /**
     * @param $query
     */
    public function scopeAllOrderData($query): void
    {
        /** @var Order $query */
        $query->with([
            'modifications.modification',
            'products' => function ($query): void {
                /** @var OrderProduct $query */
                $query->with([
                    'bundle.bundle',
                    'smart_collection_ids',
                    'modifications.modification',
                    'product' => function ($query): void {
                        $query->withoutGlobalScopes();
                        /** @var Product $query */
                        $query->with([
                            'variant' => function ($query): void {
                                /** @var Variant $query */
                                $query->with('image.image');
                            }, 'category', 'vendor', 'image',
                            'tag_ids', 'smart_collection_ids',
                        ]);
                    }, 'discounting', 'discounts' => function ($query): void {
                        $query->with(['discount.meta_data', 'meta_data', 'meta_data_single']);
                    }, 'tax', 'options' => function ($query): void {
                        /** @var Builder $query */
                        $query->with('field.options');
                    }, 'variant' => function ($query): void {
                        /** @var Variant $query */
                        $query->withoutGlobalScopes();
                        $query->with('image.image');
                    }, 'category' => function ($query): void {
                        $query->withoutGlobalScopes();
                    }, 'vendor' => function ($query): void {
                        $query->withoutGlobalScopes();
                    }, 'crossSell.cross_sell', 'upSell.up_sell', 'meta_data']);
            },
            'totals', 'shippingAddress', 'billingAddress', 'taxes', 'meta',
            'discounts' => function ($query): void {
                $query->with(['targets', 'codes_pro', 'meta_data', 'meta_data_single', 'discount.meta_data']);
            },
            'customer' => function ($query): void {
                /** @var Customer $query */
                $query->with('group')
                    ->with(['custom_data' => function ($query): void {
                        $query->with('field.options')
                            ->has('field');
                    }]);
            },
            'shipping' => function ($query): void {
                /** @var OrderShipping $query */
                $query->with('provider');
            },
            'payments.payment_provider.configuration',
            'payment.payment_provider.configuration',
        ]);
    }

    /**
     * @param $query
     */
    public function scopeForReturnPage($query): void
    {
        /** @var Order $query */
        $query->with([
            'modifications.modification',
            'products' => function ($query): void {
                /** @var OrderProduct $query */
                $query->with([
                    'bundle.bundle', 'files',
                    'smart_collection_ids',
                    'modifications.modification',
                    'product' => function ($query): void {
                        $query->withoutGlobalScopes();
                        /** @var Product $query */
                        $query->with([
                            'variant' => function ($query): void {
                                /** @var Variant $query */
                                $query->with('image.image');
                            }, 'category', 'vendor', 'image',
                            'tag_ids', 'smart_collection_ids',
                        ]);
                    }, 'discounting', 'discounts' => function ($query): void {
                        $query->with(['discount.meta_data', 'meta_data', 'meta_data_single']);
                    }, 'tax', 'options' => function ($query): void {
                        /** @var Builder $query */
                        $query->with('field.options');
                    }, 'variant' => function ($query): void {
                        /** @var Variant $query */
                        $query->withoutGlobalScopes();
                        $query->with('image.image');
                    }, 'category' => function ($query): void {
                        $query->withoutGlobalScopes();
                    }, 'vendor' => function ($query): void {
                        $query->withoutGlobalScopes();
                    }, 'crossSell.cross_sell', 'upSell.up_sell', 'meta_data']);
            },
            'totals', 'payment', 'payments',
            'shippingAddress', 'billingAddress',
            'taxes', 'meta',
            'discounts' => function ($query): void {
                $query->with(['targets', 'codes_pro', 'meta_data', 'meta_data_single', 'discount.meta_data']);
            },
            'customer' => function ($query): void {
                /** @var Customer $query */
                $query->with('group')
                    ->with(['custom_data' => function ($query): void {
                        $query->with('field.options')
                            ->has('field');
                    }]);
            },
            'shipping' => function ($query): void {
                /** @var OrderShipping $query */
                $query->with('provider');
            },
//            'payments.payment_provider.configuration',
//            'payment.payment_provider.configuration',
            'meta'
        ]);
    }

    /**
     * @param $order_id
     * @param null $fields
     * @param bool $where_clause
     * @param bool $include_image
     * @param bool $include_tracking
     * @return BaseCollection|OrderProduct[]
     */
    public static function productGetByOrderId(
        $order_id,
        /** @noinspection PhpUnusedParameterInspection */
        $fields = null,
        $where_clause = false,
        $include_image = true,
        $include_tracking = false
    ) {
        $product_model = OrderProduct::with(['product', 'options']);
        $table = $product_model->getModel()->getTable();

        $where = sprintf("`order_id` = '%s'", $order_id);

        if (!empty($where_clause)) {
            $where .= ' AND ' . $where_clause;
        }

        $join = 'LEFT OUTER JOIN `products` ON `' . $table . '`.`product_id` = `products`.`id`';
        $addon = '`products`.`url_handle`';

        if ($include_image) {
            $helper = new ImageVariant();

            $join .= ' LEFT OUTER JOIN `' . $helper->getTable() . '` ON `' . $table . '`.`variant_id` = `' . $helper->getTable() . '`.`parent_id`';
            $addon .= ', `products`.`image_id`, `' . $helper->getTable() . '`.`product_image_id` as variant_image_id';

            $product_model = $product_model->groupBy('orders_products.id');
        }

        if ($include_tracking) {
            if (!$include_image) {
                $addon .= ', `products`.`tracking`, `products`.`continue_selling`';
            } else {
                $helper = new ImageVariant();
                $addon .= ',
                    `products`.`image_id`,
                    `' . $helper->getTable() . '`.`product_image_id` as variant_image_id,
                    `products`.`tracking`,
                    `products`.`continue_selling`';
            }
        }

        if ($join) {
            $product_model = $product_model->joinRaw($join);
        }

        if ($addon) {
            $product_model = $product_model->select([$table . '.*', \Illuminate\Support\Facades\DB::raw($addon)]);
        }

        return $product_model
            ->whereRaw($where)
            ->get()->keyBy('id');

    }

    public function getShippingProviderWithQuotes()
    {
        if ($this->shipping->overload ?? false) {
            return $this->shipping->provider;
        }

        if (
            !($shipping = $this->shipping) ||
            !($provider = $shipping->provider) ||
            $this->meta_pluck->isEmpty() ||
            !is_array($quotes = json_decode((string) $this->meta_pluck->get('quotes'), true)) ||
            !($service_id = $this->meta_pluck->get('service_id', $shipping->service_id))
        ) {
            return;
        }

        if (!empty($quotes[$service_id])) {
            if ($this->isRecalculateLocked()) {
                $quotes[$service_id]['price'] = moneyInput($shipping->order_amount_with_vat);
                $quotes[$service_id]['price_formatted'] = money($shipping->order_amount_with_vat);
            } elseif (
                $provider->manager &&
                $provider->manager->getPricingType() == 'calculator_fixed' &&
                ($tax = $provider->manager->getFixedPrice()) > 0
            ) {
                $quotes[$service_id]['price'] = moneyInput($shipping->order_amount_with_vat - (Format::toIntegerPrice($tax)));
                $quotes[$service_id]['price_formatted'] = money($shipping->order_amount_with_vat - (Format::toIntegerPrice($tax)));
            } else {
                $quotes[$service_id]['price'] = moneyInput($shipping->order_amount_with_vat);
                $quotes[$service_id]['price_formatted'] = money($shipping->order_amount_with_vat);
            }
        }

        /** @var ShippingProvider $provider */
        $provider->setAttribute('quotes', $quotes)
            ->setAttribute('service_id', $service_id)
            ->setAttribute('service_name', $this->meta_pluck->get('service_name', $shipping->service_name))
            ->setAttribute('price', $shipping->order_amount)
            ->setAttribute('insurance', $shipping->order_has_insurance ? $shipping->order_insurance : 0);

        if ($provider->manager) {
            $provider->manager->setOrder($this);
        }

        return $provider->syncOriginal();
    }

    public function isRecalculateLocked(): bool
    {
        return $this->meta_pluck->get('recalculate_locked', ($this->payment->status ?? null) == 'completed' ? 1 : 0) == 1;
    }

    /**
     * @param      $order_id
     * @param null $output
     *
     * @return string|void
     * @throws Error
     * @throws MpdfException
     * @throws SmartyException
     * @throws Throwable
     */
    public static function invoiceGenerate($order_id, $output = null)
    {
        if (setting('invoicing', 'yes') == YesNo::False) {
            return false;
        }

        /** @var null|Order $order */
        $order = null;
        if ($order_id instanceof Order) {
            //            $order = $order_id;
            $order = static::allOrderData()->find($order_id->id);
        } elseif (is_numeric($order_id)) {
            $order = static::allOrderData()->find($order_id);
        }

        if (!$order) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        //https://cloudcartad.atlassian.net/browse/IM-355
        if (!$order->invoice_number) {
            return false;
        }

        //disable send invoice without number for zora.bg
        if (site('site_id') == 402 && !$order->invoice_number) {
            return false;
        }

        if (!$order->allow_invoicing) {
            return false;
        }

        $locale = $order->locale ?: site('language', site('language_cp'));
        app()->setLocale($locale);

        //end load data for email and invoice
        $mpdf = new Mpdf(['utf8', 'A4', '', '', 20, 20, 40, 20, 10, 10]);

        $mpdf->SetProtection(['modify', 'print', 'copy']);

        $mpdf->showWatermarkText = true;
        $mpdf->watermark_font = 'Arial';
        $mpdf->watermarkTextAlpha = 0.1;
        $mpdf->allow_charset_conversion = false;
        $mpdf->SetDisplayMode('fullpage');

        if ($order->invoice_number) {
            $mpdf->SetTitle(__('sf.order.invoice.title') . ' ' . $order->invoice_number);
        } else {
            $mpdf->SetTitle(__('sf.order.invoice.title'));
        }

        $mpdf->SetAuthor(__('sf.order.invoice.author'));

        if (empty(setting('invoice_body', ''))) {
            $tpl = View::make('sitecp::orders.invoice', [
                'invoice' => $order->getInvoice(),
            ]);
        } else {
            $tpl = OrderPrint::bodyForInvoice($order);
        }

        if ($locale == 'bg') {
            $settingWatermark = setting('invoice_watermark', '');
            if (!in_array($order->status, ['paid', 'completed', 'pending']) && in_array($order->status, OrderStatus::$statuses)) {
                //            $mpdf->SetWatermarkText($order->status_formatted);
                $mpdf->SetWatermarkText($settingWatermark ?: __('sf.order.status_voided'));
            } else {
                $mpdf->SetWatermarkText($settingWatermark ?: __('order.invoice.type.original'));
            }
        }

        $mpdf->WriteHTML($tpl);
        if (Setting::showPoweredBy()) {
            $mpdf->SetHTMLFooter('<span style="font-size: 12px; color: #666;">' . __('core.invoice_footer') . '</span>');
        }

        if (!empty($output)) {
            $order_date = $order->date_added->format("Y-m-d");
            return $mpdf->Output(sprintf('order_%s_invoice_%s.pdf', $order_id, $order_date), $output);
        }

        return $mpdf->Output('receipt.pdf', 'S');
    }

    /**
     * @return bool|\Carbon\Carbon|BaseCollection|int|mixed|static
     * @throws Throwable
     */
    public function generateInvoiceNumber()
    {
        if (!$this->allow_invoicing) {
            return false;
        }

        if ($this->invoice_number) {
            return $this->invoice_number;
        }

        if (setting('invoice_generate', 1) == 2) {
            return false;
        }

        if ($this->isReadyForInvoice()) {
            $result = $this->_generateInvoiceNumber();
            return $result ? $this->invoice_number : false;
        }

        return false;
    }

    /**
     * @param $number
     * @param null|mixed $date
     * @return false|int|mixed
     * @throws Throwable
     */
    public function generateInvoiceNumberManual($number = null, $date = null)
    {
        if (!$this->allow_invoicing) {
            return false;
        }

        if ($this->invoice_number) {
            return $this->invoice_number;
        }

        if ($this->isReadyForInvoice()) {
            if ($number) {
                $this->invoice_number = $number;
                $this->invoice_date = !empty($date) ? $date : Carbon::now('UTC');
                $this->update([]);
            } else {
                $number = $this->_generateInvoiceNumber();
            }

            return $number ? $this->invoice_number : false;
        }

        return false;
    }

    /**
     * @return bool
     */
    public function isReadyForInvoice(): bool
    {
        //        return $this->status_fulfillment == 'fulfilled' || in_array($this->status, ['completed', 'paid']);
        //        return $this->status_fulfillment == 'fulfilled' || in_array($this->status, ['completed']) || $this->isDigital($this->id);
        //zora not paid
        if (in_array(site('site_id'), [402, 8766])) {
            return $this->status_fulfillment == 'fulfilled' || in_array($this->status, ['completed']) || static::isDigital($this->id);
        } else {
            return $this->status_fulfillment == 'fulfilled' || in_array($this->status, ['completed', 'paid']) || static::isDigital($this->id);
        }
    }

    /**
     * @return bool
     * @throws Throwable
     */
    private function _generateInvoiceNumber(): bool
    {
        return retry(5, function () {
            $this->invoice_number = $this->incrementInvoiceNumber();
            $this->invoice_date = Carbon::now('UTC');
            if (CustomIntegration::supportGenerateInvoiceNumber()) {
                CustomIntegration::generateInvoiceNumber($this);
            }

            if (CustomIntegrationModule::supportGenerateInvoiceNumber()) {
                CustomIntegrationModule::generateInvoiceNumber($this);
            }

            return $this->update([]);
        }, 1000);
        //        try {
        //            $this->_max_try_generate_invoice_number++;
        //            $this->invoice_number = $this->incrementInvoiceNumber();
        //            $this->invoice_date = Carbon::now('UTC');
        //            if (CustomIntegration::supportGenerateInvoiceNumber()) {
        //                CustomIntegration::generateInvoiceNumber($this);
        //            }
        //            if (CustomIntegrationModule::supportGenerateInvoiceNumber()) {
        //                CustomIntegrationModule::generateInvoiceNumber($this);
        //            }
        //
        //            return $this->update([]);
        //        } catch (Throwable $e) {
        //            if (preg_match('~Duplicate\s*entry(.*)orders_invoice_number_unique~', $e->getMessage(), $m) && $this->_max_try_generate_invoice_number <= 3) {
        //                usleep(100000);
        //                return $this->_generateInvoiceNumber();
        //            }
        //
        //            throw $e;
        //        }
    }

    /**
     * @param int|null $max
     * @return int
     * @throws Throwable
     */
    public function updateIncerementHash(?int $max = null)
    {
        if (is_null($max) || $max < 1) {
            $max = 3;
        }

        try {
            $this->_max_try_generate_hash++;

            $originalHash = (function () {
                $originalHash = Str::random(7);
                while (true) {
                    if (
                        is_numeric($originalHash) ||
                        is_numeric(substr($originalHash, 0, 1)) ||
                        static::where('increment_hash', strval($originalHash))->where('id', '<>', $this->id)->exists()
                    ) {
                        $originalHash = Str::random(7);
                        continue;
                    }

                    break;
                }

                return $originalHash;
            })();

            return $this->update([
                'increment_hash' => $originalHash
            ]);
        } catch (Throwable $throwable) {
            if (preg_match('~Duplicate\s*entry(.*)orders_increment_hash_unique~', $throwable->getMessage(), $m) && $this->_max_try_generate_hash <= $max) {
                usleep(100000);
                return $this->updateIncerementHash();
            }

            throw $throwable;
        }
    }

    /**
     * @return string
     */
    public function getOrderStatusDropdown()
    {
        if (!$this->exists) {
            return OrderStatus::$status_default;
        }

        if ($this->status_fulfillment == 'fulfilled') {
            if (!in_array($this->status, ['completed', 'cancelled'])) {
                return $this->status_fulfillment;
            }
        }

        if ($this->meta_pluck->get('is_draft')) {
            return 'draft';
        }

        return $this->status;
    }

    /**
     * @return Carbon|BaseCollection|int|mixed|static
     */
    public function incrementInvoiceNumber(): int
    {
        $invoice_number = (int)static::whereNotNull('invoice_number')
            ->where('id', '<>', $this->id)
            ->max('invoice_number');

        $invoice_number++;
        return $invoice_number;
    }


    /**
     * @return Carbon|BaseCollection|int|mixed|static
     */
    public function incrementCreditNumber(): int
    {
        $invoice_number = (int)static::whereNotNull('credit_number')
            ->where('id', '<>', $this->id)
            ->max('credit_number');

        $invoice_number++;
        return $invoice_number;
    }

    /**
     * @param      $id
     * @param null $fields
     * @param bool $for_update
     *
     * @return \Illuminate\Database\Eloquent\Model|mixed
     */
    public static function paymentGet($id, $fields = null, $for_update = false)
    {
        return OrderPayment::find($id);
    }

    /**
     * @param      $order_id
     * @param null $note
     */
    public static function editNoteCustomer($order_id, $note = null): void
    {
        $model = static::find($order_id);
        if ($model) {
            $model->update(['note_customer' => !empty($note) ? $note : null]);
        }
    }

    /**
     * @param $note
     * @return Order
     * @throws Error
     */
    public function editNoteAdministrator($note): static
    {
        if (Text::length($note) > 16777210) {
            throw new Error(
                sprintf(__('order.err.note_administrator_max_chars_%1$s'), 16777210),
                'note_administrator'
            );
        }

        if ($this->isArchived()) {
            throw new Error(__('order.err.cannot_perform_this_operation_on_archived_order'));
        }

        if ($this->note_administrator == $note) {
            throw new Error(__('order.err.nothing_changed'));
        }

        $before = ['note_administrator' => $this->note_administrator];

        $this->note_administrator = $note ?? null;
        $this->update([]);

        $after = ['note_administrator' => $this->note_administrator];

        event(new OrderNoteEdit([
            'order_id' => $this->id,
            'before' => $before,
            'after' => $after
        ]));

        return $this;
    }

    /**
     * @param $order_id
     *
     * @return bool
     */
    public static function isDigital($order_id): bool
    {
        $total = OrderProduct::where('order_id', $order_id)
            ->where('digital', 'no')->count();

        return $total === 0;
    }

    /**
     * @return OrderPayment|null
     */
    public function getLastPendingPayment()
    {
        return $this->payments->whereIn('status', [Payments::STATUS_REQUESTED, Payments::STATUS_INITIATED])->last();
    }

    /**
     * @param $order_id
     * @param $product_id
     * @param null $fields
     * @param bool $where_clause
     * @param bool $include_image
     * @param bool $include_tracking
     * @return BaseCollection
     */
    public static function productGetByOrderAndProductId(
        $order_id,
        $product_id,
        $fields = null,
        $where_clause = false,
        $include_image = true,
        $include_tracking = false
    ) {
        $order_id = (int)$order_id;
        $product_id = (int)$product_id;

        $product_model = new OrderProduct();
        $table = $product_model->getTable();

        $where = sprintf("`order_id` = '%d' AND `product_id` = '%d'", $order_id, $product_id);

        if (!empty($where_clause)) {
            $where .= ' AND ' . $where_clause;
        }

        $join = 'LEFT OUTER JOIN `products` ON `' . $table . '`.`product_id` = `products`.`id`';
        $addon = '`products`.`url_handle`';

        if ($include_image) {
            $helper = new ImageVariant();

            $join .= ' LEFT OUTER JOIN `' . $helper->getTable() . '` ON `' . $table . '`.`variant_id` = `' . $helper->getTable() . '`.`parent_id`';
            $addon .= ', `products`.`image_id`, `' . $helper->getTable() . '`.`product_image_id` as variant_image_id';

            $product_model = $product_model->groupBy('orders_products.id');
        }

        if ($include_tracking) {
            if (!$include_image) {
                $addon .= ', `products`.`tracking`, `products`.`continue_selling`';
            } else {
                $addon .= ',
			`products`.`image_id`,
			`' . $helper->getTable() . '`.`product_image_id` as variant_image_id,
			`products`.`tracking`,
			`products`.`continue_selling`';
            }
        }

        if ($join) {
            $product_model = $product_model->joinRaw($join);
        }

        if ($addon) {
            $product_model = $product_model->select($table . '.*', \Illuminate\Support\Facades\DB::raw($addon));
        }

        return $product_model
            ->whereRaw($where)
            ->get()->keyBy('id');

    }

    /**
     * @param $filter
     *
     * @return int|null
     */
    public static function getCustomersCount($filter)
    {
        $model = static::selectRaw('COUNT(DISTINCT customer_id) AS total');
        if ($filter) {
            if (!empty($where = $filter->getWheres())) {
                if (is_array($where)) {
                    foreach ($where as $key => $value) {
                        if (is_callable($value)) {
                            $model->where($value);
                        } else {
                            $model->where($key, $value);
                        }
                    }
                } else {
                    $model->whereRaw($where);
                }
            }

            if (!empty($join = $filter->getJoins())) {
                if (is_array($join)) {
                    foreach ((array)$join as $key => $value) {
                        if (is_callable($value)) {
                            $model->join($key, $value);
                        } else {
                            $model->joinRaw($value);
                        }
                    }
                } else {
                    $model->joinRaw($join);
                }
            }
        }

        return $model->value('total');
    }

    /**
     * @throws Error
     */
    public function archive(): void
    {
        if ($this->isArchived()) {
            return;
        }

        if (!$this->meta_pluck->get('is_draft')) {
            if (!in_array($this->status, ['completed', 'cancelled'])) {
                throw new Error(__('order.err.only_completed_orders_can_be_archived'));
            }
        }

        $this->date_archived = Carbon::now();
        $this->update([]);

        event(new OrderArchiveToggle($this));
    }

    /**
     * @throws Error
     */
    public function unArchive(): void
    {
        if (!$this->isArchived()) {
            return;
        }

        $this->date_archived = null;
        $this->update([]);

        event(new OrderArchiveToggle($this));
    }

    /**
     * @return bool
     * @throws Error
     */
    public function isArchived(): bool
    {
        if ($this->date_archived) {
            return true;
        }

        return false;
    }

    /**
     * @param $customer_id
     *
     * @param array $statuses
     * @return array
     */
    public static function getCustomerTotals($customer_id, ?array $statuses = null): array
    {
        $statuses = is_null($statuses) ? OrderStatus::$statuses : $statuses;

        $orders = static::selectRaw("COUNT(`id`) as `cnt`, COALESCE(SUM(`price_total`),0) as `price_total`, COALESCE(SUM(`quantity`),0) as `quantity`, status")
            ->where('customer_id', $customer_id)->whereIn('status', $statuses)
            ->groupBy('status')->get(['cnt', 'price_total', 'quantity', 'status'])
            ->keyBy('status');

        $totals = [];
        foreach ($statuses as $status) {
            if ($orders->has($status)) {
                $totals[$status] = $orders->get($status)
                    ->setAppends(['price_total_formatted', 'status_formatted', 'status_color'])
                    ->toArray();
                $totals[$status]['quantity'] = round($totals[$status]['quantity']);
            } else {
                $totals[$status] = (new static(['status' => $status]))
                    ->setAppends(['price_total_formatted', 'status_formatted', 'status_color'])
                    ->toArray();
                $totals[$status]['cnt'] = 0;
                $totals[$status]['quantity'] = 0;
            }
        }

        return $totals;
    }

    /**
     * @param $customer_id
     *
     * @return array
     */
    public static function abandonedGetCustomerTotals($customer_id): array
    {
        $totals = ['cnt' => 0, 'price_total' => 0, 'price_total_formatted' => Format::money(0)];
        AbandonedCart::whereAbandoned()->whereCustomer($customer_id)
            ->with('items')->get()->map(function (AbandonedCart $cart) use (&$totals): void {
                $totals['cnt']++;
                $totals['price_total'] += $cart->getTotal();
            });
        $totals['price_total_formatted'] = Format::money($totals['price_total']);

        return $totals;
    }

    /**
     * @param $customer_id
     *
     * @param array $statuses
     * @return array
     */
    public static function paymentGetCustomerTotals($customer_id, array $statuses = ['completed', 'refunded', 'chargebacked', 'pending']): array
    {
        $totals = [];
        $payments = OrderPayment::selectRaw("COUNT(`orders_payments`.`id`) as `cnt`, COALESCE(SUM(`orders_payments`.`amount`),0) as `amount`, `orders_payments`.`status`")
            ->join('orders', 'orders_payments.order_id', '=', 'orders.id')
            ->where('orders.customer_id', $customer_id)->whereIn('orders_payments.status', $statuses)
            ->groupBy('orders_payments.status')
            ->get(['cnt', 'amount', 'status'])
            ->keyBy('status');

        foreach ($statuses as $status) {
            if ($payments->has($status)) {
                $totals[$status] = $payments->get($status)
                    ->setAppends(['amount_formatted', 'status_formatted', 'status_color'])
                    ->toArray();
            } else {
                $totals[$status] = (new OrderPayment(['status' => $status, 'amount' => 0]))
                    ->setAppends(['amount_formatted', 'status_formatted', 'status_color'])
                    ->toArray();
                $totals[$status]['cnt'] = 0;
            }
        }

        return $totals;
    }

    /**
     * @param $customer_id
     *
     * @return array
     */
    public static function discountGetCustomerTotals($customer_id)
    {

        $customer_id = intval($customer_id);

        $discount = OrderDiscount::selectRaw("COUNT(`orders_discounts`.`id`) as `cnt`")
            ->joinRaw("INNER JOIN `orders` ON `orders_discounts`.`order_id` = `orders`.`id`")
            ->where('orders.customer_id', $customer_id)->where('orders.status', 'completed')
            ->first();

        return $discount->toArray();
    }

    /**
     * @param $id
     *
     * @return bool
     */
    public static function exists($id): bool
    {
        return static::find($id) ? true : false;
    }

    /**
     * @return null|PaymentProviders
     */
    public function getOrderPaymentProvider()
    {
        if (!$this->exists) {
            return;
        }

        $payment = $this->payments->last();
        if (!$payment) {
            return;
        }

        return ArrayCache::remember(
            'payment_provider_information_' . $payment->provider,
            fn () => PaymentProviders::findByProvider($payment->provider)
        );
    }

    /**
     * @return null|string
     */
    public function getOrderPaymentProviderName()
    {
        /** @var PaymentProviders $provider */
        $provider = $this->getOrderPaymentProvider();
        return $provider->storefront_name ?? null;
    }

    //    /**
    //     * @param Order|int $order
    //     * @param array $product_edit_dirty
    //     * @todo Verify working conditions with multi-payment order.
    //     * @todo Optimization may be required as several SQL queries are launched to achieve the purpose.
    //     *
    //     * @todo Hot-fix function that updates the last payment of an order with its total value.
    //     * @todo This aims to keep payment values consistent with the order, because for example Shipping rely on COD payments.
    //     */
    //    public static function updatePaymentAmount($order, array $product_edit_dirty = null)
    //    {
    //        if (!$order instanceof Order) {
    //            $order = static::find($order);
    //        }
    //        $order->updateLastPaymentAmount();
    //    }

    /**
     *
     */
    public function updateLastPaymentAmount(): void
    {
        /** @var OrderPayment $last_payment */
        $last_payment = $this->payments->last();
        $newTotal = $this->getInvoiceTotal();
        //        if (
        //            !is_null($last_payment->payment->authorize_amount ?? null) &&
        //            $last_payment->payment->authorize_amount < $newTotal
        //        ) {
        //            throw new Error(__('order.error.authorize_amount', [
        //                'amount' => money($last_payment->payment->authorize_amount, $last_payment->order->getCurrency(), $last_payment->order->getLanguage()),
        //                'total' => money($newTotal, $last_payment->order->getCurrency(), $last_payment->order->getLanguage()),
        //            ]));
        //        }

        if ($last_payment) {
            if ($last_payment->payment && !in_array($last_payment->payment->status, [Payments::STATUS_COMPLETED, Payments::STATUS_REFUNDED])) {
                $last_payment->payment->amount = $newTotal;
                $last_payment->payment->save();
            } else {
                $last_payment->amount = $newTotal;
                $last_payment->save();
            }

            event(new OrderPaymentUpdated($last_payment->setRelation('order', $this)));
        }
    }


    /**
     * @param ShippingProvider|null $shipping_provider
     * @param array $parameters
     * @param bool $force
     * @return ShippingProvider
     * @throws Error
     * @throws Throwable
     */
    public function initShippingQuote(?ShippingProvider $shipping_provider = null, array $parameters = [], $force = false)
    {
        if ($shipping_provider === null) {
            if (!($shipping_provider = $this->shipping->provider ?? null)) {
                throw new Error(__('order.err.invalid_shipping_provider'));
            }
        }

        if (!$this->shippingAddress || $this->shippingAddress->hasMissingData()) {
            throw new Error(__('order.err.invalid_address'));
        }

        if ($shipping_provider->id != ($this->shipping->provider_id ?? null)) {
            $this->shippingAddress->mapOmnishipParametersByProvider($shipping_provider);
        } elseif (!empty($parameters['service_id'])) {
            $shipping_provider->service_id = $parameters['service_id'];
        } elseif ($service_id = $this->meta_pluck->get('service_id', $this->shipping->service_id ?? null)) {
            $shipping_provider->service_id = $service_id;
        }

        $existsQuotes = false;
        $shipping_provider->manager->setOrder($this);
        if ($force === false && !empty($parameters['quotes']) && is_array($parameters['quotes']) && !empty($parameters['quotes'][$shipping_provider->service_id])) {
            $shipping_provider_quotes = $parameters['quotes'];
            $existsQuotes = true;
        } elseif ($force === false && $shipping_provider->id == ($this->shipping->provider_id ?? null) && is_array($services = json_decode((string) $this->meta_pluck->get('quotes'), true)) && !empty($services[$shipping_provider->service_id])) {
            $shipping_provider_quotes = $services;
            $existsQuotes = true;
        } else {
            $shipping_provider_quotes = $shipping_provider->manager->getQuotesForProvider($request_response);

            if (($_COOKIE['quote-logs'] ?? null) == $shipping_provider->manager->getKey() && $request_response && (!empty($request_response->request) || !empty($request_response->response))) {
                try {
                    LogShippingQuotes::create([
                        'site_id' => (int)site('site_id'),
                        'order' => $this->getKey(),
                        'request' => $request_response->request ?? null,
                        'response' => $request_response->response ?? null,
                        'type' => $request_response->type ?? null,
                    ]);
                } catch (Throwable) {
                }
            }
        }

        $formatter = new QuotesFormat($shipping_provider->manager, $shipping_provider_quotes);
        $quotes_formatters = $formatter->getQuotesFormatted();
        $shipping_provider_quotes = $formatter->getQuotes();

        if (!$force && (!$shipping_provider_quotes || $shipping_provider_quotes->isEmpty())) {
            return $this->initShippingQuote($shipping_provider, $parameters, true);
        }

        //@todo change this by p.iliev command!!
        if (!$quotes_formatters instanceof \Omniship\Common\ShippingQuoteBag || $quotes_formatters->isEmpty()) {
            if (($shipping_provider->manager ?? false) && ($error = $shipping_provider->manager->getError()) && !empty($error['message'])) {
                throw new Error($error['message']);
            } else {
                $field = null;
                if (activeRoute('admin.orders.products.add')) {

                } elseif (activeRoute('admin.orders.products.edit')) {
                    $field = 'price';
                } elseif (activeRoute('admin.orders.shipping.change')) {
                    $field = 'shipping_provider';
                }

                if (!$shipping_provider->manager->isExternal() && $shipping_provider->rates->isNotEmpty()) {
                    if ($shipping_provider->type == 'price') {
                        throw new Error(__('order.err.shipping_provider_no_matching_rate_price', [
                            'min' => money($shipping_provider->rates->min('from'), $this->getCurrency(), $this->getLanguage()),
                            'max' => money($shipping_provider->rates->min('to'), $this->getCurrency(), $this->getLanguage()),
                            'total' => money($this->getTotalBeforeShipping(), $this->getCurrency(), $this->getLanguage()),
                        ]), $field);
                    } elseif ($shipping_provider->type == 'weight') {
                        throw new Error(__('order.err.shipping_provider_no_matching_rate_price', [
                            'min' => weight($shipping_provider->rates->min('from')),
                            'max' => weight($shipping_provider->rates->min('to')),
                            'total' => weight($this->weight),
                        ]), $field);
                    } else {
                        throw new Error(__('order.err.shipping_provider_no_matching_rate_price_and_weight'), $field);
                    }
                } else {
                    throw new Error(__('order.err.shipping_provider_no_matching_rate'), $field);
                }
            }
        }

        $old_shipping = $this->shipping;

        /** @var ShippingQuote $quote */
        $quote = $quotes_formatters->sortBy('price', SORT_ASC)
            ->first();

        $shipping_provider->setAttribute('service_id', $parameters['service_id'] ?? $quote->getId())
            ->setAttribute('service_name', $parameters['service_name'] ?? $quote->getName())
            ->setAttribute('quotes', $shipping_provider->quotes);

        $savePrice = false;
        if (($this->payment->status ?? null) == 'completed') {
            if ($existsQuotes && ($parameters['overwrite_price'] ?? false) === true && is_numeric($parameters['price'] ?? null)) {
                $allQuotes = with($quotes_formatters)->map(function (ShippingQuote $quote) use ($parameters, $shipping_provider) {
                    $quote->setPrice(Format::moneyInput($parameters['price']));
                    $quote = $shipping_provider->manager->formatQuotesForDisplay(new ShippingQuoteBag([$quote]), false)->first();
                    return $quote;
                });
                $shipping_provider->quotes = $allQuotes;
                $shipping_provider->quotes_formatted = $allQuotes;
                $shipping_provider->quote = $allQuotes->sortBy('price', SORT_ASC)->first();
                $shipping_provider->quote_formatted = $shipping_provider->quote;
                $savePrice = $old_shipping ?: true;
            } else {
                $oldPrice = $old_shipping->order_amount ?? 0;
                if ($old_shipping->new_format ?? false) {
                    $oldPrice = $old_shipping->order_amount_with_vat;
                }

                /** @var OrderTotals $oldTotalShipping */
                $oldTotalShipping = $this->totals->where('group', 'shipping')->firstWhere('key', 'shipping');
                $allQuotes = with($quotes_formatters)->map(function (ShippingQuote $quote) use ($oldPrice, $oldTotalShipping, $old_shipping, $shipping_provider) {
                    $quote->setPrice(moneyFloat($oldPrice));
                    $quote = $shipping_provider->manager->formatQuotesForDisplay(new ShippingQuoteBag([$quote]), false)->first();
                    if ($oldTotalShipping) {
                        $quote->setParameter('allow_modify_vat', (int)!$oldTotalShipping->hide_in_invoice)
                            ->setParameter('price_vat', $quote->getPrice() - Format::moneyInput($oldTotalShipping->price_without_vat))
                            ->setParameter('price_with_vat', $quote->getPrice())
                            ->setParameter('price_without_vat', Format::moneyInput($oldTotalShipping->price_without_vat));
                        if (!$oldTotalShipping->hide_in_invoice && $quote->getType() == 'calculator') {
                            $quote->setType('custom');
                        }

                        $shipping_provider->manager->quotePriceFormats($quote);
                    }

                    return $quote;
                });
                $shipping_provider->quotes = $allQuotes;
                $shipping_provider->quotes_formatted = $allQuotes;
                $shipping_provider->quote = $allQuotes->sortBy('price', SORT_ASC)->first();
                $shipping_provider->quote_formatted = $shipping_provider->quote;
                $savePrice = $old_shipping ?: true;
            }
        } elseif ($existsQuotes && ($parameters['overwrite_price'] ?? false) === true && is_numeric($parameters['price'] ?? null)) {
            $allQuotes = with($quotes_formatters)->map(function (ShippingQuote $quote) use ($parameters, $shipping_provider) {
                $quote->setPrice(moneyFloat($parameters['price']));
                $quote = $shipping_provider->manager->formatQuotesForDisplay(new ShippingQuoteBag([$quote]), false)->first();
                return $quote;
            });
            $shipping_provider->quotes = $allQuotes;
            $shipping_provider->quotes_formatted = $allQuotes;
            $shipping_provider->quote = $allQuotes->sortBy('price', SORT_ASC)->first();
            $shipping_provider->quote_formatted = $shipping_provider->quote;
            $savePrice = $old_shipping ?: true;
        } else {
            $shipping_provider->quotes = $quotes_formatters;
            $shipping_provider->quotes_formatted = $quotes_formatters;
        }

        if ($shipping_provider->quote) {
            $shipping_provider->setAttribute('service_id', $shipping_provider->quote->getId())
                ->setAttribute('service_name', $shipping_provider->quote->getName());
        }

        $shipping_provider->rate = new ShippingRate([
            'provider_id' => $shipping_provider->id,
            'amount' => $shipping_price = Format::toIntegerPrice($shipping_provider->quote_formatted ? $shipping_provider->quote_formatted->getPrice() : 0)
        ]);

        $this->unsetRelation('shipping');

        return $shipping_provider->setAttribute('savePrice', $savePrice)
            ->syncOriginal();
    }

    /**
     * @param ShippingProvider $shipping_provider
     * @return OrderShipping
     * @throws Error
     */
    public function updateShippingFromInitQuote(ShippingProvider $shipping_provider)
    {
        $metaData = [
            'pricing_type' => $shipping_provider->manager->getPricingType(),
            'integration' => $shipping_provider->manager->getKey(),
            'insurance' => $shipping_provider->manager->supportsInsurance(),
            'default_weight' => Format::toIntegerWeight($shipping_provider->manager->getDefaultWeight()),
            'fixed_price' => $shipping_provider->manager->getSetting('fixed_price'),
            //speedy
            'special_delivery_requirements' => $shipping_provider->manager->getSetting('special_delivery_requirements'),
            'option_before_payment' => $shipping_provider->manager->getOptionBeforePayment(),
            'fragile' => $shipping_provider->manager->getSetting('fragile'),
            'back_documents' => $shipping_provider->manager->getSetting('back_documents'),
            'back_receipt' => $shipping_provider->manager->getSetting('back_receipt'),
            'documents' => $shipping_provider->manager->getSetting('documents'),
            'saturday_delivery' => $shipping_provider->manager->getSetting('saturday_delivery'),
            //econt
            'priority_type' => null,
            //all
            'total' => $shipping_provider->rate->amount,
            'quotes' => json_encode($shipping_provider->quotes ? $shipping_provider->quotes->toArray() : null),
            'rate' => in_array($shipping_provider->type, ['price', 'weight']) && $shipping_provider->rate ? $shipping_provider->rate->toJson() : null,
            'rates' => in_array($shipping_provider->type, ['price', 'weight']) && $shipping_provider->rate ? json_encode([$shipping_provider->rate]) : null,
            'omniship.credentials' => json_encode($shipping_provider->manager->getCredentials()),
            'money_transfer' => $shipping_provider->manager->isMoneyTransfer(),
        ];

        //        if($shipping_provider->service_id && $shipping_provider->quotes->has($shipping_provider->service_id)) {
        //            $shipping_provider->quote = $shipping_provider->quotes->get($shipping_provider->service_id);
        //        }

        if ($shipping_provider->quote_formatted) {
            $metaData = array_merge($metaData, [
                'service_id' => $shipping_provider->quote_formatted->getId(),
                'service_name' => $shipping_provider->quote_formatted->getName(),
                'currency' => $shipping_provider->quote_formatted->getCurrency(),
                //'total' => $shipping_provider->quote_formatted->getPrice(),
            ]);
        }

        $this->setMeta($metaData);

        // calculate insurance amount
        //$shipping_provider->formatInsurance($this->manager->getTotalWithoutShipping());

        $has_insurance = $shipping_provider->manager->isExternal() ? false : $shipping_provider->insurance_enable && $shipping_provider->quote_formatted && $shipping_provider->quote_formatted->getInsurance() > 0;
        $data = [
            'order_id' => $this->id,
            'provider_id' => $shipping_provider->id,
            'provider_name' => $shipping_provider->name,
            'provider_type' => $shipping_provider->type,
            'provider_from' => $shipping_provider->rate->from ?? 0,
            'provider_to' => $shipping_provider->rate->to ?? 0,
            'provider_amount' => $shipping_provider->rate->amount ?? 0,
            'order_has_insurance' => $has_insurance ? 'yes' : 'no',
            'provider_insurance' => $has_insurance ? $shipping_provider->insurance : null,
            //'order_amount' => $shipping_provider->rate->amount + $shipping_provider->getAttribute('insurance_amount'),
            'order_amount' => $shipping_provider->rate->amount ?? 0,
        ];

        if ($shipping_provider->quote_formatted) {
            $data = array_merge($data, [
                'order_insurance' => $has_insurance ? Format::toIntegerPrice($shipping_provider->quote_formatted->getInsurance()) : null,
                //new
                'new_format' => 1,
                'exclude_vat' => $shipping_provider->quote_formatted->getParameter('exclude_vat'),
                'order_amount_vat' => ($order_amount_vat = Format::toIntegerPrice($shipping_provider->quote_formatted->getParameter('price_vat'))),
                'order_amount_with_vat' => ($price_with_vat = Format::toIntegerPrice($shipping_provider->quote_formatted->getParameter('price_with_vat'))),
                'order_amount_without_vat' => ($price_without_vat = Format::toIntegerPrice($shipping_provider->quote_formatted->getParameter('price_without_vat'))),
                //price 2
                'provider_amount_vat' => $order_amount_vat,
                'provider_amount_with_vat' => $price_with_vat,
                'provider_amount_without_vat' => $price_without_vat,
                //order_insurance
                'order_insurance_vat' => $has_insurance ? Format::toIntegerPrice($shipping_provider->quote_formatted->getParameter('insurance_vat')) : null,
                'order_insurance_with_vat' => $has_insurance ? Format::toIntegerPrice($shipping_provider->quote_formatted->getParameter('insurance_with_vat')) : null,
                'order_insurance_without_vat' => $has_insurance ? Format::toIntegerPrice($shipping_provider->quote_formatted->getParameter('insurance_without_vat')) : null,
            ]);
        }

        if (($savePrice = $shipping_provider->getAttribute('savePrice')) && $savePrice !== true) {
            $data = array_merge($data, [
                'order_has_insurance' => $savePrice->order_has_insurance,
                'provider_insurance' => $savePrice->provider_insurance,
                'order_insurance' => $savePrice->order_insurance,
                'order_insurance_vat' => $savePrice->order_insurance_vat,
                'order_insurance_with_vat' => $savePrice->order_insurance_with_vat,
                'order_insurance_without_vat' => $savePrice->order_insurance_without_vat,
            ]);
        }

        if (activeRoute('admin.orders.shipping.change apps.*.changePickup admin.orders.products.*')) {
            $this->setMeta(['side' => $this->getWaybillSideWithoutMeta()]);
        }

        return $this->setRelation('shipping', $this->shipping()
            ->updateOrCreate([], $data))->shipping->setRelation('provider', $shipping_provider)
            ->setAttribute('overload', true)
            ->syncOriginal();
    }


    /**
     * @param $shipping_provider_id
     * @param bool $insurance
     * @param bool $force
     * @return $this
     * @throws Error
     * @throws Throwable
     */
    public function changeShipping($shipping_provider_id, $insurance = false, $force = false): static
    {
        $shipping_provider = ShippingProvider::find($shipping_provider_id);
        if (!$shipping_provider) {
            throw new Error(__('order.err.invalid_shipping_provider'));
        }

        $old_shipping = $this->shipping;
        $shipping_provider = $this->initShippingQuote($shipping_provider, [], $force);

        if ($insurance && empty($shipping_provider->insurance)) {
            throw new Error(__('order.err.shipping_provider_does_not_have_insurance'));
        }

        if ($this->shipping) {
            $this->shipping->fill([
                "provider_id" => $shipping_provider->id,
                "provider_name" => $shipping_provider->name
            ])->setRelation('provider', $shipping_provider);
        }

        $new_shipping = $this->updateShippingFromInitQuote($shipping_provider);

        if ($this->shippingAddress) {
            $this->shippingAddress->update([
                'integration' => $shipping_provider->manager->getKey()
            ]);
        }

        $this->setRelation('shipping', $new_shipping)
            ->load('meta');

        event(new OrderShippingChange($this, $new_shipping, $old_shipping));

        return $this;
    }

    /**
     * @param mixed $shipping_provider_id
     * @param mixed $insurance
     * @return mixed
     */
    public function changePaymentCalculate($shipping_provider_id, $insurance = false): void
    {
        $shipping_provider = ShippingProvider::find($shipping_provider_id);
        $shipping_provider = $this->initShippingQuote($shipping_provider, [], true);
        if ($insurance && empty($shipping_provider->insurance)) {
            throw new Error(__('order.err.shipping_provider_does_not_have_insurance'));
        }

        $new_shipping = $this->updateShippingFromInitQuote($shipping_provider);
    }

    /**
     * @return mixed|null
     */
    public function getBolId()
    {
        return $this->meta_pluck->get('bol_id');
    }

    /**
     * @return bool
     */
    public function isCashOnDelivery(): bool
    {
        if (!empty($this->payment->payment_provider->provider) && $this->payment->payment_provider->provider == 'cod') {
            return true;
        }

        return false;
    }

    /**
     * @return int
     */
    public function getInsuranceAmount()
    {
        return $this->getTotalWithoutShipping();
    }

    /**
     * @return float
     */
    public function getInsuranceAmountInput(): string|int
    {
        return Format::moneyInput($this->getInsuranceAmount());
    }

    /**
     * @param bool $full
     * @return array
     */
    public function api($full = false)
    {
        $data = $this->traitApi($full);

        try {
            $data['customer_ip'] = long2ip($this->customer_ip);
        } catch (Throwable) {
            //
        }

        //discounts format
        //        $data['discounts'] = $this->discounts->map(function (OrderDiscount $discount) {
        //            if ($discount->isCode() && $discount->isOverwriteProductPrice()) {
        //                return null;
        //            }
        //
        //            return $discount->api();
        //        })->filter()->values()->all();
        $data['discounts'] = $this->getTotals()->only(['discount.before', 'discount.after'])
            ->collapse()->map(fn (CartTotal $total): array => [
                'id' => $total->getModel()->id ?? null,
                'name' => $total->getName(),
                'code' => !empty($total->getModel()->code) ? $total->getModel()->code : null,
                'type' => $total->getModel()->original_type ?? $total->getModel()->type ?? null,
                'type_value' => is_numeric($total->getModel()->type_value ?? null) ? percentFloat($total->getModel()->type_value) : null,
                'order_over' => is_numeric($total->getModel()->order_over ?? null) ? percentFloat($total->getModel()->order_over) : null,
            ])->values()->all();

        //shipping format
        if ($this->shipping) {
            $data['shipping'] = $this->shipping->api();
            if ($this->shipping->provider->integration ?? false) {
                $data['shipping']['provider'] = $this->shipping->provider->integration;
            }

            if ($this->meta_pluck->get('service_id') && $this->meta_pluck->get('service_name')) {
                $data['shipping']['service_id'] = $this->meta_pluck->get('service_id');
                $data['shipping']['service_name'] = $this->meta_pluck->get('service_name');
            }

            if ($this->fulfillment) {
                $keys = [
                    'shipping_tracking_url' => 'tracking_url',
                    'shipping_tracking_number' => 'tracking_number',
                    'date_fulfilled' => 'updated_at',
                    'shipping_date_delivery' => 'date_delivery',
                    'shipping_date_expedition' => 'date_expedition'
                ];
                foreach ($keys as $field => $key) {
                    $data['shipping'][$key] = $this->fulfillment->{$field} instanceof Carbon ? $this->fulfillment->{$field}->format('Y-m-d\TH:i:sP') : $this->fulfillment->{$field};
                }
            }

            if ($date = $this->desired_delivery_date) {
                $data['desired_delivery_date'] = $this->desired_delivery_date;
            }

            if ($geo_zone = ($this->shipping->provider->geo_zone ?? null)) {
                $data['shipping']['geo_zone'] = $geo_zone->name;
            }
        }

        $customFields = [];
        if ($this->customer->custom_data ?? false) {
            $options = FormFieldOptions::get()->keyBy('id');
            foreach ($this->customer->custom_data as $customData) {
                $option = is_scalar($customData->value) ? $options->get($customData->value) : false;
                if ($option && $customData->field) {
                    $customFields[$customData->field->name] = $option->name;
                }
            }
        }

        $data['customer_custom_fields'] = $customFields;

        //        //products format
        if ($this->products->count()) {
            $data['products'] = $this->products->map(function (OrderProduct $product) use (&$data) {
                if (!empty($data['discounts']) && $product->order_discount_id) {
                    $ids = array_column($data['discounts'], 'id');
                    if (($index = array_search($product->order_discount_id, $ids, true)) !== false) {
                        $data['discounts'][$index]['order_product_id'] = $product->id;
                    }
                }

                return $product->api();
            })->values()->all();
        }

        //        //payments format
        if ($this->payments->count()) {
            $data['payments'] = $this->payments->map(fn (OrderPayment $payment) => $payment->api())->all();
        }

        if ($this->payment) {
            $data['payment'] = $this->payment->api();
        }

        //        //taxes format
        if ($this->taxes->count()) {
            $data['taxes'] = $this->taxes->where('product_id', null)->map(fn (OrderTax $tax) => $tax->api())->values()->all();
        }

        //        //shipping address format
        if ($this->shippingAddress) {
            $data['shipping_address'] = $this->shippingAddress->api();
        }

        //        //billing address format
        if ($this->billingAddress) {
            $data['billing_address'] = $this->billingAddress->api();
        } elseif ($this->shippingAddress) {
            $data['billing_address'] = $data['shipping_address'];
        }

        //$data['products_total'] = is_numeric($data['price_products_subtotal']) ? (float)Format::moneyInput($data['price_products_subtotal']) : $data['price_products_subtotal'];
        $data['order_subtotal'] = is_numeric($data['price_subtotal']) ? (float)Format::moneyInput($data['price_subtotal']) : $data['price_subtotal'];
        $data['order_total'] = is_numeric($data['price_total']) ? (float)Format::moneyInput($data['price_total']) : $data['price_total'];
        $data['weight'] = is_numeric($data['weight']) ? (float)Weight::input($data['weight']) : $data['weight'];

        foreach (['price_products_subtotal', 'price_subtotal', 'price_total'] as $key) {
            if (array_key_exists($key, $data)) {
                unset($data[$key]);
            }
        }

        return $data;
    }

    /**
     * @param string $type
     * @throws ReflectionException
     * @deprecated
     */
    public function events($type = 'updated'): void
    {
        if (!$this->exists || $this->meta_pluck->get('is_draft')) {
            return;
        }

        $this->refresh();
        switch ($type) {
            case 'updated':
                event(new HookEvent(HookEvent::OrderUpdated, $this));
                break;
            case 'created':
                event(new HookEvent(HookEvent::OrderCreated, $this));
                break;
        }
    }

    /**
     * @param string $action
     * @param bool $queue
     */
    public function hooks(string $action = 'updated', bool $queue = false): void
    {
        if (!$this->exists || $this->meta_pluck->get('is_draft')) {
            return;
        }

        $type = null;
        if ($action === 'updated') {
            $type = HookEvent::OrderUpdated;
        } elseif ($action === 'created') {
            $type = HookEvent::OrderCreated;
        }

        if (!$type) {
            return;
        }

        if($queue) {
            HookEventJob::dispatch($type, $this->id)
                ->delay(5)->onQueue('order-events8');
        } else {
            $this->refresh();
            event(new HookEvent($type, $this, app()->runningInConsole() ? null : 1));
        }
    }

    /**
     * @return null |null
     * @throws SiteCpAccessDenied
     */
    public function checkLockingAndLock(): void
    {
        if (!$this->exists) {
            return;
        }

        /** @var $admin Admin */
        $admin = Auth::admin();
        if ($admin->type == 'owner') {
            return;
        }

        //        if ($this->date_locking && $this->moderator_id != $admin->id) {
        //            if ($this->date_locking > Carbon::now()->subMinutes(setting('lock_orders_time', 7))) {
        //                throw new SiteCpAccessDenied(__('order.is_opened_from_other', ['user' => $this->lockFrom->username]));
        //            }
        //        }

        if (($locked = $this->meta_pluck->get('locked')) && ($locked = @unserialize($locked))) {
            if (
                $locked['moderator_id'] != $admin->id &&
                $locked['date_locking'] instanceof Carbon &&
                $locked['date_locking'] > Carbon::now()->subMinutes(setting('lock_orders_time', 7))
            ) {
                throw new SiteCpAccessDenied(__('order.is_opened_from_other', ['user' => $locked['lockFrom'] ?? null]));
            }
        }

        $this->setMeta([
            'locked' => serialize([
                'date_locking' => Carbon::now(),
                'moderator_id' => $admin->id,
                'lockFrom' => $admin->username,
            ])
        ]);

        //        $this->update([
        //            'date_locking' => Carbon::now(),
        //            'moderator_id' => $admin->id
        //        ]);
    }

    /**
     * @return integer
     */
    public function sumTaxes()
    {
        try {
            return $this->getTotals()
                ->only(['tax.before', 'tax.after'])
                ->collapse()->sum('price');
        } catch (Throwable) {
        }

        return $this->taxes->sum('order_amount');
    }

    /**
     * @param array $meta_data
     * @return $this
     */
    public function setMeta(array $meta_data = []): static
    {
        return $this->updateMeta($meta_data);
    }

    /**
     * @param array $meta_data
     * @return bool
     */
    public function createMetas(array $meta_data = [])
    {
        array_walk($meta_data, function (&$value, $key): void {
            $value = [
                'order_id' => $this->id,
                'parameter' => $key,
                'value' => $value
            ];
        });

        return $this->meta()->insert(array_values($meta_data));
    }

    /**
     * @param $key
     * @param $value
     * @return OrderMeta
     */
    public function createMeta($key, $value): OrderMeta
    {
        return $this->meta()->create([
            'parameter' => $key,
            'value' => $value
        ]);
    }

    /**
     * @param array $meta_data
     * @return $this
     */
    public function updateMeta(array $meta_data = []): static
    {
        if (empty($meta_data)) {
            return $this;
        }

        $ids = $this->meta()->whereIn('parameter', array_keys($meta_data))->pluck('id');
        if ($ids->isNotEmpty()) {
            retry(3, fn() => $this->meta()->whereKey($ids)->delete(), 300);
        }

        $this->meta()->insert(collect($meta_data)->map(fn ($value, $parameter): array => [
            'order_id' => $this->id,
            'parameter' => $parameter,
            'value' => $value,
        ])->values()->all());
        //        foreach ($meta_data as $key => $value) {
        //            $this->meta()->updateOrCreate([
        //                'parameter' => $key
        //            ], ['value' => $value]);
        //        }

        if ($this->relationLoaded('meta')) {
            $this->load('meta');
        }

        if (isset($this->data['meta_pluck'])) {
            unset($this->data['meta_pluck']);
        }

        return $this;
    }

    /**
     * @param TaxContract $tax
     * @param array $extra
     * @return OrderTax|TaxContract
     */
    public function createTaxNew(TaxContract $tax, array $extra = []): void
    {
        $this->taxes()->create(array_merge([
            'tax_id' => $tax->getId(),
            'tax_name' => $tax->getName(),
            'tax_description' => $tax->getDescription(),
            'tax_target' => $tax->getTarget(),
            'tax_geo_zone_id' => $tax->getGeoZoneId(),
            'tax_price_with_vat' => $tax->isPriceWithVat(),
            'tax_type' => $tax->getType(),
            'tax_shipping' => $tax->isBeforeShipping() ? YesNo::False : YesNo::True,
            'tax_tax' => $tax->getTaxValue(),
            'tax_vat' => $tax->isVat() ? YesNo::True : YesNo::False,
            'order_amount' => $tax->getAmount(),
            'override_id' => $tax->getOverrideId(),
            'oss_registration' => $tax->isOssRegistration(),
            'payment_provider' => $tax->getPaymentProvider(),
            'shipping_provider' => $tax->getShippingProvider(),
        ], $extra));
    }

    /**
     * @param Tax $tax
     * @param null|mixed $shipping_provider
     * @param null|mixed $payment_provider
     * @return Tax|void
     */
    public function getTaxesByShippingAndPayment(Tax $tax, $shipping_provider = null, $payment_provider = null)
    {
        if ($payment_provider || $this->payment) {
            $payment_provider ??= $this->payment->provider;
            if (!empty($this->shipping) && !empty($this->shipping->provider_id)) {
                $shipping_provider ??= $this->shipping->provider_id;
            }

            if (!empty($tax->getPaymentProvider()) && !empty($tax->getShippingProvider())) {
                if ($payment_provider == $tax->getPaymentProvider() && $shipping_provider == $tax->getShippingProvider()) {
                    return $tax;
                }
            } elseif (!empty($tax->getPaymentProvider())) {
                if ($payment_provider == $tax->getPaymentProvider()) {
                    return $tax;
                }
            } elseif (!empty($tax->getShippingProvider())) {
                if ($shipping_provider == $tax->getShippingProvider()) {
                    return $tax;
                }
            }
        }
    }

    /**
     * @param array|string $meta_keys
     * @return $this
     */
    public function removeMeta($meta_keys): static
    {
        $ids = $this->meta()->whereIn('parameter', (array)$meta_keys)->pluck('id');
        if ($ids->isNotEmpty()) {
            $this->meta()->whereKey($ids)->delete();
        }

        if ($this->relationLoaded('meta')) {
            $this->load('meta');
        }

        if (isset($this->data['meta_pluck'])) {
            unset($this->data['meta_pluck']);
        }

        return $this;
    }

    /**
     * @return Order
     * @throws Exception
     */
    public function updateOrderTotals(): Order
    {
        if (!$this->exists) {
            return $this;
        }

        $weight = $this->getWeight();
        if(!$weight && $this->getShippingManager()){
            $weight = $this->getShippingManager()->getTotalWeight();
        }
        $update = [
            'price_products_subtotal' => ($sub_total = $this->getSubTotal()),
            'price_subtotal' => $sub_total,
            'price_total' => $this->getTotal(),
            'quantity' => $this->getProducts()->sum('quantity'),
            'weight' => $weight,
        ];

        $this->updateLastPaymentAmount();
        $this->updateTotalsFromManager();

        $this->taxes()->where('tax_vat', YesNo::False)
            ->whereNull('payment_provider')
            ->whereNull('shipping_provider')
            ->whereNull('product_id')->delete();
        foreach ($this->getNotVatTaxes() as $tax) {
            if (is_null($tax->payment_provider) && is_null($tax->shipping_provider)) {
                $this->createTaxNew($tax);
            }
        }

        $update['without_vat_reasons'] = null;
        $this->taxes()->where('tax_vat', YesNo::Yes)->whereNull('product_id')->delete();
        if (($globalVatTax = $this->getVatByAddress()) !== null) {
            $this->createTaxNew($globalVatTax);
            $update['without_vat_reasons'] = $globalVatTax->getWithoutVatReason();
        }

        $this->update($update);

        return $this;
    }

    /**
     * @param OrderProduct $product_dirty
     * @return $this
     * @throws Error
     */
    public function quantityIncrementDecrementProductEdit(OrderProduct $product_dirty): static
    {
        if ($product_dirty->tracked == YesNo::False) {
            return $this;
        }

        /** @var OrderProduct $order_product */
        $order_product = $this->products()->with(['product', 'variant'])->find($product_dirty->id);
        if ($order_product && $order_product->variant && $order_product->tracking == YesNo::True) {

            if (CustomIntegration::supportQuantityIncrementDecrementProducts()) {
                CustomIntegration::quantityIncrementDecrementProducts($order_product);
            }

            if (CustomIntegrationModule::supportQuantityIncrementDecrementProducts()) {
                CustomIntegrationModule::quantityIncrementDecrementProducts($order_product);
            }

            // calculating threshold
            $threshold = false;
            if (!empty($order_product->threshold)) {
                $threshold = $order_product->threshold;
            } elseif (setting('product_threshold')) {
                $threshold = setting('product_threshold');
            }

            $old_quantity = $order_product->variant->quantity;
            if (($order_product->variant->quantity + $product_dirty->quantity) >= $order_product->quantity || $order_product->product->continue_selling == YesNo::True) {
                $quantity = $product_dirty->quantity - $order_product->quantity;
                $order_product->variant->quantity += $quantity;

                if (($old_quantity == 0 && $order_product->product->continue_selling == YesNo::True && $order_product->variant->quantity > 0) || $order_product->variant->quantity < 0) {
                    $order_product->variant->quantity = 0;
                }
            } elseif ($order_product->product->continue_selling != YesNo::True) {
                throw new Error(sprintf(__('order.err.not_enough_quantity_for_%1$s'), $order_product->name));
            }

            $order_product->variant->save();

            if ($order_product->variant->quantity == 0) {
                $this->_addOutOfStockProduct($order_product);
            } elseif ($threshold > $order_product->variant->quantity) {
                $this->_addLowStockProduct($order_product, $order_product->variant->quantity);
            }
        }

        if (!empty($this->_low_stock_products)) {
            QueueNotifyAdmin::sendLowQuantityNotification($this->_low_stock_products);
        }

        if (!empty($this->_out_of_stock_products)) {
            QueueNotifyAdmin::sendOutOfStockNotification($this->_out_of_stock_products);
        }

        return $this;
    }

    /**
     * @param string $operation (increment/decrement)
     * @return boolean
     */
    public function allowIncrementDecrementProducts($operation): bool
    {
        if (
            $operation == 'decrement' &&
            (
                $this->status_fulfillment == 'fulfilled' ||
                ($this->status_for_quantity_decrease == 'paid' && in_array($this->status, [OrderStatus::PAID, OrderStatus::AUTHORIZED, OrderStatus::COMPLETED])) ||
                ($this->status_for_quantity_decrease == 'pending' && in_array($this->status, [OrderStatus::PAID, OrderStatus::AUTHORIZED, OrderStatus::COMPLETED, OrderStatus::PENDING]))
            )
        ) {
            return true;
        }

        if ($operation == 'increment' && in_array($this->status, [OrderStatus::PAID, OrderStatus::AUTHORIZED, OrderStatus::COMPLETED])) {
            return false;
        }

        if (
            $operation == 'increment' &&
            (
                ($this->status_fulfillment != 'fulfilled' && $this->status_for_quantity_decrease == 'paid') ||
                ($this->status_for_quantity_decrease == 'paid' && !in_array($this->status, [OrderStatus::PAID, OrderStatus::AUTHORIZED, OrderStatus::COMPLETED])) ||
                ($this->status_for_quantity_decrease == 'pending' && !in_array($this->status, [OrderStatus::PAID, OrderStatus::AUTHORIZED, OrderStatus::COMPLETED, OrderStatus::PENDING]))
            )
        ) {
            return true;
        }

        return false;
    }

    /**
     * @return string
     */
    public static function getOrderStatusDecrementProducts()
    {
        return setting('order_status_for_quantity_decrease', 'paid');
    }

    /**
     * @return OrderMeta|HasOne
     */
    public function meta_status_for_quantity_decrease()
    {
        return $this->meta_single()->where('parameter', 'status_for_quantity_decrease');
    }

    /**
     * decrement transfer qty from product to order product
     * increment transfer qty from order product to product
     * @param string $operation (increment|decrement)
     * @return $this
     * @throws Error
     */
    public function quantityIncrementDecrementProducts($operation): static
    {
        if (!$this->allowIncrementDecrementProducts($operation)) {
            return $this;
        }

        /** @var OrderProduct[]|Collection $order_products */
        $order_products = $this->products()->with(['product', 'variant'])
            ->where('tracked', $operation == 'increment' ? YesNo::True : YesNo::False)->get();

        foreach ($order_products as $order_product) {
            $this->quantityIncrementDecrementProduct($order_product, $operation);
        }

        if (!empty($this->_low_stock_products)) {
            QueueNotifyAdmin::sendLowQuantityNotification($this->_low_stock_products);
        }

        if (!empty($this->_out_of_stock_products)) {
            QueueNotifyAdmin::sendOutOfStockNotification($this->_out_of_stock_products);
        }

        return $this;
    }

    /**
     * decrement transfer qty from product to order product
     * increment transfer qty from order product to product
     * @param string $operation (increment|decrement)
     * @return $this
     * @throws Error
     */
    public function quantityIncrementDecrementProductsFulfillmentRemove($operation): static
    {
        if (!$this->allowIncrementDecrementProducts($operation) || $this->status_for_quantity_decrease != 'paid') {
            return $this;
        }

        /** @var OrderProduct[]|Collection $order_products */
        $order_products = $this->products()->with(['product', 'variant'])
            ->where('tracked', $operation == 'increment' ? YesNo::True : YesNo::False)->get();

        foreach ($order_products as $order_product) {
            $this->quantityIncrementDecrementProduct($order_product, $operation);
        }

        if (!empty($this->_low_stock_products)) {
            QueueNotifyAdmin::sendLowQuantityNotification($this->_low_stock_products);
        }

        if (!empty($this->_out_of_stock_products)) {
            QueueNotifyAdmin::sendOutOfStockNotification($this->_out_of_stock_products);
        }

        return $this;
    }

    /**
     * @param OrderProduct $order_product
     * @param $operation
     * @return $this
     * @throws Error
     * @throws Throwable
     */
    public function quantityIncrementDecrementProduct(OrderProduct $order_product, $operation): static
    {
        if (activeRoute('admin.orders.products.delete') ? false : !$this->allowIncrementDecrementProducts($operation)) {
            return $this;
        }

        if (CustomIntegration::supportQuantityIncrementDecrementProducts()) {
            CustomIntegration::quantityIncrementDecrementProducts($order_product);
        }

        if (CustomIntegrationModule::supportQuantityIncrementDecrementProducts()) {
            CustomIntegrationModule::quantityIncrementDecrementProducts($order_product);
        }

        if ($order_product->variant && $order_product->tracking == YesNo::True) {
            // calculating threshold
            $threshold = false;
            if (!empty($order_product->threshold)) {
                $threshold = $order_product->threshold;
            } elseif (setting('product_threshold')) {
                $threshold = setting('product_threshold');
            }

            $old_quantity = $order_product->variant->quantity;
            if ($operation == 'decrement') {
                $update = null;
                if ($old_quantity >= $order_product->quantity || $order_product->product->continue_selling == YesNo::True) {
                    $update = [
                        'quantity' => $order_product->variant->quantity - $order_product->quantity
                    ];
                    //$order_product->variant->quantity -= $order_product->quantity;
                    if (($old_quantity == 0 && $order_product->product->continue_selling == YesNo::True && $update['quantity'] > 0) || $update['quantity'] < 0) {
                        $update['quantity'] = 0;
                    }
                } elseif ($order_product->product->continue_selling != YesNo::True) {
                    throw new Error(sprintf(__('order.err.not_enough_quantity_for_%1$s'), $order_product->name));
                }

                if ($update) {
                    $order_product->variant->updateColumns($update, true, $order_product->product, [
                        'action' => 'order',
                        'order_id' => $this->id,
                    ]);
                }

                $order_product->variant->save();
                $order_product->update(['tracked' => YesNo::True]);

                if ($order_product->variant->quantity == 0) {
                    $this->_addOutOfStockProduct($order_product);
                } elseif ($threshold >= $order_product->variant->quantity) {
                    $this->_addLowStockProduct($order_product, $order_product->variant->quantity);
                }
            } elseif ($operation == 'increment') {
                $update = null;
                if (($order_product->product->continue_selling == YesNo::True && $old_quantity > 0) || $order_product->product->continue_selling != YesNo::True) {
                    $update['quantity'] = $order_product->variant->quantity + $order_product->quantity;
                } elseif ($order_product->product->continue_selling == YesNo::True && $old_quantity == 0) {
                    $update['quantity'] = 0;
                }

                if ($update) {
                    $order_product->variant->updateColumns($update, true, $order_product->product, [
                        'action' => 'order',
                        'order_id' => $this->id,
                    ]);
                }

                $order_product->variant->save();
                //$order_product->update(['tracked' => $this->allowIncrementDecrementProducts('decrement') ? YesNo::True : YesNo::False]);
                $order_product->update(['tracked' => YesNo::False]);

                if ($order_product->variant->quantity == 0) {
                    $this->_addOutOfStockProduct($order_product);
                } elseif ($threshold >= $order_product->variant->quantity) {
                    $this->_addLowStockProduct($order_product, $order_product->variant->quantity);
                }
            }
        }

        if (!Apps::installed('store_locations') && !activeRoute('checkout.payment.submit')) {
            if ($order_product->product_id) {
                ProductTemp::updateTempTableAndPopulateByProduct($order_product->product_id);
            }
        }

        return $this;
    }

    /**
     * @return Order
     */
    public function updateTotalsFromManager(): static
    {
        $this->totals()->delete();
        $total_row = 0;
        foreach ($this->getTotals() as $group => $totals) {
            /** @var CartTotal $total */
            foreach ($totals as $key => $total) {
                $this->totals()->create(array_merge($total->toArray(), [
                    'group' => $group,
                    'key' => $key,
                    'sort_order' => $total_row++,
                ]));
            }
        }

        return $this;
    }

    /**
     * @return $this|Order
     * @throws Error
     */
    public function paymentSyncStatus()
    {
        if ($this->manual) {
            return $this;
        }

        $old_status = $this->status;
        $status = $this->status;

        $total_payment_amount = 0;

        // TRACK FOR ORDER STATUS
        $pending = 0; // 'initiated', 'requested', 'pending', 'held'
        $timeouted = 0;
        $voided = 0;
        $cancelled = 0;
        $failed = 0;
        $chargebacked = 0;
        $refunded = 0;
        $completed = 0;
        $authorized = 0;

        foreach ($this->payments as $payment) {
            if ($payment->status === 'authorized') {
                $authorized++;
            } elseif ($payment->status === 'completed') {
                $completed++;
            } elseif ($payment->status === 'chargebacked') {
                $chargebacked++;
            } elseif ($payment->status === 'refunded') {
                $refunded++;
            } elseif ($payment->status === 'failed') {
                $failed++;
            } elseif ($payment->status === 'cancelled') {
                $cancelled++;
            } elseif ($payment->status === 'voided') {
                $voided++;
            } elseif ($payment->status === 'timeouted') {
                $timeouted++;
            } elseif (in_array($payment->status, [Payments::STATUS_INITIATED, Payments::STATUS_REQUESTED, Payments::STATUS_PENDING, 'held'])) {
                $pending++;
            }
        }

        //        if ($this->price_total == $total_payment_amount) {
        if ($authorized > 0) {
            $status = 'authorized';
        } elseif ($completed > 0) {
            $status = 'paid';
        } elseif ($pending > 0) {
            $status = 'pending';
        } elseif ($chargebacked > 0) {
            $status = 'chargebacked';
        } elseif ($refunded > 0) {
            $status = 'refunded';
        } elseif ($voided > 0) {
            $status = 'voided';
        } elseif ($failed > 0) {
            $status = 'failed';
        } elseif ($cancelled > 0) {
            $status = 'cancelled';
        } elseif ($timeouted > 0) {
            $status = 'timeouted';
        } else {
            $status = 'pending';
        }

        if ($status == 'paid' && $this->status_fulfillment == 'fulfilled' && setting('order_complete', 1)) {
            $status = 'completed';
        }

        return $this->changeStatus($status);
    }

    public function syncDiscountUses(): void
    {
        foreach ($this->discounts as $order_discount) {
            /** @var OrderDiscount $order_discount */
            if (!empty($order_discount->discount_id) && !empty($order_discount->discount)) {
                $order_discount->discount->incrementUses();
            }
        }
    }

    /**
     * @return BaseCollection
     * @throws Error
     */
    public function getWaybillSides()
    {
        $shipping_manager = $this->getShippingManager();
        $sides = $shipping_manager ? $shipping_manager->getWaybillSides() : null;
        if (!($sides instanceof BaseCollection)) {
            $sides = collect();
        }

        $pricing = $shipping_manager ? $shipping_manager->getPricingType() : null;

        if (
            !$pricing ||
            in_array($this->status, ['paid', 'completed']) ||
            in_array($pricing, ['fixed_price', 'fixed_weight', 'calculator_fixed', 'price_and_weight']) ||
            $this->has_free_shipping ||
            ($this->payment->payment_provider->is_seller_payer_shipping ?? false) ||
            ($shipping_manager && ($free = $shipping_manager->getFreeShippingTotal('input')) > 0 && $free <= $this->getTotalWithoutShipping('input'))/* ||
            (($shipping_provider = $this->getShippingProviderWithQuotes()) && $shipping_provider->quote_formatted && $shipping_provider->quote_formatted->getParameter('allow_modify_vat'))*/
        ) {
            $sides->forget(Consts::PAYER_RECEIVER);
        }

        return $sides;
    }

    /**
     * @return string|null
     * @throws Error
     */
    public function getWaybillSide()
    {
        $sides = $this->getWaybillSides();

        $side = $this->meta_pluck->get('side', optional($this->getShippingManager())->getPayerSide());
        if ($sides->has($side)) {
            return $side;
        }

        return $sides->keys()->first();
    }

    /**
     * @return string|null
     * @throws Error
     */
    public function getWaybillSideWithoutMeta()
    {
        $sides = $this->getWaybillSides();

        $side = optional($this->getShippingManager())->getPayerSide(false);
        if ($sides->has($side)) {
            return $side;
        }

        return $sides->keys()->first();
    }

    /**
     * gets order history by order id
     *
     * @param bool $groupByDate
     *
     * @return array Model
     *
     */
    public function getHistory($groupByDate = true): array
    {
        /** @var Collection $history */
        $history = $this->history()->with('admin')->get();

        $history->prepend(new OrderHistory([
            "order_id" => $this->id,
            "message" => "order_add",
            "message_data" => json_encode([
                'customer_email' => $this->customer_email,
                'customer_first_name' => $this->customer_first_name,
                'customer_last_name' => $this->customer_last_name,
                'date_added' => $this->date_added->format(DateTimeFormat::getFormatByTemplate()),
                'is_admin' => (bool)$this->meta_pluck->get('is_admin'),
            ]),
            "admin_id" => null,
            "action" => "order_add",
            "date" => $this->date_added,
        ]));

        $result = [];
        $country_codes = [];

        $logs = collect();
        if ($history->firstWhere('log_id', '!=', 'null')) {
            try {
                $logs = LogWaybill::site()->where('order_id', $this->id)->get()->keyBy('id');
            } catch (Throwable) {

            }
        }

        if(Invoicing::getActiveProvider() && Invoicing::getNumberReceipt($this)){
            $before = $history->filter(fn ($history): bool => $history->date <= $this->receipt_date);
            $before->push(new OrderHistory([
                "order_id" => $this->id,
                "message" => "order_receipt_sent",
                "message_data" => json_encode([
                    'customer_email' => $this->customer_email,
                    'customer_first_name' => $this->customer_first_name,
                    'customer_last_name' => $this->customer_last_name,
                    'receipt_date' => Invoicing::getDateReceipt($this)->format(DateTimeFormat::getFormatByTemplate()),
                    'receipt_number' => Invoicing::getNumberReceipt($this),
                ]),
                "admin_id" => null,
                "action" => "order_receipt_sent",
                "date" => Invoicing::getDateReceipt($this)
            ]));

            $history = collect($before)
                ->merge($history->filter(fn ($history): bool => $history->date > $this->receipt_date));
        }

        foreach ($history as $item) {

            $item->setRelation('order', $this);

            if (!empty($item->message_data)) {

                if (is_array($item->message_data)) {
                    if (isset($item->message_data['before']) && isset($item->message_data['before']['country'])) {
                        if (!in_array($item->message_data['before']['country'], $country_codes) &&
                            $item->message_data['before']['country']
                        ) {
                            $country_codes[] = $item->message_data['before']['country'];
                        }
                    }

                    if (isset($item->message_data['after']) && isset($item->message_data['after']['country'])) {
                        if (!in_array($item->message_data['after']['country'], $country_codes) &&
                            $item->message_data['after']['country']
                        ) {
                            $country_codes[] = $item->message_data['after']['country'];
                        }
                    }
                }

            }

            if ($item->action == 27 && $item->log_id) {
                $item->setAttribute('waybill', $logs->get($item->log_id))
                    ->syncOriginal();
            }

            if ($groupByDate) {
                $date = explode(' ', (string) $item->date)[0];
                $result[$date][] = $item;
            }
        }

        if (!empty($country_codes)) {
            $countries = \App\Common\Country::getListByCode($country_codes);
        } else {
            $countries = null;
        }

        return [
            'records' => $groupByDate ? $result : $history,
            'countries' => $countries
        ];

    }

    /**
     * @param OrderProduct $order_product
     * @param $quantity
     */
    private function _addLowStockProduct(OrderProduct $order_product, $quantity): void
    {
        if ($order_product->product->continue_selling == YesNo::False) {
            $this->_low_stock_products[$order_product->product_id]['name'] = $order_product->name;
            $this->_low_stock_products[$order_product->product_id]['sku'] = $order_product->sku;
            $this->_low_stock_products[$order_product->product_id]['quantity'] = $quantity;
            $this->_low_stock_products[$order_product->product_id]['variant'] = '';

            if ($order_product->v3) {
                $this->_low_stock_products[$order_product->product_id]['variant'] = $order_product->v1 . ' ' . $order_product->v2 . ' ' . $order_product->v3;
            } elseif ($order_product->v2) {
                $this->_low_stock_products[$order_product->product_id]['variant'] = $order_product->v1 . ' ' . $order_product->v2;
            } elseif ($order_product->v1) {
                $this->_low_stock_products[$order_product->product_id]['variant'] = $order_product->v1;
            }
        }
    }

    /**
     * @param OrderProduct $order_product
     */
    private function _addOutOfStockProduct(OrderProduct $order_product): void
    {
        //        if($order_product->product->continue_selling == YesNo::False) {
        $this->_out_of_stock_products[$order_product->product_id]['name'] = $order_product->name;
        $this->_out_of_stock_products[$order_product->product_id]['sku'] = $order_product->sku;
        $this->_out_of_stock_products[$order_product->product_id]['variant'] = '';

        if ($order_product->v3) {
            $this->_out_of_stock_products[$order_product->product_id]['variant'] = $order_product->v1 . ' ' . $order_product->v2 . ' ' . $order_product->v3;
        } elseif ($order_product->v2) {
            $this->_out_of_stock_products[$order_product->product_id]['variant'] = $order_product->v1 . ' ' . $order_product->v2;
        } elseif ($order_product->v1) {
            $this->_out_of_stock_products[$order_product->product_id]['variant'] = $order_product->v1;
        }

        //        }
    }

    /**
     * @param $feature
     * @return int|mixed
     * @throws Exception
     */
    public static function planUsage($feature)
    {
        if (site('sand_box')) {
            return 0;
        }

        switch ($feature) {
            // total orders amount
            case 'orders_amount':
                return OrderTotals::where('key', 'total')->sum('price_input');
            // case 'orders_revenue':
            //     // orders revenue for the last 6 months in EUR
            //     $revenue = static::where('date_added', '>=', now()->subMonths(6))
            //         ->whereNotIn('status', ['cancelled', 'failed', 'refunded'])
            //         ->sum('price_total');

            //     $revenue /= 100;
            //     if (site('currency') != 'EUR') {
            //         $revenue = round(Currency::convert($revenue, site('currency'), 'EUR'), 2);
            //     }

            //     return $revenue;
            default:
                return 0;
        }
    }

    /**
     * @return array
     */
    public function formatInvoiceListCp(): array
    {
        $this->number = '<a href="' . \LinkerCp::order($this->id) . '">' . __('order.order_#') . $this->id . '</a><br>' .
            Tools::viewDetails(__('order.action.view_details'), \LinkerCp::order($this->id), '_self');

        $this->price_total_formatted = '<span class="money">' . Format::money($this->price_total) . '</span>';

        $this->date_added_formatted = '<span class="date">' . Format::date($this->date_added) . '</span><br />' .
            '<span class="time">' . Format::time($this->date_added) . '</span>';


        //        http://lyubo.ccdev.pro/admin/orders/action/invoice?order_id=80&output=I
        if (!is_null($this->credit_number)) {
            $this->download = '<a class="btn btn-primary" target="_blank" href="' . route('admin.orders.invoice', $this->id) . '"">' . __('order.download.invoice') . '</a> <a class="btn btn-primary" target="_blank" href="' . route('admin.order.credit.action', $this->id) . '"">' . __('order.download.credit') . '</a>';
        } else {
            $this->download = '<a class="btn btn-primary" target="_blank" href="' . route('admin.orders.invoice', $this->id) . '"">' . __('order.download.invoice') . '</a>';
        }

        $customer = $this->customer;
        $this->customer = $this->customer_full_name;

        if ($customer) {
            $this->customer = '<a class="customer" target="_blank" href="' . route('admin.customers.details', $customer->id) . '">' . $this->customer_full_name . '</a><br />' .
                '<a class="customer_orders" href="' . \LinkerCp::orders(['customer' => $customer->id]) . '">' . $customer->orders_count . ' ' . __('order.label.orders') . '</a>';
        }

        return [
            'id' => $this->id,
            'number' => $this->number,
            'customer' => $this->customer,
            'price_total_formatted' => $this->price_total_formatted,
            'date_added_formatted' => $this->date_added_formatted,
            'download' => $this->download,
            'invoice_number' => $this->invoice_number,
        ];
    }

    /**
     * @return string
     */
    public function getStatusClass(): string
    {
        if ($this->meta_pluck->get('is_draft')) {
            return 'notification-gray';
        }

        if (!empty($this->date_archived)) {
            return 'notification-gray';
        }

        if (in_array($this->status, ['completed', 'paid'])) {
            return 'notification-green';
        }

        if ($this->status === 'pending') {
            return $this->status_fulfillment === 'fulfilled' ? 'notification-purple' : 'notification-orange';
        }

        return in_array($this->status, OrderStatus::$statuses) ? 'notification-red' : 'notification-orange';
    }

    /**
     * @return array
     */
    public function formatListCp(): array
    {
        $channelMark = '';
        /**
         * The channel mark can be replaced by icon or some other indication for the channel.
         */
        if ($this->channel) {
            switch ($this->channel) {
                case 'facebook':
                    $channelMark = '<i class="table-grid-order-icon order-icon fab fa-facebook-square"></i>';
                    break;
            }
        }

        if ($cartSource = $this->meta_pluck->get('cart.source')) {
            $img_url = config('url.img') . 'sitecp/img/marketing/facebook/messenger-logo.svg';
            switch ($cartSource) {
                case 'messenger-bot':
                    $channelMark = '<i class="table-grid-order-icon order-icon"><img src="' . $img_url . '" width="15" height="15"/></i>';
                    break;
            }
        }

        $fastOrderIcon = $this->meta_pluck->get('fast_order') ? ' <i class="fal fa-tachometer-alt-fast"></i>' : '';

        $this->number = '<div class="table-grid-td-row no-wrap"> <a class="table-grid-order-number" href="' . \LinkerCp::order($this->id) . '">'
            . __('order.order_#') . ($this->id != $this->order_number ? ($this->id . ' (' . $this->increment_hash . ')') : $this->id)
            . '</a>'
            . $fastOrderIcon
            . $channelMark
            . '<a href="' . route('admin.orders.details', ['order_id' => $this->id, 'preview' => true]) . '" class="table-grid-icon warning btn btn-link border-0" data-panel-class="wide order-preview" data-ajax-panel="true"><i class="fal fa-eye cc-grey"></i></a>'
            . '</div>';
        if ($this->customer_id) {
            $customer_filter = route('admin.orders', '?filters[customer]=' . $this->customer_id);
            $this->number .= '<div class="table-grid-td-row table-grid-meta"><span class="table-grid-order-by">' . __('global.from') . '</span> <a class="table-grid-order-customer" href="' . \LinkerCp::customer($this->customer_id) . '">' . $this->customer_first_name . ' ' . $this->customer_last_name . '</a> <span class="table-grid-order-count">(<a href="' . $customer_filter . '">' . ($this->customer->orders_total ?? 0) . '</a>)</span></div>';
        }

        $payment_name = null;
        $payment_className = null;
        if ($this->payments->isNotEmpty() && ($payment = $this->payments->last()) && $payment->payment_provider) {
            $payment_name = $payment->payment_provider->storefront_name ?? null;
            $payment_className = $payment->payment_provider->order_color_class_name;
        }

        $this->price_total_formatted = '<div class="table-grid-td-row"><strong class="table-grid-order-amount no-wrap">' . Format::money($this->price_total) . '</strong></div><div class="table-grid-td-row"><span class="table-grid-order-payment-method table-grid-meta ' . $payment_className . '">' . $payment_name . '</span></div>';
        $this->date_added_formatted = '<div class="table-grid-td-row"><span class="table-grid-order-date no-wrap">' . Format::date($this->date_added) . '</span></div><div class="table-grid-td-row"><span class="table-grid-order-time table-grid-meta no-wrap">' . Format::time($this->date_added) . '</span></div>';
        if (trim(strval($this->note_customer))) {
            $this->comment = '<span class="table-grid-icon warning"><i class="fal fa-file tooltips" data-placement="left" data-original-title="' . htmlspecialchars(
                $this->note_customer,
                ENT_QUOTES,
                'utf-8'
            ) . '"></i></span>';
        } else {
            $this->comment = '<span class="table-grid-icon"><i class="fal fa-file tooltips" data-placement="left" data-original-title="' . htmlspecialchars(
                __('order.no_comment'),
                ENT_QUOTES,
                'utf-8'
            ) . '"></i></span>';
        }

        if ($this->note_administrator) {
            $this->comment .= '<br><span class="table-grid-icon warning"><i class="fal fa-file tooltips" data-placement="left" data-original-title="' . htmlspecialchars(
                $this->note_administrator,
                ENT_QUOTES,
                'utf-8'
            ) . '"></i></span>';
        } else {
            $this->comment .= '<br><span class="table-grid-icon"><i class="fal fa-file tooltips" data-placement="left" data-original-title="' . htmlspecialchars(
                __('order.no_comment'),
                ENT_QUOTES,
                'utf-8'
            ) . '"></i></span>';
        }

        //        $this->comment .= \Illuminate\Support\Facades\View::make('orders.details.print-label', ['order' => $this])->render();

        if (is_null($this->shippingAddress)) {
            $this->address = __('order.no_address');
        } else {
            $this->address = '<div class="table-grid-td-row"><span class="table-grid-order-address-city">' . $this->shippingAddress->city_name . '</span></div><div class="table-grid-td-row"><span class="table-grid-order-address-country table-grid-meta">' . $this->shippingAddress->country_name . '</span></div>';
        }

        if (Apps::installed(ShippingHoursManager::APP_KEY)) {
            if ($this->shipping_date) {
                $this->shipping_date_formatted = '<div class="table-grid-td-row"><span class="table-grid-order-date no-wrap">' . Format::date($this->shipping_date) . '</span></div><div class="table-grid-td-row"><span class="table-grid-order-time table-grid-meta no-wrap">' . Format::time($this->shipping_from) . ' - ' . Format::time($this->shipping_to) . '</span></div>';
            } else {
                $this->shipping_date_formatted = '';
            }
        }

        $is_fulfillment = false;
        $receiving = null;
        $fulfillment = null;
        if ($this->fulfillments->count() && ($fulfillment = $this->fulfillments->last()) && $fulfillment->shipping_date_expedition) {
            $this->fulfillment = '<div class="table-grid-td-row"><span class="table-grid-order-fulfillment-date table-grid-meta no-wrap">' . Format::date($fulfillment->shipping_date_expedition) . '</span><br><span class="table-grid-order-payment-method table-grid-meta">' . (!empty($this->shipping->provider->name) ? $this->shipping->provider->name : '') . '</span></div>';
            $is_fulfillment = true;
            $receiving = $fulfillment->shipping_date_delivery;
        } else {
            $this->fulfillment = '<div class="table-grid-td-row"><span class="table-grid-order-fulfillment-date table-grid-meta no-wrap">&nbsp;</span><br><span class="table-grid-order-payment-method table-grid-meta">' . (!empty($this->shipping->provider->name) ? $this->shipping->provider->name : '') . '</span></div>';
        }

        if ($receiving && $is_fulfillment) {
            $this->receiving = '<div class="table-grid-td-row"> <span class="table-grid-order-receive-date nitification notification-success no-wrap">' . Format::date($receiving) . '</span> </div>';
        } else {
            $this->receiving = '';
        }

        if ($fulfillment && $fulfillment->shipping_tracking_number) {
            $this->receiving .= '<div class="table-grid-td-row"><span class="table-grid-order-tracking-number table-grid-meta no-wrap">' . $fulfillment->shipping_tracking_number . '</span>';
            $url = $fulfillment->shipping_tracking_url ?: ShippingProvider::TRACK17 . $fulfillment->shipping_tracking_number;
            if ($url) {
                $this->receiving .= ' <a href="' . $url . '" class="shipping-tracking-url" target="_blank"><i class="fa fa-external-link" aria-hidden="true"></i></a>';
            }

            $this->receiving .= '</div>';
        }

        $this->status_formatted = '<span class="notification ' . $this->getStatusClass() . '">' . CommonStatus::order($this->getOrderStatusDropdown()) . '</span>';

        return [
            'id' => $this->id,
            'number' => $this->number,
            'address' => $this->address,
            'date_added_formatted' => $this->date_added_formatted,
            'fulfillment' => $this->fulfillment,
            'receiving' => $this->receiving,
            'shipping_date_formatted' => $this->shipping_date_formatted,
            'status_formatted' => $this->status_formatted,
            'price_total_formatted' => $this->price_total_formatted,
            'comment' => $this->comment,
        ];
    }

    /**
     * @param CartItem|ItemContract $product
     * @return OrderProduct
     * @throws Error
     */
    public function createOrderProductFromItemContract(ItemContract $product): OrderProduct
    {
        $product->validateOrderQuantity();

        /** @var null|OrderFulfillment $order_product_fulfillment */
        $order_product_fulfillment = null;
        if ($product->isDigital()) {
            $order_product_fulfillment = $this->fulfillments()->create([
                'date_fulfilled' => Carbon::now('UTC'),
            ]);
        }

        $order_bundle = null;
        if ($product->bundle_id && !empty($product->bundle->bundle_id) && !empty(CartBundle::find($product->bundle_id))) {
            $order_bundle = $this->bundles()->firstOrCreate([
                'bundle_id' => $product->bundle->bundle_id,
                'cart_bundle_id' => $product->bundle_id
            ]);
        }

        /** @var OrderProduct $order_product */
        $data = [
            'quantity' => $product->getQuantity(),
            'product_id' => $product->getId(),
            'variant_id' => $product->getVariantId(),
            'name' => $product->getName(),
            'sku' => $product->getSku(),
            'barcode' => $product->getBarcode(),
            'weight' => $product->getWeight(),
            'order_fulfillment_id' => $order_product_fulfillment->id ?? null,
            'vendor_id' => $product->getVendorId(),
            'vendor_name' => $product->getVendorName(),
            'category_id' => $product->getCategoryId(),
            'category_name' => $product->getCategoryName(),
            'sale' => $product->hasDiscounts() ? YesNo::True : YesNo::False,
            'new' => in_array($product->new ?? null, ['yes', 'no']) ? $product->new : 'no',
            'digital' => $product->isDigital() ? YesNo::True : YesNo::False,
            'tracked' => YesNo::False,
            'order_bundle_id' => $order_bundle->id ?? null,
            //new tax refactoring
            'price' => $product->getPrice()->getPrice(),
            'price_with_vat' => $product->getPrice()->getPriceWithVat(),
            'price_without_vat' => $product->getPrice()->getPriceWithoutVat(),
            'price_vat' => $product->getPrice()->getPriceVat(),
            'order_price' => round($product->getTotalPriceWithOptionsAfterDiscountsWithModification() / $product->getQuantity()),
            'exclude_vat' => $product->hasVat() && $product->getVat()->isAllowExcludeVat(),
            //
            'new_format' => 1,
            //end new tax refactoring
            'width' => $product->getWidth(),
            'height' => $product->getLength(),
            'depth' => $product->getDepth(),
        ];

        if (Apps::installed(GroceryStoreManager::APP_KEY)) {
            $data = array_merge($data, [
                'unit_id' => $product->unit_id,
                'unit_value' => $product->unit_value,
                'units' => $product->units->map(fn (Units $unit) => $unit->getAttributes())->all(),
                'base_unit_value' => $product->base_unit_value,
                'base_unit_id' => $product->base_unit_id,
                'unit_type' => $product->unit_type,
            ]);
        }

        if ($product->hasParameters()) {
            foreach ($product->getParameters() as $parameter) {
                $data = array_merge($data, [
                    'p' . $parameter['index'] => $parameter['name'],
                    'v' . $parameter['index'] => $parameter['value'],
                ]);
            }
        }

        $order_product = $this->products()->create($data);
        $order_product->setRelation('order', $this);

        $order_product->setQuantityBeforeOrder($product->product_quantity);
        $order_product->setContinueSellingBeforeOrder($product->continue_selling == YesNo::True);

        if ($product->hasVat()) {
            $this->createTaxNew(($vat = $product->getVat()), [
                'tax_tax' => $vat->getTaxFinalValue(),
                'order_amount' => $product->getTotalVatWithOptionsAfterDiscounts(),
                'product_id' => $order_product->getKey(),
            ]);
        }

        if ($product->hasOptions()) {
            /** @var ItemOptionContract $option */
            foreach ($product->getOptions() as $option) {
                $order_product->options()->create([
                    'field_id' => $option->field_id > 0 ? $option->field_id : null,
                    'field_option_id' => $option->field_option_id,
                    'type' => $option->getType(),
                    'name' => $option->getName(),
                    'option' => $option->getOption(),
                    'value' => $option->getValue(),
                    'value_symbol' => $option->getSymbol(),
                    'apply_over_price_type' => $option->getApplyOverPriceType(),
                    'per_item' => $option->isPerItem(),
                    'amount_type' => $option->getAmountType(),
                    'amount' => $option->getAmount(),
                    //new tax refactoring
                    //
                    'new_format' => 1,
                    'exclude_vat' => $order_product->exclude_vat,
                    'price' => $option->getPrice()->getPrice(),
                    'price_with_vat' => $product->getPrice()->getPriceWithVat(),
                    'price_without_vat' => $product->getPrice()->getPriceWithoutVat(),
                    'price_vat' => $product->getPrice()->getPriceVat(),
                    'discount_price' => $option->getDiscountedPrice()->getPrice(),
                    'discount_price_with_vat' => $option->getDiscountedPrice()->getPriceWithVat(),
                    'discount_price_without_vat' => $option->getDiscountedPrice()->getPriceWithoutVat(),
                    'discount_price_vat' => $option->getDiscountedPrice()->getPriceVat(),
                    //end new tax refactoring
                ]);
            }
        }

        if ($crossSell = ($product->cross_sell->cart_cross_sell->cross_sell ?? null)) {
            $order_product->crossSell()->create([
                'order_id' => $this->id,
                'cross_sell_id' => $crossSell->id,
                'price' => !is_null($product->discount_price) ? $product->discount_price : $product->price + $product->options_price,
                'real_price' => (int)$product->price + $product->options_price,
                'name' => $crossSell->name,
                'type' => 'action',
                'discount_type' => $crossSell->discount_type,
                'free_products' => $crossSell->free_products,
                'discount_percent' => $crossSell->discount_percent,
            ]);
        }

        if ($crossSellTarget = ($product->cross_sell_item_target->cart_cross_sell->cross_sell ?? null)) {
            $order_product->crossSell()->create([
                'order_id' => $this->id,
                'cross_sell_id' => $crossSellTarget->id,
                'price' => !is_null($product->discount_price) ? $product->discount_price : $product->price + $product->options_price,
                'real_price' => (int)$product->price + $product->options_price,
                'name' => $crossSellTarget->name,
                'type' => 'target',
                'discount_type' => $crossSellTarget->discount_type,
                'free_products' => $crossSellTarget->free_products,
                'discount_percent' => $crossSellTarget->discount_percent,
            ]);
        }

        if ($product->up_sell_for_create) {
            $order_product->upSell()->create($product->up_sell_for_create);
        }

        if ($product->meta_data->isNotEmpty()) {
            foreach ($product->meta_data as $external_meta) {
                $order_product->global_meta_data()->updateOrCreate([
                    'parameter' => $external_meta->parameter,
                ], [
                    'value' => $external_meta->value
                ]);
            }
        }

        $this->createOrderProductDiscountsFromItemContract($product, $order_product);

        return $order_product;
    }

    /**
     * @param CartItem|ItemContract $product
     * @return array<array, OrderProduct>
     * @throws Error
     */
    public function editOrderProductFromItemContract(ItemContract $product): array
    {
        //$product->validateOrderQuantity($product->tracked == YesNo::True);

        /** @var null|OrderFulfillment $order_product_fulfillment */
        $order_product_fulfillment = null;
        if($product->order_fulfillment_id) {
            $product->fulfillment()->delete();
        }

        if ($product->isDigital()) {
            $order_product_fulfillment = $this->fulfillments()->create([
                'date_fulfilled' => Carbon::now('UTC'),
            ]);
        }

        $product->fill([
            'quantity' => $product->getQuantity(),
            'order_fulfillment_id' => $order_product_fulfillment->id ?? null,
            'sale' => $product->hasDiscounts() ? YesNo::True : YesNo::False,
            'digital' => $product->isDigital() ? YesNo::True : YesNo::False,
            //new tax refactoring
            'price' => $product->getPrice()->getPrice(),
            'price_with_vat' => $product->getPrice()->getPriceWithVat(),
            'price_without_vat' => $product->getPrice()->getPriceWithoutVat(),
            'price_vat' => $product->getPrice()->getPriceVat(),
            'order_price' => round($product->getTotalPriceWithOptionsAfterDiscountsWithModification() / $product->getQuantity()),
            'exclude_vat' => $product->hasVat() && $product->getVat()->isAllowExcludeVat(),
        ]);

        $dirty = $product->getDirtyOriginal();

        $product->save();

        $product->tax()->delete();
        if ($product->hasVat()) {
            $this->createTaxNew(($vat = $product->getVat()), [
                'tax_tax' => $vat->getTaxFinalValue(),
                'order_amount' => $product->getTotalVatWithOptionsAfterDiscounts(),
                'product_id' => $product->id,
            ]);
        }

        if ($product->hasOptions()) {
            /** @var ItemOptionContract $option */
            foreach ($product->getOptions() as $option) {
                if ($order_option = $product->options()->find($option->id)) {
                    $order_option->update([
                        'amount' => $option->getAmount(),
                        'exclude_vat' => $product->exclude_vat,
                        'price' => $option->getPrice()->getPrice(),
                        'price_with_vat' => $product->getPrice()->getPriceWithVat(),
                        'price_without_vat' => $product->getPrice()->getPriceWithoutVat(),
                        'price_vat' => $product->getPrice()->getPriceVat(),
                        'discount_price' => $option->getDiscountedPrice()->getPrice(),
                        'discount_price_with_vat' => $option->getDiscountedPrice()->getPriceWithVat(),
                        'discount_price_without_vat' => $option->getDiscountedPrice()->getPriceWithoutVat(),
                        'discount_price_vat' => $option->getDiscountedPrice()->getPriceVat(),
                    ]);
                }
            }
        }

        $product->discounts()->delete();
        $this->createOrderProductDiscountsFromItemContract($product);

        ksort($dirty);

        return [$dirty, $product];
    }

    /**
     * @return BaseCollection<ItemContract,OrderProduct>
     */
    public function updateOrderProductsFromSelf(): BaseCollection
    {
        return $this->products->map(function (ItemContract $itemContract): void {
            $this->editOrderProductFromItemContract($itemContract);
        });
    }

    /**
     * @param OrderProduct|ItemContract $product
     * @param ItemContract|null $orderProduct
     * @return void
     * @throws Error
     */
    public function createOrderProductDiscountsFromItemContract(ItemContract $product, ?ItemContract $orderProduct = null): void
    {
        if ($product->hasDiscounts()) {
            $parent_id = $this->getRelationValue('discounts')->where('code', '<>', null)->firstWhere('overwrite_product_price', 1)->id ?? null;
            /** @var DiscountContract $discount */
            foreach ($product->getDiscounts() as $discount) {
                $orderProduct = $orderProduct ?: $product;
                /** @var OrderDiscount $newDiscount */
                $newDiscount = $orderProduct->discounts()->create((function (DiscountContract $discount, $parent_id): array {
                    $data = [
                        'order_id' => $this->getKey(),
                        'discount_id' => is_numeric($discount->getId()) && $discount->getId() > 0 ? $discount->getId() : null,
                        'name' => $discount->getName(),
                        'code' => $discount->getCode(),
                        'code_format' => $discount->getBarcodeType(),
                        'code_prefix' => $discount->getCodePrefix(),
                        'code_apply' => $discount->isCodeApply(),
                        'type' => $discount->getType(),
                        'target_product_id' => $discount->getProductId(),
                        'target_product_category_id' => null,
                        'target_customer_group_id' => $discount->getCustomerGroupId(),
                        'hide_discount_price' => !$discount->isMsrp() && $discount->isHideDiscountedPrice(),
                        'msrp_price' => $discount->isMsrp() ? $discount->getMsrp() : null,
                        'override' => $discount->isOverwrite(),
                        'apply_regular_price' => $discount->isAllowCodeApplyAsRegularPrice(),
                        'key' => $discount->getDiscountKey(),
                        'order_price' => $discount->getProductAmount(),
                        'type_value' => $discount->getValue(),
                        'order_over' => $discount->getType() == 'fixed' ? null : $discount->getOrderOver(),
                        'discount_group' => $discount->getType() == 'code-pro' ? $discount->getType() : ($discount->isCodeContainer() ? 'container' : 'code'),
                        'code_pro_id' => $discount->getType() == 'code-pro' ? ($discount->code_pro->id ?? null) : null,
                    ];

                    if ($discount->getDiscountKey() == 'discount_code') {
                        $data['parent_id'] = $parent_id;
                        $data['barcode_prefix'] = $discount->isBarcodePrefix();
                    }

                    return $data;
                })($discount, $parent_id));

                if (
                    !$discount->isVirtual() &&
                    is_numeric($max_uses = $discount->getMaxUses()) &&
                    $discount->getUses() >= $max_uses
                ) {
                    throw new Error(__('sf.widget.checkout.err.discount_not_enough_uses_left'));
                }

                $discount->setAttribute('order_discount_id', $newDiscount->id);
                if (in_array($newDiscount->getDiscountKey(), ['discount_code']) || $newDiscount->getType() == 'code-pro') {
                    $orderProduct->setDiscountCode($newDiscount);
                } else {
                    $orderProduct->setDiscount($newDiscount);
                }

                $discount->syncOriginal();
            }
        }
    }

    /**
     * @param mixed $type
     * @return mixed
     */
    public function allowEditAddress($type): bool
    {
        if ($type == 'billing') {
            if (in_array($this->status, ['paid', 'completed']) || $this->status_fulfillment == 'fulfilled') {
                return false;
            }

            return true;
        } elseif ($type == 'shipping') {
            if (in_array($this->status, ['completed']) || $this->status_fulfillment == 'fulfilled') {
                return false;
            }

            return true;
        }

        return false;
    }

    /**
     * @param $order_id
     * @param null $output
     * @return string
     * @throws Error
     * @throws MpdfException
     * @throws Throwable
     */
    public static function receiptGenerate($order_id, $output = null)
    {
        /** @var null|Order $order */
        $order = null;
        if ($order_id instanceof Order) {
            $order_id = $order_id->id;
        }

        if (is_numeric($order_id)) {
            $order = static::allOrderData()->find($order_id);
        }

        if (!$order) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        if (!$order->generateReceiptNumber()) {
            return;
        }

        return switchLanguageCallback(function () use ($order, $output) {
            $mpdf = new Mpdf([
                'mode' => 'utf8',
                'format' => 'B',
                'margin_left' => 4,
                'margin_right' => 4,
                'margin_top' => 4,
                'margin_bottom' => 4,
                'margin_header' => 0,
                'margin_footer' => 0,
            ]);

            $receipt = view('n18_audit::receipt', [
                'order' => $order,
                'receipt' => $order->getReceipt(),
                'totals' => $order->totals->where('hide_in_invoice', 0),
                'store_details' => Setting::getStoreDetails(),
            ]);

            $mpdf->WriteHTML($receipt);

            if (Setting::showPoweredBy()) {
                $mpdf->SetHTMLFooter('<div style="font-size: 10px; color: #666; text-align: center">' . __('core.invoice_footer') . '</div>');
            }

            $mpdf->SetProtection(['print']);
            $mpdf->SetDisplayMode('fullpage');
            //        $mpdf->SetTitle(__('n18_audit::app.receipt.title'));
            //        $mpdf->SetAuthor(__('sf.order.invoice.author'));

            if (!empty($output)) {
                $order_date = $order->date_added->format("Y-m-d");
                return $mpdf->Output(sprintf('order_%s_receipt_%s.pdf', $order->order_number, $order_date), $output);
            }

            return $mpdf->Output('receipt.pdf', 'S');
        }, $order->locale);
    }

    /**
     * @return bool|\Carbon\Carbon|BaseCollection|int|mixed|static
     * @throws \Throwable
     */
    public function generateReceiptNumber()
    {
        if (!Apps::installed(AuditManager::APP_KEY)) {
            return false;
        }

        if ($this->receipt_number) {
            return $this->receipt_number;
        }

        if ($this->isReadyForReceipt()) {
            $result = $this->_generateReceiptNumber();
            return $result ? $this->receipt_number : false;
        }

        return false;
    }

    public function isReadyForReceipt(): bool
    {
        return $this->isReadyForInvoice();
        //return $this->status_fulfillment == 'fulfilled' || in_array($this->status, ['completed', 'paid']) || $this->isDigital($this->id);
    }

    /**
     * @return int
     * @throws \Throwable
     */
    private function _generateReceiptNumber()
    {
        return retry(5, function () {
            $this->receipt_number = $this->incrementReceiptNumber();
            $this->receipt_date = Carbon::now('UTC');

            return $this->update([]);
        }, 2000);
        //        try {
        //            $this->_max_try_generate_receipt_number++;
        //            $this->receipt_number = $this->incrementReceiptNumber();
        //            $this->receipt_date = Carbon::now('UTC');
        //
        //            return $this->update([]);
        //        } catch (\Throwable $e) {
        //            if (preg_match('~Duplicate\s*entry(.*)orders_receipt_number_unique~', $e->getMessage(), $m) && $this->_max_try_generate_receipt_number <= 3) {
        //                usleep(100000);
        //                return $this->_generateReceiptNumber();
        //            }
        //
        //            throw $e;
        //        }
    }

    /**
     * @return Carbon|BaseCollection|int|mixed|static
     */
    public function incrementReceiptNumber(): int
    {
        $invoice_number = (int)static::whereNotNull('receipt_number')
            ->where('id', '<>', $this->id)
            ->max('receipt_number');

        $invoice_number++;
        return $invoice_number;

        //        if (!ArrayCache::has('receipt:number.increment')) {
        //            $receipt_number = static::whereNotNull('receipt_number')->orderBy('receipt_number', 'desc')->first(['receipt_number'])->receipt_number ?? 0;
        //        } else {
        //            $receipt_number = ArrayCache::get('receipt:number.increment');
        //        }
        //
        //        $receipt_number++;
        //        ArrayCache::set('receipt:number.increment', $receipt_number);
        //
        //        return $receipt_number;
    }

    //////////////////// END N18 Audit ////////////////////

    public static function getLastMonthCount(): int
    {
        return (int)self::whereDate('date_added', '<=', now())
            ->whereDate('date_added', '>=', now()->subMonth())
            ->whereNotIn('status', ['cancelled', 'failed', 'refunded'])
            ->count('id');
    }

    public static function getLastYearCount(): int
    {
        return (int)self::whereDate('date_added', '<=', now())
            ->whereDate('date_added', '>=', now()->subYear())
            ->whereNotIn('status', ['cancelled', 'failed', 'refunded'])
            ->count('id');
    }

    public static function getLastMonthRevenue(): int|float
    {
        return self::whereDate('date_added', '<=', now())
                ->whereDate('date_added', '>=', now()->subMonth())
                ->whereNotIn('status', ['cancelled', 'failed', 'refunded'])
                ->sum('price_total') / 100;
    }

    public static function getLastYearRevenue(): int|float
    {
        return self::whereDate('date_added', '<=', now())
                ->whereDate('date_added', '>=', now()->subYear())
                ->whereNotIn('status', ['cancelled', 'failed', 'refunded'])
                ->sum('price_total') / 100;
    }

    /**
     * @param Order $order
     * @param null $output
     * @return string|void
     * @throws MpdfException
     * @throws SmartyException
     */
    public static function creditGenerate(Order $order, $output = null)
    {
        $locale = $order->locale ?: site('language', site('language_cp'));
        app()->setLocale($locale);

        //end load data for email and invoice
        $mpdf = new Mpdf(['utf8', 'A4', '', '', 20, 20, 40, 20, 10, 10]);

        $mpdf->SetProtection(['print']);

        $mpdf->showWatermarkText = true;
        $mpdf->watermark_font = 'Arial';
        $mpdf->watermarkTextAlpha = 0.1;
        $mpdf->allow_charset_conversion = false;
        $mpdf->SetDisplayMode('fullpage');

        $creditData = $order->getCreditNote();
        if ($creditData->getCreditNumber()) {
            $mpdf->SetTitle(__('sf.order.credit.title') . ' ' . $creditData->getCreditNumber());
        } else {
            $mpdf->SetTitle(__('sf.order.credit.title'));
        }

        $mpdf->SetAuthor(__('sf.order.invoice.author'));

        if (empty(setting('credit_body', ''))) {
            $tpl = View::fetch('sitecp::orders.credit', [
                'invoice' => $creditData,
            ]);
        } else {
            $tpl = OrderPrint::bodyForCredit($order);
        }


        if ($creditData->getLanguage() == 'bg') {
            $settingWatermark = setting('invoice_watermark', '');
            $mpdf->SetWatermarkText($settingWatermark ?: __('order.invoice.type.original'));
        }

        $mpdf->WriteHTML($tpl);
        if (Setting::showPoweredBy()) {
            $mpdf->SetHTMLFooter('<span style="font-size: 12px; color: #666;">' . __('core.invoice_footer') . '</span>');
        }

        if (!empty($output)) {
            $order_date = ($order->credit_date ?: $order->invoice_date)->format('Y-m-d');
            return $mpdf->Output(sprintf('order_%s_credit_%s.pdf', $order->id, $order_date), $output);
        }

        return $mpdf->Output('receipt.pdf', 'S');
    }


    /**
     * @param Order $order
     * @return bool
     */
    public function hasOnlyDigitalProducts()
    {
        $onlyDigitalProducts = true;
        foreach ($this->products as $product) {
            if (isset($product->product->digital) && $product->product->digital === YesNo::True) {
                continue;
            }

            $onlyDigitalProducts = false;
        }

        return $onlyDigitalProducts;
    }

    public function toHook(): OrderWebHook
    {
        return new OrderWebHook($this);
    }

    /**
     * @return HasOne|OrderFulfillmentReturn
     */
    public function fulfillment_return()
    {
        return $this->hasOne(OrderFulfillmentReturn::class, 'order_id', 'id')
            ->orderBy('id', 'desc');
    }

    /**
     * The attributes that should be cast to native types.
     *
     * @return array
     */
    #[\Override]
    protected function casts(): array
    {
        return [
            'json_data' => 'json',
            'customer_geoip' => 'json',
            'quantity' => 'float',
            'date_archived' => 'datetime',
            'date_locking' => 'datetime',
            'invoice_date' => 'datetime',
            'shipping_from' => 'datetime',
            'shipping_to' => 'datetime',
            'shipping_date' => 'datetime',
            'receipt_date' => 'datetime',
            'credit_date' => 'datetime',
        ];
    }

    public function zora_order_logs(): HasMany
    {
        return $this->hasMany(OrderSendLog::class);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getAllowMakeWaybillByStatusAttribute($value): bool
    {
        $notAllowed = array_diff(OrderStatus::$statuses, [OrderStatus::PENDING, OrderStatus::PAID, OrderStatus::AUTHORIZED]);
        return !in_array($this->status, $notAllowed);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getMetaPluckAttribute($value)
    {
        return $this->data['meta_pluck'] = Arr::get($this->data, 'meta_pluck', fn () => $this->meta->pluck('value', 'parameter'));
    }
    /**
     * @param Illuminate\Support\Collection $value
     * @return mixed
     */
    public function setMetaPluckAttribute(BaseCollection $value)
    {
        $this->data['meta_pluck'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDateAddedFormattedAttribute($value)
    {
        if (empty($this->data['date_added_formatted'])) {
            $this->data['date_added_formatted'] = $this->date_added ? $this->date_added->format(DateTimeFormat::getFormatByTemplate($this->date_template)) : null;
        }
        return $this->data['date_added_formatted'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setDateAddedFormattedAttribute($value)
    {
        $this->data['date_added_formatted'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceTotalFormattedAttribute($value)
    {
        if (empty($this->data['price_total_formatted'])) {
            $this->data['price_total_formatted'] = Format::money($this->price_total);
        }
        return $this->data['price_total_formatted'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setPriceTotalFormattedAttribute($value)
    {
        $this->data['price_total_formatted'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceTotalInputAttribute($value)
    {
        if (empty($this->data['price_total_input'])) {
            $this->data['price_total_input'] = Format::moneyInput($this->price_total);
        }
        return $this->data['price_total_input'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setPriceTotalInputAttribute($value)
    {
        $this->data['price_total_input'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getWeightInputAttribute($value): string
    {
        return Weight::input($this->weight);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getWeightFormattedAttribute($value): string
    {
        return Weight::format($this->weight);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceSubtotalInputAttribute($value)
    {
        if (empty($this->data['price_subtotal_input'])) {
            $this->data['price_subtotal_input'] = Format::moneyInput($this->price_subtotal);
        }
        return $this->data['price_subtotal_input'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setPriceSubtotalInputAttribute($value)
    {
        $this->data['price_subtotal_input'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceSubtotalFormattedAttribute($value)
    {
        if (empty($this->data['price_subtotal_formatted'])) {
            $this->data['price_subtotal_formatted'] = Format::money($this->price_subtotal);
        }
        return $this->data['price_subtotal_formatted'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setPriceSubtotalFormattedAttribute($value)
    {
        $this->data['price_subtotal_formatted'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceProductsSubtotalFormattedAttribute($value)
    {
        if (empty($this->data['price_products_subtotal_formatted'])) {
            $this->data['price_products_subtotal_formatted'] = Format::money($this->price_products_subtotal);
        }
        return $this->data['price_products_subtotal_formatted'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setPriceProductsSubtotalFormattedAttribute($value)
    {
        $this->data['price_products_subtotal_formatted'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceProductsSubtotalInputAttribute($value)
    {
        if (empty($this->data['price_products_subtotal_input'])) {
            $this->data['price_products_subtotal_input'] = Format::moneyInput($this->price_products_subtotal);
        }
        return $this->data['price_products_subtotal_input'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getStatusFormattedAttribute($value)
    {
        if (empty($this->data['status_formatted'])) {
            $this->data['status_formatted'] = CommonStatus::order($this->status);
        }
        return $this->data['status_formatted'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setStatusFormattedAttribute($value)
    {
        $this->data['status_formatted'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getStatusFulfillmentFormattedAttribute($value)
    {
        if (empty($this->data['status_fulfillment_formatted'])) {
            $this->data['status_fulfillment_formatted'] = CommonStatus::shipping($this->status_fulfillment);
        }
        return $this->data['status_fulfillment_formatted'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setStatusFulfillmentFormattedAttribute($value)
    {
        $this->data['status_fulfillment_formatted'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getLocaleAttribute($value)
    {
        if ($this->exists && empty($this->attributes['locale'])) {
            return site('language');
        }
        return $this->attributes['locale'] ?? null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getCurrencyAttribute($value)
    {
        if ($this->exists && empty($this->attributes['currency'])) {
            /** @var OrderTotals $subtotal */
            if ($this->relationLoaded('totals') && ($subtotal = $this->totals->firstWhere('key', 'subtotal')) && $subtotal->currency) {
                return $this->attributes['currency'] = $subtotal->currency;
            } elseif (($subtotal = $this->totals()->where('group', 'subtotal')->where('key', 'subtotal')->first()) && $subtotal->currency) {
                return $this->attributes['currency'] = $subtotal->currency;
            }

            return \site('currency');
        }
        return $this->attributes['currency'] ?? null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getAllowInvoicingAttribute($value)
    {
        if ($this->invoice_number) {
            return true;
        }
        if (setting('invoicing', 'yes') == YesNo::True) {
            return setting('billing_invoicing', 'yes') == YesNo::True ? !!$this->billingAddress : true;
        }
        return false;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getAllowCreditNoteAttribute($value)
    {
        if (setting('invoicing', 'yes') == YesNo::True) {
            return in_array($this->status, ['cancelled', 'refunded'], false) && $this->invoice_number;
        }
        return false;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getAllowEditAttribute($value): bool
    {
        //added 13.04.2020 by @zino
        //Peter Iliev  7:35 PM
        //моля те разреши редакция на поръчка при статус платена
        // its not a bug, its a feature, check help center ...
        return in_array($this->status, ['pending', 'paid', 'authorized']);
        return $this->status == 'pending';
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getHasFreeShippingAttribute($value): bool
    {
        $shippingDiscount = $this->discounts->first(fn (OrderDiscount $discount): bool => $discount->type == 'shipping' || str_contains($discount->type, 'payment-shipping-'));
        return $shippingDiscount && (is_null($shippingDiscount->order_over) || $shippingDiscount->order_over <= $this->price_total);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getOmnishipProviderAttribute($value)
    {
        if (empty($this->data['omniship_provider'])) {
            $this->data['omniship_provider'] = null;
            if ($this->meta_pluck->has('integration')) {
                $this->data['omniship_provider'] = $this->meta_pluck->get('integration');
            } elseif ($this->shippingAddress) {
                $this->data['omniship_provider'] = $this->shippingAddress->integration;
            }
        }
        if (str_contains((string) $this->data['omniship_provider'], '_') && !empty($this->shipping->provider->integration)) {
            $this->data['omniship_provider'] = $this->shipping->provider->integration;
        }
        return $this->data['omniship_provider'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getSenderAddressAttribute($value)
    {
        if (empty($this->data['sender_address'])) {
            $this->data['sender_address'] = 'default';
            if ($this->omniship_provider && !empty($sender_address = json_decode((string) $this->meta_pluck->get($this->omniship_provider . '.sender_address'), true))) {
                $this->data['sender_address'] = ($sender_address['type'] ?? '') . '-' . ($sender_address['id'] ?? '');
            }
        }
        return $this->data['sender_address'];
    }
    /**
     * @param Omniship\Common\Address|null $address
     * @return mixed
     */
    public function setSenderAddressAttribute(?\Omniship\Common\Address $address = null)
    {
        if ($address->getParameter('type') == 'default') {

        }
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getShippingManagerAttribute($value)
    {
        throw new \InvalidArgumentException(sprintf('Method %s is deprecated!', __FUNCTION__));
        if ((($shipping = $this->getShipping()) !== null)) {
            return $shipping->manager;
        }
        return;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDesiredDeliveryDateAttribute($value): ?array
    {
        if (Apps::installed(ShippingHoursManager::APP_KEY)) {
            if ($this->shipping_date) {
                return [
                    'from' => $this->shipping_date->clone()->setTimeFrom($this->shipping_from)->timezone(site('timezone') ?: 'UTC')->toIso8601String(),
                    'to' => $this->shipping_date->clone()->setTimeFrom($this->shipping_to)->timezone(site('timezone') ?: 'UTC')->toIso8601String()
                ];
            }
        }
        return null;
    }
    /**
     * @return mixed
     */
    public function getNoteCustomerDecodedAttribute()
    {
        if (empty($this->data['note_customer_decoded'])) {
            $this->data['note_customer_decoded'] = htmlspecialchars_decode((string) $this->note_customer, ENT_QUOTES);
        }
        return $this->data['note_customer_decoded'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getStatusColorAttribute($value)
    {
        if (empty($this->data['status_color'])) {
            $this->data['status_color'] = 'red';
            if (in_array($this->status, ['completed', 'paid'])) {
                $this->data['status_color'] = $this->date_archived ? 'grey' : 'green';
            } elseif (in_array($this->status, ['pending', 'abandoned'])) {
                $this->data['status_color'] = 'orange';
            }
        }
        return $this->data['status_color'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setStatusColorAttribute($value)
    {
        $this->data['status_color'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getStatusFulfillmentColorAttribute($value)
    {
        if (empty($this->data['status_fulfillment_color'])) {
            $this->data['status_fulfillment_color'] = $this->status_fulfillment == 'fulfilled' ? 'green' : 'red';
        }
        return $this->data['status_fulfillment_color'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setStatusFulfillmentColorAttribute($value)
    {
        $this->data['status_fulfillment_color'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getRestoreSourceAttribute($value)
    {
        if (empty($this->data['restore_source'])) {
            /** @var OrderMeta $restore_source */
            if (!empty($restore_source = $this->meta()->where('parameter', 'restore_source')->first())) {
                return in_array($restore_source->value, [AbandonedCart::EMAIL_SOURCE, AbandonedCart::MESSENGER_SOURCE]) ? __('order.filter.recovered_from_' . $restore_source->value) : null;
            }
        }
        return $this->data['restore_source'] ?? null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getContentAttribute($value)
    {
        if (empty($this->data['content'])) {
            $content = $this->products->map(function (OrderProduct $product) {
                if ($this->content_key != 'name' && empty($product->getAttribute($this->content_key))) {
                    return $product->name;
                }

                return $product->getAttribute($this->content_key);
            });
            $this->data['content'] = collect($content->all())->filter()->unique()->implode(', ');
        }
        return $this->data['content'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setContentAttribute($value)
    {
        $this->data['content'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getContentKeyAttribute($value)
    {
        if (empty($this->data['content_key'])) {
            if (!empty($this->shipping->provider->manager)) {
                $this->data['content_key'] = $this->shipping->provider->manager->getSetting('order_content', 'name');
            } else {
                $this->data['content_key'] = 'name';
            }
        }
        return $this->data['content_key'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setContentKeyAttribute($value)
    {
        $this->data['content_key'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getCustomerIpCountryAttribute($value)
    {
        if (empty($this->data['customer_ip_country'])) {
            if (!empty($this->customer_geoip['country_iso'])) {
                $this->data['customer_ip_country'] = Country::get(strtoupper((string) $this->customer_geoip['country_iso']));
            }
        }
        return $this->data['customer_ip_country'] ?? null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getStatusForQuantityDecreaseAttribute($value)
    {
        return $this->meta_status_for_quantity_decrease->value ?? static::getOrderStatusDecrementProducts();
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getCustomerFullNameAttribute($value)
    {
        if (empty($this->data['full_name'])) {
            if ($this->customer_first_name || $this->customer_last_name) {
                $this->data['full_name'] = str_replace([
                    '{first_name}',
                    '{last_name}'
                ], [
                    $this->customer_first_name,
                    $this->customer_last_name,
                ], setting('customer_name_display', '{first_name} {last_name}'));
            } elseif ($this->shippingAddress && $this->shippingAddress->full_name) {
                $this->data['full_name'] = $this->shippingAddress->full_name;
            } elseif ($this->shippingAddress && $this->shippingAddress->full_name) {
                $this->data['full_name'] = $this->shippingAddress->full_name;
            } else {
                $this->data['full_name'] = null;
            }
        }
        return $this->data['full_name'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getOrderNumberAttribute($value)
    {
        return match (setting('order_id_display', 'id')) {
            'increment_hash' => $this->increment_hash,
            default => $this->id,
        };
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getShippingDateFormattedAttribute($value)
    {
        if (!Apps::installed(ShippingHoursManager::APP_KEY) || !$this->shipping_date) {
            return;
        }
        return __('sf.shipping_date.delivery', [
            'date' => DateTimeFormat::getFormat($this->shipping_date, '{date}'),
            'from' => DateTimeFormat::getFormat($this->shipping_from, '{time}'),
            'to' => DateTimeFormat::getFormat($this->shipping_to, '{time}'),
        ]);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getTicketBarcodePngAttribute($value)
    {
        return ArrayCache::remember('ticket_barcode.png.' . $this->id, function () {
            if (empty($this->meta_pluck['ticket_barcode'])) {
                return;
            }

            try {
                return DNS1D::getBarcodePNG($this->meta_pluck['ticket_barcode'], 'C39', 3, 60);
            } catch (Throwable) {
            }

            return;
        });
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getTicketBarcodeSvgAttribute($value)
    {
        return ArrayCache::remember('ticket_barcode.svg.' . $this->id, function () {
            if (empty($this->meta_pluck['ticket_barcode'])) {
                return;
            }

            try {
                return DNS1D::getBarcodeSvg($this->meta_pluck['ticket_barcode'], 'C39', 2, 60);
            } catch (Throwable) {
            }

            return;
        });
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getIsFulfilledAttribute($value)
    {
        $digitalProducts = $this->products->where('digital', YesNo::True);
        if ($digitalProducts->isEmpty()) {
            return $this->status_fulfillment == 'fulfilled' || $this->fulfillments->isNotEmpty();
        }
        if ($digitalProducts->count() == $this->products->count()) {
            return true;
        }
        return $this->status_fulfillment == 'fulfilled';
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getReceiptNumberFormattedAttribute($value): ?string
    {
        if ($this->receipt_number) {
            return sprintf('%010d', $this->receipt_number);
        }
        return null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getN18AuditQrStringAttribute($value): string
    {
        return sprintf(
            '%s*%s*%s*%s*%s*%s',
            Apps::setting(AuditManager::APP_KEY, 'uin'),
            $this->id,
            $this->payment->payment_id,
            $this->receipt_date ? $this->receipt_date->format('Y-m-d') : '',
            $this->receipt_date ? $this->receipt_date->format('H:i:s') : '',
            $this->price_total_input
        );
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getN18AuditQrImagePngAttribute($value)
    {
        return DNS2D::getBarcodePng($this->n18_audit_qr_string, 'QRCODE');
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getN18AuditQrImageSvgAttribute($value)
    {
        return DNS2D::getBarcodeSvg($this->n18_audit_qr_string, 'QRCODE');
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getN18AuditQrImageSvgHtmlAttribute($value)
    {
        return str_replace('<?xml version="1.0" standalone="no"?>', '', $this->n18_audit_qr_image_svg);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getReceiptTotalsAttribute($value)
    {
        $totals = $this->totals->where('hide_in_invoice', 0)->keyBy(fn (OrderTotals $total): string => sprintf('%s.%s', $total->group, $total->key))->map(function (OrderTotals $total) {
            if ($total->group == 'subtotal') {
                return [
                    'name' => 'Междинна сума',
                    'price' => $total->price_formatted
                ];
            } elseif ($total->group == 'total') {
                return [
                    'name' => 'Обща сума',
                    'price' => $total->price_formatted
                ];
            } elseif ($total->is_vat) {
                return [
                    'name' => sprintf('%s (%s)', $total->invoice_name, $total->description),
                    'price' => $total->price_formatted
                ];
            } elseif (in_array($total->group, ['discount.before', 'discount.after'])) {
                return [
                    'name' => $total->invoice_name,
                    'price' => $total->price_formatted
                ];
            } elseif (in_array($total->group, ['shipping', 'omniship'])) {
                return [
                    'name' => sprintf('%s (%s)', $total->name, $total->description),
                    'price' => $total->price_formatted
                ];
            }
        })->filter();
        //        $totals = $this->totals->keyBy(function($a) { return sprintf('%s.%s', $a->group, $a->key); });
        return $this->getTotalsSimple()->map(function (CartTotal $total, $key) {
            if (in_array($key, ['subtotal.subtotal']) && !$total->use_for_total_without_vat) {
                return;
            }

            $prefix = null;
            if (str_starts_with($key, 'discount.')) {
                $prefix = __('sf.order.invoice.label.discount');
            } elseif (str_starts_with($key, 'shipping.') || str_starts_with($key, 'omniship.')) {
                $prefix = __('sf.order.invoice.label.shipping');
            } elseif (str_starts_with($key, 'tax.') && !$total->is_vat) {
                $prefix = __('sf.order.invoice.label.tax');
            }

            return (object)[
                'name' => $prefix ? sprintf('%s (%s)', $prefix, $total->name) : $total->name,
                'price' => $total->price_formatted,
                'total' => $key == 'total.total'
            ];
        })->filter();
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getInvoiceDateAttribute($value): ?\Carbon\Carbon
    {
        if (!($date = $this->getAttributeFromArray('invoice_date'))) {
            return null;
        }
        return Carbon::createFromFormat($this->getDateFormat(), $date, 'UTC')->timezone(site('timezone'));
    }
}
