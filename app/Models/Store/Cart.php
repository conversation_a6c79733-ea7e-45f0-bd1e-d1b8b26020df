<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.4.2017 г.
 * Time: 17:10 ч.
 */

namespace App\Models\Store;

use Agent;
use App\Helper\Store\Abstraction\Cart\CartStoreMethods;
use App\Helper\Store\CartMessage;
use App\Helper\Store\CartTotal;
use App\Helper\Store\Contracts\ItemContract;
use App\Helper\Store\Contracts\Modifications\TotalModificationContract;
use App\Helper\Store\Contracts\OrderContract;
use App\Models\Base\AbstractCart;
use App\Traits\Cart\CartAddresses;
use App\Traits\Cart\CartBundlesMethods;
use App\Traits\Cart\CartCrossUpSell;
use App\Traits\Cart\CartCustomer;
use App\Traits\Cart\CartDiscountCode;
use App\Traits\Cart\CartHelpMethods;
use App\Traits\Cart\CartItemsMethods;
use App\Traits\Cart\CartOnDeleting;
use App\Traits\Cart\CartPayment;
use App\Traits\Cart\CartRelations;
use App\Traits\Cart\CartSetAndMerge;
use App\Traits\Cart\CartShipping;
use App\Traits\Cart\CartShippingQuotes;
use App\Traits\Cart\GetSet\CartClearItems;
use App\Traits\Cart\GetSet\CartGeoName;
use App\Traits\Cart\GetSet\CartProducts;
use App\Traits\CartItem\CartItemDiscountPriority;
use App\Traits\DuplicateEntryCheck;
use Exception;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Throwable;

/**
 * @property-read Collection|Meta[] $products
 */
class Cart extends AbstractCart implements OrderContract
{
    use CartItemDiscountPriority;
    use DuplicateEntryCheck;
    use SoftDeletes;
    //new refactoring
    use CartRelations;
    use CartHelpMethods;
    use CartOnDeleting;
    use CartDiscountCode;
    use CartAddresses;
    use CartCustomer;
    use CartShippingQuotes;
    use CartShipping;
    use CartPayment;
    use CartSetAndMerge;
    use CartBundlesMethods;
    use CartItemsMethods;
    use CartCrossUpSell;

    use CartGeoName;
    use CartProducts;
    use CartClearItems;

    //new abstraction for cart & order
    use CartStoreMethods;

    protected $hidden = [
        'payment_json', 'billing_address_json', 'shipping_address_json', 'shipping_json',
    ];

    protected $data = [];

    protected $__modifications = [];

    protected $__messages = [];

    /**
     * Get the current cart instance
     *
     * @return self|null
     * @throws Throwable
     */
    public static function getCurrent(): ?self
    {
        $instance = static::instance();
        return $instance ?: null;
    }

    #[\Override]
    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub

        static::creating(function (Cart $cart): void {
            $cart->fill([
                'device' => Agent::isMobile() || Agent::isTablet() ? 'mobile' : 'desktop'
            ]);
        });
    }

    /**
     * @param $key
     * @return bool
     */
    public function hasItem($key): bool
    {
        return !!$this->getItem($key);
    }

    /**
     * Used in cross sell
     * @param $key
     * @return bool
     * @throws Throwable
     */
    public function hasItemByProductId($key): bool
    {
        $instance = static::instance();
        return $instance ? !!$instance->items->where('product_id', $key)->isNotEmpty() : false;
    }

    /**
     * @param $key
     * @return null|CartItem
     */
    public function getItem($key)
    {
        return $this->items->firstWhere('key', $key);
    }

    /**
     * @param $id
     * @return null|CartItem
     */
    public function getTotalQuantityByVariantId($id)
    {
        return $this->items->where('variant_id', $id)->sum('quantity');
    }

    /**
     * @param $key
     * @return CartItem|null
     */
    public function product($key)
    {
        if ($key instanceof ItemContract) {
            $key = $key->getItemKey();
        }

        return $this->products->get($key);
    }

    /**
     * @param $key
     * @return boolean
     */
    public function hasProduct($key)
    {
        if ($key instanceof ItemContract) {
            $key = $key->getItemKey();
        }

        return $this->clear_items->has($key);
    }

    /**
     * @param $step
     * @param bool $save
     * @return Cart
     * @throws Throwable
     */
    public function setStep($step, $save = true): static
    {
        $this->fill([
            'step' => $step
        ]);

        $clearQuotes = false;
        if (in_array($step, ['authorize', 'shippingAddress'])) {
            $clearQuotes = !!$this->shipping_address_id;
            $this->setShipping(null, false);
        }

        if (in_array($step, ['authorize'])) {
            $this->setShipping(null, false);
            $this->setShippingAddress(null, false);
        }

        if ($clearQuotes) {
            $this->shipping_quotes()->delete();
        }

        if ($save) {
            $this->save();
        }

        return $this;
    }

    /**
     * @return string
     * @throws Throwable
     */
    public function getStep()
    {
        return $this->step ?? 'authorize';
    }

    /**
     * @return Cart
     * @throws Throwable
     */
    public function setCartUpdateStep(): static
    {
        if (activeRoute('cart.update-bulk')) {
            return $this;
        }

        if ($this->checkout && $this->items->isEmpty()) {
            $this->checkout = 0;
        }

        //@todo this must be correct by next logic
        if ($this->has_billing_address && $this->billing_address_id) {
            $this->setShipping(null, false);
            return $this->setStep('billingAddress');
        } elseif ($this->shipping_address_id) {
            $this->setShipping(null, false);
            return $this->setStep('shippingAddress');
        }

        return $this->setStep('authorize');
    }

    /**
     * @return Cart
     */
    public function resetProducts(): static
    {
        $this->unsetRelation('items');
        foreach (['products', 'query_products', 'has_products', 'chosen_address', 'taxes', 'quantity', 'clear_items'] as $key) {
            if (array_key_exists($key, $this->data)) {
                unset($this->data[$key]);
            }
        }

        return $this;
    }

    /**
     * @param $total
     * @param null|string $type
     * @return int|string|null
     * @throws Throwable
     */
    public function total($total, ?string $type = null)
    {
        /** @var CartTotal $total */
        if ($total = Arr::get($this->getTotals(), $total)) {
            switch ($type) {
                case null:
                default:
                    return $total->getPrice();
                case 'input':
                    return $total->getPriceInput();
                case 'formatted':
                    return $total->getPriceFormatted();
            }
        }

        return null;
    }

    /**
     * On user logout remove key for cart
     * @return Cart
     * @throws Exception|Throwable
     */
    public function logout(): static
    {
        //by zino 12.11.2021
        /*if ($this->exists && $this->isLogged()) {
            $this->update(['key' => null]);
        }

        static::storeCartKey(null);*/

        $this->setStep('authorize');

        return $this;
    }

    /**
     * @param $key
     * @param null $value
     * @param bool $touch
     * @return $this
     * @throws Exception
     */
    public function setMeta($key, $value = null, $touch = true): static
    {
        if ($key instanceof Collection) {
            /** @var Collection $key */
            $key = $key->pluck('value', 'parameter')->all();
        }

        static::saveDuplicateEntryCheck(function () use ($key, $value, $touch): void {
            if (is_array($key)) {
                foreach ($key as $k => $v) {
                    $this->setMeta($k, $v, false);
                }
            } else {
                $this->meta()->updateOrCreate([
                    'parameter' => $key
                ], [
                    'value' => $value
                ]);
            }
        });

        if ($touch && $this->relationLoaded('meta')) {
            //$this->touch();
            $this->load('meta');
        }

        return $this;
    }

    /**
     * @param $key
     * @param null $default
     * @return mixed
     */
    public function getMeta($key, $default = null)
    {
        $meta = $this->meta->pluck('value', 'parameter');
        if (is_array($m = $meta->get($key, $default))) {
            $meta = $m;
        } else {
            $meta = unserialize(strval($meta->get($key, $default)));
        }

        return $meta ?: $default;
    }

    public function isRecalculateLocked(): bool
    {
        return false;
    }

    /**
     * @return Collection|TotalModificationContract[]
     */
    public function getModificationTotals(): Collection
    {
        return collect($this->__modifications['totals'] ?? []);
    }

    /**
     * @param \App\Helper\Store\Contracts\Modifications\TotalModificationContract $modification
     * @return mixed
     */
    public function setModificationTotal(TotalModificationContract $modification): OrderContract
    {
        $this->__modifications['totals'][$modification->getKey()] = $modification->setGroup('cart');
        return $this;
    }

    /**
     * @return Collection|TotalModificationContract[]
     */
    public function getMessages(): Collection
    {
        return collect($this->__messages);
    }

    /**
     * @param \App\Helper\Store\CartMessage $message
     * @return mixed
     */
    public function setMessage(CartMessage $message): OrderContract
    {
        $this->__messages[$message->getType() . $message->getMessage()] = $message;
        return $this;
    }

    #[\Override]
    protected function casts(): array
    {
        return [
            'shipping_address_json' => 'json',
            'billing_address_json' => 'json',
            'shipping_json' => 'json',
            'payment_json' => 'json',
            'discount_code_container_json' => 'json',
            'discount_container_code' => 'json',
            'initial' => 'float',
            'installment' => 'float',
            'date_sent' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

}
