<?php

/**
 * Created by ModelGenerator.
 * User: jooorooo <<EMAIL>>
 * Date: 2018
 */

namespace App\Models\Base;

/**
 * Foreign Keys in table: theme_blocks_groups
 * --------- theme_blocks_groups_theme_id_foreign ---------
 * Field: theme_id
 * Reference table: theme
 * Reference field: id
 * Actions: On delete - CASCADE; On update - NO ACTION
 */

use Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;

/**
 * @property integer id
 * @property integer theme_id
 * @property string name
 * @property string group
 * @property integer max_blocks
 * @property \Carbon\Carbon created_at
 * @property \Carbon\Carbon updated_at
 * @method static QueryBuilder|EloquentBuilder|AbstractThemeBlocksGroups whereId($value)
 * @method static QueryBuilder|EloquentBuilder|AbstractThemeBlocksGroups whereThemeId($value)
 * @method static QueryBuilder|EloquentBuilder|AbstractThemeBlocksGroups whereName($value)
 * @method static QueryBuilder|EloquentBuilder|AbstractThemeBlocksGroups whereGroup($value)
 * @method static QueryBuilder|EloquentBuilder|AbstractThemeBlocksGroups whereMaxBlocks($value)
 * @method static QueryBuilder|EloquentBuilder|AbstractThemeBlocksGroups whereCreatedAt($value)
 * @method static QueryBuilder|EloquentBuilder|AbstractThemeBlocksGroups whereUpdatedAt($value)
 *
 * @category   Base Abstract Models
 * @package    App\Models\Base
 * <AUTHOR> <<EMAIL>>
 * @version    Release: 5.7.29
 */
abstract class AbstractThemeBlocksGroups extends Eloquent
{

    use ModelTrait, DbTimezone;

    protected $table = 'theme_blocks_groups';

    protected $fillable = [
        'theme_id', 'name', 'group',
        'max_blocks'
    ];
    protected $guarded = [
        'id'
    ];


}
