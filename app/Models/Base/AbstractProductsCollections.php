<?php

/**
 * Created by ModelGenerator.
 * User: jooorooo <<EMAIL>>
 * Date: 2018
 */

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\Collections\Collections as CollectionsModel;
use App\Traits\UrlHandle;
use App\Models\Setting\UrlHandleHistory;
use App\Traits\SeoFields;
use App\Contracts\SeoFieldContract;

/**
 * @property integer id
 * @property string name
 * @property string url_handle
 * @property string seo_title
 * @property string seo_description
 * @property string default_sort_by
 * @property string image
 * @property integer max_thumb_size
 * @property string operation
 * @property integer manual
 * @property \Carbon\Carbon created_at
 * @property \Carbon\Carbon updated_at
 * @property string url
 * @property mixed mutated
 * @property \App\Models\Collections\Collections array
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Collections\CollectionsRules[] $rules
 * @property-read integer $rules_count
 * @property-read integer $products_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Setting\UrlHandleHistory[] $url_history
 * @property-read integer $url_history_count
 * @property-read \App\Models\System\Storage $storage
 * **** from trait App\Traits\UrlHandle ****
 * @method static void bootUrlHandle()
 * @method bool hasEditHandleField()
 * @method bool enableCreateHistory()
 * @method bool enableDeleteHistory()
 * @method array getReserved()
 * @method string getSeparator()
 * @method string getNameField()
 * @method string getHandleField()
 * @method null|string generateHandle()
 * @method string slug($title)
 * @method static \Illuminate\Database\Eloquent\Model getByUrl($url)
 * @method static \Illuminate\Database\Eloquent\Collection getAllByUrl($url)
 * **** from trait App\Traits\SeoFields ****
 * @method static void bootSeoFields()
 * @method bool hasSeoField($name)
 * @method string getSeoNameField()
 * @method string getSeoDescriptionField()
 * @method  getSeoDescriptionAttribute()
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel whereId($value)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel whereName($value)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel whereUrlHandle($value)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel whereSeoTitle($value)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel whereSeoDescription($value)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel whereDefaultSortBy($value)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel whereImage($value)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel whereMaxThumbSize($value)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel whereOperation($value)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel whereManual($value)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel whereCreatedAt($value)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel whereUpdatedAt($value)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel active()
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel withActiveProductsCount()
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel manual()
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel notManual()
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel whereHasMorph($type, \Closure $callable = null)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel urlHandle($url_handle)
 * @method static QueryBuilder|EloquentBuilder|CollectionsModel fileName($filename)
 * @method static \Illuminate\Support\Collection|CollectionsModel[] getRelationList($where = null, $join = null, \App\Helper\Grid $grid = null)
 *
 * @category   Base Abstract Models
 * @package    App\Models\Base
 * <AUTHOR> <<EMAIL>>
 * @version    Release: 5.7.29
 */
abstract class AbstractProductsCollections extends Eloquent implements SeoFieldContract
{

    use ModelTrait, DbTimezone, UrlHandle, SeoFields;

    protected $url_handle_field = 'url_handle';

    protected $name_field = 'name';

    protected $description_field = 'name';

    protected $_seo_fields = ['seo_title', 'seo_description'];

    protected $table = 'products_collections';

    protected $fillable = [
        'name', 'url_handle', 'seo_title',
        'seo_description', 'default_sort_by', 'max_thumb_size',
        'operation', 'manual'
    ];
    protected $guarded = [
        'id', 'image'
    ];


    /**
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany|\App\Models\Setting\UrlHandleHistory
     */
    public function url_history()
    {
        return $this->morphMany(UrlHandleHistory::class, 'url_history', 'type', 'model_id', $this->getKeyName());
    }


}
