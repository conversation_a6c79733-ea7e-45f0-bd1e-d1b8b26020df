<?php

declare(strict_types=1);

namespace App\Models\Marketing\UpSell;

use App\Common\DateTimeFormat;
use App\Exceptions\Error;
use App\Helper\Format;
use App\Helper\YesNo;
use App\Integration\UpCrossSell\Traits\Colors;
use App\Integration\UpCrossSell\Traits\Cookies;
use App\Integration\UpCrossSell\Traits\Tree;
use App\Models\Base\AbstractUpSell;
use App\Models\Marketing\CrossSell\CrossSell;
use App\Models\Order\Order;
use App\Models\Order\OrderProductUpSell;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Store\CartItem;
use App\Scopes\VariantEnableSell;
use App\Traits\Crudling;
use App\Traits\DateTemplate;
use App\Traits\PlanUsage;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

/**
 * @property Collection|UpSell[]|CrossSell[] $childes
 */
class UpSell extends AbstractUpSell
{
    use Crudling;
    use DateTemplate;
    use Tree;
    use Colors;
    use Cookies;
    use PlanUsage;

    protected $appends = [
        'active_from_formatted', 'active_to_formatted',
        'total', 'total_input', 'total_formatted',
        'offer_price_input', 'offer_price_formatted',
        'trigger_price_input', 'trigger_price_formatted'
    ];

    /**
     * @var array
     */
    protected $data = [];

    /**
     * @var boolean
     */
    protected $formatted = false;

    protected function casts(): array
    {
        return ['active_from' => 'datetime', 'active_to' => 'datetime'];
    }

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    #[\Override]
    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub

        static::saving(function (UpSell $upSell): void {
            if (!$upSell->offer_override_price) {
                $upSell->offer_price = null;
            }

            if (!$upSell->trigger_override_price) {
                $upSell->trigger_price = null;
            }

            if ($upSell->isDirty('trigger_variant_id')) {
                CartItem::whereUpSellTriggerVariantId($upSell->getOriginal('trigger_variant_id'))->update([
                    'up_sell_trigger_variant_id' => null
                ]);
            }
        });
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne|\App\Models\Product\Product
     */
    public function trigger_product()
    {
        return $this->hasOne(Product::class, 'id', 'trigger_product_id')
            ->withHiddenItems();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne|\App\Models\Product\Product
     */
    public function trigger_variant()
    {
        return $this->hasOne(Variant::class, 'id', 'trigger_variant_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne|\App\Models\Product\Product
     */
    public function offer_product()
    {
        return $this->hasOne(Product::class, 'id', 'offer_product_id')
            ->withHiddenItems();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne|\App\Models\Product\Product
     */
    public function offer_variant()
    {
        return $this->hasOne(Variant::class, 'id', 'offer_variant_id');
    }

    /**
     * @param $query
     */
    public function scopeActive($query): void
    {
        /** @var UpSell $query */
        $query->whereStatus(1)->where(function ($query): void {
            /** @var UpSell $query */
            $query->where(function ($query): void {
                /** @var UpSell $query */
                $query->whereNull('active_from')
                    ->orWhere('active_from', '<=', Carbon::now(site('timezone')));
            })->where(function ($query): void {
                /** @var UpSell $query */
                $query->whereNull('active_to')
                    ->orWhere('active_to', '>=', Carbon::now(site('timezone')));
            });
        });
    }

    /**
     * @param $query
     */
    public function scopeWithSalesGenerated($query): void
    {
        $sql = OrderProductUpSell::whereHas('product.order', function ($query): void {
            /** @var Order $query */
            $query->whereIn('status', ['pending', 'paid', 'completed']);
        })->selectRaw('COALESCE(SUM(total * (SELECT quantity FROM orders_products WHERE id = orders_products_up_sell.order_product_id LIMIT 1)),0)')
            ->whereRaw('`up_sell_id` = `up_sell`.`id`');

        /** @var UpSell $query */
        if (!in_array_multiple(['*', 'up_sell.*', '`up_sell`.*'], $query->getQuery()->columns ?: [])) {
            $query->select('*');
        }

        $query->selectSub('IFNULL((' . $sql->toSql() . '), 0)', 'sales_generated')->addBinding($sql->getBindings(), 'select');
    }

    /**
     * @param Builder $query
     */
    public function scopeTotalSales($query): void
    {
        $sql = OrderProductUpSell::whereHas('product.order', function ($query): void {
            /** @var Order $query */
            $query->whereIn('status', ['pending', 'paid', 'completed']);
        })->selectRaw('COUNT(DISTINCT order_product_id)')->whereRaw('`up_sell_id` = `up_sell`.`id`');
        /** @var UpSell $query */
        if (!in_array_multiple(['*', 'up_sell.*', '`up_sell`.*'], $query->getQuery()->columns ?: [])) {
            $query->select('*');
        }

        $query->selectRaw('@total_orders:=(' . $sql->toSql() . ') AS total_sales')
            ->addBinding($sql->getBindings(), 'select');
    }

    /**
     * @param Builder $query
     */
    public function scopeWithTotalSales($query): void
    {
        /** @var UpSell $query */
        $query->totalSales();
    }

    /**
     * @param $query
     */
    public function scopeWithSuccessRate($query): void
    {
        /** @var UpSell $query */
        if ($this->columnInSelect($query, '@total_orders')) {
            $query->selectRaw('IF((@total_orders) > 0 , (100/(`added_to_cart` / @total_orders))*100, 0) AS `success_rate`');
        } else {
            $sql = OrderProductUpSell::whereHas('product.order', function ($query): void {
                /** @var Order $query */
                $query->whereIn('status', ['pending', 'paid', 'completed']);
            })->selectRaw('COUNT(DISTINCT order_product_id)')->whereRaw('`up_sell_id` = `up_sell`.`id`');
            /** @var UpSell $query */
            if (!in_array_multiple(['*', 'up_sell.*', '`up_sell`.*'], $query->getQuery()->columns ?: [])) {
                $query->select('*');
            }

            $query->selectRaw('@total_orders:=(' . $sql->toSql() . ') AS total_orders, IF((@total_orders) > 0 , (100/(`added_to_cart` / @total_orders))*100, 0) AS `success_rate`')
                ->addBinding($sql->getBindings(), 'select');
        }
    }

    /**
     * @param $query
     * @param string $boolean
     */
    public function scopeInStockOfferCheck($query, $boolean = 'and'): void
    {
        $hide_out_of_stock = Product::active()->inStock()->whereHas('variants', function (Builder $query): void {
            $query->where('products_variants.id', \Illuminate\Support\Facades\DB::raw('up_sell.offer_variant_id'));
        })->where('products.id', \Illuminate\Support\Facades\DB::raw('up_sell.offer_product_id'));

        /** @var UpSell $query */
        if (!in_array_multiple(['*', 'up_sell.*', '`up_sell`.*'], $query->getQuery()->columns ?: [])) {
            $query->select('*');
        }

        $query->whereRaw('EXISTS(' . $hide_out_of_stock->toSql() . ')', $hide_out_of_stock->getBindings(), $boolean);
    }

    /**
     * @param $query
     */
    public function scopeWithInStockOffer($query): void
    {
        $hide_out_of_stock = Product::selectRaw('(SELECT COALESCE(SUM(quantity),0) FROM products_variants WHERE item_id = products.id AND products_variants.id = up_sell.offer_variant_id AND price IS NOT NULL LIMIT 1) AS quantity')
            ->where(function ($query): void {
                /** @var Product $query */
                $query->where(function ($query): void {
                    /** @var Product $query */
                    $query->where(function ($query): void {
                        /** @var Product $query */
                        $query->where('products.tracking', YesNo::True)
                            ->where('products.continue_selling', YesNo::False)
                            ->whereHas('variants', function ($variant): void {
                                /** @var Variant $variant */
                                $variant->whereNotNull('price')->whereNotNull('quantity')
                                    ->where('quantity', '>', 0);
                                $variant->withoutGlobalScope(new VariantEnableSell());
                                $variant->whereRaw('products_variants.id = up_sell.offer_variant_id');
                            });
                    })->orWhere('products.tracking', YesNo::False)
                        ->orWhere('products.continue_selling', YesNo::True);
                });
            })->limit(1)->whereRaw('products.id = up_sell.offer_product_id');

        /** @var UpSell $query */
        if (!in_array_multiple(['*', 'up_sell.*', '`up_sell`.*'], $query->getQuery()->columns ?: [])) {
            $query->select('*');
        }

        $query->selectSub('(' . $hide_out_of_stock->toSql() . ') > 0', 'in_stock_offer')->addBinding($hide_out_of_stock->getBindings(), 'select');
    }

    /**
     * @param $query
     * @param string $boolean
     */
    public function scopeOutStockTriggerCheck($query, $boolean = 'and'): void
    {
        $display_trigger_out_of_stock = Product::selectRaw('(SELECT COALESCE(IF(SUM(quantity) > 0, 1, 0),0) FROM products_variants WHERE item_id = products.id AND products_variants.id = up_sell.trigger_variant_id AND price IS NOT NULL LIMIT 1) AS quantity')
            ->where(function ($query): void {
                /** @var Product $query */
                $query->where(function ($query): void {
                    /** @var Product $query */
                    $query->where(function ($query): void {
                        /** @var Product $query */
                        $query->where('products.tracking', YesNo::True)
                            ->where('products.continue_selling', YesNo::False)
                            ->whereHas('variants', function ($variant): void {
                                /** @var Variant $variant */
                                $variant->whereNotNull('price')->whereNotNull('quantity')
                                    ->where('quantity', '=', 0);
                                $variant->withoutGlobalScope(new VariantEnableSell());
                                $variant->whereRaw('products_variants.id = up_sell.trigger_variant_id');
                            });
                    });
                });
            })->limit(1)->whereRaw('products.id = up_sell.trigger_product_id');

        /** @var UpSell $query */
        if (!in_array_multiple(['*', 'up_sell.*', '`up_sell`.*'], $query->getQuery()->columns ?: [])) {
            $query->select('*');
        }

        $query->whereRaw('IF(`display_trigger_out_of_stock`, (' . $display_trigger_out_of_stock->toSql() . '), 0) = 0', $display_trigger_out_of_stock->getBindings(), $boolean);
    }

    /**
     * @return null|UpSell
     */
    public function format(): static
    {
        if (!$this->exists || $this->formatted) {
            return $this;
        }

        $this->setRelation('trigger_product', $this->_format($this->trigger_variant->item, $this->trigger_variant));
        $this->setRelation('offer_product', $this->_format($this->offer_variant->item, $this->offer_variant));

        //        foreach (['offer_title', 'offer_description', 'button_name', 'cancel_button_name'] AS $key) {
        //            $this->setAttribute($key . '_formatted', $this->_replace($this->{$key}, [
        //                '{$only}' => $this->total_formatted,
        //                '{$trigger_product_name}' => $this->trigger_product->name,
        //                '{$trigger_product_price}' => $this->trigger_variant->price_formatted,
        //                '{$offer_product_name}' => $this->offer_product->name,
        //                '{$offer_product_price}' => $this->offer_variant->price_formatted,
        //            ]));
        //        }

        $this->formatted = true;

        return $this;
    }

    /**
     * @return array
     */
    public static function getVarsLegend(): array
    {
        return [
            '{$only}' => __('up_sell.vars.legend.only'),
            '{$trigger_product_name}' => __('up_sell.vars.legend.trigger_product_name'),
            '{$trigger_product_price}' => __('up_sell.vars.legend.trigger_product_price'),
            '{$offer_product_name}' => __('up_sell.vars.legend.offer_product_name'),
            '{$offer_product_price}' => __('up_sell.vars.legend.offer_product_price'),
        ];
    }

    /**
     * @param array $ids
     * @return Collection|UpSell[]
     */
    public static function duplicate(array $ids)
    {
        $new_records = collect();
        static::whereIn('id', $ids)->get()->map(function (UpSell $record) use ($new_records): void {
            $new = static::create(array_merge(Arr::only($record->toArray(), [
                'status', 'trigger_product_id', 'trigger_variant_id', 'offer_product_id',
                'offer_variant_id', 'active_from', 'active_to', 'offer_title', 'offer_description', 'only_additional_cost',
                'match_quantity', 'hide_out_of_stock', 'display_trigger_out_of_stock',
                'event', 'background', 'text_color', 'button_name', 'button_background', 'button_text_color',
                'cancel_button_name', 'popup_effect', 'trigger_quantity', 'trigger_price',
                'trigger_override_price', 'offer_quantity', 'offer_price',
                'offer_override_price', 'parent', 'group'
            ]), ['name' => sprintf('%s - Copy', $record->name)]));

            $new_records->push($new);
        });

        return $new_records;
    }

    /**
     * @param bool $value
     * @return $this
     */
    public function setSandBox($value): static
    {
        $this->update([
            $this->getSandBoxFieldName() => (int)$value
        ]);

        if ($this->child_yes) {
            $this->child_yes->setSandBox($value);
        }

        if ($this->child_no) {
            $this->child_no->setSandBox($value);
        }

        return $this;
    }

    /**
     * @param Product $product
     * @param Variant $variant
     * @return Product
     */
    private function _format(Product $product, Variant $variant): Product
    {
        if (!$variant->detailed_discount) {
            $product->setAttribute('product_to_discount_id', null);
        }

        if ($variant->detailed_discount) {
            $variant->setAttribute('discounted', $variant->detailed_discount->discount);
            $variant->setAttribute('price_discounted', $variant->detailed_discount->price);
            $variant->setAttribute('price_discounted_formatted', Format::money($variant->getAttribute('price_discounted')));
            $variant->setAttribute('price_discounted_input', Format::moneyInput($variant->getAttribute('price_discounted')));

            $variant->setAttribute('price_saved', $variant->price - $variant->getAttribute('price_discounted'));
            $variant->setAttribute('price_saved_formatted', Format::money($variant->getAttribute('price_saved')));
            $variant->setAttribute('price_saved_input', Format::moneyInput($variant->getAttribute('price_saved')));
        }

        return $product;
    }

    /**
     * @param string $string
     * @param array $replace
     * @return string|array
     */
    private function _replace($string, array $replace): string|array
    {
        $string = str_replace(array_keys($replace), $replace, (string)$string);
        return $string;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getActiveFromFormattedAttribute($value)
    {
        if (!isset($this->data['active_from_formatted'])) {
            $this->data['active_from_formatted'] = $this->active_from ? $this->active_from->format($this->date_template) : null;
        }
        return $this->data['active_from_formatted'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setActiveFromFormattedAttribute($value)
    {
        $this->data['active_from_formatted'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getActiveToFormattedAttribute($value)
    {
        if (!isset($this->data['active_to_formatted'])) {
            $this->data['active_to_formatted'] = $this->active_to ? $this->active_to->format($this->date_template) : null;
        }
        return $this->data['active_to_formatted'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setActiveToFormattedAttribute($value)
    {
        $this->data['active_to_formatted'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setActiveFromAttribute($value): void
    {
        $this->attributes['active_from'] = !empty($value) ? Carbon::createFromFormat(DateTimeFormat::$date_formats[setting('date_format')]['format'], $value) : null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setActiveToAttribute($value): void
    {
        $this->attributes['active_to'] = !empty($value) ? Carbon::createFromFormat(DateTimeFormat::$date_formats[setting('date_format')]['format'], $value) : null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getTriggerProductJsonAttribute($value)
    {
        if ($this->trigger_product_id && $this->trigger_product) {
            return json_encode([
                'id' => $this->trigger_product->id,
                'name' => $this->trigger_product->name,
                'total_variants' => $this->trigger_product->total_variants,
                'default_variant_id' => $this->trigger_product->default_variant_id
            ]);
        }
        return null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getTriggerVariantJsonAttribute($value)
    {
        $result = [];
        if ($this->trigger_variant_id && $this->trigger_variant) {
            for ($i = 1; $i <= $this->trigger_product->total_variants; $i++) {
                $result[$this->trigger_product->getAttribute('p' . $i)] = $this->trigger_variant->getAttribute('v' . $i);
            }
        }
        return json_encode($result);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getOfferProductJsonAttribute($value)
    {
        if ($this->offer_product_id && $this->offer_product) {
            return json_encode([
                'id' => $this->offer_product->id,
                'name' => $this->offer_product->name,
                'total_variants' => $this->offer_product->total_variants,
                'default_variant_id' => $this->offer_product->default_variant_id
            ]);
        }
        return null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getOfferVariantJsonAttribute($value)
    {
        $result = [];
        if ($this->offer_variant_id && $this->offer_variant) {
            for ($i = 1; $i <= $this->offer_product->total_variants; $i++) {
                $result[$this->offer_product->getAttribute('p' . $i)] = $this->offer_variant->getAttribute('v' . $i);
            }
        }
        return json_encode($result);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setTriggerVariantIdAttribute($value): void
    {
        $this->attributes['trigger_variant_id'] = !empty($value) ? $value : null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setOfferVariantIdAttribute($value): void
    {
        $this->attributes['offer_variant_id'] = !empty($value) ? $value : null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getTotalAttribute($value)
    {
        if (!isset($this->data['only']) && $this->offer_product && $this->trigger_product && $this->offer_variant && $this->trigger_variant) {
            $trigger_price = $this->trigger_variant->detailed_discount ? $this->trigger_variant->getAttribute('price_discounted') : $this->trigger_product->price_from;
            if ($this->offer_override_price) {
                $offer_price = $this->offer_price;
            } else {
                $offer_price = $this->offer_variant->detailed_discount ? $this->offer_product->getAttribute('price_discounted') : $this->offer_product->price_from;
            }

            $this->data['only'] = $offer_price - $trigger_price;
        }
        return $this->data['only'] ?? 0;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setTotalAttribute($value)
    {
        $this->data['only'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getTotalInputAttribute($value): string|int
    {
        return Format::moneyInput($this->total);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getTotalFormattedAttribute($value): string
    {
        return Format::money($this->total);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getOfferPriceInputAttribute($value): string|int
    {
        if ($this->offer_override_price) {
            return Format::moneyInput($this->offer_price);
        }
        return 0;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getOfferPriceFormattedAttribute($value): string
    {
        if ($this->offer_override_price) {
            return Format::money($this->offer_price);
        }
        return Format::money(0);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setOfferPriceAttribute($value): void
    {
        $this->attributes['offer_price'] = Format::toIntegerPrice($value ?: 0);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getTriggerPriceInputAttribute($value): string|int
    {
        if ($this->offer_override_price) {
            return Format::moneyInput($this->trigger_price);
        }
        return 0;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getTriggerPriceFormattedAttribute($value): string
    {
        if ($this->offer_override_price) {
            return Format::money($this->trigger_price);
        }
        return Format::money(0);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setTriggerPriceAttribute($value): void
    {
        $this->attributes['trigger_price'] = Format::toIntegerPrice($value ?: 0);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getOfferTitleFormattedAttribute($value): string|array
    {
        return $this->_replace($this->offer_title, [
            '{$only}' => $this->total_formatted,
            '{$trigger_product_name}' => $this->trigger_product->name,
            '{$trigger_product_price}' => $this->trigger_variant->price_formatted,
            '{$offer_product_name}' => $this->offer_product->name,
            '{$offer_product_price}' => $this->offer_variant->price_formatted,
        ]);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getOfferDescriptionFormattedAttribute($value): string|array
    {
        return $this->_replace($this->offer_description, [
            '{$only}' => $this->total_formatted,
            '{$trigger_product_name}' => $this->trigger_product->name,
            '{$trigger_product_price}' => $this->trigger_variant->price_formatted,
            '{$offer_product_name}' => $this->offer_product->name,
            '{$offer_product_price}' => $this->offer_variant->price_formatted,
        ]);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getButtonNameFormattedAttribute($value): string|array
    {
        return $this->_replace($this->button_name, [
            '{$only}' => $this->total_formatted,
            '{$trigger_product_name}' => $this->trigger_product->name,
            '{$trigger_product_price}' => $this->trigger_variant->price_formatted,
            '{$offer_product_name}' => $this->offer_product->name,
            '{$offer_product_price}' => $this->offer_variant->price_formatted,
        ]);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getCancelButtonNameFormattedAttribute($value): string|array
    {
        return $this->_replace($this->cancel_button_name, [
            '{$only}' => $this->total_formatted,
            '{$trigger_product_name}' => $this->trigger_product->name,
            '{$trigger_product_price}' => $this->trigger_variant->price_formatted,
            '{$offer_product_name}' => $this->offer_product->name,
            '{$offer_product_price}' => $this->offer_variant->price_formatted,
        ]);
    }

}
