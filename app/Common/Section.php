<?php

declare(strict_types=1);

namespace App\Common;

use Illuminate\Support\Arr;
use Illuminate\Support\Str;

/**
 * Class Section
 *
 * Class for handling site sections links.
 * Creates links by template files in themes.
 *
 * @package   App\Common
 * @category  common
 * <AUTHOR>
 * @copyright Ev<PERSON><PERSON> <CloudCart ® 2015>
 * @version   1.0
 */
class Section
{
    /**
     * @var array allowed sections
     */
    private $_allowed = [
        'about',
        'contacts',
        'blog',
        'cart',
        'home',
        //'products',
        'vendors',
        'bundles',
    ];

    /**
     * Construct
     *
     * @param array $allowed allowed sections
     */
    public function __construct(array $allowed = [])
    {
        if (\Auth::getCustomerAccess() !== 'guest') {
            $this->_allowed = array_merge($this->_allowed, [
                'account',
                'account/files',
                'account/orders',
                'account/payments',
                'account/address/shipping',
                'account/address/billing',
                'account/wishlist',
                'login',
                'register'
            ]);
        }

        if (!empty($allowed)) {
            $this->_allowed = $allowed;
        }
    }

    /**
     * Get formatted links from template files.
     *
     * @param string|null $filename
     *
     * @return array
     */
    public function get($filename = null)
    {
        $allowed = array_merge($this->_allowed, array_keys(config('sections.allowed', [])));

        $sections = array_combine($allowed, $allowed);
        asort($sections);

        unset($sections['about']);

        if ($filename) {
            return Arr::where($sections, fn($link) => Str::contains($link, $filename));
        }

        return $sections;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function routed()
    {
        return collect($this->get())->map(function ($path): array {
            $route = $path;
            if ($routed = static::toRoute($path)) {
                $route = $routed;
            }

            return [
                'route' => $route,
                'name' => $this->findLabel($route)
            ];
        })->sortBy('route')
            ->pluck('name', 'route');
    }

    /**
     * @param mixed $route
     * @return mixed
     */
    protected function findLabel($route)
    {
        $route = str_replace('/', '.', $route);
        if ($label = config()->get('sections.labels.' . $route)) {
            $route = $label;
        } else {
            $route = 'sections.' . $route;
        }

        return __($route);
    }

    /**
     * Checks if files exist.
     *
     * @param string $file
     *
     * @return bool
     */
    public function exists($file): bool
    {
        return !empty($this->get($file));
    }

    /**
     * Checks if files exist.
     *
     * @param string $file
     *
     * @return bool
     */
    public function existsByRoute($file)
    {
        return $this->routed()->has($file);
    }

    /**
     * Format section link.
     *
     * @param string $file
     *
     * @return bool|string
     */
    public function format(string $file): string|false
    {
        if ($file == 'about') {
            return '/contacts';
        }

        if ($this->exists($file)) {
            return '/' . $file;
        }

        return false;
    }

    /**
     * @param mixed $section
     * @return mixed
     */
    public static function toRoute($section)
    {
        $sections = array_merge([
            'about' => 'contacts',
            'contacts' => 'contacts',
            'blog' => 'blog.list',
            'cart' => 'cart.site',
            'home' => 'site.home',
            'products' => 'products.list',
            'bundles' => 'bundles.list.list',
            'vendors' => 'site.vendors',

            'account' => 'site.account',
            'account/files' => 'site.account.files',
            'account/orders' => 'site.account.orders',
            'account/payments' => 'site.account.payments',
            'account/address/shipping' => 'site.account.address.shipping.list',
            'account/address/billing' => 'site.account.address.billing.list',
            'account/wishlist' => 'site.account.wishlist',
            'login' => 'site.auth.login',
            'register' => 'site.auth.register',
        ], config('sections.allowed', []));

        return $sections[$section] ?? null;
    }

    /**
     * @param $route
     * @return int|string|null
     */
    public static function fromRoute($route)
    {
        $sections = array_merge([
            'about' => 'contacts',
            'contacts' => 'contacts',
            'blog' => 'blog.list',
            'cart' => 'cart.site',
            'home' => 'site.home',
            'products' => 'products.list',
            'bundles' => 'bundles.list.list',
            'vendors' => 'site.vendors',

            'account' => 'site.account',
            'account/files' => 'site.account.files',
            'account/orders' => 'site.account.orders',
            'account/payments' => 'site.account.payments',
            'account/address/shipping' => 'site.account.address.shipping.list',
            'account/address/billing' => 'site.account.address.billing.list',
            'account/wishlist' => 'site.account.wishlist',
            'login' => 'site.auth.login',
            'register' => 'site.auth.register',
        ], config('sections.allowed', []));

        $flipped = array_flip($sections);

        return $flipped[$route] ?? null;
    }

    /**
     * @param mixed $section
     * @return mixed
     */
    public static function toPath($section): int|string|false
    {
        $sections = array_merge([
            'contacts' => 'contacts',
            'blog' => 'blog.list',
            'cart' => 'cart.site',
            'home' => 'site.home',
            'products' => 'products.list',
            'bundles' => 'bundles.list.list',
            'vendors' => 'site.vendors',

            'account' => 'site.account',
            'account/files' => 'site.account.files',
            'account/orders' => 'site.account.orders',
            'account/payments' => 'site.account.payments',
            'account/address/shipping' => 'site.account.address.shipping.list',
            'account/address/billing' => 'site.account.address.billing.list',
            'account/wishlist' => 'site.account.wishlist',
            'login' => 'site.auth.login',
            'register' => 'site.auth.register',
        ], config('sections.allowed', []));

        return array_search($section, $sections, true);
    }
}
