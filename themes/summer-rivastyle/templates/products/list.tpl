{include file="../layout/header.tpl"}

{$widget->setSeo("products")}
<!-- BEGIN: content -->

<div class="_content">
	<div class="_breadcrumb-container _section-separator">
		<div class="container">
			<div class="row">
				<div class="col-sm-12">
					{include file="widgets/common/breadcrumbs.tpl" breadcrumbs=W::productsListing()->getBreadcrumbs() active=null}
				</div>
			</div>
		</div>
	</div>

	<div class="container">
		<div class="row">
			<div class="col-md-3">
				{include file="./list-sidebar.tpl" categories=true}
			</div>

			<div class="js-products-box col-md-9">
				{include file="./list-titles.tpl"}
				{include file="./list-filters.tpl"}

				<div class="js-product-list">
					<div class="js-loading-products loader-container hide">
						<span class="loader">
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
						</span>
					</div>

					<div class="_products-list-main js-products-container js-empty-on-ajax">
						{include file="./../../widgets/product/list.tpl" utilities=W::utilities() products=W::productsListing()->getProducts() widget_products=$widget->products custom_labels=true product_bar=true image_size='600x600' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
						{include file="widgets/common/pagging-new.tpl" page=W::filters()->getPage() pages=W::productsListing()->getPages() link=W::productsListing()->getLink() elements=W::productsListing()->getPaginator() ajax_link=W::productsListing()->getPaggingLink()}
					</div>
				</div>

				{include file="./list-info.tpl"}
			</div>
		</div>
	</div>
	
	<!-- LOAD: seo -->
	{include file="widgets/common/microdata/breadcrumb.tpl" breadcrumbs=W::productsListing()->getBreadcrumbs() active=null}


</div><!--// END: content -->
{include file="../layout/footer.tpl"}