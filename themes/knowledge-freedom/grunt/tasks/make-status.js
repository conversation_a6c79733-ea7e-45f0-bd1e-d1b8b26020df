var path = require('../core/_PATH');
var file = require('../core/_FILE');
var data = require('../../config/grunt.js');

module.exports = function (grunt) {
    grunt.task.registerTask('make-status', function () {
        if (grunt.file.exists(path.pub_styles + '/' + file.styles_vendors)) {
            grunt.log.writeln(data.theme.name + " is in development mode");
        } else {
            grunt.log.writeln(data.theme.name + " is in production mode");
        }
    });
};
