module.exports = function (grunt) {
    grunt.registerTask('make-install', [
        'modernizr_builder',
        'json_generator:bowerrc',
        'json_generator:bower',
        'shell:bowerInstall',
		'mkdir:themeDir',
		'replace:themejson',
        'less',
        'googlefonts',
        'embedfont',
        'copy:vendorsFonts',
        'sprite',
        'svg_sprite',
        'copy:svgSprite',
        'copy:images',
		'copy:imagesDefaults',
		'copy:sitecpImages',
		'copy:gateImages',
		'copy:gateImagesSmaller',
        'concat:vendors',
        'concat:custom',
		'template',
        'make-server'
    ]);
};
