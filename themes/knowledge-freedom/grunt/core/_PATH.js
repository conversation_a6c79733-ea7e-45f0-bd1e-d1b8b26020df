var dir = require('./_DIR');
var data = require('../../config/grunt.js');

module.exports = {

	/*=============================================================================*\
		PRIVATE
	\*=============================================================================*/

	//src:						dir.assets + '/' + dir.private,
	src_fonts:					dir.assets + '/' + dir.fonts,
	src_images:					dir.assets + '/' + dir.images,
	src_images_gif_sprite:		dir.assets + '/' + dir.images + '/' + dir.gif + '/' + dir.sprite,
	src_images_jpg_sprite:		dir.assets + '/' + dir.images + '/' + dir.jpg + '/' + dir.sprite,
	src_images_png_sprite:		dir.assets + '/' + dir.images + '/' + dir.png + '/' + dir.sprite,
	src_images_svg_sprite:		dir.assets + '/' + dir.images + '/' + dir.svg + '/' + dir.sprite,
	src_scripts:				dir.assets + '/' + dir.scripts,
	src_scripts_libs:			dir.assets + '/' + dir.scripts + '/' + dir.libs,
	src_styles:					dir.assets + '/' + dir.styles,
	src_vendors:				dir.assets + '/' + dir.vendors,
	src_vendors_modernizr:		dir.assets + '/' + dir.vendors + '/' + dir.modernizr,
	src_cdn_images_defaults:	dir.base + '/' + dir.assets + '/' + dir.themes + '/' + dir.themex + '/' + dir.img + '/' + dir.defaults,

	/*=============================================================================*\
	    PUBLIC
	\*=============================================================================*/

	pub:						dir.base + '/' + dir.assets + '/' + dir.themes + '/' + data.theme.name,
	pub_fonts:					dir.base + '/' + dir.assets + '/' + dir.themes + '/' + data.theme.name + '/' + dir.fonts,
	pub_fonts_embed:			dir.base + '/' + dir.assets + '/' + dir.themes + '/' + data.theme.name + '/' + dir.fonts + '/' + dir.embed,
    pub_fonts_external:			dir.base + '/' + dir.assets + '/' + dir.themes + '/' + data.theme.name + '/' + dir.fonts + '/' + dir.external,
    pub_fonts_google:			dir.base + '/' + dir.assets + '/' + dir.themes + '/' + data.theme.name + '/' + dir.fonts + '/' + dir.google,
	pub_images:					dir.base + '/' + dir.assets + '/' + dir.themes + '/' + data.theme.name + '/' + dir.img,
	pub_images_sprite:			dir.base + '/' + dir.assets + '/' + dir.themes + '/' + data.theme.name + '/' + dir.img + '/' + dir.sprite,
	pub_scripts:				dir.base + '/' + dir.assets + '/' + dir.themes + '/' + data.theme.name + '/' + dir.js,
	pub_styles:					dir.base + '/' + dir.assets + '/' + dir.themes + '/' + data.theme.name + '/' + dir.css,

	pub_cdn_styles:				dir.themes + '/' + data.theme.name + '/' + dir.css,
	pub_cdn_scripts:			dir.themes + '/' + data.theme.name + '/' + dir.js,
	pub_cdn_images:				dir.themes + '/' + data.theme.name + '/' + dir.img,
	pub_cdn_images_sprite:		dir.themes + '/' + data.theme.name + '/' + dir.img + '/' + dir.sprite,
	pub_cdn_images_defaults:	dir.base + '/' + dir.assets + '/' + dir.themes + '/' + data.theme.name + '/' + dir.img + '/' + dir.defaults,

	/*=============================================================================*\
		PATTERNS
	\*=============================================================================*/

	patterns:					dir.grunt + '/' + dir.patterns,

	/*=============================================================================*\
	    TEMPLATES
	\*=============================================================================*/

	templates:					dir.templates,
    templates_layout:		    dir.templates + '/' + dir.layout,
	templates_layout_src:		dir.templates + '/' + dir.layout + '/' + dir.src,

	/*=============================================================================*\
		CONFIG
	\*=============================================================================*/

	config:						dir.config,

	/*=============================================================================*\
		TRANSLATIONS
	\*=============================================================================*/

	src_translations:				dir.base + '/' + dir.builder + '/' + dir.protected + '/' + dir.i18n_front + '/' + dir.defaults + '/' + dir.themex,
	pub_translations:				dir.base + '/' + dir.builder + '/' + dir.protected + '/' + dir.i18n_front + '/' + dir.defaults + '/' + data.theme.name,

	/*=============================================================================*\
		SITECP
	\*=============================================================================*/

	src_sitecp_images:				dir.base + '/' + dir.assets + '/' + dir.sitecp + '/' + dir.img + '/' + dir.templates + '/' + dir.themex,
	pub_sitecp_images:				dir.base + '/' + dir.assets + '/' + dir.sitecp + '/' + dir.img + '/' + dir.templates + '/' + data.theme.name,

	/*=============================================================================*\
		GATE
	\*=============================================================================*/

	src_gate_images:				dir.base + '/' + dir.assets + '/' + dir.gate + '/' + dir.images + '/' + dir.themes,
	pub_gate_images:				dir.base + '/' + dir.assets + '/' + dir.gate + '/' + dir.images + '/' + dir.themes,
	src_gate_images_smaller:		dir.base + '/' + dir.assets + '/' + dir.gate + '/' + dir.images + '/' + dir.themes + '/' + dir.smaller,
	pub_gate_images_smaller:		dir.base + '/' + dir.assets + '/' + dir.gate + '/' + dir.images + '/' + dir.themes + '/' + dir.smaller

};