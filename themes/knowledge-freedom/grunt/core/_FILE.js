module.exports = {

	/*=============================================================================*\
	    FONTS
	\*=============================================================================*/

	fonts_all:					'*.{otf,eot,svg,ttf,woff,woff2}',

	/*=============================================================================*\
	    IMAGES
	\*=============================================================================*/

	images_all:					'*.{gif,jpg,png,svg}',
	images_raster:				'*.{gif,jpg,png}',
	images_gif:					'*.gif',
	images_gif_retina:			'*@2x.gif',
	images_sprite_gif:			'sprite.gif',
	images_sprite_gif_retina:	'<EMAIL>',
	images_jpg:					'*.jpg',
	images_jpg_retina:			'*@2x.jpg',
	images_sprite_jpg:			'sprite.jpg',
	images_sprite_jpg_retina:	'<EMAIL>',
	images_png:					'*.png',
	images_png_retina:			'*@2x.png',
	images_sprite_png:			'sprite.png',
	images_sprite_png_retina:	'<EMAIL>',
	images_svg:					'*.svg',
	images_sprite_svg:			'sprite.svg',

	/*=============================================================================*\
	    SCRIPTS
	\*=============================================================================*/

	scripts_js:					'*.js',
	scripts:					'scripts.js',
	scripts_min:				'scripts.min.js',
	scripts_custom:				'scripts-custom.js',
	scripts_vendors:			'scripts-vendors.js',
	scripts_modernizr:			'modernizr.js',
	scripts_global:				'global.js',

	/*=============================================================================*\
	    STYLES
	\*=============================================================================*/

	styles_css:					'*.css',
	styles_less:				'*.less',
	styles:						'styles.css',
	styles_min:					'styles.min.css',
	styles_rtl:					'styles-rtl.css',
	styles_rtl_min:				'styles-rtl.min.css',
    styles_tidy:				'styles.tidy.css',
	styles_custom:				'styles-custom.css',
	styles_custom_rtl:			'styles-custom-rtl.css',
	styles_vendors:				'styles-vendors.css',
	styles_vendors_rtl:			'styles-vendors-rtl.css',
	styles_sprite_gif:			'styles-sprite-gif.css',
	styles_sprite_png:			'styles-sprite-png.css',
	styles_sprite_jpg:			'styles-sprite-jpg.css',
	styles_sprite_svg:			'styles-sprite-svg.css',
	styles_fonts_google:		'google-fonts.css',
	styles_fonts_embed:			'embed-fonts.css',
	styles_less_import:			'import.less',

	/*=============================================================================*\
	    PATTERNS
	\*=============================================================================*/

	ptn_csslint:				'.csslintrc',
	ptn_template_header_assets:	'ptn.template-header-assets.tpl',
	ptn_template_footer_assets:	'ptn.template-footer-assets.tpl',
	ptn_modernizr:				'ptn.modernizr.json',
	ptn_sprite_gif:				'ptn.sprite-gif.hbs',
	ptn_sprite_png:				'ptn.sprite-png.hbs',
	ptn_sprite_jpg:				'ptn.sprite-jpg.hbs',
	ptn_sprite_svg:				'ptn.sprite-svg.hbs',

	/*=============================================================================*\
	    TEMPLATES
	\*=============================================================================*/

	templates_all:				'*.{html,php,phtml,tpl}',
	templates_tpl:				'*.tpl',
	templates_header_assets:	'header_assets.tpl',
	templates_footer_assets:	'footer_assets.tpl',

	/*=============================================================================*\
		CONFIG
	\*=============================================================================*/

	config_grunt:				'grunt.js',
	config_theme:				'theme.json',

	/*=============================================================================*\
	    EXTENSIONS
	\*=============================================================================*/

	ext_tpl:					'.tpl',
	ext_png:					'.png'
};
