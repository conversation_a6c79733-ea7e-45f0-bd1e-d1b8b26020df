/*=============================================================================*\
    FOOTER
\*=============================================================================*/

._footer-title {
    ._h4();
    display: block;
    border-bottom: 1px solid;
    border-color: @color-footer-borders; /* theme */
    padding-bottom: 11px;
    margin-bottom: 14px;
    font-weight: 900;
    line-height: @line-height-base;
    color: @color-footer-titles; /* theme */
    position: relative;

    &:before {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 50px;
        border-bottom: 1px solid;
        border-color: @color-footer-highlight; /* theme */
    }
}

._footer {
    color: @color-footer-text;
    background-color: @color-footer-background; /* theme */
    border-width: 1px 0;
    border-style: solid;
    border-color: @color-footer-borders; /* theme */
}

._footer-boxes {
    background-color: @color-footer-background; /* theme */
	color: @color-footer-text; /* theme */
    padding: 50px 0;
    margin: 0 -15px;

    ._text,
    ._newsletter,
    ._navigation-footer {
        float: left;
        width: 25%;
        padding: 0 15px;
    }

    ._navigation-footer {
        width: 100%;
    }

    ._text {
        float: right;

        h6 {
            ._footer-title();
        }

        a {
            color: @color-footer-text; /* theme */

            @media @hover {
                &:hover {
                    color: @color-footer-highlight; /* theme */
                    text-decoration: none;
                }
            }
        }
    }

    ._newsletter,
    ._text {
        &:first-child {
            + ._navigation-footer {
                width: 75%;

                ._navigation-footer-list-item {
                    width: 33.33%;

                    &:nth-child(4n+1) {
                        clear: none;
                    }

                    &:nth-child(3n+1) {
                        clear: both;
                    }
                }

                @media (min-width: 992px) {
                    &._navigation-footer-max-cols-5 {
                        li._navigation-footer-list-item {
                            width: 25%;
    
                            &:nth-child(2n+1),
                            &:nth-child(3n+1),
                            &:nth-child(4n+1),
                            &:nth-child(5n+1) {
                                clear: none;
                            }
    
                            &:nth-child(4n+1) {
                                clear: both;
                            }
                        }
                    }
    
                    &._navigation-footer-max-cols-6 {
                        li._navigation-footer-list-item {
                            width: 20%;
    
                            &:nth-child(2n+1),
                            &:nth-child(3n+1),
                            &:nth-child(4n+1),
                            &:nth-child(6n+1) {
                                clear: none;
                            }
    
                            &:nth-child(5n+1) {
                                clear: both;
                            }
                        }
                    }
                }
            }
        }    
    }

    ._newsletter + ._text + ._navigation-footer {
        width: 50%;

        ._navigation-footer-list-item {
            width: 50%;

            &:nth-child(3n+1) {
                clear: none;
            }

            &:nth-child(2n+1) {
                clear: both;
            }
        }

        @media (min-width: 992px) {
            &._navigation-footer-max-cols-5 {
                li._navigation-footer-list-item {
                    width: 33.33%;

                    &:nth-child(2n+1),
                    &:nth-child(4n+1),
                    &:nth-child(5n+1) {
                        clear: none;
                    }

                    &:nth-child(3n+1) {
                        clear: both;
                    }
                }
            }

            &._navigation-footer-max-cols-6 {
                li._navigation-footer-list-item {
                    width: 25%;

                    &:nth-child(2n+1),
                    &:nth-child(3n+1),
                    &:nth-child(5n+1),
                    &:nth-child(6n+1) {
                        clear: none;
                    }

                    &:nth-child(4n+1) {
                        clear: both;
                    }
                }
            }
        }
    }

    ._newsletter {
        font-size: calc(@font-size-main ~'-' 2px); /* theme */

        ._field {
            background: transparent; /* theme */
            border-color: @color-footer-borders; /* theme */
        }

        ._button {
            color: @color-footer-highlight; /* theme */
        }
    }
}

._footer-legal {
    background-color: @color-footer-bottom-bar-background; /* theme */
    color: @color-footer-bottom-bar-text; /* theme */
    padding: 15px 0;
    font-size: 10px;
    text-transform: uppercase;

    a {
        color: @color-footer-bottom-bar-links; /* theme */

        @media @hover {
            &:hover {
               color: @color-footer-bottom-bar-highlight; /* theme */
            }
        }
    }
}

._footer-legal-inner {
    display: table;
    width: 100%;
}

._footer-legal-left,
._footer-legal-right {
    display: table-cell;
    vertical-align: middle;
    width: 50%;
}

._copyright,
._powered {
    display: inline-block;
    margin-right: 15px;
}

._footer-legal-right {
    text-align: right;
}

@import "footers/footer-two";

._to-top {
    background-color: @color-button-thertiary-background; /* theme */
    border: 1px solid;
    border-color: @color-button-thertiary-borders; /* theme */
    width: 50px;
    height: 50px;
    line-height: 48px;
    color: @color-button-thertiary-text; /* theme */
    text-align: center;
    font-size: 24px;
    position: fixed;
    bottom: 90px;
    right: 90px;
    z-index: @z-to-top;
    transition: .2s;
    opacity: 0;
    visibility: hidden;

    &:focus {
        background-color: @color-button-thertiary-background; /* theme */
        border-color: @color-button-thertiary-borders; /* theme */
        color: @color-button-thertiary-text; /* theme */
    }

    @media @hover {
        &:hover {
            background-color: @color-button-thertiary-background-hover; /* theme */
            border-color: @color-button-thertiary-borders-hover; /* theme */
            color: @color-button-thertiary-text-hover; /* theme */
        }
    }

    &.open {
        opacity: 1;
        visibility: visible;
    }

    &:before {
        content:"\f062";
        font-family: @font-awesome;
    }
}

@media (max-width: @screen-sm-max) {
    ._footer-boxes {
        padding: 30px 0;

        ._text,
        ._newsletter {
            width: 50%;
        }

        ._text {
            float: none;
        }

        ._navigation-footer {
            width: 100%;
        }

        ._newsletter {
            + ._text {
                float: right;
            }
        }

        ._newsletter,
        ._text {
            margin-bottom: 30px;

            &:first-child {
                + ._navigation-footer {
                    width: 100%;

                    ._navigation-footer-list-item {
                        width: 50%;

                        &:nth-child(3n+1) {
                            clear: none;
                        }

                        &:nth-child(2n+1) {
                            clear: both;
                        }
                    }
                }
            }    
        }

        ._newsletter + ._text + ._navigation-footer {
            width: 100%;

            ._navigation-footer-list-item {
                width: 50%;
            }
        }
    }
}

@media (max-width: @screen-xxs-max) {
    ._footer-boxes {
        ._text,
        ._newsletter,
        ._navigation-footer {
            float: none;
        }

        ._newsletter {
            + ._text {
                float: none;
            }
        }

        ._text,
        ._newsletter {
            float: none;
            width: 100%;

            &:first-child {
                + ._navigation-footer {
                    ._navigation-footer-list-item {
                        width: 100%;
                    }
                }
            }    
        }

        ._newsletter + ._text + ._navigation-footer {
            ._navigation-footer-list-item {
                width: 100%;
            }
        }
    }
}