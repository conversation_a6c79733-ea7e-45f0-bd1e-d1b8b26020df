@import "./sidebar/sidebar-one";
@import "./sidebar/sidebar-two";

._sidebar {
  ._navigation-links {

    ul {
      list-style-type: none;
      border: 0;

      ul {
        display: none;
        padding-left: 15px;
        margin-top: 7px;
        text-transform: initial;

        li {
          &:last-child {
            margin-bottom: 0;
          }
        }

        a,
        ._collapse {
          color: @color-second-meta-text; /* theme */

          @media @hover {
            &:hover {
              color: @color-second-highlight; /* theme */
            }
          }
        }
      }
    }

    li {
      border: 0;
      font-weight: 400;
      position: relative;
      line-height: 1.5;
      margin-bottom: @sidebar-list-item-offset;
      display: block;
      padding: 0;
      width: 100%;

      &.item-collapse {
        > a {
          padding-right: @collapse-icon-size;
        }
      }
    }

    a,
    ._collapse {
      display: inline-block;
      color: @color-second-text; /* theme */
      padding: 0;

      @media @hover {
        &:hover {
          color: @color-second-highlight; /* theme */
        }
      }
    }

    .opener {
      > ul {
        display: block;
      }
    }

    .active {
      > a {
        color: @color-second-highlight; /* theme */
      }

      > ._collapse {
        color: @color-second-highlight; /* theme */
      }

      > ._remove {
        position: absolute;
        top: 3px;
        right: 0;
      }
    }

    ._collapse {
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      width: @collapse-icon-size;
      height: @collapse-icon-size;
      line-height: @collapse-icon-size;
      text-align: center;
      position: absolute;
      top: 0;
      right: 0;
      transition: .2s;

      &:before {
        content: '+';
        .centerer(true, true);
      }

      &._collapse-active {
        &:before {
          content: '–';
        }
      }
    }

    ._filter-categories-list {
      &.collapsed {
        ._filter-categories-list-all {
          display: block;
        }

        li {
          display: none;

          &.open {
            display: block;

            li {
              display: block;
            }
          }
        }
      }
    }
  }

  &._sidebar-page {
    background-color: @color-second-background; /* theme */
    padding: 20px;
    margin-bottom: 10px;
    color: @color-second-text; /* theme */
    font-size: calc(@font-size-main ~'-' 1px); /* theme */
    line-height: @line-height-medium;

    &:last-child {
      margin-bottom: 0;
    }

    &.open {
      ._sidebar-box-title {
        border-bottom: 1px solid;
        border-color: @color-second-borders; /* theme */
        padding: 0 0 9px;
        margin: 0 0 15px;
        display: block;

        &:before {
          display: block;
        }

        &.js-sidebar-box-toggle {
          h4 {
            &:after {
              content: "\f106";
            }
          }
        }
      }

      ._sidebar-box-body {
        display: block;
      }
    }
  }
}

._sidebar-button {
  display: none;
  margin-bottom: 10px;
}

._sidebar-main-button {
  display: none;
  margin-bottom: 30px;
}

._sidebar-list {
  margin-bottom: -@sidebar-list-item-offset;

  ._blog-recent-articles,
  ._blog-recent-comments {
    margin-bottom: @sidebar-list-item-offset;
  }
}