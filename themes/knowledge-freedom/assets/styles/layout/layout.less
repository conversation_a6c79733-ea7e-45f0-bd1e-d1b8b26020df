/*=============================================================================*\
    LAYOUT
\*=============================================================================*/

/*  SECTIONS
-------------------------------------------------------------------------------*/

._section-separator {
    margin-bottom: @separator;
}

._section-separator-small {
	margin-bottom: @separator-small;
}

._section-title {
    display: block;
    border-bottom: 1px solid;
    border-color: @color-main-borders; /* theme */
    padding-bottom: 15px;
    margin-bottom: 30px;
    line-height: @line-height-low;
    color: @color-main-titles; /* theme */
    position: relative;
    .uppercase();

    &:before {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 50px;
        border-bottom: 1px solid;
        border-color: @color-main-highlight; /* theme */
    }

    h1,h2,h3,h4,h5,h6 {
    	font-weight: bold;
    }
}

._section-title-inner {
    display: table;
    width: 100%;
}

._section-title-text {
    display: table-cell;
    vertical-align: top;
    padding-right: 20px;
}

._section-title-addon {
    display: table-cell;
    vertical-align: bottom;
    width: 1px;
    white-space: nowrap;
}

._section-title-actions {
    position: absolute;
    top: -2px;
    right: 0;

    ._button {
        vertical-align: middle;
    }

    .swiper-button-disabled {
        opacity: .2;
        cursor: default;
        pointer-events: none;
    }
}

._secondary-title {
	margin-bottom: @separator-small;
	.uppercase();
}

/*  IMAGES ORIENTATION
-------------------------------------------------------------------------------*/

._product-image-thumb-holder,
._product-details-image-thumb,
._cart-product-image-thumb-holder,
._order-details-product-image-thumb,
._cart-compact-products-list-item-image-thumb,
._cart-summary-products-list-item-image-thumb,
._wishlist-compact-products-list-item-image-thumb,
._compare-box-item-image-thumb {
    display: block;
    position: relative;
    padding-bottom: @image-orientation; /* theme */

    img {
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;
        .centerer(true, true);
    }
}