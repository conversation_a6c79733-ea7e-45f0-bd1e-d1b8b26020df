/*=============================================================================*\
    SIDEBAR
\*=============================================================================*/

@sidebar-two-checkbox-size: 14px;
@sidebar-two-checkbox-offset: @sidebar-two-checkbox-size + 10px;
@sidebar-two-checkbox-size-mobile: 22px;
@sidebar-two-checkbox-offset-mobile: @sidebar-two-checkbox-size-mobile + 10px;

._filters-two {
  ._sidebar-list {
    ul {
      list-style-type: none;

      ul {
        margin-top: 2px;
        padding-left: 10px;
      }
    }

    li {
      position: relative;
      margin-bottom: 2px;
    }

    a {
      color: @color-second-meta-text; /* theme */

      @media @hover {
        &:hover {
          color: @color-main-highlight; /* theme */
        }

        &:not(._remove) {
          &:hover {
            &:before {
              border-left-color: @color-main-highlight; /* theme */
            }
          }
        }
      }
    }

    .active {
      > a {
        color: @color-main-highlight; /* theme */
        font-weight: bold;

        &:not(._remove) {
          padding-right: 15px;

          &:before {
            border-left-color: @color-main-highlight; /* theme */
          }
        }
      }

      > ._remove {
        position: absolute;
        top: 3px;
        right: 0;
      }
    }

    ._collapse {
      display: none;
      font-size: 20px;
      font-weight: bold;
      cursor: pointer;
      width: 22px;
      height: 22px;
      color: @color-second-meta-text; /* theme */
      line-height: 22px;
      text-align: center;
      position: absolute;
      top: 0;
      right: 0;

      &:before {
        content: '+';
        .centerer(true, true);
      }

      &._collapse-active {
        &:before {
          content: '–';
        }
      }
    }

    .item-collapse {
      > a {
        &:not(._remove) {
          padding-right: 22px;
        }
      }
    }

    &-arrow {
      a {
        &:not(._remove) {
          display: inline-block;
          position: relative;
          padding-left: 11px;

          &:before {
            content: '';
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
            border-left: 4px solid;
            border-left-color: @color-second-meta-text; /* theme */
            position: absolute;
            top: 10px;
            left: 1px;
          }
        }
      }
    }

    ._filter-categories-list {
      &.collapsed {
        ._filter-categories-list-all {
          display: block;
        }

        li {
          display: none;

          &.open {
            display: block;

            li {
              display: block;
            }
          }
        }
      }
    }
  }

  ._sidebar-box-sort-container {
    display: none;
  }

  ._checkbox {
    width: 100%;
    height: @sidebar-two-checkbox-size;
    padding-left: @sidebar-two-checkbox-offset;
    padding-top: 0;

    .checker {
      input, span {
        width: @sidebar-two-checkbox-size;
        height: @sidebar-two-checkbox-size;
      }

      span {
        &.checked {
          &:before {
            font-size: 8px;
          }
        }
      }
    }
  }

  ._radio {
    width: @sidebar-two-checkbox-size;
    height: @sidebar-two-checkbox-size;
    padding-top: 0;

    .radio {
      input, span {
        width: @sidebar-two-checkbox-size;
        height: @sidebar-two-checkbox-size;
      }
    }
  }

  @media (min-width: 1201px) {
    ._sidebar-products {
      ._sidebar-buttons {
        display: none;
      }

      ._sidebar-box-container {
        float: left;
        margin-right: 30px;
        margin-top: 5px;
        margin-bottom: 5px;
        position: relative;

        &:hover {
          z-index: 2;

          ._sidebar-box {
            display: block;
          }
        }
      }

      ._sidebar-box-title {
        cursor: pointer;
        padding-top: 5px;
        padding-bottom: 5px;

        h5 {
          padding-right: 15px;
          position: relative;
          font-size: 16px; /* theme */
          font-weight: @font-weight-main; /* theme */

          &:after {
            content: "\f107";
            font-family: @font-awesome;
            position: absolute;
            top: 0;
            right: 0;
            line-height: 1;
          }
        }
      }

      ._sidebar-box {
        display: none;
        background-color: @color-main-background; /* theme */
        border: 1px solid;
        border-color: @color-main-borders; /* theme */
        border-radius: 5px;
        padding: 15px;
        position: absolute;
        top: 100%;
        left: -15px;
        width: 560px;
      }

      ._sidebar-box-price-range,
      ._sidebar-box-type {
        width: 300px;
      }

      ._sidebar-list,
      ._filter-type {
        column-count: 3;
        column-gap: 15px;
      }

      ._filter-type {
        column-count: 2;
      }
    }

    ._sidebar-blog {
      ._sidebar-box-container {
        border-width: 0;
        border-bottom-width: 1px;
        border-style: solid;
        border-color: @color-main-borders; /* theme */
        padding-bottom: 20px;
        margin-bottom: 22px;

        &:last-child {
          padding-bottom: 0;
          margin-bottom: 0;
          border-bottom: 0;
        }
      }

      ._sidebar-box-title {
        margin-bottom: 5px;
      }

      ._sidebar-buttons {
        display: none;
      }
    }

    ._sidebar-buttons-blog {
      display: none;
    }
  }

  @media (max-width: @screen-lg-min) {
    ._sidebar {
      font-size: calc(@font-size-main ~'-' 1px); /* theme */

      &-box-container {
        //border-width: 0;
        //border-bottom-width: 1px;
        //border-style: solid;
        //border-color: @color-main-borders; /* theme */
        //padding-bottom: 20px;
        //margin-bottom: 22px;

        &:last-child {
          padding-bottom: 0;
          margin-bottom: 0;
          border-bottom: 0;
        }
      }

      &-list {
        ul {
          ul {
            display: none;
          }
        }

        .opener {
          > ul {
            display: block;
          }
        }

        ._collapse {
          display: block;
        }
      }

      &-list-collapsing,
      &-box {
        &-title {
          margin-bottom: 5px;
        }
      }

      &-products {
        display: none;

        ._sidebar-buttons {
          display: block;
        }

        &.open {
          display: block;
        }

        &-button {
          text-align: right;

          ._button {
            padding: 11px 16px;
          }
        }
      }

      ._sidebar-buttons {
        display: none;
        padding-bottom: 15px;
        margin-bottom: 12px;
        //border-bottom: 1px solid;
        //border-color: @color-main-borders; /* theme */
      }
    }

    ._sidebar-list {
      .active {
        > a {
          font-weight: normal;
        }
      }

      li {
        margin-bottom: 8px;
      }
    }

    ._sidebar-box-container,
    ._filter-category-property {
      background-color: @color-second-background; /* theme */
      padding: 20px;
      margin-bottom: 10px;
      color: @color-second-text; /* theme */
      font-size: calc(@font-size-main ~'-' 1px); /* theme */
      line-height: @line-height-medium;

      &:last-child {
        margin-bottom: 0;
      }

      &.open {
        ._sidebar-box-title,
        ._filter-category-property-title {
          border-bottom: 1px solid;
          border-color: @color-second-borders; /* theme */
          padding: 0 0 9px;
          margin: 0 0 15px;
          display: block;

          &:before {
            display: block;
          }

          &.js-sidebar-box-toggle,
          &.js-filter-category-property-toggle {
            h5 {
              &:after {
                content: "\f106";
              }
            }
          }
        }

        ._sidebar-box-body {
          display: block;
        }

        ._sidebar-box {
          display: block;
        }
      }

      h5 {
        ._h6();
        font-weight: 700;
        line-height: @line-height-low;
        color: @color-second-titles; /* theme */
        .uppercase();
        position: relative;
        width: 100%;

        &:after {
          content:"\f107";
          font-family: @font-awesome;
          color: @color-second-meta-text; /* theme */
          font-size: 18px;
          right: 0;
          .centerer(false, true);
        }
      }

      ._select {
          margin-bottom: 15px;
      }

      ._form {
        ._form-row {
          margin-bottom: 10px;
        }
      }
    }

    ._sidebar-box {
      display: none;
    }

    ._sidebar-box-title,
    ._filter-category-property-title {
      position: relative;

      &:before {
        display: none;
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 50px;
        border-bottom: 1px solid;
        border-color: @color-second-highlight; /* theme */
      }
    }

    ._checkbox {
      width: 100%;
      height: @sidebar-two-checkbox-size-mobile;
      padding-left: @sidebar-two-checkbox-offset-mobile;
      padding-top: 0;
      min-height: 22px;
      position: relative;
      line-height: 1.2;

      .checker {
        input, span {
          width: @sidebar-two-checkbox-size-mobile;
          height: @sidebar-two-checkbox-size-mobile;
        }

        span {
          &.checked {
            &:before {
              font-size: @sidebar-two-checkbox-size;
            }
          }
        }
      }

      +._checkbox {
        margin-top: 10px;
      }
    }

    ._filter-categories-list {
      &-all {
        margin-bottom: 8px;
        text-transform: uppercase;

        &:before {
            content: "\f104";
            font-family: @font-awesome;
            margin-right: 8px;
        }
      }

      >ul{
        >li {
          text-transform: uppercase;
        }
      }

      ul {
        ul {
          margin-top: 7px;
          text-transform: initial;
        }
      }

      li {
        margin-bottom: 8px;
      }
    }

    ._sidebar-list-arrow {
      a {
        &:not(._remove) {
          padding-left: 0;
          &:before {
            content: none;
          }
        }
      }
    }
  }

  @media (max-width: @screen-xs-max) {
    ._sidebar-box-sort-container {
      display: block;
    }
  }

  /*  SIDEBAR BUTTONS
  -------------------------------------------------------------------------------*/

  ._sidebar-buttons {
    &-products {
      float: left;
    }

    &-blog {
      margin-bottom: 15px;
    }

    ._button-close {
      display: none;
    }

    &.open {
      ._button-open {
        display: none;
      }

      ._button-close {
        display: inline-block;
      }
    }
  }
}