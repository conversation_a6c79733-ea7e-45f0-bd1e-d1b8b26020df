/*=============================================================================*\
    SIDEBAR
\*=============================================================================*/

@collapse-icon-size: 18px;
@sidebar-list-item-offset: 8px;

._filters-one,
._sidebar-blog {
  ._sidebar-box {
    background-color: @color-second-background; /* theme */
    padding: 20px;
    margin-bottom: 10px;
    color: @color-second-text; /* theme */
    font-size: calc(@font-size-main ~'-' 1px); /* theme */
    line-height: @line-height-medium;

    &:last-child {
      margin-bottom: 0;
    }

    &.open {
      ._sidebar-box-title {
        border-bottom: 1px solid;
        border-color: @color-second-borders; /* theme */
        padding: 0 0 9px;
        margin: 0 0 15px;
        display: block;

        &:before {
          display: block;
        }

        &.js-sidebar-box-toggle {
          h4 {
            &:after {
              content: "\f106";
            }
          }
        }
      }

      ._sidebar-box-body {
        display: block;
      }
    }
  }

  ._sidebar-box-sort {
    display: none;
  }

  ._sidebar-box-title {
    margin: -20px;
    padding: 20px;
    position: relative;

    &:before {
      display: none;
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      width: 50px;
      border-bottom: 1px solid;
      border-color: @color-second-highlight; /* theme */
    }

    &.js-sidebar-box-toggle {
      cursor: pointer;

      h4 {
        position: relative;

        &:after {
          content: "\f107";
          font-family: @font-awesome;
          color: @color-second-meta-text; /* theme */
          font-size: 18px;
          right: 0;
          .centerer(false, true);
        }
      }
    }

    h4 {
      ._h6();
      font-weight: 900;
      padding-right: 20px;
      line-height: @line-height-low;
      color: @color-second-titles; /* theme */
      .uppercase();
    }
  }

  ._sidebar-box-body {
    display: none;
  }

  ._sidebar-list {
    margin-bottom: -@sidebar-list-item-offset;
    .uppercase();

    ul {
      list-style-type: none;

      ul {
        display: none;
        padding-left: 15px;
        margin-top: 7px;
        text-transform: initial;

        li {
          &:last-child {
            margin-bottom: 0;
          }
        }

        a,
        ._collapse {
          color: @color-second-meta-text; /* theme */

          @media @hover {
            &:hover {
              color: @color-second-highlight; /* theme */
            }
          }
        }
      }
    }

    li {
      position: relative;
      margin-bottom: @sidebar-list-item-offset;

      &.item-collapse {
        > a {
          padding-right: @collapse-icon-size;
        }
      }
    }

    a,
    ._collapse {
      display: inline-block;
      color: @color-second-text; /* theme */

      @media @hover {
        &:hover {
          color: @color-second-highlight; /* theme */
        }
      }
    }

    .opener {
      > ul {
        display: block;
      }
    }

    .active {
      > a {
        color: @color-second-highlight; /* theme */
      }

      > ._collapse {
        color: @color-second-highlight; /* theme */
      }

      > ._remove {
        position: absolute;
        top: 3px;
        right: 0;
      }
    }

    ._collapse {
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      width: @collapse-icon-size;
      height: @collapse-icon-size;
      line-height: @collapse-icon-size;
      text-align: center;
      position: absolute;
      top: 0;
      right: 0;
      transition: .2s;

      &:before {
        content: '+';
        .centerer(true, true);
      }

      &._collapse-active {
        &:before {
          content: '–';
        }
      }
    }

    ._filter-categories-list {
      &.collapsed {
        ._filter-categories-list-all {
          display: block;
        }

        li {
          display: none;

          &.open {
            display: block;

            li {
              display: block;
            }
          }
        }
      }
    }
  }
}

._sidebar-box,
._cart-summary,
._product-details-parameters,
._product-details-sticky {
  ._button {
    .button-colors(@color-second-button-background, @color-second-button-borders, @color-second-button-text);

    @media @hover {
      &:hover {
        .button-colors(@color-second-button-background-hover, @color-second-button-borders-hover, @color-second-button-text-hover);
      }
    }

    &[disabled] {
      .button-colors(@color-button-disabled-background, @color-button-disabled-background, @color-button-disabled-text);
    }
  }

  ._button-secondary {
    .button-colors(@color-second-button-secondary-background, @color-second-button-secondary-borders, @color-second-button-secondary-text);

    @media @hover {
      &:hover {
        .button-colors(@color-second-button-secondary-background-hover, @color-second-button-secondary-borders-hover, @color-second-button-secondary-text-hover);
      }
    }

    &[disabled] {
      .button-colors(@color-button-disabled-background, @color-button-disabled-background, @color-button-disabled-text);
    }
  }
}