._header-variant-four {
  &._header {
    background-color: @color-header-background; /* theme */
    z-index: 3;
  }

  ._topbar {
    background-color: @color-topbar-background; /* theme */
    border-width: 0;
    border-bottom-width: 1px;
    border-style: solid;
    border-color: @color-topbar-borders; /* theme */
    position: relative;
    z-index: 61;

    &-inner {
      display: table;
      width: 100%;
      height: 30px;
    }
  }

  ._utilities {
    display: table-cell;
    vertical-align: middle;
    text-align: right;
    min-width: 150px;
    font-size: 0;
    line-height: 0;
  }

  ._navbar {
    position: relative;
    z-index: 4;
    border-width: 0;
    border-bottom-width: 1px;
    border-style: solid;
    border-color: @color-header-borders; /* theme */
    perspective: 1000px;

    &-inner {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

    [class^="col-"] {
      position: static;
    }
  }

  /*=============================================================================*\
      LOGO
  \*=============================================================================*/

  ._logo {
    align-self: center;
    width: 160px;
    padding: 10px 0;

    &-text {
      ._h1();
    }

    img {
      max-width: 100%;
      max-height: 56px;
      width: auto;
      height: auto;
    }

    @media @hover {
      &:hover {
        ._helper-logo {
          opacity: 1;
          visibility: visible;
        }
      }
    }

    ._helper-logo {
      position: absolute;
      color: @color-main-text; /* theme */
      bottom: 28px;
      left: 100%;
      white-space: nowrap;
      margin-left: 15px;
      visibility: hidden;
      opacity: 0;
      -webkit-transition: .2s;
      transition: .2s;
    }
  }

  /*=============================================================================*\
      SEARCH FORM
  \*=============================================================================*/

  ._search-form {
    padding: 10px 0;
    width: auto;
    .clearfix();

    &-container {
      align-self: center;
      width: 160px;
    }

    ._field {
      border: 1px solid;
      border-radius: 5px;
      float: right;
      transition: .3s;
      height: 38px;

      &:focus {
        border-color: @color-forms-fields-borders; /* theme */
        outline-color: @color-main-highlight; /* theme */
        width: calc(~'100% + 20px');
        width: -webkit-calc(~'100% + 20px');

        + ._button {
          .translate(-20px, 0);
        }
      }
    }

    ._field-icon {
      position: relative;
      vertical-align: middle;
      .field-icon(38px, @color-forms-fields-icons, left);

      ._field {
        padding-right: 15px;
      }
    }

    ._button {
      padding: 0;
      transition: .3s;

      &:hover {
        color: @color-main-highlight; /* theme */
      }

      ._figure-stack-icon {
        display: inline-block;
      }
    }

    .scroll-wrapper {
      left: auto;
      right: 0;
      width: 300px;
    }
  }

  ._user-controls {
    display: inline-block;
    vertical-align: middle;
    text-align: left;

    ul {
      font-size: 0;
      line-height: 0;
      list-style-type: none;
    }

    li {
      display: inline-block;
      font-size: @font-size-main; /* theme */

      + li {
        margin-left: 10px;
      }
    }

    ._figure-stack {
      &-label {
        display: none;
      }

      &-icon {
        top: 0;
      }

      .fa-user {
        &:before {
          content:"\f007";
        }
      }

      .glyphicon-off {
        &:before {
          font-family: @font-awesome;
          content:"\f011";
        }
      }
    }
  }
}