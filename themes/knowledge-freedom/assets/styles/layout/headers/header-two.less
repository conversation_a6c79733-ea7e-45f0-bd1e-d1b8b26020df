/*=============================================================================*\
     HEADER
 \*=============================================================================*/

._header-variant-two {
  &._header {
    background-color: @color-header-background; /* theme */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    transition: .5s ease-in-out;
    z-index: @z-header-fixed;

    + *,
    + ._content,
    + ._promo-bar-container.hidden + ._content {
      margin-top: 91px;
    }
    +._nav-mobile {
      margin-top: 0;
    }
  }

  ._topbar {
    border-width: 0;
    border-bottom-width: 1px;
    border-style: solid;
    position: relative;
    z-index: 5;

    &-inner {
      display: table;
      width: 100%;
      height: 30px;
    }
  }

  ._utilities {
    align-self: center;
    width: 160px;
    text-align: right;
    font-size: 0;
    line-height: 0;
  }

  ._navbar {
    background-color: @color-header-background; /* theme */
    border-width: 0;
    border-bottom-width: 1px;
    border-style: solid;
    border-color: @color-header-borders; /* theme */
    perspective: 1000px;
    position: relative;
    z-index: 4;

    &-inner {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

    [class^="col-"] {
      position: static;
    }
  }

  ._navbar-mobile-button {
    margin: 18px 0;
    margin-left: 20px;
    width: 29px;
    align-self: center;
    display: none;
  }

  ._search-form {
    padding: 0;
  }

  ._field {
    display: block;
    border-radius: 5px;
    height: 38px;
    width: 100%;
  }


  /*=============================================================================*\
    SEARCH FORM
\*=============================================================================*/

  ._search-form-toggler,
  ._search-form-close {
    display: inline-block;
    font-size: 16px;
    line-height: 1;
    background: transparent;
    border: 0;
    vertical-align: middle;
    outline: 0;

    .fa {
      font-size: inherit;
      font-family: "Font Awesome 5 Pro";
      font-weight: 300;
      line-height: 1;
      color: @color-header-icons; /* theme */
    }

    @media @hover {
      &:hover {
        color: @color-header-icons-hover; /* theme */
        .fa {
          color: @color-header-icons-hover; /* theme */
        }
      }
    }
  }

  ._search-form {
    .clearfix();
    width: 100%;

    &-container {
      background-color: @color-header-background; /* theme */
      border: 0;
      border-bottom-width: 1px;
      border-style: solid;
      border-color: @color-header-borders; /* theme */
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 3;
      display: flex;
      align-items: center;
      transition: .5s;

      &.open {
        transform: translate(0, 100%);
      }
    }

    ._field-icon {
      position: relative;
      vertical-align: middle;
      .field-icon(38px, @color-forms-fields-icons, left);
      width: 100%;
    }

    ._button {
      padding: 0;
      color: @color-header-icons; /* theme */

      &:hover {
        color: @color-header-icons-hover; /* theme */
      }

      ._figure-stack-icon {
        display: inline-block;
      }
    }

    .scroll-wrapper {
      left: auto;
      right: 0;
      width: 100%;
    }
  }

  ._search-form-close {
    position: absolute;
    .centerer(false, true);
    right: 15px;
  }

  /*=============================================================================*\
      LOGO
  \*=============================================================================*/

  ._logo {
    align-self: center;
    width: 160px;
    padding: 14px 0;

    &-text {
      ._h1();
    }

    img {
      max-width: 100%;
      max-height: 62px;
      width: auto;
      height: auto;
      vertical-align: middle;
    }

    @media @hover {
      &:hover {
        ._helper-logo {
          opacity: 1;
          visibility: visible;
        }
      }
    }

    ._helper-logo {
      position: absolute;
      color: @color-main-text; /* theme */
      bottom: 28px;
      left: 100%;
      white-space: nowrap;
      margin-left: 15px;
      visibility: hidden;
      opacity: 0;
      -webkit-transition: .2s;
      transition: .2s;
    }
  }
}