._header-variant-three {
  ._topbar {
    background-color: @color-topbar-background; /* theme */
    border-width: 0;
    border-bottom-width: 1px;
    border-style: solid;
    border-color: @color-topbar-borders; /* theme */
    position: relative;
    z-index: 5;

    &-inner {
      display: table;
      width: 100%;
      height: 30px;
    }
  }
  &._header-fixed {
    transform: translate(0, 0);
  }
}

@media (min-width: @screen-desktop-min) {
  ._header-variant-three {
    &._header-variant-one {
      &._header {
        height: 123px;
      }
    }
    ._logo {
      width: 170px;
    }
    ._type-megamenu ._navigation-main-list-item-link,
    ._navigation-main-list-item-link {
      padding: 10px 20px;
    }
  }
  ._promo-bar-container:not('.hidden') {
    +._header-variant-three {
      ~._content {
        padding-top: 140px;
      }
    }
  }
}