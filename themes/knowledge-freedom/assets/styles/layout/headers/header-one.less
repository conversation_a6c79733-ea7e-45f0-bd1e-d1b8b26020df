/*=============================================================================*\
    HEADER
\*=============================================================================*/

@header-icons-size: 30px;
@header-icons-font-size: 18px;
@header-icons-offsets: 10px;
@header-vertical-offsets: 20px;
@header-horizontal-offsets: 36px;

/*  HEADER
-------------------------------------------------------------------------------*/

._header-variant-one {

  &._header {
    background-color: @color-header-background; /* theme */
  }

  ._topbar-inner {
    display: table;
    width: 100%;
    position: relative;
    z-index: @z-topbar;

    ._text {
      width: 670px;
      padding: 0 @header-horizontal-offsets/2;
      font-size: 11px;
      line-height: @line-height-low;
      color: @color-header-text; /* theme */

      .alignleft,
      .alignright {
        margin-top: 0;
        margin-bottom: 0;
      }

      .alignleft {
        margin-right: 12px;
      }

      .alignright {
        margin-left: 12px;
      }
    }

    ._search-form {
      + ._text {
        width: 236px;
      }
    }

    ._text,
    ._utilities,
    ._search-form {
      display: table-cell;
      vertical-align: middle;
    }
  }

  ._utilities {
    text-align: right;
    font-size: 0;
    line-height: 0;
    padding-top: @header-vertical-offsets;
    padding-bottom: @header-vertical-offsets;
    padding-left: @header-horizontal-offsets/2;
  }

  &._header-fixed {
    color: @color-header-text; /* theme */
    border-bottom: 1px solid;
    border-color: @color-header-borders; /* theme */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: @z-header-fixed;
    transform: translate(0, -100%);
    transition: .5s;

    &.open {
      transform: translate(0, 0);
    }

    ._categories-menu {
      height: 50px;
      z-index: 1;

      &.open {
        ._categories-menu-button {
          box-shadow: inset 0 0 0 1px @color-dropdowns-borders;
        }
      }
    }

    ._utilities {
      position: relative;
      z-index: 1;
      padding-top: @header-vertical-offsets/2;
      padding-bottom: @header-vertical-offsets/2;
    }

    ._search-form-wrapper {
      display: block;
      z-index: 0;
      transition: .5s;
      transform: translate(0, -100%);

      &.open {
        transform: translate(0, 0);
      }
    }
  }

  ._header-fixed-outer {
    background-color: @color-header-background; /* theme */
    position: relative;
    z-index: 61;
  }

  ._header-fixed-inner {
    display: table;
    table-layout: fixed;
    width: 100%;
  }

  ._header-fixed-left {
    display: table-cell;
    vertical-align: middle;
    padding-top: @header-vertical-offsets/2;
    padding-bottom: @header-vertical-offsets/2;
  }

  ._header-fixed-left-inner {
    display: flex;
    align-items: center;
    width: 100%;

    ._categories-menu-dropdown {
      margin-top: -1px;
    }
  }

  ._header-fixed-right {
    display: table-cell;
    vertical-align: middle;
  }



  ._user-controls-button,
  ._cart-compact-button,
  ._wishlist-compact-button {
    display: block;
    border-radius: 50%;
    width: @header-icons-size;
    height: @header-icons-size;
    line-height: @header-icons-size;
    text-align: center;
    color: @color-header-icons; /* theme */
    transition: .2s;

    &:focus {
      color: @color-header-icons; /* theme */
    }

    @media @hover {
      &:hover {
        color: @color-header-icons-hover; /* theme */
      }
    }

    ._figure-stack-label {
      display: none;
    }
  }
}