._footer-variant-two {
    &._footer {
        font-size: @font-size-main;
        /* theme */
        padding: 100px 0 64px;

        a {
            &:hover {
                text-decoration: none;
            }
        }
    }

    ._footer-cols {
        .clearfix();
        border-width: 0;
        border-bottom-width: 1px;
        border-style: solid;
        padding-bottom: 19px;
        margin-bottom: 19px;
        border-color: @color-footer-borders;
        /* theme */

        ._text {
            float: right;
            width: 25%;

            h6 {
                color: @color-footer-titles;
                /* theme */
                line-height: @line-height-base;
                font-size: @font-size-main;
                /* theme */
                margin-bottom: 8px;
                .uppercase();
            }

            p {
                margin-bottom: 10px;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            a {

                &:hover {
                    text-decoration: none;
                }
            }

            +._navigation-footer {
                float: left;
                width: 75%;

                ul {
                    ul {
                        li {
                            width: auto;
                        }
                    }
                }

                li {
                    width: 33.33%;

                    &:nth-child(4n+1) {
                        clear: none;
                    }

                    &:nth-child(3n+1) {
                        clear: both;
                    }
                }

                @media (min-width: 992px) {
                    &._navigation-footer-max-cols-5 {
                        ._navigation-footer-list-item {
                            width: 25%;


                            &:nth-child(3n+1) {
                                clear: none;
                            }

                            &:nth-child(4n+1) {
                                clear: both;
                            }

                            &:nth-child(5n+1) {
                                clear: none;
                            }
                        }
                    }

                    &._navigation-footer-max-cols-6 {
                        ._navigation-footer-list-item {
                            width: 20%;

                            &:nth-child(3n+1) {
                                clear: none;
                            }

                            &:nth-child(4n+1) {
                                clear: none;
                            }

                            &:nth-child(5n+1) {
                                clear: both;
                            }

                            &:nth-child(6n+1) {
                                clear: none;
                            }
                        }
                    }
                }
            }
        }
    }

    ._footer-utilities {
        display: table;
        table-layout: fixed;
        width: 100%;
    }

    ._footer-socials,
    ._footer-legal,
    ._footer-providers {
        display: table-cell;
        vertical-align: middle;
    }

    ._social-icons {
        text-align: left;

        i {
            vertical-align: baseline;
        }
    }

    ._copyright,
    ._powered,
    ._opc {
        display: block;
        text-align: center;
        margin-right: 0;
    }

    ._footer-legal {
        text-align: center;
        padding: 0;
        width: 50%;
    }

    ._footer-providers {
        text-align: right;
    }

    ._navigation-footer-list-item-link {
        border-bottom: none;
        padding-bottom: 0;
        margin-bottom: 8px;

        &:before {
            content: none;
        }
    }
}