/*=============================================================================*\
    GRID
\*=============================================================================*/

@grid-offset-horizontal: 15px;
@product-offset-vertical: 0;
@showcase-offset-vertical: 30px;
@banners-offset-vertical: 30px;
@blog-offset-vertical: 30px;

/*	Products and showcase
-------------------------------------------------------------------------------*/

.page-page,
.page-preview {
    ._content {
        &:not(._static-page) {
            padding: 0;
        }
    }
}

._grid-row {
    background-repeat: no-repeat;
}

._grid-section {
    margin-top: @separator;

    &:first-child {
        margin-top: 0;
    }

    ._text {
        ._text-title {
            ._section-title();

            h6 {
                ._h2();
            }
        }

        ._text-title-empty {
            display: none;
        }
    }
}

._products-list {
    margin-top: -@product-offset-vertical;
}

._product {
    margin-top: @product-offset-vertical;
}

._showcase-list {
    margin-top: -@showcase-offset-vertical;
}

._showcase-item {
    margin-top: @showcase-offset-vertical;
}

._banners-list {
    margin-top: -@banners-offset-vertical;
}

._banner {
    margin-top: @banners-offset-vertical;
}

._blog-list-articles {
    margin-top: -@blog-offset-vertical;
}

._blog-list-article {
    margin-top: @blog-offset-vertical;
}

._products-list,
._showcase-list,
._banners-list,
._blog-list-articles {
    .clearfix();
    margin-left: -@grid-offset-horizontal;
    margin-right: -@grid-offset-horizontal;
}

._product {
    padding-top: @grid-offset-horizontal + 5px;
    padding-left: @grid-offset-horizontal + 5px;
    padding-right: @grid-offset-horizontal + 5px;
}

._product-list-main ._product {
    padding-top: @grid-offset-horizontal;
}

._product-list-main ._product,
._showcase-item,
._banner,
._blog-list-article {
    padding-left: @grid-offset-horizontal;
    padding-right: @grid-offset-horizontal;
}

._product,
._showcase-item,
._banner,
._blog-list-article {
    float: left;
    width: 100%;

    &.full {
        width: 100%;

        &:before {
            display: none;
        }
    }

    &.half {
        width: 50%;

        &:nth-child(2n+1) {
            clear: both;

            &:before {
                display: none;
            }
        }
    }

    &.one-third {
        width: 33.33%;

        &:nth-child(3n+1) {
            clear: both;

            &:before {
                display: none;
            }
        }
    }

    &.one-fourth {
        width: 25%;

        &:nth-child(4n+1) {
            clear: both;

            &:before {
                display: none;
            }
        }
    }

    &.one-fifth {
        width: 20%;

        &:nth-child(5n+1) {
            clear: both;

            &:before {
                display: none;
            }
        }
    }

    &.one-sixth {
        width: 16.66%;

        &:nth-child(6n+1) {
            clear: both;

            &:before {
                display: none;
            }
        }
    }
}

@media (max-width: @screen-md-max) {
    .col-md-4 {
        ._product,
        ._showcase-item,
        ._banner,
        ._blog-list-article {
            &.half {
                width: 100%;

                &:nth-child(2n+1) {
                    &:before {
                        display: none;
                    }
                }

            }
        }
    }

    .col-md-8 {
        ._product,
        ._showcase-item,
        ._banner,
        ._blog-list-article {
            &.one-third,
            &.one-fourth,
            &.one-fifth,
            &.one-sixth {
                width: 50%;

                &:nth-child(6n+1),
                &:nth-child(5n+1),
                &:nth-child(4n+1),
                &:nth-child(3n+1) {
                    clear: none;

                    &:before {
                        display: block;
                    }
                }

                &:nth-child(2n+1) {
                    clear: both;

                    &:before {
                        display: none;
                    }
                }
            }
        }
    }

    .col-md-6 {
        ._product,
        ._showcase-item,
        ._banner,
        ._blog-list-article {
            &.one-third,
            &.one-fourth,
            &.one-fifth,
            &.one-sixth {
                width: 50%;

                &:nth-child(6n+1),
                &:nth-child(5n+1),
                &:nth-child(4n+1),
                &:nth-child(3n+1) {
                    clear: none;

                    &:before {
                        display: block;
                    }
                }

                &:nth-child(2n+1) {
                    clear: both;

                    &:before {
                        display: none;
                    }
                }
            }
        }
    }

    .col-md-9,
    .col-md-10 {
        ._product,
        ._showcase-item,
        ._banner,
        ._blog-list-article {
            &.one-fourth {
                width: 50%;

                &:nth-child(4n+1) {
                    clear: none;

                    &:before {
                        display: block;
                    }
                }

                &:nth-child(2n+1) {
                    clear: both;

                    &:before {
                        display: none;
                    }
                }
            }

            &.one-fifth,
            &.one-sixth {
                width: 33.33%;

                &:nth-child(6n+1),
                &:nth-child(5n+1) {
                    clear: none;

                    &:before {
                        display: block;
                    }
                }

                &:nth-child(3n+1) {
                    clear: both;

                    &:before {
                        display: none;
                    }
                }
            }
        }
    }

    .col-md-12 {
        ._product,
        ._showcase-item,
        ._banner,
        ._blog-list-article {
            &.one-fourth {
                width: 50%;

                &:nth-child(4n+1) {
                    clear: both;

                    &:before {
                        display: block;
                    }
                }
                
                &:nth-child(2n+1) {
                    clear: both;

                    &:before {
                        display: none;
                    }
                }
            }
            
            &.one-fifth,
            &.one-sixth {
                width: 33.33%;

                &:nth-child(6n+1),
                &:nth-child(5n+1) {
                    clear: none;

                    &:before {
                        display: block;
                    }
                }

                &:nth-child(3n+1) {
                    clear: both;

                    &:before {
                        display: none;
                    }
                }
            }
        }
    }
}

@media (max-width: @screen-sm-max) {
    ._grid-row {
        [class^="col-md"] {
            + [class^="col-md"] {
                margin-top: @separator;
            }
        }
    }

    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-8 {
        ._product,
        ._showcase-item,
        ._banner,
        ._blog-list-article {
            &.full,
            &.one-third,
            &.one-fifth,
            &.one-sixth {
                width: 33.33%;

                &:nth-child(2n+1) {
                    clear: none ;

                    &:before {
                        display: block;
                    }
                }

                &:nth-child(3n+1) {
                    clear: both;

                    &:before {
                        display: none;
                    }
                }
            }
        }
    }

    .col-md-4 {
        ._product,
        ._showcase-item,
        ._banner,
        ._blog-list-article {
            &.half {
                width: 50%;

                &:nth-child(2n+1) {
                    &:before {
                        display: none;
                    }
                }
            }
        }
    }

    .col-md-6 {
        ._product,
        ._showcase-item,
        ._banner,
        ._blog-list-article {
            &.one-third,
            &.one-fourth,
            &.one-fifth,
            &.one-sixth {
                width: 33.33%;

                &:nth-child(2n+1) {
                    clear: none;

                    &:before {
                        display: block;
                    }
                }

                &:nth-child(3n+1) {
                    clear: both;

                    &:before {
                        display: none;
                    }
                }
            }
        }
    }

    .col-md-8 {
        ._product,
        ._showcase-item,
        ._banner,
        ._blog-list-article {
            &.full {
                width: 100%;
            }
        }
    }
}

@media (max-width: @screen-xs-max) {
    @grid-offset-horizontal-mobile: 7px;

    ._products-list,
    ._showcase-list,
    ._banners-list,
    ._blog-list-articles {
        margin-left: -@grid-offset-horizontal-mobile;
        margin-right: -@grid-offset-horizontal-mobile;
    }

    ._product-list-main ._product,
    ._product,
    ._showcase-item,
    ._banner,
    ._blog-list-article {
        padding-left: @grid-offset-horizontal-mobile;
        padding-right: @grid-offset-horizontal-mobile;
    }

    ._product-list-main ._product,
    ._product {
        padding-top: @grid-offset-horizontal-mobile;
    }

    ._banners-list {
        margin-top: -@grid-offset-horizontal-mobile*2;
    }

    ._banner {
        margin-top: @grid-offset-horizontal-mobile*2;
    }

    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-6,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-12 {
        ._product,
        ._showcase-item,
        ._banner,
        ._blog-list-article {
            &.half,
            &.one-third,
            &.one-fourth,
            &.one-fifth,
            &.one-sixth {
                width: 50%;

                &:nth-child(3n+1),
                &:nth-child(4n+1),
                &:nth-child(5n+1),
                &:nth-child(6n+1) {
                    clear: none;

                    &:before {
                        display: block;
                    }
                }

                &:nth-child(2n+1) {
                    clear: both;

                    &:before {
                        display: none;
                    }
                }
            }
        }
    }

    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-6,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-12 {
        ._product,
        ._showcase-item,
        ._banner,
        ._blog-list-article {
            &.full {
                float: none;
                display: block;
                width: 100%;

                &:nth-child(2n+1),
                &:nth-child(3n+1),
                &:nth-child(4n+1),
                &:nth-child(5n+1),
                &:nth-child(6n+1) {
                    &:before {
                        display: none;
                    }
                }
            }
        }

        ._blog-list-article {
            &.half,
            &.one-third,
            &.one-fourth,
            &.one-fifth,
            &.one-sixth {
                width: 100%;

                &:nth-child(2n+1),
                &:nth-child(3n+1),
                &:nth-child(4n+1),
                &:nth-child(5n+1),
                &:nth-child(6n+1) {
                    &:before {
                        display: none;
                    }
                }
            }
        }
    }
}

@media (max-width: @screen-xxxs-max) {
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-6,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-12 {
        ._product,
        ._showcase-item,
        ._banner {
            float: none;
            display: block;

            &.half,
            &.one-third,
            &.one-fourth,
            &.one-fifth,
            &.one-sixth {
                width: 100%;

                &:nth-child(2n+1),
                &:nth-child(3n+1),
                &:nth-child(4n+1),
                &:nth-child(5n+1),
                &:nth-child(6n+1) {
                    &:before {
                        display: none;
                    }
                }
            }
        }
    }
}