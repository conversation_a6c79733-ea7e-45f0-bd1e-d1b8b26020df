/*=============================================================================*\
    CONTENT
\*=============================================================================*/

._wrapper {
	opacity: 1;
	overflow-x: hidden;

	&._page-loaded {
		opacity: 1;
	}
}

._content {
	padding-top: @separator;
	padding-bottom: @separator;

	> ._breadcrumb-container,
	> ._breadcrumb-wrapper {
		&:first-child {
			margin-top: -@separator;
		}
	}
}