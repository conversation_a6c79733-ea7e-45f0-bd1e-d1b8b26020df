@import "headers/header-one";
@import "headers/header-two";
@import "headers/header-three";
@import "headers/header-four";

@media (min-width: @screen-lg) {
  ._header,
  ._header-fixed {
    .container {
      max-width: 1530px;
      width: 100%;
    }
  }
}

@media (min-width: @screen-desktop) {
  ._header-variant-two {
    ._navbar {
      ._utilities {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        ._user-controls {
          ul {
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
}

/*  NAV MOBILE
-------------------------------------------------------------------------------*/

._nav-mobile-button {
  display: block;
  color: @color-header-icons; /* theme */

  @media @hover {
    &:hover {
      color: @color-header-icons-hover; /* theme */
    }
  }
}

._nav-mobile-button-icon {
  font-size: 18px;
}

._nav-mobile {
  background-color: @color-dropdowns-background; /* theme */
  border-right: 1px solid;
  border-color: @color-dropdowns-borders; /* theme */
  color: @color-dropdowns-text; /* theme */
  width: 250px;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: @z-nav-mobile;
  transition: .2s;
  transform: translate(-100%, 0);

  &.open {
    transform: translate(0, 0);

    ._nav-mobile-close {
      display: block;
    }
  }
}

._nav-mobile-inner {
  height: 100%;
  overflow: auto;
}

._nav-mobile-backdrop {
  background-color: rgba(0, 0, 0, .65);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: @z-nav-mobile - 1;
  opacity: 0;
  visibility: hidden;
  transition: .2s;

  &.open {
    opacity: 1;
    visibility: visible;
  }
}

._nav-mobile-close {
  display: none;
  font-size: 18px;
  color: #bbb; /* theme */
  text-shadow: 0 0 5px rgba(0, 0, 0, 1);
  position: absolute;
  top: 5px;
  right: -25px;

  @media @hover {
    &:hover {
      color: @color-dropdowns-highlight; /* theme */
    }
  }
}

._nav-mobile-categories {
  ._filter-categories-list {
    > ul {
      list-style-type: none;

      > li {
        border-bottom: 1px solid;
        border-color: @color-dropdowns-borders; /* theme */

        > a {
          display: block;
          padding: @menu-vertical-offsets @menu-horizontal-offsets @menu-vertical-offsets-bottom;
          color: @color-dropdowns-text; /* theme */
          font-weight: bold;
          .uppercase();
        }

        > ul {
          background-color: darken(@color-dropdowns-background, 3%);
          border-top: 1px solid;
          border-color: @color-dropdowns-borders; /* theme */
          padding-top: 12px;
          padding-bottom: 10px;

          li {
            position: relative;
          }

          a {
            color: darken(@color-dropdowns-text, 3%);
          }

          ._collapse {
            top: 0;
          }
        }

        ul {
          display: none;
          list-style-type: none;
          padding-left: @menu-horizontal-offsets;
        }
      }
    }

    .active {
      > a,
      > ._collapse {
        color: @color-dropdowns-highlight; /* theme */
      }
    }

    .opener {
      > ul {
        display: block;
      }

      > ._collapse {
        &:before {
          content:"\f068";
        }
      }
    }

    .item-collapse {
      position: relative;

      > a {
        padding-right: 2*@menu-horizontal-offsets;
      }
    }

    ._collapse {
      width: 20px;
      height: @menu-font-size*@menu-line-height;
      line-height: @menu-font-size*@menu-line-height;
      text-align: center;
      position: absolute;
      top: @menu-vertical-offsets;
      right: 10px;
      z-index: 1;

      &:before {
        content:"\f067";
        font-family: @font-awesome;
        font-size: 12px;
      }
    }
  }
}

._nav-mobile-menu {
  @main-collapse-icon-size: @nav-font-size*@nav-line-height+@menu-vertical-offsets;
  @secondary-collapse-icon-size: @nav-font-size*@nav-line-height+@menu-vertical-offsets/2;

  ._navigation {
    display: block;
    text-align: left;
    padding: @menu-vertical-offsets/2 0;

    .collapse-icon {
      display: block;
      position: absolute;
      top: 0;
      right: 0;
      text-align: center;
      font-size: 0;

      &:before {
        font-size: 18px;
      }
    }

    .collapse-icon-active {
      &:before {
        content: "\f106";
      }
    }

    .cc-open {
      > ._navigation-dropdown {
        display: block;
      }
    }
  }

  ._navigation-main-list-item {
    display: block;
    padding: 0;

    @media @hover {
      &:hover {
        > a {
          background-color: transparent; /* theme */
          color: @color-dropdowns-highlight; /* theme */
        }
      }
    }

    &:first-child {
      margin-top: 0;
      margin-bottom: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }

    &:before {
      display: none;
    }
  }

  ._navigation-main-list-item-link {
    color: @color-dropdowns-text; /* theme */
    padding: @menu-vertical-offsets/2 @menu-horizontal-offsets;
    font-weight: bold;

    > .collapse-icon {
      width: @main-collapse-icon-size;
      height: @main-collapse-icon-size;
      line-height: @main-collapse-icon-size;
    }
  }

  ._navigation-main-list-item-link,
  ._navigation-dropdown-list-item-link {
    position: relative;
    display: block;
    padding: 10px 20px;
    .uppercase();
  }

  ._navigation-dropdown {
    position: static;
    width: auto;
    padding: 0;
    padding-left: 10px;
    border: 0;
    opacity: 1;
    visibility: visible;
    transform: translate(0, 0);
    display: none;
  }

  ._navigation-dropdown-list-item {
    border: 0;
    margin: 0 0 7px;
    padding: 0;

    ._navigation-dropdown {
      margin-top: 7px;
    }

    &.item-collapse {
      a {
        padding-right: 30px;
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  ._navigation-dropdown-list-item-link {
    margin: 0;
    padding-top: @menu-vertical-offsets/4;
    padding-bottom: @menu-vertical-offsets/4;
    color: @color-dropdowns-meta-text; /* theme */

    > .collapse-icon {
      width: @main-collapse-icon-size;
      height: @secondary-collapse-icon-size;
      line-height: @secondary-collapse-icon-size;
    }
  }
}

/*  UTILITIES
-------------------------------------------------------------------------------*/

._user-controls-button,
._cart-compact-button,
._wishlist-compact-button {
  color: @color-header-icons; /* theme */
  transition: .2s;

  &:focus {
    color: @color-header-icons; /* theme */
  }

  @media @hover {
    &:hover {
      color: @color-header-icons-hover; /* theme */
    }
  }

  ._figure-stack-label {
    display: none;
  }

  ._figure-stack-icon {
    font-family: "Font Awesome 5 Pro";
    font-weight: 300;
    font-size: @header-icons-font-size;
    top: 0;
  }

  ._bubble {
    position: absolute;
    top: -3px;
    right: -3px;
  }
}

._nav-mobile-backdrop {
  background-color: rgba(0, 0, 0, .65);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: @z-nav-mobile - 1;
  opacity: 0;
  visibility: hidden;
  transition: .2s;

  &.open {
    opacity: 1;
    visibility: visible;
  }
}

.page-home {
  ._header {
    &._header-transparent {
      background-color: transparent;
      border-color: transparent;
      display: block;
      position: absolute;
      left: 0;
      right: 0;
      width: 100%;

      ._topbar,
      ._header-fixed-outer {
        background-color: transparent;
        border-color: transparent;
      }

      ._navbar {
        background-color: transparent;
        border-color: transparent;
      }

      ._navigation-main-list-item {
        &:hover {
          > a {
            background-color: transparent;
          }
        }
      }
    }
  }
}

._header {
  &._full-width {
    .container {
      width: 100%;
      max-width: 100%;
    }
  }
}

.page-product {
  ._header-variant-one,
  ._header-variant-two,
  ._header-variant-three,
  ._header-variant-four,
  ._promo-bar-container {
    &._header-fixed {
      position: absolute;
    }
  }

  ._promo-bar-container-three {
    &._promo-bar-container {
      position: absolute;
    }
  }
}