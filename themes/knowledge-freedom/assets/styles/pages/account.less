/*=============================================================================*\
    ACCOUNT
\*=============================================================================*/

/*  MAIN
-------------------------------------------------------------------------------*/
._account-table {
	table {
		width: 100%;
		line-height: @line-height-medium;
	}

	th {
		white-space: nowrap;
	}
	
	th,
	td {
		border-bottom: 1px solid;
		border-bottom-color: @color-main-borders; /* theme */
		padding: 10px;
		color: @color-main-meta-text; /* theme */

		&:first-child {
			padding-left: 0;
		}

		&:last-child {
			padding-left: 0;
		}
	}

	.text-alignright {
		text-align: right;
	}

	.fa {
		color: @color-main-icons; /* theme */

		&:hover {
			opacity: .7;
		}
	}

	._pagination {
		margin-top: @separator-small;
		margin-bottom: 0;
	}

	&-date {
		white-space: nowrap;
	}
}

._address-book-list-actions {
	._figure-stack-icon {
		color: @color-main-icons; /* theme */

		&:hover {
			color: @color-main-highlight; /* theme */
		}
	}
}

.status-payment-green,
.status-order-green,
.status-fulfilment-green {
	color: @color-notification-success-text;
}

.status-payment-orange,
.status-order-orange,
.status-fulfilment-orange {
	color: @color-notification-note-text;
}

.status-payment-red,
.status-order-red,
.status-fulfilment-red {
	color: @color-notification-error-text;
}