/*=============================================================================*\
    CART
\*=============================================================================*/

/*  CART SUMMARY PRODUCTS
-------------------------------------------------------------------------------*/

._cart-summary {
	background-color: @color-second-background; /* theme */
	padding: 20px;
	margin-bottom: 20px;
	font-size: calc(@font-size-main ~'-' 1px); /* theme */
	line-height: @line-height-low;

	&:last-child {
		margin-bottom: 0;
	}

	._section-title {
		padding-bottom: 5px;
		margin-bottom: 20px;

		h3 {
			._h6();
		}
	}
}

._cart-summary-title {
	border-bottom: 1px solid;
	border-color: @color-second-borders; /* theme */
	padding-bottom: 16px;
	margin-bottom: 20px;
	.uppercase();

	h2 {
		color: @color-second-titles; /* theme */
	}
}
	
._cart-summary-actions {
	._form-row {
		margin-bottom: 20px;

		&:last-child {
			margin-bottom: 0;
			text-align: center;
		}
	}

	._button {
		width: 100%;
	}
}

._cart-summary-products-list {
	border-color: @color-second-borders; /* theme */
	margin-bottom: 15px;
}

._cart-summary-products-list-item {
	._remove {
		color: @color-second-text; /* theme */

		@media @hover {
			&:hover {
				color: @color-second-highlight; /* theme */
			}
		}
	}
}

._cart-summary-products-list-item-link {
	color: @color-second-text; /* theme */

	@media @hover {
		&:hover {
			color: @color-second-highlight; /* theme */							
		}
	}
}

._cart-summary-products-list-item-image-thumb {
	background-color: @color-second-image-box; /* theme */
	border-color: @color-second-borders; /* theme */
}

._cart-summary-products-list-item-title {
	color: @color-second-secondary-text; /* theme */
}

._cart-summary-products-list-item-parameters {
	display: block;
	color: @color-second-meta-text; /* theme */
	font-size: calc(@font-size-main ~'-' 2px); /* theme */
	margin-bottom: 4px;
}


/*  TOTALS
-------------------------------------------------------------------------------*/

._cart-totals-box {
	border-bottom: 1px solid;
	border-color: @color-second-borders; /* theme */
	margin-bottom: 20px;
	padding-bottom: 15px;
	font-size: @font-size-main; /* theme */
	color: @color-second-meta-text; /* theme */
}

._cart-totals-box-row {
	display: table;
	width: 100%;
	margin-bottom: 10px;

	&:last-child {
		margin-bottom: 0;
	}

	&.final-total {
		border-top-width: 1px;
		border-top-style: solid;
		border-top-color: @color-second-borders; /* theme */
		margin-top: 15px;
		padding-top: 15px;
		color: @color-second-text; /* theme */
		font-weight: bold;
	}
}

._cart-totals-box-row-title {
	display: table-cell;
	vertical-align: top;
	padding-right: 20px;

    &.shipping-title {
        span {
            display: block;
            font-size: 10px;
        }
    }
}

._cart-totals-box-row-value {
	display: table-cell;
	vertical-align: top;
	width: 1px;
	white-space: nowrap;
	color: @color-second-text; /* theme */
	text-align: right;

	& when (@rtl) {
		direction: ltr;
	}
}

._cart-totals-medals {
	margin-bottom: 20px;
}

/*  DISCOUNT CODE
-------------------------------------------------------------------------------*/

._cart-discount-code {
	margin-bottom: 20px;
	font-size: calc(@font-size-main ~'-' 1px); /* theme */
	font-weight: bold;

	a {
		&:before {
			content: '+ '
		}

		&:hover {
			opacity: .7;
		}
	}

	._field-stack-addon {
		padding-left: 10px;
	}
}

._cart-discount-code-applied {
	position: relative;
}

._cart-discount-code-applied-remove {
	margin-left: 3px;
	display: inline-block;
	position: relative;
	top: -1px;

	._remove {
		font-size: 0;
		
		&:before {
			color: @color-second-text; /* theme */
			font-family: @font-awesome;
			content:"\f00d";
		}
	}
}

._cart-discount-code-form {
	display: none;

	&.show {
		display: block;
	}

	form {
		margin-top: 10px;
	}
}

._text-box-checkout {
	margin-top: 20px;
	font-size: calc(@font-size-main ~'-' 2px); /* theme */
	color: @color-second-meta-text; /* theme */

	h1,h2,h3,h4,h5,h6 {
		margin-bottom: 2px;
		font-weight: bold;
	}
}