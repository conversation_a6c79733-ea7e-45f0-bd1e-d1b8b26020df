/*=============================================================================*\
    HOME
\*=============================================================================*/

.page-home {
	._content {
		padding: 0;
	}
}

/*  TEXT BOXES
-------------------------------------------------------------------------------*/

@tooltip-arrow-size: 12px;

._text-boxes-container {
	margin-left: -15px;
	margin-right: -15px;
	white-space: nowrap;
	position: relative;
	font-size: 0;
}

._text-boxes {
	._text-box {
		width: 25%;
		padding: 0 15px;
	}
}

._text-box {
	display: inline-block;
	vertical-align: top;
	font-size: @font-size-main; /* theme */
	white-space: normal;
	position: relative;

	@media @hover {
		&:hover {
			._text-box-tooltip {
				opacity: 1;
				visibility: visible;
			}
		}
	}
}

.swiper-pagination-bullets {
	text-align: center;

	.swiper-pagination-bullet {
		background-color: @color-main-borders; /* theme */
		border-radius: 50%;
		width: 10px;
		height: 10px;
		margin: 20px 3px 0 !important;

		&.swiper-pagination-bullet-active {
			background-color: @color-main-highlight; /* theme */

			&:first-child:last-child {
				display: none;
			}
		}
	}
}

._text-box-main {
	background-color: @color-text-box-background; /* theme */
	border: 2px solid;
	border-color: @color-text-box-borders; /* theme */
	padding: 18px 20px;

	._text {
		color: @color-text-box-text; /* theme */
		font-size: calc(@font-size-main ~'-' 2px); /* theme */
		line-height: @line-height-medium;

		p {
			margin-bottom: 16px;

			&:last-child {
				margin-bottom: 0;
			}
		}

		h1,h2,h3,h4,h5,h6 {
			color: @color-text-box-titles; /* theme */
			font-weight: bold;
		}
	}
}

._text-box-tooltip {
	background-color: @color-text-box-tooltip-background; /* theme */
	border: 1px solid;
	border-color: @color-text-box-tooltip-borders; /* theme */
	width: 300px;
	padding: 16px 20px;
	bottom: calc(100% ~'+' @tooltip-arrow-size);
	opacity: 0;
	visibility: hidden;
	transition: .5s;
	z-index: @z-text-boxes;
	.centerer(true, false);

	&:before {
		content: '';
		border-left: @tooltip-arrow-size solid transparent;
		border-right: @tooltip-arrow-size solid transparent;
		border-top: @tooltip-arrow-size solid;
		border-top-color: @color-text-box-tooltip-background; /* theme */
		top: 100%;
		.centerer(true, false);
	}
	
	._text {
		color: @color-text-box-tooltip-text; /* theme */
		font-size: calc(@font-size-main ~'-' 2px); /* theme */
		line-height: @line-height-medium;

		h1,h2,h3,h4,h5,h6 {
			color: @color-text-box-tooltip-text; /* theme */
			margin-bottom: 14px;
			font-weight: bold;
		}
	}
}

._homepage-text {
	background-size: cover;
	background-position: center center;
	padding: 100px 0;

	&:last-child {
		margin-bottom: 0;
	}

	+ ._homepage-text {
		margin-top: -@separator - 1px;
	}

	._text {
		h1,h2,h3,h4,h5,h6 {
			font-weight: 300;

			strong {
				font-weight: 900;
			}
		}

		//h1 {
		//	font-size: 100px; /* theme */
		//	margin-bottom: 30px;
		//}

		//h2 {
		//	font-size: 80px; /* theme */
		//	margin-bottom: 30px;
		//}

		//h3 {
		//	font-size: 40px; /* theme */
		//	margin-bottom: 20px;
		//}

		//h4 {
		//	font-size: 30px; /* theme */
		//	margin-bottom: 10px;
		//}
		//
		//h5 {
		//	font-size: 24px; /* theme */
		//	margin-bottom: 10px;
		//}

		//h6 {
		//	font-size: 20px; /* theme */
		//	margin-bottom: 10px;
		//}
	}
}

._homepage-text1 {
	background-color: @color-text1-background; /* theme */
	color: @color-text1-text; /* theme */

	h1,h2,h3,h4,h5,h6 {
		color: @color-text1-titles; /* theme */
	}

	a {
		color: @color-text1-highlight; /* theme */
	}

	._button {
		color: @color-button-text; /* theme */

		@media @hover {
			&:hover {
				color: @color-button-text-hover; /* theme */
			}
		}
	}

	._button-secondary {
		color: @color-button-secondary-text; /* theme */

		@media @hover {
			&:hover {
				color: @color-button-secondary-text-hover; /* theme */
			}
		}
	}
}

._homepage-text2 {
	background-color: @color-text2-background; /* theme */
	color: @color-text2-text; /* theme */

	h1,h2,h3,h4,h5,h6 {
		color: @color-text2-titles; /* theme */
	}

	a {
		color: @color-text2-highlight; /* theme */
	}

	._button {
		color: @color-button-text; /* theme */

		@media @hover {
			&:hover {
				color: @color-button-text-hover; /* theme */
			}
		}
	}

	._button-secondary {
		color: @color-button-secondary-text; /* theme */

		@media @hover {
			&:hover {
				color: @color-button-secondary-text-hover; /* theme */
			}
		}
	}
}

._homepage-text3 {
	background-color: @color-text3-background; /* theme */
	color: @color-text3-text; /* theme */

	h1,h2,h3,h4,h5,h6 {
		color: @color-text3-titles; /* theme */
	}

	a {
		color: @color-text3-highlight; /* theme */
	}

	._button {
		color: @color-button-text; /* theme */

		@media @hover {
			&:hover {
				color: @color-button-text-hover; /* theme */
			}
		}
	}

	._button-secondary {
		color: @color-button-secondary-text; /* theme */

		@media @hover {
			&:hover {
				color: @color-button-secondary-text-hover; /* theme */
			}
		}
	}
}

._slider-container {
	+ ._homepage-text {
		margin-top: -@separator - 1px;
	}
}