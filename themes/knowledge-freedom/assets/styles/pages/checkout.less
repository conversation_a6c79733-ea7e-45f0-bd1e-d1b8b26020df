/*=============================================================================*\
    CHECKOUT
\*=============================================================================*/

/*  MAIN
-------------------------------------------------------------------------------*/

._checkout-express,
._checkout-cart {
	[class*='col-'] {
		position: static;
	}
}

._checkout-express {
	[id*='step-'] {
		&.step-disabled {
			height: 0;
			margin: 0;
			padding: 0;
			opacity: 0;
			overflow: hidden;
			position: relative;

			&:before {
				position: absolute;
				top: 0;
				bottom: 0;
				left: 0;
				right: 0;
				content: '';
				z-index: 1000;
				background-color: rgba(255, 255, 255, .7);
				cursor: not-allowed;
			}
		}
	}
}

._checkout-steps {
	._section-title,
	._secondary-title {
		text-align: left;

		h3 {
			._h2();
		}
	}
}

/*  RETURN
-------------------------------------------------------------------------------*/

._checkout-return {
	max-width: 625px;
	padding: 50px 0;
	margin: 0 auto;
	text-align: center;

	h1  {
		margin-bottom: 50px;
		.uppercase();
	}

	p {
		margin-bottom: 20px;
	}

	._button {
		display: block;
		max-width: 400px;
		margin: 50px auto 0;
	}
}

._checkout-return-icon {
	font-size: 100px;
}