/*=============================================================================*\
    LOGO
\*=============================================================================*/

._logo {
	display: table-cell;
	vertical-align: middle;
	text-align: center;
	width: 262.5px;
	padding: @header-vertical-offsets/2 @header-horizontal-offsets/2;

	@media @hover {
		&:hover {
			._helper-logo {
				opacity: 1;
				visibility: visible;
			}
		}
	}

	img {
		max-width: 100%;
		max-height: 70px;
		width: auto;
		height: auto;
	}

	._helper-logo {
		position: absolute;
		color: @color-header-text; /* theme */
		bottom: 28px;
		left: 100%;
		white-space: nowrap;
		margin-left: 15px;
		visibility: hidden;
		opacity: 0;
		-webkit-transition: .2s;
		        transition: .2s;
	}
}

._logo-link,
._logo-image {
	display: inline-block;
}

.logo-text {
	._h1();
}


._logo {
	&._center-logo {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
	}
}