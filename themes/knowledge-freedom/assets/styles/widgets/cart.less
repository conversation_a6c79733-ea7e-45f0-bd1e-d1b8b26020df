/*=============================================================================*\
    CART
\*=============================================================================*/

/*  COMPACT
-------------------------------------------------------------------------------*/

._cart-compact {
	._cart-compact {
		padding: 0;
		margin: 0;
	}
}

._cart-compact,
._wishlist-compact {
	display: inline-block;
	vertical-align: middle;
	position: relative;
	font-size: calc(@font-size-main ~'-' 1px); /* theme */
	line-height: @line-height-low;
	text-align: left;
	margin-left: @header-icons-offsets;

	@media @hover {
		&:hover {
			._cart-compact-dropdown,
			._wishlist-compact-dropdown {
				opacity: 1;
				visibility: visible;
			}
		}
	}
}

._cart-compact-dropdown,
._wishlist-compact-dropdown {
	width: 360px;
	position: absolute;
	top: 100%;
	right: 0;
	padding-top: @header-vertical-offsets;
	opacity: 0;
	visibility: hidden;
	transition: .2s;
}

._cart-compact-products-wrapper,
._wishlist-compact-products-wrapper {
	background-color: @color-dropdowns-background; /* theme */
	border: 1px solid;
	border-color: @color-dropdowns-borders; /* theme */
	padding: 20px;
}

._cart-compact-products,
._wishlist-compact-products {
	._button {
		display: block;
		width: 100%;
		.button-colors(@color-dropdowns-button-background, @color-dropdowns-button-borders, @color-dropdowns-button-text);

		@media @hover {
			&:hover {
				.button-colors(@color-dropdowns-button-background-hover, @color-dropdowns-button-borders-hover, @color-dropdowns-button-text-hover);
			}
		}

		&[disabled] {
			.button-colors(@color-button-disabled-background, @color-button-disabled-background, @color-button-disabled-text);
		}
	}
}

._cart-compact-products-list,
._wishlist-compact-products-list,
._cart-summary-products-list {
	list-style-type: none;
	border-bottom: 1px solid;
	border-color: @color-dropdowns-borders; /* theme */
	margin-bottom: 20px;
}

._cart-compact-products-list,
._wishlist-compact-products-list {
	max-height: 321px;
	overflow: auto;
}

._cart-compact-products-list-item,
._wishlist-compact-products-list-item,
._cart-summary-products-list-item {
	display: block;
	position: relative;
	margin-bottom: 20px;

	@media @hover {
		&:hover {
			._remove {
				opacity: 1;
				visibility: visible !important;
			}
		}
	}

	._remove {
		color: @color-dropdowns-text; /* theme */
		position: absolute;
		top: 0;
		right: 0;
		opacity: 0;
		visibility: hidden !important;
		transition: .2s;

		@media @hover {
			&:hover {
				color: @color-dropdowns-highlight; /* theme */
			}
		}
	}
}

._cart-compact-products-list-item-link,
._wishlist-compact-products-list-item-link,
._cart-summary-products-list-item-link {
	display: table;
	width: 100%;
	color: @color-dropdowns-text; /* theme */
	transition: .2s;

	@media @hover {
		&:hover {
			color: @color-dropdowns-highlight; /* theme */							
		}
	}
}

._cart-compact-products-list-item-image,
._wishlist-compact-products-list-item-image,
._cart-summary-products-list-item-image {
	display: table-cell;
	vertical-align: top;
	width: 60px;
}

._cart-compact-products-list-item-image-thumb,
._wishlist-compact-products-list-item-image-thumb,
._cart-summary-products-list-item-image-thumb {
	background-color: @color-dropdowns-image-box; /* theme */
}

._cart-compact-products-list-item-image-thumb,
._wishlist-compact-products-list-item-image-thumb {
	img {
		max-width: calc(100% ~'-' 6px);
		max-height: calc(100% ~'-' 6px);
	}
}

._cart-summary-products-list-item-image-thumb {
	background-color: @color-dropdowns-image-box; /* theme */
	box-shadow: inset 0 0 0 1px @color-dropdowns-borders; /* theme */
}

._cart-compact-products-list-item-info,
._wishlist-compact-products-list-item-info,
._cart-summary-products-list-item-info {
	display: table-cell;
	vertical-align: top;
	padding: 0 12px;
}

._cart-compact-products-list-item-title,
._wishlist-compact-products-list-item-title,
._cart-summary-products-list-item-title {
	display: block;
	color: @color-dropdowns-secondary-text; /* theme */
	margin-bottom: 4px;
}

._cart-compact-products-list-item-price,
._wishlist-compact-products-list-item-price,
._cart-summary-products-list-item-price {
	display: block;
	font-weight: bold;

	del {
		display: none;
	}
}

._cart-compact-products-list-item-parameters,
._wishlist-compact-products-list-item-parameters {
	display: block;
	color: @color-dropdowns-meta-text; /* theme */
	font-size: calc(@font-size-main ~'-' 2px); /* theme */
	margin-bottom: 4px;
	display: none;
}

._cart-compact-products-subtotal,
._cart-totals-box-row.final-total {
	margin-bottom: 20px;
	color: @color-dropdowns-text; /* theme */
	font-size: calc(@font-size-main ~'+' 2px); /* theme */
	font-weight: bold;
}

._cart-compact-products-subtotal-num,
._cart-totals-box-num {
	font-weight: bold;
	float: right;	
}

._bubble {
	display: inline-block;
	background-color: @color-header-icons-bubble-background; /* theme */
	color: @color-header-icons-bubble-text; /* theme */
	padding: 0;
	border-radius: 100%;
	font-size: calc(@font-size-main ~'-' 4px); /* theme */
	text-align: center;
	font-weight: 400;
	width: 16px;
	height: 16px;
	line-height: 16px;
}

._cart-compact,
._wishlist-compact {
	>._figure-stack  {
		._figure-stack-label {
			display: none;
		}
	}
}

/*  PRODUCTS LIST
-------------------------------------------------------------------------------*/

._cart-product {
	border-bottom: 1px solid;
	border-bottom-color: @color-main-borders; /* theme */
	padding-bottom: 30px;
	margin-bottom: 30px;
	position: relative;
	
	&:last-child {
		padding-bottom: 0;
		border-bottom: 0;
		margin-bottom: 0;

		._cart-product-quantity,
		._cart-product-total-price {
			bottom: 0;
		}
	}

	&:hover {
		._remove {
			opacity: 1;
			visibility: visible !important;
		}
	}

    ._remove {
        position: absolute;
        top: 0;
        right: 0;
        color: @color-main-secondary-text; /* theme */
        opacity: 0;
        visibility: hidden !important;

        &:before {
        	font-size: calc(@font-size-main ~'-' 2px); /* theme */
        }

		@media @hover {
	        &:hover {
	        	color: @color-main-highlight; /* theme */
	        }
		}
    }
}

._cart-product-info {
	display: table;
	width: 100%;
}

._cart-product-image {
	display: table-cell;
	vertical-align: top;
	width: 250px;
	padding-top: 3px;
}

._cart-product-image-thumb {
	background-color: @color-second-background; /* theme */
	position: relative;

	img {
		max-width: 100%;
		max-height: 100%;
		.centerer(true, true);
	}
}

._cart-product-box {
	padding-left: 30px;
	padding-bottom: 55px;
	display: table-cell;
	vertical-align: top;
	color: @color-main-meta-text; /* theme */
	font-size: calc(@font-size-main ~'-' 1px);
	line-height: @line-height-medium;
}

._cart-product-name {
	margin-bottom: 28px;
	padding-right: 25px;

	h4 {
		._h2();
	}
}

._cart-product-parameters {
	font-size: calc(@font-size-main ~'+' 2px); /* theme */
	margin-top: 5px;
}

._cart-product-parameter {
	display: block;
	
	& when (@rtl) {
		direction: ltr;
	}
}

._cart-product-single-price {
	font-size: calc(@font-size-main ~'+' 2px); /* theme */
	text-align: left;
	
	& when (@rtl) {
		direction: ltr;
	}
}

._cart-product-quantity {
	position: absolute;
	bottom: 30px;
	left: 280px;
	font-size: 0;
	
	.input-group {
		width: 150px;
	}
}

._cart-product-quantity-total {
	font-size: @font-size-main; /* theme */
	
	&:before {
		content: 'x ';
	}
}

._cart-product-total-price {
	font-size: calc(@font-size-main ~'+' 4px); /* theme */
	font-weight: bold;
	line-height: 1;
	color: @color-main-secondary-text; /* theme */
	position: absolute;
	right: 0;
	bottom: 30px;
	
	& when (@rtl) {
		direction: ltr;
	}
}