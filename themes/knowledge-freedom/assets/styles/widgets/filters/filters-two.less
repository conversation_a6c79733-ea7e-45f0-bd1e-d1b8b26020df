/*=============================================================================*\
    FILTERS
\*=============================================================================*/

._sidebar-buttons-products {
  display: none;
}

._filters-two {
  &._filters {
    display: table;
    width: 100%;
  }

  ._filters-left,
  ._filters-right {
    display: table-cell;
    vertical-align: top;
  }

  ._filters-right {
    width: 1px;
  }

  ._filters-row {
    float: right;
    display: table;
  }

  ._filters-col {
    display: table-cell;
    vertical-align: top;
    width: 180px;
    padding-left: 15px;

    &-small {
      width: 70px;
    }
  }
}
/*  TYPE
-------------------------------------------------------------------------------*/

._filters-two {
  ._filter-type {
    margin-bottom: 5px;

    &:last-child {
      margin-bottom: 0;
    }

    ._button {
      display: block;
      background: transparent;
      text-align: left;
      padding: 0;
      color: @color-main-text; /* theme */
      font-family: @font-family-main; /* theme */
      font-size: inherit;
      text-transform: initial;
      font-weight: normal;
      line-height: @line-height-base;
      margin-bottom: 2px;

      &:hover {
        opacity: 1;
        color: @color-main-highlight; /* theme */
      }

      &:focus {
        color: @color-main-text; /* theme */
      }

      &-active {
        color: @color-main-highlight; /* theme */

        &:focus {
          color: @color-main-highlight; /* theme */
        }
      }
    }
  }
}


/*  CATEGORIES
-------------------------------------------------------------------------------*/

._filters-two {
  ._filter-categories-list {
    &-all {
      display: none;
      background: transparent;
      outline: 0;
      color: @color-main-secondary-text; /* theme */
      margin-bottom: 4px;
      transition: .2s;

      @media @hover {
        &:hover {
          color: @color-main-highlight; /* theme */
        }
      }
    }
  }
}

/*  CATEGORY PROPERTIES
-------------------------------------------------------------------------------*/

._filters-two {
  ._filter-category-property {
    &-title {
      cursor: pointer;
      line-height: 1;
    }

    ._form-row {
      margin-top: 0;
      margin-bottom: 0;
    }
  }

  @media (min-width: 1201px) {
    ._filter-category-property {
      float: left;
      margin-top: 5px;
      margin-bottom: 5px;
      margin-right: 30px;
      position: relative;

      &:hover {
        z-index: 2;

        ._form {
          display: block;
        }
      }

      ._form {
        display: none;
        background-color: @color-main-background; /* theme */
        border: 1px solid;
        border-color: @color-main-borders; /* theme */
        border-radius: 5px;
        padding: 20px;
        position: absolute;
        top: 100%;
        left: -15px;
        width: 560px;
      }

      ._form-row,
      ._form-col {
        display: block;
      }
    }

    ._filter-category-property-plain,
    ._filter-category-property-radio,
    ._filter-category-property-select,
    ._filter-category-property-2d,
    ._filter-category-property-numeric_alpha {
      ._form {
        column-count: 3;
        column-gap: 15px;
      }
    }

    ._filter-category-property-image {
      ._form {
        width: 355px;
      }
    }

    ._filter-category-property-color {
      ._form {
        width: 435px;
      }
    }

    ._filter-category-property-title {
      padding-top: 5px;
      padding-bottom: 5px;

      h5 {
        padding-right: 15px;
        position: relative;
        font-size: 16px; /* theme */
        font-weight: @font-weight-main; /* theme */

        &:after {
          content: "\f107";
          font-family: @font-awesome;
          position: absolute;
          top: 0;
          right: 0;
          line-height: 1;
        }
      }
    }
  }

  @media (max-width: @screen-lg-min) {
    ._filter-category-properties {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ._filter-category-property {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }

      &.open {
        //._filter-category-property-title {
        //  h5 {
        //    &:after {
        //      content: '-';
        //    }
        //  }
        //}

        ._form {
          display: block;
        }
      }

      //&-title {
      //  margin-bottom: 5px;
      //
      //  h5 {
      //    display: inline-block;
      //    padding-right: 12px;
      //    position: relative;
      //
      //    &:after {
      //      position: absolute;
      //      top: 0;
      //      right: 0;
      //      content: '+';
      //    }
      //  }
      //}

      ._form {
        display: none;
      }
    }
  }

  ._filter-category-property-image,
  ._filter-category-property-color {
    ._form-row,
    ._form-col {
      display: block;
      width: auto;
      margin: 0;
      padding: 0;
    }

    ._form-inner {
      margin-left: -1%;
      margin-bottom: -1%;
      .clearfix();
    }

    ._form-row {
      float: left;
      margin-left: 1%;
      margin-bottom: 1%;

      + ._form-row {
        margin-top: 0;
      }
    }

    ._checkbox {
      display: block;
      height: 0;
      padding: 0;
      font-size: 0; /* theme */
      line-height: 0;
      position: relative;
      overflow: hidden;
      word-break: break-word;

      &.active {
        border-color: @color-main-highlight; /* theme */
      }

      .checker {
        display: none;
      }
    }
  }

  ._filter-category-property-image {
    ._form-row {
      width: 24%;
      max-width: 60px;
    }

    ._checkbox {
      border: 1px solid;
      border-color: @color-main-borders; /* theme */
      padding-bottom: calc(100% ~'-' 2px);

      &.active {
        border-width: 2px;
        padding-bottom: calc(100% ~'-' 4px);
      }

      img {
        .centerer(true, true);
      }
    }
  }

  ._filter-category-property-color {
    ._form-row {
      width: 11.5%;
      max-width: 40px;
    }

    ._checkbox {
      border: 1px solid rgba(0, 0, 0, .1);
      padding-bottom: calc(100% ~'-' 2px);
      width: 100%;

      .checker {
        width: 100%;
        span {
          width: 100%;
        }
      }

      &.active {
        border-width: 2px;
        padding-bottom: calc(100% ~'-' 4px);
      }
    }
  }
}

/*  PRICE RANGE
-------------------------------------------------------------------------------*/

._filters-two {
  ._filter-price-range {
    &-holder {
      .clearfix();
      display: block;
    }

    &-from {
      float: left;
    }

    &-to {
      float: right;
    }

    &-dash {
      display: none;
    }

    ._form-actions-reverse {
      text-align: center;
    }

    ._button,
    ._form-actions-reverse ._button {
      float: none;
      display: block;
      margin: 0;

      + ._button {
        margin-top: 5px;
      }
    }
  }
}

/*  SIDEBAR BUTTONS
-------------------------------------------------------------------------------*/

._filters-two {
  ._sidebar-buttons {
    &-products {
      float: left;
    }

    &-blog {
      margin-bottom: 15px;
    }

    ._button-close {
      display: none;
    }

    &.open {
      ._button-open {
        display: none;
      }

      ._button-close {
        display: inline-block;
      }
    }
  }
}