/*=============================================================================*\
  FILTERS
\*=============================================================================*/

._filters-one {
  ._filters-row {
    margin-bottom: @separator-small;
    .clearfix();

    select {
      height: 40px;
    }

    .select2-choice {
      height: 40px;
      line-height: 36px;
    }

    ._button {
      padding-top: 13px;
      padding-bottom: 11px;
    }
  }

  ._filters-col-sidebar-button {
    display: none;
    float: left;
    margin-right: 10px;
  }

  ._filters-col-sort {
    float: left;
    width: 260px;
  }

  ._filters-col-perpage {
    float: right;
    width: 130px;
  }

  /*  TYPE
  -------------------------------------------------------------------------------*/

  ._filter-type {
    ._checkbox {
      display: block;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  /*  VENDORS
  -------------------------------------------------------------------------------*/

  ._filter-vendors-checkboxes {
    list-style-type: none;
    max-height: 350px;
    overflow-x: hidden;
    overflow-y: auto;
    padding-top: 2px;
    padding-bottom: 2px;

    li {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  /*  CATEGORIES
  -------------------------------------------------------------------------------*/

  ._filter-categories-list-all {
    display: none;
    background: transparent;
    outline: 0;
    color: @color-second-meta-text; /* theme */
    margin-bottom: @sidebar-list-item-offset;
    transition: .2s;
    .uppercase();

    &:before {
      content:"\f104";
      font-family: @font-awesome;
      margin-right: 8px;
    }

    @media @hover {
      &:hover {
        color: @color-second-highlight; /* theme */
      }
    }
  }

  /*  CATEGORY PROPERTIES
  -------------------------------------------------------------------------------*/

  ._filter-category-properties {
    margin-bottom: 10px;
  }

  ._filter-category-property {
    ._filters-one ._sidebar-box();

    ._form-inner {
      max-height: 350px;
      overflow-x: hidden;
      overflow-y: auto;
      padding-top: 2px;
      padding-bottom: 2px;
    }

    &.open {
      ._filter-category-property-title {
        border-bottom: 1px solid;
        border-color: @color-second-borders; /* theme */
        padding: 0 0 9px;
        margin: 0 0 15px;

        &:before {
          display: block;
        }

        h5 {
          &:after {
            content:"\f106";
          }
        }
      }

      ._form {
        display: block;
      }
    }

    ._form {
      display: none;
    }

    ._form-row {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  ._filter-category-property-image,
  ._filter-category-property-color {
    ._form-row,
    ._form-col {
      display: block;
      width: auto;
      margin: 0;
      padding: 0;
    }

    ._form-inner {
      margin-left: -1%;
      margin-bottom: -1%;
      .clearfix();
    }

    ._form-row {
      float: left;
      margin-left: 1%;
      margin-bottom: 1%;
    }

    ._checkbox {
      display: block;
      height: 0;
      padding: 0;
      font-size: 0; /* theme */
      line-height: 0;
      position: relative;
      overflow: hidden;
      word-break: break-word;

      &.active {
        border-color: @color-main-highlight; /* theme */
      }

      .checker {
        display: none;
      }
    }
  }

  ._filter-category-property-image {
    ._form-row {
      width: 24%;
      max-width: 100px;
    }

    ._checkbox {
      border: 1px solid;
      border-color: @color-main-borders; /* theme */
      padding-bottom: calc(100% ~'-' 2px);

      &.active {
        border-width: 2px;
        padding-bottom: calc(100% ~'-' 4px);
      }

      img {
        .centerer(true, true);
      }
    }
  }

  ._filter-category-property-color {
    ._form-row {
      width: 11.5%;
      max-width: 40px;
    }

    ._checkbox {
      border: 2px solid transparent;
      padding-bottom: calc(100% ~'-' 4px);
    }
  }

  ._filter-category-property-title {
    margin: -20px;
    padding: 20px;
    position: relative;
    cursor: pointer;

    &:before {
      display: none;
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      width: 50px;
      border-bottom: 1px solid;
      border-color: @color-second-highlight; /* theme */
    }

    h5 {
      ._h6();
      font-weight: 900;
      line-height: @line-height-low;
      color: @color-second-titles; /* theme */
      position: relative;
      .uppercase();

      &:after {
        content:"\f107";
        font-family: @font-awesome;
        color: @color-second-meta-text; /* theme */
        font-size: 18px;
        right: 0;
        .centerer(false, true);
      }
    }
  }

  /*  PRICE RANGE
  -------------------------------------------------------------------------------*/

  ._filter-price-range-holder {
    .clearfix();
    display: block;
  }

  ._filter-price-range-dash {
    margin: 0 3px;
  }

}

._filter-price-range {
  ._button {
    float: none;
    display: block;
    width: 100%;
    ._button-small();

    + ._button {
      margin-top: 5px;
    }
  }

  ._form-row {
    &:last-child {
      margin-bottom: 0;
    }
  }
}