._type-dropdown {
  &._navbar {
    background-color: @color-navigation-background; /* theme */
    border-top: 1px solid;
    border-color: @color-header-borders; /* theme */
    position: relative;
    z-index: @z-navbar;

    &.no-slider {
      border-bottom-width: 1px;
      border-bottom-style: solid;
    }
  }

  ._navigation {
    vertical-align: middle;
    font-size: 0;
    line-height: 0;
    text-align: center;

    ul {
      list-style-type: none;
    }

    li {
      position: relative;

      @media @hover {
        &:hover {
          > ._navigation-dropdown {
            opacity: 1;
            visibility: visible;
          }
        }
      }
    }

    .collapse-icon {
      display: none;
    }
  }

  ._navigation-main-list-item {
    display: inline-block;
    font-size: @nav-font-size;
    line-height: @nav-line-height;
    position: relative;
    .uppercase();

    &.dd-left {
      > ._navigation-dropdown {
        left: auto;
        right: 0;
      }
    }

    @media @hover {
      &:hover {
        z-index: 1;

        > a {
          background-color: @color-navigation-hover-background; /* theme */
          color: @color-navigation-hover-text; /* theme */

          &:focus {
            color: @color-navigation-hover-text; /* theme */
          }
        }
      }
    }
  }

  ._navigation-dropdown {
    background-color: @color-dropdowns-background; /* theme */
    border: 1px solid;
    border-color: @color-dropdowns-borders; /* theme */
    width: 200px;
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translate(-50%; 0);
    opacity: 0;
    visibility: hidden;
    transition: .5s;
    min-width: 100%;

    ._navigation-dropdown {
      top: -1px;
      left: 100%;
      transform: translate(0; 0);
      min-width: initial;
    }
  }

  ._navigation-dropdown-list-item {
    border-bottom: 1px solid;
    border-color: @color-dropdowns-borders; /* theme */
    font-size: @nav-font-size;

    @media @hover {
      &:hover {
        > a {
          color: @color-dropdowns-highlight; /* theme */

          &:focus {
            color: @color-dropdowns-highlight; /* theme */
          }
        }
      }
    }

    &:last-child {
      border-bottom: 0;
    }

    &.dd-left {
      > ._navigation-dropdown {
        left: auto;
        right: 100%;
      }
    }
  }

  ._navigation-dropdown-list-item-link {
    display: block;
    color: @color-dropdowns-text; /* theme */
    padding: 10px @nav-a-horizontal-padding;

    &:focus {
      color: @color-dropdowns-text; /* theme */
    }
  }
}