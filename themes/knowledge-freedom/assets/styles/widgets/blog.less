/*=============================================================================*\
    BLOG
\*=============================================================================*/

/*  ARTICLE
-------------------------------------------------------------------------------*/

._blog-article-image {
	margin-bottom: 15px;
	overflow: hidden;

	img {
		max-width: 100%;
		height: auto;
	}
}

._blog-article-time {
	color: @color-main-meta-text; /* theme */
	font-size: calc(@font-size-main ~'-' 2px); /* theme */
	margin-bottom: 10px;
}

._blog-article-title {
	._section-title();
	text-transform: initial;
	margin-bottom: 15px;

	h2 {
		font-weight: normal;
	}
}

._blog-article-text,
._blog-article-share,
._blog-article-comments {
	margin-bottom: @separator;

	&:last-child {
		margin-bottom: 0;
	}
}

._blog-article-comment {
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: @color-main-borders; /* theme */
	margin-bottom: 30px;
	padding-bottom: 30px;
	.clearfix();

	&:last-child {
		border-bottom: 0;
		padding-bottom: 0;
		margin-bottom: 0;
	}
}

._blog-article-comment-author {
	color: @color-main-titles; /* theme */
}

._blog-article-comment-info {
	overflow: hidden;
}

._blog-article-comment-image {
	float: left;
	margin-top: 5px;
	margin-right: 15px;
}

._blog-article-comment-time {
	font-size: calc(@font-size-main ~'-' 2px); /* theme */
	color: @color-main-meta-text; /* theme */

	&:before {
		content: '\2014';
		margin-right: 4px;
	}
}

._blog-article-comment-text {
	display: block;
	margin-top: 10px;
	font-style: italic;
}

/*  BLOG SHOWCASE
-------------------------------------------------------------------------------*/

._blog-list-articles {
    .clearfix();
}

._blog-list-article-inner {
    border: 1px solid;
    border-color: @color-main-borders; /* theme */
    overflow: hidden;
}

._blog-list-article-image {
    float: none;
    border: 0;
    border-radius: 0;
    width: auto;
    margin: 0;
    text-align: center;
}

._blog-list-article-info {
    padding: 17px 20px;
    text-align: center;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
}

._blog-list-article-meta,
._blog-list-article-title,
._blog-list-article-viewmore,
._blog-list-article-text {
    width: 100%;
}

._blog-list-article-meta {
    order: 1;
    color: @color-main-meta-text; /* theme */
    font-size: calc(@font-size-main ~'-' 2px); /* theme */
    line-height: @line-height-low;
    margin-bottom: 5px;
    display: none;
}

._blog-list-article-title {
    order: 2;
    margin-bottom: 16px;
}

._blog-list-article-title-tag {
    ._h6();

    a {
        color: @color-main-titles; /* theme */

        @media @hover {
            &:hover {
                color: @color-main-highlight; /* theme */
            }
        }
    }
}

._blog-list-article-text {
    color: @color-main-text; /* theme */
    line-height: 1.3;
    order: 3;
}

._blog-list-article-viewmore {
    display: none;
    order: 4;
}

._blog-list-article-viewmore-link {
    ._button();
    ._button-small();
    ._button-small-ghost();
}

._blog-list-article-category,
._blog-list-article-author {
    display: none;
}

._blog-showcase-container {
    margin-bottom: 60px;
}

/*  Slider
-------------------------------------------------------------------------------*/

._blog-showcase {
    overflow: hidden;

    ._blog-list-articles {
        margin: 0;
        font-size: 0;
        white-space: nowrap;
    }

    ._blog-list-article {
        float: none;
        display: inline-block;
        vertical-align: top;
        white-space: normal;
        padding: 0;
        margin: 0;
        margin-right: 30px;
    }
}

/*  LIST
-------------------------------------------------------------------------------*/

._blog-main {
	._blog-list-articles {
        margin: 0;
        position: relative;
	}

	._blog-list-article {
        float: none;
        width: auto;
        padding: 0;
        margin: 0;
        margin-bottom: 30px;

        &:last-child {
            margin-bottom: 0;
        }
    }

	._blog-list-article-inner {
        border: 0;
		.clearfix();
	}

	._blog-list-article-image {
		float: left;
		border: 1px solid;
		border-color: @color-main-borders; /* theme */
		width: 360px;
		margin-right: 30px;
		overflow: hidden;
	}

	.blog-list-article-image-thumb {
		display: block;
		overflow: hidden;

		@media @hover {
			&:hover {
				img {
					transform: scale(1.05);
				}
			}
		}

		img {
			transition: .5s;
		}
	}

    ._blog-list-article-viewmore {
        display: block;
    }

    ._blog-list-article-info {
        text-align: inherit;
        padding: 0;
    }

    ._blog-list-article-title {
        margin-bottom: 23px;
    }

    ._blog-list-article-title-tag {
        ._h4();

        a {
            color: @color-main-titles; /* theme */

            @media @hover {
                &:hover {
                    color: @color-main-highlight; /* theme */
                }
            }
        }
    }

    ._blog-list-article-text {
        margin-bottom: 15px;
        color: @color-main-meta-text; /* theme */
    }

    ._blog-list-article-meta {
        margin-bottom: 15px;
    }
}

/*  SIDEBAR
-------------------------------------------------------------------------------*/

/*  Categories */
._blog-categories {
    ._filter-remove {
        display: none;
    }
}
._blog-category-image {
	display: none;
}

/*  Recent articles */
._blog-recent-articles,
._blog-recent-comments {
	text-transform: initial;

	li {
		border-bottom: 1px solid;
		border-color: @color-second-borders; /* theme */
		padding-bottom: 15px;
		margin-bottom: 15px;

		&:last-child {
			border-bottom: 0;
			padding-bottom: 0;
			margin-bottom: 0;
		}
	}
}

._blog-recent-article-image {
	display: none;
}

._blog-recent-article-title {
	display: block;
	font-weight: bold;
	margin-bottom: 1px;
}

._blog-recent-article-time {
	display: block;
	color: @color-main-meta-text; /* theme */
	font-size: calc(@font-size-main ~'-' 2px); /* theme */
}

/*  Recent comments */
._blog-recent-comments-image {
	display: none;
}

._blog-recent-comments-author {
	display: block;
	color: @color-main-meta-text; /* theme */
	font-size: calc(@font-size-main ~'-' 2px); /* theme */
	margin-bottom: 1px;
}

._blog-recent-comments-article {
	display: block;
	font-weight: bold;
}