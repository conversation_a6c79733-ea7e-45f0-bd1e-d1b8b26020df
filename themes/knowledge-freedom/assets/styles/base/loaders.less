/*=============================================================================*\
    LOADERS
\*=============================================================================*/

/*  PAGE LOADER
-------------------------------------------------------------------------------*/

._page-loaded {
    ~ ._page-loader {
        opacity: 0;
        visibility: hidden;
    }
}

._page-loader {
    background-color: @color-main-background; /* theme */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: table;
    z-index: @z-page-loader;
    transition: .2s;

    img {
        max-width: 100%;
        animation: pulse 1s infinite cubic-bezier(0.21, 0.53, 0.56, .8);
    }
}

._page-loader-inner {
    display: table-cell;
    vertical-align: middle;
    text-align: center;
    padding: 20px;
}

/* #Loader
-------------------------------------------------------------------------------*/

.loader-container {
    background-color: @color-main-background; /* theme */
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9;

    ~ .product-list,
    ~ .alert {
        opacity: .2;
    }

    &.hide {
        ~ .product-list,
        ~ .alert {
            opacity: 1;
        }
    }
}

.loader {
    position: absolute;
    .centerer(true, true);

    span {
        background-color: @color-main-highlight; /* theme */
        border-radius: 100%;
        width: 15px;
        height: 15px;
        margin: 2px;
        position: absolute;
        -webkit-animation: dot-spin-fade 1s infinite linear;
                animation: dot-spin-fade 1s infinite linear;
        
        &:nth-child(1) {
            top: 25px;
            left: 0;
            -webkit-animation-delay: -0.96s;
                    animation-delay: -0.96s;
        }

        &:nth-child(2) {
            top: 17.04545px;
            left: 17.04545px;
            -webkit-animation-delay: -0.84s;
                    animation-delay: -0.84s;
        }

        &:nth-child(3) {
            top: 0;
            left: 25px;
            -webkit-animation-delay: -0.72s;
                    animation-delay: -0.72s;
        }

        &:nth-child(4) {
            top: -17.04545px;
            left: 17.04545px;
            -webkit-animation-delay: -0.6s;
                    animation-delay: -0.6s;
        }

        &:nth-child(5) {
            top: -25px;
            left: 0;
            -webkit-animation-delay: -0.48s;
                    animation-delay: -0.48s;
        }

        &:nth-child(6) {
            top: -17.04545px;
            left: -17.04545px;
            -webkit-animation-delay: -0.36s;
                    animation-delay: -0.36s;
        }

        &:nth-child(7) {
            top: 0;
            left: -25px;
            -webkit-animation-delay: -0.24s;
                    animation-delay: -0.24s;
        }

        &:nth-child(8) {
            top: 17.04545px;
            left: -17.04545px;
            -webkit-animation-delay: -0.12s;
                    animation-delay: -0.12s;
        }
    }
}

/* #TRIPPLE DOT
-------------------------------------------------------------------------------*/

.loader-dots {
    font-size: 0;
    line-height: 0;
    text-align: center;
    white-space: nowrap;

    span {
        display: inline-block;
        border-radius: 100%;
        width: 8px;
        height: 8px;
        margin: 2px;
        -webkit-animation: dot-pulse 1s infinite linear;
                animation: dot-pulse 1s infinite linear;

        &:nth-child(1) {
            -webkit-animation-delay: -.5s;
                    animation-delay: -.5s;
        }

        &:nth-child(2) {
            -webkit-animation-delay: -.25s;
                    animation-delay: -.25s;
        }
        
        &:nth-child(3) {
            -webkit-animation-delay: 0;
                    animation-delay: 0;
        }
    }
}