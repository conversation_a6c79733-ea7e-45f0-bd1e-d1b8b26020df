/*=============================================================================*\
    HELPERS
\*=============================================================================*/

.absolute {
    position: absolute !important
}

.block {
    display: block !important
}

.center {
    display: block !important;
    margin-left: auto !important;
    margin-right: auto !important
}

.center-block {
    display: block !important;
    margin-left: auto !important;
    margin-right: auto !important
}

.clearfix {
    &:after {
        clear: both;
        content: '';
        display: table;
    }
}

.fixed {
    position: fixed !important
}

.hide {
    display: none !important
}

.invisible {
    visibility: hidden !important
}

.left {
    float: left !important
}

.relative {
    position: relative !important;
}

.right {
    float: right !important
}

.overflow-y-hidden {
    overflow-y: hidden;
}

.rtl-ltr {
    direction: ltr;
    display: inline-block;
}

.static {
    position: static !important;
}

.text-center {
    text-align: center !important
}

.text-justify {
    text-align: justify !important
}

.text-left {
    text-align: left !important
}

.text-overflow {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.text-right {
    text-align: right !important
}

.visible {
    visibility: visible !important
}

.pos-top {
    top: 0 !important;
}

.pos-bottom {
    bottom: 0 !important;
}

.pos-left {
    left: 0 !important;
}

.pos-right {
    right: 0 !important;
}