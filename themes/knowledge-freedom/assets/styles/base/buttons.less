/*=============================================================================*\
    BUTTONS
\*=============================================================================*/

.button-colors(@background, @borders, @text) {
	background-color: @background; /* theme */
	border-color: @borders; /* theme */
	color: @text; /* theme */

	&:active {
		color: @text; /* theme */
	}

	&:focus {
		outline: 0;
		color: @text; /* theme */
	}

	&.loading {
		color: @background; /* theme */
	}

	.loader-dots {
		span {
			background-color: @text; /* theme */
		}
	}
}

._button {
	display: inline-block;
	border: 2px solid;
	padding: 14px 25px 11px;
	font-family: @font-family-buttons; /* theme */
	font-size: @font-size-buttons; /* theme */
	font-weight: @font-weight-buttons; /* theme */
	font-style: @font-style-buttons; /* theme */
	line-height: @line-height-button;
	text-align: center;
	cursor: pointer;
	transition: .2s;
	position: relative;
	.uppercase();
	.button-colors(@color-button-background, @color-button-borders, @color-button-text);

	@media @hover {
		&:hover {
			text-decoration: none;
			.button-colors(@color-button-background-hover, @color-button-borders-hover, @color-button-text-hover);
		}
	}

	&[disabled] {
		.button-colors(@color-button-disabled-background, @color-button-disabled-background, @color-button-disabled-text);
		cursor: default;
	}

	.loader-dots {
		.centerer(true, true);
	}

	._figure-stack-label {
		padding: 0 !important;
	}

	._figure-stack-icon {
		display: none;
	}
}

._button-full {
	width: 100%;
}

._button-secondary {
	.button-colors(@color-button-secondary-background, @color-button-secondary-borders, @color-button-secondary-text);

	@media @hover {
		&:hover {
			.button-colors(@color-button-secondary-background-hover, @color-button-secondary-borders-hover, @color-button-secondary-text-hover);
		}
	}
}

._button-small {
	border-width: 1px;
	font-size: calc(@font-size-main ~'-' 2px); /* theme */
	padding: 9px 20px 7px;
}

._button-small-ghost {
	.button-colors(@color-button-thertiary-background, @color-button-thertiary-borders, @color-button-thertiary-text);

	@media @hover {
		&:hover {
			.button-colors(@color-button-thertiary-background-hover, @color-button-thertiary-borders-hover, @color-button-thertiary-text-hover);
		}
	}
}

._button-icon {
	padding: 5px 12px 4px;
	font-size: 18px; /* theme */
}

/*  REMOVE
-------------------------------------------------------------------------------*/

._remove {
	display: inline-block;
	font-size: 0;
	transition: .2s;

	&:before {
		font-family: @font-glyphicons;
		font-size: calc(@font-size-main ~'-' 4px); /* theme */
		content: '\e014';
	}
}

