{$dependency = null}
{$js = null}
{capture "content"}
    {if Illuminate\Support\Str::endsWith($content_display,'.blade.php')}
        {blade_include file="$content_display"}
    {else}
        {include file="$content_display"}
    {/if}
{/capture}
{if isset($fetch) && $fetch === true}
    {if Request::ajax()}<div>{/if}
    {if !empty($dependency)}
        {foreach $dependency as $item}
            {$item nofilter}
        {/foreach}
    {/if}
    {$smarty.capture.content nofilter}
    {if !empty($js)}
        {foreach $js as $item}
            {$item nofilter}
        {/foreach}
    {/if}
    {if Request::ajax()}</div>{/if}
{else}
    <!doctype html>
    <html class="no-js" lang="{locale()}">
    <head>
        <link rel="dns-prefetch" href="{$img_url}">
        <link rel="preconnect" href="{$img_url}">
        <link rel="dns-prefetch" href="{config('url.storage')}">
        <link rel="preconnect" href="{config('url.storage')}">
        <link rel="dns-prefetch" href="https://{config('url.domains.cc_analytics')}">
        <link rel="preconnect" href="https://{config('url.domains.cc_analytics')}">
        <link rel="dns-prefetch" href="{config('url.storage')}">
        <link rel="preconnect" href="{config('url.storage')}">
        {if Apps::installed('app.xml_feed.profitshare')}
        <link rel="dns-prefetch" href="https://profitshare.bg">
        <link rel="preconnect" href="https://profitshare.bg">
        {/if}
        {if Apps::installed('app.xml_feed.retargeting')}
        <link rel="dns-prefetch" href="https://tracking.retargeting.biz">
        <link rel="preconnect" href="https://tracking.retargeting.biz">
        {/if}
        <link rel="dns-prefetch" href="https://www.googletagmanager.com">
        <link rel="preconnect" href="https://www.googletagmanager.com">
        <link rel="dns-prefetch" href="https://connect.facebook.net">
        <link rel="preconnect" href="https://connect.facebook.net">
        <link rel="dns-prefetch" href="https://www.google-analytics.com">
        <link rel="preconnect" href="https://www.google-analytics.com">
        <link rel="dns-prefetch" href="https://www.googleadservices.com">
        <link rel="preconnect" href="https://www.googleadservices.com">
        {if Apps::installed('app.xml_feed.glami')}
        <link rel="dns-prefetch" href="https://www.glami.bg">
        <link rel="preconnect" href="https://www.glami.bg">
        {/if}
        <link rel="dns-prefetch" href="https://googleads.g.doubleclick.net">
        <link rel="preconnect" href="https://googleads.g.doubleclick.net">
        <link rel="dns-prefetch" href="https://maps.googleapis.com">
        <link rel="preconnect" href="https://maps.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <script type="text/javascript">
            window.dataLayer = window.dataLayer || [];
            window.gtag = window.gtag || function () {
                if (typeof (dataLayer) !== 'undefined') {
                    dataLayer.push(arguments);
                }
            };

            window.cc_settings = {json_encode([
                "priceSup" => App\Helper\Format::getIsAllowSupPrice(),
                "gdpr" => GDPR::getJavascriptConfiguration()
            ]) nofilter}
        </script>

        {if strpos(gethostname(), 'builder-google-') === false && false}
        <script type="text/javascript" src="{{$img_url}}site/js/bd.js?{{app('last_build')}}"></script>
        <script type="text/javascript">
            let v = {json_encode(stringReverse()) nofilter}
            {if inDevelopment()}
            {literal}
            try {
                CcBotDetect.then((botd) => botd.detect())
                    .then((result) => {
                        if(result.bot) {
                            fetch(`/bdwdata?res=${result.bot ? 1 : 0}&v=${v}`);
                        }
                    })
            } catch (e) {

            }
            {/literal}
            {else}
            {literal}
            try{CcBotDetect.then(t=>t.detect()).then(t=>{t.bot&&fetch(`/bdwdata?res=${t.bot?1:0}&v=${v}`)})}catch(t){}
            {/literal}
            {/if}
        </script>
        {/if}

        <script type="text/javascript" src="{{$img_url}}site/js/modules/gdpr.min.js?{{app('last_build')}}"></script>
        {if GDPR::isActiveWithConsentGoogle()}
            <script type="text/javascript">
                gtag('consent', 'default', {
                    'ad_personalization': 'denied',
                    'ad_storage': 'denied',
                    'ad_user_data': 'denied',
                    'analytics_storage': 'denied',
                    'functionality_storage': 'denied',
                    'personalization_storage': 'denied',
                    'security_storage': 'denied'
                });
            </script>
        {/if}

        <title>{$widget->getSeo('title')|default:"SITE"}</title>
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes">
        <meta name="description" content="{$widget->getSeo('description')|default}">
        <meta name="author" content="CloudCart LLC">
        <meta name='designer' content='{site('template')}'>
        <meta name="csrf-token" content="{csrf_token()}"/>

        <script type="text/javascript" src="{$img_url}global/js/cc-ready.js?{app('last_build')}"></script>
        {JsResponse::renderFiles() nofilter}

        <script type="text/javascript">
            {JsResponse::render('top') nofilter}
        </script>

        {\App\Models\System\AppsManager::getManager('google_search_console')->getMeta() nofilter}

        {if setting('data.js')}
            <script type="text/javascript" src="{config('url.storage')}{setting('data.js')}"></script>
        {/if}

        {include file=base_path('themes/_global/templates/other/site_routes.tpl')}
        {script core="googleapis.maps" language=site('language') libraries="places"}{/script}

        {include file="_global/google_fonts.tpl"}
        {include file="_global/tools/zopim.tpl"}
        {include file="_global/dependency.tpl"}

        <!-- LOAD: font-awesome pro -->
        <link href="{$img_url}builder/global/fontawesome-pro/css/all.min.css?{app('last_build')}" rel="stylesheet"
              type="text/css"/>
        <link href="{$img_url}builder/global/fontawesome-pro/css/duotone.min.css?{app('last_build')}" rel="stylesheet"
              type="text/css"/>

        <!-- LOAD: styles -->
        {include file="_global/css_global.tpl"}
        {include file="./{site('template')}/templates/layout/src/header_assets.tpl"}
        {include file="_global/custom_css_js.tpl"}
        {if Apps::installed('multilang')}
            {$lang_versions = App\Models\System\AppsManager::getManager('multilang')->getHreflang()}
            {foreach $lang_versions as $lang => $url}
                <link rel="alternate" hreflang="{$lang}" href="{$url}"/>
            {/foreach}
        {/if}
        {$widget->canonical()}

        <link rel="alternate" type="alpplication/rss+xml" title="{setting('site_name')}" href="{Linker::feed()}"/>
        {if Apps::enabled('mailchimp')}
            {Apps::setting('mailchimp', 'script_fragment') nofilter}
        {/if}
        {*        {schemaOrganization() nofilter}*}

        {if Widget::has('buttonsConfiguration') && Widget::get('buttonsConfiguration')->isEnabled()}
            {$buttonsConfiguration = Widget::get('buttonsConfiguration')}
            <style>
                ._button,
                #credit-calculator-js {
                    border-radius: {$buttonsConfiguration->getBorderRadius()|default}
                }
                ._parameter-file-button ._button {
                    border-radius: inherit;
                }
            </style>
        {/if}

        {if Widget::has('gridConfiguration') && Widget::get('gridConfiguration')->isEnabled()}
            {$gridConfiguration = Widget::get('gridConfiguration')}
            <style>
                .container {
                    padding-left: {$gridConfiguration->getSetting('offset_mobile')}px;
                    padding-right: {$gridConfiguration->getSetting('offset_mobile')}px;
                }

                @media (min-width: 1200px) {
                    .container {
                        width: 100%;
                        {if {$gridConfiguration->getSetting('grid_width_full')}}
                            max-width: 100%;
                        {else}
                            max-width: {$gridConfiguration->getSetting('grid_width')}px;
                        {/if}
                        padding-left: {$gridConfiguration->getSetting('offset_desktop')}px;
                        padding-right: {$gridConfiguration->getSetting('offset_desktop')}px;
                    }

                    ._header .container,
                    ._header-fixed .container {
                        {if {$gridConfiguration->getSetting('grid_width_full')}}
                            max-width: 100%;
                        {else}
                            max-width: {$gridConfiguration->getSetting('grid_width')}px;
                        {/if}
                        width: 100%;
                    }
                }
            </style>
        {/if}

        {capture append="js"}
            <script type="text/javascript">
                $(document).on("click", '.js-variant-is-selected', function (e) {
                    {literal}
                    var that = $(this),
                        submit_data = {
                            'variant_id': that.data('variant_id')
                        };

                    for (let t = 1; t <= 3; t++) {
                        let vNid = that.data('v' + t + '_id');
                        if (vNid && vNid > 0) {
                            submit_data['v' + t] = vNid;
                        }
                    }

                    {/literal}

                    e.preventDefault();
                    Ajax.ajax({
                        type: "POST",
                        url: '{route('cart.add')}',
                        data: submit_data
                    });
                    return false;
                });

                $(document).off("cc.variant.changed.property").on("cc.variant.changed.property", function (e, variant) {
                    if (cc_page_data && cc_page_data.type === 'product' && variant) {
                        cc_page_data.parameter_id = variant.parameter_id || variant.item_id;
                        cc_page_data.discount_price = variant.price_discounted_input;
                        cc_page_data.price = variant.price_input;
                    }
                });

            </script>
        {/capture}
        {if Apps::installed('product_review') && Apps::enabled('product_review')}
            <script src="{{$img_url}}site/js/plugins/simple-rating.js"></script>
        {/if}

        {if Apps::installed('app.xml_feed.ad_scout')}
            <script async src="https://adscout.io/adscout-script/{Apps::setting('app.xml_feed.ad_scout', 'domain_code')}"></script>
        {/if}
        <!-- HEAD_OG_TAGS -->

        <style>
            .cc-summary-product-title {
                font-weight: 600;
                font-size: 14px;
            }

            .cc-summary-product-image {
                width: 80px;
            }

            .cc-summary-product-parameters {
                margin-bottom: 2px;
            }

            .cc-cart-product-total-price {
                position: relative;
                bottom: auto;
                top: 10px;
            }
        </style>

        {if \Modules\Marketing\Campaign\Core\Models\Apps\CampaignChannels::hasConfiguredChannelsBySubscriberChannel('web_push')}
            {\Modules\Marketing\Campaign\Core\Models\Apps\CampaignChannels::getConfiguredChannelsBySubscriberChannel('web_push')->renderSf() nofilter}
        {/if}
    </head>
    <body class="page-{segment(1)|default:'home'} {$smarty.capture.body_class}">
    {include file="_global/alert_is_admin.tpl"}
    {* TODO: site_id not to be hardcoded *}
    {if site('site_id') == 16609}
        <script type="text/javascript">
            var statTotalProducts = {App\Models\Product\Product::count()};
            var statTotalOrders = {App\Models\Order\Order::count()};
        </script>
    {/if}
    {include file="./_global/tools/google_tags_noscript.tpl"}
    {$smarty.capture.content nofilter}

    <div id="search-autocomplete-wrapper"></div>
    {include file="widgets/mailchimp/popup.tpl"}
    {include file=base_path('themes/_global/templates/other/gdpr-popup.tpl')}

    {include file=Illuminate\Support\Facades\View::make('global::cart.include.continue-shopping')->getPath()}

    {include file="./{site('template')}/templates/layout/src/footer_assets.tpl"}

    <script type="text/javascript" src="{$img_url}site/js/build.js?{app('last_build')}"></script>
    
    <!-- Analytics scripts included directly to prevent nested script tags in JsResponse -->
    {include file="_global/tools/google_analytics_dynamic.tpl"}
    {include file="_global/tools/google_tags.tpl"}

    <!-- JsResponse JavaScript output begins -->
    <script type="text/javascript">
        {JsResponse::render('bottom') nofilter}
    </script>
    <!-- JsResponse JavaScript output ends -->

    <customerUuid></customerUuid>

    {if Apps::installed('click-to-call') && Apps::enabled('click-to-call')}
        {include file="widgets/other/click-to-call.tpl"}
    {/if}

    {if inDevelopment()}
        <script type="text/javascript" async src="{$img_url}site/cb/loader.js?{app('last_build')}"></script>
    {else}
        <script type="text/javascript" async src="{$img_url}site/cb/loader.min.js?{app('last_build')}"></script>
    {/if}

    <script>
        $(document).on('hidden.bs.modal', function (e) {
            $('.modal-backdrop').remove()
        });
    </script>

    {if GDPR::isActiveWithConsentGoogle()}
        <script type="text/javascript">
            CCGDPR.callOnAcceptedConsentModeForTraffic(function() {
                gtag('consent', 'update', {
                    'ad_personalization': 'granted',
                    'ad_storage': 'granted',
                    'ad_user_data': 'granted',
                    'analytics_storage': 'granted',
                    'functionality_storage': 'granted',
                    'personalization_storage': 'granted',
                    'security_storage': 'granted'
                });
            });
        </script>
    {/if}

    <!-- CART-JS-WATCH -->

        <!-- BEGIN APPS CODES -->
        {view('live-chat::site.install') nofilter}

    </body>
    </html>
{/if}
