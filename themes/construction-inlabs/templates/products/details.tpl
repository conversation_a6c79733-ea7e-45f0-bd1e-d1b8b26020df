{if Request::ajax() || $include|default}
    <!-- LOAD: popup widgets -->

    {if $include|default}
        {$product = $include}
    {/if}

    {include file="widgets/common/product_popup.tpl"}

    <!-- BEGIN: popup -->
    <div class="_popup _popup-product-details">
        <div class="container-fluid product-details-js" data-product-id="{$product->id}">
            <form class="add-to-cart-form-js js-form-submit-ajax" action="{route('cart.add')}" method="POST">
                <div class="row">
                    <div class="col-md-6">
                        {if $product->is_bundle}
                            <div class="_product-details-bundle js-product-fixed-treshold">
                                {include file=base_path('themes/_global/templates/product/bundle/choose.tpl') display_button=false product=$product quantity_uicontrol="spinner" select_uicontrol="select2"}
                            </div>
                        {else}
                            {include file="widgets/product/details/primary_image.tpl" zoom_uicontrol="jqueryZoom" custom_labels=true}
                            <div class="_product-details-gallery-container">
                                {include file="widgets/product/details/gallery_images_slider.tpl" initSlider=true sliderNavigation=true sliderSlidesPerView="6" breakpoints="369 768 992 1200" itemsPerBreakpoint="3 4 7 5" spaceBetweenItems="15 15 15 15" defaultSpaceBetweenItems="15"}
                            </div>
                        {/if}
                    </div>
                    <div class="col-md-6">
                        {include file="./sidebar.tpl"}
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!--// END: popup -->
{else}


    {include file="../layout/header.tpl"}
    <!-- BEGIN: content -->
    <main class="_content">
        <div class="_breadcrumb-wrapper">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="_breadcrumb-wrapper-inner">
                            {if $product->category}
                                <div class="_section-title">
                                    <h2>{$product->category->name}</h2>
                                </div>
                                {include file="widgets/common/breadcrumbs.tpl" breadcrumbs=$product->breadcrumb active=$product->name}
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container product-details-js" data-product-id="{$product->id}">
            <form class="add-to-cart-form-js js-form-submit-ajax" action="{route('cart.add')}" method="POST">
                <div class="_section-separator">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="_product-details">
                                {if $product->is_bundle}
                                    <div class="_product-details-bundle js-product-fixed-treshold">
                                        {include file=base_path('themes/_global/templates/product/bundle/choose.tpl') display_button=false product=$product quantity_uicontrol="spinner" select_uicontrol="select2"}
                                    </div>
                                {else}
                                    {include file="widgets/product/details/primary_image.tpl" zoom_uicontrol="jqueryZoom" custom_labels=true}
                                    <div class="_product-details-gallery-container">
                                        {include file="widgets/product/details/gallery_images_slider.tpl" initSlider=true sliderNavigation=true sliderSlidesPerView="6" breakpoints="369 768 992 1200" itemsPerBreakpoint="3 4 7 5" spaceBetweenItems="15 15 15 15" defaultSpaceBetweenItems="15"}
                                    </div>
                                {/if}
                            </div>
                        </div>
                        <div class="col-md-6">
                            {include file="./sidebar.tpl"}
                        </div>
                    </div>
                </div>
            </form>
        </div>

        {if Widget::has('productInBundles') && Widget::get('productInBundles')->isEnabled() && Widget::get('productInBundles')->getBundles()->count()}
            <div class="container _section-separator">
                <div class="row">
                    <div class="col-md-12">
                        <div class="_section-title">
                            <h2>{t}sf.product.product_in_bundle{/t}</h2>
                        </div>

                        {include file="{base_path('themes/widgets/product/bundleProducts.tpl')}" bundles=Widget::get('productInBundles')}
                    </div>
                </div>
            </div>
        {/if}

        <div class="container">
            <div class="_section-separator">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="_product-details-tabs">
                            <ul class="_product-details-tabs-menu">
                                {if $product->description || !$product->tags->isEmpty()}
                                    {if W::ProductsDetails('show_product_description') || W::ProductsDetails('social_media_share') || !$product->tags->isEmpty()}
                                        <li>
                                            <a href="#product-description" class="_button">
                                                <span class="_figure-stack">
                                                    <span class="_figure-stack-label">{$product->description_title|default:"{t}sf.global.label.description{/t}"}</span>
                                                </span>
                                            </a>
                                        </li>
                                    {/if}
                                {/if}
                                {if W::ProductsDetails('show_brand') || W::ProductsDetails('show_category') || ($product->publicFiles && $product->publicFiles->count()) || W::ProductsDetails('show_page')}
                                    <li>
                                        <a href="#product-characteristics" class="_button">
                                            <span class="_figure-stack">
                                                <span class="_figure-stack-label">{t}sf.product.details.characteristics{/t}</span>
                                            </span> </a>
                                    </li>
                                {/if}
                                {if $product->tabs->count()}
                                    {foreach $product->tabs AS $dinamic_tab}
                                        <li>
                                            <a class="_button" href="#product-details-dinamic-tab-{$dinamic_tab->id}">
                                                 <span class="_figure-stack">
                                                    <span class="_figure-stack-label">{$dinamic_tab->name}</span>
                                                </span>
                                            </a>
                                        </li>
                                    {/foreach}
                                {/if}
                                {if Apps::enabled('facebook_comments') || Apps::enabled('disqus_comments')|| Apps::enabled('yotpo')}
                                    <li>
                                        <a href="#product-comments" class="_button">
                                            <span class="_figure-stack">
                                                <span class="_figure-stack-label">{t}sf.global.comments{/t}</span>
                                            </span> </a>
                                    </li>
                                {/if}
                            </ul>
                            {if $product->description || !$product->tags->isEmpty()}
                                <div class="_product-details-tabs-item" id="product-description">
                                    {if W::ProductsDetails('show_product_description')}
                                        <div class="_product-details-description">
                                            <div class="_textbox" data-article-content data-language="{locale()}">
                                                {if !empty($product->description)}
                                                    {$product->description nofilter}
                                                {else}
                                                    {t}sf.global.nodescription{/t}
                                                {/if}
                                            </div>
                                        </div>
                                    {/if}
                                    {if !$product->tags->isEmpty()}
                                        <div class="_product-details-tags">
                                            <div class="_section-title">
                                                <h3>{t}sf.global.tags{/t}</h3>
                                            </div>
                                            {include file="widgets/common/tags.tpl" tags=$product->tags}
                                        </div>
                                    {/if}
                                    {if W::ProductsDetails('social_media_share')}
                                        <div class="_product-details-share">
                                            {include file="widgets/extra/share.tpl" widget=W::share()}
                                        </div>
                                    {/if}
                                </div>
                            {/if}
                            <div class="_product-details-tabs-item" id="product-characteristics">
                                <div class="_product-details-characteristics">
                                    <div class="_textbox">
                                        <div class="_product-details-meta">
                                            <ul>
                                                {if !empty($product->vendor) && W::ProductsDetails('show_brand')}
                                                    <li>
                                                        <span class="_product-details-meta-title">{t}sf.global.label.vendor{/t}</span>
                                                        <span class="_product-details-meta-value">
                                                            <a href="{$product->vendor->url()}">
                                                                {$product->vendor->name}
                                                            </a>
                                                        </span>
                                                    </li>
                                                {/if}
                                                {if !empty($product->category) && W::ProductsDetails('show_category')}
                                                    <li>
                                                        <span class="_product-details-meta-title">{t}sf.global.label.category{/t}</span>
                                                        <span class="_product-details-meta-value">
                                                            <a href="{$product->category->url()}">
                                                                {$product->category->name}
                                                            </a>
                                                        </span>
                                                    </li>
                                                {/if}
                                                {if $product->publicFiles && $product->publicFiles->count()}
                                                    {foreach from=$product->publicFiles item=file}
                                                        <li>
                                                            <span class="_product-details-meta-title">{t}sf.global.files.for.download{/t}</span>
                                                            <span class="_product-details-meta-value">
                                                                <a target="_blank" href="{route('site.download.public', [$product->url_handle, $file->mask])}">{$file->name}</a>
                                                            </span>
                                                        </li>
                                                    {/foreach}
                                                {/if}
                                                {if W::ProductsDetails('show_page')}
                                                    {$page = W::ProductsDetails()->getInformationPage()}
                                                    {if $page}
                                                        <li>
                                                            <span class="_product-details-meta-title"></span>
                                                            {if W::ProductsDetails('show_link_as_popup')}
                                                                {$popup_url = $page->url()}
                                                                <span class="_product-details-meta-value"> <a href="{$popup_url}" data-ajax-panel="true">{$page->name}</a></span>
                                                            {else}
                                                                {$page_url = $page->url()}
                                                                <span class="_product-details-meta-value"><a href="{$page_url}">{$page->name}</a></span>
                                                            {/if}
                                                        </li>
                                                    {/if}
                                                {/if}
                                                {if !empty($product->size_chart)}
                                                    <li>
                                                        <a href="{route('page', $product->size_chart['url_handle'])}" class="_product-details-size-chart" data-ajax-panel  target="_blank">{$product->size_chart['name']}</a>
                                                    </li>
                                                {/if}
                                            </ul>
                                        </div>
                                        {if W::ProductsDetails('show_categories_characteristics') && !W::categoryProperties()->getPropertiesForProduct($product)->isEmpty()}
                                            <div class="_product-details-properties-wrapper">
                                                {include file="widgets/product/details/category_properties.tpl" properties=W::categoryProperties()->getPropertiesForProduct($product)}
                                            </div>
                                        {/if}
                                    </div>
                                </div>
                            </div>
                            {if $product->tabs->count()}
                                {foreach $product->tabs AS $dinamic_tab}
                                    <div class="_product-details-tabs-item" id="product-details-dinamic-tab-{$dinamic_tab->id}">
                                        <div class="_textbox" data-article-content data-language="{locale()}">
                                            {$dinamic_tab->description nofilter}
                                        </div>
                                    </div>
                                {/foreach}
                            {/if}
                            {if Apps::enabled('facebook_comments') || Apps::enabled('disqus_comments') || Apps::enabled('yotpo')}
                                <div class="_product-details-tabs-item" id="product-comments">
                                    <div class="_product-details-comments">
                                        {if Apps::enabled('yotpo')}
                                            {\App\Models\System\AppsManager::getManager('yotpo')->render($product) nofilter}
                                        {/if}
                                        {if Apps::enabled('facebook_comments')}
                                            {include file="widgets/product/details/comments-facebook.tpl"}
                                        {/if}
                                        {if Apps::enabled('disqus_comments')}
                                            {include file="widgets/product/details/comments-disqus.tpl"}
                                        {/if}
                                    </div>
                                </div>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        </div>
 		{if Apps::installed('product_review') && Apps::enabled('product_review') && !empty($product->product_review['description'])}
			<div class="container _section-separator _product-review-container">
				<div class="row">
					<div class="col-md-12">
						{$product->product_review['description'] nofilter}
					</div>
				</div>
			</div>
		{/if}

		{if Widget::get('product.linked')->getProducts($product)->count() > 0 && Widget::has('product.linked') && Widget::get('product.linked')->getSetting('enabled') && Widget::get('product.linked')->getSetting('position') == 'section_recommended'}
			<div class="container">
                <div class="_section-separator">
                    <div class="row">
                        <div class="col-md-12">
							{if Widget::get('product.linked')->getHeaderString()|default}
								<div class="_section-title">
									<h2>{Widget::get('product.linked')->getHeaderString()}</h2>
								</div>
							{/if}

							{include file="widgets/product/list.tpl" utilities=W::utilities() products=Widget::get('product.linked')->getProducts($product) widget=Widget::get('product.linked') image_size='600x600' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600'] custom_labels=true product_bar=true}
						</div>
					</div>
				</div>
			</div>
		{/if}

        {if W::productsRelated()->isEnabled() && W::productsRelated()->getProducts($product)->count()}
            <div class="container">
                <div class="_section-separator">
                    <div class="row">
                        <div class="col-md-12">
                            {if W::productsRelated()->getHeaderString()|default}
                                <div class="_section-title">
                                    <h2>{W::productsRelated()->getHeaderString()}</h2>
                                </div>
                            {/if}

                            {include file="widgets/product/list.tpl" utilities=W::utilities() products=W::productsRelated()->getProducts($product) widget=W::productsRelated() image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600'] custom_labels=true product_bar=true}
                        </div>
                    </div>
                </div>
            </div>
        {/if}

        {if Apps::setting('instagram', 'active_in_details_page')}
            <div class="container _section-separator">
                <div class="row">
                    <div class="col-md-12">
                        {Instagram::images($product) nofilter}
                    </div>
                </div>
            </div>
        {/if}

        <!-- LOAD: seo -->
        {*
{include file="widgets/common/microdata/product.tpl" related_products_widget=W::productsRelated()}
*}
        {include file="widgets/common/microdata/breadcrumb.tpl" breadcrumbs=$product->breadcrumb active=$product->name}

    </main>
    <!--// END: content -->
    {include file="../layout/footer.tpl"}
{/if}
