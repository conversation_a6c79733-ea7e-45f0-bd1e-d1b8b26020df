{include file="widgets/product/list.tpl" utilities=W::utilities() products=W::productsListing()->getProducts() widget_products=$widget->products image_size='300x300' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600'] custom_labels=true product_bar=true}
{include file="widgets/common/pagging-new.tpl" page=W::filters()->getPage() pages=W::productsListing()->getPages() link=W::productsListing()->getLink() elements=W::productsListing()->getPaginator() ajax_link=W::productsListing()->getPaggingLink()}