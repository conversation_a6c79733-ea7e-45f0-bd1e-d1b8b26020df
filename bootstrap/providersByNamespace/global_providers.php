<?php

return [
    App\GlobalServiceProviders\DatabaseServiceProvider::class,
    App\GlobalServiceProviders\SentryTagsServiceProvider::class,
    App\GlobalServiceProviders\LogSlowQueriesServiceProvider::class,
    App\GlobalServiceProviders\DbShemaParserServiceProvider::class,
    App\GlobalServiceProviders\EventServiceProvider::class,
    App\GlobalServiceProviders\EloquentBuilderServiceProvider::class,
    App\GlobalServiceProviders\QueryBuilderServiceProvider::class,
    App\GlobalServiceProviders\MaxMindServiceProvider::class,
    App\GlobalServiceProviders\SettingsServiceProvider::class,
    App\GlobalServiceProviders\ModulesServiceProvider::class,
    App\GlobalServiceProviders\AppServiceProvider::class,
    App\GlobalServiceProviders\LinkerHelperProvider::class,
    App\GlobalServiceProviders\LinkerCpHelperProvider::class,
    App\GlobalServiceProviders\DatabaseSetMaxStatementTimeServiceProvider::class,
    App\GlobalServiceProviders\RelationMapProvider::class,
    App\GlobalServiceProviders\ValidationServiceProvider::class,
    App\GlobalServiceProviders\BroadcastServiceProvider::class,
    App\GlobalServiceProviders\CommandsServiceProvider::class,
    App\GlobalServiceProviders\EIKValidatorServiceProvider::class,
    App\Integration\Facebook\Providers\PageServiceProvider::class,
    App\GlobalServiceProviders\GA4ServiceProvider::class,
    App\GlobalServiceProviders\GAMPServiceProvider::class,
    App\GlobalServiceProviders\MailEventServiceProvider::class,
    App\GlobalServiceProviders\AgentServiceProvider::class,
    App\GlobalServiceProviders\PdpServiceProvider::class,
    App\GlobalServiceProviders\ViewFactoryExtendServiceProvider::class,
    App\GlobalServiceProviders\RouteBindServiceProvider::class,
    App\GlobalServiceProviders\LiquidShareServiceProvider::class,
    App\LiquidEngine\Providers\FormRequestExtendsProvider::class,
    App\Providers\CollectionsServiceProvider::class,
    App\LiquidEngine\Providers\AppsAsseticsServiceProvider::class,
];
