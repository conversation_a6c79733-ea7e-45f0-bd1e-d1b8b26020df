<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateProductsToCollectionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('products_to_collections')) {
            Schema::create('products_to_collections', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->unsignedInteger('collection_id');
                $table->unsignedInteger('product_id');

                $table->foreign('collection_id', 'fk_ptc_collection_id')
                    ->references('id')
                    ->on('products_collections')
                    ->onDelete('CASCADE')
                    ->onUpdate('NO ACTION');

                $table->foreign('product_id', 'fk_ptc_product_id')
                    ->references('id')
                    ->on('products')
                    ->onDelete('CASCADE')
                    ->onUpdate('NO ACTION');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('products_to_collections');
    }
}
