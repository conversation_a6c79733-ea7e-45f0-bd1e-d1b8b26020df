<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateThemeBlocksGroupsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('theme_blocks_groups')) {
            Schema::create('theme_blocks_groups', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->unsignedInteger('theme_id');
                $table->string('name')->index();
                $table->string('group')->index('idx_group');
                $table->unsignedInteger('max_blocks')->nullable();
                $table->timestamps();

                $table->foreign('theme_id')
                    ->references('id')->on('theme')->onDelete('CASCADE')->onUpdate('NO ACTION');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('theme_blocks_groups');
    }
}
