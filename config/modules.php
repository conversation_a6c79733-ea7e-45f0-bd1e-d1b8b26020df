<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | Module Namespace
    |--------------------------------------------------------------------------
    |
    | Default module namespace.
    |
    */
    'namespace' => 'Modules',

    'paths' => [
        /*
        |--------------------------------------------------------------------------
        | Modules path
        |--------------------------------------------------------------------------
        |
        | This path is used to save the generated module.
        | This path will also be added automatically to the list of scanned folders.
        |
        */
        'modules' => base_path('modules'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Allowed modules array
    |--------------------------------------------------------------------------
    |
    | Here you may define all modules
    |
    */

    'installed' => [
        'core.core',
        'core.export',
        'core.cc2_factory',
        'core.cc2_factory_email',
        'core.invoicing',
        'core.navigations',
        'core.smart_collections',
        'name_com',
        'cloudflare',
        'cloud_cart_capital',
        'cc_segments',
        'plan_turnover',
        'importer',
        'json_import',
        'datalayer_events',
        'imagga',
        'js_response',
        'custom_integrations',
        'site_integrations.zora_b2b',
        'site_integrations.listing_colors',
        'site_integrations.listing_variants',
        'site_integrations.listing_images',
        'apps.marketing.bumper_offer',

        'marketing.segments.core',
        'marketing.segments.conditions.amount',
        'marketing.segments.conditions.average',
        'marketing.segments.conditions.begin_order',
        'marketing.segments.conditions.cart',
        'marketing.segments.conditions.cart.abandoned',
        'marketing.segments.conditions.category',
        'marketing.segments.conditions.country',
        'marketing.segments.conditions.country.region',
        'marketing.segments.conditions.customer',
        'marketing.segments.conditions.customer.custom_field',
        'marketing.segments.conditions.customer_group',
        'marketing.segments.conditions.date',
        'marketing.segments.conditions.date_interval',
        'marketing.segments.conditions.discount',
        'marketing.segments.conditions.order',
        'marketing.segments.conditions.order.last',
        'marketing.segments.conditions.order.status',
        'marketing.segments.conditions.order.status_fulfillment',
        'marketing.segments.conditions.others.first_name',
        'marketing.segments.conditions.others.last_name',
        'marketing.segments.conditions.page',
        'marketing.segments.conditions.payment',
        'marketing.segments.conditions.price',
        'marketing.segments.conditions.product',
        'marketing.segments.conditions.product.newest',
        'marketing.segments.conditions.product.sale',
        'marketing.segments.conditions.quantity',
        'marketing.segments.conditions.shipping',
        'marketing.segments.conditions.subscriber',
        'marketing.segments.conditions.subscriber.browser',
        'marketing.segments.conditions.subscriber.channel',
        'marketing.segments.conditions.subscriber.channel.contains',
        'marketing.segments.conditions.subscriber.channel.verified',
        'marketing.segments.conditions.subscriber.click_rate',
        'marketing.segments.conditions.subscriber.custom_field',
        'marketing.segments.conditions.subscriber.device_type',
        'marketing.segments.conditions.subscriber.from',
        'marketing.segments.conditions.subscriber.from_form',
        'marketing.segments.conditions.subscriber.last_active',
        'marketing.segments.conditions.subscriber.missing_product',
        'marketing.segments.conditions.subscriber.open_rate',
        'marketing.segments.conditions.subscriber.os',
        'marketing.segments.conditions.subscriber.rfm',
        'marketing.segments.conditions.subscriber.type',
        'marketing.segments.conditions.tag',
        'marketing.segments.conditions.times',
        'marketing.segments.conditions.utm_campaign',
        'marketing.segments.conditions.utm_medium',
        'marketing.segments.conditions.utm_source',
        'marketing.segments.conditions.vendor',
        'marketing.segments.conditions.view',
        'marketing.segments.conditions.wish_list',
        'marketing.segments.conditions.without_order',
        'marketing.campaign.core',
        'marketing.campaign.channels.email',
        'marketing.campaign.channels.messenger_message',
        'marketing.campaign.channels.set_customer_group',
        'marketing.campaign.channels.set_tags',
        'marketing.campaign.channels.sms_msghub_message',
        'marketing.campaign.channels.sms_nth_message',
        'marketing.campaign.channels.viber_message',
        'marketing.campaign.channels.web_push',
        'marketing.products_banners',
        'marketing.products_labels',
        'cc_analytics',
        'cloudio',

        // new structure
        'apps.exports.xml_feed',
        'apps.exports.ad_scout',
        'apps.marketing.click_to_call',
        'apps.payment_app',
        'apps.shippings.omniship',
        'apps.shippings.acscourier',
        'apps.shippings.speedy',
        'apps.shippings.econt',
        'apps.shippings.dhl',
        'apps.shippings.rapido',
        'apps.shippings.fancourier',
        'apps.shippings.berry',
        'apps.shippings.cargus',
        'apps.shippings.elslogistic',
        'apps.shippings.evropat',
        'apps.shippings.dpdromania',
        'apps.shippings.glovo',
        'apps.shippings.albanian_courier',
        'apps.shippings.speedex',
        'apps.shippings.mikmik',
        'apps.shippings.sameday',
        'apps.shippings.boxnow',
        'apps.shippings.ultracep',
        'apps.shippings.ntclogistics',
        'apps.shippings.tcscourier',
        'apps.shippings.dhlexpress',
        'apps.shippings.sendcloud',
        'apps.shippings.eushipment',
        'apps.shippings.shipping_hours',
        'apps.administration.multylang',
        'apps.administration.flix_facts',
        'apps.erp.microbg',
        'apps.erp.posmaster',
        'apps.erp.workflow',
        'apps.google.google_tags',
        'apps.google.google_analytics',
        'apps.google.google_search_console',
        'apps.google.google_connect',
        'apps.erp.gensoft',
        'apps.erp.versus_erp',
        'apps.erp.selmatic',
        'apps.erp.colibri',
        'apps.erp.it4profit',
        'apps.erp.polycomp',
        'apps.erp.rkeeper',
        'apps.erp.barsy',
        'apps.erp.also',
        'apps.erp.brands_distribution',
        'apps.erp.zeron',
        'apps.google.google_workspace',
        'apps.administration.smart_bill',
        'apps.google.google_sheets',
        'apps.imports.xml_import',
        'apps.exports.xml_feed_generator',
        'apps.erp.microinvest',
        'apps.erp.universum',
        'apps.erp.microbg',
        'apps.erp.posmaster',
        'apps.erp.finaleinventory',
        'apps.marketing.mailchimp',
        'apps.administration.domain_redirect',
        'apps.facebook.facebook_connect',
        'apps.facebook.facebook_comments',
        'apps.others.live_chat',
        'apps.administration.e_store_content',
        'apps.others.size_chart',
        'apps.administration.lets_encrypt',
        'apps.others.datalayer',
        'apps.administration.n18.audit',
        'apps.administration.load_bee',
        'apps.administration.private_store',
        'apps.marketing.disqus_comments',
        'apps.others.zopim',
        'apps.administration.suppliers',
        'apps.marketing.seo.spinner',
        'apps.administration.smtp',
        'apps.administration.product_options',
        'apps.administration.imos3d',
        'apps.google.google_dynamic',
        'apps.imports.xml_sync',
        'apps.administration.grocery_store',
        'apps.administration.brand_model',
        'apps.others.fast_order',
        'apps.others.gdpr',
        'apps.others.product_review',
        'apps.others.product_review.subscriber_segments.comment',
        'apps.others.product_review.subscriber_segments.comment_average',
        'apps.others.product_review.subscriber_segments.comment_category',
        'apps.others.product_review.subscriber_segments.comment_date',
        'apps.others.product_review.subscriber_segments.comment_date_interval',
        'apps.others.product_review.subscriber_segments.comment_link_send',
        'apps.others.product_review.subscriber_segments.comment_times',
        'apps.others.product_review.subscriber_segments.comment_vendor',
        'apps.others.product_review.subscriber_segments.last_comment',
        'apps.others.product_review.subscriber_segments.order_without_comment',
        'apps.others.product_review.subscriber_segments.rating',
        'apps.others.product_review.subscriber_segments.without_comment',
        'apps.erp.vali_computers',
        'apps.administration.stores_sync',
        'apps.others.olx',
        'apps.administration.membership',
//        'apps.administration.membership.subscriber_segments.*',
        'apps.administration.membership.subscriber_segments.membership',
        'apps.administration.membership.subscriber_segments.membership_expiration',
        'apps.administration.bundles',
        'apps.administration.store_locations',
        'apps.administration.profics',
        'apps.others.yotpo',
        'apps.imports.csv_import',
        'apps.imports.blog_csv_import',
        'apps.administration.pick_and_pack',
        'apps.others.requested_app',
        'marketing.cart_rules',
        'apps.administration.fgo',
        'apps.administration.frisbo',
        'apps.google.google_shopping',
        'apps.shippings.dpdbulgaria',
        'apps.shippings.gls',
        'apps.administration.szamlazz',
        'apps.shippings.dexpress',

        // Liquid template engine
        'liquid_builder.liquid_engine',
    ],

];
