<?php
/** @noinspection PhpIncludeInspection */

use Illuminate\Routing\Router;

if (app_namespace() != 'sitecp') {
    return;
}

forgetRoute('admin.navigation.*');

//storefront
Route::group(['prefix' => 'storefront', 'namespace' => 'Storefront'], function () {

//    Route::group(['prefix' => 'templates'], function (\Illuminate\Routing\Router $route) {
//        // Storefront -> Templates
//        $route->get('/', ['as' => 'admin.templates.list', 'uses' => 'TemplatesController@index']);
//        $route->get('/{mapping}', ['as' => 'admin.templates.change', 'uses' => 'TemplatesController@change']);
//    });

//    Route::group(['prefix' => 'custom-assets'], function (\Illuminate\Routing\Router $route) {
//        $route->get('/', ['as' => 'admin.custom.assets', 'uses' => 'CustomCssJsController@index']);
//        $route->post('save', ['as' => 'admin.custom.assets_save', 'uses' => 'CustomCssJsController@save']);
//    });

    //navigation
    Route::group(['prefix' => 'navigation'], function (Router $route) {
        $route->match(['get', 'post'],'/', ['as' => 'admin.navigation.list', 'uses' => 'NavigationGroupsController@index']);
        $route->get('create', ['as' => 'admin.navigation.create', 'uses' => 'NavigationGroupsController@create']);
        $route->post('create', ['as' => 'admin.navigation.create', 'uses' => 'NavigationGroupsController@store']);
        $route->get('edit/{menu_id}', ['as' => 'admin.navigation.edit', 'uses' => 'NavigationGroupsController@edit']);
        $route->post('edit/{menu_id}', ['as' => 'admin.navigation.edit', 'uses' => 'NavigationGroupsController@update']);
        $route->get('delete/{menu_id}', ['as' => 'admin.navigation.delete', 'uses' => 'NavigationGroupsController@delete']);

        Route::group(['prefix' => 'links/{menu_id}'], function (Router $route) {
            $route->match(['get', 'post'],'/', ['as' => 'admin.navigation-item.list', 'uses' => 'NavigationController@index']);
            $route->get('create-select', ['as' => 'admin.navigation-item.create.select', 'uses' => 'NavigationController@createSelect']);
            $route->get('create/{type}', ['as' => 'admin.navigation-item.create', 'uses' => 'NavigationController@create']);
            $route->post('create/{type}', ['as' => 'admin.navigation-item.create', 'uses' => 'NavigationController@store']);
            $route->get('edit/{navigation_id}', ['as' => 'admin.navigation-item.edit', 'uses' => 'NavigationController@edit']);
            $route->post('edit/{navigation_id}', ['as' => 'admin.navigation-item.edit', 'uses' => 'NavigationController@update']);
            $route->post('reorder', ['as' => 'admin.navigation-item.reorder', 'uses' => 'NavigationController@reorder']);
            $route->get('delete/{navigation_id}', ['as' => 'admin.navigation-item.delete', 'uses' => 'NavigationController@delete']);
            $route->get('reset', ['as' => 'admin.navigation-item.reset', 'uses' => 'NavigationController@reset']);
        });

    });

//    //translations
//    Route::group(['prefix' => 'translations'], function (\Illuminate\Routing\Router $route) {
//        $route->any('list', ['as' => 'admin.translations.list', 'uses' => 'TranslationsController@index']);
//        Route::group(['prefix' => 'action', 'namespace' => 'Translations'], function (\Illuminate\Routing\Router $route) {
//            $route->post('edit', ['as' => 'admin.translations.action.edit', 'uses' => 'ActionController@edit']);
//            $route->post('reset', ['as' => 'admin.translations.action.reset', 'uses' => 'ActionController@reset']);
//            $route->get('reset-all', ['as' => 'admin.translations.action.reset_all', 'uses' => 'ActionController@resetAll']);
//            $route->get('switch_translations/{status?}', ['as' => 'admin.translations.action.switch_translations', 'uses' => 'ActionController@switchTranslations']);
//        });
//    });

    Route::group(['namespace' => 'ThemeSettings'], function (Router $route) {
        $route->group(['prefix' => 'settings'], function (Router $route) {
            $route->match(['get', 'post'], '/', ['as' => 'admin.theme.settings.list', 'uses' => 'SettingsController@index']);
            $route->get('edit/{section}', ['as' => 'admin.theme.settings.edit', 'uses' => 'SettingsController@edit']);
            $route->post('edit/{section}', ['as' => 'admin.theme.settings.edit', 'uses' => 'SettingsController@update']);
        });

        $route->group(['prefix' => 'theme-editor'], function (Router $route) {
            $route->get('/', ['as' => 'admin.theme.editor.list', 'uses' => 'EditorController@index']);
//            $route->get('edit/{path}', ['as' => 'admin.theme.editor.edit', 'uses' => 'EditorController@edit'])
//                ->where('path', '(.*)\.liquid$');
            $route->get('ajax', ['as' => 'admin.theme.editor.ajax', 'uses' => 'EditorController@ajax']);
            $route->get('close', ['as' => 'admin.theme.editor.close', 'uses' => 'EditorController@close']);
        });

        $route->group(['prefix' => 'theme-builder', 'namespace' => 'ThemeBuilder'], function (Router $route) {
            $route->match(['get', 'post'], '/', ['as' => 'admin.theme-builder.list', 'uses' => 'IndexController@index']);
        });
    });
});

//selections
Route::group(['prefix' => 'collections', 'namespace' => 'Collections'], function () {
    Route::match(['get', 'post'], '/', ['as' => 'admin.collections.list', 'uses' => 'CollectionController@index']);
    Route::get('create', ['as' => 'admin.collections.create', 'uses' => 'CollectionController@form']);
    Route::post('create', ['as' => 'admin.collections.create', 'uses' => 'CollectionController@store']);
    Route::get('edit/{selection_id}', ['as' => 'admin.collections.edit', 'uses' => 'CollectionController@form']);
    Route::post('edit/{selection_id}', ['as' => 'admin.collections.edit', 'uses' => 'CollectionController@update']);
});

forgetRoute('admin.seo.meta_titles');
//settings
Route::group(['prefix' => 'marketing'], function () {
    Route::group(['namespace' => 'Marketing', 'prefix' => 'seo/meta'], function () {
        Route::match(['get', 'post'], '/', ['as' => 'admin.seo.meta_titles', 'uses' => 'MetaTitlesController@index']);
        Route::get('edit/{key}', ['as' => 'admin.seo.meta_titles.edit', 'uses' => 'MetaTitlesController@edit']);
        Route::post('edit/{key}', ['as' => 'admin.seo.meta_titles.edit', 'uses' => 'MetaTitlesController@update']);
    });
});
