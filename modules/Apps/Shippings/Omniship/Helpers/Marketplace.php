<?php
/**
 * Created by PhpStorm.
 * User: joro
 * Date: 31.10.2017 г.
 * Time: 15:18 ч.
 */

namespace Modules\Apps\Shippings\Omniship\Helpers;

use ArrayAccess;
use JsonSerializable;
use Omniship\Interfaces\ArrayableInterface;
use Omniship\Interfaces\ComponentInterface;
use Omniship\Interfaces\JsonableInterface;
use Omniship\Traits\Parameters;
use Omniship\Traits\ArrayAccess AS TraitArrayAccess;

class Marketplace implements ComponentInterface, ArrayableInterface, JsonSerializable, JsonableInterface, ArrayAccess
{

    use Parameters, TraitArrayAccess;

    /**
     * Create a new item with the specified parameters
     *
     * @param array|null $parameters An array of parameters to set on the new object
     */
    public function __construct(array $parameters = null)
    {
        $this->initialize($parameters);
    }

    /**
     * Get city id
     */
    public function getId(): int|string|null
    {
        return $this->getParameter('id');
    }

    /**
     * Set city id
     * @param $value
     * @return $this
     */
    public function setId($value)
    {
        return $this->setParameter('id', $value);
    }

    /**
     * Get city name
     */
    public function getName(): ?string
    {
        return $this->getParameter('name');
    }

    /**
     * Set city name
     * @param $value
     * @return $this
     */
    public function setName($value)
    {
        return $this->setParameter('name', $value);
    }
}
