<?php

declare(strict_types=1);

namespace Modules\Apps\Shippings\Dpdbulgaria\Http\Controllers;

use App\Common\DateTimeFormat;
use App\Helper\Store\CartTotal;
use App\Models\Gateway\Currency;
use App\Models\Tax\Tax;
use Exception;
use App\Exceptions\Error;
use App\Helper\Format;
use App\Helper\OmniShip\Address;
use App\Helper\SiteCp\Modal;
use App\Helper\YesNo;
use App\Locale\Weight;
use App\Models\Apps\SiteRequiredSetting;
use App\Models\Order\Order;
use App\Models\Order\OrderFulfillment;
use App\Models\Router\Logs;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Modules\Apps\Shippings\Dpdbulgaria\Http\Requests\ConfigurationRequest;
use Modules\Apps\Shippings\Dpdbulgaria\Http\Requests\PickupCpRequest;
use Modules\Apps\Shippings\Dpdbulgaria\Http\Requests\WaybillRequest;
use Modules\Apps\Shippings\Dpdbulgaria\Manager;
use Modules\Apps\Shippings\Dpdbulgaria\Models\Offices;
use Modules\Apps\Shippings\Omniship\Http\Controllers\AbstractShippingController;
use Omniship\Common\Bill\Create;
use Omniship\Consts;
use Throwable;

class DpdbulgariaController extends AbstractShippingController
{
    protected $manager;

    /**
     * @var string
     */
    protected $locale;

    /**
     * @param Modules\Apps\Shippings\Dpdbulgaria\Manager $manager
     * @return mixed
     */
    public function __construct(Manager $manager)
    {
        $this->manager = $manager;
        $this->locale = locale();

        parent::__construct($manager);
    }

    /**
     * @return array
     * @throws Error
     */
    #[\Override]
    public function getMeta(): array
    {
        $active_session = $this->manager->isActiveSession();
        $provider = $this->manager->getProvider();
        if ($active_session) {
            $clients = $this->formatClientAddress($this->manager->getSetting('address_id'));
        } else {
            $clients = [];
        }

        $types = [];
        foreach ($this->manager::PRICING_TYPES as $type) {
            $types[$type] = __($this->manager::APP_KEY . '::' . $this->manager::APP_KEY . '.' . $type);
        }

        $settings = [
            'clients' => array_map(fn($client): array => [
                'id' => (string)$client['clientId'],
                'name' => $client['name'],
            ], $clients),
            'boxes' => json_encode($provider->boxes()->get(['boxes.id', 'boxes.name'])),
        ];

        return $settings;
    }

    /**
     * @param $client_id
     * @return array
     */
    protected function formatClientAddress($client_id)
    {
        if ($addresses = $this->manager->getSetting('client_address')) {
            if (!empty($addresses)) {
                return $addresses;
            }
        }

        $result = $this->manager->getClient()->getClienAdrresses($client_id);
        $address = [];
        if (!empty($result)) {
            foreach (collect($result) as $res) {
                $address[] = [
                    'clientId' => $res->clientId,
                    'name' => $res->clientName . ' - ' . $res->objectName . ' (' . $res->address->fullAddressString . ')',
                    'address' => $res->address->fullAddressString,
                    'phone' => \Illuminate\Support\Arr::first($res->phones)->number ?? null
                ];
            }

            $this->manager->updateSetting('client_address', $address);
        }

        return $address;
    }

    /**
     * @param Request $request
     * @return Response
     * @throws Error
     */
    public function getOffices(Request $request): \Illuminate\Http\Response
    {
        try {
            $offices = $this->manager->findOffices($request->query('query'), $request->query('city_id'));
            /** @var Collection $offices */
            $return = ['results' => $offices, 'more' => false];
        } catch (Exception $exception) {
            $return['error'] = $exception->getMessage();
        }

        return new Response($return);

    }


    /**
     * @param Request $request
     * @param $type
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|Response
     */
    public function getLockers(Request $request, $type = null): \Illuminate\Http\Response
    {
        try {
            $offices = $this->manager->findLockers($request->query('query'), $request->query('city_id'));
            if (in_array($type, [Consts::OFFICE_TYPE_APT, Consts::OFFICE_TYPE_OFFICE])) {
                /** @var Collection $offices */
                $offices = $offices->where('type', $type);
            }

            $return = ['results' => $this->_format($offices), 'more' => false];
        } catch (Exception $exception) {
            $return['error'] = $exception->getMessage();
        }

        return new Response($return);
    }

    /**
     * @param ConfigurationRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     * @throws Throwable
     */
    public function saveConfig(ConfigurationRequest $request)
    {
        $inputKey = $this->manager::APP_KEY;
        try {
            \Illuminate\Support\Facades\DB::transaction(function () use ($request, $inputKey): void {
                $post = [];
                $post['client_id'] = $this->manager->getSetting('client_id');
                if ($this->manager->getSetting('contract_info')) {
                    $post['contract_info'] = $this->manager->getSetting('contract_info');
                }

                if ($this->manager->getSetting('client_address')) {
                    $post['client_address'] = $this->manager->getSetting('client_address');
                }

                if ($this->manager->getSetting('client_services')) {
                    $post['client_services'] = $this->manager->getSetting('client_services');
                }

                if ($this->manager->getSetting('client_addresses_' . $post['client_id'])) {
                    $post['client_addresses_' . $post['client_id']] = $this->manager->getSetting('client_addresses_' . $post['client_id']);
                }

                $this->manager->emptySettings();
                $post = array_merge($request->input($inputKey, []), $post);
                if (isset($post['post_boxes'])) {
                    $tmp = [];
                    foreach ($post['post_boxes']['to'] as $row => $weight) {
                        $box = $post['post_boxes']['type'][$row];
                        if ($box && in_array($box, ['XS', 'S', 'M', 'L']) && $weight > 0) {
                            $tmp[] = [
                                'weight' => number_format($weight, 2, '.', ''),
                                'type' => $box
                            ];
                        }
                    }

                    $post['post_boxes'] = collect($tmp)->keyBy('type')->values()->all();
                } else {
                    $post['post_boxes'] = [];
                }

                if (!$this->manager->supportSyncPayments()) {
                    $post['sync_payments'] = 0;
                }

                $getContract = $this->manager->getClient()->getContractInfo();
                $allowMoneyTransfer = false;
                if ($getContract && isset($getContract->cod) && $getContract->cod->moneyTransferAllowed) {
                    $allowMoneyTransfer = true;
                }

                if (!$allowMoneyTransfer) {
                    $post['money_transfer'] = 0;
                }

                $this->manager->updateSettings($post);
                $this->manager->updateActive((int)$request->input($inputKey . '.status'));

                $provider = $this->manager->getProvider();

                $provider->edit([
                    'name' => $request->input($inputKey . '.provider_name'),
                    'type' => null,
                    'target' => $request->input($inputKey . '.target') ?: 'regions',
                    'geo_zone_id' => $request->input($inputKey . '.geo_zone_id'),
                    'active' => (int)$request->input($inputKey . '.status'),
                ]);

                $provider->attachRates($request->input($inputKey, []));
                $provider->attachBoxes($request->input($inputKey . '.boxes', []));
                $provider->attachPayments(!$request->has('payments_all'), $request->input('payments_providers', []));
            });

            try {
                SiteRequiredSetting::sync();
            } catch (Throwable $e) {
            }

            if ($this->manager->getSetting('sync_payments')) {
                \OmniShip::startSyncPayments();
            }

            $this->manager->getProvider()->uploadImageSplFileInfo($request->file('image'));

            return $this->settings();
        } catch (Exception $exception) {
            return response()->json([
                'message' => $exception->getMessage(),
            ], 503);
        }
    }

    /**
     * @param Request $request
     * @param $order_id
     * @return Response
     * @throws Error
     * @throws Throwable
     */
    public function waybill(Request $request, $order_id)
    {
        $order = Order::with(['shipping', 'shippingAddress', 'products'])->find($order_id);

        if (!$order || $order->omniship_provider != $this->manager->getKey()) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        $this->manager->setOrder($order);
        $order->setAttribute('price_total_formatted', Format::moneyInput($order->price_total));
        $order->setAttribute('price_products_subtotal_formatted', Format::moneyInput($order->price_products_subtotal));
        $order->setAttribute('price_subtotal_formatted', Format::moneyInput($order->price_subtotal));

        $settings = $this->manager->getSettings();

        $currency = site('currency');
        $addresses = collect();
        if (
            ($order->shippingAddress->office_id ?? false) &&
            ($office = $this->manager->getOfficeById($order->shippingAddress->office_id))
        ) {
            $isApt = $office->getType() == Consts::OFFICE_TYPE_APT;
        }

        $calculate = $this->recalculate($request, $order, false, $params);
        $weight_unit = Weight::getSignMetric();
        $weight_unit_short = Weight::getSignShort();
        $options_before_payment = [
            'no_option' => __('No options'),
            'TEST' => __('Test before payement'),
            'OPEN' => __('Open before payment'),
        ];

        $dateNow = Carbon::now()->timezone('Europe/Sofia');
        if ($dateNow->format('H:i') < 17 && !$dateNow->isWeekend()) {
            $send_date = $dateNow->format('d.m.Y');
        } else {
            $send_date = $dateNow->addDay()->format('d.m.Y');
        }

        return \Illuminate\Support\Facades\View::modal($this->manager::APP_KEY . '::waybill', [
            'calculate' => $calculate,
            'clients' => $this->formatClientAddress($this->manager->getSetting('address_id')),
            'sender_addresses' => $addresses,
            'date_format' => DateTimeFormat::$date_formats[setting('date_format')]['format_js'],
            'send_date' => $send_date,
            'order' => $order,
            'order_id' => $order_id,
            'payed' => in_array($order->status, ['paid', 'completed']),
            'currency' => $currency,
            'settings' => $settings,
            'address' => $order->shippingAddress ? $order->shippingAddress->address : new Address(),
            'contents' => $order->content,
            'order_contents' => __('omniship.waybill.enter.contents', ['order_id' => $order->order_number], $this->manager->getManager()->getLanguageCode()),
            'cash_on_delivery_amount' => $params['cash_on_delivery_amount'],
            'declared_amount' => $order->getInsuranceAmountInput(),
            'side_sender' => in_array($this->manager->getPricingType(), ['fixed_price', 'fixed_weight']),
            'waybill_sides' => $order->getWaybillSides(),
            'return_shipment' => !empty($order->meta_pluck['return_shipment']) ? $order->meta_pluck['return_shipment'] : $this->manager->getSetting('return_shipment'),
            'side' => $order->meta_pluck['side'] ?? $settings->get('side'),
            'to_office' => !is_null($order->shippingAddress->office_id) ?? false,
//            'weight' =>  is_numeric($weight) ? number_format(floatval($weight), 3) : null,
            'weight' => $this->manager->getLatestRequest() ? $this->manager->getLatestRequest()->getWeight() : $this->manager->getWeightFromItems($this->manager->convertOrderProductsToBag($order->products), $this->manager->getDefaultWeight()),
            'meta_data' => $order->meta,
            'weight_unit' => $weight_unit,
            'weight_unit_short' => $weight_unit_short,
            'boxes' => json_decode((string)$order->meta_pluck->get('boxes')) ?? [],
            'customer_note' => $order->content,
            'options_before_payment' => $options_before_payment,
            'insurance_amount' => $params['insurance_amount'],
            'payment_method' => $order->payment->provider,
            'option_before_payment' => str_upper($settings->get('option_before_payment')),
            'item_count' => 1,
            'services' => $this->manager->getServices(),
            'declared_value' => $this->manager->getSetting('declared_value'),
            'is_vat_exist' => Tax::vat()->exists(),
            //'inputPrefix' => $this->manager::APP_KEY,
        ], null,
            Modal::cancelButton()
//            . Modal::button(__('shipping.recalculate'), 'btn-default js-btn-calculate')
            . Modal::button(__('shipping.generate_bill_of_lading'), 'btn-primary js-generate-bill-of-lading'));
    }

    /**
     * @param Order $order
     * @param Request $request
     * @param $save_address
     * @param $waybill
     * @return array
     * @throws Error
     */
    #[\Override]
    protected function initCalculateAndGenerate(Order $order, Request $request, $save_address = false, $waybill = false): array
    {
        if (!$this->manager->getOrder()) {
            $this->manager->setOrder($order);
        }

        if ($request->isMethod('post')) {
            if ($request->input('waybill.weight') < 0.1) {
                throw new Error(__('Weight must be at least 0.1'));
            }

            $order->updateMeta([
                'contents' => $request->input('waybill.contents'),
                'weight' => $request->input('waybill.weight'),
                'packing' => $request->input('waybill.packing'),
                'items_count' => $request->input('waybill.items_count', 1),
                'service_id' => $request->input('waybill.service_id'),
                'customer_note' => $request->input('waybill.customer_note'),
                'package' => json_encode($request->input('waybill.package')),
                'special_delivery_requirements' => $request->input('waybill.special_delivery_requirements'),
                'option_before_payment' => $request->input('waybill.option_before_payment'),
                'instruction_returns' => $request->input('waybill.instruction_returns'),
                'insurance' => $request->input('waybill.insurance'),
                'fragile' => $request->input('waybill.fragile'),
                'insurance_amount' => $request->input('waybill.totalInsurance'),
                'back_documents' => $request->input('waybill.back_documents'),
                'package_id' => $request->input('waybill.package_id'),
                'documents' => $request->input('waybill.documents'),
                'cod' => $request->input('waybill.cod'),
                'side' => $request->input('waybill.side'),
                'custom_pieces' => json_encode($request->input('custom_pieces')),
            ]);
            if ($order->meta_pluck->get('cod')) {
                $order->updateMeta(['cod_manual' => $request->input('waybill.total')]);
            } else {
                $order->removeMeta('cod_manual');
            }
        }

        if (!$request->isMethod('post')) {
            $allowed_services = (array)$this->manager->getSetting('allowed_methods');
            $service_id = $order->meta_pluck->get('service_id');
            if (!in_array($service_id, $allowed_services)) {
                $service_id = Arr::first($allowed_services);
            }

            $insurance_amount = $order->meta_pluck->get('insurance') ? $order->getInsuranceAmountInput() : 0;
        } else {
            // $firs_allowed_tacking_day = Carbon::createFromFormat(DateTimeFormat::$date_formats[setting('date_format')]['format'], $request->input('send_date'));
            $insurance_amount = $request->input('waybill.insurance') ? $request->input('waybill.totalInsurance') : 0;
        }

        $cash_on_delivery_amount = 0;

        if ($this->manager::APP_KEY == 'dpdbulgaria') {
            $currency = 'BGN';
        } else {
            $currency = 'RON';
        }

        if (!empty($order->shippingAddress->office_id)) {
            $reciver_address = new Address([
                'office' => $this->manager->getOfficeById($order->shippingAddress->office_id),
                'country' => $this->manager->getCountryById($order->shippingAddress->country_id),
                //  'city' => $this->manager->getCityById($order->shippingAddress->city_id),
            ]);
            $currency = $reciver_address->getCountry()->getCurrency();
        } else {
            $reciver_address = $this->manager->getReceiverAddress($order->shippingAddress->address);
            $currency = $reciver_address->getCountry()->getCurrency();
        }

        if ($order->payment->provider == 'cod') {
            if ($order->meta_pluck->get('cod')) {
                $cash_on_delivery_amount = $order->meta_pluck->get('cod_manual');
            } elseif ($order->isCashOnDelivery()) {
                $cash_on_delivery_amount = Format::moneyInput($this->manager->getCashOnDeliveryTotal($order));
            }
            $cash_on_delivery_amount = floatval(str_replace(',', '.', (string)$cash_on_delivery_amount));

            $cash_on_delivery_amount = number_format(Currency::convert($cash_on_delivery_amount, site('currency'), $currency), 2, '.', '');
        }

        $instruction_returns = 0;
        if ($request->method() == 'POST') {
            $itemCount = $request->input('waybill.items_count');
            if (!empty($request->input('waybill.instruction_returns'))) {
                $instruction_returns = 1;
            } else {
                $instruction_returns = 0;
            }

            $sender_address = $this->manager->getSenderAddress();
            $sender_address->setId($request->input($this->manager::APP_KEY . '.address_id', $this->manager->getSetting('address_id')));
            if($request->input('dpdbulgaria.address_id')){
                $sender_address->setId($request->input('dpdbulgaria.address_id'));
            }
            if($request->input('dpdbulgaria.client_name')){
                $explodeName = explode(' ', $request->input('dpdbulgaria.client_name'));
                if(!empty($explodeName[0])){
                    $sender_address->setFirstName($explodeName[0]);
                }
                if(!empty($explodeName[1])){
                    $sender_address->setLastName($explodeName[1]);
                }
            }
            if($request->input('dpdbulgaria.client_phone')){
                $sender_address->setPhone($request->input('dpdbulgaria.client_phone'));
            }
        } else {
            $instruction_returns = $this->manager->getSetting('instruction_returns');
            $itemCount = null;
            $sender_address = $this->manager->getSenderAddress();
        }

        if ($request->isMethod('post')) {
            $firs_allowed_tacking_day = Carbon::createFromFormat(DateTimeFormat::$date_formats[setting('date_format')]['format'], $request->input('send_date'));
        }


        $fiscalReceiptItems = [];
        if ($request->method() == 'POST' && $request->input('waybill.fiscal_receipt', 0) == 1) {
            foreach ($order->products as $product) {
                if ($product->getTotalPriceWithOptionsAfterDiscountsWithoutVatWithModification() > 0) {
                    if ($product->getVat() && $vatGroup = $this->manager->getVatGroup($product->getVat()->getTaxValue() > 0 ? $product->getVat()->getTaxValue() / 100 : 0)) {
                        if (is_null($vatGroup)) {
                            $fiscalReceiptItems = [];
                            break;
                        } else {
                            $fiscalReceiptItems[] = [
                                'description' => mb_substr($product->getName(), 0, 50, 'UTF-8'),
                                'vatGroup' => $vatGroup,
                                'amount' => round($product->getTotalPriceWithOptionsAfterDiscountsWithoutVatWithModification() / 100, 2),
                                'amountWithVat' => round($product->getTotalPriceWithOptionsAfterDiscountsWithModification() / 100, 2)
                            ];
                        }
                    }
                }
            }
        }


        if ($request->input('waybill.side') == 'SENDER' && $fiscalReceiptItems !== [] && $order->shipping->order_amount_with_vat > 0) {
            if ($order->shipping->order_amount_without_vat > 0) {
                $vatAmount = $order->shipping->order_amount_with_vat - $order->shipping->order_amount_without_vat;
                if ($vatAmount > 0) {
                    $vatPercentage = round(($vatAmount / $order->shipping->order_amount_without_vat) * 100);
                }
                $fiscalReceiptItems[] = [
                    'description' => $order->shipping->provider_name,
                    'vatGroup' => $order->shipping->order_amount_with_vat > $order->shipping->order_amount_without_vat ? $this->manager->getVatGroup((int)$vatPercentage ?? 0) : 'А',
                    'amount' => Format::moneyInput($order->shipping->order_amount_without_vat),
                    'amountWithVat' => Format::moneyInput($order->shipping->order_amount_with_vat)
                ];
            }
        }

        $totals = $order->getTotals();

        $taxes = $totals->only(['tax.before', 'tax.after'])
            ->collapse()->where('use_for_total', true);
        /** @var CartTotal $tax */
        foreach ($taxes as $tax) {
            if ($tax->price > 0) {
                $vatAmount = $tax->price - $tax->price_without_vat;
                if ($vatAmount > 0) {
                    $vatPercentage = round(($vatAmount / $tax->price_without_vat) * 100);
                }

                $fiscalReceiptItems[] = [
                    'description' => $tax->getInvoiceName(),
                    'vatGroup' => $tax->price > $tax->price_without_vat ? $this->manager->getVatGroup((int)$vatPercentage ?? 0) : 'А',
                    'amount' => Format::moneyInput($tax->getPriceWithoutVat()),
                    'amountWithVat' => Format::moneyInput($tax->getPrice())
                ];
            }
        }

        $params = [
            'shipment_date' => $firs_allowed_tacking_day ?? null,
            'transaction_id' => $order->id, //create
            'service_id' => $request->isMethod('post') ? $request->input('waybill.service_id') : $order->meta_pluck->get('service_id'), //create
            'client_note' => Str::substr(str_replace(['%', '&'], '', (string)$request->input('waybill.customer_note')), 0, 200), //create
            'content' => Str::substr($request->input('waybill.contents'), 0, 100), //create
            'receiver_email' => $order->customer_email,
            'receiver_address' => $reciver_address,
            'sender_address' => $sender_address,
            'items' => ($items = $this->manager->convertOrderProductsToBag($order->products)),
            'number_of_pieces' => $request->input('waybill.items_count') ?: 1,
            'payer' => $request->isMethod('post') ? $request->input('waybill.side') : $this->manager->getSetting('side'),
            'option_before_payment' => $request->method() == 'POST' ? $request->input('waybill.option_before_payment') : $this->manager->getSetting('option_before_payment'),
            'instruction_returns' => $instruction_returns,
            'back_documents' => $request->method() == 'POST' ? $request->input('waybill.back_documents') : $order->meta_pluck->get('back_documents'),
            'back_receipt' => $request->method() == 'POST' ? $request->input('waybill.back_receipt') : $order->meta_pluck->get('back_receipt'),
            'declared_amount' => $request->method() == 'POST' ? $request->input('waybill.totalInsurance') : $order->meta_pluck->get('totalInsurance'),
            'other_parameters' => [
                'fragile' => $request->method() == 'POST' ? $request->input('waybill.fragile') : $order->meta_pluck->get('fragile'),
                'documents' => $request->method() == 'POST' ? $request->input('waybill.documents') : $order->meta_pluck->get('documents'),
                'return_pay' => $request->method() == 'POST' ? $request->input('waybill.instruction_returns', 0) : (int)$this->manager->getSetting('instruction_returns'),
                'return_service' => $request->method() == 'POST' ? $request->input('waybill.return_service') : $this->manager->getSetting('instruction_returns_service'),
                'pos_enabled' => $request->method() == 'POST' ? $request->input('waybill.pos_enabled') : $this->manager->getSetting('pos_enabled'),
                'services' => [$order->meta_pluck->get('service_id')],
                'items_count' => $itemCount,
                'saturday_delivery' => $request->method() == 'POST' ? $request->input('waybill.saturday_delivery') == 1 ? true : false : false,
                'return_voucher' => $request->method() == 'POST' ? $request->input('waybill.return_voucher') : null,
                'return_voucher_service' => $request->method() == 'POST' ? $request->input('waybill.return_voucher_service') : null,
                'return_voucher_payer' => $request->method() == 'POST' ? $request->input('waybill.return_voucher_payer') : null,
                'return_voucher_validity' => $request->method() == 'POST' ? (int)$request->input('waybill.return_voucher_validity') : null,
                'address_id' => $request->method() == 'POST' ? $request->input($this->manager::APP_KEY . '.address_id', $this->manager->getSetting('address_id')) : $this->manager->getSetting('address_id'),
                'content_package' => $request->method() == 'POST' ? $request->input('waybill.packing', 'BOX') : $this->manager->getSetting('packing', 'BOX'),
                'fiscal_receipt' => $request->method() == 'POST' ? $request->input('waybill.fiscal_receipt', 0) : 0,
                'fiscal_receipt_items' => $fiscalReceiptItems,
                'order_hash' => setting('order_id_display') == 'increment_hash' ? $order->increment_hash : $order->id,
                'cod_ref2' => $this->manager->getSetting('cod_ref2', 0),
                'payer_id' => $this->manager->getSetting('payer_id'),
                'item_sizes' => (int)$this->manager->getSetting('item_sizes'),
            ],
            'insurance_amount' => $insurance_amount,
            'cash_on_delivery_amount' => $cash_on_delivery_amount,
            'money_transfer' => $this->manager->getSetting('money_transfer', 0) == 1,
            'weight' => $request->method() == 'POST' ? $request->input('waybill.weight') : Weight::input($order->products->sum('weight')),
            'package_type' => $request->method() == 'POST' ? $request->input('waybill.packing') : $this->manager->getSetting('packing'),
            'is_documents' => $request->method() == 'POST' ? $request->input('waybill.documents') : (bool)$this->manager->getSetting('documents'),
            'items_count' => $request->method() == 'POST' ? $request->input('waybill.items_count') : null,
            'pieces' => array_map(function ($data) {
                if (!empty($data->name)) {
                    unset($data->name);
                }

                return $data;
            }, (array)json_decode((string)$order->meta_pluck->get('boxes'))),
        ];

        if ($params['number_of_pieces'] > 1) {
            $params['pieces'] = $request->input('custom_pieces');
        }

        if ($request->isMethod('post')) {
            if (!empty($instruction_returns = $request->input('waybill.instruction_returns'))) {
                $params['instruction_returns'] = $instruction_returns;
            } else {
                $params['instruction_returns'] = '';
            }
        }

//        if ($request->isMethod('post')) {
//            if (in_array($option = $request->input('waybill.option_before_payment'), [Consts::OPTION_BEFORE_PAYMENT_OPEN, Consts::OPTION_BEFORE_PAYMENT_TEST])) {
//                $params['option_before_payment'] = $option;
//            } else {
//                $params['option_before_payment'] = false;
//            }
//        } elseif (!is_null($obp = $this->manager->getOptionBeforePayment())) {
//            $params['option_before_payment'] = $obp;
//        }
//
//        if(!$this->manager->allowOptionBeforePayment($address->address)) {
//            $params['option_before_payment'] = false;
//        }

        return $params;
    }

    /**
     * @param PickupCpRequest $request
     * @param $order_id
     * @param $type
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|Response
     * @throws Error
     */
    public function changePickupSave(PickupCpRequest $request, $order_id, string $type)
    {
        return $this->changePickupSaveAbstract($request, $order_id, $type);
    }

    /**
     * @param CalculateCpRequest $request
     * @param $order_id
     * @return Response
     * @throws Error
     * @throws Throwable
     */
    public function calculate(Request $request, $order_id): \Illuminate\Http\Response
    {
        $order = Order::find($order_id);
        if (!$order) {
            return new Response([
                'status' => 'error',
                'msg' => __('order.status.error.order_not_found')
            ]);
        }

        $calculate = $this->recalculate($request, $order, true);

        $return = ['status' => 'error'];
        $error = $this->manager->getClient()->getError();
        if (!$error) {
            if ($calculate->count()) {
                $return['pickup'] = $request->input('waybill.pickup');
                $return['status'] = 'success';
                $return['calculate'] = $calculate;
            } else {
                $return['msg'] = 'За съжаление, не можем да намерим услуга на DPD, която да обслужва подадения от вас адрес. Моля, изберете/въведете друг адрес за доставка';
            }
        } else {
            $return['msg'] = $error;
        }

        if (!empty($return['msg'])) {
            //$return['field'] = 'js-serices-place-holder';
        }

        return new Response($return);
    }

    /**
     * @param Modules\Apps\Shippings\Dpdbulgaria\Http\Requests\WaybillRequest $request
     * @param mixed $order_id
     * @return mixed
     */
    public function waybillSave(WaybillRequest $request, $order_id)
    {
        /** @var Order $order */
        $order = Order::with(['shipping', 'shippingAddress', 'products'])->find($order_id);

        if (!$order || ($order->omniship_provider != $this->manager->getKey())) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        $waybillStd = new \stdClass();
        $waybillStd->waybill_id = null;

        $request_response = new \stdClass();
        try {
            /** @var Create $waybill */
            $waybill = \Illuminate\Support\Facades\DB::transaction(function () use ($order, $request, $waybillStd, &$request_response) {
                $params = $this->initCalculateAndGenerate($order, $request, true);
                $params['receiver_address'] = $this->manager->getReceiverAddress($order->shippingAddress->address);
                $params['sender_address'] = $this->manager->getSenderAddress();
                $result = $this->manager->createBillOfLading($params, $request_response);
                if (!$result) {
                    throw new Error((string)$this->manager->getClient()->getError());
                } elseif ($result->getError()) {
                    throw new Error((string)$result->getError());
                }

                $categories = $order->products->where('digital', YesNo::False)->pluck('category_id')->toArray();
                $result = $this->manager->formatCreate(Format::moneyInput($order->price_total - $order->shipping->order_amount), $result, null, $categories);

                $explode_bolId = explode('-', (string)$result->getBolId());
                $waybillStd->waybill_id = \Illuminate\Support\Arr::first($explode_bolId);

                $services = $this->manager->getServices();
                if (!is_array($services)) {
                    $services = collect($this->manager->getServices()->toArray())->pluck('name', 'id');
                } else {
                    $services = collect($services)->pluck('name', 'id');
                }

                $order->updateMeta([
                    'integration' => $this->manager->getKey(),
                    'service_id' => $request->input('waybill.service_id'),
                    'service_name' => $services->get($request->input('waybill.service_id')),
                    'bol_id' => $waybillStd->waybill_id,
                    'total' => Format::toIntegerPrice($result->getTotal()),
                    'original_total' => $result->getParameter('original_price') != $result->getTotal() ? Format::toIntegerPrice($result->getParameter('original_price')) : null,
                    'currency' => $result->getCurrency(),
                    'special_delivery_requirements' => $request->input('waybill.special_delivery_requirements'),
                    'cod' => $request->input('waybill.cod'),
                    'option_before_payment' => $request->input('waybill.option_before_payment'),
                    'instruction_returns' => $request->input('waybill.instruction_returns'),
                    'back_documents' => $request->input('waybill.back_documents'),
                    'insurance' => $request->input('waybill.insurance'),
                    'fragile' => $request->input('waybill.fragile'),
                    'side' => $params['payer'],
                    'omniship.credentials' => json_encode($this->manager->getCredentials()),
                    'money_transfer' => $this->manager->isMoneyTransfer(),
                    'parcels_number' => $result->getBolId(),
                    'is_return_voucher' => $request->input('waybill.return_voucher'),
                ]);

                $fulfilled = $order->changeStatus('fulfilled', true, [
                    'shipping_tracking_number' => $waybillStd->waybill_id,
                    'shipping_date_delivery' => $result->getEstimatedDeliveryDate(),
                    'shipping_date_expedition' => $result->getPickupDate(),
                    'shipping_tracking_url' => $this->manager->getSetting('tracking', 'provider') == 'cloudcart' ? \LinkerCp::siteFullLink('/tracking/' . $waybillStd->waybill_id) : $this->manager->trackingUrl($waybillStd->waybill_id)
                ]);
                if ($fulfilled && $fulfilled instanceof OrderFulfillment) {
                    $fulfilled->update([
                        'shipping_tracking_number' => $waybillStd->waybill_id,
                        'shipping_date_delivery' => $result->getEstimatedDeliveryDate(),
                        'shipping_date_expedition' => $result->getPickupDate(),
                        'shipping_tracking_url' => $this->manager->getSetting('tracking', 'provider') == 'cloudcart' ? \LinkerCp::siteFullLink('/tracking/' . $waybillStd->waybill_id) : $this->manager->trackingUrl($waybillStd->waybill_id)
                    ]);
                }

                if (!!request()->input('sync_order_and_provider_amount') && (!in_array($order->status, ['completed', 'paid']) || $order->meta_pluck->get('recalculate_locked', 0) == 0)) {
                    $shipping_provider = $order->initShippingQuote(null, [
                        'service_id' => $params['service_id'],
                        'overwrite_price' => true,
                        'price' => Format::toIntegerPrice($result->getTotal())
                    ]);

                    $order->updateShippingFromInitQuote($shipping_provider);

                    $order->updateOrderTotals();
                }

                return $result;
            });

            $this->logWaybill($request_response, $order);

            return response(array_merge(['status' => 'success'], $waybill->toArray()));
        } catch (Exception $exception) {
            if (!empty($waybillStd->waybill_id)) {
                $this->manager->cancelBillOfLading($waybillStd->waybill_id);
            }

            if (!($exception instanceof Error)) {
                Logs::createFromThrowable($exception, 'DPD Romania waybillSave');
            }

            $this->logWaybill($request_response, $order);

            return response([
                'status' => 'error',
                'field' => null,
                'msg' => $this->manager->translateError($exception->getMessage())
            ]);
        }
    }

    /**
     * @param $order_id
     * @param $type
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|Response
     * @throws Error
     */
    public function pdf($order_id, $type, $is_return = 0)
    {
        /** @var Order $order */
        $order = Order::find($order_id);

        if (!$order || !$order->getBolId()) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        $this->manager->getManager()->setOtherParameters('printer_type', $type);
        if ($is_return) {
            $this->manager->getManager()->setOtherParameters('return_voucher', true);
        }

        $pdf = $this->manager->getPdf($order->meta_pluck->get('parcels_number'));
        if (!$pdf) {
            throw new Error($this->manager->getClient()->getError() ? : 'PDF is not generated');
        }

        return response($pdf)
            ->header('content-type', 'application/pdf');
    }

    /**
     * @param Illuminate\Http\Request $request
     * @param mixed $order_id
     * @return mixed
     */
    public function pdfSelect(Request $request, $order_id)
    {
        $type = null;
        if ($this->manager->getSetting('speedy_print_size')) {
            $type = $this->manager->getSetting('speedy_print_size');
        }

        /** @var Order $order */
        $order = Order::find($order_id);

        if (!$order || !$order->getBolId()) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        $method = $request->ajax() ? 'modal' : 'make';

        return View::$method($this->manager::APP_KEY . '::pdf-select', [
            'order' => $order,
            'type' => $type,
            'return_voucher' => $order->meta_pluck->get('is_return_voucher', 0),
        ], $request->ajax() ? null : [], false);
    }

    #[\Override]
    public function getSelectsOptions(): array
    {
        $values = parent::getSelectsOptions();
        $active_session = $this->manager->isActiveSession();
        if ($active_session) {
            $clients = $this->formatClientAddress($this->manager->getSetting('address_id'));
        } else {
            $clients = [];
        }

        return array_merge($values, [
            'clients' => array_map(fn($client): array => [
                'id' => (string)$client['clientId'],
                'name' => $client['name'],
            ], $clients),
        ]);
    }

    /**
     * @param Request $request
     * @return Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function bulkPrint(Request $request)
    {
        $this->validate($request, [
            'order_ids' => 'required|array',
            'order_ids.*' => 'required|integer',
            'type' => 'required|string|in:A4,A6',
            'is_return' => 'boolean',
        ], [
            'order_ids.required' => 'Orders is required',
            'order_ids.*.required' => 'Order id is required',
            'order_ids.*.integer' => 'Order id must be integer',
            'type.required' => 'Type is required',
            'type.string' => 'Type must be string',
            'type.in' => 'Type must be A4 or A6',
            'is_return.boolean' => 'Is return must be boolean',
        ]);
        $orders = Order::with('fulfillment')->whereHas('shipping', function ($query): void {
            $query->where('provider_id', $this->manager->getProvider()->getKey());
        })->where('status_fulfillment', 'fulfilled')->whereIn('id', $request->input('order_ids'))->get();
        $bold_ids = $orders->map(fn(Order $order) => $order->fulfillment->shipping_tracking_number)->filter()->toArray();

        $this->manager->getManager()->setOtherParameters('printer_type', $request->input('type'));
        if ($request->input('is_return')) {
            $this->manager->getManager()->setOtherParameters('return_voucher', true);
        }

        try {
            $pdf = $this->manager->getPdf($bold_ids);
        } catch (Throwable $throwable) {
            return response()->json([
                'message' => $throwable->getMessage()
            ], 422);
        }

        if (!$pdf) {
            throw new Error($this->manager->getClient()->getError(), 'order_ids', 422);
        }

        return response($pdf)
            ->header('content-type', 'application/pdf');
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCountries()
    {
        $countries = Offices::has('country')->select('countryId')->groupBy(['countryId'])->get();
        $countries = $countries->map(fn($country): array => [
            'id' => $country->countryId,
            'name' => $country->country->name
        ]);
        return response()->json($countries);
    }

    /**
     * @return array|JsonResponse
     * @throws Error
     */
    #[\Override]
    public function getServices()
    {
        if ($this->manager->supportsGetServices() && ($this->manager->isActiveSession())) {
            return response()->json($this->manager->getServices());
        }

        return [];
    }
}
