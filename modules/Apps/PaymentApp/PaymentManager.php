<?php

declare(strict_types=1);

namespace Modules\Apps\PaymentApp;

use App\Helper\YesNo;
use App\Models\Gateway\PaymentProviders;
use Modules\Apps\Abstractions\Managers\AbstractAppManager;

class PaymentManager extends AbstractAppManager
{
    /** @var string $_app_key */
    protected string $_app_key;

    /**
     * @param \App\Models\Gateway\PaymentProviders $provider
     * @return mixed
     */
    public function __construct(protected \App\Models\Gateway\PaymentProviders $provider)
    {
        $this->_app_key = $this->provider->provider;
    }

    public function getProvider(): PaymentProviders
    {
        return $this->provider;
    }

    /**
     * @param mixed $mapping
     * @return mixed
     */
    public function isInstalled($mapping = null): bool
    {
        return $this->provider->configuration && parent::isInstalled($mapping);
    }

    /**
     * @param mixed $mapping
     * @return mixed
     */
    public function isActive($mapping = null): bool
    {
        return ($this->provider->configuration->active ?? null) === YesNo::True && parent::isActive($mapping);
    }

    /**
     * @return bool
     */
    public function isConfigured(): bool
    {
        return !empty($this->provider->configuration);
    }

    #[\Override]
    public function uninstall(): void
    {
        if ($this->provider->configuration) {
            $this->provider->configuration->delete();
        }
    }

    public function postUninstall(): void
    {
        $this->uninstall();
    }
}
