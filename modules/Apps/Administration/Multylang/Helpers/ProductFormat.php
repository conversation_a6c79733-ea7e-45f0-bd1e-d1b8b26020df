<?php

declare(strict_types=1);

namespace Modules\Apps\Administration\Multylang\Helpers;

use App\Exceptions\Error;
use App\Helper\Format;
use App\Helper\Plan;
use App\Models\Product\Product as ProductModel;
use Google\Cloud\Core\Exception\GoogleException;
use Google\Cloud\Core\Exception\ServiceException;
use Modules\Importer\Models\HistoryImportLogTask;
use Exception;
use App\Helper\Import\AbstractErpFormatter;
use App\Models\Router\Exceptions;
use Modules\Importer\DataFormat\CategoryProperty;
use Modules\Importer\DataFormat\Product;
use Modules\Apps\Administration\Multylang\MultylangManager;
use Modules\Apps\Administration\Multylang\Models\Relationships;

class ProductFormat extends AbstractErpFormatter
{
    public $language;

    /**
     * @var int
     */
    public $symbols;

    public $url;

    /**
     * @param HistoryImportLogTask|null $settings
     * @param mixed $site_id
     */
    public function __construct(MultylangManager $manager, public $settings, public $site_id)
    {
        $this->manager = $manager;
        $this->symbols = 0;
        $getSite = switchSiteCallback(fn(): array => [
            'language' => site()->language,
            'url' => site()->getSiteUrl('primary')
        ], $this->site_id);
        $this->language = $getSite['language'];
        $this->url = $getSite['url'];
    }

    /**
     * @param ProductModel $product
     * @return mixed|null
     */
    public function format(ProductModel $product)
    {
        try {
            return $this->import($product);
        } catch (Exception $exception) {
            Exceptions::createFromThrowable($exception, 'Multilang import error for product: ' . $product['name']);
        }

        return $id ?? null;
    }

    /**
     * @param ProductModel $product
     * @return Product
     * @throws Error
     * @throws GoogleException
     * @throws ServiceException
     */
    protected function import(ProductModel $product)
    {
        $images = $product->images->map(fn($image) => $image->image_url)->all();

        $properties_format = $this->setProperties($product);
        $product = $product->toArray();
        unset($product['images'], $product['image']);
        $formatted = new Product($product);
        if (!empty($this->settings['translate']) && !empty($this->settings['translate']['title'])) {
            $name = $formatted->getName();
            if ((Plan::remaining($this->manager::SYNC_TRANSLATE) - mb_strlen((string) $name) > 0) && mb_strlen((string) $name) > 0) {
                $getRemaining = (int)$this->manager->getSetting('remaining_for_translation', 0);
                $this->symbols += mb_strlen((string) $name);
                $this->manager->updateSetting('remaining_for_translation', $getRemaining + mb_strlen((string) $name));
                $translateName = Support::translate(site()->language, $this->language, [$name]);
                $formatted->setName($translateName[0]['text']);
            }
        }

        if (!empty($this->settings['translate']) && !empty($this->settings['translate']['description'])) {
            $description = $formatted->getDescription();
            if ((Plan::remaining($this->manager::SYNC_TRANSLATE) - mb_strlen((string) $description) > 0) && mb_strlen((string) $description) > 0) {
                $getRemaining = (int)$this->manager->getSetting('remaining_for_translation', 0);
                $this->manager->updateSetting('remaining_for_translation', $getRemaining + mb_strlen((string) $description));
                $translateName = Support::translate(site()->language, $this->language, [$description]);
                $this->symbols += mb_strlen((string) $description);
                $formatted->setDescription(html_entity_decode((string) preg_replace('/([A-Za-z])([0-9])/', '$1  $2', (string) $translateName[0]['text'])));
            }

            $short_description = $formatted->getShortDescription();
            if ((Plan::remaining($this->manager::SYNC_TRANSLATE) - mb_strlen((string) $short_description) > 0) && mb_strlen((string) $short_description) > 0) {
                $getRemaining = (int)$this->manager->getSetting('remaining_for_translation', 0);
                $this->manager->updateSetting('remaining_for_translation', $getRemaining + mb_strlen((string) $short_description));
                $translateName = Support::translate(site()->language, $this->language, [$short_description]);
                $this->symbols += mb_strlen((string) $short_description);
                $formatted->setShortDescription(html_entity_decode((string) $translateName[0]['text']));
            }

        }

        if ($formatted->getVariants()->count() > 1) {
            $variantsMetadata = [];
            foreach ($product['variants'] as $v) {
                foreach ($v['external_meta_data'] as $meta) {
                    if ($meta['integration'] == 'gensoft') {
                        $variantsMetadata[] = $meta;
                    }
                }
            }

            $this->setVariants($formatted, $variantsMetadata);
        } else {
            $variantMeta = !empty($product['variant']['external_meta_data']) ? $product['variant']['external_meta_data'] : [];
            $this->setVariant($formatted, $variantMeta);
        }

        $this->setCategory($formatted);
        if ($properties_format) {
            $formatted->setCategoryProperties($properties_format);
        }

        if (!empty($this->settings['translate']) && !empty($this->settings['translate']['product_tags'])) {
            $this->setTags($formatted);
        }

        $description = $this->replaceUrl($formatted->getDescription());
        $formatted->setDescription($description);
        $short_description = $this->replaceUrl($formatted->getShortDescription());
        $formatted->setShortDescription($short_description);
        $this->setTabs($formatted);
        $formatted->setId(null);
        $formatted->setVendorId(null);
        $formatted->setSeoTitle(null);
        $formatted->setSeoDescription(null);
        $formatted->setContinueSelling($product['continue_selling'] == 'yes' ? true : false);
        $formatted->setThreshold($product['threshold'] > 0 ? $product['threshold'] : null);
        $formatted->setUrlHandle(null);
        $formatted->setImages($images);
        $formatted->setAppImport($this->manager::APP_KEY . '-' . $product['id']);
        if ($this->symbols > 0) {
            Support::createAppLog($this->manager, $this->symbols);
        }

        return $formatted;
    }

    /**
     * @param int $price_original
     * @return array|float|int|string|string[]
     */
    protected function updatePrice($price_original)
    {
        $setting = $this->settings;
        if (!empty($setting) && (!empty($setting['price_change']) || !empty($setting['price_round']))) {
            $round = $setting['price_round'];
            $change = $setting['price_change'];
            if (is_numeric($change) && $change > 0) {
                $price = round($price_original * $change);
            } else {
                $price = $price_original;
            }

            $price = number_format($price, 2, '.', '');
            if (is_numeric($round)) {
                $new_price = substr_replace($price, (string)$round, -2);
                return $new_price;
            } elseif (!empty($price)) {
                return $price;
            }
        }

        return $price_original;
    }

    /**
     * @param $table
     * @param $original
     * @return array
     * @throws \Google\Cloud\Core\Exception\GoogleException
     * @throws \Google\Cloud\Core\Exception\ServiceException
     */
    protected function replaceText($table, array $original): array
    {
        $translated = Support::isTranslated($table, array_values($original), $this->site_id);
        $newArray = [];
        foreach ($translated as $key => $value) {
            if (in_array($value, $original)) {
                $id = array_search($value, $original, true);
                $newArray[$id] = $key;
            }
        }

        $diff = array_diff(array_keys($original), array_keys($newArray));
        if (empty($diff)) {
            return $newArray;
        }

        $forTranslate = [];
        $forInsert = [];
        foreach ($diff as $item) {
            if ((Plan::remaining($this->manager::SYNC_TRANSLATE) - mb_strlen((string) $original[$item]) > 0) && mb_strlen((string) $original[$item]) > 0) {
                $getRemaining = (int)$this->manager->getSetting('remaining_for_translation', 0);
                $this->manager->updateSetting('remaining_for_translation', $getRemaining + mb_strlen((string) $original[$item]));
                $this->symbols += mb_strlen((string) $original[$item]);
                $forTranslate[] = $original[$item];
            }
        }

        $translate = Support::translate(site()->language, $this->language, array_values($forTranslate));
        foreach ($translate as $trans) {
            $search = array_search($trans['input'], $original, true);
            $newArray[$search] = $trans['text'];
            $forInsert[] = [
                'table' => $table,
                'old_string' => $trans['input'],
                'new_string' => $trans['text'],
            ];
        }

        Support::populateTranslate($forInsert, $this->site_id);
        return $newArray;
    }

    /**
     * @param $formatted
     * @return void
     * @throws \Google\Cloud\Core\Exception\GoogleException
     * @throws \Google\Cloud\Core\Exception\ServiceException
     */
    protected function setCategory(&$formatted)
    {
        $oldId = $formatted->getCategoryId();
        $formatted->setCategoryId(null);
        $categories = $formatted->getCategory()->getPath();
        $path = [];
        $categoryName =  $formatted->getCategory()->getName();
        foreach ($categories as $category) {
            if ($category['name'] != $categoryName) {
                $path[] = $category['name'];
            }
        }

        if (!empty($this->settings['translate']) && !empty($this->settings['translate']['category'])) {
            if ($path !== []) {
                $path = $this->replaceText('type__products_categories', $path);
            }

            if (!$this->manager->isMainSite()) {
                $categoryId = switchSiteCallback(function () use ($path, $oldId) {
                    $findId = Relationships::where(['site_id' => site('site_id'), 'type' => 'type__products_categories', 'primary_id' => $oldId])->first();
                    if (!empty($findId)) {
                        return $findId->secondary_id;
                    }
                }, $this->manager->getSetting('main_site'));
                if (!empty($categoryId)) {
                    $formatted->setCategoryId($categoryId);
                }
            }

            $translateCategory = $this->replaceText('type__products_categories', [$formatted->getCategory()->getName()]);
            $categoryName = $translateCategory[0] ?? $formatted->getCategory()->getName();
        }

        $formatted->setCategory([
            'name' => $categoryName,
            'path' => $path,
            'import_key' => $this->manager::APP_KEY
        ]);
    }

    /**
     * @param $formatted
     * @param mixed $metadata
     * @return void
     * @throws Error
     * @throws \Google\Cloud\Core\Exception\GoogleException
     * @throws \Google\Cloud\Core\Exception\ServiceException
     */
    protected function setVariants(&$formatted, $metadata)
    {
        if ($formatted->getVariants()->count() > 0) {
            $formatted->setVariant(null);
            $formatted->getVariants()->each(function ($v) use ($metadata): void {
                $price = $this->updatePrice(Format::moneyInput($v->getPrice()));
                foreach ($metadata as $meta) {
                    if ($meta['record_id'] == $v->getId()) {
                        $v->setExternalMetaDataIntegration($meta['integration']);
                        $v->setExternalMetaDataKey($meta['external_record_key']);
                    }
                }

                $v->setId(null);
                $v->setPrice($price);
                $v->setUpdates(['external_meta_data_integration', 'external_meta_data_key']);
                if ($v->getWeight() > 0) {
                    $weight = $v->getWeight() / 1000;
                    $v->setWeight($weight);
                }
            });
            if (!empty($this->settings['translate']) && !empty($this->settings['translate']['variety'])) {
                $v1 = $formatted->getVariants()->pluck('v1')->unique()->toArray();
                $v2 = $formatted->getVariants()->pluck('v2')->unique()->toArray();
                $v3 = $formatted->getVariants()->pluck('v3')->unique()->toArray();
                $v = array_filter(array_merge($v1, $v2, $v3));
                $variants = $this->replaceText('products_parameters_options', array_combine($v, $v));
                $formatted->getVariants()->each(function ($variant) use ($variants): void {
                    foreach ($variants as $old => $new) {
                        if (!empty($variant->v1) && $variant->v1 == $old) {
                            $variant->v1 = $new;
                        }

                        if (!empty($variant->v2) && $variant->v2 == $old) {
                            $variant->v2 = $new;
                        }

                        if (!empty($variant->v3) && $variant->v3 == $old) {
                            $variant->v3 = $new;
                        }
                    }
                });
                $populate = [];
                if (!empty($formatted->getP1())) {
                    $translated = Support::isTranslated('products_parameters', [$formatted->getP1()], $this->site_id);
                    if (!empty($translated)) {
                        $formatted->setP1(\Illuminate\Support\Arr::first(array_keys($translated)));
                    } else {
                        if ((Plan::remaining($this->manager::SYNC_TRANSLATE) - mb_strlen((string) $formatted->getP1()) > 0)) {
                            $getRemaining = (int)$this->manager->getSetting('remaining_for_translation', 0);
                            $this->manager->updateSetting('remaining_for_translation', $getRemaining + mb_strlen((string) $formatted->getP1()));
                            $this->symbols += mb_strlen((string) $formatted->getP1());
                            $p1 = Support::translate(site()->language, $this->language, [$formatted->getP1()]);
                            $populate[] = [
                                'table' => 'products_parameters',
                                'old_string' => $formatted->getP1(),
                                'new_string' => $p1[0]['text'],
                            ];
                            $formatted->setP1($p1[0]['text']);
                        }
                    }
                }

                if (!empty($formatted->getP2())) {
                    $translated = Support::isTranslated('products_parameters', [$formatted->getP2()], $this->site_id);
                    if (!empty($translated)) {
                        $formatted->setP2(\Illuminate\Support\Arr::first(array_keys($translated)));
                    } else {
                        if ((Plan::remaining($this->manager::SYNC_TRANSLATE) - mb_strlen((string) $formatted->getP2()) > 0)) {
                            $getRemaining = (int)$this->manager->getSetting('remaining_for_translation', 0);
                            $this->manager->updateSetting('remaining_for_translation', $getRemaining + mb_strlen((string) $formatted->getP2()));
                            $p2 = Support::translate(site()->language, $this->language, [$formatted->getP2()]);
                            $this->symbols += mb_strlen((string) $formatted->getP2());
                            $populate[] = [
                                'table' => 'products_parameters',
                                'old_string' => $formatted->getP2(),
                                'new_string' => $p2[0]['text'],
                            ];
                            $formatted->setP2($p2[0]['text']);
                        }
                    }
                }

                if (!empty($formatted->getP3())) {
                    $translated = Support::isTranslated('products_parameters', [$formatted->getP3()], $this->site_id);
                    if (!empty($translated)) {
                        $formatted->setP3(\Illuminate\Support\Arr::first(array_keys($translated)));
                    } else {
                        if ((Plan::remaining($this->manager::SYNC_TRANSLATE) - mb_strlen((string) $formatted->getP3()) > 0)) {
                            $getRemaining = (int)$this->manager->getSetting('remaining_for_translation', 0);
                            $this->manager->updateSetting('remaining_for_translation', $getRemaining + mb_strlen((string) $formatted->getP3()));
                            $p3 = Support::translate(site()->language, $this->language, [$formatted->getP3()]);
                            $this->symbols += mb_strlen((string) $formatted->getP3());
                            $populate[] = [
                                'table' => 'products_parameters',
                                'old_string' => $formatted->getP3(),
                                'new_string' => $p3[0]['text'],
                            ];
                            $formatted->setP3($p3[0]['text']);
                        }
                    }
                }

                Support::populateTranslate($populate, $this->site_id);
            }
        }
    }

    /**
     * @param $formatted
     * @param mixed $productMeta
     * @return void
     */
    protected function setVariant(&$formatted, $productMeta = [])
    {
        $defaultVariant = $formatted->getVariants()->first();
        $formatted->setVariants([]);
        $formatted->setVariant($defaultVariant);
        $formatted->getVariant()->setId(null);
        //$formatted->getVariant()->setSku(null);
        $formatted->getVariant()->setPrice($this->updatePrice(Format::moneyInput($defaultVariant->getPrice())));
        foreach ($productMeta as $meta) {
            if ($meta['integration'] == 'gensoft') {
                $formatted->getVariant()->setExternalMetaDataIntegration($meta['integration']);
                $formatted->getVariant()->setExternalMetaDataKey($meta['external_record_key']);
            }
        }

        if ($formatted->getVariant()->getWeight() > 0) {
            $weight = $formatted->getVariant()->getWeight() / 1000;
            $formatted->getVariant()->setWeight($weight);
        }

        $formatted->getVariant()->setUpdates(['external_meta_data_integration', 'external_meta_data_key']);
    }

    /**
     * @param $product
     * @return array|void
     * @throws \Google\Cloud\Core\Exception\GoogleException
     * @throws \Google\Cloud\Core\Exception\ServiceException
     */
    protected function setProperties($product)
    {
        if (!empty($product->properties)) {
            $properties = $product->properties->unique('name')->pluck('name', 'id')->toArray();
            $properties_format = [];
            if (!empty($this->settings['translate']) && !empty($this->settings['translate']['properties'])) {
                $properties = $this->replaceText('properties', $properties);
            }

            foreach ($properties as $id => $name) {
                $values = $product->categoryPropertiesOptionsAll->where('property_id', $id)->pluck('value')->toArray();
                if (!empty($this->settings['translate']) && !empty($this->settings['translate']['properties'])) {
                    $values = $this->replaceText('properties_options', $values);
                }

                $properties_format[] = new CategoryProperty([
                    'name' => $name,
                    'values' => $values,
                ]);
            }

            return $properties_format;
        }
    }

    /**
     * @param $formatted
     * @return void
     * @throws Error
     * @throws \Google\Cloud\Core\Exception\GoogleException
     * @throws \Google\Cloud\Core\Exception\ServiceException
     */
    protected function setTabs(&$formatted)
    {
        if (!empty($formatted->getTabs())) {
            foreach ($formatted->getTabs() as $tab) {
                $tab->setId(null);
                if ((Plan::remaining($this->manager::SYNC_TRANSLATE) - mb_strlen((string) $tab->getName()) > 0) && mb_strlen((string) $tab->getName()) > 0) {
                    $getRemaining = (int)$this->manager->getSetting('remaining_for_translation', 0);
                    $this->manager->updateSetting('remaining_for_translation', $getRemaining + mb_strlen((string) $tab->getName()));
                    $tab->setName(Support::translate(site()->language, $this->language, [$tab->getName()])[0]['text']);
                    $this->symbols += mb_strlen((string) $tab->getName());
                }

                if ((Plan::remaining($this->manager::SYNC_TRANSLATE) - mb_strlen((string) $tab->getDescription()) > 0) && mb_strlen((string) $tab->getDescription()) > 0) {
                    $getRemaining = (int)$this->manager->getSetting('remaining_for_translation', 0);
                    $this->manager->updateSetting('remaining_for_translation', $getRemaining + mb_strlen((string) $tab->getDescription()));
                    $tab->setDescription(Support::translate(site()->language, $this->language, [$tab->getDescription()])[0]['text']);
                    $this->symbols += mb_strlen((string) $tab->getDescription());
                }
            }
        }
    }

    /**
     * @param $formatted
     * @return void
     * @throws \Google\Cloud\Core\Exception\GoogleException
     * @throws \Google\Cloud\Core\Exception\ServiceException
     */
    protected function setTags(&$formatted)
    {
        if (!empty($formatted->getTags())) {
            $tags = [];
            foreach ($formatted->getTags() as $tag) {
                $tags[] = $tag->getTag();
            }

            $translated = $this->replaceText('tags__products_tags', $tags);
            $newTags = [];
            foreach ($translated as $tag) {
                $newTags[] = ['tag' => $tag];
            }

            $formatted->setTags($newTags);
        }
    }

    /**
     * @param $string
     * @return array|mixed|string|string[]
     */
    protected function replaceUrl($string)
    {
        $urlManipulation = !empty($this->settings['url_manipulation']) ? $this->settings['url_manipulation'] : null;
        $urlRemove = !empty($this->settings['url_remove']) && $urlManipulation == 3 ? $this->settings['url_remove'] : null;
        if (empty($urlManipulation) || $urlManipulation == 2) {
            return $string;
        }

//        $cleanedString = preg_replace('/<a\b[^>]*>.*?<\/a>/', '', (string) $string);
        try {
            $escapedDomain = preg_quote((string) site()->url, '/');
            $pattern = '/<a\s[^>]*href=(\'|")' . $escapedDomain . '([^\'">]+)(\'|")[^>]*>/i';
            preg_match_all($pattern, (string) $string, $matches);
            $foundLinks = $matches[0];
            foreach ($foundLinks as $link) {
                $getLikn = explode('"', $link);
                if (empty($getLikn[1])) {
                    continue;
                }

                if ($urlManipulation == 1) {
                    $string = str_replace($getLikn[1], '#', $string);
                } else {
                    $newUrl = $this->checkMapping($getLikn[1]);
                    if (!empty($newUrl)) {
                        $string = str_replace($getLikn[1], $newUrl, $string);
                    } elseif (!empty($urlRemove)) {
                        $string = str_replace($getLikn[1], '#', $string);
                    }
                }
            }

            return $string;
        } catch (Exception $exception) {
            Exceptions::createFromThrowable($exception, 'Multilang replace url error');
            return $string;
        }
    }

    /**
     * @param $url
     * @return string|void
     */
    protected function checkMapping($url)
    {
        $url_replace = parse_url((string) $url);
        $explode = array_filter(explode('/', $url_replace['path']));
        $type = \Illuminate\Support\Arr::first($explode);
        $url_slug = \Illuminate\Support\Arr::last($explode);
        $getUrl = Relationships::where(['site_id' => $this->site_id, 'primary_slug' => $url_slug, 'type' => $this->manager->relationType($type)])->first();
        if (!empty($getUrl)) {
            return $this->url . '/' . $type . '/' . $getUrl->secondary_slug;
        }
    }

}
