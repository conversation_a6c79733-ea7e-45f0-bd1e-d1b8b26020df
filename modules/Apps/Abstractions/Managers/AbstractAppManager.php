<?php

declare(strict_types=1);

namespace Modules\Apps\Abstractions\Managers;

use App\Contracts\AppsContract;
use App\Exceptions\Error;
use App\Traits\PlanUsage;
use Closure;
use Illuminate\Support\Traits\Localizable;
use Modules\Apps\Abstractions\Managers;
use Modules\Core\Core\Models\Alerts;

/**
 * @method isConfigured()
 */
abstract class AbstractAppManager implements AppsContract
{
    use PlanUsage;
    use Localizable;
    use Managers\Contracts\ApplicationMainInformation;
    use Managers\Contracts\ApplicationSettings;
    use Managers\Contracts\ApplicationMetaSettings;
    use Managers\Contracts\ApplicationSupports;
    use Managers\Contracts\ApplicationRequired;
    use Managers\Contracts\ApplicationPlanPriority;
    use Managers\Contracts\ApplicationMainInformation;
    use Managers\Contracts\ApplicationSettings;
    use Managers\Contracts\ApplicationSupports;
    use Managers\Contracts\ApplicationRequired;
    use Managers\Contracts\ApplicationPlanPriority;

    /**
     * @return void
     */
    public function install(): void
    {
        // TODO: Implement install() method.
    }

    /**
     * @return void
     */
    public function uninstall(): void
    {
        // TODO: Implement uninstall() method.
    }

    /**
     * @inerhitDoc
     * @param mixed $is_install
     */
    public function getMigrationsPath($is_install): ?string
    {
        return null;
    }

    /**
     * @param string $mapping
     * @param string $message
     * @param bool $notifyEmail
     * @param string $type
     * @param Closure|null $extend
     * @return null|Alerts
     * @throws Error
     */
    public function notifiy(
        string $mapping,
        string $message,
        bool $notifyEmail = false,
        string $type = Alerts::TYPE_ERROR,
        ?Closure $extend = null
    ): ?Alerts {
        $app = $this->getGlobalApp();
        if (!$app || empty($mapping) || empty($message)) {
            return null;
        }

        $alert = $app->alert()
            ->setMapping($mapping)
            ->setMessage($message)
            ->setAllowSendNotification($notifyEmail)
            ->setType($type);

        if ($extend !== null) {
            $extend($alert);
        }

        return $alert->save();
    }
}
