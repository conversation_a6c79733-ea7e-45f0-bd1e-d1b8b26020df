<?php

declare(strict_types=1);

namespace Modules\Cloudio\Http\Controllers;

use Api\Http\Controllers\Controller;
use App\Exceptions\Error;
use App\Helper\ArrayTree;
use App\Helper\Plan;
use App\Models\Apps\ApplicationTokens;
use App\Models\Mongo\ApplicationLogs;
use App\Models\Mongo\ApplicationLogsSystem;
use App\Models\Mongo\Queue;
use App\Models\Product\Product;
use App\Models\Product\Vendor;
use App\Models\Queue\SiteQueue;
use Illuminate\Http\Request;
use Modules\Cloudio\Jobs\GenerateProductDescription;
use Modules\Cloudio\Jobs\GenerateProductMetaDescription;
use Modules\Cloudio\Jobs\GenerateProductShortDescription;
use Modules\Cloudio\Manager;
use Modules\Core\Core\Traits\GridFilters;
use Modules\Core\SmartCollections\Models\ProductSelection;

class ApiController extends Controller
{
    use GridFilters;

    protected \Modules\Cloudio\Manager $manager;

    /**
     * SettingController constructor.
     * @param Manager $manager
     * @throws Error
     */
    public function __construct()
    {
        $this->manager = new Manager();
    }

    /**
     * @return array
     * @throws Error
     */
    public function info(): array
    {
        $app = $this->manager->getGlobalApp();
        return [
            'isInstalled' => $this->manager->isInstalled(),
            'isActive' => $this->manager->isActive(),
            'title' => $app->name,
            'description' => $app->description,
            'short_description' => $app->short_description,
            'help_url' => $app->help_url,
            'image' => $app->icon,
            'tokens' => [
                'total' => (int)Plan::featureValue($this->manager::FEATURE),
                'used' => (int)Plan::used($this->manager::FEATURE),
                'remaining' => (int)Plan::remaining($this->manager::FEATURE),
                'buy_url' => \LinkerCp::path('plan/feature/' . $this->manager::FEATURE)
            ]
        ];

    }

    /**
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     */
    public function getSkills()
    {
        return response()->json($this->manager->skills());
    }

    /**
     * @param $type
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     */
    public function getSkillInfo($type)
    {
        $skill = $this->manager->skills($type);
        if (!$skill) {
            return response()->json([], 404);
        }

        return response()->json($skill);
    }


    /**
     * @param $id
     * @param null|mixed $skill
     * @return array|void
     */
    public function isGenerated($id, $skill = null): array
    {
        $data = ApplicationLogs::with('tokens')->where('_id', $id)->first();
        if (!empty($data) && empty($type)) {
            if ($data->progress == 'complete') {
                $tokens = 0;
                if (!empty($data->tokens) && !empty($data->tokens->cc_tokens)) {
                    $tokens = (int)$data->tokens->cc_tokens;
                }

                return [
                    'status' => true,
                    'log_id' => $id,
                    'resource_id' => $data->resource_id,
                    'type' => $data->type,
                    'response' => $data->response,
                    'tokens' => $tokens,
                    'date' => $data->updated_at->format('d.m.Y H:i'),
                    'is_error' => $data->is_error
                ];
            }

            return [
                'progress' => $data->progress,
                'status' => false,
            ];
        }

        $data = ApplicationLogsSystem::where('_id', $id)->first();
        if (!empty($data)) {
            if ($data->progress == 'complete') {
                $tokens = 0;
                if (!empty($data->tokens) && !empty($data->tokens->cc_tokens)) {
                    $tokens = (int)$data->tokens->cc_tokens;
                }

                return [
                    'status' => true,
                    'log_id' => $id,
                    'resource_id' => $data->resource_id,
                    'type' => $data->type,
                    'response' => $data->response,
                    'tokens' => $tokens,
                    'date' => $data->updated_at->format('d.m.Y H:i'),
                    'is_error' => $data->is_error
                ];
            }

            return [
                'status' => false,
            ];
        }

        return [
            'status' => false,
        ];
    }

    /**
     * @param $skill
     * @param int $active
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     */
    public function updateSkillActive(Request $request)
    {
        if ($request->has('key') && $request->has('status') && !empty($this->manager::skills[$request->input('key')])) {
            $this->manager->updateSetting($request->input('key'), $request->input('status') == 1);
            return response()->json(['msg' => 'success']);
        }

        return response()->json([], 422);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLog()
    {
        $type = str_replace('-', '_', request()->segment(3));
        if (!isset($this->manager::skills[$type]) || empty($this->manager::skills[$type]['log'])) {
            return response()->json([], 404);
        }

        $getType = $this->manager::skills[$type]['log'];
        $logs = ApplicationLogs::owner()->with(['item', 'tokens'])
            ->where(['type' => $getType, 'progress' => 'complete'])->orderBy('_id', 'desc');
        if (!empty(\request()->get('logid'))) {
            $logs->where('system_id', \request()->get('logid'));
        }

        $logs = $logs->paginate(20);
        $logs->transform(function ($log) use ($type) {
            $log->id = $log->_id;
            $log->skill = __('cloudio::app.services.' . $type);
            $log->date = $log->progress == 'complete' ? $log->updated_at->format('d.m.Y H:i') : $log->created_at->format('d.m.Y H:i');
            $log->item_name = '';
            $log->item_url = '';
            $log->action = '';
            if (!empty($log->item)) {
                $log->item_id = $log->item->id ?? null;
                if (method_exists($log->item, 'url')) {
                    $log->item_url = '/admin/products/edit/' . $log->item->id;
                }

                $log->item_name = $log->item->name ?? null;
            }

            if (is_array($log->response)) {
                if (!empty($log->response['type'])) {
                    $log->action = $log->response['type'];
                }
            }

            $log->token = 0;
            if ($log->progress == 'complete' && !empty($log->tokens) && !empty($log->tokens->cc_tokens)) {
                $log->token = (int)$log->tokens->cc_tokens;
            }

            return $log->only(['id', 'skill', 'action', 'date', 'progress', 'item_id', 'item_name', 'item_url', 'token', 'response', 'is_error']);
        });
        return response()->json($logs);
    }

    /**
     * @param $logId
     * @param $rating
     * @return \Illuminate\Http\JsonResponse
     */
    public function skillAssessment($logId, $rating)
    {
        $get = ApplicationTokens::where('log_id', $logId)->first();
        if ($get) {
            $get->update(['evaluation' => $rating]);
            return response()->json([
                'status' => 'success',
                'msg' => __('cloudio::app.rating.success')
            ]);
        }

        return response()->json([
            'status' => 'error',
            'msg' => __('cloudio::app.rating.error')
        ]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancelJob(Request $request)
    {
        $getSettings = $this->manager->getSetting('job_' . $request->input('type') . '_' . $request->input('product_id'));
        if (!empty($getSettings)) {
            if (!empty($getSettings['job_id'])) {
                Queue::whereKey($getSettings['job_id'])->delete();
            }

            if (!empty($getSettings['log_id'])) {
                ApplicationLogs::whereKey($getSettings['log_id'])->delete();
            }

            $this->manager->removeSetting('job_' . $request->input('type') . '_' . $request->input('product_id'));
            return response()->json([
                'status' => 'success',
                'msg' => __('cloudio::app.cancel.success')
            ]);
        }

        if (!empty($request->input(['log_id']))) {
            ApplicationLogs::whereKey($request->input(['log_id']))->delete();
            return response()->json([
                'status' => 'success',
                'msg' => __('cloudio::app.cancel.success')
            ]);
        }

        return response()->json([
            'status' => 'error',
            'msg' => __('cloudio::app.cancel.error')
        ]);

    }

    /**
     * @return array
     */
    public function getTokens(): array
    {
        return [
            'total' => (int)Plan::featureValue($this->manager::FEATURE),
            'used' => (int)Plan::used($this->manager::FEATURE),
            'remaining' => (int)Plan::remaining($this->manager::FEATURE),
            'buy_url' => \LinkerCp::path('plan/feature/' . $this->manager::FEATURE)
        ];
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws Error
     * @throws \Throwable
     */
    public function restart(Request $request)
    {
        if (!empty($request->input('type')) && !empty($request->input('product_id')) && !empty($settings = $this->manager->getSetting('job_' . $request->input('type') . '_' . $request->input('product_id')))) {
            switch ($request->input('type')) {
                case 'product_description':
                    $job = SiteQueue::executeQueueTask(GenerateProductDescription::class, [
                        'product_id' => $request->input('product_id'),
                        'settings' => $settings['settings'],
                        'log_id' => $settings['log_id'],
                    ], null);
                    break;
                case 'short_description':
                    $job = SiteQueue::executeQueueTask(GenerateProductShortDescription::class, [
                        'product_id' => $request->input('product_id'),
                        'settings' => $settings['settings'],
                        'log_id' => $settings['log_id'],
                    ], null);
                    break;
                case 'meta_description':
                    $job = SiteQueue::executeQueueTask(GenerateProductMetaDescription::class, [
                        'product_id' => $request->input('product_id'),
                        'settings' => $settings['settings'],
                        'log_id' => $settings['log_id'],
                    ], null);
                    break;
                default:
            }

            if (!empty($job) && !empty($job->__toString())) {
                ApplicationLogs::find($settings['log_id'])->update(['progress' => 'starting', 'is_error' => false, 'response' => null]);
                $this->manager->updateSetting('job_' . $request->input('type') . '_' . $request->input('product_id'), [
                    'job_id' => $job->__toString(),
                    'log_id' => $settings['log_id'],
                    'settings' => $settings['settings']
                ]);
                return response()->json([
                    'log_id' => !empty($settings['log_id']) ? $settings['log_id'] : null,
                    'status' => 'success',
                ]);
            }
        }

        return response()->json([
            'status' => 'error',
        ], 422);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductSelections(Request $request)
    {
        $value = request()->query('query');

        $model = ProductSelection::where('name', 'like', sprintf('%%%s%%', $value))
            ->orderby('id', 'DESC');

        $results = $this->getRecords($model);

        $results->transform(fn(ProductSelection $selection) =>
            /** @var ProductSelection $record */
            (object)[
            'id' => $selection->id,
            'name' => $selection->name,
        ]);

        return response()->json($results);

    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductCategories(Request $request)
    {
        $categories = ArrayTree::createAutocomplete(\App\Models\Product\Category::all());
        return response()->json(array_column($categories, "name", "id"));

    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductVendors(Request $request)
    {
        $value = request()->query('query');

        $model = Vendor::where('name', 'like', sprintf('%%%s%%', $value))
            ->orderby('id', 'DESC');

        $results = $this->getRecords($model);

        $results->transform(fn(Vendor $vendor) =>
            /** @var Vendor $record */
            (object)[
            'id' => $vendor->id,
            'name' => $vendor->name,
            'img' => $vendor->getImage()
        ]);

        return response()->json($results);

    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProducts(Request $request)
    {
        $value = request()->query('query');

        $model = Product::where('name', 'like', sprintf('%%%s%%', $value))
            ->orderby('id', 'DESC');

        $results = $this->getRecords($model);

        $results->transform(fn(Product $product) =>
            /** @var Product $record */
            (object)[
            'id' => $product->id,
            'name' => $product->name,
            'img' => $product->getImage()
        ]);

        return response()->json($results);

    }
}
