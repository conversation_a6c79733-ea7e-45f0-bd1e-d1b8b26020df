<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 31.5.2019 г.
 * Time: 14:39 ч.
 */

namespace Modules\JsResponse;

use Modules\JsResponse\Contracts\HtmlResponseContract;
use Symfony\Component\HttpFoundation\Response;

abstract class AbstractHtmlResponse implements HtmlResponseContract
{

    protected $response;

    public function __construct(Response $response = null)
    {
        $this->response = $response;
    }

    /**
     * @return null|Response
     */
    public function getResponse()
    {
        return $this->response;
    }

    /**
     * @param mixed $response
     * @return AbstractHtmlResponse
     */
    public function setResponse(Response $response): AbstractHtmlResponse
    {
        $this->response = $response;
        return $this;
    }

}
