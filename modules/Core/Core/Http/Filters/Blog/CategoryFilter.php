<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Filters\Blog;

use Illuminate\Http\Request;
use Modules\Core\Core\Helpers\AbstractGridFilter;
use Modules\Core\Core\Helpers\GridFilterRules;
use Modules\Core\Core\Traits\GridFilter\QueryFilter;

class CategoryFilter extends AbstractGridFilter
{
    use QueryFilter;

    protected $queryFilterColumns = [
        'name','id'
    ];

    /**
     * @inheritdoc
     */
    public function getSavedSearchQueryFilters(): ?array
    {
        return [
            'module' => 'blog',
            'filter' => 'category'
        ];
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    #[\Override]
    protected function validateRequestRules(Request $request): \Modules\Core\Core\Helpers\GridFilterRules
    {
        return GridFilterRules::make([
            'query' => 'string',
            'comments' => 'string|in:no,automatic,moderator'
        ])->setOrder('id,name');
    }

    /**
     * @param $operator
     * @param $value
     */
    public function filterComments($operator, $value): void
    {
        $this->setWhere('author_id', function ($query) use ($operator, $value): void {
            $query->where('comments', $value);
        });

        $this->setFilters('comments', $value);
    }
}
