<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Request\Products;

use App\Common\DateTimeFormat;
use App\Exceptions\Error;
use App\Helper\Text;
use App\Models\Product\Parameter;
use App\Models\Product\ParameterOption;
use App\Models\Product\Product;
use Illuminate\Contracts\Support\Arrayable;
use App\Helper\Validate as LibValidate;
use Modules\Core\Requests\AbstractFormRequest;

class ProductRequest extends AbstractFormRequest
{
    protected $name_max = 191;

    protected $description_max = 250000;

    protected $variants_max = 500;

    protected $siteDateTimeFormat;

    protected $isAutoSave = false;

    protected $isDraft = false;

    /**
     * @return void
     */
    protected function prepareForValidation()
    {
        $input = $this->all();

        if (isset($input['variants']) && empty($input['p'])) {
            $input['p'] = [];
            $checked = [];

            for ($i = 1; $i <= 3; $i++) {
                if (!empty($input['p' . $i]) && empty($input['p' . $i . '_id'])) {
                    $parameter = Parameter::create([
                        'name' => $input['p' . $i],
                        'type' => 'select',
                        'visible' => 1
                    ]);
                    $input['p' . $i . '_id'] = $parameter->id;
                }
            }


            foreach ($input['variants'] as $key => $var) {
                for ($i = 1; $i <= 3; $i++) {
                    if (!empty($var['v' . $i . '_id'])) {
                        $parameterOption = ParameterOption::with('parameter')
                            ->where(['name' => $var['v' . $i], 'parameter_id' => $input['p' . $i . '_id']])
                            ->firstOr(fn() => ParameterOption::create([
                                'name' => $var['v' . $i],
                                'parameter_id' => $input['p' . $i . '_id'],
                            ]));
                        if (!empty($checked[$i] = $parameterOption)
                            && trim((string) $checked[$i]->parameter->name)
                            && trim((string) $checked[$i]->name)) {
                            $input['p'][$checked[$i]->parameter->id] = [
                                'id' => $checked[$i]->parameter->id,
                                'name' => $checked[$i]->parameter->name,
                            ];
                            $input['variants'][$key]['v' . $i . '_id'] = $parameterOption->id;
                            $input['variants'][$key]['v' . $i . 'r'] = [
                                'id' => $parameterOption->id,
                                'name' => $parameterOption->name,
                            ];
                        }
                    }
                }
            }

            $input['p'] = count($input['p']) ? array_values($input['p']) : null;
        }

        if (empty($input['type']) || !is_string($input['type'])) {
            $input['type'] = Product::TYPE_SIMPLE;
        }

        if ($input['type'] == Product::TYPE_DIGITAL) {
            $input['digital'] = 'yes';
        }

        if (isset($input['shipping']) && $input['shipping'] == 'yes' && isset($input['variant']) && !isset($input['variant']['weight'])) {
            $input['variant']['weight'] = 0;
        }

        if (!isset($input['shipping'])) {
            $input['shipping'] = 'no';
        }

        if ($input['shipping'] == 'no') {
            unset($input['variant']['weight']);
        }

        $this->replace($input);
    }

    /**
     * @return array
     */
    public function passedValidation()
    {
        $data = $this->all();
        if (isset($data['shipping']) && $data['shipping'] == 'yes') {
            $data['require_shipping_address'] = 'yes';
        }

        unset($data['shipping']);

        if (isset($data['tracking']) && $data['tracking'] == 'yes') {
            $data['track_inventory'] = 'yes';
        }

        unset($data['tracking'], $data['shipping']);
        $data['is_hidden'] = 0;
        if (isset($data['hidden'])) {
            if ($data['hidden'] == 'yes') {
                $data['is_hidden'] = 1;
            } else {
                $data['is_hidden'] = 0;
            }
        }

        //        if (isset($data['continue_selling']) && $data['continue_selling'] == 'yes') {
        //            $data['continue_selling'] = 1;
        //        } else {
        //            $data['continue_selling'] = 0;
        //        }

        return $data;
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        $this->isAutoSave = request()->has('save');
        $this->isDraft = request()->has('draft');
        $this->siteDateTimeFormat = DateTimeFormat::$date_formats[setting('date_format')]['format'] . ' ' . DateTimeFormat::$time_formats[setting('time_format')]['format'];

        if ($this->route()->getName() == 'admin.core.products.store.draft') {
            return [
                'name' => 'required|max:' . $this->name_max,
                'category_id' => 'exists:type__products_categories,id',
                'type' => 'required|string|type_validations|in:' . implode(',', Product::TYPES),
                'type_digital' => 'required_if:type,' . Product::TYPE_DIGITAL . '|in:page,file',
            ];
        }

        $rules = [
            'name' => 'required|max:' . $this->name_max,
            'description' => 'max:' . $this->description_max,
            'publish_date' => ['nullable', 'date_format:'.$this->siteDateTimeFormat],
            'active_to' => ['nullable', 'date_format:'.$this->siteDateTimeFormat],
            'vendor_id' => 'exists:type__products_vendors,id',
            'category_id' => 'exists:type__products_categories,id',
            'type' => 'required|string|type_validations|in:' . implode(',', Product::TYPES),
            'shipping' => 'in:yes,no',
            'tracking' => 'in:yes,no',
            'continue_selling' => 'in:yes,no|continue_selling_validations',
            'threshold' => 'nullable|integer|threshold_validations',
            'variant' => 'required_if:type,' . Product::TYPE_SIMPLE . '|array',
            'variants' => 'required_if:type,' . Product::TYPE_MULTIPLE . '|array|variants_unique|max:' . $this->variants_max,
            'variants.*.v1' => 'sometimes|required|string|max:191|validate_parameters|validate_variants',
            'variants.*.v1_id' => 'sometimes|required|integer|exists:products_parameters_options,id',
            'variants.*.v2' => 'sometimes|required|string|max:191|validate_parameters|validate_variants',
            'variants.*.v2_id' => 'sometimes|required|integer|exists:products_parameters_options,id',
            'variants.*.v3' => 'sometimes|required|string|max:191|validate_parameters|validate_variants',
            'variants.*.v3_id' => 'sometimes|required|integer|exists:products_parameters_options,id',
            'variants.*.sku' => 'sometimes|nullable|string|max:191',
            'variants.*.barcode' => 'sometimes|nullable|string|max:191',
            'variants.*.weight' => 'bail|sometimes|required_if:shipping,yes|nullable|numeric|min:0.01,max:10000000',
            'variants.*.quantity' => 'sometimes|required_if:tracking,yes|numeric|max:50000000',
            'variants.*.price' => 'sometimes|numeric|validate_price|max:1000000000',
            'variants.*.discount_price' => 'sometimes|numeric|validate_discount_price|max:1000000000',
            'variants.*.type' => 'sometimes|in:price,percent',
            'variants.*.percent' => 'sometimes|numeric|validate_price',
            'variants.*.minimum' => 'sometimes|numeric',
            'variant.sku' => 'sometimes|nullable|string|max:191',
            'variant.barcode' => 'sometimes|nullable|string|max:191',
            'variant.weight' => 'sometimes|required_if:shipping,yes|numeric|max:10000000|min:0.01',
            'variant.quantity' => 'sometimes|required_if:tracking,yes|numeric|max:50000000',
            'variant.price' => 'sometimes|numeric|validate_price|max:1000000000',
            'variants.discount_price' => 'sometimes|numeric|validate_discount_price',
            'variant.type' => 'sometimes|in:price,percent',
            'variant.percent' => 'sometimes|numeric|validate_price',
            'variant.minimum' => 'sometimes|numeric',
            'type_digital' => 'required_if:type,' . Product::TYPE_DIGITAL . '|in:page,file',
            'page_id' => 'validate_app:membership|required_if:type_digital,page|array|exists:pages,id',
            'days' => 'nullable|integer',
            'tabs' => 'array',
            'tabs.*.name' => 'sometimes|required|string|max:191',
            'tabs.*.description' => 'nullable|string',
            'meta.width' => 'nullable|numeric|min:0',
            'meta.depth' => 'nullable|numeric|min:0',
            'meta.height' => 'nullable|numeric|min:0',
            'sort_order' => 'nullable|integer',
            'unit_id' => 'nullable|exists:products_units,id|validate_app:grocery_store',
            'base_unit_id' => 'nullable|exists:products_units,id|validate_app:grocery_store',
            'unit_type' => 'nullable|in:measured,countable',
            'base_unit_value' => 'nullable|int|min:1',
            'other_categories' => 'array',
            'other_categories.*.id' => 'int|exists:type__products_categories,id',
            'tags' => 'nullable|string',
            'brand_model' => 'validate_app:brand_model',
            'brand_model.model_id' => 'required_with:brand_model|exists:@brand_model_models,id',
            'linked' => 'array',
            'linked.*.linked_id' => 'exists:products,id',
            'linked.*.two_way' => 'in:0,1',
            'status_id' => 'nullable|exists:product_statuses,id',
            'out_of_stock_id' => 'nullable|exists:product_statuses,id',
        ];

        if ($this->route('admin.core.products.update')) {
            $rules['file'] = 'required_if:type_digital,file|file';
        }

        return $rules;
    }

    /*
     * @return array
     */
    #[\Override]
    public function messages()
    {
        return [
            'name.required' => 'Product name is required',
            'name.max' => "The maximum allowed characters for 'name' are {max}",
            'description.max' => "The maximum allowed characters for 'description' are {max}",
            'publish_date.date_format' => 'The publish date format is invalid. Format should be: '.$this->siteDateTimeFormat,
            'active_to.date_format' => 'The publish date format is invalid. Format should be: '.$this->siteDateTimeFormat,
            'active_to.after' => 'The active to date must be a date after the publish date',
            'vendor_id.exists' => 'The selected vendor is invalid. Please select a valid vendor',
            'category_id.exists' => 'The selected category is invalid. Please select a valid category',
            'category_id.required' => 'You have not selected a main category for the product. Please select a main category for the product',
            'type.required' => 'Product type is required',
            'type.string' => 'Product type must be a string',
            'type.in' => 'Invalid product type. Please select a valid product type. Valid product types are: ' . implode(', ', Product::TYPES),
            'shipping.in' => 'Invalid Require shipping value. Please select a valid Require shipping value. Valid shipping values are: yes, no',
            'tracking.in' => 'Invalid Track inventory value. Please select a valid Track inventory value. Valid tracking values are: yes, no',
            'type.type_validations' => __en('product.err.digital_file_cannot_have_multiple_variants'),
            'continue_selling.in' => 'Invalid Continue selling value. Please select a valid Continue selling value. Valid continue selling values are: yes, no',
            'continue_selling.continue_selling_validations' => __en('product.err.cannot_continue_selling_untracked_product'),
            'threshold.numeric' => 'The threshold must be a number',
            'threshold.threshold_validations' => 'The threshold field is only applicable to tracked products.',
            'variant.required_if' => 'The variant field is required for simple products',
            'variants.required_if' => 'The variants field is required for multiple products',
            'variants.max' => 'The maximum allowed variants for a product is {max}',
            'variants.array' => 'The variants field must be an array',
            'type_digital.required_if' => 'The type digital field is required for digital products',
            'type_digital.in' => 'Invalid type digital value. Please select a valid type digital value. Valid type digital values are: page, file',
            'variants.not_in' => 'The variants field is required for type is not digital',
            'variants.variants_unique' => __en('variant.err.duplicate'),
            'variants.*.v1.validate_parameters' => 'Invalid parameter value. Please select a valid parameter value',
            'variants.*.sku.max' => "The maximum allowed characters for 'sku' are {max}",
            'variants.*.barcode' => "The maximum allowed characters for 'barcode' are {max}",
            'variants.*.weight.required_if' => 'The weight field is required for products that require shipping',
            'variants.*.weight.numeric' => 'The weight must be a number',
            'variants.*.weight.max' => 'The maximum allowed weight for a product is {max}',
            'variants.*.quantity.required_if' => 'The quantity field is required for tracked products',
            'variants.*.quantity.numeric' => 'The quantity must be a number',
            'variants.*.quantity.max' => 'The maximum allowed quantity for a product is {max}',
            'variants.*.price.numeric' => 'The price must be a number',
            'variants.*.discount_price.numeric' => 'The discount price must be a number',
            'variants.*.discount_price.validate_discount_price' => 'The discount price must be a valid currency amount',
            'variants.*.price.validate_price' => 'The price must be a valid currency amount',
            'variants.*.type.in' => 'Invalid price type. Please select a valid price type. Valid price types are: price, percent',
            'variant.sku.max' => "The maximum allowed characters for 'sku' are {max}",
            'variant.barcode' => "The maximum allowed characters for 'barcode' are {max}",
            'variant.weight.required_if' => 'The weight field is required for products that require shipping',
            'variant.weight.numeric' => 'The weight must be a number',
            'variant.weight.max' => 'The maximum allowed weight for a product is {max}',
            'variant.quantity.required_if' => 'The quantity field is required for tracked products',
            'variant.quantity.numeric' => 'The quantity must be a number',
            'variant.quantity.max' => 'The maximum allowed quantity for a product is {max}',
            'variant.price.numeric' => 'The price must be a number',
            'variant.discount_price.numeric' => 'The discount price must be a number',
            'variant.discount_price.validate_discount_price' => 'The discount price must be a valid currency amount',
            'variant.price.validate_price' => 'The price must be a valid currency amount',
            'variant.type.in' => 'Invalid price type. Please select a valid price type. Valid price types are: price, percent',
            'variant.percent.numeric' => 'The percent must be a number',
            'variant.percent.validate_price' => 'The percent must be a valid percent',
            'variant.minimum.numeric' => 'The minimum must be a number',
            'variants.*.percent.numeric' => 'The percent must be a number',
            'variants.*.percent.validate_price' => 'The percent must be a valid percent',
            'variants.*.minimum.numeric' => 'The minimum must be a number',
            'page_id.required_if' => 'The page field is required for digital products',
            'page_id.exists' => 'The selected page is invalid. Please select a valid page',
            'page_id.validate_app' => 'Is not install membership module',
            'days.integer' => 'The days field must be a number',
            'file.required_if' => 'The file field is required for digital products',
            'file.file' => 'The file must be a file',
            'tabs.array' => 'The tabs field must be an array',
            'tabs.*.name.required' => 'The tab name field is required',
            'tabs.*.name.string' => 'The tab name must be a string',
            'tabs.*.name.max' => "The maximum allowed characters for 'tab name' are {max}",
            'tabs.*.description.required' => 'The tab description field is required',
            'tabs.*.description.string' => 'The tab description must be a string',
            'meta.width.numeric' => 'The width must be a number',
            'meta.width.min' => 'The width must be a number greater than or equal to 0',
            'meta.depth.numeric' => 'The depth must be a number',
            'meta.depth.min' => 'The depth must be a number greater than or equal to 0',
            'meta.height.numeric' => 'The height must be a number',
            'meta.height.min' => 'The height must be a number greater than or equal to 0',
            'sort_order.integer' => 'The sort order must be a number',
            'unit_id.exists' => 'The selected unit is invalid. Please select a valid unit',
            'base_unit_id.exists' => 'The selected unit is invalid. Please select a valid unit',
            "other_categories.array" => "The other categories field must be an array",
            "other_categories.exists" => "The selected other categories is invalid. Please select a valid other categories",
            "tags.string" => "The tags field must be a string",
            "brand_model.validate_app" => "Is not install brand_model module",
            "brand_model.brand_id.required_with" => "The brand field is required",
            "brand_model.brand_id.exists" => "The selected brand is invalid. Please select a valid brand",
            "linked.array" => "The linked field must be an array",
            "linked.*.linked_id.exists" => "The selected linked product is invalid. Please select a valid linked product",
            "linked.*.two_way.in" => "Invalid two way value. Please select a valid two way value. Valid two way values are: 0, 1",
            "status_id.exists" => "The selected status is invalid. Please select a valid status",
            "out_of_stock_id.exists" => "The selected out of stock status is invalid. Please select a valid out of stock status",
            'variants.*.v1.max' => "The maximum allowed characters for 'v1' are {max}",
            'variants.*.v1_id.exists' => 'The selected v1 is invalid. Please select a valid v1',
            'variants.*.v2.validate_parameters' => 'Invalid parameter value. Please select a valid parameter value',
            'variants.*.v2.max' => "The maximum allowed characters for 'v2' are {max}",
            'variants.*.v2_id.exists' => 'The selected v2 is invalid. Please select a valid v2',
            'variants.*.v3.validate_parameters' => 'Invalid parameter value. Please select a valid parameter value',
            'variants.*.v3.max' => "The maximum allowed characters for 'v3' are {max}",
            'variants.*.v3_id.exists' => 'The selected v3 is invalid. Please select a valid v3',
        ];
    }

    /**
     * @param \Illuminate\Contracts\Validation\Validator $validator
     * @return void
     */
    public function withValidator(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        $validator->sometimes('category_id', 'required', function ($input): bool {
            $return = true;
            if ($this->isDraft) {
                $return = false;
            } elseif ($this->input('type') == Product::TYPE_BUNDLE) {
                $return = false;
            }

            return $return;
        });

        $validator->addExtension('type_validations', fn($attribute, $value, $parameters, $validator): bool => $this->validateType($attribute, $value, $parameters, $validator));

        $validator->addExtension('continue_selling_validations', function (): bool {
            if ($this->input('tracking', 'no') == 'no' && $this->input('continue_selling') == 'yes') {
                return false;
            }

            return true;
        });

        $validator->addExtension('threshold_validations', function (): bool {
            if ($this->input('tracking', 'no') == 'no' && $this->input('threshold') != null) {
                return false;
            }

            return true;
        });

        $validator->addExtension('validate_parameters', fn($attribute, $value, $parameters, $validator): bool => $this->validateParameters($attribute, $value, $parameters, $validator));

        $validator->addExtension('validate_variants', fn($attribute, $value, $parameters, $validator): bool => $this->validateVariants($attribute, $value, $parameters, $validator));

        $validator->addExtension('validate_price', function ($attribute, $value): bool {
            $index = explode('.', $attribute)[1];
            $price_type = $this->input('variants.' . $index . '.type', 'price');
            if ($price_type == 'price') {
                if ($value != '') {
                    if (!empty($variant['price'])) {
                        try {
                            LibValidate::currencyAmount($variant['price'], 10);
                        } catch (Error) {
                            return false;
                        }
                    }
                }
            } elseif ($price_type == 'percent') {
                try {
                    LibValidate::percent($value, 100);
                } catch (Error) {
                    return false;
                }
            }

            return true;
        });

        $validator->addExtension('variants_unique', function (): bool {
            $parameters_count = count($this->input('p') ?? []);
            $variants = $this->input('variants') ?? [];
            $combinations = [];
            foreach ($variants as $variant) {
                $combinations[] = [$variant['v1'] ?? null, $variant['v2'] ?? null, $variant['v3'] ?? null];
            }

            foreach ($combinations as $search_key => $search) {
                foreach ($combinations as $key => $combination) {
                    if ($key === $search_key) {
                        continue;
                    }

                    for ($i = 0; $i < $parameters_count; $i++) {
                        if ($search[$i] !== $combination[$i]) {
                            continue 2;
                        }
                    }

                    return false;
                }
            }

            return true;
        });

        $validator->addExtension('validate_app', fn($attribute, $value, $parameters, $validator) => \App\Applications\Facades\Apps::installed($parameters[0]));

        $validator->addExtension('validate_page', function ($attribute, $value, $parameters, $validator): void {

        });

        $validator->addExtension('validate_discount_price', function ($attribute, $value): bool {
            if ($value != '') {
                try {
                    LibValidate::currencyAmount($value, 10);
                } catch (Error) {
                    return false;
                }
            }

            return true;
        });
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     * @param $validator
     * @return bool
     */
    protected function validateType($attribute, $value, $parameters, $validator): bool
    {
        if ($this->has('p') && $this->input('type') == Product::TYPE_DIGITAL) {
            return false;
        }

        return true;
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     * @param $validator
     * @return bool
     */
    protected function validateParameters($attribute, $value, $parameters, $validator): bool
    {
        $parameters = $this->input('p', []);
        if ($parameters instanceof Arrayable) {
            $parameters = $parameters->toArray();
        }

        $fields['p'] = array_filter($parameters);
        if (!empty($fields['p'])) {
            $parameter_names = [];
            $parameter_ids = [];
            foreach ($fields['p'] as $parameter) {
                $parameter_names[] = $parameter['name'];
                $parameter_ids[] = $parameter['id'];
            }

            if (count($fields['p']) != count(array_unique($parameter_names)) || count($fields['p']) != count(array_unique($parameter_ids))) {
                $validator->setCustomMessages([$attribute => __en('product.err.duplicate_parameters')]);
                return false;
            }

            foreach ($fields['p'] as $parameter) {

                if (empty($parameter)) {
                    $validator->setCustomMessages([$attribute => __en('product.err.invalid_parameter')]);
                    return false;
                }

                $length = Text::length($parameter['name']);

                if ($length < 1 || $length > 191) {
                    $validator->setCustomMessages([$attribute => __en(sprintf(__('product.err.parameter_must_be_between_%1$s_and_%2$s_characters_long'), 1, 191))]);
                    return false;
                }
            }
        } elseif (!empty($this->input('variants'))) {
            $validator->setCustomMessages([$attribute => __en('product.err.no_parameters_chosen')]);
            return false;
        }

        return true;
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     * @param $validator
     * @return bool
     */
    protected function validateVariants($attribute, $value, $parameters, $validator): bool
    {
        $parameters_count = count($this->input('p', []));

        $variants = $this->input('variants', []);

        if (count($variants) > $this->variants_max) {
            $this->validator->setCustomMessages([$attribute => __en(sprintf(__('variant.err.max_allowed_%1$s_exceeded'), $this->variants_max))]);
            return false;
        }

        if ($parameters_count === 0) {
            $validator->setCustomMessages([$attribute => __en('variant.err.parameters_required')]);
            return false;
        }

        return true;
    }
}
