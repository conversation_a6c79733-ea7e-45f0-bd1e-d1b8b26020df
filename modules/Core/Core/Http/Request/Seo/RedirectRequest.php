<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Request\Seo;

use App\Common\Section;
use App\Models\Blog\Article;
use App\Models\Blog\Blog;
use App\Models\Page\Page;
use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Models\Redirect\Redirect;
use Illuminate\Foundation\Http\FormRequest;
use App\Models\Product\Vendor;

class RedirectRequest extends FormRequest
{
    public function rules(): array
    {
        $rules = [
            'old_url' => 'required|string|is_existing_url',
            'location' => 'required|in:' . implode(',', Redirect::getTypesKeys()),
            'item_id' => 'validate_item',
            'new_url' => 'validate_new_url'
        ];

        return $rules;
    }

    #[\Override]
    public function messages(): array
    {
        return [
            'old_url.required' => 'Old URL is required',
            'old_url.string' => 'Old URL must be a string',
            'location.required' => 'Location is required',
            'location.in' => 'Location is not valid',
            'item_id.validate_item' => 'Item is not valid',
            'new_url.validate_new_url' => 'New URL is not valid',
            'old_url.is_existing_url' => 'Old URL is already exist'
        ];
    }


    /**
     * @param \Illuminate\Contracts\Validation\Validator $validator
     * @return void
     */
    public function withValidator(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        $validator->addExtension('validate_item', function ($attribute, $value, $parameters, $validator): bool {
            if ($this->input('location') == 'category') {
                if (!Category::find($value)) {
                    $validator->errors()->add($attribute, __en('category.err.not_found'));
                    return false;
                }
            } elseif ($this->input('location') == 'product') {
                if (!Product::find($value)) {
                    $validator->errors()->add($attribute, __en('product.err.not_found'));
                    return false;
                }
            } elseif ($this->input('location') == 'page') {
                if (!Page::find($value)) {
                    $validator->errors()->add($attribute, __en('page.err.not_found'));
                    return false;
                }
            } elseif ($this->input('location') == 'vendor') {
                if (!Vendor::find($value)) {
                    $validator->errors()->add($attribute, __en('vendor.err.not_found'));
                    return false;
                }
            } elseif ($this->input('location') == 'manual') {
                return true;
            } elseif ($this->input('location') == 'external') {
                return true;
            } elseif ($this->input('location') == 'section') {
                return true;
            } elseif ($this->input('location') == 'blog') {
                if (!Blog::find($value)) {
                    $validator->errors()->add($attribute, __en('vendor.err.not_found'));
                    return false;
                }
            } elseif ($this->input('location') == 'article') {
                if (!Article::find($value)) {
                    $validator->errors()->add($attribute, __en('vendor.err.not_found'));
                    return false;
                }
            }

            return true;
        });
        $validator->addExtension('validate_new_url', function ($attribute, $value, $parameters, $validator): bool {
            if ($this->input('location') == 'section') {
                if (!in_array($value, (new Section())->get())) {
                    $validator->errors()->add($attribute, 'Value is not valid');
                    return false;
                }
            } elseif (in_array($this->input('location'), ['manual', 'external'])) {
                if (empty($value)) {
                    $validator->errors()->add($attribute, 'Value is required');
                    return false;
                }
            }

            return true;
        });
        $validator->addExtension('is_existing_url', function ($attribute, $value, $parameters, $validator): bool {
            $id = $this->route('id');
            if (empty($id)) {
                $is_exist = (Redirect::where('old_url', urldecode((string) $this->input('old_url')))->count() == 0) ? false : true;
            } else {
                $is_exist = (Redirect::where('old_url', urldecode((string) $this->input('old_url')))->where('id', '!=', $id)->count() == 0) ? false : true;
            }

            if ($is_exist == true) {
                $validator->errors()->add($attribute, 'Old URL is already exist');
                return false;
            }

            return true;
        });
    }
}
