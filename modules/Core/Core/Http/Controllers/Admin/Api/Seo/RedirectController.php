<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Controllers\Admin\Api\Seo;

use App\Common\Section;
use App\Models\Redirect\Redirect;
use Modules\Core\Core\Http\Filters\Seo\RedirectFilter;
use Modules\Core\Core\Http\Request\Seo\RedirectRequest;
use Modules\Core\Core\Traits\GridFilters;

class RedirectController
{
    use GridFilters;

    public function index()
    {
        $model = Redirect::with('item');
        $records = $this->getRecords($model, RedirectFilter::class);
        try {
            $records->transform(fn($item): array => $this->transform($item));

            $records->setMeta('type', Redirect::getTypesKeys());
            $records->setMeta('sections', (new Section())->get());
            return response()->json($records);
        } catch (\Exception $exception) {
            return response()->json([
                $exception->getMessage()
            ], 500);
        }
    }

    /**
     * @param Redirect $item
     * @return array
     */
    private function transform(Redirect $item): array
    {
        if ($item->item) {
            if ($item->location == 'category') {
                $arr = [
                    'id' => $item->item->id,
                    'name' => $item->item->path->implode('name', ' > '),
                ];
            } else {
                $arr = [
                    'id' => $item->item->id,
                    'name' => $item->item->name,
                ];
            }
        }

        return [
            'id' => $item->id,
            'old_url' => $item->old_url,
            'new_url' => $item->getNewUrlFormatted(),
            'item' => $arr ?? null,
            'location' => $item->location,
        ];
    }

    /**
     * @param RedirectRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(RedirectRequest $request)
    {
        $record = Redirect::create([
            'location' => $request->input('location'),
            'old_url' => $request->input('old_url'),
            'new_url' => $request->input('new_url'),
            'item_type' => $request->input('item_type'),
            'item_id' => $request->input('item_id'),
        ]);
        return response()->json($this->transform($record));
    }

    /**
     * @param RedirectRequest $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(RedirectRequest $request, $id)
    {
        $record = Redirect::findOrFail($id);
        $record->update([
            'location' => $request->input('location'),
            'old_url' => $request->input('old_url'),
            'new_url' => $request->input('new_url'),
            'item_type' => $request->input('location'),
            'item_id' => $request->input('item_id'),
        ]);
        return response()->json($this->transform($record));
    }

    /**
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function delete($id)
    {
        $record = Redirect::findOrFail($id);
        $record->delete();
        return response()->json([], 204);
    }
}
