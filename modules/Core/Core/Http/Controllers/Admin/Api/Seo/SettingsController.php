<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Controllers\Admin\Api\Seo;

use App\Common\Theme;
use App\Exceptions\Error;
use App\Exceptions\Errors;
use App\Helper\Validator;
use App\Helper\YesNo;
use App\Http\Controllers\Controller;
use App\Models\StoreFront\FrontWidget;
use Illuminate\Support\Arr;
use Modules\Core\Core\Helpers\Seo\SeoSettingsFormatter;
use Illuminate\Http\Request;

class SettingsController extends Controller
{
    protected \Modules\Core\Core\Helpers\Seo\SeoSettingsFormatter $formatter;

    public function __construct()
    {
        $this->formatter = new SeoSettingsFormatter();
    }

    public function index()
    {
        return response()->json($this->formatter->toArray());
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateRobotsTxt(Request $request)
    {
        $setting = setting();
        $robots = $request->input('robots');
        $setting->set('robots.txt', $robots);
        $setting->set('update_robots', \Carbon\Carbon::now()->timestamp);
        $setting->save();

        return response()->json([]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateRSSFeed(Request $request)
    {
        $this->validate($request, [
            'rss_feed_count' => 'int|min:1|required|max:100',
        ], [
            'rss_feed_count.min' => 'Minimum value is 1',
            'rss_feed_count.required' => 'rss_feed_count is required',
            'rss_feed_count.max' => 'Maximum value is 100',
        ]);

        $setting = setting();
        $setting->set(Arr::only($request->input(), [
            'rss_feed_count'
        ]));

        $setting->save();
        return response()->json([]);
    }

    /**
     * @param null $isActive
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateCanonicalActivity($isActive)
    {
        $setting = setting();
        $setting->set('canonical_is_active',  $isActive);
        $setting->save();

        return response()->json(['canonical_is_active' => $isActive]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function updateNoIndexLimit(Request $request)
    {
        $this->validate($request, [
            'noindex_query_limit' => 'required_with:allow_noindex_query_limit|int|min:0|max:5',
            'allow_noindex_query_limit' => 'required|int'
        ], [
            'noindex_query_limit.required_with' => 'Noindex query limit is required',
            'noindex_query_limit.min' => 'Minimum value is 0',
            'noindex_query_limit.max' => 'Maximum value is 5',
        ]);
        setting()
            ->set($request->all(['noindex_query_limit', 'allow_noindex_query_limit']))
            ->save();

        return response()->json([]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     * @throws Errors
     */
    public function updateAddThisSettings(Request $request)
    {
        $data = $request->input();
        $widget = $this->_validateAddThisSettings($data);
        $data['default_content'] = false;

        $widget->saveSettings($data);

        FrontWidget::firstOrNew(['mapping' => $widget->getWidgetName()])->fill([
            'settings' => $data,
            'global' => 'no', // TODO: check if this is needed
        ])->save();

        setting()->set('og_image_url', $request->get('og_image_url'))->save();

        return response()->json([]);
    }

    /**
     * @param array $data
     * @return mixed
     * @throws Error
     * @throws Errors
     * @throws \Exception
     */
    protected function _validateAddThisSettings(array &$data)
    {
        $widget = \Widget::get('extra.addThisShare');
        $name_mapping = $widget->getWidgetName();
        $widget_mapping = Theme::get(sprintf('widgets.%s.map', $name_mapping));
        $restrictions = $widget->getRestrictions();

        if (\Illuminate\Support\Arr::dot($restrictions) != $restrictions) {
            $data = Arr::only(\Illuminate\Support\Arr::dot($data), array_keys(\Illuminate\Support\Arr::dot($restrictions)));
        } else {
            $data = Arr::only($data, array_keys($restrictions));
        }

        $validator = new Validator(\Illuminate\Support\Arr::dot($restrictions), $widget_mapping);
        try {
            $validator->validate($data);
        } catch (\Exception $exception) {
            if ($exception instanceof Errors) {
                $errors = [];
                foreach ($exception->getErrors() as $field => $err) {
                    if (is_numeric($field)) {
                        $errors[] = $err;
                    } else {
                        $errors[$this->_formatAddThisField($field)] = $err;
                    }
                }

                throw new Errors($errors);
            }

            throw $exception;
        }

        $arr = [];
        foreach ($data as $k => $v) {
            \Illuminate\Support\Arr::set($arr, $k, $v);
        }

        $data = $arr;
        return $widget;
    }

    /**
     * @param $field
     * @return string
     */
    protected function _formatAddThisField($field)
    {
        if (!str_contains((string) $field, '.')) {
            return $field;
        }

        $parts = explode('.', (string) $field);
        $prefix = array_shift($parts);
        return $prefix . '[' . implode('][', $parts) . ']';
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function updateMetaPageTitle(Request $request)
    {
        $this->validate($request, [
            'meta_page' => 'required'
        ], [
            'meta_page.required' => 'Meta page is required'
        ]);
        $setting = setting();
        $setting->set(Arr::only($request->input(), [
            'meta_page'
        ]));

        $setting->save();

        return response()->json([]);
    }

}
