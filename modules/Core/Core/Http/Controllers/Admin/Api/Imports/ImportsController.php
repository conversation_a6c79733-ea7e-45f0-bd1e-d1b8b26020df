<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Controllers\Admin\Api\Imports;

use App\Exceptions\HttpNotFound;
use App\Helper\CSVFileAnalyst;
use App\Helper\CsvImporter;
use App\Helper\Import\BlogFormatter;
use App\Helper\Import\CustomersFormatter;
use App\Helper\Import\ProductsFormatter;
use App\Helper\Import\RedirectsFormatter;
use App\Http\Controllers\Controller;
use App\Models\Customer\Customer;
use App\Models\Queue\SiteQueue;
use App\Models\Router\Exceptions;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Modules\Apps\Imports\BlogCsvImport\BlogCVSImportManager;
use Modules\Apps\Imports\CsvImport\CSVImportManager;
use Modules\Core\Core\Http\Request\Imports\ImportRequest;
use Modules\Core\Core\Models\Imports\CsvModel;
use Modules\Core\Core\Models\MongoDb\CC2FaTasks;
use Modules\Core\Core\Traits\CC2FaTasks\CC2FaTaskHelper;
use Illuminate\Http\Request;
use Modules\Core\Core\Traits\GridFilters;
use Illuminate\Support\Facades\Storage;
use Exception;
use Schema;
use Throwable;

class ImportsController extends Controller
{
    use CC2FaTaskHelper;
    use GridFilters;

    public const PRODUCTS_DEFAULT_SETTINGS = [
        'has_header_line' => '0',
        'publish_as_active' => 'no',
        'publish_as_featured' => '0',
        'publish_as_new' => 'no',
        'require_shipping' => 'no',
        'quantity_tracking' => 'yes',
        'continue_sell' => 'no',
    ];

    public const CUSTOMERS_DEFAULT_SETTINGS = [
        'has_header_line' => '0',
    ];

    public const REDIRECTS_DEFAULT_SETTINGS = [
        'has_header_line' => '0',
    ];

    public const BLOG_DEFAULT_SETTINGS = [
        'has_header_line' => '0',
    ];

    protected $delimiter = ',';

    protected $line_delimiter = '\\r\\n';

    protected $first_row = [];

    protected $requiredHash = [
        CC2FaTasks::ACTION_IMPORT_CUSTOMERS => 'customers',
        CC2FaTasks::ACTION_IMPORT_SUBSCRIBERS => 'subscribers',
    ];

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $model = new CsvModel();
        $results = $this->getRecords($model);

        return response()->json($results);
    }

    /**
     * @param $type
     * @param null $hash
     * @return JsonResponse
     */
    public function meta($type, $hash = null): JsonResponse
    {
        switch ($type) {
            case 'products':
                $required_fields = ProductsFormatter::$products_import_required_fields;
                $import_fields = ProductsFormatter::$products_import_fields;
                $multiple_fields = ProductsFormatter::$products_multiple_fields;
                $default_settings = self::PRODUCTS_DEFAULT_SETTINGS;
                break;
            case 'customers':
                if (($check = $this->checkHashVue(CC2FaTasks::ACTION_IMPORT_CUSTOMERS, $hash)) !== null) {
                    return $check;
                }

                $required_fields = CustomersFormatter::$import_required_fields;
                $import_fields = CustomersFormatter::$import_fields;
                $multiple_fields = [];
                $default_settings = self::CUSTOMERS_DEFAULT_SETTINGS;
                break;
            case 'redirects':
                $required_fields = RedirectsFormatter::$redirects_import_required_fields;
                $import_fields = RedirectsFormatter::$redirects_import_fields;
                $multiple_fields = [];
                $default_settings = self::REDIRECTS_DEFAULT_SETTINGS;
                break;
            case 'blog':
                $required_fields = BlogFormatter::$blog_import_required_fields;
                $import_fields = BlogFormatter::$blog_import_fields;
                $multiple_fields = [];
                $default_settings = self::BLOG_DEFAULT_SETTINGS;
                break;
            default:
                throw new HttpNotFound();
        }

        return response()->json([
            'required_fields' => $required_fields,
            'import_fields' => $import_fields,
            'multiple_fields' => $multiple_fields,
            'default_settings' => $default_settings,
            'required_2fa' => in_array($type, $this->requiredHash),
        ]);
    }

    /**
     * @param ImportRequest $request
     * @param string $type
     * @param null|mixed $action
     * @param null|mixed $hash
     * @return \Illuminate\Http\JsonResponse
     * @throws Exception
     */
    public function save(ImportRequest $request, string $type, $action = null, $hash = null)
    {
        //        if($request->__log) {
        //            $request->__log->update([
        //                'status' => CC2FaTasks::STATUS_VERIFIED,
        //            ]);
        //        }

        if ($action == 'import_customers') {
            $this->validate($request, [
                'customer_group_id' => 'required|integer|exists:type__customer_groups,id',

            ], [
                'customer_group_id.required' => 'Customer group is required',
                'customer_group_id.integer' => 'Customer group must be an integer',
                'customer_group_id.exists' => 'Customer group not found',
            ]);
        }

        if (in_array($action, [CC2FaTasks::ACTION_IMPORT_CUSTOMERS, CC2FaTasks::ACTION_IMPORT_SUBSCRIBERS])) {
            if (($check = $this->checkHashVue(CC2FaTasks::ACTION_IMPORT_CUSTOMERS, $hash)) !== null) {
                return $check;
            }
        }

        return $this->saveFileData($request->file('import_file'), $type);
    }

    /**
     * @param $file
     * @param $type
     * @return \Illuminate\Http\JsonResponse
     * @throws Exception
     */
    protected function saveFileData(\Illuminate\Http\UploadedFile $file, $type)
    {
        $settings = request()->except(['import_file']);

        if ($type == 'customers' && empty($settings['customer_group_id'])) {
            $settings['customer_group_id'] = Customer::groupGetDefaultGroupId();
        }

        try {
            $upload = $this->uploadContentInDatabase($file, $type, $settings);
        } catch (Exception $exception) {
            Exceptions::createFromThrowable($exception);
            return response()->json([
                'error' => 'Invalid file',
            ], 500);
        }

        $fileName = site_dir('tmp/' . \Illuminate\Support\Str::slug($file->getClientOriginalName()) . '.tmp');

        if ($type === 'blog') {
            $manager = new BlogCVSImportManager();
        } else {
            $manager = new CSVImportManager();
        }

        $manager->updateSetting($type, $settings);

        if (Storage::exists($fileName)) {
            Storage::delete($fileName);
        }

        unlink($file->getPathname());

        return response()->json([
            'id' => $upload->id,
        ]);
    }

    /**
     * @param UploadedFile $fileData
     * @param string $type
     * @param array $settings
     * @return array
     * @throws Exception
     */
    protected function uploadContentInDatabase(UploadedFile $fileData, string $type, array $settings): CsvModel
    {
        $total_columns = $this->createTable($fileData, $tableName = 'csv_import_' . \Carbon\Carbon::now()->timestamp, $type);

        $ignored_lines = 0;
        $importer = new CsvImporter($fileData->getPathname(), 0, $this->delimiter, 0);

        if ($ignored_lines) {
            $importer->get($ignored_lines);
        }

        $total = 0;
        while ($data = $importer->get(500)) {
            $total++;
            \Illuminate\Support\Facades\DB::table($tableName)->insert(array_map(function (array $row) use ($total_columns) {
                $row = Arr::first(array_chunk($row, $total_columns));
                for ($i = Arr::last(array_keys($row)) + 1; $i < $total_columns; $i++) {
                    $row[$i] = null;
                }

                return $row;
            }, $data));
        }

        if (Schema::hasTable($tableName)) {
            Schema::table($tableName, function (Blueprint $table): void {
                $table->increments('row_id')->unsigned();
            });
        }

        SiteQueue::createQueueByMapping('delete_csv_tables');
        $manager = $type == 'blog' ? new BlogCVSImportManager() : new CSVImportManager();
        $manager->updateSetting('table', $tableName);

        return CsvModel::create([
            'type' => $type,
            'tableName' => $tableName,
            'filename' => $fileData->getClientOriginalName(),
            'settings' => $settings,
        ]);
    }

    /**
     * @param UploadedFile $file
     * @param $tableName
     * @param $type
     * @return int
     */
    protected function createTable(UploadedFile $file, $tableName, $type): int
    {
        $handle = fopen($file->getPathname(), 'r');

        $analyse = CSVFileAnalyst::analyseFile($file->getPathname(), 10, $type === 'redirects');
        $this->delimiter = $analyse['delimiter']['value'];
        $this->line_delimiter = $analyse['line_ending']['value'];

        $first_row = fgetcsv($handle, null, $this->delimiter);
        fclose($handle);

        $this->first_row = $first_row;
        $total_columns = count($first_row);

        if (!Schema::hasTable($tableName)) {
            Schema::create($tableName, function (Blueprint $table) use ($total_columns): void {
                for ($i = 0; $i < $total_columns; $i++) {
                    $table->longText($i);
                }
            });
        } else {
            \Illuminate\Support\Facades\DB::table($tableName)->truncate();
        }

        return $total_columns;
    }

    /**
     * @param $table
     * @param mixed $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFirstLine($id)
    {
        $csvTask = CsvModel::findOrFail($id);
        $rows = $csvTask->csvImport();
        $first_line = $rows->first();
        unset($first_line->row_id);
        return response()->json($first_line);
    }

    /**
     * @param Request $request
     * @param $id
     * @param null|mixed $hash
     * @return \Illuminate\Http\JsonResponse
     * @throws Throwable
     * @throws \App\Exceptions\Error
     * @throws \Illuminate\Validation\ValidationException
     */
    public function mapping(Request $request, $id, $hash = null)
    {
        $csvTask = CsvModel::findOrFail($id);

        if (in_array('import_' . $csvTask->type, [CC2FaTasks::ACTION_IMPORT_CUSTOMERS, CC2FaTasks::ACTION_IMPORT_SUBSCRIBERS])) {
            if (($check = $this->checkHashVue(CC2FaTasks::ACTION_IMPORT_CUSTOMERS, $hash)) !== null) {
                return $check;
            }
        }

        if ($csvTask->type == 'products') {
            $this->validate($request, [
                'import_binds' => 'required|array',
                'import_binds.product\\.id' => 'required',
                'import_binds.product\\.name' => 'required',
            ], [
                'import_binds.required' => 'Import binds are required',
                'import_binds.array' => 'Import binds must be an array',
                'import_binds.product\\.id.required' => 'Product ID is required',
                'import_binds.product\\.name.required' => 'Product Name is required',
            ]);
        }

        if ($csvTask->type == 'redirects') {
            $this->validate($request, [
                'import_binds' => 'required|array',
                'import_binds.redirect\\.old_url' => 'required',
                'import_binds.redirect\\.new_url' => 'required',
            ], [
                'import_binds.required' => 'Import binds are required',
                'import_binds.array' => 'Import binds must be an array',
                'import_binds.redirect\\.old_url.required' => 'Old URL is required',
                'import_binds.redirect\\.new_url.required' => 'New URL is required',
            ]);
        }

        $manager = $csvTask->type == 'blog' ? new BlogCVSImportManager() : new CSVImportManager();
        $table = $csvTask->tableName;
        $total = \Illuminate\Support\Facades\DB::table($table)->count();
        if ($csvTask->settings['has_header_line'] == 1) {
            \Illuminate\Support\Facades\DB::table($table)->orderBy('row_id')->limit(1)->delete();
        }

        $mapping = $request->input('import_binds', []);
        $csvTask->mapping = $mapping;
        $csvTask->total_products = $total;
        $csvTask->status = 'in_progress';
        $csvTask->save();
        if($csvTask->type == 'redirects'){
            SiteQueue::removeQueueByMapping('redirects_import');
            SiteQueue::executeQueueTask('redirects_import', ['settings' => $csvTask->settings]);
        } else {
            SiteQueue::removeQueueByMapping($csvTask->type . '_import_csv');
            SiteQueue::executeQueueTask($csvTask->type . '_import_csv', ['taskId' => $csvTask->id]);
        }
        $manager->setWorking();
        $manager->setTotal($total);
        return response()->json([
            'success' => true,
            'total' => $total,
        ]);
    }

    /**
     * @param $type
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\Error
     */
    public function progress($type)
    {
        $manager = $type == 'blog' ? new BlogCVSImportManager() : new CSVImportManager();
        return response()->json($manager->progress());
    }
}
