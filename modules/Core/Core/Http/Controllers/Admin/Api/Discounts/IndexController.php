<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Controllers\Admin\Api\Discounts;

use App\Common\DateTimeFormat;
use App\Exceptions\Error;
use App\Helper\Plan;
use App\Helper\YesNo;
use App\Http\Controllers\Controller;
use App\Models\Discount\Discount;
use App\Models\Gate\PlanFeature;
use App\Models\Router\Exceptions;
use App\Models\System\GlobalMetaData;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Core\Core\Http\Filters\Discount\DiscountAutocompleteFilter;
use Modules\Core\Core\Http\Filters\Discount\DiscountFilter;
use Modules\Core\Core\Http\Request\Discounts\DiscountRequest;
use Modules\Core\Core\Traits\GridFilters;
use Closure;
use Illuminate\Support\Arr;


class IndexController extends Controller
{
    use GridFilters;

    public function __construct()
    {
        $this->middleware(function (Request $request, Closure $next) {
            $discountKey = sprintf(
                'discount-%s',
                $request->route()->parameter('type', 'global')
            );

            if (!Plan::enabled($discountKey)) {

                $plans = \App\Models\Gate\Plan::active()
                    ->whereHas('details')
                    ->get()->filter(function ($plan) use ($discountKey): bool {
                        $value = Plan::featureValueModel($discountKey, $plan->mapping);
                        if (is_null($value) || (is_bool($value) && $value === false)) {
                            return true;
                        }

                        return false;
                    });

                return response()->json([
                    'msg' => __('restrict.plan_feature_not_enabled'),
                    'plans' => $plans->pluck('name')->implode(', ')
                ], 403);
            }

            return $next($request);

        })->only('store');
    }

    /**
     * @return JsonResponse
     * @throws Error
     */
    public function search(): JsonResponse
    {
        // Create the base model query
        $model = Discount::orderBy('name', 'asc')->select(['id', 'name']);

        // Apply the filters using your custom filtering logic
        $results = $this->getRecords($model, DiscountAutocompleteFilter::class);

        // Transform the results to the desired format
        $results->transform(fn(Discount $discount): array => [
            'id' => $discount->id,
            'name' => $discount->name,
        ]);

        // Return the results as a JSON response
        return response()->json($results);
    }

    /**
     * Get all discounts
     * @return JsonResponse
     * @throws Error
     */
    public function index()
    {
        $model = Discount::information()
            ->whereNotIn('discounts.type', ['label', 'banner'])->with(['meta_data_single' => function ($query): void {
                /** @var GlobalMetaData $query */
                $query->whereParameter('last_generate');
            }])->withCount(['meta_data as is_countdown' => function ($query): void {
                $query->where('parameter', 'countdown_minutes');
            }, 'codes_pro']);

        $records = $this->getRecords($model, DiscountFilter::class);
        $records->transform(fn($discount): \App\Models\Discount\Discount => $this->transform($discount));

        $records->setMeta('types', $this->getChooseTypes());
        $records->setMeta('features', $this->getFeatures());
        $records->setMeta('used_statuses', Discount::getUsedStatuses());
        return response()->json($records);
    }

    /**
     * Transform the model
     * @param Discount $discount
     * @return Discount
     */
    protected function transform(Discount $discount): Discount
    {
        return $discount;
    }

    /**
     * @return array
     */
    protected function getChooseTypes(): array
    {
        return [
            [
                'title' => 'Cart rules',
                'key' => 'cart_rules',
                'icon' => 'all-icons-discount-type-cart-rules',
                'text' => 'This discount is applicable only if its conditions match in the cart',
                'visual' => false
            ],
            [
                'title' => 'Global discount',
                'key' => 'global',
                'icon' => 'all-icons-discount-type-global',
                'text' => 'This discount is always applicable. No discount code is required',
                'visual' => false
            ],
            [
                'title' => 'Discount with promo code',
                'key' => 'code',
                'icon' => 'all-icons-discount-type-code',
                'text' => 'This discount is applicable only when the discount code is provided',
                'visual' => false
            ],
            [
                'title' => 'Discount with multiple promo codes - Container',
                'key' => 'container',
                'icon' => 'all-icons-discount-type-multiple',
                'text' => 'WIth this discount type you can create a discount with multiple promo codes',
                'visual' => false
            ],
            [
                'title' => 'Fixed discount',
                'key' => 'fixed',
                'icon' => 'all-icons-discount-type-fixed',
                'text' => 'With the fixed discount you can set a different fixed prices for one or more products',
                'visual' => false
            ],
            [
                'title' => 'Quantity discount',
                'key' => 'quantity',
                'icon' => 'all-icons-discount-type-quantity',
                'text' => 'With this discount you can decrease a product`s price based on the quantity added to the cart',
                'visual' => false
            ],
            [
                'title' => 'Countdown discount',
                'key' => 'countdown',
                'icon' => 'all-icons-discount-type-countdown',
                'text' => 'With this discount you will be able to lower the price of the entire order for a certain amount of time',
                'visual' => false
            ],
            [
                'title' => 'Discount code',
                'key' => 'code-pro',
                'icon' => 'all-icons-discount-type-code-pro',
                'text' => 'Create multiple promo codes with different multiple discount terms',
                'visual' => false
            ],
            [
                'title' => 'Label Discount',
                'key' => 'product-labels',
                'icon' => 'all-icons-discount-type-label',
                'text' => 'With this type of discount you can create new label for your products',
                'visual' => true
            ],
            [
                'title' => 'Visual Label/Image',
                'key' => 'product-banners',
                'icon' => 'all-icons-discount-type-banner',
                'text' => 'Here you can place a visual lable like sticker or image over specific product or group of products',
                'visual' => true
            ],
        ];
    }

    /**
     * Get features
     * @return array
     */
    protected function getFeatures(): array
    {
        $features = [];
        $featuresArray = ['discount-code-pro-generator', 'discount-code-pro'];
        foreach ($featuresArray as $feature) {
            if ($planFeature = PlanFeature::findByMap($feature)) {
                $features[] = [
                    'group' => 'discounts',
                    'id' => $planFeature->id,
                    'mapping' => $planFeature->mapping,
                    'name' => $planFeature->name_translated,
                    'current' => Plan::enabled($feature),
                    'used' => Plan::used($feature),
                    'suffix' => null,
                    'type' => [],
                    'cast' => $planFeature->cast,
                ];
            }
        }

        return $features;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateStatuses(Request $request)
    {
        $settings = setting();
        if (empty($statuses = $request->input('statuses', []))) {
            $settings->forget('discounts_used_statuses');
        } else {
            $settings->set('discounts_used_statuses', json_encode($statuses));
        }

        try {
            $settings->save();
            return response()->json(Discount::getUsedStatuses());
        } catch (\Throwable $throwable) {
            Exceptions::createFromThrowable($throwable);
            return response()->json([
                'msg' => $throwable->getMessage()
            ], 500);
        }

    }

    /**
     * Create new discount
     * @param Request $request
     * @param $type
     * @return JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(DiscountRequest $request, $type = null)
    {
        try {
            $data = $request->all();
            if (key_exists('date_start', $data) && !empty($data['date_start'])) {
                $data['date_start'] = \Carbon\Carbon::createFromFormat(DateTimeFormat::$date_formats[setting('date_format', 2)]['format'], $data['date_start'])->format('Y-m-d');
            }

            if (key_exists('date_end', $data) && !empty($data['date_end'])) {
                $data['date_end'] = \Carbon\Carbon::createFromFormat(DateTimeFormat::$date_formats[setting('date_format', 2)]['format'], $data['date_end'])->format('Y-m-d');
            }

            $allowedKeys = [
                'name', 'max_uses', 'code', 'type', 'type_value', 'order_over',
                'date_start', 'date_end', 'active', 'color', 'text_color', 'is_container',
                'position', 'timer_list', 'timer_details', 'geo_zone_id', 'code_prefix',
                'code_format', 'code_apply', 'discount_amount_type_in_label', 'maxused_user',
                'only_customer', 'hide_discount_price', 'apply_regular_price',
                'barcode_prefix', 'msrp'
            ];

            $discount = \Illuminate\Support\Facades\DB::transaction(function () use ($request, $type, $data, $allowedKeys) {
                if (isset($type) && $type == 'code-pro') {
                    return Discount::create([
                        'name' => $request->input('name'),
                        'type' => 'code-pro',
                        'date_start' => Carbon::now(),
                        'active' => $request->input('active', YesNo::True),
                    ]);
                } else {
                    return Discount::create(Arr::only($data, $allowedKeys));
                }
            });

            if (isset($type) && $type != 'code-pro') {
                $force_save = 0;
                if (in_array($discount->type, ['flat', 'percent'])) {
                    $force_save = $request->input('settings') == 'order_over' && $request->input('force_save');
                } elseif ($discount->type == 'shipping') {
                    $force_save = $request->input('force_save');
                }

                $discount->update([
                    'force_save' => $force_save,
                ]);

                Discount::updateByType($data, $discount);
            }

            return response()->json($this->transform($discount));
        } catch (\Throwable $throwable) {
            Exceptions::createFromThrowable($throwable);
            return response()->json([
                'msg' => $throwable->getMessage()
            ], 500);
        }
    }

    /**
     * Update discount
     * @param DiscountRequest $request
     * @param $id
     * @param null|mixed $type
     * @return JsonResponse
     */
    public function update(DiscountRequest $request, $id, $type = null)
    {
        try {
            $discount = \Illuminate\Support\Facades\DB::transaction(function () use ($request, $id): void {
                $discount = Discount::findOrFail($id);
                $discount->update($request->only([
                    'name', 'max_uses', 'code', 'type', 'type_value', 'order_over', 'date_start', 'date_end', 'active', 'color', 'text_color', 'is_container', 'position', 'timer_list', 'timer_details', 'geo_zone_id', 'code_prefix', 'code_format', 'code_apply', 'discount_amount_type_in_label', 'maxused_user', 'only_customer', 'hide_discount_price', 'apply_regular_price', 'barcode_prefix', 'msrp'
                ]));
            });
            Discount::updateByType($request->all(), $discount, true);
            return response()->json($this->transform($discount));
        } catch (\Throwable $throwable) {
            Exceptions::createFromThrowable($throwable);
            return response()->json([
                'msg' => $throwable->getMessage()
            ], 500);
        }
    }

    /**
     *
     * @param Request $request
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|\Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function lastGenerate(Request $request)
    {
        $this->validate($request, [
            'ids' => 'required|array|exists:discounts,id',
            'ids.*' => 'integer'
        ], [
            'ids.required' => 'ids array is required',
            'ids.array' => 'ids must be an array',
            'ids.exists' => 'ids must be valid discount ids',
            'ids.*.integer' => 'ids must be integers'
        ]);
        $ids = $request->input('ids', []);
        $discounts = Discount::whereIn('id', $ids)->whereHas('meta_data_single', function ($query): void {
            /** @var GlobalMetaData $query */
            $query->whereParameter('last_generate');
        })->with(['meta_data_single' => function ($query): void {
            /** @var GlobalMetaData $query */
            $query->whereParameter('last_generate');
        }])->get()->map(function (Discount $discount): ?array {
            if ($discount->active == YesNo::True && ($discount->meta_data_single->parameter ?? '') == 'last_generate') {
                $extended = sprintf(
                    '<i class="fa fa-info-circle tooltips" title="%s"></i>',
                    __('discount.queue.generate.latest', ['date' => Carbon::createFromTimestamp((string)$discount->meta_data_single->value, 'UTC')->timezone(site('timezone'))->format(DateTimeFormat::getFormatByTemplate())])
                );
            } else {
                return null;
            }

            return [
                'id' => $discount->id,
                'info' => $extended
            ];
        })->filter();

        return response($discounts);
    }

    /**
     * Show discount information
     * @param $id
     * @return JsonResponse
     */
    public function show($id)
    {
        $model = Discount::information()->whereNotIn('discounts.type', ['label', 'banner'])->with(['meta_data_single' => function ($query): void {
            /** @var GlobalMetaData $query */
            $query->whereParameter('last_generate');
        }])->withCount(['meta_data as is_countdown' => function ($query): void {
            $query->where('parameter', 'countdown_minutes');
        }, 'codes_pro']);
        $discount = $model->findOrFail($id);
        return response()->json($this->transform($discount));
    }

    /**
     * Delete discount
     * @param $id
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $this->validate($request, [
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:discounts,id',
        ], [
            'ids.required' => 'ids array is required',
            'ids.array' => 'ids must be an array',
            'ids.*.integer' => 'each id must be an integer',
            'ids.*.exists' => 'each id must be a valid discount id',
        ]);
        Discount::whereIn('id', $request->input('ids'))->delete();
        return response()->json([], 204);
    }

    /**
     * Change status of discounts
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function changeStatus(Request $request): JsonResponse
    {
        $this->validate($request, [
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:discounts,id',
            'status' => 'required|in:yes,no',
        ], [
            'ids.required' => 'ids array is required',
            'ids.array' => 'ids must be an array',
            'ids.*.integer' => 'each id must be an integer',
            'ids.*.exists' => 'each id must be a valid discount id',
            'status.required' => 'status is required',
            'status.in' => 'status must be either yes or no',
        ]);

        Discount::whereIn('id', $request->input('ids'))->update(['active' => $request->input('status')]);

        return response()->json([], 204);
    }

}
