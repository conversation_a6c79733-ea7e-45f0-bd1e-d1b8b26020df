<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Controllers\Admin\Api\Discounts;

use Api\Http\Controllers\Controller;
use App\Events\Models\ProductUpdated;
use App\Helper\Temp\ProductTemp;
use App\Helper\YesNo;
use App\Models\Discount\Discount;
use App\Models\Discount\ProductToDiscount;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use Modules\Core\Core\Http\Filters\Discount\FixedFilter;
use Modules\Core\Core\Http\Request\Discounts\ProductsRequest;
use Modules\Core\Core\Traits\GridFilters;
use Illuminate\Http\Request;

class FixedController extends Controller
{
    use GridFilters;

    /**
     * Get all fixed products for discount
     * @param $id
     * @param mixed $discount_id
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\Error
     */
    public function index($discount_id)
    {
        $records = $this->getRecords($this->getModel($discount_id)->groupBy('product_id'), FixedFilter::class);
        $records->transform(function ($item) {
            $discount = $item->getAttributes();

            $item->product_id = $item->product->id;
            $item->product_name = $item->product->name;
            $item->url_handle = $item->product->url_handle;
            $item->img = optional($item->product->image)->image_url;
            $item->discount = $discount;
            $item->items = $this->transformProducts($item->product);
            return $item->only(['id', 'discount_name', 'product_name', 'product_id','img','url_handle', 'active', 'discount']);
        });

        return response()->json($records);
    }

    /**
     * @param mixed $discount_id
     * @return mixed
     */
    private function getModel($discount_id)
    {
        return Discount::whereType('fixed')->findOrFail($discount_id)->fixed_products()->with(['product.variant']);
    }

    /**
     * Get fixed product for discount
     * @param $discount_id
     * @param $product_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function get($discount_id, $product_id)
    {
        $discount = Discount::whereType('fixed')->findOrFail($discount_id);
        $item = $discount->fixed_products()->where('product_id', $product_id)->with(['product.variant', 'product.list_product_to_discount_cp'])->first();
        return response()->json([
            'id' => $product_id,
            'product_name' => $item->product->name,
            'discount' => [
                'id' => $discount->id,
                'name' => $discount->name,
            ],
            'items' => $this->transformProducts($item->product)
        ]);

    }

    /**
     * Save fixed product for discount
     * @param $discount_id
     * @param $product_id
     * @return \Illuminate\Http\JsonResponse
     */
    protected function transformProducts(Product $item)
    {
        foreach ($item->variants as $v => $variant) {
            $variants[$v] = [
                'variant_id' => $variant->id,
                'price_input' => $variant->price_input,
                'discount_price' => $item->list_product_to_discount_cp->where('variant_id', $variant->id)->first()->price_input ?? null,
                'msrp_price' => $item->list_product_to_discount_cp->where('variant_id', $variant->id)->first()->msrp_price ?? null,
            ];
            for ($i = 1; $i <= $item->total_variants; $i++) {
                $variants[$v] = array_merge($variants[$v], [
                    'p' . $i . '_id' => $item->getAttribute('p' . $i . '_id'),
                    'p' . $i => $item->getAttribute('p' . $i),
                    'v' . $i . '_id' => $variant->getAttribute('v' . $i . '_id'),
                    'v' . $i => $variant->getAttribute('v' . $i),
                ]);
            }
        }

        return $variants;
    }

    /**
     * @param ProductsRequest $request
     * @param $discount_id
     * @return \Illuminate\Http\JsonResponse
     * @throws \Throwable
     */
    public function store(ProductsRequest $request, $discount_id)
    {
        $discount = Discount::whereType('fixed')->findOrFail($discount_id);
        $getVariants = Variant::whereIn('id', array_column($request->all(), 'variant_id'))->get();
        \Illuminate\Support\Facades\DB::transaction(function () use ($request, $discount, $getVariants): void {
            foreach ($request->all() as $price) {
                $variantPrice = $getVariants->where('id', $price['variant_id'])->first()->price;
                Discount::addFixedDiscount($discount, new ProductToDiscount([
                    'product_id' => $price['product_id'],
                    'variant_id' => $price['variant_id'],
                    'price' => $price['price'],
                    'save' => (function ($price, $discount, $variant_price): int|float {
                        if ($discount->msrp && !empty($price['msrp'])) {
                            return $price['msrp'] - $price['fixed_price'];
                        }

                        return $variant_price - $price['price'];
                    })($price, $discount, $variantPrice),
                    'msrp_price' => !empty($price['msrp']) && $discount->msrp ? $price['msrp'] : null
                ]));
            }

            Product::whereIn('id', array_column($request->all(), 'product_id'))->get()->map(function ($product): void {
                $product->updateDefaultVariantByDiscountedPrice();
                ProductTemp::updateTempTableAndPopulateByProduct($product->id);
                $product::clearCache();
            });
        });

        return response()->json(['message' => 'Discounts added successfully'], 201);
    }

    /**
     * Update fixed product for discount
     * @param ProductsRequest $request
     * @param $discount_id
     * @param $product_id
     * @return \Illuminate\Http\JsonResponse
     * @throws \Throwable
     */
    public function update(ProductsRequest $request, $discount_id, $product_id)
    {
        $discount = Discount::whereType('fixed')->findOrFail($discount_id);
        $getVariants = Variant::whereIn('id', array_column($request->all(), 'variant_id'))->get();
        \Illuminate\Support\Facades\DB::transaction(function () use ($request, $discount, $getVariants, $product_id): void {
            ProductToDiscount::where('product_id', $product_id)->delete();
            foreach ($request->all() as $price) {
                $variantPrice = $getVariants->where('id', $price['variant_id'])->first()->price;
                Discount::addFixedDiscount($discount, new ProductToDiscount([
                    'product_id' => $price['product_id'],
                    'variant_id' => $price['variant_id'],
                    'price' => $price['price'],
                    'save' => (function ($price, $discount, $variant_price): int|float {
                        if ($discount->msrp && !empty($price['msrp'])) {
                            return $price['msrp'] - $price['fixed_price'];
                        }

                        return $variant_price - $price['price'];
                    })($price, $discount, $variantPrice),
                    'msrp_price' => !empty($price['msrp']) && $discount->msrp ? $price['msrp'] : null
                ]));
            }

            Product::whereIn('id', array_column($request->all(), 'product_id'))->get()->map(function ($product): void {
                $product->updateDefaultVariantByDiscountedPrice();
                ProductTemp::updateTempTableAndPopulateByProduct($product->id);
                $product::clearCache();
            });
        });

        return response()->json(['message' => 'Discounts updated successfully'], 200);
    }

    /**
     * Update status of fixed product for discount
     * @param $discount_id
     * @param $product_id
     * @param int $status
     * @return \Illuminate\Http\JsonResponse
     * @throws \Throwable
     */
    public function updateStatus(Request $request, $discount_id)
    {
        $this->validate($request, [
            'status' => 'required|in:yes,no',
            'product_ids.*' => 'required|integer|exists:product_to_discount,product_id',
        ], [
            'status.required' => 'Status is required',
            'status.in' => 'Status must be yes or no',
            'product_ids.*.required' => 'Product ID is required',
            'product_ids.*.integer' => 'Product ID must be an integer',
            'product_ids.*.exists' => 'Product does not exist',
        ]);

        $status = YesNo::toInt($request->input('status'));
        \Illuminate\Support\Facades\DB::transaction(function () use ($discount_id, $status, $request): void {
            foreach ($request->input('product_ids', []) as $product_id) {
                $discounts = ProductToDiscount::whereDiscountId($discount_id)->whereProductId($product_id)->get();
                $discounts->map(function (ProductToDiscount $discount) use ($status): void {
                    $discount->update(['active' => $status]);
                });

                if ($discounts->isNotEmpty()) {
                    event(new ProductUpdated($discounts->first()->product));
                    ProductTemp::updateTempTableAndPopulateByProduct($product_id);
                }
            }
        });
        return response()->json(['message' => 'Discounts updated successfully'], 200);

    }

    /**
     * @param Illuminate\Http\Request $request
     * @param mixed $discount_id
     * @return mixed
     */
    public function delete(Request $request, $discount_id)
    {
        $this->validate($request, [
            'ids.product_ids.*' => 'required|integer|exists:product_to_discount,product_id',
        ], [
            'ids.product_ids.*.required' => 'Product ID is required',
            'ids.product_ids.*.integer' => 'Product ID must be an integer',
            'ids.product_ids.*.exists' => 'Product does not exist',
        ]);
        $ids = $request->input('ids.product_ids');

        $discount = Discount::whereType('fixed')->findOrFail($discount_id);
        \Illuminate\Support\Facades\DB::transaction(function () use ($ids, $discount): void {
            $discounts = ProductToDiscount::whereDiscountId($discount->id)->whereIn('product_id', $ids)->get();
            $discounts->map(function (ProductToDiscount $discount): void {
                $discount->delete();
            });

            if ($discounts->isNotEmpty()) {
                event(new ProductUpdated($discounts->first()->product));

                ProductTemp::updateTempTableAndPopulateByProduct($ids);
            }
        });

        return response()->json(['message' => 'Discounts deleted successfully'], 204);
    }
}
