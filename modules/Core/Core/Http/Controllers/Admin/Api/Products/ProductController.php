<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Controllers\Admin\Api\Products;

use App\Events\Models\ProductUpdated;
use App\Exceptions\Error;
use App\Helper\Image;
use App\Helper\Import\ProductsFormatter;
use App\Helper\Plan;
use App\Helper\Temp\ProductTemp;
use App\Helper\YesNo;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Sitecp\ProductsController;
use App\Models\Category\PropertyValue;
use App\Models\Gate\PlanFeature;
use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Models\Product\ProductFilesGroups;
use App\Models\Product\Status;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;
use Modules\Apps\Administration\GroceryStore\Models\Units;
use Modules\Apps\Others\ProductReview\Models\ProductReview;
use Modules\Core\Core\Helpers\Grid;
use Modules\Core\Core\Helpers\Products\ProductFormatter;
use Modules\Core\Core\Http\Filters\Products\ProductAutocompleteFilter;
use Modules\Core\Core\Http\Filters\Products\ProductFilter;
use Modules\Core\Core\Http\Request\Products\AddPropertyToProductRequest;
use Modules\Core\Core\Http\Request\Products\ProductRequest;
use Modules\Core\Core\Traits\GridFilters;

/**
 *
 */
class ProductController extends Controller
{
    use GridFilters;

    /**
     * Display a list of products.
     *
     * This method retrieves a list of products with their related data such as
     * images, categories, vendor details, variants, and reviews. The records
     * are filtered and transformed for the response.
     *
     * @return JsonResponse The JSON response containing the list of products.
     * @throws Error If there is an error during retrieval or transformation.
     */
    public function index(): JsonResponse
    {
        $model = Product::whereTypeNotBundle()->with([
            'image', 'category.path:type__products_categories.id,name', 'vendor:id,name,url_handle',
            'p1r:id,name', 'p2r:id,name', 'p3r:id,name', 'variants' => function ($query): void {
                $query->withoutGlobalScopes()->with(['v1r:id,name', 'v2r:id,name', 'v3r:id,name']);
            }, 'variant' => function ($query): void {
                $query->withoutGlobalScopes()->with(['v1r:id,name', 'v2r:id,name', 'v3r:id,name']);
            }, 'meta_data', 'external_meta_data'
        ])->select('products.*')->with(['change_log' => function ($query): void {
            $query->groupBy('record_id');
        }])->with(['external_meta_data' => function ($query): void {
            $query->whereIn('integration', ['temporary', 'number']);
        }]);

        $records = $this->getRecords($model, ProductFilter::class, fn(): \Modules\Core\Core\Helpers\Grid => new Grid('date_added', 'desc'));

        $records->setMeta(
            'product_review',
            ProductFormatter::isAllowProductReview()
        );

        $records->setMeta('features', $this->getFeatures());

        $reviews = collect();
        if ($records->getMeta('product_review')) {
            $reviews = ProductReview::getSummaryByIds($records->pluck('id')->all());
        }

        $records->transform(fn(Product $product): array => ProductFormatter::make($product)
            ->addReviewSummary($reviews->get($product->id))->toList());

        return response()->json($records);
    }

    /**
     * @return JsonResponse
     */
    public function createSelect()
    {
        $types = ['simple'];
        if (Plan::enabled('multi_variants')) {
            $types[] = 'multiple';
        }

        if (Plan::enabled('digital_products')) {
            $types[] = 'digital';
        }

        if (ProductFormatter::isAllowMembership()) {
            $types[] = 'membership';
        }

        return response()->json([
            'types' => $types,
        ]);
    }

    /**
     * @param $type
     * @return JsonResponse
     */
    public function create($type)
    {
        $product = new Product([
            'type' => $type
        ]);

        $formatter = ProductFormatter::make($product);

        return response()->json($formatter->toForm());
    }

    /**
     * @param $id
     * @return JsonResponse
     */
    public function view($id): JsonResponse
    {
        $model = Product::whereTypeNotBundle()->with([
            'tabs', 'category.path:type__products_categories.id,name', 'vendor:id,name,url_handle',
            'categories.path:type__products_categories.id,name', 'smart_collections:product_selections.id,product_selections.name', 'list_product_to_discount_cp', 'list_product_to_discount_cp.discount',
            'p1r:id,name', 'p2r:id,name', 'p3r:id,name', 'variants' => function ($query): void {
                $query->with(['v1r:id,name', 'v2r:id,name', 'v3r:id,name', 'images']);
            },
            'variants.discount',
            'images' => function ($q): void {
                $q->whereNull('gallery_id')->with(['gallery']);
            },
            'categoryPropertiesOptions', 'categoryPropertiesOptions.property', 'files', 'tags', 'upload', 'meta_data', 'external_meta_data',
            'linkedProducts',
        ]);

        if (ProductFormatter::isAllowBrandModel()) {
            $model->with('to_brand_model.model.brand');
        }

        $product = $model->find($id);

        if (!$product) {
            return response()->json([], 404);
        }

        $formatter = ProductFormatter::make($product);

        return response()->json($formatter->toForm());
    }

    /**
     * Update the status of multiple products.
     *
     * This method allows updating the status of multiple products based on the
     * provided status name and status value. Valid status names are:
     * 'active', 'new', 'hidden', and 'featured'. It also checks if the plan
     * feature for hidden products is enabled.
     *
     * @param Request $request The request instance.
     * @param string $statusName The name of the status to update ('active', 'new', 'hidden', 'featured').
     * @param int $status The new status value.
     * @return JsonResponse The JSON response containing the result of the status update.
     * @throws ValidationException If the request validation fails.
     */
    public function status(Request $request, string $statusName, int $status): JsonResponse
    {
        $this->validate($request, [
            'ids' => 'required|array',
            'ids.*' => 'required|int|exists:products,id',
        ]);

        if ($statusName && $statusName == 'hidden' && $status && !Plan::enabled('hidden_products')) {
            return response()->json(['feature' => \Illuminate\Support\Arr::first(array_filter($this->getFeatures(), fn($feature): bool => $feature['mapping'] == 'hidden_products'))], 402);
        }

        $result = Product::find($request->input('ids'))->map(function (Product $product) use ($statusName, $status): array {
            $helper = ProductFormatter::make($product);
            return $helper->changeStatus($statusName, $status);
        });

        ProductTemp::updateTempTableAndPopulateByProduct($request->input('ids'));

        return response()->json($result);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws \Throwable
     * @see ProductsController::bulkCopy()
     */
    public function duplicate(Request $request): JsonResponse
    {
        $this->validate($request, [
            'ids' => 'required|array',
            'ids.*' => 'required|int|exists:products,id',
        ]);

        $products = Product::whereIn('id', $request->input('ids'))
            ->with(['variant', 'variants', 'categories', 'tags', 'tabs', 'files', 'clear_bundles'])
            ->get();

        /**@var Product $product */
        $newProducts = [];
        foreach ($products as $product) {
            \Illuminate\Support\Facades\DB::transaction(function () use ($product, &$newProducts): void {
                $product_copy = $product->makeProductCopy();
                $newProducts[] = ProductFormatter::make($product_copy)->toList();
                ProductTemp::updateTempTableAndPopulateByProduct($product_copy->id);
            }, 5);
        }

        return response()->json($newProducts);
    }

    /**
     * @param Request $request
     * @param $status
     * @return JsonResponse
     * @throws ValidationException
     * @throws \Throwable
     * @see ProductsController::bulkActivateTracking()
     * @see ProductsController::bulkStopTracking()
     * @see ProductsController::bulkSetTracking()
     */
    public function tracking(Request $request, $status): JsonResponse
    {
        $this->validateIds($request);

        $tracking = YesNo::reverse($status);
        $quantity = $request->input('quantity');

        $products = Product::with('variants')
            ->whereIn('id', $request->input('ids'))
            ->get();

        foreach ($products as $product) {
            foreach ($product->variants as $variant) {
                $variant->updateColumns(['quantity' => $quantity], false);
            }

            $product->columnUpdate('tracking', $tracking);
        }

        ProductTemp::updateTempTableAndPopulateByProduct($request->input('ids'));

        Product::clearCache();

        return response()->json(['success' => true]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws \Throwable
     * @see ProductsController::bulkCategorySet()
     */
    public function setCategory(Request $request): JsonResponse
    {
        $this->validateIds($request, [
            'category_id' => 'required|int|exists:type__products_categories,id',
        ]);

        $products = Product::whereIn('id', $request->input('ids'))->get();

        foreach ($products as $product) {
            $product->columnUpdate('category_id', $request->input('category_id'));
        }

        ProductTemp::updateTempTableAndPopulateByProduct($request->input('ids'));

        Product::clearCache();

        return response()->json(['success' => true]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws \Throwable
     * @see ProductsController::bulkVendorSet()
     */
    public function setVendor(Request $request): JsonResponse
    {
        $this->validateIds($request, [
            'vendor_id' => 'required|int|exists:type__products_vendors,id',
        ]);

        $products = Product::whereIn('id', $request->input('ids'))->get();

        foreach ($products as $product) {
            $product->columnUpdate('vendor_id', $request->input('vendor_id'));
        }

        ProductTemp::updateTempTableAndPopulateByProduct($request->input('ids'));

        Product::clearCache();

        return response()->json(['success' => true]);
    }

    /**
     * @param Request $request
     * @param $status
     * @return JsonResponse
     * @throws ValidationException
     * @throws \Throwable
     * @see ProductsController::bulkInStockSet()
     * @see ProductsController::bulkOutStockSet()
     */
    public function setStockStatus(Request $request, $status): JsonResponse
    {
        $this->validateIds($request, [
            'status_id' => 'required|int|exists:product_statuses,id',
        ]);

        $column = $status == 'in-stock' ? 'status_id' : 'out_of_stock_id';
        $query = Product::whereIn('id', $request->input('ids'));
        if ($column == 'out_of_stock_id') {
            $query->where('tracking', 'yes');
        }

        $products = $query->get();

        foreach ($products as $product) {
            $product->columnUpdate($column, $request->input('status_id'));
        }

        ProductTemp::updateTempTableAndPopulateByProduct($request->input('ids'));

        Product::clearCache();

        return response()->json(['success' => true]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws \Throwable
     * @see ProductsController::bulkSortOrderSet()
     */
    public function setSortOrder(Request $request): JsonResponse
    {
        $this->validateIds($request, [
            'sort_order' => 'required|int|min:0',
        ]);

        $products = Product::whereIn('id', $request->input('ids'))->get();

        foreach ($products as $product) {
            $product->columnUpdate('sort_order', $request->input('sort_order'));
        }

        ProductTemp::updateTempTableAndPopulateByProduct($request->input('ids'));

        Product::clearCache();

        return response()->json(['success' => true]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws \Throwable
     * @see ProductsController::bulkTagsSet()
     */
    public function setTags(Request $request): JsonResponse
    {
        $this->validateIds($request, [
            'tags' => 'required|array',
            'tags.*' => 'required|string|max:191',
        ]);

        $products = Product::whereIn('id', $request->input('ids'))
            ->with(['tags'])
            ->get();

        $tags = implode(',', $request->input('tags'));
        foreach ($products as $product) {
            /** @var Product $product */
            $product->attachTags($tags, false, true);
        }

        ProductTemp::updateTempTableAndPopulateByProduct($request->input('ids'));

        Product::clearCache();

        return response()->json(['success' => true]);
    }

    /**
     * Delete multiple products.
     *
     * This method deletes the products with the provided IDs from the request.
     *
     * @param Request $request The request instance.
     * @return JsonResponse The JSON response indicating the success of the operation.
     * @throws ValidationException If the request validation fails.
     */
    public function delete(Request $request): JsonResponse
    {
        $this->validateIds($request);

        Product::deleteProductById($request->input('ids'));

//        ProductTemp::updateTempTableAndPopulateByProduct($request->input('ids'));

        return response()->json(['success' => true]);
    }

    /**
     * Search for products by name.
     *
     * This method searches for products whose names match the provided query
     * string. It returns the search results in a simplified format.
     *
     * @return JsonResponse The JSON response containing the search results.
     * @throws Error If there is an error during the search process.
     */
    public function search(string $type = 'products'): JsonResponse
    {
        if ($type == 'items') {
            $model = Product::withoutGlobalScopes()->orderBy('id', 'DESC');
        } else {
            $model = Product::orderBy('id', 'DESC');
        }

        $results = $this->getRecords($model, ProductAutocompleteFilter::class);

        $results->transform(fn(Product $product) => (object)[
            'id' => $product->id,
            'name' => $product->name,
            'img' => $product->getImage('150x150'),
        ]);

        return response()->json($results);
    }

    /**
     * Get meta information about enabled features and product reviews.
     *
     * This method retrieves meta information such as whether product reviews are enabled
     * and the features that are enabled in the current plan.
     *
     * @return JsonResponse The JSON response containing the meta information.
     */
    public function meta(): JsonResponse
    {
        $allowed_ext = [];
        $mime_types = [];
        foreach (config('upload.dir_types') as $dir) {
            $allowed_ext = array_merge($allowed_ext, config('upload.types_' . $dir . '.extensions', []));
            $mime_types = array_merge($mime_types, config('upload.types_' . $dir . '.mime-types', []));
        }

        $results = [
            'product_review' => ProductFormatter::isAllowProductReview(),
            'suppliers' => ProductFormatter::isAllowSuppliers(),
            'brand_model' => ProductFormatter::isAllowBrandModel(),
            'imos3d' => ProductFormatter::isAllowImos3D(),
            'grocery_store' => ProductFormatter::isAllowUnits(),
            'membership' => ProductFormatter::isAllowMembership(),
            'units' => (ProductFormatter::isAllowUnits() ? ProductFormatter::getUnits()->values() : collect())->map(fn(Units $unit): array => [
                'id' => $unit->id,
                'path' => $unit->name,
                'name' => $unit->original_name,
                'short_name' => $unit->short_name,
                'multiple_name' => $unit->multiple_name,
                'steps' => $unit->steps,
                'decimals' => $unit->decimals,
                'parent_id' => $unit->parent_id,
            ]),
            'uploads' => [
                'image' => [
                    'mimes' => config('image.allowed_mimetypes'),
                    'extensions' => config('image.allowed_extensions'),
                    'max_file_size' => Image::MAX_SIZE,
                    'max_width' => Image::MAX_WIDTH,
                    'max_height' => Image::MAX_HEIGHT,
                ],
                'digital_files' => [
                    'mimes' => ProductsFormatter::$digital_files_types['mime-types'],
                    'extensions' => ProductsFormatter::$digital_files_types['extensions'],
                    'max_file_size' => return_bytes(config('upload.files_max_size') . 'MB'),
                ],
                'files' => [
                    'mimes' => $mime_types,
                    'extensions' => $allowed_ext,
                    'max_file_size' => return_bytes(config('upload.files_max_size') . 'MB'),
                    'groups' => ProductFilesGroups::select(['id', 'name', 'position'])->orderBy('name')->get()->map(fn(ProductFilesGroups $group): array => [
                        'id' => $group->id,
                        'name' => $group->name,
                        'position' => $group->position,
                    ])
                ],
            ],
            'features' => $this->getFeatures(),
            'statuses' => Status::select(['id', 'name', 'type'])->orderBy('name')->get()->map(fn(Status $status): array => [
                'id' => $status->id,
                'name' => $status->name,
                'type' => $status->type,
            ])->all(),
        ];

        return response()->json($results);
    }

    /**
     * Get enabled features.
     *
     * This method retrieves the features that are enabled in the current plan.
     * It returns an array of feature details.
     *
     * @return array The array of enabled feature details.
     */
    protected function getFeatures(): array
    {
        $features = [];
        $featuresArray = ['products', 'hidden_products', 'digital_products', 'multi_variants', 'variants.listing'];
        $result = Product::selectRaw('
             COUNT(*) as total,
             SUM(CASE WHEN digital = "yes" THEN 1 ELSE 0 END) as digital_count,
             SUM(CASE WHEN is_hidden = 1 THEN 1 ELSE 0 END) as hidden_count
        ')->first();
        $planFeatures = PlanFeature::whereIn('mapping', $featuresArray)->get()->keyBy('mapping');

        foreach ($featuresArray as $feature) {
            if ($planFeature = $planFeatures->get($feature)) {
                switch ($feature) {
                    case 'hidden_products':
                        $current = Plan::featureValue($planFeature->mapping);
                        $used = $result->hidden_count;
                        break;
                    case 'digital_products':
                        $current = Plan::featureValue($planFeature->mapping);
                        $used = $result->digital_count;
                        break;
                    case 'multi_variants':
                        $current = Plan::enabled($feature);
                        $used = null;
                        break;
                    case 'variants.listing':
                        $current = Plan::enabled($feature);
                        $used = null;
                        break;
                    case 'products':
                        $current = Plan::featureValue($planFeature->mapping);
                        $used = $result->total;
                        break;
                    default:
                        $current = null;
                        $used = null;
                        break;
                }

                $features[] = [
                    'group' => 'products',
                    'id' => $planFeature->id,
                    'mapping' => $planFeature->mapping,
                    'name' => $planFeature->name_translated,
                    'current' => $current,
                    'used' => $used,
                    'suffix' => null,
                    'type' => [],
                    'cast' => $planFeature->cast,
                ];
            }
        }

        return $features;
    }

    /**
     * @param ProductRequest $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function store(ProductRequest $request)
    {
        try {
            $inputs = $request->passedValidation();

            $auto_save = $request->query('save');
            $draft = $request->query('draft');

            /** @var $product Product */
            $product = \Illuminate\Support\Facades\DB::transaction(function () use ($auto_save, $draft, $inputs): \App\Models\Product\Product {
                $product = Product::add($inputs, $auto_save || $draft, true, false);


                $product->attachTabs($inputs['tabs'] ?? []);

                $product->attachMeta($inputs['meta'] ?? [], ['width', 'depth', 'height', 'unit_price_input']);

                $product->attachCategories($inputs['other_categories'] ?? []);
                $product->attachLinkedProducts($inputs['linked'] ?? []);
                $product->attachToSmartCollections($inputs['smart_collections'] ?? []);

                ProductTemp::updateTempTableAndPopulateByProduct($product->id);

                return $product;
            });

            $product->updateDefaultVariantByDiscountedPrice();
            return $this->view($product->id);
        } catch (\Throwable $throwable) {
            return response()->json([
                'status' => 'error',
                'msg' => $throwable->getMessage()
            ], 500);
        }
    }

    /**
     * @param ProductRequest $request
     * @param $product_id
     * @return JsonResponse
     * @throws \Throwable
     */
    public function update(ProductRequest $request, $product_id)
    {
        $draft = $request->query('draft');

        /** @var Product $product */
        $product = Product::findOrFail($product_id);
        try {
            \Illuminate\Support\Facades\DB::transaction(function () use ($product, $draft, $request) {
                $input = $request->passedValidation();
                if ($product->category_id != $input['category_id']) {
                    $product->propertyOptions()->detach();
                }


                if (($markAsNew = $product->getNewInterval()) && $product->new == 'no' && ($input['new'] ?? 'no') == 'yes') {
                    $product->new_from = $markAsNew;
                    $product->new = 'yes';
                }

                if (($markAsNew = $product->getFeaturedInterval()) && $product->featured == 0 && ($input['featured'] ?? 0) == 1) {
                    $product->featured_from = $markAsNew;
                    $product->featured = 1;
                }

                $product = $product->editModel($input, !!$draft, true, false);

                $updated = false;
                if (!$draft) {
                    // $product->active = YesNo::True;
                    $product->changeLogRegisterExtend(['attributes' => $product->getDirtyOriginal()]);
                    if ($product->isDirty()) {
                        $product->save();
                        $updated = true;
                    }
                }


                $product->attachTabs($request->input('tabs', []));

                $product->attachMeta($request->input('meta', []), ['width', 'depth', 'height', 'unit_price_input']);

                $product->attachCategories($request->input('other_categories', []));

                $product->attachLinkedProducts($request->input('linked', []));

                $product->attachToSmartCollections($request->input('smart_collections', []));

                if ($updated) {
                    event(new ProductUpdated($product));
                }

                ProductTemp::updateTempTableAndPopulateByProduct($product->id);

                return $product;
            });

            $log = $product->differenceLogDispatch(fn(): array => [
                'initiator' => [
                    'initiator' => 'sitecp',
                    'id' => \Auth::adminId(),
                    'name' => \Auth::admin()->log_name ?? null,
                    'type' => \Auth::admin()->type ?? null,
                    'ccConsole' => session('cc_console_login.auth_id'),
                    'action' => 'edit',
                ]
            ]);

            $product->syncBundlesPrices($log);
        } catch (\Exception $exception) {
            if ($exception instanceof Error) {
                return response()->json([
                    'status' => 'error',
                    'msg' => $exception->getMessage()
                ], 400);
            }

            return response()->json([
                'status' => 'error',
                'msg' => $exception->getMessage()
            ], 500);
        }

        $product->updateDefaultVariantByDiscountedPrice();

        return $this->view($product->id);
    }

    /**
     * @param Request $request
     * @param array $additionalRules
     * @return void
     * @throws ValidationException
     */
    protected function validateIds(Request $request, array $additionalRules = [])
    {
        $this->validate($request, array_merge([
            'ids' => 'required|array',
            'ids.*' => 'required|int|exists:products,id',
        ], $additionalRules));
    }

    /**
     * Update product properties.
     * @param AddPropertyToProductRequest $request
     * @param $product_id
     * @return JsonResponse
     */
    public function addPropertyToProduct(AddPropertyToProductRequest $request, $product_id)
    {
        $product = Product::findOrFail($product_id);
        $category = Category::find($product->category_id);

        $category_properties = $category->properties->keyBy('id')->map(fn($property): array => $property->data = [])->toArray();

        $properties = $request->all();

        $request_properties = [];
        foreach ($properties as $prop) {
            $request_properties[$prop['property_id']] = $prop['option_ids'];
        }

        $properties = array_replace_recursive($category_properties, $request_properties);
        $success = true;

        foreach ($properties as $property_id => $options) {
            if (is_array($options)) {
                $success = PropertyValue::updateMultiValue($product_id, $property_id, $options);
            }
        }

        return response()->json(['success' => $success]);
    }

    /**
     * Create a draft product.
     * @param ProductRequest $request
     * @return JsonResponse
     */
    public function storeDraftProduct(ProductRequest $request): \Illuminate\Http\JsonResponse
    {
        $product = Product::create([
            'name' => $request->input('name'),
            'category_id' => $request->input('category_id'),
            'type' => $request->input('type'),
            'type_digital' => $request->input('type_digital'),
            'active' => 'no',
            'tracking' => 'no',
            'shipping' => 'no',
        ]);

        $variant = $product->variant()->create([
            'item_id' => $product->id,
            'price' => 0
        ]);
        $product->default_variant_id = $variant->id;
        $product->save();

        return $this->view($product->id);
    }

    /**
     * @return JsonResponse
     */
    public function getImportKeys()
    {
        return response()->json(Product::getImportsData()->map(fn($import): array => [
            'id' => $import['key'],
            'name' => $import['title']
        ]));

    }


}
