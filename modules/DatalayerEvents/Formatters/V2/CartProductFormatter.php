<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 14.3.2019 г.
 * Time: 15:23 ч.
 */

namespace Modules\DatalayerEvents\Formatters\V2;

use App\Models\Store\CartItem;
use App\Models\Store\CartItemOption;

class CartProductFormatter
{

    /**
     * @param CartItem $product
     * @return array|null
     */
    public static function format($product = null)
    {
        if(!($product instanceof CartItem)) {
            return null;
        }

        return [
            'product_id' => $product->product_id,
            "name" => $product->name,
            "price" => $product->price_input ?? null,
            "discount_price" => $product->discount_price ? $product->discount_price_input : null,
            "currency" => $product->currency,
            "category" => $product->category_name,
            "google_taxonomy_id" => $product->product->category->google_taxonomy->id ?? null,
            "google_taxonomy_name" => $product->product->category->google_taxonomy->name ?? null,
            "brand" => $product->vendor_name,
            "digital" => $product->is_digital ? 1 : 0,
            "url" => $product->url(),
            "image_url" => $product->hasImage() ? $product->getImage('600x600') : null,
            "type" => $product->product->type ?? null,
            'p1' => $product->p1,
            'p2' => $product->p2,
            'p3' => $product->p3,
            'quantity' => $product->quantity,
            "variant" => [
                'key' => $product->variant->compare_key ?? null,
                'v1' => $product->v1,
                'v2' => $product->v2,
                'v3' => $product->v3,
                'sku' => $product->sku,
                'barcode' => $product->barcode,
                'price' => $product->discount_total ? $product->discount_total_input : $product->price_input,
            ],
            "options" => $product->options->map(function(CartItemOption $option) {
                return [
                    'name' => $option->name,
                    'values' => [
                        [
                            'value' => $option->type == 'file' ? $option->file_url : ($option->option ? : $option->value_formatted),
                            'price' => $option->field_value->amount_input ?? null
                        ]
                    ]
                ];
            })->all(),
        ];
    }
}
