<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 14.3.2019 г.
 * Time: 15:23 ч.
 */

namespace Modules\DatalayerEvents\Formatters\V2;

use App\Models\Store\CartItem;
use App\Models\Store\Cart as CartModel;
use App\Models\Store\Cart;

class CartFormatter
{

    /**
     * @param $cart
     * @return array|null
     */
    public static function format($cart)
    {
        if (!$cart || !($cart instanceof Cart)) {
            return null;
        }

        return [
            'cart_id' => $cart->id,
            'key' => $cart->key ?: CartModel::getNewCartKey(),
            'user' => $cart->customer_get ? CustomerFormatter::format($cart->customer_get) : null,
            'products' => $cart->products->map(function (CartItem $item) {
                return CartProductFormatter::format($item);
            })->values()->all()
        ];
    }

}
