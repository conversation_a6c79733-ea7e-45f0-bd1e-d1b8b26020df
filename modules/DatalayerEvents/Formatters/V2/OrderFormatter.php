<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 14.3.2019 г.
 * Time: 16:30 ч.
 */

namespace Modules\DatalayerEvents\Formatters\V2;

use App\Helper\Store\CartTotal;
use App\Helper\YesNo;
use App\Models\Order\Order;
use App\Models\Order\OrderProduct;

class OrderFormatter
{

    /**
     * @param Order $order
     * @param null $cart_id
     * @return array|null
     */
    public static function format(Order $order, $cart_id = null)
    {
        $return = [
            'cart_id' => $cart_id,
            'order_id' => $order->id,
            'subtotal' => $order->price_subtotal_input,
            'total' => $order->price_total_input,
            'quantity' => $order->quantity,
            'weight' => $order->weight_input,
            'vat_included' => (int)($order->vat_included == YesNo::True),
            'created_at' => $order->date_added->toIso8601String(),
            'updated_at' => $order->updated_at->toIso8601String(),
            'status' => $order->status,
            'status_fulfillment' => $order->status_fulfillment,
            'invoice_number' => $order->invoice_number,
            'invoice_date' => $order->invoice_date ? $order->invoice_date->toIso8601String() : null,
            'user' => CustomerFormatter::format($order->customer, $order),
            'products' => $order->products->map(function(OrderProduct $orderProduct) {
                return OrderProductFormatter::format($orderProduct);
            })->all(),
            'totals' => $order->manager->getTotalsSimple()->map(function(CartTotal $total) {
                return [
                    "key" => $total->getKey(),
                    "name" => $total->getName(),
                    "description" => $total->getDescription(),
                    "value" => $total->getValue(),
                    "price" => $total->getPriceInput(),
                ];
            })->values()->all(),
            'shipping' => $order->shipping ? [
                'name' => $order->shipping->provider_name,
                'price' => $order->shipping->order_amount_input,
                'integration' => $order->shipping->provider->integration ?? null,
                'service' => [
                    'id' => $order->shipping->service_id ?? null,
                    'name' => $order->shipping->service_name ?? null,
                ],
            ] : null,
            'payment' => $order->payment ? [
                'name' => $order->payment->provider_name,
                'price' => $order->payment->amount_input,
                'hash' => $order->payment->hash,
                'status' => $order->payment->status
            ] : null
        ];

        //mailchimp
        if(!empty($order->json_data['mailchimp']['campaign_id'])) {
            $return['mailchimp']['campaign_id'] = $order->json_data['mailchimp']['campaign_id'];
        }
        if(!empty($order->json_data['mailchimp']['tracking_code'])) {
            $return['mailchimp']['tracking_code'] = $order->json_data['mailchimp']['tracking_code'];
        }

        $utm = $order->meta_pluck->only(['utm_source', 'utm_medium', 'utm_campaign']);
        if($utm->count()) {
            $return['utm'] = $utm->all();
        }

        return $return;
    }
}
