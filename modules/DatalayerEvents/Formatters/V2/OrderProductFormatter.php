<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 14.3.2019 г.
 * Time: 15:23 ч.
 */

namespace Modules\DatalayerEvents\Formatters\V2;

use App\Helper\YesNo;
use App\Models\Order\OrderProduct;
use App\Models\Order\OrderProductOptions;
use App\Models\Product\Product;

class OrderProductFormatter
{

    /**
     * @param Product $product
     * @return array|null
     * @throws \App\Exceptions\Error
     */
    public static function format($product = null)
    {
        if(!($product instanceof OrderProduct)) {
            return null;
        }

        return [
            'order_product_id' => $product->id,
            'product_id' => $product->product_id,
            "name" => $product->name,
            'price' => $product->price_input,
            'quantity' => $product->quantity,
            'discount_price' => ($discounted_price = ($product->order_price_input != $product->price_input ? $product->order_price_input : null)),
            "currency" => site('currency'),
            "category" => $product->category_name ?? null,
            "google_taxonomy_id" => $product->category->google_taxonomy->id ?? null,
            "google_taxonomy_name" => $product->category->google_taxonomy->name ?? null,
            "brand" => $product->vendor_name ?? null,
            "digital" => $product->digital == YesNo::True ? 1 : 0,
            "url" => $product->url(),
            "image_url" => $product->hasImage() ? $product->getImage('600x600') : null,
            "type" => $product->type,
            'p1' => $product->p1,
            'p2' => $product->p2,
            'p3' => $product->p3,
            'variant' => [
                'key' => $product->variant->compare_key ?? null,
                'v1' => $product->v1,
                'v2' => $product->v2,
                'v3' => $product->v3,
                'sku' => $product->sku,
                'barcode' => $product->barcode,
                'price' => $discounted_price ? $discounted_price : $product->price_input,
            ],
            'options' => $product->options->map(function(OrderProductOptions $option) {
                return [
                    'name' => $option->name,
                    'values' => [
                        [
                            'value' => $option->type == 'file' ? $option->file_url : ($option->option ? : $option->value_formatted),
                            'price' => $option->amount_input
                        ]
                    ]
                ];
            })->all()
        ];
    }

}
