<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 14.3.2019 г.
 * Time: 15:23 ч.
 */

namespace Modules\DatalayerEvents\Formatters\V2;

use App\Exceptions\Error;
use App\Models\Product\Category;

class CategoryFormatter
{

    /**
     * @param $category
     * @return array|null
     * @throws Error
     */
    public static function format($category)
    {
        if (!$category || !($category instanceof Category)) {
            return null;
        }

        return [
            'category_id' => $category->id,
            "name" => $category->name,
            "path" => $category->path->pluck('name')->all(),
            "url" => $category->url(),
            "image_url" => $category->hasImage() ? $category->getImage('600x600') : null,
            "google_taxonomy_id" => $category->google_taxonomy->id ?? null,
            "google_taxonomy_name" => $category->google_taxonomy->name ?? null,
        ];

    }

}
