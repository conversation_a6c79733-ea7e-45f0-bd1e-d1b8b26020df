<?php

declare(strict_types=1);

namespace Liquid;

use ErrorException;
use Illuminate\View\Compilers\CompilerInterface;
use Illuminate\View\Engines\PhpEngine;
use Liquid\Exceptions\SyntaxError;
use Throwable;

class JsonCompilerEngine extends PhpEngine
{
    /**
     * A stack of the last compiled templates.
     *
     * @var array
     */
    protected $lastCompiled = [];

    /**
     * Create a new Blade view engine instance.
     *
     * @param \Illuminate\View\Compilers\CompilerInterface $compiler
     * @param array $config
     */
    public function __construct(/**
     * The Blade compiler instance.
     */
    protected \Illuminate\View\Compilers\CompilerInterface $compiler, array $config = [])
    {
        foreach ($config as $key => $value) {
            if (method_exists($this->compiler, $method = \Illuminate\Support\Str::camel('set_' . $key))) {
                $this->compiler->$method($value);
            }
        }
    }

    /**
     * Get the evaluated contents of the view.
     *
     * @param TemplateContent $path
     * @param array $data
     * @return string|null
     * @throws ErrorException
     */
    #[\Override]
    public function get($path, array $data = [])
    {
        $this->lastCompiled[] = $path->getName();

        $obLevel = ob_get_level();
        try {
            $this->compiler->setFileMtime($path);

            $this->compiler->compile($path);

            $results = $this->compiler->render($path, $data);

            array_pop($this->lastCompiled);

            return $results;
        } catch (Throwable $e) {
            $this->handleViewException($e, $obLevel);
        }
    }

    /**
     * Handle a view exception.
     *
     * @param Throwable|\Exception $e
     * @param int $obLevel
     * @return void
     *
     * @throws $e
     */
    #[\Override]
    protected function handleViewException(Throwable|\Exception $e, $obLevel)
    {
        $e = new ErrorException($this->getMessage($e), 0, 1, $e->getFile(), $e->getLine(), $e);

        while (ob_get_level() > $obLevel) {
            ob_end_clean();
        }

        throw $e;
    }

    /**
     * Get the exception message for an exception.
     *
     * @param Throwable|\Exception $e
     * @return string
     */
    protected function getMessage(Throwable|\Exception $e): string
    {
        if($e instanceof SyntaxError) {
            return $e->getMessage() . ' View: ' . $e->getFile() . ' on line ' . $e->getLine() . '. Called from ' . last($this->lastCompiled);
        }
        return $e->getMessage() . ' (View: ' . last($this->lastCompiled) . ')';
    }
}
