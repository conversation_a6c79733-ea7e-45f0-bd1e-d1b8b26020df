<?php

declare(strict_types=1);

namespace Liquid;

use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;
use Illuminate\View\Engines\EngineResolver;
use Illuminate\View\Factory;
use Liquid\Factory as LiquidFactory;
use Liquid\Storage\CompiledStoreManager;
use Liquid\Storage\Stores\FileCompiledStore;
use Liquid\Storage\Stores\DatabaseCompiledStore;

class LiquidServiceProvider extends ServiceProvider
{
    #[\Override]
    public function register(): void
    {
        $this->setupConfig();
        $this->registerCompiledStoreManager();
        $this->registerLiquidViewFinderManager();
        $this->registerLiquidViewFinder();
        $this->registerLiquidCompiler();
        $this->registerLiquidJsonCompiler();
        $this->registerLiquidFactory();
        $this->registerEngineResolver();
    }

    /**
     * Register the CompiledStoreManager and its drivers
     */
    protected function registerCompiledStoreManager(): void
    {
        // Register the CompiledStoreManager as singleton
        $this->app->singleton('liquid.compiled.store.manager', function ($app): CompiledStoreManager {
            return new CompiledStoreManager($app);
        });

        // Alias for easier access
        $this->app->alias('liquid.compiled.store.manager', CompiledStoreManager::class);

        // Register default drivers
        $this->registerCompiledStoreDrivers();
    }

    /**
     * Register the compiled store drivers
     */
    protected function registerCompiledStoreDrivers(): void
    {
        $manager = $this->app->make('liquid.compiled.store.manager');

        // Register file driver
        $manager->extend('file', function ($app, $config): FileCompiledStore {
            return new FileCompiledStore(
                $app['files'],
                $config['path'] ?? storage_path('framework/views/compiled/liquid'),
                $config['debug'] ?? false
            );
        });

        // Register database driver
        $manager->extend('database', function ($app, $config): DatabaseCompiledStore {
            $connection = $app['db']->connection($config['connection'] ?? null);

            return new DatabaseCompiledStore(
                $connection,
                $config['table'] ?? 'liquid_compiled_templates',
                $config['cache_size'] ?? 100,
                $config['debug'] ?? false
            );
        });

        // Future Redis driver can be added here
        // $manager->extend('redis', function ($app, $config): RedisCompiledStore {
        //     return new RedisCompiledStore(
        //         $app['redis']->connection($config['connection'] ?? 'default'),
        //         $config['prefix'] ?? 'liquid_compiled',
        //         $config['ttl'] ?? 86400,
        //         $config['debug'] ?? false
        //     );
        // });
    }

    /**
     * Register the Liquid view finder.
     *
     * @return void
     */
    public function registerLiquidViewFinderManager()
    {
        // The Compiler engine requires an instance of the CompilerInterface, which in
        // this case will be the Blade compiler, so we'll first create the compiler
        // instance to pass into the engine so it can compile the views properly.
        $this->app->singleton('liquid.view.finder.manager', function (Application $app) {
            return new ViewFinderManager($app);
        });
    }

    /**
     * Register the Liquid view finder.
     *
     * @return void
     */
    public function registerLiquidViewFinder()
    {
        // The Compiler engine requires an instance of the CompilerInterface, which in
        // this case will be the Blade compiler, so we'll first create the compiler
        // instance to pass into the engine so it can compile the views properly.
        $this->app->singleton('liquid.view.finder', function (Application $app) {
            $driver = $this->app->make('config')->get('liquid.finder.default', 'file');

            return $this->app->make('liquid.view.finder.manager')->driver($driver);
        });
    }

    protected function setupConfig(): void
    {
        $this->mergeConfigFrom($file = __DIR__ . '/../../config/liquid.php', 'liquid');

        $this->publishes([
            $file => config_path('liquid.php')
        ], 'config');
    }

    protected function registerLiquidCompiler(): void
    {
        $this->app->singleton('liquid.compiler', function ($app): LiquidCompiler {
            $config = $app['config']->get('liquid');

            // Get store from CompiledStoreManager
            $storeManager = $app->make("liquid.compiled.store.manager");
            $store = $storeManager->driver();

            $compiler = new LiquidCompiler(
                $app->make('files'),
                $app->make("files"), storage_path("framework/views/compiled/liquid")
            );

            $compiler->setCompiledStore($store);
            return $compiler
                ->setAutoEscape($config['auto_escape'] ?? true)
                ->setTags($config['tags'] ?? [])
                ->setFilters($config['filters'] ?? [])
                ->setCompiler(new CompilerEngine($compiler));
        });
    }

    protected function registerLiquidJsonCompiler(): void
    {
        $this->app->singleton('liquid.json_compiler', function (Application $app): JsonLiquidCompiler {
            // Get store from CompiledStoreManager
            $storeManager = $app->make("liquid.compiled.store.manager");
            $store = $storeManager->driver();

            $compiler = new JsonLiquidCompiler(
                $app->make('files'),
                $app->make("files"), storage_path("framework/views/compiled/liquid")
            );

            $compiler->setLiquidCompiler($this->app->make('liquid.compiler'));
            $compiler->setCompiledStore($store)
                ->setCompiler(new JsonCompilerEngine($compiler));

            return $compiler;
        });
    }

    /**
     * Register the Liquid engine implementation.
     *
     * @return void
     */
    public function registerLiquidFactory()
    {
        // The Compiler engine requires an instance of the CompilerInterface, which in
        // this case will be the Blade compiler, so we'll first create the compiler
        // instance to pass into the engine so it can compile the views properly.
        $this->app->singleton('liquid.factory', function () {
            return new LiquidFactory(
                $this->app->make('liquid.engine.resolver'),
                $this->app->make('liquid.view.finder'),
                $this->app->make('events')
            );
        });
    }

    /**
     * Register the engine resolver instance.
     *
     * @return void
     */
    public function registerEngineResolver()
    {
        $this->app->singleton('liquid.engine.resolver', function () {
            $resolver = new EngineResolver;

            // Next, we will register the various view engines with the resolver so that the
            // environment will resolve the engines needed for various views based on the
            // extension of view file. We call a method for each of the view's engines.
            foreach (['liquid', 'json'] as $engine) {
                $this->{'register'.ucfirst($engine).'Engine'}($resolver);
            }

            return $resolver;
        });
    }

    protected function registerLiquidEngine(EngineResolver $resolver)
    {
        $resolver->register('liquid', function () {
            return $this->app->make('liquid.compiler');
        });
    }

    protected function registerJsonEngine(EngineResolver $resolver)
    {
        $resolver->register('json', function () {
            return $this->app->make('liquid.json_compiler');
        });
    }
}
