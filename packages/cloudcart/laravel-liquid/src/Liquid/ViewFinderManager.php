<?php

namespace Liquid;

use Closure;
use Illuminate\Foundation\Application;
use Illuminate\View\ViewFinderInterface;
use InvalidArgumentException;
use Liquid\ViewFinders\DatabaseViewFinder;
use Liquid\ViewFinders\FileViewFinder;

class ViewFinderManager
{
    /**
     * The application instance.
     *
     * @var Application
     */
    protected Application $app;

    /**
     * The array of resolved cache stores.
     *
     * @var array
     */
    protected array $stores = [];

    /**
     * The registered custom driver creators.
     *
     * @var array
     */
    protected array $customCreators = [];

    /**
     * Create a new Cache manager instance.
     *
     * @param  Application  $app
     * @return void
     */
    public function __construct(Application $app)
    {
        $this->app = $app;
    }

    /**
     * Get a cache store instance by name, wrapped in a repository.
     *
     * @param  string|null  $name
     * @return ViewFinderInterface
     */
    public function store(?string $name = null): ViewFinderInterface
    {
        $name = $name ?: $this->getDefaultDriver();

        return $this->stores[$name] = $this->get($name);
    }

    /**
     * Get a cache driver instance.
     *
     * @param  string|null  $driver
     * @return ViewFinderInterface
     */
    public function driver(?string $driver = null): ViewFinderInterface
    {
        return $this->store($driver);
    }

    /**
     * Attempt to get the store from the local cache.
     *
     * @param  string  $name
     * @return ViewFinderInterface
     */
    protected function get(string $name): ViewFinderInterface
    {
        return $this->stores[$name] ?? $this->resolve($name);
    }

    /**
     * Resolve the given store.
     *
     * @param  string  $name
     * @return ViewFinderInterface
     *
     * @throws InvalidArgumentException
     */
    protected function resolve(string $name): ViewFinderInterface
    {
        $config = $this->getConfig($name);

        if (is_null($config)) {
            throw new InvalidArgumentException("Cache store [{$name}] is not defined.");
        }

        if (isset($this->customCreators[$config['driver']])) {
            return $this->callCustomCreator($config);
        } else {
            $driverMethod = 'create'.ucfirst($config['driver']).'Driver';

            if (method_exists($this, $driverMethod)) {
                return $this->{$driverMethod}($config);
            } else {
                throw new InvalidArgumentException("Driver [{$config['driver']}] is not supported.");
            }
        }
    }

    /**
     * Call a custom driver creator.
     *
     * @param  array  $config
     * @return ViewFinderInterface
     */
    protected function callCustomCreator(array $config): ViewFinderInterface
    {
        return $this->customCreators[$config['driver']]($this->app, $config);
    }

    /**
     * Create an instance of the file cache driver.
     *
     * @param array $config
     * @return FileViewFinder|ViewFinderInterface
     */
    protected function createFileDriver(array $config): FileViewFinder|ViewFinderInterface
    {
        return new FileViewFinder($this->app['files'], (array)($config['paths'] ?? []));
    }

    /**
     * Create an instance of the database cache driver.
     *
     * @param array $config
     * @return DatabaseViewFinder|ViewFinderInterface
     */
    protected function createDatabaseDriver(array $config): DatabaseViewFinder|ViewFinderInterface
    {
        $connection = $this->app['db']->connection($config['connection'] ?? null);

        return new DatabaseViewFinder(
            $connection, $config['table']
        );
    }

    /**
     * Get the cache connection configuration.
     *
     * @param  string  $name
     * @return mixed
     */
    protected function getConfig(string $name): mixed
    {
        return $this->app['config']["liquid.finder.drivers.{$name}"];
    }

    /**
     * Get the default cache driver name.
     *
     * @return string
     */
    public function getDefaultDriver(): string
    {
        return $this->app['config']['liquid.finder.default'];
    }

    /**
     * Set the default cache driver name.
     *
     * @param  string  $name
     * @return void
     */
    public function setDefaultDriver(string $name): void
    {
        $this->app['config']['cache.default'] = $name;
    }

    /**
     * Unset the given driver instances.
     *
     * @param array|string|null $name
     * @return $this
     */
    public function forgetDriver(null|array|string $name = null): static
    {
        $name = $name ?? $this->getDefaultDriver();

        foreach ((array) $name as $cacheName) {
            if (isset($this->stores[$cacheName])) {
                unset($this->stores[$cacheName]);
            }
        }

        return $this;
    }

    /**
     * Register a custom driver creator Closure.
     *
     * @param  string  $driver
     * @param  Closure  $callback
     * @return $this
     */
    public function extend(string $driver, Closure $callback): static
    {
        $this->customCreators[$driver] = $callback->bindTo($this, $this);

        return $this;
    }

    /**
     * Dynamically call the default driver instance.
     *
     * @param  string  $method
     * @param  array  $parameters
     * @return mixed
     */
    public function __call(string $method, array $parameters)
    {
        return $this->store()->$method(...$parameters);
    }

}
