<?php

declare(strict_types=1);

namespace Liquid;

class HtmlString implements \Stringable
{
    /**
     * Create a new HTML string instance.
     *
     * @param string $html
     * @return void
     */
    public function __construct(
        /**
         * The HTML string.
         */
        protected $html
    )
    {
    }

    /**
     * Get the HTML string.
     *
     * @return string
     */
    public function __toString(): string
    {
        return $this->html;
    }
}
