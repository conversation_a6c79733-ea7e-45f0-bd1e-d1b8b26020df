<?php

declare(strict_types=1);

/**
 * JS: https://github.com/harttle/liquidjs
 *
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid;

use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Contracts\View\Engine as BaseEngine;
use Illuminate\View\Compilers\Compiler;
use Illuminate\View\Compilers\CompilerInterface;
use Illuminate\View\ViewFinderInterface;
use Liquid\Storage\CompiledStoreInterface;
use Liquid\Traits\TokenizeTrait;
use ReflectionException;

/**
 * The Template class.
 *
 * http://cheat.markdunkley.com/
 * https://stackoverflow.com/questions/29123188/enabling-liquid-templating-syntax-highlight-in-webstorm-phpstorm/29337624#29337624
 * https://github.com/Shopify/liquid
 *
 */
class LiquidCompiler extends Compiler implements CompilerInterface, BaseEngine
{

    use TokenizeTrait;

    /**
     * @var int The parser line number
     */
    public int $lineNumber = 1;

    /**
     * @var Document The root of the node tree
     */
    protected $root;

    /**
     * @var array Globally included filters
     */
    protected $filters = [];

    /**
     * @var array Custom tags
     */
    protected $tags = [];

    /**
     * @var bool $auto_escape
     */
    protected bool $auto_escape = true;

    /**
     * @var TemplateContent
     */
    protected null|TemplateContent $path = null;

    /**
     * @var array
     */
    protected $filemtime = [];

    /**
     * @var CompilerEngine
     */
    protected $compiler;

    /**
     * @var string|null Custom compiled path for Liquid views
     */
    protected ?string $compiledDir = null;

    /**
     * @var CompiledStoreInterface
     */
    protected $compiledStore = null;

    // Operations tags.
    const OPERATION_TAGS = ['{%', '%}'];

    // Variable tags.
    const VARIABLE_TAG = ['{{', '}}'];

    const ANY_STARTING_TAG = self::VARIABLE_TAG[0] . '|' . self::OPERATION_TAGS[0];

    const PARTIAL_TEMPLATE_PARSER = self::VARIABLE_TAG[0] . '.*?' . self::VARIABLE_TAG[1] . '|' . self::OPERATION_TAGS[0] . '.*?' . self::OPERATION_TAGS[1];

    const TEMPLATE_PARSER = self::PARTIAL_TEMPLATE_PARSER . '|' . self::ANY_STARTING_TAG;

    // Variable name.
    const VARIABLE_NAME = '[a-zA-Z_][a-zA-Z_0-9.-]*';

    const QUOTED_FRAGMENT = '"[^"]*"|\'[^\']*\'|(?:[^\s,\|\'"]|"[^"]*"|\'[^\']*\')+';

    const QUOTED_FRAGMENT_FILTER_ARGUMENT = '"[^":]*"|\'[^\':]*\'|(?:[^\s:,\|\'"]|"[^":]*"|\'[^\':]*\')+';

    const TAG_ATTRIBUTES = '/(\w+)\s*\:\s*(' . self::QUOTED_FRAGMENT . ')/';

    /**
     * @param string $dir
     * @return $this
     */
    public function setCompiledDir(string $dir): static
    {
        $this->compiledDir = $dir;
        return $this;
    }

    /**
     * Set the compiled store instance
     */
    public function setCompiledStore($store): static
    {
        $this->compiledStore = $store;
        return $this;
    }

    public function getCompiledPath($path): string
    {
        if(empty($this->compiledDir)) {
            throw new \Exception('Compiled dir is empty or not set!');
        }

        $dir = $this->compiledDir;

        if (!is_dir($dir)) {
            @mkdir($dir, 0777, true);
        }

        return $dir . '/' . md5($path) . '.php';
    }


    /**
     * Get the path currently being compiled.
     *
     * @return TemplateContent
     */
    public function getPath(): ?TemplateContent
    {
        return $this->path;
    }

    /**
     * Set the path currently being compiled.
     *
     * @param TemplateContent $path
     * @return void
     */
    public function setPath(TemplateContent $path): void
    {
        $this->path = $path;
    }

    /**
     * @return ViewFinderInterface
     */
    public function getViewFinder(): ViewFinderInterface
    {
        return app('liquid.view.finder');
    }

    /**
     * @return bool
     */
    public function getAutoEscape()
    {
        return $this->auto_escape;
    }

    /**
     * Set tags
     *
     * @param bool $value
     * @return LiquidCompiler
     */
    public function setAutoEscape($value): static
    {
        $this->auto_escape = $value;
        return $this;
    }

    /**
     * Set tags
     *
     * @param array $tags
     * @return LiquidCompiler
     */
    public function setTags(array $tags): static
    {
        foreach ($tags as $key => $value) {
            $this->registerTag($key, $value);
        }

        return $this;
    }

    /**
     * Register custom Tags
     *
     * @param string $name
     * @param string $class
     * @return LiquidCompiler
     */
    public function registerTag($name, $class): static
    {
        if ($class instanceof \Closure) {
            throw new \InvalidArgumentException('Type "Closure" is not allowed for tag!');
        }

        $this->tags[$name] = $class;
        return $this;
    }

    /**
     * @return array
     */
    public function getTags()
    {
        return $this->tags ?: [];
    }

    /**
     * Register the filter
     *
     * @param string $filter
     * @return LiquidCompiler
     */
    public function registerFilter($filter): static
    {
        if ($filter instanceof \Closure) {
            throw new \InvalidArgumentException('Type "Closure" is not allowed for filter!');
        }

        $this->filters[] = $filter;
        return $this;
    }

    /**
     * Set the filters
     *
     * @param array $filters
     * @return LiquidCompiler
     */
    public function setFilters(array $filters): static
    {
        array_map($this->registerFilter(...), $filters);
        return $this;
    }

    /**
     * Get the filters
     *
     * @return array
     */
    public function getFilters()
    {
        return $this->filters;
    }

    /**
     * @param TemplateContent $path
     * @return string
     */
    public function getFileSource(TemplateContent $path)
    {
        return $path->getContent();
    }

    /**
     * @param $template
     * @return TemplateContent
     */
    public function findTemplate($template)
    {
        /** @var TemplateContent $path */
        if ($path = $this->getViewFinder()->find($template)) {
            if (!array_key_exists($path->getPath(), $this->filemtime)) {
                $this->filemtime[$path->getPath()] = $path->getFileMtime();
            }
        }

        return $path;
    }

    /**
     * @param TemplateContent $path
     * @return void
     */
    public function setFileMtime(TemplateContent $path): void
    {
        if (!array_key_exists($path->getPath(), $this->filemtime)) {
            $this->filemtime[$path->getPath()] = $path->getFileMtime();
        }
    }

    /**
     * @return null|string
     */
    public function lastFileMTime(): ?string
    {
        return array_key_last($this->filemtime);
    }

    /**
     * @param $path
     * @return string
     */
    public function getTemplateSource($path)
    {
        return $this->getFileSource($this->findTemplate($path));
    }

    /**
     * Compile the view at the given path.
     *
     * @param TemplateContent $path
     * @return void
     */
    public function compile($path): void
    {
        $this->root = $this->compileTemplateContent($path);

        if ($this->isExpired($path->getName())) {
            $this->compiledStore->store($path->getName(), serialize($this->root));
        }
    }

    /**
     * Compile the view at the given path.
     *
     * @param TemplateContent $path
     * @return Document
     */
    public function compileTemplateContent(TemplateContent $path): Document
    {
        $this->setPath($path);
        $raw = $path->getContent();

        $templateTokens = $this->tokenize($raw);

        return new Document(null, $templateTokens, $this);
    }

    /**
     * Renders the current template
     *
     * @param TemplateContent $path
     * @param array $assigns an array of values for the template
     *
     * @return string
     * @throws LiquidException
     * @throws ReflectionException
     */
    public function render($path, array $assigns = [])
    {
        $context = new Context(
            $assigns,
            [
                'liquid_compiler' => $this,
            ]
        );

        if ($this->filters) {
            foreach ($this->filters as $filter) {
                $context->addFilter($filter);
            }
        }

        $this->root = unserialize($this->compiledStore->get($path->getName()));

        $result = $this->root->render($context);

        return $result;
    }

    /**
     * Renders the current template
     *
     * @param array $assigns an array of values for the template
     *
     * @return string
     * @throws LiquidException
     * @throws ReflectionException
     */
    public function renderInline(Document $root, array $assigns = [])
    {
        $context = new Context(
            $assigns,
            [
                'liquid_compiler' => $this,
            ]
        );

        if ($this->filters) {
            foreach ($this->filters as $filter) {
                $context->addFilter($filter);
            }
        }

        $result = $root->render($context);

        return $result;
    }

    /**
     * Renders the current template
     *
     * @param array $assigns an array of values for the template
     *
     * @return string
     * @throws LiquidException
     * @throws ReflectionException
     */
    public function renderContentForLayout(array $assigns = [])
    {
        $context = new Context(
            $assigns,
            [
                'liquid_compiler' => $this,
            ]
        );

        if ($this->filters) {
            foreach ($this->filters as $filter) {
                $context->addFilter($filter);
            }
        }

        $templateTokens = $this->tokenize("{%- layout 'theme' -%}");
        $root = new Document(null, $templateTokens, $this);

        $result = $root->render($context);

        return $result;
    }

    /**
     * Determine if the view at the given path is expired.
     *
     * @param string $path
     * @return bool
     */
    #[\Override]
    public function isExpired($path)
    {
        if(config('app.debug')) {
            return true;
        }

        // If the compiled file doesn't exist we will indicate that the view is expired
        // so that it can be re-compiled. Else, we will verify the last modification
        // of the views is less than the modification times of the compiled views.
        if (!$this->compiledStore->exists($path)) {
            return true;
        }

        $pathLastModify = count($this->filemtime) > 0 ? max($this->filemtime) : $this->compiledStore->lastModified($path);

        return $pathLastModify >= $this->compiledStore->lastModified($path);
    }


    /**
     * @param mixed $text
     * @return mixed
     */
    public function getTextLine($text): int
    {
        $pattern = '/' . preg_quote((string)$text, '/') . '/i';
        $lineNumber = 0;
        $content = $this->getPath()?->getContent();
        if ($content && preg_match($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
            //PREG_OFFSET_CAPTURE will add offset of the found string to the array of matches
            //now get a substring of the offset length and explode it by \n
            $lineNumber = count(explode(PHP_EOL, substr($content, 0, intval($matches[0][1]))));
        }

        return $lineNumber;
    }

    public function getCompiler(): CompilerEngine
    {
        return $this->compiler;
    }

    public function setCompiler(CompilerEngine $compiler): static
    {
        $this->compiler = $compiler;
        return $this;
    }

    public function get($path, array $data = [])
    {
        return $this->getCompiler()->get($path, $data);
    }
}
