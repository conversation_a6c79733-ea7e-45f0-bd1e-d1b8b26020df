<?php

namespace Liquid;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\View\View as BaseView;

class View extends BaseView
{
    /**
     * Create a new view instance.
     *
     * @param  Factory  $factory
     * @param  LiquidCompiler|JsonLiquidCompiler  $engine
     * @param  string  $view
     * @param  TemplateContent  $path
     * @param  mixed  $data
     * @return void
     */
    public function __construct(Factory $factory, LiquidCompiler|JsonLiquidCompiler $engine, string $view, TemplateContent $path, array $data = [])
    {
        $this->view = $view;
        $this->path = $path;
        $this->engine = $engine;
        $this->factory = $factory;

        $this->data = $data instanceof Arrayable ? $data->toArray() : $data;
    }
}
