<?php

namespace Liquid\Filters;

use Liquid\Exceptions\BaseFilterError;
use Liquid\Exceptions\FilterError;
use Iterator;
use Traversable;

class ArrFilters extends AbstractFilters
{

    /**
     * Joins elements of an array with a given character between them
     *
     * @param array|Traversable $input
     * @param string $glue
     *
     * @return string
     */
    public function join($input, string $glue = ' ')
    {
        if ($input instanceof Traversable) {
            $str = '';
            foreach ($input as $elem) {
                if ($str) {
                    $str .= $glue;
                }

                $str .= $elem;
            }

            return $str;
        }

        return is_array($input) ? implode($glue, $input) : $input;
    }

    /**
     * Returns the first element of an array
     *
     * @param array|\Iterator $input
     *
     * @return mixed
     */
    public function first($input)
    {
        if ($input instanceof Iterator) {
            $input->rewind();
            return $input->current();
        }

        return is_array($input) ? reset($input) : $input;
    }

    /**
     * Returns the last element of an array
     *
     * @param array|Traversable $input
     *
     * @return mixed
     */
    public function last($input)
    {
        if ($input instanceof Traversable) {
            $last = null;
            foreach ($input as $elem) {
                $last = $elem;
            }

            return $last;
        }

        return is_array($input) ? end($input) : $input;
    }

    /**
     * Concat array
     *
     * @param array $input
     *
     * @return array
     */
    public function concat(...$input)
    {
        try {
            $this->__validate($input, 2, [
                1 => 'array',
            ]);

            if (!$this->__isArray($input[0])) {
                $input[0] = [$input[0]];
            }

            $className = $this->__isCollection($input[0]) ? get_class($input[0]) : null;

            $input = array_map(function ($input) {
                return $this->__isCollection($input) ? $input->all() : $input;
            }, $input);

            $input = call_user_func_array('array_merge', $input);
            if ($className) {
                $input = new $className($input);
            }

            return $input;
        } catch (BaseFilterError $e) {
            throw new FilterError(sprintf(
                'Liquid error: "%s" %s',
                __FUNCTION__,
                $e->getMessage()
            ), $this->context->getToken());
        }
    }

    /**
     * Map/collect on a given property
     *
     * @param array|Traversable $input
     * @param string $property
     */
    public function map($input, $property = null)
    {
        if ($input instanceof Traversable) {
            $input = iterator_to_array($input);
        }

        if (!is_array($input)) {
            return $input;
        }

        return array_map(fn($elem) => $this->context->getValue($elem, $property), $input);
    }

    /**
     * Reverse the elements of an array
     *
     * @param array|Traversable $input
     */
    public function reverse($input): int|float|string|bool|array
    {
        if (is_scalar($input)) {
            return $input;
        }

        if ($input instanceof Traversable) {
            $input = iterator_to_array($input);
        }

        return array_reverse($input);
    }

    /**
     * Sort the elements of an array
     *
     * @param array|Traversable $input
     * @param string $property use this property of an array element
     */
    public function sort($input, $property = null)
    {
        if (is_scalar($input)) {
            return $input;
        }

        if ($input instanceof \Traversable) {
            $input = iterator_to_array($input);
        }

        if ($property === null) {
            asort($input);
        } else {
            if (!is_array($input)) {
                return [];
            } else {
                $first = reset($input);
            }

            if ($first !== false && $this->context->hasGetValue($first, $property)) {
                uasort($input, function ($a, $b) use ($property): int {
                    $valueA = $this->context->getValue($a, $property);
                    $valueB = $this->context->getValue($b, $property);
                    return $valueA <=> $valueB;
                });
            }
        }

        return $input;
    }


    /**
     * Filter elements of an array
     *
     * @param $input
     *
     * @return array|string
     */
    public function where(...$input)
    {
        try {
            $this->__validate($input, 3, [
                1 => 'scalar',
                2 => 'scalar',
            ]);

            if ($className = $this->__isCollection($input[0]) ? get_class($input[0]) : null) {
                $input[0] = $input[0]->all();
            }

            $input[0] = array_filter($input[0], function ($context) use ($input) {
                return $this->context->getValue($context, $input[1]) === $input[2];
            });

            if ($className) {
                $input[0] = new $className($input[0]);
            }

            return $input[0];
        } catch (BaseFilterError $e) {
            throw new FilterError(sprintf(
                'Liquid error: "%s" %s',
                __FUNCTION__,
                $e->getMessage()
            ), $this->context->getToken());
        }
    }

    /**
     * Remove duplicate elements from an array
     *
     * @param array|\Traversable $input
     */
    public function uniq($input): int|float|string|bool|array
    {
        if (is_scalar($input)) {
            return $input;
        }

        if ($input instanceof \Traversable) {
            $input = iterator_to_array($input);
        }

        if (!is_array($input)) {
            $input = [];
        }

        return array_unique($input);
    }

    /**
     * Split an array into chunks
     *
     * @param array|\Traversable $input
     *
     * @return array
     */
    public function chunk($input, $size = null)
    {
        if ($input instanceof \Traversable) {
            $input = iterator_to_array($input);
        }

        if (!is_numeric($size)) {
            return [];
        }

        return is_array($input) ? array_chunk($input, $size) : [];
    }

}
