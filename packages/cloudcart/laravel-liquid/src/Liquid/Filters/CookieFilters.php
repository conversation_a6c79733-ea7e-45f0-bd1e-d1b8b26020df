<?php

declare(strict_types=1);

namespace Liquid\Filters;

use Illuminate\Support\Arr;

class CookieFilters
{
    /**
     * Get JS cookie data
     * @param string $name
     * @param null $default
     * @return mixed
     */
    public function cookie($name, $default = null)
    {
        $name = preg_replace('~[^a-zA-Z0-9\_]~', '_', $clearName = $name);
        return Arr::get($_COOKIE ?? [], $name, Arr::get($_COOKIE ?? [], $clearName, $default));
    }
}
