<?php

declare(strict_types=1);

namespace Liquid\Filters;

class LineItemsForCartFilters
{
    /**
     * Filters the cart’s line_items down to those matching this product.
     *
     * Shopify’s original filter signature is:
     *   {{ cart | line_items_for: product }}
     *
     * @param array<mixed> $cart
     * @param array<mixed> $product
     * @return array<mixed>
     */
    public function line_items_for(array $cart, array $product): array
    {
        $items = $cart['line_items'] ?? [];

        return array_values(array_filter(
            $items,
            fn($line) => ($line['product_id'] ?? null) === ($product['id'] ?? null)
        ));
    }
}
