<?php

declare(strict_types=1);

namespace Liquid\Filters;

use Carbon\Carbon;

class DateFilters extends AbstractFilters
{
    /**
     * @param $input
     * @param $format
     * @return false|string
     */
    public static function date($input, $format)
    {
        if ($input instanceof Carbon) {
            $input = $input->timestamp;
        } elseif (!is_numeric($input)) {
            $input = \Carbon\Carbon::parse(strval($input))->getTimestamp();
        }

        if ($format == 'r') {
            return \Carbon\Carbon::createFromTimestamp($input)->format($format);
        }

        return strftime($format, $input);
    }
}
