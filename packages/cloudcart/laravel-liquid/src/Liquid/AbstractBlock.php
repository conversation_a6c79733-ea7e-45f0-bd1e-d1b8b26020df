<?php

declare(strict_types=1);

/**
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid;

use Illuminate\Support\Str;
use Liquid\Exceptions\SyntaxError;
use Liquid\Tokens\TagToken;
use Liquid\Tokens\TextToken;
use Liquid\Tokens\VariableToken;
use ReflectionClass;
use ReflectionException;

/**
 * Base class for blocks.
 */
class AbstractBlock extends AbstractTag
{
    /**
     * @var AbstractTag[]
     */
    protected $nodelist = [];

    /**
     * @return array
     */
    public function getNodelist()
    {
        return $this->nodelist;
    }

    /**
     * Parses the given tokens
     *
     * @param array $tokens
     *
     * @return void
     * @throws LiquidException
     * @throws ReflectionException
     */
    #[\Override]
    public function parse(array &$tokens): void
    {
        $startRegexp = new Regexp('/^' . LiquidCompiler::OPERATION_TAGS[0] . '(-)?/');
        $tagRegexp = new Regexp('/^' . LiquidCompiler::OPERATION_TAGS[0] . '(-)?\s*(\w+)\s*(.*)?' . LiquidCompiler::OPERATION_TAGS[1] . '$/s');
        $variableStartRegexp = new Regexp('/^' . LiquidCompiler::VARIABLE_TAG[0] . '/');

        $this->nodelist = [];

        if (!is_array($tokens) || empty($tokens)) {
            return;
        }

        $tags = $this->compiler->getTags();

        while (count($tokens)) {
            $raw = array_shift($tokens);

            if (preg_match('/\A\s*\{\%(-)?\s*liquid\b/si', $raw)) {
                $token = $raw;
            } else {
                $token = preg_replace('/\R+/u', ' ', $raw);
            }

            if ($startRegexp->match($token)) {
                if ($tagRegexp->match($token)) {
                    // If we found the proper block delimiter just end parsing here and let the outer block proceed
                    if ($tagRegexp->matches[2] == $this->blockDelimiter()) {
                        $this->endTag();
                        return;
                    }

                    $tagName = null;
                    if (array_key_exists($tagRegexp->matches[2], $tags)) {
                        $tagName = $tags[$tagRegexp->matches[2]];
                    }

                    $filters = [];
                    if ($tagRegexp->matches[1] == '-') {
                        $filters[] = 'lstrip';
                    }

                    if (str_ends_with((string)$tagRegexp->matches[3], '-')) {
                        $filters[] = 'rstrip';
                        $tagRegexp->matches[3] = substr((string)$tagRegexp->matches[3], 0, -1);
                    }

                    if ($tagName !== null) {
                        /** @var AbstractTag $node */
                        $node = new $tagName($tagRegexp->matches[3], $tokens, $this->compiler);
                        if ($filters) {
                            $node->setFilters($filters);
                        }

                        $this->nodelist[] = $node;
                        if (in_array($tagRegexp->matches[2], ['extends', 'layout'])) {
                            return;
                        }

                    } else {
                        $this->unknownTag(
                            $tagRegexp->matches[2],
                            $tagRegexp->matches[3],
                            $tokens,
                            $this->compiler->lastFileMTime(),
                            $this->compiler->getTextLine($tagRegexp->matches[0])
                        );
                    }
                } else {
                    throw new LiquidException(sprintf('Tag %s was not properly terminated', $token));
                }

            } elseif ($variableStartRegexp->match($token)) {
                $this->nodelist[] = $this->createVariable($token);

            } elseif ($token != '') {
                $this->nodelist[] = $token;
            }
        }

        $this->assertMissingDelimitation();
    }

    /**
     * Render the block.
     *
     * @param Context $context
     *
     * @return string
     */
    #[\Override]
    public function render(Context $context): string
    {
        return $this->renderAll($this->nodelist, $context);
    }

    /**
     * Renders all the given nodelist's nodes
     *
     * @param array $list
     * @param Context $context
     *
     * @return string
     */
    protected function renderAll(array $list, Context $context): string
    {
        $result = '';

        foreach ($list as $token) {
            if (is_object($token) && method_exists($token, 'render')) {
                $renderResult = $token->render($context);
            } else {
                $renderResult = $token;
            }

            if (is_scalar($renderResult)) {
                $result .= $renderResult;
            } elseif (is_object($renderResult) && method_exists($renderResult, '__toString')) {
                $result .= $renderResult->__toString();
            }

            if (isset($context->registers['break'])) {
                break;
            }

            if (isset($context->registers['continue'])) {
                break;
            }
        }

        return $result;
    }

    /**
     * An action to execute when the end tag is reached
     */
    protected function endTag()
    {
        // Do nothing by default
    }

    /**
     * Handler for unknown tags
     *
     * @param string $tag
     * @param array|string $params
     * @param array $tokens
     * @param string|null $file
     * @param int $line
     *
     * @throws LiquidException
     * @throws ReflectionException
     * @throws SyntaxError
     */
    protected function unknownTag(string $tag, array|string $params, array $tokens, ?string $file = null, int $line = 0)
    {
        switch ($tag) {
            case 'else':
                throw new LiquidException($this->blockName() . " does not expect else tag");
            case 'end':
                throw new LiquidException(
                    "'end' is not a valid delimiter for " .
                    $this->blockName() .
                    " tags. Use " .
                    $this->blockDelimiter()
                );
            default:
                $path = $this->compiler->getPath();
                $e = new SyntaxError(
                    sprintf('Unknown "%s" tag.', $tag),
                    $line,
                    $path->getName()
                );
                $e->addSuggestions($tag, array_keys($this->compiler->getTags()));
                throw $e;
        }
    }

    /**
     * This method is called at the end of parsing, and will through an error unless
     * this method is subclassed, like it is for Document
     *
     * @return void
     * @throws LiquidException
     * @throws ReflectionException
     */
    protected function assertMissingDelimitation(): void
    {
        if($path = $this->compiler->getPath()) {
            throw new SyntaxError(
                sprintf('"%s%s" tag was never closed.', $this->blockName(), trim(strval($this->markup)) ? ' ' . trim($this->markup) : ""),
                $this->compiler->getTextLine(sprintf('%s%s', $this->blockName(), trim(strval($this->markup)) ? ' ' . trim($this->markup) : "")),
                $path->getName()
            );
        }
        throw new LiquidException($this->blockName() . " tag was never closed");
    }

    /**
     * Returns the string that delimits the end of the block
     *
     * @return string
     * @throws ReflectionException
     */
    protected function blockDelimiter(): string
    {
        return "end" . $this->blockName();
    }

    /**
     * Returns the name of the block
     *
     * @return string
     * @throws ReflectionException
     */
    private function blockName(): string
    {
        $reflection = new ReflectionClass($this);
        return str_replace('tag', '', strtolower($reflection->getShortName()));
    }

    /**
     * Create a variable for the given token
     *
     * @param string $token
     *
     * @return Variable
     * @throws \Liquid\LiquidException
     */
    public function createVariable($token): \Liquid\Variable
    {
        $variableRegexp = new Regexp('/^' . LiquidCompiler::VARIABLE_TAG[0] . '(.*)' . LiquidCompiler::VARIABLE_TAG[1] . '$/');
        if ($variableRegexp->match($token)) {
            return $this->compileVariable($variableRegexp->matches[1]);
        }

        throw new LiquidException(sprintf('Variable %s was not properly terminated', $token));
    }

    /**
     * Create a variable for the given token
     *
     * @param string $token
     *
     * @return Variable
     * @throws \Liquid\LiquidException
     */
    private function compileVariable($variable): \Liquid\Variable
    {
        $filters = [];
        if (str_starts_with((string)$variable, '-')) {
            $filters[] = 'lstrip';
            $variable = substr((string)$variable, 1);
        }

        if (str_ends_with((string)$variable, '-')) {
            $filters[] = 'rstrip';
            $variable = substr((string)$variable, 0, -1);
        }

        $variableObject = new Variable(trim((string)$variable), $this->compiler);
        $variableObject->preSetFilters($filters);
        return $variableObject;
    }

    private function textTokenValidate(TextToken $token)
    {
        $code = $token->getCode();

        if(preg_match('/(' . LiquidCompiler::ANY_STARTING_TAG . ')/', $code, $match, PREG_OFFSET_CAPTURE)) {
            $line = preg_split("/(\<|\n)/", Str::substr($code, $match[0][1]))[0] ?? null;
            $tokenName = Str::substr($line, 2);
            if($match[0][0] == LiquidCompiler::OPERATION_TAGS[0]) {
                $type = 'Tag';
                preg_match('/(\w+)\s*?(.*)/', $tokenName, $m);
                $newToken = new TagToken($match[0][1] + $token->getStart(), $line, $token->getSource(), $m[1] ?? null, Str::substr($line, 2), $m[2] ?? null);
                $tokenHelp = $newToken->getTag();
            } else {
                $type = 'Variable';
                $v = new Variable($tokenName, $this->compiler);
                $newToken = new TagToken($match[0][1] + $token->getStart(), $line, $token->getSource(), $v->getName(), Str::substr($line, 2), null);
                $tokenHelp = $newToken->getTag();
            }

            $newToken->setName($token->getName());

            throw new SyntaxError(sprintf('%s [%s] was not properly terminated', $type, $tokenHelp));
        }

        return true;
    }
}
