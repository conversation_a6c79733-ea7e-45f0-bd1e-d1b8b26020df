<?php

declare(strict_types=1);


namespace Liquid;

use ErrorException;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\View\Compilers\Compiler;
use Illuminate\View\Compilers\CompilerInterface;
use Liquid\Storage\CompiledStoreInterface;

class JsonLiquidCompiler extends Compiler implements CompilerInterface
{
    /**
     * @var null|JsonCompilerEngine
     */
    protected ?JsonCompilerEngine $compiler = null;

    /**
     * @var null|LiquidCompiler
     */
    protected ?LiquidCompiler $liquid_compiler = null;

    /**
     * @var CompiledStoreInterface
     */
    protected $compiledStore = null;

    public function getCompiledPath($path): string
    {
        return $this->getLiquidCompiler()->getCompiledPath($path);
    }

    /**
     * @param $path
     * @return void
     */
    public function setFileMtime($path): void
    {
        $this->liquid_compiler->setFileMtime($path);
    }

    /**
     * @return null|string
     */
    public function lastFileMTime(): ?string
    {
        return $this->liquid_compiler->lastFileMTime();
    }

    /**
     * Compile the view at the given path.
     *
     * @param TemplateContent $path
     * @return void
     * @throws FileNotFoundException
     */
    public function compile($path): void
    {
        if ($this->isExpired($path->getName())) {
            $raw = $path->getContent();
            $data = json_decode($raw, true);

            $data['sections'] = array_map(function($section) {
                return [
                    'section' => $section,
                    'compiled' => $this->getLiquidCompiler()->compileTemplateContent(
                        $this->getLiquidCompiler()->findTemplate('sections/' . $section['type'])
                    )
                ];
            }, $data['sections'] ?? []);

            foreach($data['sections'] as $s) {
                try {
                    serialize($s);
                } catch (\Throwable $e) {
                    // Throw LiquidException for serialization errors
                    throw new LiquidException(
                        'Failed to serialize section data: ' . $e->getMessage() .
                        ' (Section type: ' . (is_array($s) ? 'array' : gettype($s)) . ')',
                        0,
                        $e
                    );
                }
            }

            $this->compiledStore->store($path->getName(), serialize($data));
        }
    }

    /**
     * Renders the current template
     *
     * @param TemplateContent $path
     * @param array $assigns an array of values for the template
     *
     * @return string
     * @throws ErrorException
     * @throws FileNotFoundException
     * @throws LiquidException
     * @throws \ReflectionException
     */
    public function render($path, array $assigns = [])
    {
        $data = unserialize($this->compiledStore->get($path->getName()));
        $sections = $data['sections'] ?? [];
        if(!empty($data['order']) && is_array($data['order'])) {
            $order = $data['order'];
            uksort($sections, function($key1, $key2) use ($order) {
                return (array_search($key1, $order) > array_search($key2, $order));
            });
        }

        $sectionHtml = [];
        //@todo must by refactoring
        foreach ($sections as $id => $section) {
            $sectionViewData = [
                'section' => $section['section'],
                'sectionId' => $id,
            ];

            if ($section['section']['type'] === 'tabs-collection') {
                $sectionViewData['collection'] = [
                    'products' => \App\Models\Product\Product::paginate(12),
                ];
            }

            $sectionHtml[$id] = $this->getLiquidCompiler()->renderInline($section['compiled'], array_merge($assigns, $sectionViewData));
        }

        $assigns['content_for_layout'] = implode("\n", $sectionHtml);

        return $this->getLiquidCompiler()->renderContentForLayout($assigns);
    }

    /**
     * Determine if the view at the given path is expired.
     *
     * @param string $path
     * @return bool
     */
    #[\Override]
    public function isExpired($path)
    {
        return $this->liquid_compiler->isExpired($path);
    }

    public function setLiquidCompiler(?LiquidCompiler $liquid_compiler): JsonLiquidCompiler
    {
        $this->liquid_compiler = $liquid_compiler;
        return $this;
    }

    public function getLiquidCompiler(): ?LiquidCompiler
    {
        if($this->liquid_compiler === null) {
            $this->liquid_compiler = \App::make('liquid.compiler');
        }

        return $this->liquid_compiler;
    }

    public function getCompiler(): JsonCompilerEngine
    {
        return $this->compiler;
    }

    public function setCompiler(JsonCompilerEngine $compiler): static
    {
        $this->compiler = $compiler;
        return $this;
    }

    /**
     * Set the compiled store instance
     */
    public function setCompiledStore($store): static
    {
        $this->compiledStore = $store;
        return $this;
    }

    public function get($path, array $data = [])
    {
        return $this->getCompiler()->get($path, $data);
    }
}
