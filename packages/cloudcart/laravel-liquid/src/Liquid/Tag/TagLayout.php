<?php

declare(strict_types=1);

/**
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid\Tag;

use Liquid\AbstractTag;
use Liquid\Context;
use Liquid\Document;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;

/**
 * https://github.com/harrydeluxe/php-liquid/wiki/Template-Inheritance
 * Layout a template by another one.
 *
 * Example:
 *
 *     {% layout "base" %}
 */
class TagLayout extends AbstractTag
{
    /**
     * @var string The name of the template
     */
    private ?string $layoutPath = null;

    /**
     * @var Document The Document that represents the included template
     */
    private ?\Liquid\Document $document = null;

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     *
     * @param LiquidCompiler $compiler
     * @throws LiquidException
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $regex = new Regexp('/\(?("[^"]+"|\'[^\']+\')?\)?/');
        if ($regex->match($markup)) {
            if(!empty($regex->matches[1])) {
                $this->layoutPath = substr((string)$regex->matches[1], 1, strlen((string)$regex->matches[1]) - 2);
            } else {
                $this->layoutPath = 'none';
            }
        } else {
            throw new LiquidException("Error in tag 'layout' - Valid syntax: layout '[template path]'");
        }

        parent::__construct($markup, $tokens, $compiler);
    }

    /**
     * Parses the tokens
     *
     * @param array $tokens
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     */
    #[\Override]
    public function parse(array &$tokens): void
    {
        if ($this->layoutPath == 'none') {
            $rest = $tokens;
        } else {
            // read the source of the template and create a new sub document
            $source = $this->compiler->getTemplateSource('layout/' . $this->layoutPath);

            // tokens in this new document
            $rest = $this->tokenize($source);
        }

        $this->document = new Document(null, $rest, $this->compiler);
    }

    /**
     * Renders the node
     *
     * @param Context $context
     *
     * @return string
     * @throws LiquidException
     */
    #[\Override]
    public function render(Context $context)
    {
        $context->push();
        $result = $this->document->render($context);
        $context->pop();
        return $result;
    }
}
