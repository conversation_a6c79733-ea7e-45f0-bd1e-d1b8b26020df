<?php

declare(strict_types=1);

/**
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid\Tag;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Liquid\AbstractBlock;
use Liquid\Constant;
use Liquid\Context;
use Liquid\Drop\ForLoopDrop;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;
use Liquid\Traits\TransformLaravelModel;

/**
 * Loops over an array, assigning the current value to a given variable
 *
 * Example:
 *
 *     {%for item in array%} {{item}} {%endfor%}
 *
 *     With an array of 1, 2, 3, 4, will return 1 2 3 4
 *
 *       or
 *
 *       {%for i in (1..10)%} {{i}} {%endfor%}
 *       {%for i in (1..variable)%} {{i}} {%endfor%}
 *
 */
class TagFor extends AbstractBlock
{

    use TransformLaravelModel;

    /**
     * @var string The name of the loop
     */
    private string $name;

    /**
     * @var string The variable name to use in the loop
     */
    private string $variableName;

    /**
     * @var string The collection name to loop over
     */
    private string $collectionName;

    /**
     * @var string The type of loop (collection or digit)
     */
    private string $type = 'collection';

    /**
     * @var int|null The start of the range for digit loops
     */
    private ?int $start = null;

    /**
     * @var array Shopify-specific attributes
     */
    protected array $shopify_attributes = [
        'limit', 'offset', 'reversed', 'range', 'cols', 'rows'
    ];

    /**
     * Array holding the nodes to render for each logical block
     */
    private array $nodelistHolders = [];

    /**
     * Array holding the block type, block markup (conditions) and block nodelist
     *
     * @var array
     */
    protected $blocks = [];

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     *
     * @param LiquidCompiler $compiler
     * @throws LiquidException
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $this->nodelist = &$this->nodelistHolders[count($this->blocks)];
        $this->blocks[] = ['for', $markup, &$this->nodelist];

        parent::__construct($markup, $tokens, $compiler);

        $syntaxRegexp = new Regexp('/(\w+)\s+in\s+(' . Constant::VariableSignaturePartial . ')/');

        if ($syntaxRegexp->match($markup)) {
            $this->variableName = $syntaxRegexp->matches[1];
            $this->collectionName = $syntaxRegexp->matches[2];
            $this->name = $syntaxRegexp->matches[1] . '-' . $syntaxRegexp->matches[2];
            $this->extractAttributes($markup);
        } else {
            $syntaxRegexp = new Regexp('/^([a-zA-Z_][\w]*)\s+in\s+\((\d+|' . LiquidCompiler::VARIABLE_NAME . ')\s*\.\.\s*(\d+|' . LiquidCompiler::VARIABLE_NAME . ')\)$/');
            if ($syntaxRegexp->match($markup)) {
                $this->type = 'digit';
                $this->variableName = $syntaxRegexp->matches[1];
                $this->start = (int)$syntaxRegexp->matches[2];
                $this->collectionName = $syntaxRegexp->matches[3];
                $this->name = $syntaxRegexp->matches[1] . '-digit';
                $this->extractAttributes($markup);
            } else {
                throw new LiquidException("Syntax Error in 'for' - Valid syntax: for [item] in [collection]");
            }
        }
    }

    /**
     * Handler for unknown tags, handle else tags
     *
     * @param string $tag
     * @param array|string $params
     * @param array $tokens
     * @param int $line
     */
    #[\Override]
    protected function unknownTag(string $tag, array|string $params, array $tokens, ?string $file = null, int $line = 0)
    {
        if ($tag == 'else') {
            // Update reference to nodelistHolder for this block
            $this->nodelist = &$this->nodelistHolders[count($this->blocks) + 1];
            $this->nodelistHolders[count($this->blocks) + 1] = [];

            $this->blocks[] = [$tag, $params, &$this->nodelist];
        } else {
            parent::unknownTag($tag, $params, $tokens, $file, $line);
        }
    }

    /**
     * Renders the tag
     *
     * @param Context $context
     *
     * @return null|string
     */
    #[\Override]
    public function render(Context $context): string
    {
        if (!isset($context->registers['for'])) {
            $context->registers['for'] = [];
        }

        switch ($this->type) {
            case 'collection':
                $collection = $context->get($this->collectionName);
                if ($collection instanceof Model || $collection instanceof Builder || $collection instanceof Relation) {
                    $collection = $collection->offset($this->validateOffset($this->attributes['offset'] ?? 0))
                        ->limit($this->validateNumberItems($this->attributes['limit'] ?? 50))->get()->all();
                    $collection = $this->transformModel($collection);
                }

                if ($collection instanceof \Traversable) {
                    $collection = iterator_to_array($collection);
                }

                if (is_null($collection) || !is_array($collection) || count($collection) == 0) {
                    if (!$collection) {
                        foreach ($this->blocks as $block) {
                            if ($block[0] == 'else') {
                                return $this->renderAll($block[2], $context);
                            }
                        }
                        return '';
                    }
                    $context->push();
                    $nodelist = array_pop($this->nodelistHolders);
                    $result = $this->renderAll($nodelist, $context);
                    $context->pop();
                    return $result;
                }

                // Apply Shopify filters
                $collection = $this->applyShopifyFilters($collection, $context);

                $range = [0, count($collection)];
                $result = '';
                $segment = array_slice($collection, $range[0], $range[1] - $range[0]);

                $context->push();
                $length = count($segment);

                // Get parent loop if exists
                $parentLoop = $context->get('forloop');
                $parentLoopDrop = $parentLoop instanceof ForLoopDrop ? $parentLoop : null;

                // Create ForLoopDrop instance
                $forLoopDrop = new ForLoopDrop($this->name, $length, $parentLoopDrop);

                foreach ($segment as $item) {
                    $context->set($this->variableName, $item);
                    $context->set('forloop', $forLoopDrop);

                    $result .= $this->renderAll($this->nodelist, $context);
                    $forLoopDrop->increment();
                }

                $context->pop();
                return $result;

            case 'digit':
                $start = $this->start;
                $end = $this->collectionName;

                if (!is_numeric($start)) {
                    $start = $context->get($start);
                }
                if (!is_numeric($end)) {
                    $end = $context->get($end);
                }

                $start = (int)$start;
                $end = (int)$end;

                $range = range($start, $end);
                if (isset($this->attributes['reversed'])) {
                    $range = array_reverse($range);
                }

                $result = '';
                $context->push();
                $length = count($range);

                // Get parent loop if exists
                $parentLoop = $context->get('forloop');
                $parentLoopDrop = $parentLoop instanceof ForLoopDrop ? $parentLoop : null;

                // Create ForLoopDrop instance
                $forLoopDrop = new ForLoopDrop($this->name, $length, $parentLoopDrop);

                foreach ($range as $i) {
                    $context->set($this->variableName, $i);
                    $context->set('forloop', $forLoopDrop);

                    $result .= $this->renderAll($this->nodelist, $context);
                    $forLoopDrop->increment();
                }

                $context->pop();
                return $result;
        }

        return '';
    }

    /**
     * Extracts reversed attributes from a markup string.
     *
     * @param string $markup
     */
    #[\Override]
    protected function extractAttributes($markup)
    {
        $this->attributes = [];

        // Extract standard attributes
        $attributeRegex = new Regexp('/\s*(\w+)\s*:\s*(' . LiquidCompiler::QUOTED_FRAGMENT . ')/');
        $attributeRegex->matchAll($markup);

        foreach ($attributeRegex->matches[1] as $index => $key) {
            $this->attributes[$key] = $attributeRegex->matches[2][$index];
        }

        // Extract Shopify-specific attributes
        foreach ($this->shopify_attributes as $attribute) {
            if (str_contains($markup, $attribute)) {
                $this->attributes[$attribute] = true;
            }
        }

        // Handle range attribute
        if (isset($this->attributes['range']) && is_string($this->attributes['range'])) {
            $range = explode('..', $this->attributes['range']);
            if (count($range) === 2) {
                $this->start = (int)$range[0];
                $this->collectionName = $range[1];
                $this->type = 'digit';
            }
        }

        // Handle cols and rows for tablerow
        if (isset($this->attributes['cols'])) {
            $this->attributes['cols'] = (int)$this->attributes['cols'];
        }
        if (isset($this->attributes['rows'])) {
            $this->attributes['rows'] = (int)$this->attributes['rows'];
        }
    }

    protected function applyShopifyFilters($collection, Context $context)
    {
        // Apply limit
        if (isset($this->attributes['limit'])) {
            $limit = (int)$this->attributes['limit'];
            $collection = array_slice($collection, 0, $limit);
        }

        // Apply offset
        if (isset($this->attributes['offset'])) {
            $offset = (int)$this->attributes['offset'];
            $collection = array_slice($collection, $offset);
        }

        // Apply reversed
        if (isset($this->attributes['reversed'])) {
            $collection = array_reverse($collection);
        }

        // Apply cols and rows for tablerow
        if (isset($this->attributes['cols']) || isset($this->attributes['rows'])) {
            $cols = $this->attributes['cols'] ?? 3;
            $rows = $this->attributes['rows'] ?? null;

            $chunks = array_chunk($collection, $cols);
            if ($rows !== null) {
                $chunks = array_slice($chunks, 0, $rows);
            }
            $collection = $chunks;
        }

        return $collection;
    }
}
