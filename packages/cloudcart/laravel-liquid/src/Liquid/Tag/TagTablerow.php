<?php

declare(strict_types=1);

/**
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid\Tag;

use Liquid\AbstractBlock;
use Liquid\Context;
use Liquid\Drop\TablerowLoopDrop;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;

/**
 * Quickly create a table from a collection
 */
class TagTablerow extends AbstractBlock
{
    /**
     * The variable name of the table tag
     *
     * @var string
     */
    public $variableName;

    /**
     * The collection name of the table tags
     *
     * @var string
     */
    public $collectionName;

    /**
     * Additional attributes
     *
     * @var array
     */
    public $attributes = [];

    /**
     * Shopify-specific attributes
     *
     * @var array
     */
    protected array $shopify_attributes = [
        'limit', 'offset', 'cols', 'range'
    ];

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     *
     * @param LiquidCompiler $compiler
     * @throws LiquidException
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        parent::__construct($markup, $tokens, $compiler);

        $syntax = new Regexp('/(\w+)\s+in\s+(' . LiquidCompiler::VARIABLE_NAME . ')/');

        if ($syntax->match($markup)) {
            $this->variableName = $syntax->matches[1];
            $this->collectionName = $syntax->matches[2];

            $this->extractAttributes($markup);
        } else {
            throw new LiquidException("Syntax Error in 'table_row loop' - Valid syntax: tablerow [item] in [collection] cols:3");
        }
    }

    /**
     * Renders the current node
     *
     * @param Context $context
     *
     * @return string
     * @throws LiquidException
     */
    #[\Override]
    public function render(Context $context): string
    {
        $collection = $context->get($this->collectionName);

        // Handle range attribute (e.g., range: (1..10))
        if (isset($this->attributes['range'])) {
            $rangeValue = $context->get($this->attributes['range']);
            if (is_string($rangeValue) && preg_match('/\((\d+)\.\.(\d+)\)/', $rangeValue, $matches)) {
                $start = (int)$matches[1];
                $end = (int)$matches[2];
                $collection = range($start, $end);
            }
        }

        if ($collection instanceof \Traversable) {
            $collection = iterator_to_array($collection);
        }

        if (!is_array($collection)) {
            return '';
        }

        // discard keys
        $collection = array_values($collection);

        if (isset($this->attributes['limit']) || isset($this->attributes['offset'])) {
            $limit = $context->get($this->attributes['limit']);
            $offset = $context->get($this->attributes['offset']);
            $collection = array_slice($collection, $offset, $limit);
        }

        $length = count($collection);

        $cols = isset($this->attributes['cols']) ? $context->get($this->attributes['cols']) : PHP_INT_MAX;

        $context->push();

        $result = '';

        $rows = array_chunk($collection, $cols);
        $rows = array_map(function ($columns) use ($cols): array {
            $columns = array_replace(array_fill(0, $cols, null), $columns);
            return $columns;
        }, $rows);

        // Create TablerowLoopDrop instance
        $tablerowLoopDrop = new TablerowLoopDrop($this->collectionName, $length, $cols);

        $index = 0;
        foreach ($rows as $rowIndex => $columns) {
            $result .= '<tr class="row' . ($rowIndex + 1) . "\">\n";
            $break = false;
            $continue = false;
            foreach ($columns as $colIndex => $col) {
                $context->set($this->variableName, null);
                $context->set('tablerowloop', null);

                $result .= '<td class="col' . ($colIndex + 1) . "\">\n";

                if ($index < $length) {
                    $context->set($this->variableName, $col);
                    $context->set('tablerowloop', $tablerowLoopDrop);

                    $break = $break ?: isset($context->registers['break']);
                    $continue = isset($context->registers['continue']);

                    if (!$continue || !$break) {
                        $result .= trim($this->renderAll($this->nodelist, $context));
                    }

                    if (isset($context->registers['continue'])) {
                        unset($context->registers['continue']);
                    }

                    if (isset($context->registers['break'])) {
                        unset($context->registers['break']);
                    }
                }

                $result .= "</td>\n";

                $index++;
                if ($index < $length) {
                    $tablerowLoopDrop->increment();
                }
            }

            $result .= "</tr>\n";

            if ($break) {
                unset($context->registers['break']);
                break;
            }
        }

        if (isset($context->registers['break'])) {
            unset($context->registers['break']);
        }

        if (isset($context->registers['continue'])) {
            unset($context->registers['continue']);
        }

        $context->pop();

        $result .= "</tr>\n";

        return $result;
    }

    /**
     * Extract attributes from markup
     *
     * @param string $markup
     * @return void
     */
    protected function extractAttributes(string $markup): void
    {
        $this->attributes = [];

        // Extract Shopify-specific attributes
        foreach ($this->shopify_attributes as $attribute) {
            $pattern = '/' . $attribute . ':\s*(' . LiquidCompiler::QUOTED_FRAGMENT . ')/';
            if (preg_match($pattern, $markup, $matches)) {
                $this->attributes[$attribute] = $matches[1];
            }
        }
    }
}
