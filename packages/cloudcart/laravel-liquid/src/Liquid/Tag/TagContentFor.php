<?php

namespace Liquid\Tag;

use Liquid\AbstractTag;
use Liquid\Context;

class TagContentFor extends AbstractTag
{
    protected string $name;

    public function __construct(string $markup, array &$tokens, $compiler = null)
    {
        parent::__construct($markup, $tokens, $compiler);

        $this->name = trim($markup, " \t\n\r\0\x0B'\"");
    }

    public function render(Context $context): string
    {
        $slots = $context->get('content_for') ?? [];
        return $slots[$this->name] ?? '';
    }
}
