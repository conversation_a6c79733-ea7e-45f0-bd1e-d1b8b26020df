<?php

declare(strict_types=1);

/**
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid\Tag;

use Liquid\AbstractTag;
use Liquid\Context;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;
use Liquid\Variable;

/**
 * Cycles between a list of values; calls to the tag will return each value in turn
 *
 * Example:
 *     {%cycle "one", "two"%} {%cycle "one", "two"%} {%cycle "one", "two"%}
 *
 *     this will return:
 *     one two one
 *
 *     Cycles can also be named, to differentiate between multiple cycle with the same values:
 *     {%cycle 1: "one", "two" %} {%cycle 2: "one", "two" %} {%cycle 1: "one", "two" %} {%cycle 2: "one", "two" %}
 *
 *     will return
 *     one one two two
 */
class TagCycle extends AbstractTag
{
    /**
     * @var string The name of the cycle
     */
    private string $name;

    /**
     * @var array The values to cycle through
     */
    private array $variables = [];

    /**
     * @var array The attributes of the tag
     */
    protected array $attributes = [];

    /**
     * @var string|null The reset value
     */
    private ?string $reset = null;

    /**
     * @var string|null The clear value
     */
    private ?string $clear = null;

    /**
     * @var array Shopify-specific attributes
     */
    protected array $shopify_attributes = [
        'group', 'name', 'reset', 'clear'
    ];

    /**
     * Constructor
     *
     * @param string $markup
     *
     * @param array $tokens
     * @param LiquidCompiler $compiler
     * @throws LiquidException
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        parent::__construct(null, $tokens, $compiler);

        $syntaxRegexp = new Regexp('/(' . LiquidCompiler::QUOTED_FRAGMENT . ')\s*:\s*(.*)/');
        if ($syntaxRegexp->match($markup)) {
            $this->name = $syntaxRegexp->matches[1];
            $this->variables = explode(',', $syntaxRegexp->matches[2]);
        } else {
            $this->variables = explode(',', $markup);
            $this->name = $this->variables[0];
        }

        $this->extractAttributes($markup);
    }

    /**
     * Renders the tag
     *
     * @return string
     * @var Context $context
     */
    #[\Override]
    public function render(Context $context)
    {
        try {
            if (!isset($context->registers['cycle'])) {
                $context->registers['cycle'] = [];
            }

            if (!isset($context->registers['cycle'][$this->name])) {
                $context->registers['cycle'][$this->name] = 0;
            }

            if (isset($this->reset)) {
                $context->registers['cycle'][$this->name] = 0;
            }

            if (isset($this->clear)) {
                unset($context->registers['cycle'][$this->name]);
                return '';
            }

            $index = $context->registers['cycle'][$this->name];
            $context->registers['cycle'][$this->name] = ($index + 1) % count($this->variables);

            return $context->get($this->variables[$index]);
        } catch (\Exception $e) {
            throw new LiquidException("Error in cycle tag: " . $e->getMessage());
        }
    }

    protected function extractAttributes($markup)
    {
        $this->attributes = [];

        // Extract standard attributes
        $attributeRegex = new Regexp('/\s*(\w+)\s*:\s*(' . LiquidCompiler::QUOTED_FRAGMENT . ')/');
        $attributeRegex->matchAll($markup);

        foreach ($attributeRegex->matches[1] as $index => $key) {
            $this->attributes[$key] = $attributeRegex->matches[2][$index];
        }

        // Extract Shopify-specific attributes
        foreach ($this->shopify_attributes as $attribute) {
            if (str_contains($markup, $attribute)) {
                $this->attributes[$attribute] = true;
            }
        }

        // Handle group attribute
        if (isset($this->attributes['group'])) {
            if (!is_string($this->attributes['group'])) {
                throw new LiquidException("Invalid group name in cycle tag");
            }
            $this->name = $this->attributes['group'];
        }

        // Handle name attribute
        if (isset($this->attributes['name'])) {
            if (!is_string($this->attributes['name'])) {
                throw new LiquidException("Invalid name in cycle tag");
            }
            $this->name = $this->attributes['name'];
        }

        // Handle reset attribute
        if (isset($this->attributes['reset'])) {
            if (!is_string($this->attributes['reset'])) {
                throw new LiquidException("Invalid reset value in cycle tag");
            }
            $this->reset = $this->attributes['reset'];
        }

        // Handle clear attribute
        if (isset($this->attributes['clear'])) {
            if (!is_string($this->attributes['clear'])) {
                throw new LiquidException("Invalid clear value in cycle tag");
            }
            $this->clear = $this->attributes['clear'];
        }
    }
}
