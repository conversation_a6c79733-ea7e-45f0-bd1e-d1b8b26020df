<?php

declare(strict_types=1);

/**
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid\Tag;

use Liquid\AbstractBlock;
use Liquid\Context;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;
use Liquid\Traits\DecisionTrait;

/**
 * A switch statement
 *
 * Example:
 *
 *     {% case condition %}{% when foo %} foo {% else %} bar {% endcase %}
 */
class TagCase extends AbstractBlock
{
    use DecisionTrait;

    /**
     * Stack of nodelists
     *
     * @var array
     */
    public $nodelists;

    /**
     * The nodelist for the else (default) nodelist
     *
     * @var array
     */
    public $elseNodelist;

    /**
     * The left value to compare
     *
     * @var string
     */
    public $left;

    /**
     * The current right value to compare
     *
     * @var mixed
     */
    public $right;

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     *
     * @param LiquidCompiler $compiler
     * @throws LiquidException
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $this->nodelists = [];
        $this->elseNodelist = [];

        parent::__construct($markup, $tokens, $compiler);

        $syntaxRegexp = new Regexp('/' . LiquidCompiler::QUOTED_FRAGMENT . '/');

        if ($syntaxRegexp->match($markup)) {
            $this->left = $syntaxRegexp->matches[0];
        } else {
            throw new LiquidException("Syntax Error in tag 'case' - Valid syntax: case [condition]"); // harry
        }
    }

    /**
     * Pushes the last nodelist onto the stack
     */
    #[\Override]
    protected function endTag()
    {
        $this->pushNodelist();
    }

    /**
     * Unknown tag handler
     *
     * @param string $tag
     * @param array|string $params
     * @param array $tokens
     * @param int $line
     *
     * @throws \Liquid\LiquidException
     */
    #[\Override]
    protected function unknownTag(string $tag, array|string $params, array $tokens, ?string $file = null, int $line = 0)
    {
        $whenSyntaxRegexp = new Regexp('/' . LiquidCompiler::QUOTED_FRAGMENT . '/');

        switch ($tag) {
            case 'when':
                // push the current nodelist onto the stack and prepare for a new one
                if ($whenSyntaxRegexp->match($params)) {
                    $this->pushNodelist();
                    $this->right = $whenSyntaxRegexp->matches[0];
                    $this->nodelist = [];

                } else {
                    throw new LiquidException("Syntax Error in tag 'case' - Valid when condition: when [condition]"); // harry
                }

                break;

            case 'else':
                // push the last nodelist onto the stack and prepare to receive the else nodes
                $this->pushNodelist();
                $this->right = null;
                $this->elseNodelist = &$this->nodelist;
                $this->nodelist = [];
                break;

            default:
                parent::unknownTag($tag, $params, $tokens, $file, $line);
        }
    }

    /**
     * Pushes the current right value and nodelist into the nodelist stack
     */
    public function pushNodelist(): void
    {
        if (!is_null($this->right)) {
            $this->nodelists[] = [$this->right, $this->nodelist];
        }
    }

    /**
     * Renders the node
     *
     * @param Context $context
     *
     * @return string
     */
    #[\Override]
    public function render(Context $context): string
    {
        $output = '';
        $runElseBlock = true;

        foreach ($this->nodelists as $data) {
            [$right, $nodelist] = $data;

            if ($this->equalVariables($this->left, $right, $context)) {
                $runElseBlock = false;

                $context->push();
                $output .= $this->renderAll($nodelist, $context);
                $context->pop();
            }
        }

        if ($runElseBlock) {
            $context->push();
            $output .= $this->renderAll($this->elseNodelist, $context);
            $context->pop();
        }

        return $output;
    }
}
