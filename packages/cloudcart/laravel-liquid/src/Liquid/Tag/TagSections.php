<?php

declare(strict_types=1);

namespace Liquid\Tag;

use Liquid\AbstractTag;
use Liquid\Context;
use Liquid\Document;
use Liquid\LiquidException;

class TagSections extends AbstractTag
{
    protected ?string $sectionGroup = null;

    public function render(Context $context): string
    {
        if ($this->sectionGroup === null) {

            $markup = trim($this->markup);

            if (
                (str_starts_with($markup, "'") && str_ends_with($markup, "'")) ||
                (str_starts_with($markup, '"') && str_ends_with($markup, '"'))
            ) {
                $markup = substr($markup, 1, -1);
            }

            $markup = trim($markup);
            $this->sectionGroup = $markup;
        }

        if (empty($this->sectionGroup)) {
            throw new LiquidException("Missing section group name in 'sections' tag.");
        }

        $sections = $context->get('sections');

        if (!is_array($sections)) {
            return '';
        }

        $output = [];

        foreach ($sections as $key => $data) {
            if (!str_starts_with(strval($key), $this->sectionGroup)) {
                continue;
            }

            if (!isset($data['type'])) {
                continue;
            }

            $document = $this->compiler->compileTemplateContent(
                $this->compiler->findTemplate('sections/' . $data['type'])
            );

            $context->push();
            foreach($data as $k=>$v) {
                $context->set($k, $v);
            }

            $output[] = $document->render($context);
            $context->pop();
        }

        return implode("\n", $output);
    }
}

