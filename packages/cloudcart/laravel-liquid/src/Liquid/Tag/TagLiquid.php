<?php

declare(strict_types=1);

namespace Liquid\Tag;

use Liquid\AbstractTag;
use Liquid\Document;
use Liquid\LiquidCompiler;
use Liquid\Context;

class TagLiquid extends AbstractTag
{
    private Document $document;

    #[\Override]
    public function parse(array &$tokens): void
    {
        $source = trim($this->markup);

        if ($source === '') {
            $tokens = [];
            $this->document = new Document(null, $tokens, $this->compiler);
            return;
        }

        $source = str_replace(["\r\n", "\r"], "\n", $source);

        // Filter out comment lines that start with #
        $lines = explode("\n", $source);
        $filteredLines = [];

        foreach ($lines as $line) {
            $trimmedLine = trim($line);
            // Skip lines that start with # (comments)
            if ($trimmedLine === '' || str_starts_with($trimmedLine, '#')) {
                continue;
            }
            $filteredLines[] = $line;
        }

        // Rebuild source without comment lines
        $source = implode("\n", $filteredLines);

        // If source is empty after filtering comments, return empty document
        if (trim($source) === '') {
            $tokens = [];
            $this->document = new Document(null, $tokens, $this->compiler);
            return;
        }

        $keywords = [
            // Control flow tags
            'if', 'elsif', 'else', 'endif',
            'unless', 'endunless',
            'case', 'when', 'endcase',

            // Loop tags
            'for', 'endfor', 'tablerow', 'endtablerow',
            'break', 'continue',

            // Variable tags
            'assign', 'capture', 'endcapture',
            'increment', 'decrement',

            // Template tags
            'include', 'render', 'section', 'endsection', 'sections',
            'layout', 'block', 'endblock',
            'extends', 'content_for', 'endcontent_for',

            // Output tags
            'echo', 'comment', 'endcomment',
            'raw', 'endraw', 'inline_comment',

            // Form tags
            'form', 'endform',

            // Style/Script tags
            'style', 'endstyle', 'stylesheet', 'endstylesheet',
            'javascript', 'endjavascript', 'schema', 'endschema',

            // Utility tags
            'cycle', 'ifchanged', 'endifchanged',
            'paginate', 'endpaginate',
            'liquid', 'doc', 'enddoc',
            'yield',

            // Custom CloudCart tags
            'auth', 'endauth', 'guest', 'endguest',
            'call', 'function', 'endfunction',
            'gdpr', 'script', 'endscript',
            'orderBy', 'perPage', 'route',
            'searchInputConfig', 'socials',
            'googleReCaptcha', 'instagram',
            'csrf_token'
        ];

        $len = strlen($source);
        $positions = [];
        $inS = false;
        $inD = false;

        for ($i = 0; $i < $len; $i++) {
            $ch = $source[$i];

            if ($ch === "'" && !$inD) {
                $inS = !$inS;
                continue;
            }
            if ($ch === '"' && !$inS) {
                $inD = !$inD;
                continue;
            }

            if (!$inS && !$inD) {
                foreach ($keywords as $kw) {
                    $kl = strlen($kw);
                    if (
                        $i + $kl <= $len
                        && substr($source, $i, $kl) === $kw
                        // must be a word‐boundary on both sides
                        && ($i === 0 || ctype_space($source[$i - 1]))
                        && ($i + $kl === $len || ctype_space($source[$i + $kl]))
                    ) {
                        $positions[] = $i;
                        $i += $kl - 1;
                        break;
                    }
                }
            }
        }

        if (empty($positions)) {
            $stmts = [$source];
        } else {
            sort($positions);
            $stmts = [];
            $count = count($positions);
            for ($j = 0; $j < $count; $j++) {
                $start = $positions[$j];
                $end = ($j + 1 < $count) ? $positions[$j + 1] : $len;
                $stmts[] = trim(substr($source, $start, $end - $start));
            }
        }

        $open = LiquidCompiler::OPERATION_TAGS[0];  // "{%"
        $close = LiquidCompiler::OPERATION_TAGS[1];  // "%}"
        $rebuild = '';
        foreach ($stmts as $stmt) {
            if ($stmt !== '') {
                $rebuild .= "{$open} {$stmt} {$close}";
            }
        }

        $innerTokens = $this->compiler->tokenize($rebuild);
        $this->document = new Document(null, $innerTokens, $this->compiler);
    }

    #[\Override]
    public function render(Context $context): string
    {
        return $this->document->render($context);
    }
}
