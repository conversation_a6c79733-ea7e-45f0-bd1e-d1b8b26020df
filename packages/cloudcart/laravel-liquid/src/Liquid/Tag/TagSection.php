<?php

declare(strict_types=1);

namespace Liquid\Tag;

use App\Helper\Html;
use App\LiquidEngine\Helpers\Schema;
use App\LiquidEngine\Helpers\SchemaBlock;
use Illuminate\Contracts\View\Factory;
use Illuminate\View\View;
use Liquid\AbstractTag;
use Liquid\Constant;
use Liquid\Context;
use Liquid\Document;
use Liquid\Exceptions\SyntaxError;
use Liquid\LiquidCompiler;
use Liquid\Regexp;
use Liquid\LiquidException;
use Liquid\Tokens\TagToken;
use App\LiquidEngine\Models\Theme as ThemeModel;
use Throwable;

class TagSection extends AbstractTag
{
    /**
     * @var string The key of section
     */
    private ?string $key = null;

    /**
     * @var string The name of the template
     */
    private ?string $templateName = null;

    /**
     * @var bool check if self included
     */
    private bool $self_include = false;

    /**
     * @var Document The Document that represents the included template
     */
    private ?\Liquid\Document $document = null;

    /**
     * @var null|array schema defined for template
     */
    private $schema = null;

    /**
     * @var AbstractTag[]
     */
    protected $nodelist = array();

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     *
     * @param LiquidCompiler $compiler
     * @throws SyntaxError
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $regex = new Regexp('/("[^"]+"|\'[^\']+\')(\s+(' . $compiler::QUOTED_FRAGMENT . '+))?/');

        if ($regex->match($markup)) {
            $this->templateName = 'sections/' . ($this->key = substr($regex->matches[1], 1, strlen($regex->matches[1]) - 2));

            $this->extractAttributes($markup);
        } else {
            throw new SyntaxError("Error in tag 'section' - Valid syntax: section '[template]' [attribute:attribute_value ... \n]", $compiler);
        }

        parent::__construct($markup, $tokens, $compiler);
    }

    /**
     * @param mixed $tokens
     * @return mixed
     */
    protected function findSchema(&$tokens)
    {
        $open = false;
        $schema = null;
        $this->nodelist = array();
        while (count($tokens)) {
            $token = array_shift($tokens);
            if ($token instanceof TagToken && $token->getTag() == 'schema') {
                $open = true;
                continue;
            } elseif ($token instanceof TagToken && $token->getTag() == 'endschema') {
                $open = false;
                continue;
            } elseif ($open) {
                $schema = $this->jsonCleanDecode($token->getCode(), true);
            } else {
                $this->nodelist[] = $token;
            }
        }

        return $schema;
    }

    /**
     * @param $json
     * @param bool $assoc
     * @param int $depth
     * @param int $options
     * @return mixed
     */
    protected function jsonCleanDecode($json, $assoc = false, $depth = 512, $options = 0): mixed
    {
        $json = str_replace(array("\n", "\r"), "", $json);
        $json = preg_replace('/([{,]+)(\s*)([^"]+?)\s*:/', '$1"$3":', $json);
        $json = preg_replace('/(,)\s*}$/', '}', $json);

        return json_decode($json, $assoc, $depth, $options);
    }

    /**
     * Parses the tokens
     *
     * @param array $tokens
     *
     * @throws SyntaxError
     * @throws Throwable
     */
    public function parse(array &$tokens): void
    {
        try {
            // read the source of the template and create a new sub document
            $source = $this->compiler->getTemplateSource($this->templateName);
        } catch (Throwable $throwable) {
            if (preg_match('/View \[(.*)\] not found/', $throwable->getMessage(), $m)) {
                throw new SyntaxError(sprintf('View [%s] not found', $m[1]));
            }

            throw $throwable;
        }

        if (($tagKey = array_search(get_class($this), $this->compiler->getTags(), true)) === false) {
            $tagKey = 'section';
        }

        $regex = new Regexp(sprintf('/%s\s*' . $tagKey . '\s*%s(%s)%s/imUs', Constant::TagStartPartial, '[\'\"]', substr($this->templateName, 8), '[\'\"]'));
        $this->self_include = (bool)$regex->match($source);

        if (!$this->self_include) {
            $templateTokens = $this->tokenize($source);
            $this->schema = $this->findSchema($templateTokens);
            $this->document = new Document(null, $this->nodelist, $this->compiler);
        }
    }

    /**
     * Renders the node
     *
     * @param Context $context
     *
     * @return string|Factory|View
     * @throws LiquidException
     */
    public function render(Context $context)
    {
        if ($this->self_include) {
            return $this->_renderOutline($context);
        } else {
            return $this->_renderInline($context);
        }
    }

    /**
     * Renders the node
     *
     * @param Context $context
     *
     * @return string
     * @throws LiquidException
     */
    protected function _renderInline(Context $context): string
    {
        $context->push();

        $settings = null;
        if ($this->schema) {
            if ($context->hasKey('section.settings') && is_array($oldSectionData = $context->get('section.settings'))) {
                $settings = $this->getSchemaConfig();
                $context->set('section', array_merge($settings, [
                    'settings' => array_merge($oldSectionData, $settings['settings'] ?? [])
                ]));
            } else {
                $context->set('section', $settings = $this->getSchemaConfig());
            }
        }

        foreach ($this->attributes as $key => $value) {
            $context->set($key, $context->get($value));
        }

        $result = $this->document->render($context);

        if (($section = request()->input('section_id')) == substr($this->templateName, 8)) {
            $result = sprintf(
                '%s%s%s',
                $context->get('content_for_header'),
                $result,
                $context->get('content_for_footer')
            );
        }

        $context->pop();

        return Html::tag('div', [
            'id' => 'cc-' . str_replace(['.', '/'], '-', $this->templateName),
            'class' => 'cc-section' . (!empty($settings['class']) ? ' ' . $settings['class'] : '')
        ], $result);
    }

    /**
     * Renders the node
     *
     * @param Context $context
     *
     * @return string|Factory|View
     * @throws LiquidException
     */
    protected function _renderOutline(Context $context)
    {
        $assigns = $context->getAllAssigns();
        foreach ($this->attributes as $key => $value) {
            $assigns[$key] = $context->get($value);
        }

        return view($this->templateName, $assigns);
    }

    protected function getSchemaConfig(): array
    {
        $schema = new Schema(is_array($this->schema) ? $this->schema : []);

        //@todo must check section name && settings type
        if (!empty($this->key)) {
            $settings = collect($schema->getSettings());

            $configuration = ThemeModel::getThemeSectionConfigs($this->key, $group);

            $settings = $settings->map(function ($defaults) use ($configuration): ?array {
                if (!empty($defaults['id'])) {
                    return [
                        'id' => $defaults['id'],
                        'value' => $configuration->has($defaults['id']) ? $configuration->get($defaults['id']) : ($defaults['default'] ?? null)
                    ];
                }

                return null;
            })->filter();

            $schemaReturn = array_merge(is_array($this->schema) ? $this->schema : [], [
                'id' => $group->id ?? null,
                'name' => $schema->getName() ?: $this->key,
                'key' => $this->key,
                'settings' => $settings->pluck('value', 'id')->all()
            ]);

            if ($schema->hasBlocks()) {
                $blocks = array_map(function (SchemaBlock $block) {
                    $configurations = ThemeModel::getThemeSectionBlocksConfigs($this->key, $block->getType(), $group);

                    $allSettings = [];
                    foreach ($configurations as $configuration) {
                        $settings = collect($block->getSettings());
                        $settings = $settings->map(function ($defaults) use ($configuration): ?array {
                            if (!empty($defaults['id'])) {
                                return [
                                    'id' => $defaults['id'],
                                    'value' => $configuration->has($defaults['id']) ? $configuration->get($defaults['id']) : ($defaults['default'] ?? null)
                                ];
                            }

                            return null;
                        })->filter();

                        if ($settings->count()) {
                            $allSettings[] = [
                                'id' => $group->id ?? null,
                                'name' => $block->getName() ?: $block->getType(),
                                'type' => $block->getType(),
                                'key' => $this->key,
                                'settings' => $settings->pluck('value', 'id')->all()
                            ];
                        }
                    }

                    return collect($allSettings);
                }, $schema->getBlocks());


                if (($blocks = collect($blocks)->collapse()->filter()) && $blocks->count()) {
                    $schemaReturn['blocks'] = $blocks->all();
                }
            }

            return $schemaReturn;
        }

        return [];
    }
}
