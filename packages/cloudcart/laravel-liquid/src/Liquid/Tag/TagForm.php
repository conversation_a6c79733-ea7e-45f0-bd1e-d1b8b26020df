<?php

declare(strict_types=1);

namespace Liquid\Tag;

use Liquid\AbstractBlock;
use Liquid\Context;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;

class TagForm extends AbstractBlock
{
    protected string $formType;

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     *
     * @param LiquidCompiler $compiler
     * @throws LiquidException
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        parent::__construct($markup, $tokens, $compiler);

        $this->formType = trim($markup);
    }

    public function render(Context $context): string
    {
        // Basic opening form tag
        $action = "/{$this->formType}";
        $method = 'POST';
        $csrf = '<input type="hidden" name="_token" value="' . csrf_token() . '">';

        $output = "<form method=\"{$method}\" action=\"{$action}\">" . $csrf;
        $output .= parent::render($context); // Render inner form contents
        $output .= "</form>";

        return $output;
    }
}
