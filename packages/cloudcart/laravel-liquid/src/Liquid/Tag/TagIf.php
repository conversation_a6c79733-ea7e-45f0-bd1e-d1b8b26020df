<?php

declare(strict_types=1);

/**
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid\Tag;

use Liquid\AbstractBlock;
use Liquid\Context;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;
use Liquid\Traits\DecisionTrait;
use Liquid\Traits\HelpersTrait;

/**
 * An if statement
 *
 * Example:
 *
 *     {% if true %} YES {% else %} NO {% endif %}
 *
 *     will return:
 *     YES
 *
 * 0 is truthy
 *
 *     {% if 0 %} YES {% else %} NO {% endif %}
 *
 *     will return:
 *     YES
 */
class TagIf extends AbstractBlock
{

    use DecisionTrait;
    use HelpersTrait;
    /**
     * Array holding the nodes to render for each logical block
     */
    private array $nodelistHolders = [];

    /**
     * Array holding the block type, block markup (conditions) and block nodelist
     *
     * @var array
     */
    protected $blocks = [];

    /**
     * @var array
     */
    protected $conditional_operators = [
        '==', '!=', '>=', '<=', '>', '<', 'contains', 'starts_with', 'ends_with', 'has_key', 'blank', 'empty', 'nil', 'present', 'size', 'type', 'first', 'last'
    ];

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     * @param LiquidCompiler $compiler
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $this->nodelist = &$this->nodelistHolders[count($this->blocks)];

        $this->blocks[] = ['if', $markup, &$this->nodelist];

        parent::__construct($markup, $tokens, $compiler);
    }

    /**
     * Handler for unknown tags, handle else tags
     *
     * @param string $tag
     * @param array|string $params
     * @param array $tokens
     * @param int $line
     * @throws LiquidException
     * @throws \ReflectionException
     */
    #[\Override]
    protected function unknownTag(string $tag, array|string $params, array $tokens, ?string $file = null, int $line = 0)
    {
        if ($tag == 'else' || $tag == 'elsif') {
            $this->nodelist = &$this->nodelistHolders[count($this->blocks) + 1];
            $this->nodelistHolders[count($this->blocks) + 1] = [];

            $this->blocks[] = [$tag, $params, &$this->nodelist];

        } else {
            parent::unknownTag($tag, $params, $tokens, $file, $line);
        }
    }

    /**
     * @param $string
     * @param Context $context
     * @return string
     * @throws LiquidException
     */
    protected function recursiveReplaceBracket($string, Context $context): string
    {
        if (!is_string($string)) {
            $string = (string)$string;
        }

        // If no brackets, return the string as is
        if (!str_contains($string, '(') || !str_contains($string, ')')) {
            return $string;
        }

        // Find the outermost matching brackets
        $regex = new Regexp('/^\((([^()]*|(?R))*)\)$/');
        if ($regex->match($string)) {
            $innerContent = $regex->matches[1];
            
            // Recursively process inner content
            $processedInner = $this->recursiveReplaceBracket($innerContent, $context);
            
            // If inner content still has brackets, process it again
            if (str_contains($processedInner, '(') && str_contains($processedInner, ')')) {
                return $this->recursiveReplaceBracket($processedInner, $context);
            }
            
            // Process the inner content as a logical expression
            return $this->parseLogicalExpresion($processedInner, $context);
        }

        // If no matching brackets found, process as a logical expression
        return $this->parseLogicalExpresion($string, $context);
    }

    /**
     * @param $string
     * @param Context $context
     * @return string
     * @throws LiquidException
     */
    protected function parseLogicalExpresion($expresion, Context $context)
    {
        $expresion = trim($expresion);
        $operators = ['and', 'or', '||', '&&'];
        $operatorRegex = '/^\s*(and|or|\|\||\&\&)\s+|\s+(and|or|\|\||\&\&)\s+|\s+(and|or|\|\||\&\&)\s*$/i';
        
        if (preg_match($operatorRegex, $expresion)) {
            throw new LiquidException("Syntax Error in tag 'if' - Invalid logical expression");
        }

        $parts = preg_split($operatorRegex, $expresion, -1, PREG_SPLIT_NO_EMPTY);
        $operators = preg_split('/\s*(and|or|\|\||\&\&)\s*/i', $expresion, -1, PREG_SPLIT_NO_EMPTY | PREG_SPLIT_DELIM_CAPTURE);
        
        $result = null;
        $currentOperator = null;

        foreach ($operators as $part) {
            $part = trim($part);
            if (empty($part)) continue;

            if (in_array(strtolower($part), ['and', 'or', '||', '&&'])) {
                $currentOperator = strtolower($part);
                continue;
            }

            $condition = $this->parseCondition($part, $context);
            
            if ($result === null) {
                $result = $condition;
            } else {
                switch ($currentOperator) {
                    case 'and':
                    case '&&':
                        $result = $result && $condition;
                        break;
                    case 'or':
                    case '||':
                        $result = $result || $condition;
                        break;
                }
            }
        }

        return $result;
    }

    protected function parseCondition($condition, Context $context)
    {
        $condition = trim($condition);
        if (empty($condition)) {
            return false;
        }

        $operators = implode('|', array_map('preg_quote', $this->conditional_operators));
        $co = '(?:\s*(?:' . $operators . ')\s*)';
        $regex = '/^\s*(((?!(' . $co . ')).)*)\s*(' . $co . ')?\s*(' . LiquidCompiler::QUOTED_FRAGMENT . ')?\s*$/';

        if (!preg_match($regex, $condition, $matches)) {
            return false;
        }

        $left = trim($matches[1]);
        $operator = isset($matches[4]) ? trim($matches[4]) : null;
        $right = isset($matches[5]) ? trim($matches[5]) : null;

        if ($operator === null) {
            return $context->get($left);
        }

        $left = $context->get($left);
        if ($right !== null) {
            $right = $context->get($right);
        }

        return $this->interpretCondition($left, $right, $operator, $context);
    }

    /**
     * Render the tag
     *
     * @param Context $context
     *
     * @return string
     * @throws \Liquid\LiquidException
     */
    #[\Override]
    public function render(Context $context): string
    {
        $context->push();

        $result = '';
        foreach ($this->blocks as $block) {
            if ($block[0] == 'else') {
                $result = $this->renderAll($block[2], $context);

                break;
            }

            if ($block[0] == 'if' || $block[0] == 'elsif') {
                if ($this->recursiveReplaceBracket($block[1], $context) === 'true') {
                    $result = $this->renderAll($block[2], $context);
                    break;
                }
            }
        }

        $context->pop();

        return $result;
    }

    protected function interpretCondition($left, $right, $operator, Context $context)
    {
        // Handle special operators
        if (in_array($operator, ['blank', 'empty', 'nil', 'present'])) {
            switch ($operator) {
                case 'blank':
                    return empty($left) || trim($left) === '';
                case 'empty':
                    return empty($left);
                case 'nil':
                    return $left === null;
                case 'present':
                    return !empty($left) && trim($left) !== '';
            }
        }

        // Handle Shopify-specific operators
        if (in_array($operator, ['size', 'type', 'first', 'last'])) {
            switch ($operator) {
                case 'size':
                    if (is_array($left)) {
                        return count($left);
                    } elseif (is_string($left)) {
                        return strlen($left);
                    } elseif (is_object($left) && method_exists($left, 'count')) {
                        return $left->count();
                    }
                    return 0;
                case 'type':
                    return gettype($left);
                case 'first':
                    if (is_array($left)) {
                        return reset($left);
                    } elseif (is_string($left)) {
                        return substr($left, 0, 1);
                    }
                    return null;
                case 'last':
                    if (is_array($left)) {
                        return end($left);
                    } elseif (is_string($left)) {
                        return substr($left, -1);
                    }
                    return null;
            }
        }

        // Handle string operators
        if (in_array($operator, ['contains', 'starts_with', 'ends_with'])) {
            if (!is_string($left) && !is_numeric($left)) {
                return false;
            }
            $left = (string)$left;
            $right = (string)$right;

            switch ($operator) {
                case 'contains':
                    return str_contains($left, $right);
                case 'starts_with':
                    return str_starts_with($left, $right);
                case 'ends_with':
                    return str_ends_with($left, $right);
            }
        }

        // Handle array operators
        if ($operator === 'has_key') {
            return is_array($left) && array_key_exists($right, $left);
        }

        // Handle numeric comparisons
        if (in_array($operator, ['==', '!=', '>=', '<=', '>', '<'])) {
            if (is_numeric($left) && is_numeric($right)) {
                $left = (float)$left;
                $right = (float)$right;
            }
        }

        // Standard comparisons
        switch ($operator) {
            case '==':
                return $left == $right;
            case '!=':
                return $left != $right;
            case '>=':
                return $left >= $right;
            case '<=':
                return $left <= $right;
            case '>':
                return $left > $right;
            case '<':
                return $left < $right;
            default:
                return false;
        }
    }
}
