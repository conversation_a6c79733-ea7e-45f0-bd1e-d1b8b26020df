<?php

declare(strict_types=1);

/**
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid\Tag;

use Liquid\AbstractBlock;
use Liquid\Context;

/**
 * Creates a comment; everything inside will be ignored
 *
 * Example:
 *
 *     {% comment %} This will be ignored {% endcomment %}
 */
class TagComment extends AbstractBlock
{
    /**
     * Parse the comment block and restore any placeholders
     *
     * @param array $tokens
     */
    #[\Override]
    public function parse(array &$tokens): void
    {
        // Restore placeholders in comment content before parsing
        foreach ($tokens as &$token) {
            if (is_string($token)) {
                $token = str_replace(
                    ['__LIQUID_TAG_START__', '__LIQUID_VAR_START__', '__LIQUID_TAG_END__', '__LIQUID_VAR_END__'],
                    ['{%', '{{', '%}', '}}'],
                    $token
                );
            }
        }

        parent::parse($tokens);
    }

    /**
     * Renders the block
     *
     * @param Context $context
     *
     * @return string empty string
     */
    #[\Override]
    public function render(Context $context): string
    {
        return '';
    }
}
