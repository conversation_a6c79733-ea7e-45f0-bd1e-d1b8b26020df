<?php

declare(strict_types=1);

namespace Liquid\Tag;

use Liquid\AbstractTag;
use Liquid\Constant;
use Liquid\Context;
use Liquid\Document;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;
use Liquid\Variable;

class TagInclude extends AbstractTag
{
    protected Variable|string|null $templateName = null;
    protected ?bool $collection = null;
    protected mixed $variable = null;
    protected ?Document $document = null;
    protected bool $self_include = false;

    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $markup = trim($markup);

        $regex = new Regexp('/(' . $compiler::QUOTED_FRAGMENT . '|[a-zA-Z_][\w\.]*)\s*(with|for)?\s*(' . $compiler::QUOTED_FRAGMENT . '|[a-zA-Z_][\w\.]*)?/i');

        if ($regex->match($markup)) {
            $rawName = trim($regex->matches[1]);

            if (preg_match('/^["\'].*["\']$/', $rawName)) {
                $this->templateName = trim($rawName, '\'"');
            } else {
                $this->templateName = new Variable($rawName, $compiler);
            }

            $this->collection = isset($regex->matches[2]) && strtolower($regex->matches[2]) === 'for';
            $this->variable = $regex->matches[3] ?? null;

            $this->extractAttributes($markup);
        } else {
            throw new LiquidException("Error in tag 'include' - Valid syntax: include '[template]' (with|for) [object|collection]");
        }

        if (
            is_string($this->templateName) &&
            trim($this->templateName) === ''
        ) {
            throw new LiquidException("Include tag template name cannot be empty.");
        }

        parent::__construct($markup, $tokens, $compiler);
    }

    public function parse(array &$tokens): void
    {
        // Parsing is deferred until render time
    }

    public function render(Context $context): string
    {
        $templateName = $this->resolveTemplateName($context);

        if (empty($templateName)) {
            return "<!-- Include '' not found -->";
        }

        $source = $this->resolveTemplateSource($templateName);
        $tagName = array_search(static::class, $this->compiler->getTags(), true) ?: 'render';

        $regex = new Regexp(sprintf('/%s\s*%s\s*[\'"]%s[\'"]/imUs', Constant::TagStartPartial, $tagName, preg_quote($templateName, '/')));
        $this->self_include = (bool)$regex->match($source);

        if (!$this->self_include) {
            $templateTokens = $this->tokenize($source);
            $this->document = new Document(null, $templateTokens, $this->compiler);
        }

        return $this->self_include
            ? $this->_renderOutline($context, $templateName)
            : $this->_renderInline($context, $templateName);
    }

    protected function _renderInline(Context $context, string $templateName): string
    {
        $result = '';
        $variable = $context->get($this->variable);

        $context->push();
        foreach ($this->attributes as $key => $value) {
            $context->set($key, $context->get($value));
        }

        if ($this->collection && is_array($variable)) {
            foreach ($variable as $item) {
                $context->set($templateName, $item);
                $result .= $this->document->render($context);
            }
        } else {
            if ($this->variable !== null) {
                $context->set($templateName, $variable);
            }
            $result .= $this->document->render($context);
        }

        $context->pop();
        return $result;
    }

    protected function _renderOutline(Context $context, string $templateName): string
    {
        $result = '';
        $variable = $context->get($this->variable);
        $source = $this->resolveTemplateSource($templateName);
        $tokens = $this->tokenize($source);

        $context->push();
        foreach ($this->attributes as $key => $value) {
            $context->set($key, $context->get($value));
        }

        if ($this->collection && is_array($variable)) {
            foreach ($variable as $item) {
                $context->set($templateName, $item);
                $result .= (new Document(null, $tokens, $this->compiler))->render($context);
            }
        } else {
            if ($this->variable !== null) {
                $context->set($templateName, $variable);
            }
            $result .= (new Document(null, $tokens, $this->compiler))->render($context);
        }

        $context->pop();
        return $result;
    }

    protected function resolveTemplateName(Context $context): string
    {
        $name = null;

        if ($this->templateName instanceof Variable) {
            $name = $this->templateName->render($context);
        } elseif (is_string($this->templateName)) {
            $name = $this->templateName;
        }

        $name = trim((string)$name);

        if ($name === '') {
            throw new LiquidException("Template name cannot be empty in " . static::class);
        }

        return $name;
    }

    protected function resolveTemplateSource(string $templateName): string
    {
        $original = $templateName;

        if (is_file($templateName)) {
            return file_get_contents($templateName);
        }

        $names = [
            "snippets/$templateName",
            "theme/$templateName",
        ];

        foreach ($names as $viewName) {
            try {
                $path = $this->compiler->getViewFinder()->find($viewName);
                return $this->compiler->getFileSource($path);
            } catch (\Throwable $e) {
                // Skip and try next
            }
        }

        return sprintf("<!-- Include '%s' not found -->", $original);
    }
}
