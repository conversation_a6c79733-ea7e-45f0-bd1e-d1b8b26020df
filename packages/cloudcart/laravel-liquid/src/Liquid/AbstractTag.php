<?php

declare(strict_types=1);

/**
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid;

use Liquid\Tokens\TagToken;
use Liquid\Tokens\TextToken;
use Liquid\Tokens\VariableToken;
use Liquid\Traits\TokenizeTrait;

/**
 * Base class for tags.
 */
abstract class AbstractTag
{
    use TokenizeTrait;

    /**
     * Additional attributes
     *
     * @var array
     */
    protected $attributes = [];

    /**
     * Global filters
     *
     * @var array
     */
    protected $globalFilters = [];

    /**
     * @var null|TagToken|VariableToken|TextToken
     */
    protected $_tag_token;

    /**
     * Constructor.
     *
     * @param string $markup
     * @param array $tokens
     * @param LiquidCompiler $compiler
     */
    public function __construct(/**
     * The markup for the tag
     */
    protected $markup, array &$tokens, protected LiquidCompiler $compiler)
    {
        $this->parse($tokens);
    }

    /**
     * Parse the given tokens.
     *
     * @param array $tokens
     */
    public function parse(array &$tokens): void
    {
        // Do nothing by default
    }

    /**
     * Render the tag with the given context.
     *
     * @param Context $context
     *
     * @return string
     */
    public function render(Context $context)
    {
        return '';
    }

    /**
     * Set Filters
     *
     * @param array $filters
     */
    public function setFilters(array $filters): void
    {
        foreach ($filters as $filter) {
            if (!is_array($filter)) {
                $filter = [$filter, []];
            }

            $this->globalFilters[] = [$filter[0], isset($filter[1]) && is_array($filter[1]) ? $filter[1] : []];
        }
    }

    /**
     * Extracts tag attributes from a markup string.
     *
     * @param string $markup
     */
    protected function extractAttributes($markup)
    {
        $this->attributes = [];

        $attributeRegexp = new Regexp(LiquidCompiler::TAG_ATTRIBUTES);

        $matches = $attributeRegexp->scan($markup);

        foreach ($matches as $match) {
            $this->attributes[$match[0]] = $match[1];
        }
    }

    /**
     * Returns the name of the tag.
     *
     * @return string
     */
    protected function name()
    {
        return strtolower(static::class);
    }
}
