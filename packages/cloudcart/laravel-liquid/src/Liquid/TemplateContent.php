<?php


namespace Liquid;

class TemplateContent
{
    public function __construct(
        protected string $content,
        protected int $fileMtime,
        protected string $path,
        protected string $name,
        protected string $engine = 'liquid'
    )
    {
        //
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * @param string $content
     * @return TemplateContent
     */
    public function setContent(string $content): static
    {
        $this->content = $content;
        return $this;
    }

    /**
     * @return int
     */
    public function getFileMtime(): int
    {
        return $this->fileMtime;
    }

    /**
     * @return string
     */
    public function getPath(): string
    {
        return $this->path;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    public function getEngine(): string
    {
        return $this->engine;
    }

}
