<?php

declare(strict_types=1);

/*
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */
namespace Liquid\Cache;

use Liquid\Cache;

/**
 * Implements cache with data stored in an embedded variable with no handling of expiration dates for simplicity
 */
class Local extends Cache
{
    private $cache = [];

    /**
     * {@inheritdoc}
     */
    public function read($key, $unserialize = true)
    {
        return $this->cache[$key] ?? false;
    }

    /**
     * {@inheritdoc}
     */
    public function exists($key): bool
    {
        return isset($this->cache[$key]);
    }

    /**
     * {@inheritdoc}
     */
    public function write($key, $value, $serialize = true): bool
    {
        $this->cache[$key] = $value;
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function flush($expiredOnly = false): bool
    {
        $this->cache = [];
        return true;
    }
}
