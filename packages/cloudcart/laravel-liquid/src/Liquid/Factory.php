<?php

namespace Liquid;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\View\Factory as BaseFactory;

class Factory extends BaseFactory
{
    /**
     * Add a new namespace to the loader.
     *
     * @param string $namespace
     * @param string|array $hints
     * @return $this
     * @throws LiquidException
     */
    public function addNamespace($namespace, $hints)
    {
        throw new LiquidException(sprintf('Method "addNamespace" is not usable'));
    }

    /**
     * Add a new namespace to the loader.
     *
     * @param string $namespace
     * @param string|array $hints
     * @return $this
     * @throws LiquidException
     */
    public function replaceNamespace($namespace, $hints)
    {
        throw new LiquidException(sprintf('Method "replaceNamespace" is not usable'));
    }

    /**
     * Get the evaluated view contents for the given view.
     *
     * @param string $view
     * @param Arrayable|array $data
     * @param array $mergeData
     * @return View
     */
    #[\Override]
    public function make($view, $data = [], $mergeData = [])
    {
        /** @var TemplateContent $path */
        $path = $this->finder->find($view);

        // Next, we will create the view instance and call the view creator for the view
        // which can set any data, etc. Then we will return the view instance back to
        // the caller for rendering or performing other view manipulations on this.
        $data = array_merge($mergeData, $this->parseData($data));

        return tap($this->viewInstance($view, $path, $data), function ($view) {
            $this->callCreator($view);
        });
    }

    /**
     * Create a new view instance from the given arguments.
     *
     * @param string $view
     * @param TemplateContent $path
     * @param array|Arrayable $data
     * @return \Illuminate\Contracts\View\View
     */
    protected function viewInstance($view, $path, $data)
    {
        return new View($this, $this->engines->resolve($path->getEngine()), $view, $path, $data);
    }

    /**
     * Get the compiler instance.
     *
     * @return LiquidCompiler
     */
    public function getCompiler(): LiquidCompiler
    {
        return $this->engines->resolve('liquid');
    }

    /**
     * Flush the cache of views located by the finder.
     *
     * @return void
     */
    public function flushFinderCache()
    {
        $this->getFinder()->flush();
    }
}
