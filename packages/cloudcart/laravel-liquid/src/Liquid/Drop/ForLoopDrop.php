<?php

declare(strict_types=1);

/**
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid\Drop;

use Liquid\Drop;

/**
 * ForLoopDrop provides loop information for for loops in Liquid templates.
 * 
 * This class represents the forloop object available inside {% for %} tags,
 * providing access to loop state information like index, length, first/last status, etc.
 * It also supports nested loops through the parentloop property.
 * 
 * Available properties:
 * - first: bool - true if it's the first iteration
 * - last: bool - true if it's the last iteration  
 * - index: int - current iteration number (1-based)
 * - index0: int - current iteration number (0-based)
 * - rindex: int - remaining iterations (including current)
 * - rindex0: int - remaining iterations (0-based)
 * - length: int - total number of iterations
 * - parentloop: ForLoopDrop|null - parent loop for nested loops
 * 
 * @package Liquid\Drop
 */
class ForLoopDrop extends Drop
{
    /**
     * Current iteration index (0-based)
     */
    protected int $index = 0;

    /**
     * Loop name identifier
     */
    protected string $name;

    /**
     * Total number of items in the loop
     */
    protected int $length;

    /**
     * Parent loop for nested loops
     */
    public readonly ?ForLoopDrop $parentLoop;

    /**
     * Constructor
     *
     * @param string $name Loop name identifier
     * @param int $length Total number of items in the loop
     * @param ForLoopDrop|null $parentLoop Parent loop for nested loops
     */
    public function __construct(
        string $name,
        int $length,
        ?ForLoopDrop $parentLoop = null
    ) {
        $this->name = $name;
        $this->length = $length;
        $this->parentLoop = $parentLoop;
    }

    /**
     * Increment the loop index
     */
    public function increment(): void
    {
        $this->index += 1;
    }

    /**
     * Get the loop name
     */
    public function name(): string
    {
        return $this->name;
    }

    /**
     * Handle dynamic property access for loop variables
     *
     * @param string $method The property name being accessed
     * @return mixed The property value
     */
    protected function beforeMethod(string $method)
    {
        return match ($method) {
            'first' => $this->index === 0,
            'last' => $this->index === $this->length - 1,
            'index' => $this->index + 1,
            'index0' => $this->index,
            'rindex' => $this->length - $this->index,
            'rindex0' => $this->length - $this->index - 1,
            'length' => $this->length,
            'parentloop' => $this->parentLoop,
            'name' => $this->name,
            default => null,
        };
    }

    /**
     * Convert to string representation
     */
    public function __toString(): string
    {
        return "ForLoopDrop({$this->name})";
    }
}
