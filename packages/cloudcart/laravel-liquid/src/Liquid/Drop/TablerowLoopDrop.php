<?php

declare(strict_types=1);

/**
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid\Drop;

use Liquid\Drop;

/**
 * TablerowLoopDrop provides loop information for tablerow loops in Liquid templates.
 * 
 * This class represents the tablerowloop object available inside {% tablerow %} tags,
 * providing access to loop state information including table-specific properties like
 * row and column information.
 * 
 * Available properties:
 * - first: bool - true if it's the first item
 * - last: bool - true if it's the last item
 * - index: int - current item number (1-based)
 * - index0: int - current item number (0-based)
 * - rindex: int - remaining items (including current)
 * - rindex0: int - remaining items (0-based)
 * - length: int - total number of items
 * - col: int - current column number (1-based)
 * - col0: int - current column number (0-based)
 * - col_first: bool - true if first column
 * - col_last: bool - true if last column
 * - row: int - current row number (1-based)
 * 
 * @package Liquid\Drop
 */
class TablerowLoopDrop extends Drop
{
    /**
     * Current item index (0-based)
     */
    protected int $index = 0;

    /**
     * Collection name identifier
     */
    protected string $name;

    /**
     * Total number of items in the loop
     */
    protected int $length;

    /**
     * Number of columns per row
     */
    protected int $cols;

    /**
     * Constructor
     *
     * @param string $name Collection name identifier
     * @param int $length Total number of items in the loop
     * @param int $cols Number of columns per row
     */
    public function __construct(
        string $name,
        int $length,
        int $cols
    ) {
        $this->name = $name;
        $this->length = $length;
        $this->cols = $cols;
    }

    /**
     * Increment the loop index
     */
    public function increment(): void
    {
        $this->index += 1;
    }

    /**
     * Get the collection name
     */
    public function name(): string
    {
        return $this->name;
    }

    /**
     * Get current row number (1-based)
     */
    protected function getCurrentRow(): int
    {
        return (int)floor($this->index / $this->cols) + 1;
    }

    /**
     * Get current column number (0-based)
     */
    protected function getCurrentCol0(): int
    {
        return $this->index % $this->cols;
    }

    /**
     * Get current column number (1-based)
     */
    protected function getCurrentCol(): int
    {
        return $this->getCurrentCol0() + 1;
    }

    /**
     * Handle dynamic property access for loop variables
     *
     * @param string $method The property name being accessed
     * @return mixed The property value
     */
    protected function beforeMethod(string $method)
    {
        return match ($method) {
            'first' => $this->index === 0,
            'last' => $this->index === $this->length - 1,
            'index' => $this->index + 1,
            'index0' => $this->index,
            'rindex' => $this->length - $this->index,
            'rindex0' => $this->length - $this->index - 1,
            'length' => $this->length,
            'name' => $this->name,
            'key' => $this->index,
            'col' => $this->getCurrentCol(),
            'col0' => $this->getCurrentCol0(),
            'col_first' => $this->getCurrentCol0() === 0,
            'col_last' => $this->getCurrentCol0() === $this->cols - 1,
            'row' => $this->getCurrentRow(),
            default => null,
        };
    }

    /**
     * Convert to string representation
     */
    public function __toString(): string
    {
        return "TablerowLoopDrop({$this->name})";
    }
}
