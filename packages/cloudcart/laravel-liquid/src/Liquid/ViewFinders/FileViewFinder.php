<?php

namespace Liquid\ViewFinders;

use InvalidArgumentException;
use Illuminate\Filesystem\Filesystem;
use Liquid\TemplateContent;
use Illuminate\View\FileViewFinder as BaseFileViewFinder;

class FileViewFinder extends BaseFileViewFinder
{

    /**
     * Register a view extension with the finder.
     *
     * @var string[]
     */
    protected $extensions = ['json', 'liquid'];

    /**
     * Create a new file view loader instance.
     *
     * @param Filesystem $files
     * @param  array  $paths
     * @return void
     */
    public function __construct(Filesystem $files, array $paths)
    {
        parent::__construct($files, $paths);
    }

    /**
     * Find the given view in the list of paths.
     *
     * @param string $name
     * @param array $paths
     * @return TemplateContent
     *
     * @throws InvalidArgumentException
     */
    #[\Override]
    protected function findInPaths($name, $paths): TemplateContent
    {
        foreach ((array) $paths as $path) {
            foreach($this->extensions ? : ['liquid'] as $extension) {
                if ($this->files->exists($viewPath = $path . '/' . $name . '.' . $extension)) {
                    return new TemplateContent(
                        $this->files->get($viewPath),
                        $this->files->lastModified($viewPath),
                        $viewPath,
                        $name,
                        $extension
                    );
                }
            }
        }

        throw new InvalidArgumentException("View [{$name}] not found.");
    }

    /**
     * Prepend a location to the finder.
     *
     * @param  string  $location
     * @return void
     */
    #[\Override]
    public function prependLocation($location): void
    {
        parent::prependLocation(resource_path("themes/$location"));
    }


    /**
     * Add a location to the finder.
     *
     * @param  string  $location
     * @return void
     */
    #[\Override]
    public function addLocation($location): void
    {
        parent::addLocation(resource_path("themes/$location"));
    }
}
