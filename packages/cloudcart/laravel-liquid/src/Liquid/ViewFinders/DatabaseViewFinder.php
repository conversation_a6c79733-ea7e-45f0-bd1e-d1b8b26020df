<?php

namespace Liquid\ViewFinders;

use Illuminate\Database\ConnectionInterface;
use InvalidArgumentException;
use Liquid\TemplateContent;
use Illuminate\View\FileViewFinder as BaseFileViewFinder;

class DatabaseViewFinder extends BaseFileViewFinder
{

    /**
     * Register a view extension with the finder.
     *
     * @var string[]
     */
    protected $extensions = ['json', 'liquid'];

    /**
     * The filesystem instance.
     *
     * @var ConnectionInterface
     */
    protected ConnectionInterface $connection;

    /**
     * Create a new file view loader instance.
     *
     * @param  ConnectionInterface $connection
     * @param  string|array $table
     * @return void
     */
    public function __construct(ConnectionInterface $connection, string|array $table)
    {
        $this->connection = $connection;
        $this->paths = is_array($table) ? $table : [$table];
    }

    /**
     * Find the given view in the list of tables.
     *
     * @param  string  $name
     * @param  array   $paths
     * @return TemplateContent
     *
     * @throws \InvalidArgumentException
     */
    #[\Override]
    protected function findInPaths($name, $paths)
    {
        foreach ((array) $paths as $table) {
            foreach($this->extensions ? : ['liquid'] as $extension) {
                if ($template = $this->table($table)->where('path', $table . '/' . $name . '.' . $extension)->first()) {
                    return new TemplateContent(
                        $template->content,
                        strtotime($template->updated_at),
                        $table . '::' . $name . '.' . $extension,
                        $name,
                        $extension
                    );
                }
            }
        }

        throw new InvalidArgumentException("View [{$name}] not found.");
    }

    /**
     * Get the underlying database connection.
     *
     * @return \Illuminate\Database\ConnectionInterface
     */
    public function getConnection()
    {
        return $this->connection;
    }

    /**
     * Get a query builder for the cache table.
     *
     * @param string $table
     * @return \Illuminate\Database\Query\Builder
     */
    protected function table($table)
    {
        return $this->connection->table($table);
    }
}
