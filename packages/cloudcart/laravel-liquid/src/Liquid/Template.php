<?php

declare(strict_types=1);

namespace Liquid;

use Liquid\Exceptions\CacheException;
use Liquid\Exceptions\MissingFilesystemException;
use Liquid\Traits\TokenizeTrait;

class Template
{
    use TokenizeTrait;

    const CLASS_PREFIX = '\Liquid\Cache\\';

    /**
     * @var Document
     */
    private $root;

    /**
     * @var LiquidCompiler|null
     */
    private LiquidCompiler|null $fileSystem = null;

    /**
     * @var LiquidCompiler|null
     */
    private ?LiquidCompiler $compiler = null;

    /**
     * @var array Filters
     */
    private array $filters = [];

    /**
     * @var callable|null Tick function
     */
    private $tickFunction = null;

    /**
     * @var array Global custom tags
     */
    private static array $tags = [];

    /**
     * @var Cache|null
     */
    private static ?Cache $cache = null;

    public function __construct(LiquidCompiler|null $source = null)
    {
        if ($source instanceof LiquidCompiler) {
            $this->compiler = $source;
            $this->fileSystem = $source;
        }
    }

    /**
     * Set the cache backend
     */
    public static function setCache(Cache|array|null $cache): void
    {
        if (is_array($cache)) {
            if (isset($cache['cache']) && class_exists($classname = self::CLASS_PREFIX . ucwords($cache['cache']))) {
                self::$cache = new $classname($cache);
            } else {
                throw new CacheException('Invalid cache options!');
            }
        } elseif ($cache instanceof Cache) {
            self::$cache = $cache;
        } else {
            self::$cache = null;
        }
    }

    public static function getCache(): ?Cache
    {
        return self::$cache;
    }

    public function getRoot(): ?Document
    {
        return $this->root;
    }

    public static function registerTag(string $name, string $class): void
    {
        self::$tags[$name] = $class;
    }

    public static function getTags(): array
    {
        return self::$tags;
    }

    public function registerFilter(string $filter, ?callable $callback = null): void
    {
        if ($callback) {
            $this->filters[] = [$filter, $callback];
        } else {
            $this->filters[] = $filter;
        }
    }

    public function setTickFunction(callable $tickFunction): void
    {
        $this->tickFunction = $tickFunction;
    }

    public function parse(string $source): static
    {
        if (!self::$cache) {
            return $this->parseAlways($source);
        }

        $hash = md5($source);
        $this->root = self::$cache->read($hash);

        if ($this->root === false || $this->root->hasIncludes()) {
            $this->parseAlways($source);
            self::$cache->write($hash, $this->root);
        }

        return $this;
    }

    private function parseAlways(string $source): static
    {
        $tokens = $this->tokenize($source);

        $compilerOrFs = $this->compiler ?: $this->fileSystem;

        $this->root = new Document(null, $tokens, $compilerOrFs);

        return $this;
    }

    public function parseFile(string $templatePath): static
    {
        if (!$this->fileSystem) {
            throw new MissingFilesystemException("Could not load template without a file system or compiler");
        }

        if (!method_exists($this->fileSystem, 'readTemplateFile')) {
            throw new MissingFilesystemException("File system does not support reading templates");
        }

        $source = $this->fileSystem->readTemplateFile($templatePath);

        return $this->parse($source);
    }

    public function render(array $assigns = [], array|string|null $filters = null, array $registers = []): string
    {
        $context = new Context($assigns, $registers);

        if ($this->tickFunction) {
            $context->setTickFunction($this->tickFunction);
        }

        if ($filters !== null) {
            if (is_array($filters)) {
                $this->filters = array_merge($this->filters, $filters);
            } else {
                $this->filters[] = $filters;
            }
        }

        foreach ($this->filters as $filter) {
            if (is_array($filter)) {
                $context->addFilters(...$filter);
            } else {
                $context->addFilters($filter);
            }
        }

        return $this->root->render($context);
    }
}
