<?php

declare(strict_types=1);

namespace Liquid\Storage;

use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\Manager;
use InvalidArgumentException;

/**
 * CompiledStoreManager manages different storage drivers for compiled Liquid templates
 *
 * This class follows <PERSON><PERSON>'s Manager pattern, allowing different storage
 * backends (file, database, Redis, etc.) to be used for storing compiled templates.
 */
class CompiledStoreManager extends Manager
{
    /**
     * The application instance
     */
    protected Application $app;

    /**
     * Create a new manager instance
     */
    public function __construct(Application $app)
    {
        $this->app = $app;
    }

    /**
     * Get the default driver name
     */
    public function getDefaultDriver(): string
    {
        $config = $this->app['config']['liquid.compiled_store'];

        // Support old string format for backward compatibility
        if (is_string($config)) {
            return $config;
        }

        // New array format
        if (is_array($config) && isset($config['default'])) {
            return $config['default'];
        }

        return 'file';
    }

    /**
     * Get a driver instance with namespace propagation
     */
    public function driver($driver = null): CompiledStoreInterface
    {
        $driver = $driver ?: $this->getDefaultDriver();
        $instance = parent::driver($driver);

        // Set namespace for multi-tenant separation
        if ($instance instanceof CompiledStoreInterface) {
            $namespace = $this->generateNamespace();
            $instance->setNamespace($namespace);
        }

        return $instance;
    }

    /**
     * Get a specific driver by name (alias for driver method)
     *
     * @param string $name Driver name
     * @return CompiledStoreInterface
     */
    public function store(string $name): CompiledStoreInterface
    {
        return $this->driver($name);
    }

    /**
     * Get all available driver names
     *
     * @return array
     */
    public function getAvailableDrivers(): array
    {
        $config = $this->app['config']['liquid.compiled_store'];

        if (is_string($config)) {
            return [$config];
        }

        if (is_array($config) && isset($config['drivers'])) {
            return array_keys($config['drivers']);
        }

        return ['file']; // default fallback
    }

    /**
     * Check if a driver is available
     *
     * @param string $driver Driver name
     * @return bool
     */
    public function hasDriver(string $driver): bool
    {
        return in_array($driver, $this->getAvailableDrivers(), true);
    }

    /**
     * Create a database driver instance (public factory method)
     *
     * @param array $config Driver configuration
     * @return CompiledStoreInterface
     */
    public function createFileDriver(array $config = []): CompiledStoreInterface
    {
        $defaultConfig = $this->getDriverConfig('file');
        $config = array_merge($defaultConfig, $config);

        return new Stores\FileCompiledStore($config, $this->app['files']);
    }

    public function createDatabaseDriver(array $config = []): CompiledStoreInterface
    {
        $defaultConfig = $this->getDriverConfig('database');
        $config = array_merge($defaultConfig, $config);

        $connection = $this->app['db']->connection($config['connection'] ?? null);

        return new Stores\DatabaseCompiledStore($config, $connection);
    }

    /**
     * Extend the manager with a custom driver
     */
    public function extend($driver, \Closure $callback)
    {
        $this->customCreators[$driver] = $callback->bindTo($this, $this);

        return $this;
    }

    /**
     * Set namespace for all managed drivers
     */
    public function setNamespace(string $namespace): void
    {
        foreach ($this->drivers as $driver) {
            if ($driver instanceof CompiledStoreInterface) {
                $driver->setNamespace($namespace);
            }
        }
    }

    /**
     * Flush all compiled templates across all drivers
     *
     * @return bool
     */
    public function flushAll(): bool
    {
        $success = true;

        foreach ($this->getAvailableDrivers() as $driverName) {
            try {
                $driver = $this->driver($driverName);
                $success = $driver->flush() && $success;
            } catch (\Exception $e) {
                // Log error but continue with other drivers
                \Log::error("Failed to flush driver [{$driverName}]: " . $e->getMessage());
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Get driver statistics (if supported)
     *
     * @param string|null $driver Driver name or null for default
     * @return array
     */
    public function getStats(?string $driver = null): array
    {
        $instance = $this->driver($driver);

        if (method_exists($instance, 'getStats')) {
            return $instance->getStats();
        }

        return [
            'driver' => $driver ?: $this->getDefaultDriver(),
            'namespace' => $instance->getNamespace(),
            'stats_supported' => false,
        ];
    }

    // =======================================================================
    // Protected methods (internal implementation)
    // =======================================================================

    /**
     * Create a file driver instance (internal factory)
     */
    protected function createFileDriverInternal(): CompiledStoreInterface
    {
        return $this->createFileDriver();
    }

    /**
     * Create a database driver instance (internal factory)
     */
    protected function createDatabaseDriverInternal(): CompiledStoreInterface
    {
        return $this->createDatabaseDriver();
    }

    /**
     * Get configuration for a specific driver
     */
    protected function getDriverConfig(string $driver): array
    {
        $config = $this->app['config']['liquid.compiled_store'];

        // Support old string format
        if (is_string($config)) {
            return [];
        }

        // New array format
        if (is_array($config) && isset($config['drivers'][$driver])) {
            return $config['drivers'][$driver];
        }

        return [];
    }

    /**
     * Generate namespace for multi-tenant separation
     */
    protected function generateNamespace(): string
    {
        $namespace = app_namespace();

        if ($namespace === 'site') {
            // For site namespace, use template name
            return 'site_' . (site('template') ?? 'default');
        }

        return $namespace;
    }

    /**
     * Call a custom driver creator
     */
    protected function callCustomCreator($driver)
    {
        $config = $this->getDriverConfig($driver);

        return $this->customCreators[$driver]($this->app, $config);
    }

    /**
     * Create a driver using the appropriate factory method
     */
    protected function createDriver($driver)
    {
        // First, check if it's a built-in driver
        $method = 'create' . ucfirst($driver) . 'DriverInternal';

        if (method_exists($this, $method)) {
            return $this->$method();
        }

        // Check for custom drivers
        if (isset($this->customCreators[$driver])) {
            return $this->callCustomCreator($driver);
        }

        throw new InvalidArgumentException("Driver [{$driver}] is not supported.");
    }
}
