<?php

declare(strict_types=1);

namespace Liquid\Storage\Stores;

use Illuminate\Database\Connection;
use Illuminate\Support\Facades\Log;
use Liquid\Storage\CompiledStoreInterface;
use Liquid\Storage\AbstractCompiledStore;

/**
 * Database-based storage driver for compiled Liquid templates
 */
class DatabaseCompiledStore extends AbstractCompiledStore implements CompiledStoreInterface
{
    /**
     * Database connection instance
     */
    protected Connection $connection;

    /**
     * Database table name
     */
    protected string $table;

    /**
     * Cache for compiled templates
     */
    protected array $cache = [];

    /**
     * Maximum cache size
     */
    protected int $maxCacheSize;

    /**
     * Create a new database compiled store
     *
     * @param array $config Configuration array
     * @param Connection $connection Database connection
     */
    public function __construct(array $config, Connection $connection)
    {
        parent::__construct($config);
        
        $this->connection = $connection;
        $this->table = $this->config['table'] ?? 'liquid_compiled_templates';
        $this->maxCacheSize = $this->config['cache_size'] ?? 100;
        
        $this->ensureTableExists();
    }

    /**
     * Store compiled template content
     *
     * @param string $key The unique key for the compiled template
     * @param string $content The compiled template content
     * @return bool True if stored successfully, false otherwise
     */
    public function store(string $key, string $content): bool
    {
        try {
            $result = $this->connection->table($this->table)
                ->updateOrInsert(
                    ['key' => $key],
                    [
                        'content' => $content,
                        'compiled_at' => now(),
                        'updated_at' => now(),
                    ]
                );
            
            if ($result) {
                // Update cache
                $this->updateCache($key, $content);
                $this->debug("Stored compiled template in database", ['key' => $key, 'table' => $this->table]);
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('Failed to store compiled template in database', [
                'key' => $key,
                'error' => $e->getMessage(),
                'driver' => 'database',
                'table' => $this->table
            ]);
            return false;
        }
    }

    /**
     * Retrieve compiled template content
     *
     * @param string $key The unique key for the compiled template
     * @return string|null The compiled template content, null if not found
     */
    public function get(string $key): ?string
    {
        // Check cache first
        if (isset($this->cache[$key])) {
            $this->debug("Retrieved compiled template from cache", ['key' => $key]);
            return $this->cache[$key];
        }
        
        try {
            $record = $this->connection->table($this->table)
                ->where('key', $key)
                ->first(['content']);
            
            if ($record) {
                $content = $record->content;
                $this->updateCache($key, $content);
                $this->debug("Retrieved compiled template from database", ['key' => $key, 'table' => $this->table]);
                return $content;
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('Failed to retrieve compiled template from database', [
                'key' => $key,
                'error' => $e->getMessage(),
                'driver' => 'database',
                'table' => $this->table
            ]);
            return null;
        }
    }

    /**
     * Check if compiled template exists
     *
     * @param string $key The unique key for the compiled template
     * @return bool True if exists, false otherwise
     */
    public function exists(string $key): bool
    {
        // Check cache first
        if (isset($this->cache[$key])) {
            return true;
        }
        
        try {
            $exists = $this->connection->table($this->table)
                ->where('key', $key)
                ->exists();
            
            return $exists;
        } catch (\Exception $e) {
            Log::error('Failed to check if compiled template exists in database', [
                'key' => $key,
                'error' => $e->getMessage(),
                'driver' => 'database',
                'table' => $this->table
            ]);
            return false;
        }
    }

    /**
     * Remove compiled template
     *
     * @param string $key The unique key for the compiled template
     * @return bool True if removed successfully, false otherwise
     */
    public function forget(string $key): bool
    {
        try {
            $result = $this->connection->table($this->table)
                ->where('key', $key)
                ->delete();
            
            if ($result > 0) {
                // Remove from cache
                unset($this->cache[$key]);
                $this->debug("Removed compiled template from database", ['key' => $key, 'table' => $this->table]);
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('Failed to remove compiled template from database', [
                'key' => $key,
                'error' => $e->getMessage(),
                'driver' => 'database',
                'table' => $this->table
            ]);
            return false;
        }
    }

    /**
     * Clear all compiled templates
     *
     * @return bool True if cleared successfully, false otherwise
     */
    public function flush(): bool
    {
        try {
            $this->connection->table($this->table)->truncate();
            
            // Clear cache
            $this->cache = [];
            
            $this->debug("Flushed all compiled templates from database", ['table' => $this->table]);
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to flush compiled templates from database', [
                'error' => $e->getMessage(),
                'driver' => 'database',
                'table' => $this->table
            ]);
            return false;
        }
    }

    /**
     * Get the last modified time of compiled template
     *
     * @param string $key The unique key for the compiled template
     * @return int|null Unix timestamp of last modification, null if not found
     */
    public function lastModified(string $key): ?int
    {
        try {
            $record = $this->connection->table($this->table)
                ->where('key', $key)
                ->first(['compiled_at']);
            
            if ($record && $record->compiled_at) {
                return strtotime($record->compiled_at);
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('Failed to get last modified time for compiled template from database', [
                'key' => $key,
                'error' => $e->getMessage(),
                'driver' => 'database',
                'table' => $this->table
            ]);
            return null;
        }
    }

    /**
     * Get database table name
     *
     * @return string Table name
     */
    public function getTable(): string
    {
        return $this->table;
    }

    /**
     * Get database connection
     *
     * @return Connection Database connection
     */
    public function getConnection(): Connection
    {
        return $this->connection;
    }

    /**
     * Clean up old compiled templates
     *
     * @param int $days Number of days to keep templates
     * @return int Number of deleted records
     */
    public function cleanup(int $days = 30): int
    {
        try {
            $count = $this->connection->table($this->table)
                ->where('compiled_at', '<', now()->subDays($days))
                ->delete();
            
            $this->debug("Cleaned up old compiled templates", [
                'table' => $this->table,
                'days' => $days,
                'deleted' => $count
            ]);
            
            return $count;
        } catch (\Exception $e) {
            Log::error('Failed to cleanup old compiled templates', [
                'error' => $e->getMessage(),
                'driver' => 'database',
                'table' => $this->table,
                'days' => $days
            ]);
            return 0;
        }
    }

    /**
     * Update cache with compiled template
     *
     * @param string $key Template key
     * @param string $content Template content
     * @return void
     */
    protected function updateCache(string $key, string $content): void
    {
        // Implement simple LRU cache
        if (count($this->cache) >= $this->maxCacheSize) {
            // Remove first (oldest) item
            $firstKey = array_key_first($this->cache);
            unset($this->cache[$firstKey]);
        }
        
        $this->cache[$key] = $content;
    }

    /**
     * Ensure the database table exists
     *
     * @return void
     */
    protected function ensureTableExists(): void
    {
        if (!$this->connection->getSchemaBuilder()->hasTable($this->table)) {
            $this->debug("Creating compiled templates table", ['table' => $this->table]);
            
            $this->connection->getSchemaBuilder()->create($this->table, function ($table) {
                $table->id();
                $table->string('key')->unique();
                $table->longText('content');
                $table->timestamp('compiled_at');
                $table->timestamps();
                
                $table->index(['key', 'compiled_at']);
            });
        }
    }
} 