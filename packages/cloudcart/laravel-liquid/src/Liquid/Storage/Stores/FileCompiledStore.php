<?php

declare(strict_types=1);

namespace Liquid\Storage\Stores;

use Illuminate\Filesystem\Filesystem;
use Illuminate\Support\Facades\Log;
use Liquid\Storage\CompiledStoreInterface;
use Liquid\Storage\AbstractCompiledStore;

/**
 * File-based storage driver for compiled Liquid templates
 */
class FileCompiledStore extends AbstractCompiledStore implements CompiledStoreInterface
{
    /**
     * The filesystem instance
     */
    protected Filesystem $files;

    /**
     * Storage path for compiled templates
     */
    protected string $path;

    /**
     * Create a new file compiled store
     *
     * @param array $config Configuration array
     * @param Filesystem $files Filesystem instance
     */
    public function __construct(array $config, Filesystem $files)
    {
        parent::__construct($config);
        
        $this->files = $files;
        $this->path = $this->config['path'] ?? $this->getDefaultPath();
        
        $this->ensureDirectoryExists();
    }

    /**
     * Store compiled template content
     *
     * @param string $key The unique key for the compiled template
     * @param string $content The compiled template content
     * @return bool True if stored successfully, false otherwise
     */
    public function store(string $key, string $content): bool
    {
        try {
            $filePath = $this->getFilePath($key);
            
            // Ensure directory exists
            $this->ensureDirectoryExists();
            
            $result = $this->files->put($filePath, $content);
            
            if ($result !== false) {
                $this->debug("Stored compiled template", ['key' => $key, 'path' => $filePath]);
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('Failed to store compiled template', [
                'key' => $key,
                'error' => $e->getMessage(),
                'driver' => 'file'
            ]);
            return false;
        }
    }

    /**
     * Retrieve compiled template content
     *
     * @param string $key The unique key for the compiled template
     * @return string|null The compiled template content, null if not found
     */
    public function get(string $key): ?string
    {
        try {
            $filePath = $this->getFilePath($key);
            
            if ($this->files->exists($filePath)) {
                $content = $this->files->get($filePath);
                $this->debug("Retrieved compiled template", ['key' => $key, 'path' => $filePath]);
                return $content;
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('Failed to retrieve compiled template', [
                'key' => $key,
                'error' => $e->getMessage(),
                'driver' => 'file'
            ]);
            return null;
        }
    }

    /**
     * Check if compiled template exists
     *
     * @param string $key The unique key for the compiled template
     * @return bool True if exists, false otherwise
     */
    public function exists(string $key): bool
    {
        try {
            $filePath = $this->getFilePath($key);
            return $this->files->exists($filePath);
        } catch (\Exception $e) {
            Log::error('Failed to check if compiled template exists', [
                'key' => $key,
                'error' => $e->getMessage(),
                'driver' => 'file'
            ]);
            return false;
        }
    }

    /**
     * Remove compiled template
     *
     * @param string $key The unique key for the compiled template
     * @return bool True if removed successfully, false otherwise
     */
    public function forget(string $key): bool
    {
        try {
            $filePath = $this->getFilePath($key);
            
            if ($this->files->exists($filePath)) {
                $result = $this->files->delete($filePath);
                
                if ($result) {
                    $this->debug("Removed compiled template", ['key' => $key, 'path' => $filePath]);
                    return true;
                }
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('Failed to remove compiled template', [
                'key' => $key,
                'error' => $e->getMessage(),
                'driver' => 'file'
            ]);
            return false;
        }
    }

    /**
     * Clear all compiled templates
     *
     * @return bool True if cleared successfully, false otherwise
     */
    public function flush(): bool
    {
        try {
            if ($this->files->isDirectory($this->path)) {
                $this->files->cleanDirectory($this->path);
                $this->debug("Flushed all compiled templates", ['path' => $this->path]);
                return true;
            }
            
            return true; // Directory doesn't exist, consider it flushed
        } catch (\Exception $e) {
            Log::error('Failed to flush compiled templates', [
                'error' => $e->getMessage(),
                'driver' => 'file',
                'path' => $this->path
            ]);
            return false;
        }
    }

    /**
     * Get the last modified time of compiled template
     *
     * @param string $key The unique key for the compiled template
     * @return int|null Unix timestamp of last modification, null if not found
     */
    public function lastModified(string $key): ?int
    {
        try {
            $filePath = $this->getFilePath($key);
            
            if ($this->files->exists($filePath)) {
                return $this->files->lastModified($filePath);
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('Failed to get last modified time for compiled template', [
                'key' => $key,
                'error' => $e->getMessage(),
                'driver' => 'file'
            ]);
            return null;
        }
    }

    /**
     * Set the storage path
     *
     * @param string $path New storage path
     * @return self
     */
    public function setPath(string $path): self
    {
        $this->path = $path;
        $this->ensureDirectoryExists();
        return $this;
    }

    /**
     * Get the storage path
     *
     * @return string
     */
    public function getPath(): string
    {
        return $this->path;
    }

    /**
     * Get file path for a given key
     *
     * @param string $key Storage key
     * @return string Full file path
     */
    protected function getFilePath(string $key): string
    {
        // Ensure safe file name
        $fileName = $this->sanitizeFileName($key) . '.php';
        
        return $this->path . DIRECTORY_SEPARATOR . $fileName;
    }

    /**
     * Sanitize file name for safe storage
     *
     * @param string $fileName Original file name
     * @return string Sanitized file name
     */
    protected function sanitizeFileName(string $fileName): string
    {
        // Replace directory separators and other unsafe characters
        $safe = str_replace(['/', '\\', '..', '.'], '_', $fileName);
        
        // Limit length to avoid filesystem issues
        if (strlen($safe) > 200) {
            $safe = substr($safe, 0, 150) . '_' . md5($fileName);
        }
        
        return $safe;
    }

    /**
     * Get default storage path
     *
     * @return string Default path
     */
    protected function getDefaultPath(): string
    {
        return storage_path('framework/views/compiled/liquid');
    }

    /**
     * Ensure storage directory exists
     *
     * @return void
     */
    protected function ensureDirectoryExists(): void
    {
        if (!$this->files->isDirectory($this->path)) {
            $this->files->makeDirectory($this->path, 0755, true);
        }
    }
} 