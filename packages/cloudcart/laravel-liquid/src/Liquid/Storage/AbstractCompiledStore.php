<?php

declare(strict_types=1);

namespace Liquid\Storage;

use Illuminate\Support\Facades\Log;
use Liquid\Storage\CompiledStoreInterface;

/**
 * Abstract base class for compiled template storage drivers
 */
abstract class AbstractCompiledStore implements CompiledStoreInterface
{
    /**
     * Configuration for this store
     */
    protected array $config;

    /**
     * Namespace for multi-tenant separation
     */
    protected string $namespace = '';

    /**
     * Debug mode flag
     */
    protected bool $debug;

    /**
     * Create a new compiled store instance
     *
     * @param array $config Configuration array
     */
    public function __construct(array $config)
    {
        $this->config = $config;
        $this->debug = $config['debug'] ?? false;
    }

    /**
     * Generate a unique storage key for a template
     *
     * @param string $templateName Template name/path
     * @param array $context Additional context for key generation
     * @return string Unique storage key
     */
    public function generateKey(string $templateName, array $context = []): string
    {
        // Start with template name
        $key = $templateName;

        // Add namespace for multi-tenant separation
        if (!empty($this->namespace)) {
            $key = $this->namespace . '/' . md5($key);
        }

        // Add context if provided
        if (!empty($context)) {
            $contextHash = md5(serialize($context));
            $key .= '_' . $contextHash;
        }

        // Ensure consistent key format
        $key = str_replace(['\\', '//', '..'], ['/', '/', ''], $key);
        $key = trim($key, '/');

        return $key;
    }

    /**
     * Set the namespace for multi-tenant separation
     *
     * @param string $namespace Namespace identifier
     * @return self
     */
    public function setNamespace(string $namespace): self
    {
        $this->namespace = $namespace;
        return $this;
    }

    /**
     * Get the current namespace
     *
     * @return string Current namespace
     */
    public function getNamespace(): string
    {
        return $this->namespace;
    }

    /**
     * Log debug information if debug mode is enabled
     *
     * @param string $message Debug message
     * @param array $context Additional context
     * @return void
     */
    protected function debug(string $message, array $context = []): void
    {
        if ($this->debug) {
            Log::debug($message, array_merge($context, [
                'driver' => class_basename(static::class),
                'namespace' => $this->namespace,
            ]));
        }
    }

    /**
     * Get configuration value
     *
     * @param string $key Configuration key
     * @param mixed $default Default value if key not found
     * @return mixed Configuration value
     */
    protected function config(string $key, mixed $default = null): mixed
    {
        return $this->config[$key] ?? $default;
    }
}
