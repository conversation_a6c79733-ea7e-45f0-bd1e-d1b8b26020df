<?php

declare(strict_types=1);

namespace Liquid\Storage;

/**
 * Interface for compiled template storage drivers
 */
interface CompiledStoreInterface
{
    /**
     * Store compiled template content
     *
     * @param string $key The unique key for the compiled template
     * @param string $content The compiled template content
     * @return bool True if stored successfully, false otherwise
     */
    public function store(string $key, string $content): bool;

    /**
     * Retrieve compiled template content
     *
     * @param string $key The unique key for the compiled template
     * @return string|null The compiled template content, null if not found
     */
    public function get(string $key): ?string;

    /**
     * Check if compiled template exists
     *
     * @param string $key The unique key for the compiled template
     * @return bool True if exists, false otherwise
     */
    public function exists(string $key): bool;

    /**
     * Remove compiled template
     *
     * @param string $key The unique key for the compiled template
     * @return bool True if removed successfully, false otherwise
     */
    public function forget(string $key): bool;

    /**
     * Clear all compiled templates
     *
     * @return bool True if cleared successfully, false otherwise
     */
    public function flush(): bool;

    /**
     * Get the last modified time of compiled template
     *
     * @param string $key The unique key for the compiled template
     * @return int|null Unix timestamp of last modification, null if not found
     */
    public function lastModified(string $key): ?int;

    /**
     * Generate a unique storage key for a template
     *
     * @param string $templateName Template name/path
     * @param array $context Additional context for key generation
     * @return string Unique storage key
     */
    public function generateKey(string $templateName, array $context = []): string;

    /**
     * Set the namespace for multi-tenant separation
     *
     * @param string $namespace Namespace identifier
     * @return self
     */
    public function setNamespace(string $namespace): self;

    /**
     * Get the current namespace
     *
     * @return string Current namespace
     */
    public function getNamespace(): string;
}
