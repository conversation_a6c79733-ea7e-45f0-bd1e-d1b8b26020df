<?php

declare(strict_types=1);

namespace Liquid\Traits;

use Liquid\Token;

trait TokenizeTrait
{

    /**
     * Tokenizes the given source string
     *
     * @param string $source
     *
     * @return array|Token[]
     */
    public function tokenize(string $source): array
    {
        // strip out any `{% # … %}` linter-directives (even multiline)
        $source = preg_replace('!\{\%\s*\#.*?\%\}!s', '', $source);

        if ($source === '') {
            return [];
        }

        // if you’re on the “new” Liquid you can just reuse its built-in regex
        if (method_exists(\Liquid\Liquid::class, 'get')) {
            $regexp = \Liquid\Liquid::get('TOKENIZATION_REGEXP');
        } else {
            // Enhanced regex to handle comment blocks properly
            // First handle comment blocks as complete units, then other tags
            $source = $this->preprocessCommentBlocks($source);

            // fallback: literally split on any {% ... %} or {{ ... }}
            $regexp = '/(\{\%[\s\S]+?\%\}|\{\{[\s\S]+?\}\})/';
        }

        return preg_split(
            $regexp,
            $source,
            -1,
            PREG_SPLIT_NO_EMPTY | PREG_SPLIT_DELIM_CAPTURE
        );
    }

    /**
     * Preprocess comment blocks to handle them as complete units
     * This prevents the tokenizer from splitting multiline comments incorrectly
     *
     * @param string $source
     * @return string
     */
    private function preprocessCommentBlocks(string $source): string
    {
        // Handle comment blocks with proper nesting support
        // Match {% comment %} ... {% endcomment %} including variations with dashes
        $pattern = '/(\{\%\s*-?\s*comment\s*-?\s*\%\})(.*?)(\{\%\s*-?\s*endcomment\s*-?\s*\%\})/s';

        return preg_replace_callback($pattern, function ($matches) {
            $startTag = $matches[1];
            $content = $matches[2];
            $endTag = $matches[3];

            // Replace any {% or {{ inside the comment content with placeholders
            // to prevent them from being tokenized separately
            $content = str_replace(['{%', '{{'], ['__LIQUID_TAG_START__', '__LIQUID_VAR_START__'], $content);
            $content = str_replace(['%}', '}}'], ['__LIQUID_TAG_END__', '__LIQUID_VAR_END__'], $content);

            return $startTag . $content . $endTag;
        }, $source);
    }


}
