# ForLoopDrop and TablerowLoopDrop Implementation

## Overview

This document describes the implementation of `ForLoopDrop` and `TablerowLoopDrop` classes that provide enhanced loop functionality for Liquid templates, including support for nested loops and dynamic property access.

## ForLoopDrop Class

### Location
`packages/cloudcart/laravel-liquid/src/Liquid/Drop/ForLoopDrop.php`

### Purpose
The `ForLoopDrop` class represents the `forloop` object available inside `{% for %}` tags in Liquid templates. It provides access to loop state information and supports nested loops through the `parentloop` property.

### Features
- **Dynamic Property Access**: Uses `beforeMethod()` to handle property access
- **Nested Loop Support**: Maintains reference to parent loop via `parentloop` property
- **Shopify Compatibility**: Implements all standard Shopify forloop properties
- **Type Safety**: Uses PHP 8+ features like readonly properties and match expressions

### Available Properties
- `first`: bool - true if it's the first iteration
- `last`: bool - true if it's the last iteration  
- `index`: int - current iteration number (1-based)
- `index0`: int - current iteration number (0-based)
- `rindex`: int - remaining iterations (including current)
- `rindex0`: int - remaining iterations (0-based)
- `length`: int - total number of iterations
- `parentloop`: ForLoopDrop|null - parent loop for nested loops
- `name`: string - loop name identifier

### Usage Example
```liquid
{% for category in categories %}
  <h2>{{ category.name }} ({{ forloop.index }}/{{ forloop.length }})</h2>
  {% if forloop.first %}<div class="first-category">{% endif %}
  
  {% for product in category.products %}
    <div class="product">
      {{ product.name }} 
      (Category: {{ forloop.parentloop.index }}, Product: {{ forloop.index }})
    </div>
  {% endfor %}
  
  {% if forloop.last %}</div>{% endif %}
{% endfor %}
```

## TablerowLoopDrop Class

### Location
`packages/cloudcart/laravel-liquid/src/Liquid/Drop/TablerowLoopDrop.php`

### Purpose
The `TablerowLoopDrop` class represents the `tablerowloop` object available inside `{% tablerow %}` tags. It provides table-specific loop information including row and column data.

### Features
- **Table-Specific Properties**: Includes row, column, and position information
- **Dynamic Calculations**: Automatically calculates row/column based on index and column count
- **Shopify Compatibility**: Implements all standard Shopify tablerowloop properties

### Available Properties
- `first`: bool - true if it's the first item
- `last`: bool - true if it's the last item
- `index`: int - current item number (1-based)
- `index0`: int - current item number (0-based)
- `rindex`: int - remaining items (including current)
- `rindex0`: int - remaining items (0-based)
- `length`: int - total number of items
- `col`: int - current column number (1-based)
- `col0`: int - current column number (0-based)
- `col_first`: bool - true if first column
- `col_last`: bool - true if last column
- `row`: int - current row number (1-based)
- `name`: string - collection name identifier
- `key`: int - current item index (alias for index0)

### Usage Example
```liquid
<table class="product-grid">
  {% tablerow product in products cols: 3 %}
    <td class="product-cell{% if tablerowloop.col_first %} first-col{% endif %}{% if tablerowloop.col_last %} last-col{% endif %}">
      <div class="product">
        <h4>{{ product.title }}</h4>
        <p>Row {{ tablerowloop.row }}, Col {{ tablerowloop.col }}</p>
        <small>Item {{ tablerowloop.index }} of {{ tablerowloop.length }}</small>
      </div>
    </td>
  {% endtablerow %}
</table>
```

## Integration with TagFor and TagTablerow

### TagFor Modifications
The `TagFor` class has been updated to:
1. Import the `ForLoopDrop` class
2. Create `ForLoopDrop` instances instead of arrays
3. Support parent loop detection for nested loops
4. Use `increment()` method for loop progression

### TagTablerow Modifications
The `TagTablerow` class has been updated to:
1. Import the `TablerowLoopDrop` class
2. Create `TablerowLoopDrop` instances with column information
3. Use `increment()` method for loop progression

## Benefits

### 1. Enhanced Nested Loop Support
- Parent loop access via `forloop.parentloop`
- Maintains proper loop hierarchy
- Enables complex template logic

### 2. Type Safety
- PHP 8+ readonly properties
- Strong typing for all parameters
- Match expressions for property access

### 3. Performance
- Efficient property access through `beforeMethod()`
- Minimal memory overhead
- Lazy calculation of derived properties

### 4. Shopify Compatibility
- Full compatibility with Shopify Liquid templates
- All standard properties implemented
- Consistent behavior with Shopify's implementation

## Migration Notes

### Backward Compatibility
The implementation maintains full backward compatibility. Existing templates will continue to work without modification as the Drop classes provide the same interface as the previous array-based implementation.

### Testing
Both classes have been thoroughly tested with:
- Basic property access
- Nested loop scenarios
- Table layout calculations
- Edge cases (empty collections, single items)

## Future Enhancements

### Potential Improvements
1. **Caching**: Add property caching for frequently accessed values
2. **Events**: Add hooks for loop start/end events
3. **Debugging**: Enhanced debugging information for complex nested loops
4. **Performance**: Further optimization for large collections

### Extension Points
The classes are designed to be easily extended:
- Additional properties can be added via `beforeMethod()`
- Custom loop types can inherit from base classes
- Integration with other Liquid features is straightforward
