# CloudCart Liquid Performance Targets

## Current Performance Baseline

### Rendering Performance
- **Template compilation time**: 150-300ms (average)
- **Simple template rendering**: 50-100ms
- **Complex template rendering**: 200-500ms
- **Memory usage per render**: 2-5MB
- **Cache hit rate**: 60-70%

### Issues Identified
- [ ] Inefficient tokenization regex
- [ ] Memory leaks in drop objects
- [ ] Lack of template compilation caching
- [ ] Suboptimal multiline comment handling
- [ ] No resource limits implementation
- [ ] Poor error handling performance

## Target Performance Metrics

### Primary Targets (Must Achieve)

#### Rendering Speed
- [ ] **50% improvement in overall rendering speed**
  - Current: 200-500ms → Target: 100-250ms
  - Simple templates: 50-100ms → Target: 25-50ms
  - Complex templates: 200-500ms → Target: 100-250ms

#### Memory Usage
- [ ] **30% reduction in memory consumption**
  - Current: 2-5MB per render → Target: 1.4-3.5MB per render
  - Peak memory usage: Reduce by 30%
  - Memory leak elimination: 0 leaks detected

#### Compilation Performance
- [ ] **Sub-100ms template compilation**
  - Current: 150-300ms → Target: <100ms
  - Cache hit scenarios: <10ms
  - Cold compilation: <100ms

#### Cache Efficiency
- [ ] **90% cache hit rate**
  - Current: 60-70% → Target: 90%+
  - Template compilation cache: 95%+
  - Drop result cache: 85%+

### Secondary Targets (Should Achieve)

#### Resource Management
- [ ] **Implement resource limits**
  - Render score limit: 10,000 operations
  - Assign score limit: 1,000 operations
  - Render timeout: 5 seconds maximum
  - Memory limit: 50MB per template

#### Error Handling
- [ ] **Improved error performance**
  - Error detection time: <5ms
  - Error recovery time: <10ms
  - Error logging overhead: <1ms

#### Concurrency
- [ ] **Better concurrent performance**
  - Support 100+ concurrent renders
  - No blocking operations
  - Thread-safe caching

### Stretch Targets (Nice to Have)

#### Advanced Features
- [ ] **Streaming rendering support**
  - First byte time: <50ms
  - Streaming chunk size: 8KB
  - Memory usage during streaming: <1MB

#### Development Experience
- [ ] **Faster development cycle**
  - Template reload time: <100ms
  - Debug information generation: <10ms
  - Error reporting: Real-time

## Performance Testing Strategy

### Benchmarking Framework

#### Test Scenarios
```php
// Performance test scenarios
$scenarios = [
    'simple_template' => [
        'template' => '{{ product.title }} - {{ product.price }}',
        'data' => ['product' => $simpleProduct],
        'iterations' => 1000,
        'target_time' => 25, // ms
    ],
    'complex_template' => [
        'template' => $complexEcommerceTemplate,
        'data' => $fullEcommerceData,
        'iterations' => 100,
        'target_time' => 100, // ms
    ],
    'loop_heavy_template' => [
        'template' => $productListTemplate,
        'data' => ['products' => $largeProductArray],
        'iterations' => 50,
        'target_time' => 200, // ms
    ],
];
```

#### Memory Testing
```php
// Memory usage testing
$memoryTests = [
    'baseline_memory' => [
        'description' => 'Memory usage without liquid',
        'target' => '<1MB',
    ],
    'simple_render_memory' => [
        'description' => 'Memory for simple template',
        'target' => '<2MB',
    ],
    'complex_render_memory' => [
        'description' => 'Memory for complex template',
        'target' => '<5MB',
    ],
    'memory_leak_test' => [
        'description' => 'Memory after 1000 renders',
        'target' => 'No increase',
    ],
];
```

### Performance Monitoring

#### Key Metrics to Track
- [ ] **Response Time Metrics**
  - Average response time
  - 95th percentile response time
  - 99th percentile response time
  - Maximum response time

- [ ] **Throughput Metrics**
  - Requests per second
  - Templates rendered per second
  - Cache operations per second

- [ ] **Resource Metrics**
  - CPU usage percentage
  - Memory usage (current/peak)
  - Disk I/O operations
  - Network I/O (if applicable)

- [ ] **Error Metrics**
  - Error rate percentage
  - Error types distribution
  - Error recovery time
  - Failed compilation rate

#### Monitoring Tools
```php
// Performance monitoring implementation
class LiquidPerformanceMonitor
{
    public function startRender(string $templateName): string
    {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage(true);
        return uniqid('render_');
    }
    
    public function endRender(string $renderId): array
    {
        return [
            'duration' => microtime(true) - $this->startTime,
            'memory_used' => memory_get_usage(true) - $this->startMemory,
            'peak_memory' => memory_get_peak_usage(true),
        ];
    }
}
```

## Optimization Strategies

### Template Compilation Optimization

#### Caching Strategy
- [ ] **Multi-level caching**
  - L1: In-memory cache (APCu)
  - L2: File-based cache
  - L3: Database cache (for multi-tenant)

- [ ] **Cache invalidation**
  - Template modification detection
  - Dependency tracking
  - Smart cache warming

#### Compilation Improvements
```php
// Optimized compilation process
class OptimizedLiquidCompiler extends LiquidCompiler
{
    public function compile(string $template): CompiledTemplate
    {
        // 1. Check cache first
        if ($cached = $this->getFromCache($template)) {
            return $cached;
        }
        
        // 2. Optimized tokenization
        $tokens = $this->optimizedTokenize($template);
        
        // 3. AST optimization
        $ast = $this->buildOptimizedAST($tokens);
        
        // 4. Code generation
        $compiled = $this->generateOptimizedCode($ast);
        
        // 5. Cache result
        $this->cacheResult($template, $compiled);
        
        return $compiled;
    }
}
```

### Runtime Performance Optimization

#### Drop Object Optimization
```php
// Optimized drop with caching
#[Cache]
class OptimizedCartDrop extends Drop
{
    private array $cache = [];
    
    #[Cache(ttl: 300)] // 5 minutes
    public function items(): array
    {
        return $this->cache['items'] ??= $this->loadItems();
    }
    
    #[Cache(ttl: 60)] // 1 minute
    public function total(): float
    {
        return $this->cache['total'] ??= $this->calculateTotal();
    }
}
```

#### Memory Management
- [ ] **Object pooling for frequently used objects**
- [ ] **Lazy loading for expensive operations**
- [ ] **Weak references for circular dependencies**
- [ ] **Memory cleanup after rendering**

### Database Query Optimization

#### N+1 Query Prevention
```php
// Optimized product loading
class ProductDrop extends Drop
{
    public static function preloadForCollection(Collection $products): void
    {
        // Preload all related data in batch
        $products->load(['variants', 'images', 'categories']);
    }
    
    #[Cache]
    public function variants(): array
    {
        // Already loaded, no additional query
        return $this->product->variants;
    }
}
```

## Performance Testing Schedule

### Development Phase Testing
- [ ] **Daily performance checks**
  - Run basic performance suite
  - Check for memory leaks
  - Validate cache efficiency

- [ ] **Weekly comprehensive testing**
  - Full benchmark suite
  - Memory profiling
  - Performance regression testing

### Pre-deployment Testing
- [ ] **Load testing**
  - Simulate production load
  - Test concurrent users
  - Stress test resource limits

- [ ] **Performance validation**
  - Verify all targets met
  - Compare with baseline
  - Document improvements

### Post-deployment Monitoring
- [ ] **Real-time monitoring**
  - Performance dashboards
  - Automated alerting
  - Trend analysis

- [ ] **Regular performance reviews**
  - Weekly performance reports
  - Monthly optimization reviews
  - Quarterly target reassessment

## Success Criteria

### Minimum Acceptable Performance
- [ ] No performance regression from current system
- [ ] All critical functionality working
- [ ] Memory usage within acceptable limits
- [ ] Error rates below 1%

### Target Performance Achievement
- [ ] 50% rendering speed improvement achieved
- [ ] 30% memory reduction achieved
- [ ] 90% cache hit rate achieved
- [ ] Sub-100ms compilation achieved

### Exceptional Performance
- [ ] 70%+ rendering speed improvement
- [ ] 50%+ memory reduction
- [ ] 95%+ cache hit rate
- [ ] Sub-50ms compilation

## Performance Regression Prevention

### Automated Testing
```php
// Performance regression test
class PerformanceRegressionTest extends TestCase
{
    public function testRenderingPerformance(): void
    {
        $baseline = $this->getBaselineMetrics();
        $current = $this->measureCurrentPerformance();
        
        $this->assertLessThan(
            $baseline['render_time'] * 1.1, // 10% tolerance
            $current['render_time'],
            'Rendering performance regression detected'
        );
    }
}
```

### Continuous Monitoring
- [ ] **Performance CI/CD integration**
- [ ] **Automated performance alerts**
- [ ] **Performance trend tracking**
- [ ] **Regular performance audits**
