# Laravel Liquid Tags Optimization - Execution Plan

## Current State Analysis ✅

### Core Tags Status (packages/cloudcart/laravel-liquid/src/Liquid/Tag/)

| Tag | Status | Shopify Features | Action Required |
|-----|--------|------------------|----------------|
| TagFor | ✅ Implemented | ✅ limit, offset, reversed, range, cols, rows | Minor improvements |
| TagIf | ✅ Implemented | ✅ contains, starts_with, ends_with, blank, empty, nil, present | Add missing operators |
| TagCase | ✅ Implemented | ✅ Basic functionality | Add Shopify filters |
| TagCycle | ✅ Implemented | ✅ group, name, reset, clear | Testing needed |
| TagAssign | ✅ Implemented | ✅ Basic functionality | No action needed |
| TagCapture | ✅ Implemented | ✅ Basic functionality | No action needed |
| TagComment | ✅ Implemented | ✅ Basic functionality | No action needed |
| TagRaw | ✅ Implemented | ✅ Basic functionality | No action needed |
| TagBreak | ✅ Implemented | ✅ Basic functionality | No action needed |
| TagContinue | ✅ Implemented | ✅ Basic functionality | No action needed |
| TagIncrement | ✅ Implemented | ✅ Basic functionality | No action needed |
| TagDecrement | ✅ Implemented | ✅ Basic functionality | No action needed |
| TagTablerow | ✅ Implemented | ⚠️ Partial | Add Shopify attributes |
| TagSection | ✅ Implemented | ⚠️ Partial | Add Shopify features |
| TagSchema | ✅ Implemented | ⚠️ Partial | Add Shopify features |
| TagStyle | ✅ Implemented | ⚠️ Partial | Add Shopify features |
| TagStylesheet | ✅ Implemented | ⚠️ Partial | Add Shopify features |
| TagForm | ✅ Implemented | ⚠️ Partial | Add Shopify features |
| TagPaginate | ✅ Implemented | ⚠️ Partial | Add Shopify features |
| TagLayout | ✅ Implemented | ⚠️ Partial | Add Shopify features |
| TagRender | ✅ Implemented | ⚠️ Partial | Add Shopify features |
| TagInclude | ✅ Implemented | ⚠️ Partial | Add Shopify features |

### Custom Tags Status (app/LiquidEngine/LiquidHelpers/Tags/)

| Tag | Status | Error Handling | Action Required |
|-----|--------|----------------|----------------|
| TagAuth | ✅ Implemented | ✅ Good | No action needed |
| TagGuest | ✅ Implemented | ✅ Good | No action needed |
| TagCall | ✅ Implemented | ⚠️ Basic | Improve error handling |
| TagRoute | ✅ Implemented | ⚠️ Basic | Improve error handling |
| TagGdpr | ✅ Implemented | ⚠️ Basic | Improve error handling |
| TagFunction | ✅ Implemented | ⚠️ Basic | Improve error handling |
| TagStyle | ✅ Implemented | ✅ Good | No action needed |
| TagCsrfToken | ✅ Implemented | ✅ Good | No action needed |
| TagGoogleReCaptcha | ✅ Implemented | ⚠️ Basic | Improve error handling |

### Regex Patterns Status

| Pattern Type | Status | Issues | Action Required |
|-------------|--------|---------|----------------|
| Base Pattern | ✅ Compatible | None | No action needed |
| Common Tag | ⚠️ Partial | Quote escaping improved | Verify implementation |
| AS Pattern | ✅ Compatible | None | No action needed |
| Function Tag | ✅ Updated | Variable validation improved | Verify implementation |

## Execution Plan

### Phase 1: Critical Updates (Week 1) 🔥

#### 1.1 TagIf Enhancements
- **Priority**: HIGH
- **Task**: Add missing Shopify operators
- **Missing operators**: `size`, `type`, `first`, `last`
- **Files**: `packages/cloudcart/laravel-liquid/src/Liquid/Tag/TagIf.php`
- **Estimated time**: 2 days

#### 1.2 TagTablerow Shopify Features
- **Priority**: HIGH  
- **Task**: Add Shopify-specific attributes
- **Missing attributes**: `limit`, `offset`, `cols`, `range`
- **Files**: `packages/cloudcart/laravel-liquid/src/Liquid/Tag/TagTablerow.php`
- **Estimated time**: 1 day

#### 1.3 Error Handling for Custom Tags
- **Priority**: HIGH
- **Task**: Improve error handling in custom tags
- **Files**: 
  - `app/LiquidEngine/LiquidHelpers/Tags/TagCall.php`
  - `app/LiquidEngine/LiquidHelpers/Tags/TagRoute.php`
  - `app/LiquidEngine/LiquidHelpers/Tags/TagGdpr.php`
  - `app/LiquidEngine/LiquidHelpers/Tags/TagFunction.php`
  - `app/LiquidEngine/LiquidHelpers/Tags/TagGoogleReCaptcha.php`
- **Estimated time**: 2 days

### Phase 2: Shopify Integration (Week 2) 🛠️

#### 2.1 TagForm Shopify Features
- **Priority**: MEDIUM
- **Task**: Add Shopify-specific form handling
- **Features**: Customer forms, contact forms, newsletter forms
- **Files**: `packages/cloudcart/laravel-liquid/src/Liquid/Tag/TagForm.php`
- **Estimated time**: 3 days

#### 2.2 TagPaginate Shopify Features  
- **Priority**: MEDIUM
- **Task**: Add Shopify-specific pagination
- **Features**: Collection pagination, search pagination
- **Files**: `packages/cloudcart/laravel-liquid/src/Liquid/Tag/TagPaginate.php`
- **Estimated time**: 2 days

#### 2.3 TagSection Shopify Features
- **Priority**: MEDIUM
- **Task**: Add Shopify-specific section handling
- **Features**: Section settings, section blocks
- **Files**: `packages/cloudcart/laravel-liquid/src/Liquid/Tag/TagSection.php`
- **Estimated time**: 2 days

### Phase 3: Advanced Features (Week 3) ⚡

#### 3.1 TagLayout Shopify Features
- **Priority**: LOW
- **Task**: Add Shopify-specific layout system
- **Features**: Theme layouts, layout inheritance
- **Files**: `packages/cloudcart/laravel-liquid/src/Liquid/Tag/TagLayout.php`
- **Estimated time**: 2 days

#### 3.2 TagRender & TagInclude Enhancements
- **Priority**: LOW
- **Task**: Add Shopify-specific rendering features
- **Features**: Snippet rendering, partial rendering
- **Files**: 
  - `packages/cloudcart/laravel-liquid/src/Liquid/Tag/TagRender.php`
  - `packages/cloudcart/laravel-liquid/src/Liquid/Tag/TagInclude.php`
- **Estimated time**: 3 days

### Phase 4: Testing & Documentation (Week 4) 📝

#### 4.1 Unit Tests
- **Priority**: HIGH
- **Task**: Create comprehensive test suite
- **Coverage**: All Shopify operators, filters, attributes
- **Files**: `tests/Unit/Liquid/Tags/`
- **Estimated time**: 3 days

#### 4.2 Integration Tests
- **Priority**: MEDIUM
- **Task**: Test form handling, pagination, sections
- **Files**: `tests/Integration/Liquid/`
- **Estimated time**: 2 days

#### 4.3 Documentation
- **Priority**: MEDIUM
- **Task**: Update documentation
- **Files**: 
  - `app/LiquidEngine/LIQUID_ARCHITECTURE.md`
  - `app/LiquidEngine/Documentation/`
- **Estimated time**: 2 days

## Detailed Task Breakdown

### Task 1.1: TagIf Missing Operators

**Current operators**: `==`, `!=`, `>=`, `<=`, `>`, `<`, `contains`, `starts_with`, `ends_with`, `has_key`, `blank`, `empty`, `nil`, `present`

**Missing Shopify operators**:
- `size` - Get size of arrays/strings
- `type` - Get type of variable  
- `first` - Get first element
- `last` - Get last element

**Implementation**:
```php
// Add to $conditional_operators array
protected $conditional_operators = [
    '==', '!=', '>=', '<=', '>', '<', 'contains', 'starts_with', 'ends_with', 
    'has_key', 'blank', 'empty', 'nil', 'present', 'size', 'type', 'first', 'last'
];

// Add to interpretCondition method
case 'size':
    return is_array($left) ? count($left) : (is_string($left) ? strlen($left) : 0);
case 'type':
    return gettype($left);
case 'first':
    return is_array($left) ? reset($left) : (is_string($left) ? substr($left, 0, 1) : null);
case 'last':
    return is_array($left) ? end($left) : (is_string($left) ? substr($left, -1) : null);
```

### Task 1.2: TagTablerow Shopify Attributes

**Current implementation**: Basic tablerow functionality

**Missing Shopify attributes**:
- `limit` - Limit number of items
- `offset` - Skip items from beginning
- `cols` - Number of columns
- `range` - Range of items

**Implementation**: Similar to TagFor attributes handling

### Task 1.3: Custom Tags Error Handling

**Current issues**:
- Basic try-catch blocks
- Generic error messages
- No proper error logging
- No graceful degradation

**Improvements needed**:
- Specific error messages
- Proper error logging using CloudCart's logging system
- Graceful degradation for missing data
- Input validation

## Success Criteria

### Phase 1 Success Criteria
- [ ] All missing TagIf operators implemented and tested
- [ ] TagTablerow has all Shopify attributes
- [ ] All custom tags have improved error handling
- [ ] No breaking changes to existing functionality

### Phase 2 Success Criteria  
- [ ] TagForm supports Shopify form types
- [ ] TagPaginate works with CloudCart collections
- [ ] TagSection supports basic Shopify section features
- [ ] All features are backward compatible

### Phase 3 Success Criteria
- [ ] TagLayout supports theme inheritance
- [ ] TagRender and TagInclude work with snippets
- [ ] Performance impact is minimal
- [ ] Documentation is updated

### Phase 4 Success Criteria
- [ ] Test coverage > 90%
- [ ] All integration tests pass
- [ ] Documentation is complete
- [ ] Migration guide is available

## Risk Assessment

### High Risk
- **Breaking existing templates**: Mitigation - extensive testing
- **Performance degradation**: Mitigation - performance monitoring
- **Regex pattern conflicts**: Mitigation - careful pattern testing

### Medium Risk  
- **Shopify compatibility issues**: Mitigation - reference implementation testing
- **Custom tag conflicts**: Mitigation - namespace isolation
- **Documentation gaps**: Mitigation - comprehensive documentation review

### Low Risk
- **Minor syntax differences**: Mitigation - backward compatibility layer
- **Edge case handling**: Mitigation - comprehensive test suite

## Next Steps

1. **Immediate**: Start with Phase 1 tasks
2. **Week 1**: Complete critical updates
3. **Week 2**: Begin Shopify integration
4. **Week 3**: Implement advanced features  
5. **Week 4**: Testing and documentation

## Resources Required

- **Developer time**: 4 weeks full-time
- **Testing environment**: CloudCart development instance
- **Documentation tools**: Markdown editors
- **Testing tools**: PHPUnit, Laravel testing suite
