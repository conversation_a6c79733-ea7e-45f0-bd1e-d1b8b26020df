# Liquid Compiler Architecture Analysis

## Current Architecture Overview

### 1. LiquidCompiler and JsonLiquidCompiler Registration

Based on the analysis of the codebase, the Liquid compilers are registered through the Laravel-Liquid package which CloudCart extends.

#### Key Findings:

**LiquidServiceProvider Location:** `app/LiquidEngine/Providers/LiquidServiceProvider.php`

```php
class LiquidServiceProvider extends BaseLiquidServiceProvider
{
    public function register(): void
    {
        parent::register(); // Calls base Laravel-Liquid service provider
        $this->app->alias('liquid.factory', 'liquid');
    }

    public function boot(): void
    {
        parent::boot(); // Registers core liquid functionality
        $this->registerCustomDatabaseDriver();
        $this->registerCustomFileDriver();
    }
}
```

**Base Laravel-Liquid Integration:**
- CloudCart extends the base `LiquidServiceProvider` from the Laravel-Liquid package
- The parent `register()` method handles the registration of `LiquidCompiler` and `JsonLiquidCompiler`
- The parent `boot()` method sets up the core liquid functionality

### 2. Current compiled_path Configuration

#### Configuration Location: `config/liquid.php`

```php
'compiled_store' => 'file',
```

#### Bootstrap Process: `app/Kernel/Bootstrappers/GuessSiteByHostAndBoot.php`

**Key Method:** `bootLiquidEngine()`

```php
if ($this->app->bound('liquid.compiler')) {
    if (!is_dir($compiledDir = $this->getCompiledDir('liquid'))) {
        @mkdir($compiledDir, 0777, true);
    }

    $this->app->make('liquid.compiler')
        ->setCompiledDir($compiledDir);
}
```

**Compiled Directory Generation:**
```php
private function getCompiledDir(string $engine): string
{
    return storage_path('framework/views/compiled/') . $engine . '/' . 
           (app_namespace() == 'site' ? site('template') : app_namespace()) . '/';
}
```

**Current Directory Structure:**
```
storage/framework/views/compiled/
├── liquid/
│   ├── {theme_name}/     (for site namespace)
│   ├── sitecp/          (for sitecp namespace)
│   ├── api2/            (for api2 namespace)
│   └── {namespace}/     (for other namespaces)
```

### 3. ViewFinderManager Pattern Study

**Current ViewFinder Implementation:** `app/LiquidEngine/Providers/LiquidServiceProvider.php`

CloudCart already implements a driver-based system for view finding:

```php
// Custom drivers registration
protected function registerCustomDatabaseDriver()
{
    $this->app->make('liquid.view.finder')->extend('custom_database', function (array $app, $config) {
        $connection = $app['db']->connection($config['connection'] ?? null);
        return new CustomDatabaseViewFinder($connection, $config['table']);
    });
}

protected function registerCustomFileDriver()
{
    $this->app->make('liquid.view.finder')->extend('custom_file', function (array $app, $config) {
        $paths = array_map(function ($path) {
            return sprintf($path, site('theme.key'));
        }, (array)($config['path'] ?? []));
        return new FileViewFinder($app['files'], $paths);
    });
}
```

**ViewFinder Configuration:** `config/liquid.php`

```php
'finder' => [
    'default' => env('LIQUID_VIEW_FINDER', 'file'),
    'drivers' => [
        'file' => [
            'driver' => 'file',
            'paths' => [
                resource_path('resources/themes/_global/global-theme')
            ],
        ],
        'database' => [
            'driver' => 'database',
            'table' => 'templates',
            'connection' => null,
        ],
        'custom_file' => [
            'driver' => 'custom_file',
            'path' => [
                resource_path('themes/%s'),
                resource_path('themes/_global/global-theme'),
            ],
        ],
        'custom_database' => [
            'driver' => 'custom_database',
            'table' => 'themes_templates',
            'connection' => 'themes',
        ],
    ]
],
```

### 4. compiled_path Usage Mapping

#### Primary Usage Locations:

1. **Bootstrap Process:**
   - `app/Kernel/Bootstrappers/GuessSiteByHostAndBoot.php:354` - Sets compiled directory via `setCompiledDir()`

2. **Configuration:**
   - `config/liquid.php:112` - Contains `compiled_store` configuration (currently only stores driver type)
   - `config/view.php:14` - Contains `VIEW_COMPILED_PATH` reference (for Laravel views, not Liquid)

3. **Service Provider Integration:**
   - Base Laravel-Liquid package handles the actual compiler registration
   - CloudCart's LiquidServiceProvider extends and customizes the base behavior

### 5. Key Observations

#### Current Limitations:
1. **Hardcoded File Storage:** Only file-based compiled template storage is supported
2. **Fixed Directory Structure:** Compiled path is determined by `getCompiledDir()` method
3. **No Driver System:** Unlike ViewFinder, there's no driver-based system for compiled storage
4. **Limited Configuration:** Only `compiled_store = 'file'` is configurable

#### Architectural Strengths:
1. **Existing Driver Pattern:** ViewFinder already demonstrates how to implement a driver system
2. **Service Provider Structure:** Well-organized service provider structure for extensions
3. **Multi-tenant Support:** Current system already handles namespace-based directory separation
4. **Bootstrap Integration:** Clear bootstrap process in `GuessSiteByHostAndBoot`

### 6. Proposed Architecture Direction

Based on the analysis, the CompiledStoreManager should follow the same pattern as the ViewFinderManager:

1. **Manager Class:** Similar to `liquid.view.finder` but for `liquid.compiled.store`
2. **Driver Registration:** Use `extend()` method pattern for adding new drivers
3. **Configuration:** Follow the same structure as the `finder` configuration
4. **Service Provider Integration:** Register in `LiquidServiceProvider::register()`
5. **Bootstrap Integration:** Modify `GuessSiteByHostAndBoot::bootLiquidEngine()`

### 7. Implementation Priority

Based on the current architecture:

1. **High Priority:** Create CompiledStoreManager following ViewFinder pattern
2. **Medium Priority:** Implement file and database drivers
3. **Low Priority:** Add additional drivers (Redis, S3, etc.)

This analysis provides the foundation for implementing the CompiledStoreManager system that will integrate seamlessly with CloudCart's existing Liquid architecture. 