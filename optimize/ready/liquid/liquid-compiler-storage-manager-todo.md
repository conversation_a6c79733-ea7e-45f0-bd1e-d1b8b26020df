# TODO: Liquid Compiler Storage Manager

## Goal
Create a manager for managing storage locations of compiled Liquid files, similar to ViewFinderManager, that allows different storage methods (files, database, etc.).

## Tasks

### 1. Current Architecture Analysis
- [x] Research the base Laravel-Liquid package and how LiquidCompiler and JsonLiquidCompiler are registered
- [x] Analyze current LiquidServiceProvider and how compiled_path is set
- [x] Study ViewFinderManager pattern for reference
- [x] Map all places where compiled_path is used

**Analysis completed and documented in `optimize/liquid-architecture-analysis.md`**

### 2. Create CompiledStoreManager
- [x] Create base `CompiledStoreManager` class
- [x] Define `CompiledStoreInterface` interface for different drivers
- [x] Create abstract `AbstractCompiledStore` class
- [x] Move files to Laravel-Liquid package location

**Core architecture completed. Files created in `packages/cloudcart/laravel-liquid/src/Liquid/Storage/`:**
- `Contracts/CompiledStoreInterface.php` - Interface defining the contract for storage drivers with methods: store(), get(), exists(), forget(), flush(), lastModified(), generateKey()
- `Stores/AbstractCompiledStore.php` - Abstract base class with common functionality including namespace support, debug logging, key generation and configuration management
- `CompiledStoreManager.php` - Manager class following Laravel's Manager pattern with extend() method for custom drivers, namespace support for multi-tenant separation, and driver configuration management

**All files properly created in Laravel-Liquid package with correct namespace structure.**

### 3. Implement Drivers
- [x] **FileCompiledStore** - for file storage (current behavior)
  - [x] Create the class
  - [x] Implement methods: `store()`, `get()`, `exists()`, `forget()`
  - [x] Directory and file management
  
- [x] **DatabaseCompiledStore** - for database storage
  - [x] Auto-create table schema on initialization
  - [x] Implement DatabaseCompiledStore class with full interface compliance
  - [x] Caching for better performance with LRU cache implementation
  - [x] Cleanup functionality for old compiled templates

**Both core drivers implemented with robust features:**
- `FileCompiledStore.php` - Full filesystem storage with safety features, namespace support, file sanitization, and error handling
- `DatabaseCompiledStore.php` - Complete database storage with auto-table creation, LRU cache, cleanup functionality, and comprehensive error handling

### 4. Configuration
- [x] Update `config/liquid.php` to add new `compiled_store` section
- [x] Define drivers and their configurations:
  ```php
  'compiled_store' => [
      'default' => env('LIQUID_COMPILED_STORE', 'file'),
      'drivers' => [
          'file' => [
              'driver' => 'file',
              'path' => storage_path('framework/views/compiled/liquid'),
              'debug' => env('APP_DEBUG', false),
          ],
          'database' => [
              'driver' => 'database',
              'table' => 'liquid_compiled_templates',
              'connection' => null,
              'cache_size' => 100,
              'debug' => env('APP_DEBUG', false),
          ],
          'redis' => [
              'driver' => 'redis',
              'connection' => 'default',
              'prefix' => 'liquid_compiled',
              'ttl' => 86400,
              'debug' => env('APP_DEBUG', false),
          ],
      ]
  ],
  ```

**Configuration completed with comprehensive driver support:**
- Updated both `config/liquid.php` (CloudCart) and `packages/cloudcart/laravel-liquid/config/liquid.php` (base package)
- Added environment variable support: `LIQUID_COMPILED_STORE` for driver selection
- Included debug configuration for development debugging
- File driver with configurable storage path
- Database driver with LRU cache size configuration  
- Redis driver template prepared for future implementation
- Full backward compatibility maintained with automatic string-to-array config conversion

### 5. LiquidServiceProvider Integration
- [x] Update `LiquidServiceProvider::register()` to register CompiledStoreManager
- [x] Replace hardcoded compiled_path with dynamic driver
- [x] Create factory methods for different drivers
- [x] Update LiquidCompiler and JsonLiquidCompiler registration

### 6. CompiledStoreManager Methods
- [x] `extend(string $driver, Closure $callback)` - for adding new drivers
- [x] `driver(string $name = null)` - for getting specific driver
- [x] `getDefaultDriver()` - for getting default driver
- [x] `createFileDriver(array $config)` - factory for file driver
- [x] `createDatabaseDriver(array $config)` - factory for database driver
- [x] **BONUS**: `store(string $name)` - alias for driver method
- [x] **BONUS**: `getAvailableDrivers()` - list all available drivers
- [x] **BONUS**: `hasDriver(string $driver)` - check if driver exists
- [x] **BONUS**: `flushAll()` - flush all drivers
- [x] **BONUS**: `getStats(?string $driver = null)` - get driver statistics

### 7. CompiledStoreInterface Interface
```php
interface CompiledStoreInterface
{
    public function store(string $key, string $content): bool;
    public function get(string $key): ?string;
    public function exists(string $key): bool;
    public function forget(string $key): bool;
    public function flush(): bool;
    public function lastModified(string $key): ?int;
}
```

### 8. Bootstrap Process Updates
- [x] Modify `GuessSiteByHostAndBoot::changeViewFinderPathsForLiquid()`
- [x] Replace direct compiled_dir setting with CompiledStoreManager
- [x] Ensure backward compatibility
- [x] **NEW**: `bootLiquidCompiledStore()` - Bootstrap method for CompiledStoreManager
- [x] **NEW**: `configureLiquidCompiler()` - Configure main Liquid compiler
- [x] **NEW**: `configureLiquidJsonCompiler()` - Configure JSON Liquid compiler  
- [x] **NEW**: `generateCompiledStoreNamespace()` - Multi-tenant namespace generation
- [x] **NEW**: `fallbackToLegacyCompiledDir()` - Graceful fallback to old approach

### 9. ViewFinderManager Integration
- [ ] **Update LiquidCompiler::getFileSource()** - Use selected driver from `ViewFinderManager` instead of hardcoded paths
- [ ] **Update JsonLiquidCompiler::getFileSource()** - Use selected driver from `ViewFinderManager` instead of hardcoded paths
- [ ] Integrate with `packages/cloudcart/laravel-liquid/src/Liquid/ViewFinderManager.php`
- [ ] Ensure proper template path resolution through ViewFinder drivers
- [ ] Maintain compatibility with existing template loading logic

**Goal**: Both compilers should use the ViewFinderManager's selected driver for template file discovery instead of direct filesystem access, ensuring consistency with the template loading architecture.

### 10. Additional Drivers (Future)
- [ ] **RedisCompiledStore** - for Redis storage
- [ ] **S3CompiledStore** - for AWS S3 storage
- [ ] **MemoryCompiledStore** - for temporary memory storage

### 11. Testing and Documentation
- [ ] Unit tests for CompiledStoreManager
- [ ] Unit tests for all drivers
- [ ] Integration tests with LiquidCompiler
- [ ] Configuration and usage documentation
- [ ] Migration guide for existing installations

### 12. Performance Optimizations
- [ ] Caching of compiled templates in DatabaseCompiledStore
- [ ] Lazy loading of drivers
- [ ] Cleanup mechanism for old compiled templates
- [ ] Storage size and performance monitoring

### 13. File Structure
```
packages/cloudcart/laravel-liquid/src/Liquid/Storage/
├── CompiledStoreManager.php
├── Contracts/
│   └── CompiledStoreInterface.php
└── Stores/
    ├── AbstractCompiledStore.php
    ├── FileCompiledStore.php
    ├── DatabaseCompiledStore.php
    └── RedisCompiledStore.php (future)

app/LiquidEngine/Models/
└── CompiledTemplate.php (for database driver)
```

### 14. Migrations
- [ ] Create migration for liquid_compiled_templates table:
  ```php
  Schema::create('liquid_compiled_templates', function (Blueprint $table) {
      $table->id();
      $table->string('key')->unique();
      $table->longText('content');
      $table->timestamp('compiled_at');
      $table->timestamps();
      $table->index(['key', 'compiled_at']);
  });
  ```

## Execution Priority
1. **High Priority**: Tasks 1-5 (core architecture and file driver)
2. **Medium Priority**: Tasks 6-8 (integration and database driver)
3. **Low Priority**: Tasks 9-13 (additional features and optimizations)

## Implementation Considerations
- Backward compatibility with current file storage
- Thread-safe operations for database driver
- Proper error handling and logging
- Performance monitoring and metrics
- Graceful fallback when driver issues occur

## Known Issues

### Debug Bar Compatibility Issue
When Laravel Debug Bar is enabled, the following error occurs:

```
Barryvdh\Debugbar\DataCollector\ViewCollector::addView(): Argument #1 ($view) must be of type Illuminate\View\View, Liquid\View given, called in /var/www/cloudcart.com/cc-builder/vendor/barryvdh/laravel-debugbar/src/LaravelDebugbar.php on line 300
```

**Cause**: Debug Bar's ViewCollector expects `Illuminate\View\View` instances but receives `Liquid\View` instances from the Liquid templating engine.

**Impact**: Debug Bar cannot properly collect view information for Liquid templates.

**Potential Solutions**:
1. Create a custom ViewCollector for Liquid views
2. Add compatibility layer in Liquid\View to extend Illuminate\View\View
3. Configure Debug Bar to ignore Liquid views
4. Disable Debug Bar for Liquid-rendered routes

**Priority**: Low (development-only issue, doesn't affect production) 