# CloudCart Liquid Implementation Examples

## Keepsuit Integration Examples

### Environment Factory Implementation

```php
<?php

namespace App\Liquid;

use Keepsuit\Liquid\EnvironmentFactory;
use Keepsuit\Liquid\Environment;
use App\Liquid\Extensions\CloudCartExtension;
use App\Liquid\FileSystems\CloudCartFileSystem;
use App\Liquid\ResourceLimits\CloudCartResourceLimits;
use App\Liquid\ErrorHandlers\CloudCartErrorHandler;

class CloudCartEnvironmentFactory extends EnvironmentFactory
{
    public function buildCloudCartEnvironment(): Environment
    {
        return $this
            // Configure strict modes
            ->setStrictVariables(false) // CloudCart allows undefined variables
            ->setStrictFilters(false)   // CloudCart allows undefined filters
            ->setRethrowErrors(false)   // CloudCart handles errors gracefully
            
            // Performance settings
            ->setLazyParsing(true)      // Enable lazy parsing for better performance
            
            // CloudCart-specific filesystem
            ->setFilesystem(new CloudCartFileSystem())
            
            // Resource limits for security
            ->setResourceLimits(new CloudCartResourceLimits())
            
            // Custom error handling
            ->setErrorHandler(new CloudCartErrorHandler())
            
            // CloudCart extension with all custom features
            ->addExtension(new CloudCartExtension())
            
            ->build();
    }
}
```

### Laravel Service Provider Integration

```php
<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\View\Engines\EngineResolver;
use App\Liquid\CloudCartEnvironmentFactory;
use App\Liquid\Engines\KeepsuitLiquidEngine;
use App\Liquid\ViewFinders\CloudCartViewFinder;

class KeepsuitLiquidServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->registerEnvironment();
        $this->registerViewFinder();
        $this->registerEngine();
    }
    
    protected function registerEnvironment(): void
    {
        $this->app->singleton('liquid.environment', function ($app) {
            return (new CloudCartEnvironmentFactory())->buildCloudCartEnvironment();
        });
    }
    
    protected function registerViewFinder(): void
    {
        $this->app->singleton('liquid.view.finder', function ($app) {
            return new CloudCartViewFinder(
                $app['files'],
                $app['config']['view.paths'],
                ['liquid']
            );
        });
    }
    
    protected function registerEngine(): void
    {
        $this->app->resolving('view.engine.resolver', function (EngineResolver $resolver) {
            $resolver->register('liquid', function () {
                return new KeepsuitLiquidEngine(
                    $this->app['liquid.environment'],
                    $this->app['liquid.view.finder']
                );
            });
        });
    }
}
```

## Custom Tags Migration Examples

### Authentication Tag

```php
<?php

namespace App\Liquid\Tags;

use Keepsuit\Liquid\Tag;
use Keepsuit\Liquid\Parse\TagParseContext;
use Keepsuit\Liquid\Render\RenderContext;

class AuthTag extends Tag
{
    public static function tagName(): string
    {
        return 'auth';
    }
    
    public function parse(TagParseContext $context): static
    {
        // Parse any parameters if needed
        return $this;
    }
    
    public function render(RenderContext $context): string
    {
        if (auth()->check()) {
            return $this->renderBody($context);
        }
        
        return '';
    }
}
```

### Function Tag (Block Tag)

```php
<?php

namespace App\Liquid\Tags;

use Keepsuit\Liquid\TagBlock;
use Keepsuit\Liquid\Parse\TagParseContext;
use Keepsuit\Liquid\Render\RenderContext;

class FunctionTag extends TagBlock
{
    private string $functionName;
    private array $parameters = [];
    
    public static function tagName(): string
    {
        return 'function';
    }
    
    public function parse(TagParseContext $context): static
    {
        // Parse function name and parameters
        $markup = trim($context->getMarkup());
        
        if (preg_match('/^(\w+)(?:\s+(.+))?$/', $markup, $matches)) {
            $this->functionName = $matches[1];
            
            if (isset($matches[2])) {
                // Parse parameters
                $this->parameters = $this->parseParameters($matches[2]);
            }
        }
        
        return $this;
    }
    
    public function render(RenderContext $context): string
    {
        // Store function in context for later use
        $functionBody = $this->renderBody($context);
        
        $context->set("functions.{$this->functionName}", [
            'body' => $functionBody,
            'parameters' => $this->parameters
        ]);
        
        return '';
    }
    
    private function parseParameters(string $paramString): array
    {
        // Parse parameter list: param1, param2, param3
        return array_map('trim', explode(',', $paramString));
    }
}
```

### Route Tag

```php
<?php

namespace App\Liquid\Tags;

use Keepsuit\Liquid\Tag;
use Keepsuit\Liquid\Parse\TagParseContext;
use Keepsuit\Liquid\Render\RenderContext;

class RouteTag extends Tag
{
    private string $routeName;
    private array $parameters = [];
    
    public static function tagName(): string
    {
        return 'route';
    }
    
    public function parse(TagParseContext $context): static
    {
        $markup = trim($context->getMarkup());
        
        // Parse: route 'product.show', id: product.id
        if (preg_match('/^[\'"]([^\'"]+)[\'"](?:\s*,\s*(.+))?$/', $markup, $matches)) {
            $this->routeName = $matches[1];
            
            if (isset($matches[2])) {
                $this->parameters = $this->parseRouteParameters($matches[2]);
            }
        }
        
        return $this;
    }
    
    public function render(RenderContext $context): string
    {
        $params = [];
        
        foreach ($this->parameters as $key => $value) {
            $params[$key] = $context->resolve($value);
        }
        
        try {
            return route($this->routeName, $params);
        } catch (\Exception $e) {
            return '';
        }
    }
    
    private function parseRouteParameters(string $paramString): array
    {
        $params = [];
        
        // Parse: id: product.id, slug: product.slug
        if (preg_match_all('/(\w+):\s*([^,]+)/', $paramString, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $params[trim($match[1])] = trim($match[2]);
            }
        }
        
        return $params;
    }
}
```

## Custom Filters Migration Examples

### URL Filters

```php
<?php

namespace App\Liquid\Filters;

use Keepsuit\Liquid\Filters\FiltersProvider;
use Keepsuit\Liquid\Attributes\Hidden;

class UrlFilters extends FiltersProvider
{
    public function asset_url(string $asset): string
    {
        $theme = config('app.theme_handle', 'default');
        return asset("themes/{$theme}/assets/{$asset}");
    }
    
    public function theme_url(string $path = ''): string
    {
        $theme = config('app.theme_handle', 'default');
        return asset("themes/{$theme}/{$path}");
    }
    
    public function product_url($product): string
    {
        if (is_array($product) && isset($product['slug'])) {
            return route('product.show', $product['slug']);
        }
        
        if (is_object($product) && isset($product->slug)) {
            return route('product.show', $product->slug);
        }
        
        return '';
    }
    
    public function collection_url($collection): string
    {
        if (is_array($collection) && isset($collection['slug'])) {
            return route('collection.show', $collection['slug']);
        }
        
        if (is_object($collection) && isset($collection->slug)) {
            return route('collection.show', $collection->slug);
        }
        
        return '';
    }
    
    #[Hidden]
    public function helperMethod(): string
    {
        // This method won't be available as a filter
        return 'helper';
    }
}
```

### Asset Filters

```php
<?php

namespace App\Liquid\Filters;

use Keepsuit\Liquid\Filters\FiltersProvider;
use Keepsuit\Liquid\Attributes\Cache;
use Illuminate\Support\HtmlString;

class AssetFilters extends FiltersProvider
{
    #[Cache]
    public function asset_size(string $asset): int
    {
        $theme = config('app.theme_handle', 'default');
        $path = base_path("resources/themes/{$theme}/assets/{$asset}");
        
        return file_exists($path) ? filesize($path) : 0;
    }
    
    public function asset_modified(string $asset): int
    {
        $theme = config('app.theme_handle', 'default');
        $path = base_path("resources/themes/{$theme}/assets/{$asset}");
        
        return file_exists($path) ? filemtime($path) : 0;
    }
    
    public function inline_asset(string $asset): HtmlString
    {
        $theme = config('app.theme_handle', 'default');
        $path = base_path("resources/themes/{$theme}/assets/{$asset}");
        
        if (!file_exists($path)) {
            return new HtmlString('');
        }
        
        $content = file_get_contents($path);
        return new HtmlString($content);
    }
    
    public function script_tag(string $src, array $attributes = []): HtmlString
    {
        $attrs = '';
        foreach ($attributes as $key => $value) {
            $attrs .= " {$key}=\"" . htmlspecialchars($value) . "\"";
        }
        
        $url = $this->asset_url($src);
        return new HtmlString("<script src=\"{$url}\"{$attrs}></script>");
    }
    
    public function stylesheet_tag(string $href, array $attributes = []): HtmlString
    {
        $attrs = '';
        foreach ($attributes as $key => $value) {
            $attrs .= " {$key}=\"" . htmlspecialchars($value) . "\"";
        }
        
        $url = $this->asset_url($href);
        return new HtmlString("<link rel=\"stylesheet\" href=\"{$url}\"{$attrs}>");
    }
    
    private function asset_url(string $asset): string
    {
        $theme = config('app.theme_handle', 'default');
        return asset("themes/{$theme}/assets/{$asset}");
    }
}
```

## Drops Migration Examples

### Cart Drop with Modern Features

```php
<?php

namespace App\Liquid\Drops;

use Keepsuit\Liquid\Drop;
use Keepsuit\Liquid\Attributes\Cache;
use Keepsuit\Liquid\Attributes\Hidden;
use App\Models\Cart as CartModel;
use App\Models\CartItem as CartItemModel;

#[Cache]
class CartDrop extends Drop
{
    private ?CartModel $cart;
    
    public function __construct()
    {
        $this->cart = CartModel::getCurrent();
    }
    
    #[Cache(ttl: 300)] // Cache for 5 minutes
    public function items(): array
    {
        if (!$this->cart) {
            return [];
        }
        
        return $this->cart->items->map(function (CartItemModel $item) {
            return new CartItemDrop($item);
        })->all();
    }
    
    #[Cache(ttl: 60)] // Cache for 1 minute
    public function item_count(): int
    {
        if (!$this->cart) {
            return 0;
        }
        
        return $this->cart->items->sum('quantity');
    }
    
    #[Cache(ttl: 60)]
    public function total_price(): float
    {
        if (!$this->cart) {
            return 0.0;
        }
        
        return $this->cart->total_price;
    }
    
    #[Cache(ttl: 60)]
    public function subtotal_price(): float
    {
        if (!$this->cart) {
            return 0.0;
        }
        
        return $this->cart->subtotal_price;
    }
    
    public function currency(): string
    {
        return config('app.currency', 'USD');
    }
    
    public function checkout_url(): string
    {
        return route('checkout.index');
    }
    
    public function empty(): bool
    {
        return $this->item_count() === 0;
    }
    
    #[Hidden]
    public function internalCalculation(): float
    {
        // This method is hidden from liquid templates
        return $this->cart ? $this->cart->calculateTax() : 0.0;
    }
    
    #[Hidden]
    public function getCart(): ?CartModel
    {
        // Internal method for accessing the cart model
        return $this->cart;
    }
}
```

### Product Drop with Lazy Loading

```php
<?php

namespace App\Liquid\Drops;

use Keepsuit\Liquid\Drop;
use Keepsuit\Liquid\Attributes\Cache;
use Keepsuit\Liquid\Contracts\MapsToLiquid;
use App\Models\Product as ProductModel;

class ProductDrop extends Drop
{
    public function __construct(private ProductModel $product) {}
    
    public function id(): int
    {
        return $this->product->id;
    }
    
    public function title(): string
    {
        return $this->product->title;
    }
    
    public function description(): string
    {
        return $this->product->description;
    }
    
    public function price(): float
    {
        return $this->product->price;
    }
    
    public function compare_at_price(): ?float
    {
        return $this->product->compare_at_price;
    }
    
    public function slug(): string
    {
        return $this->product->slug;
    }
    
    public function url(): string
    {
        return route('product.show', $this->product->slug);
    }
    
    #[Cache]
    public function images(): array
    {
        return $this->product->images->map(function ($image) {
            return new ProductImageDrop($image);
        })->all();
    }
    
    #[Cache]
    public function variants(): array
    {
        return $this->product->variants->map(function ($variant) {
            return new ProductVariantDrop($variant);
        })->all();
    }
    
    public function featured_image(): ?ProductImageDrop
    {
        $featuredImage = $this->product->featured_image;
        return $featuredImage ? new ProductImageDrop($featuredImage) : null;
    }
    
    public function available(): bool
    {
        return $this->product->available;
    }
    
    public function tags(): array
    {
        return $this->product->tags->pluck('name')->all();
    }
    
    #[Cache]
    public function collections(): array
    {
        return $this->product->collections->map(function ($collection) {
            return new CollectionDrop($collection);
        })->all();
    }
}

// Make Product model automatically convert to drop
class Product extends Model implements MapsToLiquid
{
    public function toLiquid(): ProductDrop
    {
        return new ProductDrop($this);
    }
}
```

## Extension System Example

### CloudCart Extension

```php
<?php

namespace App\Liquid\Extensions;

use Keepsuit\Liquid\Extensions\Extension;
use App\Liquid\Tags\AuthTag;
use App\Liquid\Tags\GuestTag;
use App\Liquid\Tags\FunctionTag;
use App\Liquid\Tags\RouteTag;
use App\Liquid\Filters\UrlFilters;
use App\Liquid\Filters\AssetFilters;

class CloudCartExtension extends Extension
{
    public function getTags(): array
    {
        return [
            AuthTag::class,
            GuestTag::class,
            FunctionTag::class,
            RouteTag::class,
            // ... all other CloudCart tags
        ];
    }
    
    public function getFiltersProviders(): array
    {
        return [
            UrlFilters::class,
            AssetFilters::class,
            // ... all other CloudCart filter providers
        ];
    }
    
    public function getRegisters(): array
    {
        return [
            'theme' => fn() => config('app.theme_handle'),
            'shop' => fn() => app('shop'),
            'cart' => fn() => new CartDrop(),
            'customer' => fn() => auth()->check() ? new CustomerDrop(auth()->user()) : null,
        ];
    }
}
```

## Performance Optimization Examples

### Resource Limits Configuration

```php
<?php

namespace App\Liquid\ResourceLimits;

use Keepsuit\Liquid\ResourceLimits;

class CloudCartResourceLimits extends ResourceLimits
{
    public function __construct()
    {
        parent::__construct(
            renderScoreLimit: 10000,    // Maximum render operations
            assignScoreLimit: 1000,     // Maximum assign operations
            renderTimeoutMs: 5000       // 5 second timeout
        );
    }
}
```

### Error Handler Implementation

```php
<?php

namespace App\Liquid\ErrorHandlers;

use Keepsuit\Liquid\ErrorHandlers\ErrorHandler;
use Keepsuit\Liquid\Exceptions\RenderException;
use App\Models\Router\Logs;

class CloudCartErrorHandler implements ErrorHandler
{
    public function handleRenderError(RenderException $exception): string
    {
        // Log error using CloudCart logging system
        Logs::create([
            'type' => 'liquid_render_error',
            'message' => $exception->getMessage(),
            'data' => [
                'template' => $exception->getTemplateName(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
            ]
        ]);
        
        // Return user-friendly error message
        if (app()->environment('production')) {
            return '<!-- Template rendering error -->';
        }
        
        return "<!-- Error: {$exception->getMessage()} -->";
    }
}
```
