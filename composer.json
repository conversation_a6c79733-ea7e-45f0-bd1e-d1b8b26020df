{"name": "cloudcart/cc-builder", "type": "project", "description": "CloudCart Builder.", "keywords": ["store", "cloudcart"], "license": "CloudCart License", "repositories": [{"type": "composer", "url": "https://docs.lukanet.com"}, {"type": "vcs", "url": "**************:cloudcart/omniship.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-econt.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-speedy.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-dpdromania.git"}, {"type": "vcs", "url": "**************:cloudcart/eushipment-sdk.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-eushipment.git"}, {"type": "vcs", "url": "**************:cloudcart/laravel-permission.git"}, {"type": "vcs", "url": "**************:cloudcart/mongodb-session-driver.git"}, {"type": "vcs", "url": "**************:cloudcart/acme-challenge.git"}, {"type": "vcs", "url": "**************:cloudcart/mongodb-queue-driver.git"}, {"type": "vcs", "url": "**************:cloudcart/mongodb-cache-driver.git"}, {"type": "vcs", "url": "**************:cloudcart/localization.git"}, {"type": "vcs", "url": "**************:cloudcart/laravel-mysql-spatial.git"}, {"type": "vcs", "url": "**************:cloudcart/php-conversion.git"}, {"type": "vcs", "url": "**************:cloudcart/xml-parser.git"}, {"type": "vcs", "url": "**************:cloudcart/cc-image-optimizer.git"}, {"type": "vcs", "url": "**************:cloudcart/cc-elastic-email-web-api.git"}, {"type": "vcs", "url": "**************:cloudcart/laravel-elastic-email.git"}, {"type": "vcs", "url": "**************:cloudcart/omnipay-borica.git"}, {"type": "vcs", "url": "**************:cloudcart/omnipay-dskbank.git"}, {"type": "vcs", "url": "**************:cloudcart/omnipay-raiffeisen.git"}, {"type": "vcs", "url": "**************:cloudcart/omnipay-paycenter.git"}, {"type": "vcs", "url": "**************:cloudcart/omnipay-nestpay.git"}, {"type": "vcs", "url": "**************:cloudcart/omnipay-braintree.git"}, {"type": "vcs", "url": "**************:cloudcart/omnipay-cardlink.git"}, {"type": "vcs", "url": "**************:cloudcart/omnipay-euplatesc.git"}, {"type": "vcs", "url": "**************:cloudcart/omnipay-paysera.git"}, {"type": "vcs", "url": "**************:cloudcart/omnipay-librapay.git"}, {"type": "vcs", "url": "**************:cloudcart/omnipay-mobilpay.git"}, {"type": "vcs", "url": "**************:cloudcart/omnipay-cibbank.git"}, {"type": "vcs", "url": "**************:cloudcart/omnipay-epay.git"}, {"type": "vcs", "url": "**************:cloudcart/epay-one-touch.git"}, {"type": "vcs", "url": "**************:cloudcart/fibank.git"}, {"type": "vcs", "url": "**************:cloudcart/smart-ucf.git"}, {"type": "vcs", "url": "**************:cloudcart/bnp-paribas-pf.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-evropat.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-fancourier.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-berry.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-dhlexpress.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-boxnow.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-sameday.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-glovo.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-sendcloud.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-acscourier.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-grabitmk.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-ultracep.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-ntclogistics.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-speedex.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-cargus.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-tcscourier.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-mikmik.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-albaniancourier.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-dhl.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-rapido.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-fedex.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-speedy.git"}, {"type": "vcs", "url": "**************:cloudcart/mongodb-laravel.git"}, {"type": "vcs", "url": "**************:cloudcart/cc-parser.git"}, {"type": "vcs", "url": "**************:cloudcart/*********************.git"}, {"type": "vcs", "url": "**************:cloudcart/cc-laravel-smarty.git"}, {"type": "path", "url": "packages/cloudcart/laravel-liquid"}, {"type": "vcs", "url": "**************:cloudcart/opentelemetry-meta.git"}, {"type": "vcs", "url": "https://github.com/ant0x64/php-ga-measurement-protocol.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-gls.git"}, {"type": "vcs", "url": "**************:cloudcart/cloudcreativity-laravel-json-api.git"}, {"type": "vcs", "url": "**************:cloudcart/cloudcreativity-utils-object.git"}, {"type": "vcs", "url": "**************:cloudcart/neomerx-json-api.git"}, {"type": "vcs", "url": "**************:cloudcart/short-links.git"}, {"type": "vcs", "url": "**************:cloudcart/omniship-dexpress.git"}], "require": {"php": "^8.4", "ext-bcmath": "*", "ext-curl": "*", "ext-dom": "*", "ext-exif": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-imagick": "*", "ext-intl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-mongodb": "*", "ext-mysqli": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-simplexml": "*", "ext-soap": "*", "ext-ssh2": "*", "ext-zend-opcache": "*", "ext-zip": "*", "ext-zlib": "*", "acmephp/ssl": "*", "algolia/algoliasearch-client-php": "^4.16", "astrotomic/laravel-translatable": "*", "bacon/bacon-qr-code": "^3.0", "barryvdh/laravel-debugbar": "^3.14", "br33f/php-ga4-mp": "*", "braintree/braintree_php": "*", "chrisjean/php-ico": "*", "cloudcart/acme-challenge": "dev-master", "cloudcart/bnp-paribas-pf": "dev-master", "cloudcart/cc-elastic-email-web-api": "dev-master", "cloudcart/*********************": "dev-master", "cloudcart/cc-image-optimizer": "dev-master", "cloudcart/cc-laravel-smarty": "dev-master", "cloudcart/epay-one-touch": "dev-master", "cloudcart/eushipment-sdk": "dev-main", "cloudcart/fibank": "dev-master", "cloudcart/laravel-elastic-email": "dev-master", "cloudcart/laravel-liquid": "dev-master", "cloudcart/laravel-mysql-spatial": "dev-master", "cloudcart/laravel-permission": "dev-master", "cloudcart/localization": "dev-master", "cloudcart/mongodb-cache-driver": "dev-master", "cloudcart/mongodb-laravel": "dev-master", "cloudcart/mongodb-queue-driver": "dev-master", "cloudcart/mongodb-session-driver": "dev-master", "cloudcart/omnipay-borica": "dev-master", "cloudcart/omnipay-cardlink": "dev-master", "cloudcart/omnipay-cibbank": "dev-main", "cloudcart/omnipay-dskbank": "dev-master", "cloudcart/omnipay-epay": "dev-master", "cloudcart/omnipay-euplatesc": "dev-master", "cloudcart/omnipay-librapay": "dev-main", "cloudcart/omnipay-mobilpay": "dev-master", "cloudcart/omnipay-nestpay": "dev-main", "cloudcart/omnipay-paycenter": "dev-master", "cloudcart/omnipay-paysera": "dev-master", "cloudcart/omnipay-raiffeisen": "dev-master", "cloudcart/omniship": "dev-master", "cloudcart/omniship-acscourier": "dev-main", "cloudcart/omniship-albaniancourier": "dev-main", "cloudcart/omniship-berry": "dev-main", "cloudcart/omniship-boxnow": "dev-main", "cloudcart/omniship-cargus": "dev-main", "cloudcart/omniship-dhl": "dev-main", "cloudcart/omniship-dhlexpress": "dev-main", "cloudcart/omniship-dpdromania": "dev-main", "cloudcart/omniship-econt": "dev-master", "cloudcart/omniship-eushipment": "dev-main", "cloudcart/omniship-evropat": "dev-master", "cloudcart/omniship-fancourier": "dev-main", "cloudcart/omniship-fedex": "dev-main", "cloudcart/omniship-glovo": "dev-main", "cloudcart/omniship-gls": "dev-main", "cloudcart/omniship-grabitmk": "dev-main", "cloudcart/omniship-mikmik": "dev-main", "cloudcart/omniship-ntclogistics": "dev-main", "cloudcart/omniship-rapido": "dev-main", "cloudcart/omniship-sameday": "dev-main", "cloudcart/omniship-sendcloud": "dev-main", "cloudcart/omniship-speedex": "dev-main", "cloudcart/omniship-speedy": "dev-master", "cloudcart/omniship-tcscourier": "dev-main", "cloudcart/omniship-ultracep": "dev-main", "cloudcart/omniship-dexpress": "dev-main", "cloudcart/opentelemetry-meta": "dev-main", "cloudcart/smart-ucf": "dev-master", "cloudcart/xml-parser": "dev-master", "cloudcart/short-links": "dev-master", "cloudcreativity/laravel-json-api": "dev-v160-laravel-12@dev", "cloudcreativity/utils-object": "dev-v100-laravel-12@dev", "cloudflare/sdk": "^1.4", "crisu83/php-conversion": "*", "cviebrock/eloquent-taggable": "^11.0||^12.0", "developermypos/mypos-checkout-sdk": "1.3.0", "doctrine/dbal": "*", "drewm/mailchimp-api": "*", "dvdoug/boxpacker": "*", "ezyang/htmlpurifier": "^4.18", "facebook/php-business-sdk": "^22.0", "fileeye/pel": "^0.12.0", "geoip2/geoip2": "*", "giggsey/libphonenumber-for-php": "^8.13", "google/apiclient": "*", "google/cloud-storage": "*", "google/cloud-trace": "^1.8", "google/cloud-translate": "^2.0", "google/recaptcha": "^1.3", "hashids/hashids": "^5.0", "http-interop/http-factory-guzzle": "*", "illuminated/helper-functions": "^11 || ^12", "io-developer/php-whois": "*", "jaybizzle/laravel-crawler-detect": "*", "jeremykendall/php-domain-parser": "^6.4", "jgivoni/flysystem-cache-adapter": "*", "kub-at/php-simple-html-dom-parser": "^1.9", "laravel/framework": "^11 || ^12", "laravel/helpers": "^1.7", "laravel/scout": "^10.14", "laravel/slack-notification-channel": "*", "laravel/socialite": "^5.17", "laravel/tinker": "^2.9", "league/flysystem": "^3.0", "league/flysystem-ftp": "^3.0", "league/flysystem-local": "^3.0", "league/flysystem-sftp-v3": "^3.0", "lukanet/barsy-api-client": "1.0.171", "maatwebsite/excel": "^3.1", "milon/barcode": "*", "minishlink/web-push": "^9.0", "mobiledetect/mobiledetectlib": "^4.8", "mongodb/laravel-mongodb": "^5.3", "mpdf/mpdf": "*", "mpociot/vat-calculator": "^3.18", "neomerx/json-api": "dev-v109-laravel-12@dev", "omnipay/braintree": "dev-master", "omnipay/common": "^3.4", "omnipay/mollie": "^5.5", "omnipay/paypal": "^3.0", "open-telemetry/exporter-otlp": "^1.2", "open-telemetry/opentelemetry-auto-curl": "^0.0.4", "open-telemetry/opentelemetry-auto-io": "^0.0.13", "open-telemetry/opentelemetry-auto-laravel": "^1.1", "open-telemetry/opentelemetry-auto-mongodb": "dev-main", "open-telemetry/sdk": "^1.2", "openspout/openspout": "^4.29", "php-http/httplug": "^2.4", "phpstan/phpdoc-parser": "^2.0", "pragmarx/google2fa": "^8.0", "pragmarx/google2fa-qrcode": "^3.0", "propaganistas/laravel-phone": "^5.3", "retargeting/retargeting-sdk": "^1", "revolution/laravel-google-sheets": "^7.1", "robrichards/xmlseclibs": "^3.1", "sabre/xml": "^4.0", "sentry/sentry-laravel": "^4.10", "spatie/array-to-xml": "^3.4", "spatie/db-dumper": "^3.7", "spatie/laravel-analytics": "^5.5", "spatie/laravel-google-calendar": "^3.8", "spatie/laravel-googletagmanager": "^2.7", "spatie/laravel-json-api-paginate": "^1.16", "spatie/laravel-permission": "^6.16", "spatie/laravel-query-builder": "^6.3", "spatie/laravel-translatable": "^6.9", "spatie/schema-org": "^3.23", "stonemax/acme2": "*", "stripe/stripe-php": "*", "symfony/dom-crawler": "^7.2", "symfony/http-client": "^7.2", "symfony/polyfill-iconv": "^1.31", "symfony/process": "^7.2", "theiconic/php-ga-measurement-protocol": "dev-master", "timehunter/laravel-google-recaptcha-v3": "~2.5", "torann/geoip": "*", "tymon/jwt-auth": "^2.1", "webapix/mygls-sdk": "^3.0", "whichbrowser/parser": "^2.1", "yaza/laravel-google-drive-storage": "^4.1"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.2", "driftingly/rector-laravel": "^2.0", "fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.20", "laravel/sail": "^1.26", "maantje/xhprof-buggregator-laravel": "^0.6||^0.7", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0||^12.0", "filp/whoops": "^2.17", "cloudcart/cc-parser": "dev-master"}, "autoload": {"psr-4": {"App\\": "app/", "Modules\\": "modules/", "FacebookGW\\": "facebook/", "Api\\": "api2/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Console\\": "console/", "Resellers\\": "resellers/", "MarketPlace\\App\\": "marketplace/app/", "lib\\": "protected/classes/lib/"}, "classmap": ["database", "protected/_core"], "files": ["app/Helper/functions.php", "app/Helper/widget_functions.php", "app/Helper/CloudflareHelper.php"], "exclude-from-classmap": ["app/Integration/**/migrations/*", "app/Integration/**/seeds/*", "modules/**/migrations/*", "modules/**/seeds/*", "app/Integration/Payment/Ibank/lib/**", "**/SOAP/**"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "RectorRules\\": "rector-rules/"}}, "scripts": {"post-autoload-dump": ["@php artisan package:discover --ansi", "Illuminate\\Foundation\\ComposerScripts::postAutoloadDump"], "pre-install-cmd": ["rm -rf bootstrap/cache/*.php", "rm -rf storage/debugbar/*.json"], "post-install-cmd": ["@php artisan geoip:update", "@php artisan app:dev-post-install-command"], "pre-update-cmd": ["rm -rf bootstrap/cache/*.php", "rm -rf storage/debugbar/*.json"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "Illuminate\\Foundation\\ComposerScripts::postUpdate", "@php artisan ide-helper:generate", "@php artisan ide-helper:meta", "@php artisan geoip:update"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "tbachert/spi": true}, "gitlab-oauth": {"gitlab.com": "**************************"}}, "minimum-stability": "stable", "prefer-stable": true, "extra": {"laravel": {"dont-discover": ["cloudcreativity/laravel-json-api"]}}}