

{% liquid
  assign media_count = 0
  if section.settings.image_1 != blank and section.settings.image_2 != blank
    assign media_width_desktop = 100 | divided_by: 2 | append: 'vw'
  else
    assign media_width_desktop = '100vw'
  endif
  assign media_width_mobile = '100vw'
  assign sizes = '(min-width: 750px) ' | append: media_width_desktop | append: ', ' | append: media_width_mobile
  assign widths = '300, 450, 600, 750, 900, 1050, 1200, 1350, 1500, 1650, 1800, 1950, 2000, 2500, 3000, 3500, 4000, 5000'

  assign has_image_1 = false
  assign has_image_2 = false
  assign has_video_1 = false
  assign has_video_2 = false
  assign has_image = false
  assign has_video = false
  assign has_video_multiple = false
  assign has_media = false
  assign media_type_1 = section.settings.media_type_1
  assign media_type_2 = section.settings.media_type_2
  if section.settings.image_1 != blank and media_type_1 == 'image'
    assign has_image_1 = true
  endif
  if section.settings.image_2 != blank and media_type_2 == 'image'
    assign has_image_2 = true
  endif
  if section.settings.video_1 != blank and media_type_1 == 'video'
    assign has_video_1 = true
  endif
  if section.settings.video_2 != blank and media_type_2 == 'video'
    assign has_video_2 = true
  endif

  if has_image_1 or has_image_2
    assign has_image = true
  endif
  if has_video_1 or has_video_2
    assign has_video = true
  endif
  if has_video_1 and has_video_2
    assign has_video_multiple = true
  endif
  if has_image or has_video
    assign has_media = true
  endif

  assign alpha = section.settings.overlay_color.alpha
  assign is_transparent = true
  if alpha == 1 and section.settings.toggle_overlay
    assign is_transparent = false
  endif
%}

{% capture media %}
  {%- if has_image_1 -%}
    {% assign media_count = media_count | plus: 1 %}
    {{
      section.settings.image_1
      | image_url: width: 3840
      | image_tag:
        width: section.settings.image_1.width,
        widths: widths,
        height: section.settings.image_1.height,
        alt: section.settings.image_1.alt,
        class: 'hero__image',
        sizes: sizes
    }}
  {%- endif -%}

  {%- if has_video_1 -%}
    {% assign media_count = media_count | plus: 1 %}
    {{
      section.settings.video_1
      | video_tag:
        image_size: '3840x',
        autoplay: true,
        loop: true,
        controls: false,
        muted: true,
        class: 'hero__video'
    }}
  {%- endif -%}

  {%- if has_image_2 -%}
    {% assign media_count = media_count | plus: 1 %}
    {{
      section.settings.image_2
      | image_url: width: 3840
      | image_tag:
        width: section.settings.image_2.width,
        widths: widths,
        height: section.settings.image_2.height,
        alt: section.settings.image_2.alt,
        class: 'hero__image',
        sizes: sizes
    }}
  {%- endif -%}

  {%- if has_video_2 -%}
    {% assign media_count = media_count | plus: 1 %}
    {{
      section.settings.video_2
      | video_tag:
        image_size: '3840x',
        autoplay: true,
        loop: true,
        controls: false,
        muted: true,
        class: 'hero__video'
    }}
  {%- endif -%}

  {% if has_media == false and is_transparent %}
    <placeholder-image
      class="hero__image"
      data-block-id="{{ section.id }}"
      data-type="general"
    ></placeholder-image>
  {%- endif -%}
{% endcapture %}

{% capture media_blurred %}
  {% if has_image_1 %}
    {{
      section.settings.image_1
      | image_url: width: 3840
      | image_tag:
        width: section.settings.image_1.width,
        widths: widths,
        height: section.settings.image_1.height,
        alt: '',
        class: 'hero__image',
        sizes: sizes
    }}
  {% endif %}
  {% if has_image_2 %}
    {{
      section.settings.image_2
      | image_url: width: 3840
      | image_tag:
        width: section.settings.image_2.width,
        widths: widths,
        height: section.settings.image_2.height,
        alt: '',
        class: 'hero__image',
        sizes: sizes
    }}
  {% endif %}
  {% if has_media == false and is_transparent %}
    <placeholder-image
      class="hero__image"
      data-block-id="{{ section.id }}"
      data-type="general"
    ></placeholder-image>
  {% endif %}
{% endcapture %}

<div
  id="Hero-{{ section.id }}"
  class="hero color-{{ section.settings.color_scheme }}{% if section.blocks.size == 0 and section.settings.section_height == 'auto' %} hero--no-blocks-auto-height{% endif %}"
  style="
    --hero-border-style: {{ section.settings.border }};
    --hero-border-width: {{ section.settings.border_width }}px;
    --hero-border-opacity: {{ section.settings.border_opacity }}%;
    --blur-opacity: {{ section.settings.reflection_opacity | divided_by: 100.0 }};
    {% if section.settings.section_height == 'custom' %}
      --hero-min-height: {{ section.settings.section_height_custom }}svh;
    {% elsif section.settings.section_height == "full-screen" %}
       --hero-min-height: 100svh;
    {% else %}
      --hero-min-height: var(--section-height-{{ section.settings.section_height }});
    {% endif %}
  "
  {% if request.visual_preview_mode %}
    data-shopify-visual-preview
  {% endif %}
  {% if section.settings.blurred_reflection and has_video_multiple == false %}
    data-blur-shadow="true"
  {% endif %}
>
  {% if section.settings.blurred_reflection %}
    {% unless has_video_multiple or is_transparent == false %}
      <div class="hero__blurred-image">
        {{ media_blurred }}
      </div>
    {% endunless %}
  {% endif %}



  <div
    class="hero__container spacing-style section section--full-width"
    style="      {% render 'spacing-style', settings: section.settings %}"
  >
    {%- if section.settings.link != blank -%}
      <a
        href="{{ section.settings.link }}"
        class="hero__link"
        {% if section.settings.open_in_new_tab %}
          target="_blank" rel="noopener"
        {% endif %}
      ></a>
    {%- endif -%}
    <div
      class="hero__media-wrapper"
      {%- if section.settings.image_1 != blank -%}
        style="--hero-media-aspect-ratio: {{ section.settings.image_1.aspect_ratio }}; --hero-media-count: {{ media_count }};"
      {%- endif -%}
    >
      {% liquid
        if section.settings.toggle_overlay
          render 'overlay', settings: section.settings
        endif
      %}
      {{ media }}
    </div>
    <div
      class="
        hero__content-wrapper
        layout-panel-flex
        layout-panel-flex--{{ section.settings.content_direction }}
        {% if section.settings.vertical_on_mobile %}mobile-column{% endif %}
        section-content-wrapper
        {{section.settings.section_width}}
        {% if request.design_mode %}hero__content-wrapper--design-mode{% endif %}
      "
      style="{% render 'layout-panel-style', settings: section.settings %}"
    >
      {% content_for 'blocks' %}
    </div>
  </div>
</div>

{% stylesheet %}
  .hero-wrapper {
    --hero-height-offset: 0px;
  }

  body:has(> #header-group > .header-section > #header-component[transparent]):not(
      :has(> #header-group > .header-section + .shopify-section)
    )
    .hero-wrapper:first-child {
    --hero-height-offset: var(--header-group-height, 0);
  }

  .hero {
    position: relative;
    min-height: calc(var(--hero-min-height) - var(--hero-height-offset));
  }

  .hero[data-shopify-visual-preview] {
    --hero-min-height: 600px;
    min-height: 600px;
  }

  .hero__container {
    position: relative;
    overflow: hidden;
    border: var(--hero-border-width) var(--hero-border-style)
      rgba(from var(--color-border) r g b / var(--hero-border-opacity));
    min-height: inherit;
    align-items: var(--vertical-alignment-mobile);
    justify-content: var(--horizontal-alignment);
    z-index: var(--layer-base);

    @media screen and (width >= 750px) {
      align-items: var(--vertical-alignment);
    }
  }

  .hero__content-wrapper.page-width {
    grid-column: 2 / 3;
  }

  .hero__content-wrapper {
    position: relative;
    inset: 0;
    z-index: var(--layer-flat);
  }

  .hero__content-wrapper .group-block-content {
    position: relative;
  }

  .hero__media-wrapper {
    position: absolute;
    inset: 0;
    display: grid;
    grid-column: 1 / -1;
    grid-template-columns: repeat(var(--hero-media-count, 1), 1fr);
  }

  .hero--auto .hero__image,
  .hero--auto .hero__video {
    aspect-ratio: var(--hero-media-aspect-ratio);
  }

  .hero--no-blocks-auto-height {
    :is(.hero__image, .hero__video) {
      width: 100%;
      aspect-ratio: auto;
    }
    .hero__media-wrapper {
      /* When there are no blocks and the height is auto, allow the image to appear. */
      position: relative;
    }
  }

  .hero__image,
  .hero__video {
    position: relative;
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center center;
    z-index: var(--layer-base);
    overflow: hidden;
  }

  .hero__link {
    position: absolute;
    inset: 0;
    grid-column: 1 / -1;
  }

  .hero__media-wrapper,
  .hero__content-wrapper {
    pointer-events: none;
    a,
    button,
    input,
    textarea,
    select {
      pointer-events: auto;
    }
  }

  .hero__content-wrapper--design-mode {
    pointer-events: auto;
  }

  .hero[data-blur-shadow='true'] {
    --blurred-reflection-filter-saturate: saturate(1.5);
    --blurred-reflection-mask-image: linear-gradient(to bottom, black 0%, black 60%, transparent 100%);
    --blurred-reflection-box-shadow: rgb(0 0 0 / 5%) 0 0 1rem;
    --blurred-reflection-filter-blur: blur(20px);
    --blurred-reflection-scale: scale(2, 1.25);
    --blurred-reflection-padding-block-end: 60px;
  }

  .hero[data-blur-shadow='true'] .hero__container {
    overflow: inherit;
  }

  .hero[data-blur-shadow='true'] .hero__container::before {
    content: '';
    position: absolute;
    inset: 0;
    box-shadow: var(--blurred-reflection-box-shadow);
    mix-blend-mode: overlay;
    pointer-events: none;
    z-index: -1;
  }

  .hero__blurred-image {
    position: absolute;
    inset: 0;
    z-index: -1;
    mask-image: var(--blurred-reflection-mask-image);
    filter: var(--blurred-reflection-filter-saturate);
    pointer-events: none;
    transform: translateY(50%);
    overflow: hidden;
  }

  .hero__blurred-image img {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center center;
    filter: var(--blurred-reflection-filter-blur);
    opacity: var(--blur-opacity);
    transform: var(--blurred-reflection-scale);
    padding-block-end: var(--blurred-reflection-padding-block-end);

    &:not(:only-child) {
      width: 50%;

      &:last-child {
        right: 0;
        left: auto;
      }
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.hero",
  "tag": "section",
  "class": "hero-wrapper section-wrapper",
  "blocks": [
    {
      "type": "@theme"
    },
    {
      "type": "text"
    },
    {
      "type": "button"
    },
    {
      "type": "logo"
    },
    {
      "type": "jumbo-text"
    },
    {
      "type": "spacer"
    },
    {
      "type": "group"
    },
    {
      "type": "_marquee"
    }
  ],
  "disabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "url",
      "id": "link",
      "label": "t:settings.link"
    },
    {
      "type": "checkbox",
      "id": "open_in_new_tab",
      "label": "t:settings.open_new_tab",
      "default": false
    },
    {
      "type": "header",
      "content": "t:content.media"
    },
    {
      "type": "select",
      "id": "media_type_1",
      "label": "t:settings.type",
      "options": [
        {
          "value": "image",
          "label": "t:options.image"
        },
        {
          "value": "video",
          "label": "t:options.video"
        }
      ],
      "default": "image"
    },
    {
      "type": "image_picker",
      "id": "image_1",
      "label": "t:settings.image",
      "visible_if": "{{ section.settings.media_type_1 == 'image' }}"
    },
    {
      "type": "video",
      "id": "video_1",
      "label": "t:settings.video",
      "visible_if": "{{ section.settings.media_type_1 == 'video' }}"
    },
    {
      "type": "header",
      "content": "t:content.media_2"
    },
    {
      "type": "select",
      "id": "media_type_2",
      "label": "t:settings.type",
      "options": [
        {
          "value": "image",
          "label": "t:options.image"
        },
        {
          "value": "video",
          "label": "t:options.video"
        }
      ],
      "default": "image"
    },
    {
      "type": "image_picker",
      "id": "image_2",
      "label": "t:settings.image",
      "visible_if": "{{ section.settings.media_type_2 == 'image' }}"
    },
    {
      "type": "video",
      "id": "video_2",
      "label": "t:settings.video",
      "visible_if": "{{ section.settings.media_type_2 == 'video' }}"
    },
    {
      "type": "header",
      "content": "t:content.layout"
    },
    {
      "type": "select",
      "id": "content_direction",
      "label": "t:settings.direction",
      "options": [
        {
          "value": "column",
          "label": "t:options.vertical"
        },
        {
          "value": "row",
          "label": "t:options.horizontal"
        }
      ],
      "default": "column"
    },
    {
      "type": "checkbox",
      "id": "vertical_on_mobile",
      "label": "t:settings.vertical_on_mobile",
      "default": true,
      "visible_if": "{{ section.settings.content_direction == 'row' }}"
    },
    {
      "type": "select",
      "id": "horizontal_alignment",
      "label": "t:settings.alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.left"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.right"
        },
        {
          "value": "space-between",
          "label": "t:options.space_between"
        }
      ],
      "default": "flex-start",
      "visible_if": "{{ section.settings.content_direction == 'row' }}"
    },
    {
      "type": "select",
      "id": "vertical_alignment",
      "label": "t:settings.position",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.top"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.bottom"
        }
      ],
      "default": "center",
      "visible_if": "{{ section.settings.content_direction == 'row' }}"
    },
    {
      "type": "checkbox",
      "id": "align_baseline",
      "label": "t:settings.align_baseline",
      "default": false,
      "visible_if": "{{ section.settings.vertical_alignment == 'flex-end' }}"
    },
    {
      "type": "select",
      "id": "horizontal_alignment_flex_direction_column",
      "label": "t:settings.alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.left"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.right"
        }
      ],
      "default": "flex-start",
      "visible_if": "{{ section.settings.content_direction != 'row' }}"
    },
    {
      "type": "select",
      "id": "vertical_alignment_flex_direction_column",
      "label": "t:settings.position",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.top"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.bottom"
        },
        {
          "value": "space-between",
          "label": "t:options.space_between"
        }
      ],
      "default": "center",
      "visible_if": "{{ section.settings.content_direction == 'column' }}"
    },
    {
      "type": "range",
      "id": "gap",
      "label": "t:settings.gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 12
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "t:settings.width",
      "options": [
        {
          "value": "page-width",
          "label": "t:options.page"
        },
        {
          "value": "full-width",
          "label": "t:options.full"
        }
      ],
      "default": "page-width"
    },
    {
      "type": "select",
      "id": "section_height",
      "label": "t:settings.height",
      "options": [
        {
          "value": "auto",
          "label": "t:options.auto"
        },
        {
          "value": "small",
          "label": "t:options.small"
        },
        {
          "value": "medium",
          "label": "t:options.medium"
        },
        {
          "value": "large",
          "label": "t:options.large"
        },
        {
          "value": "full-screen",
          "label": "t:options.full_screen"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "medium"
    },
    {
      "type": "range",
      "id": "section_height_custom",
      "label": "t:settings.custom_height",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "unit": "%",
      "visible_if": "{{ section.settings.section_height == 'custom' }}"
    },
    {
      "type": "header",
      "content": "t:content.appearance"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "toggle_overlay",
      "label": "t:settings.media_overlay"
    },
    {
      "type": "color",
      "id": "overlay_color",
      "label": "t:settings.overlay_color",
      "alpha": true,
      "default": "#00000026",
      "visible_if": "{{ section.settings.toggle_overlay }}"
    },
    {
      "type": "select",
      "id": "overlay_style",
      "label": "t:settings.overlay_style",
      "options": [
        {
          "value": "solid",
          "label": "t:options.solid"
        },
        {
          "value": "gradient",
          "label": "t:options.gradient"
        }
      ],
      "default": "solid",
      "visible_if": "{{ section.settings.toggle_overlay }}"
    },
    {
      "type": "select",
      "id": "gradient_direction",
      "label": "t:settings.gradient_direction",
      "options": [
        {
          "value": "to top",
          "label": "t:options.up"
        },
        {
          "value": "to bottom",
          "label": "t:options.down"
        }
      ],
      "default": "to top",
      "visible_if": "{{ section.settings.toggle_overlay and section.settings.overlay_style == 'gradient' }}"
    },
    {
      "type": "checkbox",
      "id": "blurred_reflection",
      "label": "t:settings.blurred_reflection",
      "default": false,
      "info": "t:info.applies_on_image_only"
    },
    {
      "type": "range",
      "id": "reflection_opacity",
      "label": "t:settings.reflection_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 75,
      "visible_if": "{{ section.settings.blurred_reflection }}"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.hero",
      "category": "t:categories.banners",
      "blocks": {
        "text_1": {
          "type": "text",
          "settings": {
            "text": "<h1>New arrivals</h1>",
            "type_preset": "h1",
            "max_width": "narrow"
          }
        },
        "text_2": {
          "type": "text",
          "settings": {
            "text": "<p>Made with care and unconditionally loved by our customers.</p>",
            "max_width": "narrow",
            "padding-block-end": 32
          }
        },
        "button": {
          "type": "button",
          "name": "t:names.button",
          "settings": {
            "label": "Shop Now",
            "link": "shopify://collections/all"
          }
        }
      },
      "block_order": ["text_1", "text_2", "button"],
      "settings": {
        "horizontal_alignment_flex_direction_column": "center",
        "gap": 16,
        "section_height": "large",
        "color_scheme": "scheme-5",
        "padding-block-start": 40,
        "padding-block-end": 40,
        "toggle_overlay": true,
        "overlay_style": "gradient"
      }
    },
    {
      "name": "t:names.hero_marquee",
      "category": "t:categories.banners",
      "blocks": {
        "spacer": {
          "type": "spacer",
          "settings": {
            "size": "pixel",
            "pixel_size": 24
          }
        },
        "marquee": {
          "type": "_marquee",
          "blocks": {
            "text": {
              "type": "text",
              "settings": {
                "text": "<p>Explore our latest products.</p>",
                "type_preset": "h1"
              }
            }
          },
          "block_order": ["text"]
        },
        "button": {
          "type": "button",
          "settings": {
            "label": "Shop Now",
            "link": "shopify://collections/all"
          }
        }
      },
      "block_order": ["spacer", "marquee", "button"],
      "settings": {
        "horizontal_alignment_flex_direction_column": "center",
        "vertical_alignment_flex_direction_column": "space-between",
        "gap": 32,
        "section_height": "large",
        "color_scheme": "scheme-5",
        "padding-block-start": 0,
        "padding-block-end": 40,
        "toggle_overlay": true,
        "overlay_style": "gradient"
      }
    }
  ]
}
{% endschema %}
