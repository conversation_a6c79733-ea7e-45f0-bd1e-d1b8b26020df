<div x-data="{
  paymentMethod: 'card',
  t: {
    payment: 'Payment',
    paymentType: 'Payment type',
    creditCard: 'Credit card',
    paypal: 'PayPal',
    etransfer: 'eTransfer',
    cardNumber: 'Card number',
    nameOnCard: 'Name on card',
    expirationDate: 'Expiration date (MM/YYYY)',
    cvc: 'CVC',
    paypalRedirect: 'You will be redirected to PayPal to complete your purchase.',
    etransferInfo: 'You will receive instructions for eTransfer after placing your order.',
    payNow: 'Pay now'
  }
}">
  <form class="_payments-form" @submit.prevent>
    <h2 class="text-lg font-medium text-gray-900" x-text="t.payment"></h2>

    <fieldset class="mt-4">
      <legend class="sr-only" x-text="t.paymentType"></legend>
      <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-10 space-y-4 sm:space-y-0">
        <label class="flex items-center cursor-pointer">
          <input id="credit-card" name="payment-type" type="radio" value="card" x-model="paymentMethod"
            class="relative size-4 appearance-none rounded-full border border-gray-300 bg-white checked:border-[var(--accent)] checked:bg-[var(--accent)] focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[var(--accent)] disabled:border-gray-300 disabled:bg-gray-100 mr-3">
          <span class="block text-sm/6 font-medium text-gray-700" x-text="t.creditCard"></span>
        </label>
        <label class="flex items-center cursor-pointer">
          <input id="paypal" name="payment-type" type="radio" value="paypal" x-model="paymentMethod"
            class="relative size-4 appearance-none rounded-full border border-gray-300 bg-white checked:border-[var(--accent)] checked:bg-[var(--accent)] focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[var(--accent)] disabled:border-gray-300 disabled:bg-gray-100 mr-3">
          <span class="block text-sm/6 font-medium text-gray-700" x-text="t.paypal"></span>
        </label>
        <label class="flex items-center cursor-pointer">
          <input id="etransfer" name="payment-type" type="radio" value="etransfer" x-model="paymentMethod"
            class="relative size-4 appearance-none rounded-full border border-gray-300 bg-white checked:border-[var(--accent)] checked:bg-[var(--accent)] focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[var(--accent)] disabled:border-gray-300 disabled:bg-gray-100 mr-3">
          <span class="block text-sm/6 font-medium text-gray-700" x-text="t.etransfer"></span>
        </label>
      </div>
    </fieldset>

    <div class="mt-6 grid grid-cols-4 gap-x-4 gap-y-6" x-show="paymentMethod === 'card'">
      <div class="col-span-4">
        <label for="card-number" class="block text-sm/6 font-medium text-gray-700" x-text="t.cardNumber"></label>
        <div class="mt-2">
          <input type="text" id="card-number" name="card-number" autocomplete="cc-number"
            placeholder="1234 5678 9012 3456"
            x-mask="9999 9999 9999 9999"
            class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6">
        </div>
      </div>

      <div class="col-span-4">
        <label for="name-on-card" class="block text-sm/6 font-medium text-gray-700" x-text="t.nameOnCard"></label>
        <div class="mt-2">
          <input type="text" id="name-on-card" name="name-on-card" autocomplete="cc-name"
            class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6">
        </div>
      </div>

      <div class="col-span-3">
        <label for="expiration-date" class="block text-sm/6 font-medium text-gray-700" x-text="t.expirationDate"></label>
        <div class="mt-2 relative">
          <input
            type="text"
            name="expiration-date"
            id="expiration-date"
            autocomplete="cc-exp"
            placeholder="MM/YYYY"
            x-mask="99/9999"
            class="block w-full rounded-md bg-white px-3 py-2 pr-10 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6">
        </div>
      </div>

      <div class="col-span-1">
        <label for="cvc" class="block text-sm/6 font-medium text-gray-700" x-text="t.cvc"></label>
        <div class="mt-2">
          <input type="text" name="cvc" id="cvc" autocomplete="csc"
            maxlength="4"
            placeholder="CVC"
            class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6">
        </div>
      </div>
    </div>

    <div class="mt-6" x-show="paymentMethod === 'paypal'">
      <p class="text-gray-700" x-text="t.paypalRedirect"></p>
    </div>

    <div class="mt-6" x-show="paymentMethod === 'etransfer'">
      <p class="text-gray-700" x-text="t.etransferInfo"></p>
    </div>

    <button type="submit"
      class="mt-6 w-full rounded-md border border-transparent bg-[var(--accent)] px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-[var(--accent)]/90 focus:ring-2 focus:ring-[var(--accent)] focus:ring-offset-2 focus:outline-hidden"
      x-text="t.payNow">
    </button>
  </form>
</div>
