<form class="_address-form mt-4 grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-4"
      x-data="{
        firstName: '', lastName: '', email:'', address: '', apartment: '', city: '', country: '', region: '', postal: '', phone: '',
        streetNumber: '',
        billingEnabled: false,
        billing: {
          firstName: '', lastName: '', address: '', apartment: '', city: '', country: '', region: '', postal: '', phone: '',
          streetNumber: ''
        },
        error: '',
        isValid() {
          let valid = this.firstName && this.lastName && this.email && this.address && this.city && this.country && this.region && this.postal && this.phone;
          if (this.billingEnabled) {
            valid = valid &&
              this.billing.firstName && this.billing.lastName && this.billing.address &&
              this.billing.city && this.billing.country && this.billing.region &&
              this.billing.postal && this.billing.phone;
          }
          return valid;
        },
        submitAddress() {
          if (this.isValid()) {
            this.error = '';
            this.$dispatch('address-complete');
          } else {
            this.error = 'Please fill all required fields.';
          }
        }
      }"
      @submit.prevent="submitAddress"
      aria-labelledby="address-step-title"
>
    <div class="mb-4 flex items-center gap-2 sm:col-span-2">
      <span class="step-circle">2</span>
      <h2 id="address-step-title" class="text-lg font-medium text-gray-900">Shipping address</h2>
    </div>
    <template x-if="error">
      <div class="mb-2 text-red-600 text-sm sm:col-span-2" x-text="error"></div>
    </template>
    <div class="_checkout-fieldset">
        <label for="first-name" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.first_name' | t | default: 'First name' }} <span class="text-red-500">*</span>
        </label>
        <div class="mt-2">
            <input type="text" id="first-name" name="first-name" autocomplete="given-name"
                   class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                   x-model="firstName">
        </div>
    </div>
    <div class="_checkout-fieldset">
        <label for="last-name" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.last_name' | t | default: 'Last name' }} <span class="text-red-500">*</span>
        </label>
        <div class="mt-2">
            <input type="text" id="last-name" name="last-name" autocomplete="family-name"
                   class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                   x-model="lastName">
        </div>
    </div>
    <div class="_checkout-fieldset sm:col-span-1">
        <label for="email-address" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.email' | t | default: 'Email address' }} <span class="text-red-500">*</span>
        </label>
        <div class="mt-2">
            <input type="email" id="email-address" name="email-address" autocomplete="email"
                class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                x-model="email">
        </div>
    </div>
    <div class="_checkout-fieldset sm:col-span-1">
        <label for="phone" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.phone' | t | default: 'Phone' }} <span class="text-red-500">*</span>
        </label>
        <div class="mt-2">
            <input type="tel" name="phone" id="phone" autocomplete="tel"
                class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                x-model="phone">
        </div>
    </div>

    <div class="_google-maps-container sm:col-span-2">
        <label class="block text-sm/6 font-medium text-gray-700 mb-2">
            {{ 'checkout.set_location_on_map' | t | default: 'Set location on map' }}
        </label>
        <div class="mb-2 flex items-center gap-2">
            <input
                type="text"
                id="google-places-address"
                class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                placeholder="Search address (Google)"
                autocomplete="off"
            >
            <button type="button" id="use-current-location"
                class="rounded-md bg-white p-2 text-gray-700 hover:bg-gray-50 flex items-center justify-center cursor-pointer"
                title="Use current location"
            >
                <i class="fa fa-location-crosshairs"></i>
            </button>
        </div>
        <div id="address-map-container" class="mb-2" style="height: 300px; border-radius: 0.5rem; overflow: hidden;">
            <div id="address-map" style="width: 100%; height: 100%;"></div>
        </div>
    </div>

    <div class="_checkout-fieldset sm:col-span-2">
        <label for="address" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.address' | t | default: 'Address' }} <span class="text-red-500">*</span>
        </label>
        <div class="mt-2 flex gap-2">
            <input type="text" name="address" id="address" autocomplete="street-address"
                class="flex-1 block rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                x-model="address" placeholder="Street name"
                data-places-autocomplete="shipping"
            >
            <input type="text" name="street-number" id="street-number"
                class="w-24 block rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                x-model="streetNumber" placeholder="No.">
        </div>
    </div>
    <div class="_checkout-fieldset sm:col-span-2">
        <label for="apartment" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.apartment' | t | default: 'Apartment, suite, etc.' }}
        </label>
        <div class="mt-2">
            <input type="text" name="apartment" id="apartment"
                   class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                   x-model="apartment">
        </div>
    </div>
    <div class="_checkout-fieldset">
        <label for="city" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.city' | t | default: 'City' }} <span class="text-red-500">*</span>
        </label>
        <div class="mt-2">
            <input type="text" name="city" id="city" autocomplete="address-level2"
                   class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                   x-model="city"
            >
        </div>
    </div>
    <div class="_checkout-fieldset">
        <label for="country" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.country' | t | default: 'Country' }} <span class="text-red-500">*</span>
        </label>
        <div class="mt-2 grid grid-cols-1">
            <input id="country" type="text" name="country" autocomplete="country-name"
                    class="col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-2 pr-8 pl-3 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                    x-model="country"
            />
        </div>
    </div>
    <div class="_checkout-fieldset">
        <label for="region" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.region' | t | default: 'State / Province' }} <span class="text-red-500">*</span>
        </label>
        <div class="mt-2">
            <input type="text" name="region" id="region" autocomplete="address-level1"
                   class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                   x-model="region"
            >
        </div>
    </div>
    <div class="_checkout-fieldset">
        <label for="postal-code" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.postal' | t | default: 'Postal code' }} <span class="text-red-500">*</span>
        </label>
        <div class="mt-2">
            <input type="text" name="postal-code" id="postal-code" autocomplete="postal-code"
                   class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                   x-model="postal">
        </div>
    </div>

    <div class="_checkout-fieldset sm:col-span-2 flex items-center mt-2">
      <input id="billing-enabled" type="checkbox" x-model="billingEnabled"
        class="mr-2 rounded border-gray-300 text-[var(--accent)] focus:ring-[var(--accent)]" />
      <label for="billing-enabled" class="text-sm text-gray-700">
        {{ 'checkout.use_different_billing_address' | t | default: 'Use different billing address' }}
      </label>
    </div>

    <template x-if="billingEnabled">
      <div class="w-full col-span-2 grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-4 bg-gray-50 p-4 rounded">
        <div>
          <label for="billing-first-name" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.billing_first_name' | t | default: 'Billing first name' }} <span class="text-red-500">*</span>
          </label>
          <div class="mt-2">
            <input type="text" id="billing-first-name" name="billing-first-name" autocomplete="given-name"
              class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
              x-model="billing.firstName">
          </div>
        </div>
        <div>
          <label for="billing-last-name" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.billing_last_name' | t | default: 'Billing last name' }} <span class="text-red-500">*</span>
          </label>
          <div class="mt-2">
            <input type="text" id="billing-last-name" name="billing-last-name" autocomplete="family-name"
              class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
              x-model="billing.lastName">
          </div>
        </div>
        <div class="sm:col-span-2">
          <label for="billing-phone" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.billing_phone' | t | default: 'Billing phone' }} <span class="text-red-500">*</span>
          </label>
          <div class="mt-2">
            <input type="tel" id="billing-phone" name="billing-phone" autocomplete="tel"
              class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
              x-model="billing.phone">
          </div>
        </div>

        <div class="_checkout-google-maps-billing-container sm:col-span-2">
          <label class="block text-sm/6 font-medium text-gray-700 mb-2">
              {{ 'checkout.billing_map' | t | default: 'Set billing location on map' }}
          </label>
          <div class="mb-2 flex items-center gap-2">
              <input
                  type="text"
                  id="billing-google-places-address"
                  class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                  placeholder="Search billing address (Google)"
                  autocomplete="off"
              >
              <button type="button" id="billing-use-current-location"
                  class="rounded-md bg-white p-2 text-gray-700 hover:bg-gray-50 flex items-center justify-center cursor-pointer"
                  title="Use current location"
              >
                  <i class="fa fa-location-crosshairs"></i>
              </button>
          </div>
          <div id="billing-address-map-container" class="mb-2" style="height: 300px; border-radius: 0.5rem; overflow: hidden;">
              <div id="billing-address-map" style="width: 100%; height: 100%; min-height: 300px;"></div>
          </div>
        </div>

        <div class="sm:col-span-2">
          <label for="billing-address" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.billing_address' | t | default: 'Billing address' }} <span class="text-red-500">*</span>
          </label>
          <div class="mt-2 flex gap-2">
            <input type="text" id="billing-address" name="billing-address" autocomplete="street-address"
              class="flex-1 block rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
              x-model="billing.address" placeholder="Street name"
              data-places-autocomplete="billing"
            >
            <input type="text" id="billing-street-number" name="billing-street-number"
              class="w-24 block rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
              x-model="billing.streetNumber" placeholder="No.">
          </div>
        </div>
        <div class="sm:col-span-2">
          <label for="billing-apartment" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.billing_apartment' | t | default: 'Billing apartment, suite, etc.' }}
          </label>
          <div class="mt-2">
            <input type="text" id="billing-apartment" name="billing-apartment"
              class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
              x-model="billing.apartment">
          </div>
        </div>
        <div>
          <label for="billing-city" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.billing_city' | t | default: 'Billing city' }} <span class="text-red-500">*</span>
          </label>
          <div class="mt-2">
            <input type="text" id="billing-city" name="billing-city" autocomplete="address-level2"
              class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
              x-model="billing.city"
            >
          </div>
        </div>
        <div>
          <label for="billing-country" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.billing_country' | t | default: 'Billing country' }} <span class="text-red-500">*</span>
          </label>
          <div class="mt-2 grid grid-cols-1">
            <input id="billing-country" type="text" name="billing-country" autocomplete="country-name"
              class="col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-2 pr-8 pl-3 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
              x-model="billing.country"
            />
          </div>
        </div>
        <div>
          <label for="billing-region" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.billing_region' | t | default: 'Billing state / province' }} <span class="text-red-500">*</span>
          </label>
          <div class="mt-2">
            <input type="text" id="billing-region" name="billing-region" autocomplete="address-level1"
              class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
              x-model="billing.region"
            >
          </div>
        </div>
        <div>
          <label for="billing-postal-code" class="block text-sm/6 font-medium text-gray-700">
            {{ 'checkout.billing_postal' | t | default: 'Billing postal code' }} <span class="text-red-500">*</span>
          </label>
          <div class="mt-2">
            <input type="text" id="billing-postal-code" name="billing-postal-code" autocomplete="postal-code"
              class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
              x-model="billing.postal">
          </div>
        </div>
      </div>
    </template>

    <div class="sm:col-span-2">
        <button type="submit"
                class="mt-6 w-full rounded-md border border-transparent bg-[var(--accent)] px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-[var(--accent)]/90 focus:ring-2 focus:ring-[var(--accent)] focus:ring-offset-2 focus:outline-hidden disabled:cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-500"
                :disabled="!isValid()">
            {{ 'checkout.continue' | t | default: 'Continue' }}
        </button>
    </div>
</form>

<script>
window._suppressPlacesDropdown = false;
const setFieldValue = (field, value) => {
    const form = document.querySelector('form[x-data]._address-form');
    if (form && form.__x) {
        if (field in form.__x.$data) {
            form.__x.$data[field] = value;
        } else if (form.__x.$data.billingEnabled && field.startsWith('billing.')) {
            const key = field.replace('billing.', '');
            form.__x.$data.billing[key] = value;
        }
    }
    const input = document.querySelector(`[x-model="${field}"]`);
    if (input) {
        input.blur();
        input.value = value;
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
    }
};
const fillAddressFields = (place, prefix = '') => {
    if (!place?.address_components) return;
    const values = {};
    const addressFields = [
        { field: 'address', types: ['route', 'street_address'] },
        { field: 'streetNumber', types: ['street_number'] },
        { field: 'city', types: ['locality', 'postal_town', 'administrative_area_level_2'] },
        { field: 'region', types: ['administrative_area_level_1'] },
        { field: 'postal', types: ['postal_code'] },
        { field: 'country', types: ['country'] },
    ];
    for (const comp of place.address_components) {
        for (const { field, types } of addressFields) {
            if (types.some(t => comp.types.includes(t))) {
                values[field] = comp.long_name;
            }
        }
    }
    if (values.address && values.streetNumber) {
        setFieldValue(`${prefix}address`, values.address);
        setFieldValue(`${prefix}streetNumber`, values.streetNumber);
    } else if (values.address) {
        setFieldValue(`${prefix}address`, values.address);
    }
    if (values.city) setFieldValue(`${prefix}city`, values.city);
    if (values.region) setFieldValue(`${prefix}region`, values.region);
    if (values.postal) setFieldValue(`${prefix}postal`, values.postal);
    if (values.country) setFieldValue(`${prefix}country`, values.country);
};
const setMapToPlace = (place, mapContext) => {
    if (!place) return;
    let lat, lng;
    if (place.geometry?.location) {
        lat = place.geometry.location.lat();
        lng = place.geometry.location.lng();
    } else if (place.formatted_address && mapContext.geocoder) {
        mapContext.geocoder.geocode({ address: place.formatted_address }, (results, status) => {
            if (status === 'OK' && results[0]?.geometry?.location) {
                const lat = results[0].geometry.location.lat();
                const lng = results[0].geometry.location.lng();
                if (mapContext.marker) mapContext.marker.position = { lat, lng };
                if (mapContext.map) mapContext.map.setCenter({ lat, lng });
            }
        });
        return;
    }
    if (lat !== undefined && lng !== undefined) {
        if (mapContext.marker) mapContext.marker.position = { lat, lng };
        if (mapContext.map) mapContext.map.setCenter({ lat, lng });
    }
};
const initGooglePlacesInput = () => {
    const input = document.getElementById('google-places-address');
    if (input) {
        const autocomplete = new google.maps.places.Autocomplete(input, { types: ['geocode'] });
        window._addressMapContext ??= {};
        autocomplete.addListener('place_changed', () => {
            window._suppressPlacesDropdown = true;
            setTimeout(() => { window._suppressPlacesDropdown = false; }, 300);
            const place = autocomplete.getPlace();
            if (place?.formatted_address) setFieldValue('address', place.formatted_address);
            fillAddressFields(place);
            if (place?.geometry?.location && window._addressMapContext.marker && window._addressMapContext.map) {
                const lat = place.geometry.location.lat();
                const lng = place.geometry.location.lng();
                window._addressMapContext.marker.position = { lat, lng };
                window._addressMapContext.map.setCenter({ lat, lng });
            } else if (place?.formatted_address && window._addressMapContext.geocoder && window._addressMapContext.marker && window._addressMapContext.map) {
                window._addressMapContext.geocoder.geocode({ address: place.formatted_address }, (results, status) => {
                    if (status === 'OK' && results[0]?.geometry?.location) {
                        const lat = results[0].geometry.location.lat();
                        const lng = results[0].geometry.location.lng();
                        window._addressMapContext.marker.position = { lat, lng };
                        window._addressMapContext.map.setCenter({ lat, lng });
                    }
                });
            }
        });
    }
    const billingInput = document.getElementById('billing-google-places-address');
    if (billingInput) {
        const billingAutocomplete = new google.maps.places.Autocomplete(billingInput, { types: ['geocode'] });
        window._billingAddressMapContext ??= {};
        billingAutocomplete.addListener('place_changed', () => {
            window._suppressPlacesDropdown = true;
            setTimeout(() => { window._suppressPlacesDropdown = false; }, 300);
            const place = billingAutocomplete.getPlace();
            if (place?.formatted_address) setFieldValue('billing.address', place.formatted_address);
            fillAddressFields(place, 'billing.');
            if (place?.geometry?.location && window._billingAddressMapContext.marker && window._billingAddressMapContext.map) {
                const lat = place.geometry.location.lat();
                const lng = place.geometry.location.lng();
                window._billingAddressMapContext.marker.position = { lat, lng };
                window._billingAddressMapContext.map.setCenter({ lat, lng });
            } else if (place?.formatted_address && window._billingAddressMapContext.geocoder && window._billingAddressMapContext.marker && window._billingAddressMapContext.map) {
                window._billingAddressMapContext.geocoder.geocode({ address: place.formatted_address }, (results, status) => {
                    if (status === 'OK' && results[0]?.geometry?.location) {
                        const lat = results[0].geometry.location.lat();
                        const lng = results[0].geometry.location.lng();
                        window._billingAddressMapContext.marker.position = { lat, lng };
                        window._billingAddressMapContext.map.setCenter({ lat, lng });
                    }
                });
            }
        });
    }
};
const initMap = () => {
    const mapEl = document.getElementById('address-map');
    if (mapEl) {
        let lat = 42.6977, lng = 23.3219;
        const map = new google.maps.Map(mapEl, {
            center: { lat, lng },
            zoom: 14,
            mapId: 'YOUR_MAP_ID',
        });
        const marker = new google.maps.marker.AdvancedMarkerElement({
            map,
            position: { lat, lng },
            gmpDraggable: true,
        });
        const geocoder = new google.maps.Geocoder();
        window._addressMapContext = { map, marker, geocoder };
        const geocodeLatLng = (lat, lng) => {
            window._suppressPlacesDropdown = true;
            setTimeout(() => { window._suppressPlacesDropdown = false; }, 300);
            geocoder.geocode({ location: { lat, lng } }, (results, status) => {
                if (status === 'OK' && results[0]) {
                    setFieldValue('address', results[0].formatted_address);
                    fillAddressFields(results[0]);
                    const autocompleteInput = document.getElementById('google-places-address');
                    if (autocompleteInput) autocompleteInput.value = results[0].formatted_address;
                }
            });
        };
        marker.addListener('dragend', event => {
            const pos = event.latLng;
            map.panTo(pos);
            geocodeLatLng(pos.lat(), pos.lng());
        });
        document.getElementById('use-current-location').onclick = () => {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(pos => {
                    lat = pos.coords.latitude;
                    lng = pos.coords.longitude;
                    map.setCenter({ lat, lng });
                    marker.position = { lat, lng };
                    geocodeLatLng(lat, lng);
                });
            }
        };
    }
    const tryInitBillingMap = () => {
        const billingMapEl = document.getElementById('billing-address-map');
        if (billingMapEl && !billingMapEl.dataset.mapInitialized) {
            let lat = 42.6977, lng = 23.3219;
            const map = new google.maps.Map(billingMapEl, {
                center: { lat, lng },
                zoom: 14,
                mapId: 'YOUR_MAP_ID',
            });
            const marker = new google.maps.marker.AdvancedMarkerElement({
                map,
                position: { lat, lng },
                gmpDraggable: true,
            });
            const geocoder = new google.maps.Geocoder();
            window._billingAddressMapContext = { map, marker, geocoder };
            const geocodeLatLng = (lat, lng) => {
                window._suppressPlacesDropdown = true;
                setTimeout(() => { window._suppressPlacesDropdown = false; }, 300);
                geocoder.geocode({ location: { lat, lng } }, (results, status) => {
                    if (status === 'OK' && results[0]) {
                        setFieldValue('billing.address', results[0].formatted_address);
                        fillAddressFields(results[0], 'billing.');
                        const autocompleteInput = document.getElementById('billing-google-places-address');
                        if (autocompleteInput) autocompleteInput.value = results[0].formatted_address;
                    }
                });
            };
            marker.addListener('dragend', event => {
                const pos = event.latLng;
                map.panTo(pos);
                geocodeLatLng(pos.lat(), pos.lng());
            });
            document.getElementById('billing-use-current-location').onclick = () => {
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(pos => {
                        lat = pos.coords.latitude;
                        lng = pos.coords.longitude;
                        map.setCenter({ lat, lng });
                        marker.position = { lat, lng };
                        geocodeLatLng(lat, lng);
                    });
                }
            };
            billingMapEl.dataset.mapInitialized = "1";
        }
    };
    const billingCheckbox = document.getElementById('billing-enabled');
    if (billingCheckbox) {
        billingCheckbox.addEventListener('change', () => setTimeout(tryInitBillingMap, 100));
    }
    setTimeout(tryInitBillingMap, 100);
};
const initExtraPlacesAutocomplete = () => {
    const fields = [
        { id: 'address', prefix: '', mapContext: '_addressMapContext' },
        { id: 'city', prefix: '', mapContext: '_addressMapContext' },
        { id: 'region', prefix: '', mapContext: '_addressMapContext' },
        { id: 'country', prefix: '', mapContext: '_addressMapContext' },
        { id: 'billing-address', prefix: 'billing.', mapContext: '_billingAddressMapContext' },
        { id: 'billing-city', prefix: 'billing.', mapContext: '_billingAddressMapContext' },
        { id: 'billing-region', prefix: 'billing.', mapContext: '_billingAddressMapContext' },
        { id: 'billing-country', prefix: 'billing.', mapContext: '_billingAddressMapContext' }
    ];
    const autocompletes = {};
    let focusedFieldId = null;
    const setupAutocomplete = (input, prefix, mapContextKey) => {
        if (!input || input.dataset.placesAttached) return;
        input.dataset.placesAttached = "1";
        const autocomplete = new google.maps.places.Autocomplete(input, { types: ['geocode'] });
        autocompletes[input.id] = autocomplete;
        input.addEventListener('focus', () => {
            focusedFieldId = input.id;
            fields.forEach(f => {
                if (f.id !== input.id) {
                    const otherInput = document.getElementById(f.id);
                    if (otherInput && document.activeElement !== otherInput) otherInput.blur();
                }
            });
        });

        autocomplete.addListener('place_changed', () => {
            if (focusedFieldId !== input.id) return;
            const place = autocomplete.getPlace();
            if (place?.address_components) {
                fillAddressFields(place, prefix);
            }
            if (window[mapContextKey]) setMapToPlace(place, window[mapContextKey]);
        });
    };
    for (const f of fields) setupAutocomplete(document.getElementById(f.id), f.prefix, f.mapContext);
    if (!window._googlePlacesDropdownOutsideHandler) {
        window._googlePlacesDropdownOutsideHandler = true;
        document.addEventListener('mousedown', (e) => {
            let isAutocompleteInput = false;
            for (const f of fields) {
                const input = document.getElementById(f.id);
                if (input && input.contains(e.target)) {
                    isAutocompleteInput = true;
                    break;
                }
            }
            if (!isAutocompleteInput) {
                for (const f of fields) {
                    const input = document.getElementById(f.id);
                }
                focusedFieldId = null;
            }
        });
    }
};
document.addEventListener('DOMContentLoaded', () => {
    if (window._addressMapLoaded) return;
    window._addressMapLoaded = true;
    if (!document.getElementById('intl-tel-input-css')) {
        const link = document.createElement('link');
        link.id = 'intl-tel-input-css';
        link.rel = 'stylesheet';
        link.href = 'https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/25.3.1/build/css/intlTelInput.min.css';
        document.head.appendChild(link);
        const style = document.createElement('style');
        style.textContent = `.iti { display: block; }`;
        document.head.appendChild(style);
    }
    const loadIntlTelInput = callback => {
        if (window.intlTelInput) {
            callback();
            return;
        }
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/25.3.1/build/js/intlTelInput.min.js';
        script.onload = callback;
        document.body.appendChild(script);
    };
    const initIntlTelInputs = () => {
        if (window.intlTelInput) {
            const geoIpLookup = callback => {
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(position => {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        fetch(`https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${GOOGLE_MAPS_API_KEY}`)
                            .then(res => res.json())
                            .then(data => {
                                let country = 'us';
                                if (data.results?.length) {
                                    for (const comp of data.results[0].address_components) {
                                        if (comp.types.includes('country')) {
                                            country = comp.short_name.toLowerCase();
                                            break;
                                        }
                                    }
                                }
                                callback(country);
                            })
                            .catch(() => callback('us'));
                    }, () => callback('us'));
                } else {
                    callback('us');
                }
            };
            const phoneInput = document.getElementById('phone');
            if (phoneInput && !phoneInput.classList.contains('iti-initialized')) {
                window.intlTelInput(phoneInput, {
                    initialCountry: "auto",
                    geoIpLookup,
                    nationalMode: false,
                    utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js"
                });
                phoneInput.classList.add('iti-initialized');
            }
            const billingPhoneInput = document.getElementById('billing-phone');
            if (billingPhoneInput && !billingPhoneInput.classList.contains('iti-initialized')) {
                window.intlTelInput(billingPhoneInput, {
                    initialCountry: "auto",
                    geoIpLookup,
                    nationalMode: false,
                    utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js"
                });
                billingPhoneInput.classList.add('iti-initialized');
            }
        }
    };
    const billingCheckbox = document.getElementById('billing-enabled');
    if (billingCheckbox) {
        billingCheckbox.addEventListener('change', () => setTimeout(() => loadIntlTelInput(initIntlTelInputs), 150));
    }
    loadIntlTelInput(initIntlTelInputs);
    const GOOGLE_MAPS_API_KEY = document.querySelector('meta[name="google-maps-api-key"]').content;
    const loadGoogleMaps = () => {
        if (window.google?.maps?.places) return;
        if (document.getElementById('google-maps-script')) return;
        const script = document.createElement('script');
        script.id = 'google-maps-script';
        script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places,marker&loading=async&callback=initMap`;
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);
    };
    const initMapCallback = async () => {
        await initMap();
        await initExtraPlacesAutocomplete();
        await initGooglePlacesInput();
    };
    loadGoogleMaps();
    const interval = setInterval(() => {        
        if (window.google?.maps?.places) {
            clearInterval(interval);
            initMapCallback();
            const billingCheckbox = document.getElementById('billing-enabled');
            if (billingCheckbox) {
                billingCheckbox.addEventListener('change', () => setTimeout(() => {
                    initMapCallback();
                }, 200));
            }
            const observer = new MutationObserver(() => {
                initExtraPlacesAutocomplete();
            });
            observer.observe(document.body, { childList: true, subtree: true });
        }
    }, 100);
});
</script>
