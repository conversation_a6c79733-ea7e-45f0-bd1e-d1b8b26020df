<div 
  x-data="{
    open: false,
    get cartItems() { return $store?.cartItems?.items; },
  }" 
  x-init="
    open = false; 
    window.openCartSidebar = () => { 
      open = true; 
      document.body.classList.add('overflow-hidden');
    };
    if (!window.cartItems) {
      window.cartItems = [];
    }
  "
  @keydown.escape.window="open = false"
  @open-cart-sidebar.window="open = true; document.body.classList.add('overflow-hidden');"
  x-effect="if (!open) document.body.classList.remove('overflow-hidden')"
  class="_cart-panel hidden"
  :style="open ? 'display: block;' : 'display: none;'"
  x-cloak
>
  <!-- Side panel overlay with modal-like backdrop -->
  <div
    x-show="open"
    class="fixed inset-0 z-40 bg-black opacity-[0.5] transition-opacity"
    @click="open = false; document.body.classList.remove('overflow-hidden')"
    x-cloak
  ></div>

  <!-- Side panel -->
  <aside
    x-show="open"
    x-transition:enter="transition ease-in-out duration-300 transform"
    x-transition:enter-start="translate-x-full"
    x-transition:enter-end="translate-x-0"
    x-transition:leave="transition ease-in-out duration-300 transform"
    x-transition:leave-start="translate-x-0"
    x-transition:leave-end="translate-x-full"
    class="fixed top-0 right-0 z-50 h-full w-full max-w-xl bg-white shadow-xl overflow-y-auto"
    x-cloak
  >
    <div class="flex items-center justify-between px-4 py-4 border-b border-gray-200">
      <h2 class="text-lg font-medium text-gray-900">{{ 'sections.cart.title' | t }}</h2>
      <button type="button" class="text-gray-400 hover:text-gray-600 cursor-pointer" @click="open = false; document.body.classList.remove('overflow-hidden')" aria-label="Close cart">
        <svg class="h-6 w-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
        </svg>
      </button>
    </div>
    <div class="p-4">
      <!-- Render the main cart content inside the side panel -->
      <div>
        {% comment %} <div class="title-wrapper-with-link mb-6">
          <h1 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">{{ 'sections.cart.title' | t }}</h1>
          <a href="/" class="underlined-link text-[var(--accent)] hover:text-indigo-800">
            {{- 'general.continue_shopping' | t -}}
          </a>
        </div> {% endcomment %}
        <form action="{{ routes.cart_url }}" method="post" id="cart-sidepanel" class="mt-12">
          <section aria-labelledby="cart-heading-sidepanel">
            <h2 id="cart-heading-sidepanel" class="sr-only">Items in your shopping cart</h2>
            <template x-if="cartItems && cartItems.length > 0">
              <ul role="list" class="divide-y divide-gray-200 border-t border-b border-gray-200">
                <template x-for="item in cartItems" :key="item.id">
                  <li class="flex py-6 sm:py-10">
                    <div class="shrink-0">
                      <img :src="item.image" :alt="item.product.title" class="size-24 rounded-md object-cover sm:size-48" x-show="item.image">
                    </div>
                    <div class="ml-4 flex flex-1 flex-col justify-between sm:ml-6">
                      <div class="relative pr-9 sm:grid sm:grid-cols-2 sm:gap-x-6 sm:pr-0">
                        <div>
                          <div class="flex justify-between">
                            <h3 class="text-sm">
                              <a :href="item.url" class="font-medium text-gray-700 hover:text-gray-800" x-text="item.product.title"></a>
                            </h3>
                          </div>
                          <div class="mt-1 flex text-sm">
                            <template x-for="option in item.options_with_values" :key="option.value">
                              <p class="text-gray-500" x-text="option.value"></p>
                            </template>
                          </div>
                          <p class="mt-1 text-sm font-medium text-gray-900" x-text="item.original_price !== item.final_price ? ('$' + item.final_price) : ('$' + item.original_price)"></p>
                        </div>
                        <div class="mt-4 sm:mt-0 sm:pr-9">
                          <div class="grid w-full max-w-16 grid-cols-1">
                            <select :name="'updates[]'" :aria-label="'Quantity, ' + item.product.title" class="col-start-1 row-start-1 appearance-none rounded-md bg-white py-1.5 pr-8 pl-3 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6" :value="item.quantity">
                              <template x-for="i in [1,2,3,4,5]">
                                <option :value="i" x-text="i" :selected="item.quantity == i"></option>
                              </template>
                            </select>
                            <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                              <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"/>
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                </template>
              </ul>
            </template>
            <template x-if="!cartItems || cartItems.length === 0">
              <div class="cart__warnings py-12 text-center">
                <h1 class="cart__empty-text text-xl mb-4">{{ 'sections.cart.empty' | t }}</h1>
                <a href="/" class="button bg-[var(--accent)] text-white px-4 py-2 rounded hover:bg-[var(--color-button-primary-hover-bg)]">
                  {{ 'general.continue_shopping' | t }}
                </a>
              </div>
            </template>
          </section>
          <template x-if="cartItems && cartItems.length > 0">
          <section aria-labelledby="summary-heading-sidepanel" class="lg:max-w-md md:float-right w-full mt-8 rounded-lg bg-gray-50 px-4 py-6 sm:p-6">
            <h2 id="summary-heading-sidepanel" class="text-lg font-medium text-gray-900">{{ 'sections.cart.summary' | t }}</h2>
            <dl class="mt-6 space-y-4">
              <div class="flex items-center justify-between">
                <dt class="text-sm text-gray-600">{{ 'sections.cart.subtotal' | t }}</dt>
                <dd class="text-sm font-medium text-gray-900">{{ cart.total_price | money }}</dd>
              </div>
              {%- if cart.shipping_price -%}
              <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                <dt class="flex items-center text-sm text-gray-600">
                  <span>{{ 'sections.cart.shipping_estimate' | t }}</span>
                  <a href="#" class="ml-2 shrink-0 text-gray-400 hover:text-gray-500">
                    <span class="sr-only">{{ 'sections.cart.shipping_info' | t }}</span>
                    <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0ZM8.94 6.94a.75.75 0 1 1-1.061-1.061 3 3 0 1 1 2.871 5.026v.345a.75.75 0 0 1-1.5 0v-.5c0-.72.57-1.172 1.081-1.287A1.5 1.5 0 1 0 8.94 6.94ZM10 15a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" clip-rule="evenodd"/>
                    </svg>
                  </a>
                </dt>
                <dd class="text-sm font-medium text-gray-900">{{ cart.shipping_price | money }}</dd>
              </div>
              {%- endif -%}
              {%- if cart.tax_price -%}
              <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                <dt class="flex text-sm text-gray-600">
                  <span>{{ 'sections.cart.tax_estimate' | t }}</span>
                  <a href="#" class="ml-2 shrink-0 text-gray-400 hover:text-gray-500">
                    <span class="sr-only">{{ 'sections.cart.tax_info' | t }}</span>
                    <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0ZM8.94 6.94a.75.75 0 1 1-1.061-1.061 3 3 0 1 1 2.871 5.026v.345a.75.75 0 0 1-1.5 0v-.5c0-.72.57-1.172 1.081-1.287A1.5 1.5 0 1 0 8.94 6.94ZM10 15a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" clip-rule="evenodd"/>
                    </svg>
                  </a>
                </dt>
                <dd class="text-sm font-medium text-gray-900">{{ cart.tax_price | money }}</dd>
              </div>
              {%- endif -%}
              <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                <dt class="text-base font-medium text-gray-900">{{ 'sections.cart.total' | t }}</dt>
                <dd class="text-base font-medium text-gray-900">{{ cart.total_price | money }}</dd>
              </div>
            </dl>
            <div class="mt-6">
              <button type="submit" class="w-full rounded-full border border-transparent bg-[var(--accent)] px-4 py-3 text-base font-medium text-white shadow-xs hover:bg-[var(--color-button-primary-hover-bg)] focus:ring-2 focus:ring-[var(--accent)] focus:ring-offset-2 focus:ring-offset-gray-50 focus:outline-hidden cursor-pointer">
                {{ 'sections.cart.checkout' | t }}
              </button>
            </div>
          </section>
          </template>
        </form>
      </div>
    </div>
  </aside>
</div>
