<div
  class="_checkout-cart flow-root"
  aria-label="Order summary"
>
  <ul role="list" class="-my-6 divide-y divide-gray-200" id="checkout-cart-list">
    <template x-if="cartProducts.length === 0">
      <li class="py-6 text-center text-gray-500">Your cart is empty.</li>
    </template>
    <template x-for="item in cartProducts" :key="item.id">
      <li class="flex space-x-6 py-6 border-b border-gray-200" :data-item-id="item.id">
        <img :src="item.image" :alt="item.product.title" class="size-24 flex-none rounded-md bg-gray-100 object-cover">
        <div class="flex-auto">
          <div class="space-y-1 sm:flex sm:items-start sm:justify-between sm:space-x-6">
            <div class="flex-auto space-y-1 text-sm font-medium">
              <h3 class="text-gray-900">
                <a :href="item.url" x-text="item.product.title"></a>
              </h3>
              <p class="text-gray-900" x-text="`$${(item.final_price * item.quantity).toFixed(2)}`"></p>
              <template x-for="option in item.options_with_values || []" :key="option.value">
                <p class="hidden text-gray-500 sm:block" x-text="option.value"></p>
              </template>
              <p class="hidden text-gray-500 sm:block" x-text="`Qty: ${item.quantity}`"></p>
            </div>
            <div class="flex flex-none space-x-4">
              <button type="button"
                class="text-sm font-medium text-[var(--accent)] hover:text-[var(--accent)]/90 open-cart-sidebar-btn"
                @click="window.openCartSidebar && window.openCartSidebar()"
                :data-item-id="item.id"
              >Edit</button>
              <div class="flex border-l border-gray-300 pl-4">
                <button type="button" class="text-sm font-medium text-[var(--accent)] hover:text-[var(--accent)]/90 remove-cart-item-btn"
                  @click="cartProducts = cartProducts.filter(i => i.id !== item.id); if(cartProducts.length === 0) window.location.href = '/'"
                  :data-item-id="item.id"
                >Remove</button>
              </div>
            </div>
          </div>
        </div>
      </li>
    </template>
  </ul>
</div>
<div id="cart-sidebar-render"></div>
{% render 'cart-sidebar' %}