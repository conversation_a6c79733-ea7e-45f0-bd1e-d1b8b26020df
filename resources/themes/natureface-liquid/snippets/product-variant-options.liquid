{% comment %}
  Renders product variant options

  Accepts:
  - product: {Object} product object.
  - option: {Object} current product_option object.
  - block: {Object} block object.
  - picker_type: {String} type of picker to dispay


  Usage:
  {% render 'product-variant-options',
    product: product,
    option: option,
    block: block
    picker_type: picker_type
  %}
{% endcomment %}
{%- liquid
  assign variants_available_arr = product.variants | map: 'available'
  assign variants_option1_arr = product.variants | map: 'option1'
  assign variants_option2_arr = product.variants | map: 'option2'
  assign variants_option3_arr = product.variants | map: 'option3'

  assign product_form_id = 'product-form-' | append: section.id
-%}

{%- for value in option.values -%}
  {%- liquid
    assign option_disabled = true

    for option1_name in variants_option1_arr
      case option.position
        when 1
          if variants_option1_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
            assign option_disabled = false
          endif
        when 2
          if option1_name == product.selected_or_first_available_variant.option1 and variants_option2_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
            assign option_disabled = false
          endif
        when 3
          if option1_name == product.selected_or_first_available_variant.option1 and variants_option2_arr[forloop.index0] == product.selected_or_first_available_variant.option2 and variants_option3_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
            assign option_disabled = false
          endif
      endcase
    endfor

    if value.swatch.image
      assign image_url = value.swatch.image | image_url: width: 50
      assign swatch_value = 'url(' | append: image_url | append: ')'
    elsif value.swatch.color
      assign swatch_value = 'rgb(' | append: value.swatch.color.rgb | append: ')'
    else
      assign swatch_value = nil
    endif
  -%}

  {%- capture input_id -%}
    {{ section.id }}-{{ option.position }}-{{ forloop.index0 -}}
  {%- endcapture -%}

  {%- capture label_unavailable %}
    <span class="visually-hidden label-unavailable">
      {{- 'products.product.variant_sold_out_or_unavailable' | t -}}
    </span>
  {%- endcapture %}

  {%- if picker_type == 'swatch' -%}
    {% assign checked = false %}
    {% if option.selected_value == value %}
      {% assign checked = true %}
    {% endif %}
    {%
      render 'swatch-input',
      id: input_id,
      name: option.name,
      value: value | escape,
      product_form_id: product_form_id,
      checked: checked,
      disabled: option_disabled,
      shape: block.settings.swatch_shape,
      help_text: label_unavailable
    %}
  {%- elsif picker_type == 'button' -%}
    <input
      type="radio"
      id="{{ input_id }}"
      name="{{ option.name }}"
      value="{{ value | escape }}"
      form="{{ product_form_id }}"
      {% if option.selected_value == value %}
        checked
      {% endif %}
      {% if option_disabled %}
        class="disabled"
      {% endif %}
    >
    <label for="{{ input_id }}">
      {{ value -}}
      {{ label_unavailable }}
    </label>
  {%- elsif picker_type == 'dropdown' or picker_type == 'swatch_dropdown' -%}
    <option
      value="{{ value | escape }}"
      {% if option.selected_value == value %}
        selected="selected"
      {% endif %}
      {% if swatch_value and picker_type == 'swatch_dropdown' %}
        data-option-swatch-value="{{ swatch_value }}"
      {% endif %}
    >
      {% if option_disabled -%}
        {{- 'products.product.value_unavailable' | t: option_value: value -}}
      {%- else -%}
        {{- value -}}
      {%- endif %}
    </option>
  {%- endif -%}
{%- endfor -%}
