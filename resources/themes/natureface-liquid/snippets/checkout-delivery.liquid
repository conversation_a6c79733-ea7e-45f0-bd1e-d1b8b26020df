<form class="_delivery-form pb-4"
    x-data="{
        provider: '',
        delivery: '',
        error: '',
        submitDelivery() {
            if (this.provider && this.delivery) {
                this.error = '';
                this.$dispatch('delivery-complete');
            } else {
                this.error = 'Please select a shipping provider and delivery method.';
            }
        }
    }"
    @submit.prevent="submitDelivery"
    aria-labelledby="delivery-step-title"
>
    <div class="mb-4 flex items-center gap-2">
      <span class="step-circle">3</span>
      <h2 id="delivery-step-title" class="text-lg font-medium text-gray-900">Delivery method</h2>
    </div>
    <template x-if="error">
      <div class="mb-2 text-red-600 text-sm" x-text="error"></div>
    </template>

    <!-- Shipping Provider Selection -->
    <fieldset aria-label="Shipping provider" class="mt-4 mb-6">
        <legend class="block text-sm font-medium text-gray-900 mb-2">Shipping provider</legend>
        <div class="flex gap-4">
            <label class="flex items-center cursor-pointer">
                <input type="radio" name="shipping-provider" value="econt" class="sr-only" x-model="provider">
                <span class="flex items-center px-3 py-2 rounded border"
                    :class="provider === 'econt' ? 'border-[var(--accent)] ring-2 ring-[var(--accent)]' : 'border-gray-300'">
                    <span class="mr-2">
                        <svg x-show="provider === 'econt'" class="size-4 text-[var(--accent)]" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.857-9.809a.75.75 0 0 0-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z" clip-rule="evenodd" /></svg>
                    </span>
                    Econt
                </span>
            </label>
            <label class="flex items-center cursor-pointer">
                <input type="radio" name="shipping-provider" value="speedy" class="sr-only" x-model="provider">
                <span class="flex items-center px-3 py-2 rounded border"
                    :class="provider === 'speedy' ? 'border-[var(--accent)] ring-2 ring-[var(--accent)]' : 'border-gray-300'">
                    <span class="mr-2">
                        <svg x-show="provider === 'speedy'" class="size-4 text-[var(--accent)]" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.857-9.809a.75.75 0 0 0-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z" clip-rule="evenodd" /></svg>
                    </span>
                    Speedy
                </span>
            </label>
            <label class="flex items-center cursor-pointer">
                <input type="radio" name="shipping-provider" value="sameday" class="sr-only" x-model="provider">
                <span class="flex items-center px-3 py-2 rounded border"
                    :class="provider === 'sameday' ? 'border-[var(--accent)] ring-2 ring-[var(--accent)]' : 'border-gray-300'">
                    <span class="mr-2">
                        <svg x-show="provider === 'sameday'" class="size-4 text-[var(--accent)]" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.857-9.809a.75.75 0 0 0-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z" clip-rule="evenodd" /></svg>
                    </span>
                    Sameday
                </span>
            </label>
        </div>
    </fieldset>

    <fieldset aria-label="Delivery method" class="mt-4">
        <div class="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-4">
        <!-- Standard option -->
        <label
            aria-label="Standard"
            aria-description="4–10 business days for $5.00"
            class="relative flex cursor-pointer rounded-lg border bg-white p-4 shadow-xs focus:outline-hidden"
            :class="delivery === 'standard' ? 'border-[var(--accent)] ring-2 ring-[var(--accent)]' : 'border-gray-300'"
        >
            <input type="radio" name="delivery-method" value="standard" class="sr-only" x-model="delivery">
            <span class="flex flex-1">
                <span class="flex flex-col">
                    <span class="block text-sm font-medium text-gray-900">Standard</span>
                    <span class="mt-1 flex items-center text-sm text-gray-500">4–10 business days</span>
                    <span class="mt-6 text-sm font-medium text-gray-900">$5.00</span>
                </span>
            </span>
            <svg x-show="delivery === 'standard'" class="size-5 text-[var(--accent)]" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.857-9.809a.75.75 0 0 0-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z" clip-rule="evenodd" />
            </svg>
        </label>
        <!-- Express option -->
        <label
            aria-label="Express"
            aria-description="2–5 business days for $16.00"
            class="relative flex cursor-pointer rounded-lg border bg-white p-4 shadow-xs focus:outline-hidden"
            :class="delivery === 'express' ? 'border-[var(--accent)] ring-2 ring-[var(--accent)]' : 'border-gray-300'"
        >
            <input type="radio" name="delivery-method" value="express" class="sr-only" x-model="delivery">
            <span class="flex flex-1">
                <span class="flex flex-col">
                    <span class="block text-sm font-medium text-gray-900">Express</span>
                    <span class="mt-1 flex items-center text-sm text-gray-500">2–5 business days</span>
                    <span class="mt-6 text-sm font-medium text-gray-900">$16.00</span>
                </span>
            </span>
            <svg x-show="delivery === 'express'" class="size-5 text-[var(--accent)]" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.857-9.809a.75.75 0 0 0-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z" clip-rule="evenodd" />
            </svg>
        </label>
        </div>
    </fieldset>
    <button
        type="button"
        class="mt-6 w-full rounded-md border border-transparent bg-[var(--accent)] px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-[var(--accent)]/90 focus:ring-2 focus:ring-[var(--accent)] focus:ring-offset-2 focus:outline-hidden disabled:cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-500"
        :disabled="!provider || !delivery"
        @click="submitDelivery"
    >
        Continue
    </button>
</form>
