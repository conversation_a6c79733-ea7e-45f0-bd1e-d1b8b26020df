<div x-data="{
    email: '',
    phone: '',
    terms: false,
    error: '',
    isValid() {
        return this.email && this.phone && this.terms;
    },
    submitContacts() {
        if (this.isValid()) {
            this.error = '';
            this.$dispatch('contacts-complete');
        } else {
            this.error = 'Please fill all required fields and accept the terms.';
        }
    }
}">
    <form class="mt-6" @submit.prevent="submitContacts" aria-labelledby="contacts-step-title">
        <div class="mb-4 flex items-center gap-2">
          <span class="step-circle">1</span>
          <h2 id="contacts-step-title" class="text-lg font-medium text-gray-900">Contact information</h2>
        </div>
        <template x-if="error">
          <div class="mb-2 text-red-600 text-sm" x-text="error"></div>
        </template>
        <div class="mt-6">
            <label for="email-address" class="block text-sm/6 font-medium text-gray-700">
                Email address <span class="text-red-500">*</span>
            </label>
            <div class="mt-2">
                <input type="email" id="email-address" name="email-address" autocomplete="email"
                    class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                    x-model="email">
            </div>
        </div>
        <div class="mt-6">
            <label for="phone" class="block text-sm/6 font-medium text-gray-700">
                Phone number <span class="text-red-500">*</span>
            </label>
            <div class="mt-2">
                <input type="text" name="phone" id="phone" autocomplete="tel"
                    class="block w-full rounded-md bg-white px-3 py-2 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-[var(--accent)] sm:text-sm/6"
                    x-model="phone">
            </div>
        </div>
        <div class="mt-6 flex gap-3">
            <div class="flex h-5 shrink-0 items-center">
                <div class="group grid size-4 grid-cols-1">
                    <input id="terms" name="terms" type="checkbox"
                        class="col-start-1 row-start-1 appearance-none rounded-sm border border-gray-300 bg-white checked:border-[var(--accent)] checked:bg-[var(--accent)] indeterminate:border-[var(--accent)] indeterminate:bg-[var(--accent)] focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[var(--accent)] disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"
                        x-model="terms">
                    <svg class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-disabled:stroke-gray-950/25"
                        viewBox="0 0 14 14" fill="none">
                        <path class="opacity-0 group-has-checked:opacity-100" d="M3 8L6 11L11 3.5"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        <path class="opacity-0 group-has-indeterminate:opacity-100" d="M3 7H11"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </div>
            </div>
            <label for="terms" class="text-sm text-gray-500">
                <span class="text-red-500">*</span>
                I have read the terms and conditions and agree to the sale of my personal information to the highest bidder.
            </label>
        </div>
        <button type="submit"
            class="mt-6 w-full rounded-md border border-transparent bg-[var(--accent)] px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-[var(--accent)]/90 focus:ring-2 focus:ring-[var(--accent)] focus:ring-offset-2 focus:outline-hidden disabled:cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-500"
            :disabled="!isValid()"
        >
            Continue
        </button>
    </form>
</div>