{% comment %}
  Renders a swatch input component.
  Accepts:
  - id: {String} unique input id
  - name: {String} input name,
  - value: {ProductOptionValueDrop} input value,
  - product_form_id: {String} id of the form associted with the input
  - checked: {<PERSON><PERSON>an} default checked status
  - disabled: {<PERSON><PERSON><PERSON>} default disabled status
  - shape: {String} swatch shape. Accepts 'square', defaults to circle.
  - help_text: {String} additional content to render inside the label

  Usage:
   {% render 'swatch-input',
      id: input_id,
      name: input_name,
      value: input_value,
      product_form_id: product_form_id,
      checked: checked,
      disabled: disabled,
      shape: 'square'
      help_text: 'help text'
    %}
{% endcomment %}

<input
  type="radio"
  id="{{ id }}"
  name="{{ name }}"
  value="{{ value }}"
  form="{{ product_form_id }}"
  class="swatch-input__input{% if disabled %} disabled{% endif %}"
  {% if checked %}
    checked
  {% endif %}
>
<label
  for="{{ id }}"
  title="{{ value }}"
  class="swatch-input__label{% if shape == 'square' %} swatch-input__label--square{% endif %}"
>
  {% render 'swatch', swatch: value.swatch, shape: shape %}
  <span class="visually-hidden">{{ value }}</span>
  {{ help_text }}
</label>
