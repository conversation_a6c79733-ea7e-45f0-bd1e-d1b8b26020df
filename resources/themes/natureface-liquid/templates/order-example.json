{"order": ["header", "main", "footer"], "sections": {"header": {"type": "header", "settings": {}}, "main": {"type": "main-order", "settings": {"padding_top": 36, "padding_bottom": 36, "line_items": [{"title": "Sample Product A", "url": "/products/sample-product-a", "sku": "SKU-A", "quantity": 2, "final_price": 19.99, "original_price": 24.99, "unit_price": 9.99, "unit_price_measurement": null, "original_line_price": 49.98, "final_line_price": 39.98, "properties": [], "line_level_discount_allocations": [{"discount_application": {"title": "Spring Sale"}, "amount": 10.0}], "fulfillment": {"created_at": "2024-06-02T10:00:00Z", "tracking_url": "https://track.example.com/123", "tracking_company": "DHL", "tracking_number": "*********"}, "product": {"has_only_default_variant": true}, "variant": {"title": "<PERSON><PERSON><PERSON>"}, "selling_plan_allocation": null}], "order": {"name": "#1001", "created_at": "2024-06-01T12:34:56Z", "cancelled": false, "cancelled_at": null, "cancel_reason_label": null, "line_items": [{"title": "Sample Product A", "url": "/products/sample-product-a", "sku": "SKU-A", "quantity": 2, "final_price": 19.99, "original_price": 24.99, "unit_price": 9.99, "unit_price_measurement": null, "original_line_price": 49.98, "final_line_price": 39.98, "properties": [], "line_level_discount_allocations": [{"discount_application": {"title": "Spring Sale"}, "amount": 10.0}], "fulfillment": {"created_at": "2024-06-02T10:00:00Z", "tracking_url": "https://track.example.com/123", "tracking_company": "DHL", "tracking_number": "*********"}, "product": {"has_only_default_variant": true}, "variant": {"title": "<PERSON><PERSON><PERSON>"}, "selling_plan_allocation": null}], "line_items_subtotal_price": 39.98, "cart_level_discount_applications": [{"title": "Spring Sale", "total_allocated_amount": 10.0}], "shipping_methods": [{"title": "Standard Shipping", "price": 5.0}], "tax_lines": [{"title": "VAT", "rate": 0.2, "price": 8.0}], "total_duties": 0, "total_refunded_amount": 0, "total_net_amount": 52.98, "financial_status_label": "Paid", "fulfillment_status_label": "Shipped", "billing_address": "<PERSON><br>123 Main St<br>City, Country", "shipping_address": "<PERSON><br>123 Main St<br>City, Country"}}}, "footer": {"type": "footer", "settings": {}}}}