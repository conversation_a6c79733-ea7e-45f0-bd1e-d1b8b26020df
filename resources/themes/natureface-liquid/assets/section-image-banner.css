.banner {
  display: flex;
  position: relative;
  flex-direction: column;
  z-index: auto;
  isolation: isolate;
}

.banner__box {
  text-align: center;
}

/* Needed for gradient continuity with or without animation, the transform scopes the gradient to its container which happens already when animation are turned on */
.banner__box.gradient {
  transform: perspective(0);
}

@media only screen and (max-width: 749px) {
  .banner--content-align-mobile-right .banner__box {
    text-align: right;
  }

  .banner--content-align-mobile-left .banner__box {
    text-align: left;
  }
}

@media only screen and (min-width: 750px) {
  .banner--content-align-right .banner__box {
    text-align: right;
  }

  .banner--content-align-left .banner__box {
    text-align: left;
  }

  .banner--content-align-left.banner--desktop-transparent .banner__box,
  .banner--content-align-right.banner--desktop-transparent .banner__box,
  .banner--medium.banner--desktop-transparent .banner__box {
    max-width: 68rem;
  }
}

.banner__media.animate--zoom-in {
  clip-path: inset(0px);
}

.banner__media.animate--zoom-in > img:not(.zoom):not(.deferred-media__poster-button),
.banner__media.animate--zoom-in > svg:not(.zoom):not(.deferred-media__poster-button) {
  position: fixed;
  height: 100vh;
}

@media screen and (max-width: 749px) {
  .banner--small.banner--mobile-bottom:not(.banner--adapt) .banner__media,
  .banner--small.banner--stacked:not(.banner--mobile-bottom):not(.banner--adapt) > .banner__media {
    height: 28rem;
  }

  .banner--medium.banner--mobile-bottom:not(.banner--adapt) .banner__media,
  .banner--medium.banner--stacked:not(.banner--mobile-bottom):not(.banner--adapt) > .banner__media {
    height: 34rem;
  }

  .banner--large.banner--mobile-bottom:not(.banner--adapt) .banner__media,
  .banner--large.banner--stacked:not(.banner--mobile-bottom):not(.banner--adapt) > .banner__media {
    height: 39rem;
  }

  .banner--small:not(.banner--mobile-bottom):not(.banner--adapt) .banner__content {
    min-height: 28rem;
  }

  .banner--medium:not(.banner--mobile-bottom):not(.banner--adapt) .banner__content {
    min-height: 34rem;
  }

  .banner--large:not(.banner--mobile-bottom):not(.banner--adapt) .banner__content {
    min-height: 39rem;
  }
}

@media screen and (min-width: 750px) {
  .banner {
    flex-direction: row;
  }

  .banner--small:not(.banner--adapt) {
    min-height: 42rem;
  }

  .banner--medium:not(.banner--adapt) {
    min-height: 56rem;
  }

  .banner--large:not(.banner--adapt) {
    min-height: 72rem;
  }

  .banner__content.banner__content--top-left {
    align-items: flex-start;
    justify-content: flex-start;
  }

  .banner__content.banner__content--top-center {
    align-items: flex-start;
    justify-content: center;
  }

  .banner__content.banner__content--top-right {
    align-items: flex-start;
    justify-content: flex-end;
  }

  .banner__content.banner__content--middle-left {
    align-items: center;
    justify-content: flex-start;
  }

  .banner__content.banner__content--middle-center {
    align-items: center;
    justify-content: center;
  }

  .banner__content.banner__content--middle-right {
    align-items: center;
    justify-content: flex-end;
  }

  .banner__content.banner__content--bottom-left {
    align-items: flex-end;
    justify-content: flex-start;
  }

  .banner__content.banner__content--bottom-center {
    align-items: flex-end;
    justify-content: center;
  }

  .banner__content.banner__content--bottom-right {
    align-items: flex-end;
    justify-content: flex-end;
  }
}

@media screen and (max-width: 749px) {
  .banner:not(.banner--stacked) {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .banner--stacked {
    height: auto;
  }

  .banner--stacked .banner__media {
    flex-direction: column;
  }
}

.banner__media {
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
}

.banner__media-half {
  width: 50%;
}

.banner__media-half + .banner__media-half {
  right: 0;
  left: auto;
}

.banner__media-half.animate--fixed:first-child > img,
.banner__media-half.animate--zoom-in:first-child > img {
  width: 50%;
}

.banner__media-half.animate--fixed:nth-child(2) > img,
.banner__media-half.animate--zoom-in:nth-child(2) > img {
  left: 50%;
  width: 50%;
}

@media screen and (max-width: 749px) {
  .banner--stacked .animate--fixed:first-child > img,
  .banner--stacked .animate--zoom-in:first-child > img {
    width: 100%;
  }

  .banner--stacked .banner__media-half.animate--fixed:nth-child(2) > img,
  .banner--stacked .banner__media-half.animate--zoom-in:nth-child(2) > img {
    left: 0;
    width: 100%;
  }

  .banner--stacked .banner__media-half {
    width: 100%;
  }

  .banner--stacked .banner__media-half + .banner__media-half {
    order: 1;
  }
}

@media screen and (min-width: 750px) {
  .banner__media {
    height: 100%;
  }
}

.banner--adapt,
.banner--adapt_image.banner--mobile-bottom .banner__media:not(.placeholder) {
  height: auto;
}

@media screen and (max-width: 749px) {
  .banner--mobile-bottom .banner__media,
  .banner--stacked:not(.banner--mobile-bottom) .banner__media {
    position: relative;
  }

  .banner--stacked.banner--adapt .banner__content {
    height: auto;
  }

  .banner:not(.banner--mobile-bottom):not(.email-signup-banner) .banner__box {
    background: transparent;
  }

  .banner:not(.banner--mobile-bottom) .banner__box {
    border: none;
    border-radius: 0;
    box-shadow: none;
  }

  .banner:not(.banner--mobile-bottom) .button--secondary {
    --alpha-button-background: 0;
  }

  .banner--stacked:not(.banner--mobile-bottom):not(.banner--adapt) .banner__content {
    position: absolute;
    height: auto;
  }

  .banner--stacked.banner--adapt:not(.banner--mobile-bottom) .banner__content {
    max-height: 100%;
    overflow: hidden;
    position: absolute;
  }

  .banner--stacked:not(.banner--adapt) .banner__media {
    position: relative;
  }

  .banner::before {
    display: none !important;
  }

  .banner--stacked .banner__media-image-half {
    width: 100%;
  }
}

.banner__content {
  padding: 0;
  display: flex;
  position: relative;
  width: 100%;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

@media screen and (min-width: 750px) {
  .banner__content {
    padding: 5rem;
  }

  .banner__content--top-left {
    align-items: flex-start;
    justify-content: flex-start;
  }

  .banner__content--top-center {
    align-items: flex-start;
    justify-content: center;
  }

  .banner__content--top-right {
    align-items: flex-start;
    justify-content: flex-end;
  }

  .banner__content--middle-left {
    align-items: center;
    justify-content: flex-start;
  }

  .banner__content--middle-center {
    align-items: center;
    justify-content: center;
  }

  .banner__content--middle-right {
    align-items: center;
    justify-content: flex-end;
  }

  .banner__content--bottom-left {
    align-items: flex-end;
    justify-content: flex-start;
  }

  .banner__content--bottom-center {
    align-items: flex-end;
    justify-content: center;
  }

  .banner__content--bottom-right {
    align-items: flex-end;
    justify-content: flex-end;
  }
}

@media screen and (max-width: 749px) {
  .banner--mobile-bottom:not(.banner--stacked) .banner__content {
    order: 2;
  }

  .banner:not(.banner--mobile-bottom) .field__input,
  .banner--mobile-bottom:not(.banner--stacked) .banner__box.color-scheme-1 {
    background: transparent;
  }
}

.banner__box {
  padding: 4rem 3.5rem;
  position: relative;
  height: fit-content;
  align-items: center;
  text-align: center;
  width: 100%;
  word-wrap: break-word;
  z-index: 1;
}

@media screen and (min-width: 750px) {
  .banner--desktop-transparent .banner__box {
    background: transparent;
    max-width: 89rem;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }

  .banner--desktop-transparent .button--secondary {
    --alpha-button-background: 0;
  }

  .banner--desktop-transparent .content-container:after {
    display: none;
  }
}

@media screen and (max-width: 749px) {
  .banner--mobile-bottom::after,
  .banner--mobile-bottom .banner__media::after {
    display: none;
  }
}

.banner::after,
.banner__media::after {
  content: '';
  position: absolute;
  top: 0;
  background: #000000;
  opacity: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
}

.banner__box > * + .banner__text {
  margin-top: 1.5rem;
}

@media screen and (min-width: 750px) {
  .banner__box > * + .banner__text {
    margin-top: 2rem;
  }
}

.banner__box > * + * {
  margin-top: 1rem;
}

.banner__box > *:first-child {
  margin-top: 0;
}

@media screen and (max-width: 749px) {
  .banner--stacked .banner__box {
    width: 100%;
  }
}

@media screen and (min-width: 750px) {
  .banner__box {
    width: auto;
    max-width: 71rem;
    min-width: 45rem;
  }
}

@media screen and (min-width: 1400px) {
  .banner__box {
    max-width: 90rem;
  }
}

.banner__heading {
  margin-bottom: 0;
}

.banner__box .banner__heading + * {
  margin-top: 1rem;
}

.banner__buttons {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 1rem;
  max-width: 45rem;
  word-break: break-word;
}

@media screen and (max-width: 749px) {
  .banner--content-align-mobile-right .banner__buttons--multiple {
    justify-content: flex-end;
  }

  .banner--content-align-mobile-center .banner__buttons--multiple > * {
    flex-grow: 1;
    min-width: 22rem;
  }
}

@media screen and (min-width: 750px) {
  .banner--content-align-center .banner__buttons--multiple > * {
    flex-grow: 1;
    min-width: 22rem;
  }

  .banner--content-align-right .banner__buttons--multiple {
    justify-content: flex-end;
  }
}

.banner__box > * + .banner__buttons {
  margin-top: 2rem;
}

@media screen and (max-width: 749px) {
  .banner:not(.slideshow) .rte a,
  .banner:not(.slideshow) .inline-richtext a:hover,
  .banner:not(.slideshow) .rte a:hover {
    color: currentColor;
  }
}

@media screen and (min-width: 750px) {
  .banner--desktop-transparent .rte a,
  .banner--desktop-transparent .inline-richtext a:hover,
  .banner--desktop-transparent .rte a:hover {
    color: currentColor;
  }
}



/* --------------------------------------- new css -------------------------------------- */
.banner__text p {
    font-size: 25px;
}
h2.banner__heading {
    font-size: 56px;
    font-weight: bold;
}
h2.banner__heading {
    font-size: 56px;
    font-weight: bold;
    margin-top: 1rem;
    line-height: 1.5;
}
.banner__buttons .button {
    background: transparent;
    color: #21332B;
    font-size: 18px;
    border: none;
    top: -5px;
    left: 7px;
    padding: 0;
    text-decoration: none;
}
.banner__buttons .button:before, .banner__buttons .button:after{ display:none;}
.banner__buttons:before {
    content: "";
    position: absolute;
    width: 35px;
    height: 35px;
    background-color: #FFC300;
    border-radius: 50%;
}
.banner__buttons i {
    font-size: 13px;
    color: #9d8a6f;
    position: relative;
    top: 2px;
    left: 5px;
}
.percent_box span {
    font-size: 157px;
    font-weight: 700;
    color: #21332b;
    font-family: poppins;
}
.percent_box sup {
    color: #ffc300;
    font-size: 31px;
    top: -85px;
    font-weight: 500;
}
.percent_box sub {
    bottom: -.25em;
    font-size: 71px;
    font-weight: bold;
    color: #21332B;
}
.percent_box p {
    color: #ffc300;
    font-size: 35px;
    letter-spacing: 5px;
    position: relative;
    top: -36px;
    right: -101px;
    font-weight: 500;
}
.percent_box span:before {
    content: "";
    position: absolute;
    width: 230px;
    height: 230px;
    background-color: #ebebeb;
    border-radius: 50%;
    z-index: -1;
    top: 4.5rem;
    right: 4.3rem;
}
.percent_box {
    transform: rotate(354deg);
    position: absolute;
    top: 28rem;
    right: 30rem;
    height: 0rem;
}

@media screen and (max-width: 1440px) {
  .banner__buttons:after{ top:-6px;}
}

@media screen and (max-width: 1366px) {
.percent_box {
    top: 27rem;
    right: 21rem;
}

}

@media screen and (min-width: 1051px) and (max-width: 1199px) {
  .percent_box {
    top: 25rem;
    right: 7rem;
}
}


@media screen and (max-width: 1050px) {
  .percent_box{top: 26rem;    right: 20rem;    height: 0rem;}
  .percent_box span:before{ width: 200px;    height: 200px;top: 3.5rem;    right: 3.3rem;}
  h2.banner__heading{ font-size: 45px; line-height:1;}
 .percent_box {
    top: 35rem;
    right: 6rem;
}
  .percent_box span:before {
    width: 150px;
    height: 150px;
    top: 3.5rem;
    right: 3.3rem;
    /* right: 1.3rem; */
}
  .percent_box sub{ font-size:50px;}
  .percent_box span{font-size:120px;}
  
}

@media screen and (max-width: 900px) {
.percent_box sup {
    top: -69px;
}
.percent_box span:before {
    top: 1.5rem;
}
 .percent_box span {
    font-size: 91px;
} 
  .percent_box {
    top: 23rem;
}
  .percent_box p {
    font-size: 26px;
    top: -21px;
    right: -71px;
}
  
}

@media screen and (max-width: 768px) {
  h2.banner__heading{font-size: 40px; line-height:1.2;}
  .percent_box span{ font-size:85px;}
  .percent_box span:before{width: 120px;    height: 120px;   top: 2rem;    right: 3.3rem;}
  .percent_box{top: 17rem;    right: 40rem;}
.percent_box p {
    top: -20px;
    right: -40px;
    font-size: 30px;
}
    .banner__box{ padding:0px 10px;}
  .percent_box sup { color: #FFC300; font-size: 20px; top: -47px;}
  .banner__buttons:after{right: -12px;
    top: -7px;}
}
@media screen and (max-width: 430px){

 .percent_box {
    top: 24rem;
    right: 3rem;
}
  .percent_box span:before {
    width: 70px;
    height: 70px;
    ht: unset;
    top: 1rem;
    right: 2rem;
}
   .percent_box span {    font-size: 42px;}
.percent_box sup {
    font-size: 19px;
    top: -24px;
}
  .percent_box p {
    top: -2px;
    right: -13px;
    font-size: 19px;
}
}



@media screen and (max-width: 425px){
  
  h2.banner__heading { font-size: 30px; margin-top:0px;}
  .banner__text p{font-size: 17px;}
  .percent_box span:before {
    width: 70px;
    height: 70px;
    ht: unset;
    top: 1rem;
    right: 2rem;
}
  .percent_box span {    font-size: 42px;}
.percent_box sup {
    font-size: 19px;
    top: -24px;
}
 .percent_box {
    top: -15rem;
    right: 24rem;
}
  .percent_box p {    top: -15px;    right: -35px;    font-size: 18px;}
  .percent_box sub{bottom: -0.25em;    font-size: 25px;}
  .banner__buttons:after {
    right: -11px;
    top: -6px;
}
   .bloglist button.owl-next {
    right: 3rem;
    font-size: 24px;
}
  .banner__box>*+.banner__buttons {
    margin-top: 1rem;
}
}
@media screen and (min-width:389px) and (max-width: 425px){
.percent_box {
    top: 21rem;
    right: 7rem;
}
  .percent_box span {
    font-size: 62px;
}
.percent_box p {
    top: -13px;
    right: -18px;
    font-size: 16px;
}
.percent_box span:before {
    width: 85px;
    height: 85px;
    top: 1rem;
    right: 2.3rem;
}
  .banner_box:after {
    width: 200px;
    height: 200px;
    border: 2rem solid #FFC300;
    border-radius: 50%;
    right: 125px;
    top: auto;
    bottom: 1rem;
}
  .banner--medium.banner--mobile-bottom:not(.banner--adapt) .banner__media, .banner--medium.banner--stacked:not(.banner--mobile-bottom):not(.banner--adapt)>.banner__media {
    height: 0rem;
}
}

@media screen and (max-width: 400px){
  .middle_box h1.middle_text, .counter_sec .discount_text {
    font-size: 30px;
}
 
  .testimonial_list .owl-carousel .owl-next, .testimonial_list .owl-carousel .owl-prev {
    
    top: 52%;
}
}

@media screen and (max-width: 375px){
 .percent_box {
    top: 22rem;
    right: 5rem;
}
  .percent_box span:before {  top: 17px;  right: 1.3rem;}
  .banner__buttons:after {
    top: -6px;
}
 .percent_box p {
    top: -5px;
    right: -28px;
    font-size: 15px;
}
}
@media screen and (max-width: 390px){
  .banner__buttons:after {   top: -7px;}
}
@media screen and (max-width: 360px){
  .banner__buttons:after {   top: -8px;}  
  .testimonial_list .owl-carousel .owl-next, .testimonial_list .owl-carousel .owl-prev { top: 55%;}
}
@media screen and (max-width: 320px){
  .percent_box span{ font-size:32px;}
  .percent_box sup{ font-size:15px; top:-22px;}
  .percent_box p{ letter-spacing:0px;}
  .percent_box {
    top: 22rem;
    right: 4rem;
}
  .percent_box span:before {    top: 0px;  right: 0.3rem;}
  #other_textsec .rich-text__heading{ font-size:15px;}
  
}


