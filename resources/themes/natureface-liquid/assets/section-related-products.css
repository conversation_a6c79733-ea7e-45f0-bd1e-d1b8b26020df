.related-products {
  display: block;
}



.related-products__heading {
    margin: 0 0 3rem;
    letter-spacing: 0px;
    color: #373737;
    text-transform: capitalize;
    opacity: 1;
    font-size: 30px;
    font-family: 'Poppins';
}
div#mag-product-review .epr-review .epr-data {
    box-shadow: 0px;
    border-radius: 20px;
    padding: 20px;
    font-size: 15px;
}

div#mag-product-review .epr-title {
    text-align: center;
    font-size: 26px;
    margin-bottom: 10px;
    font-weight: 500;
    color: #000;
    font-family: 'Poppins';
}
.related-products .price {
    font-size: 21px;
    color: #21332B;
    font-family: poppins;
    font-weight: 400;
}
.related-products .card__heading {
    margin-top: 10px;
    margin-bottom: 0;
    font-size: 16px;
    letter-spacing: 0px;
    color: #21332b;
    text-transform: capitalize;
    opacity: 1;
    font-family: poppins;
    font-weight: 400;
}

.related-products .card__media .media img {
    object-fit: cover !important;
}


.related-products .quick-add.no-js-hidden {
    position: absolute;
    bottom: 24%;
    left: 4%;
    width: 92%;
}


@media screen and (min-width: 769px) and (max-width: 1024px) {
.quick-add.no-js-hidden {
    bottom: 21%;
}
  
}

@media screen and (min-width: 431px) and (max-width: 768px) {
.related-products .hover_linkscontent {
    top: 48%;
}

}

@media screen and (min-width: 426px) and (max-width: 430px) {
.related-products .quick-add.no-js-hidden {
    bottom: 22%;
}
.related-products .price__container {
    font-size: 18px;
}
.related-products .card__media .media img {
    object-fit: scale-down !important;
}
}


@media screen and (min-width: 391px) and (max-width: 425px) {
.related-products .quick-add.no-js-hidden {
    position: absolute;
    bottom: 21%;
    left: 0%;
    width: 100%;
}
.related-products .price__container {
    font-size: 21px;
}  
.related-products .card__media .media img {
    object-fit: scale-down !important;
}
}


@media screen and (min-width: 376px) and (max-width: 390px) {
.related-products .grid__item {
    max-width: calc(100% - var(--grid-mobile-horizontal-spacing) / 2);
}
.related-products  .grid--2-col-tablet-down .grid__item {
    width: calc(52% - var(--grid-mobile-horizontal-spacing) / 2);
}
.related-products .card__media .media img {
    object-fit: scale-down !important;
}
}

@media screen and (min-width: 320px) and (max-width: 375px) {
.related-products .grid__item {
    max-width: calc(100% - var(--grid-mobile-horizontal-spacing) / 2);
}
.related-products  .grid--2-col-tablet-down .grid__item {
    width: calc(52% - var(--grid-mobile-horizontal-spacing) / 2);
}
  .related-products .price__container {
    font-size: 21px;
}
}

@media screen and (max-width: 320px) {
.related-products .price__container {
    font-size: 21px;
}
}
