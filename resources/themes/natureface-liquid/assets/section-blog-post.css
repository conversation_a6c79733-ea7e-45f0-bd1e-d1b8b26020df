.article-template > *:first-child:not(.article-template__hero-container) {
  margin-top: 5rem;
}

@media screen and (min-width: 750px) {
  .article-template > *:first-child:not(.article-template__hero-container) {
    margin-top: calc(5rem + var(--page-width-margin));
  }
}

/* .article-template__hero-container {
  max-width: 130rem;
  margin: 0 auto;
} */

.article-template__hero-container {
    max-width: 100rem;
    margin: 0 auto;
}


.article-template__hero-small {
  height: 11rem;
}

.article-template__hero-medium {
  height: 22rem;
}

.article-template__hero-large {
  height: 33rem;
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .article-template__hero-small {
    height: 22rem;
  }

  .article-template__hero-medium {
    height: 44rem;
  }

  .article-template__hero-large {
    height: 66rem;
  }
}

@media screen and (min-width: 990px) {
  .article-template__hero-small {
    height: 27.5rem;
  }

  .article-template__hero-medium {
    height: 55rem;
  }

  .article-template__hero-large {
    height: 82.5rem;
  }
}

.article-template header {
  margin-top: 4.4rem;
  margin-bottom: 2rem;
  line-height: calc(0.8 / var(--font-body-scale));
}

@media screen and (min-width: 750px) {
  .article-template header {
    margin-top: 5rem;
  }
}

.article-template__title {
  margin: 0;
}

.article-template__title:not(:only-child) {
  margin-bottom: 1rem;
}

.article-template__link {
  font-size: 1.8rem;
  display: flex;
  justify-content: center;
  align-items: center;
  text-decoration: none;
}

.article-template__link .icon-wrap {
  display: flex;
  margin-right: 1rem;
  transform: rotate(180deg);
}

.article-template__content {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.article-template__social-sharing {
  margin-top: 3rem;
}

.article-template__social-sharing + header,
.article-template__social-sharing + .article-template__content {
  margin-top: 1.5rem;
}

.article-template__comment-wrapper {
  margin-top: 5rem;
  padding: 2.7rem 0;
}

@media screen and (min-width: 750px) {
  .article-template__comment-wrapper {
    margin-top: 6rem;
    padding: 3.6rem 0;
  }
}

.article-template__comment-wrapper h2 {
  margin-top: 0;
}

.article-template__comments {
  margin-bottom: 5rem;
}

@media screen and (min-width: 750px) {
  .article-template__comments {
    margin-bottom: 7rem;
  }
}

.article-template__comments-fields {
  margin-bottom: 4rem;
}

.article-template__comments-comment {
  color: rgba(var(--color-foreground), 0.75);
  background-color: rgb(var(--color-background));
  margin-bottom: 1.5rem;
  padding: 2rem 2rem 1.5rem;
}

@media screen and (min-width: 750px) {
  .article-template__comments-comment {
    padding: 2rem 2.5rem;
  }
}

.article-template__comments-comment p {
  margin: 0 0 1rem;
}

.article-template__comment-fields > * {
  margin-bottom: 3rem;
}

@media screen and (min-width: 750px) {
  .article-template__comment-fields {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 4rem;
  }
}

.article-template__comment-warning {
  margin: 2rem 0 2.5rem;
}

@media screen and (min-width: 990px) {
  .article-template__comments .pagination-wrapper {
    margin: 5rem 0 8rem;
  }
}

.article-template__back:last-child {
  margin-bottom: 3.2rem;
}



/* -----------extra css-------------------- */

.article-template__title {
    margin: 0;
    font-size: 30px;
    font-family: 'Poppins';
    font-weight: 500;
}


.article-template .share-button__fallback {
    display: flex;
    align-items: center;
    position: absolute;
   top: 0rem;
    left: 10.1rem;
    z-index: 3;
    width: fit-content;
    min-width: max-content;
    border-radius: var(--inputs-radius);
    border: 0;
}

.article-template .share-button__fallback:before{
  display:none;
}

.article-template .share-button__fallback:after{
   display:none;
}

.article-template  .share_social_icons {
    border: 2px solid #21332B;
    border-radius: 5px;
}

.article-template  .list-social__item .icon {
    color: #21332B;
}

.article-template .article-template__content {
    font-size: 13px;
    font-family: 'Poppins';
}

.article-template ul.social-sharing {
    display: flex;
    gap: 10px;
    padding-left: 0;
    justify-content: center;
    align-items: center;
    margin-bottom: 0rem;
    padding: 5px;
}


.article-template ul.social-sharing li a {
    color: #21332b;
    width: 2.2rem;
    height: 2.2rem;
    font-size: 20px;
    padding: 1.1rem;
}

.article-template  ul.social-sharing li {
    list-style: none;
  text-align:center;
}


@media screen and (min-width: 429px) and (max-width: 430px) {

.article-template .list-social__link {
    padding: 0.5rem;

}
  .article-template .share-button__fallback {
    top: 0rem;
    left: 7.1rem;
}
  .article-template ul.social-sharing {
    gap: 2px;
}

}

@media screen and (min-width: 426px) and (max-width: 428px) {
.article-template .share-button__fallback {
    top: -3rem;
    left: 6.1rem;
}
  .article-template ul.social-sharing {
    gap: 2px;
}

}

@media screen and (min-width: 394px) and (max-width: 425px) {
.article-template .share-button__fallback {
    top: -3rem;
    left: 6.1rem;
}
  .article-template ul.social-sharing {
    gap: 2px;
}
  .article-template ul.social-sharing li a {
    font-size: 17px;
}
}


@media screen and (min-width: 391px) and (max-width: 393px) {
.article-template .list-social__link {
    padding: 0.5rem;

}
  .article-template .share-button__fallback {
    top: -3rem;
    left: 1.1rem;
}
.article-template ul.social-sharing {
    gap: 2px;
}


}

@media screen and (min-width: 376px) and (max-width: 390px) {
.article-template .list-social__link {
    padding: 0.5rem;

}
  .article-template .share-button__fallback {
    top: -3rem;
    left: 1.1rem;
}
.article-template ul.social-sharing {
    gap: 2px;
}

}


@media screen and (min-width: 320px) and (max-width: 375px) {
.article-template ul.social-sharing li a {
    font-size: 17px;
    padding: 0.8rem;
}
.article-template ul.social-sharing {
    gap: 0px;
    padding: 3px;
}
  .article-template .share-button__fallback {
    top: 0rem;
    left: 7.1rem;
}
}


@media screen and (max-width: 320px){
.article-template .share-button__fallback {
    top: -3rem;
    left: 4.1rem;
}
}