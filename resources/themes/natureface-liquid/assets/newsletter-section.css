.newsletter__wrapper {
  padding-right: calc(4rem / var(--font-body-scale));
  padding-left: calc(4rem / var(--font-body-scale));
}

@media screen and (min-width: 750px) {
  .newsletter__wrapper {
    padding-right: 9rem;
    padding-left: 9rem;
  }
}

.newsletter__wrapper > * {
  margin-top: 0;
  margin-bottom: 0;
}

.newsletter__wrapper > * + * {
  margin-top: 2rem;
}

.newsletter__wrapper > * + .newsletter-form {
  margin-top: 3rem;
}

.newsletter__subheading {
  max-width: 70rem;
  margin-left: auto;
  margin-right: auto;
}

.newsletter__wrapper .newsletter-form__field-wrapper {
  max-width: 36rem;
}

.newsletter-form__field-wrapper .newsletter-form__message {
  margin-top: 1.5rem;
}

.newsletter__button {
  margin-top: 3rem;
  width: fit-content;
}

@media screen and (min-width: 750px) {
  .newsletter__button {
    flex-shrink: 0;
    margin: 0 0 0 1rem;
  }
}




/* -------------extra css-------------------- */

 .effect-1, {
  border: 0;
  padding: 7px 0;
  border-bottom: 1px solid #ccc;
}

.effect-1 ~ .focus-border {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #f6f6f636;
  transition: 0.4s;
}
.effect-1:focus ~ .focus-border {
  /* width: 100%; */
     width: 563px;
    height: 2px;
    background-color: #f6f6f636;
  transition: 0.4s;
}


@media screen and (max-width: 768px) {
.effect-1:focus ~ .focus-border {
     width: 465px;
}

}



@media screen and (max-width: 425px) {
.effect-1:focus ~ .focus-border {
     width: 230px;
}

}

@media screen and (max-width: 375px) {
.effect-1:focus ~ .focus-border {
     width: 170px;
}

}

@media screen and (max-width: 320px) {
.effect-1:focus ~ .focus-border {
     width: 150px;
}

}
