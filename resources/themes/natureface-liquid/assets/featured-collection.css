.feature_collection_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.collection {
  position: relative;
  overflow: hidden;
}
.collection h2.title.inline-richtext.h2.scroll-trigger.animate--slide-in {
  letter-spacing: 0px;
  color: #373737;
  text-transform: capitalize;
  opacity: 1;
  font-size: 40px;
  font-family: pt serif;
}
.viewall_button a.button {
  border: 1px solid #21332b;
  border-radius: 5px;
  opacity: 1;
  background-color: transparent;
  color: #000;
  gap: 10px;
  padding: 0rem 1rem;
}
.button:after {
  display: none;
}
.card.card--standard.card--media {
  border: 0px;
}
.card__content a:hover {
  color: black;
}
.underline-links-hover:hover a {
  text-decoration: none;
  text-underline-offset: 0.3rem;
}
.collection .collection__title.title-wrapper {
  margin-bottom: 9.5rem;
}
.card .media {
  width: 100%;
  height: 100%;
}
.card__inner {
  height: 363px;
  position: relative;
}
.card__media .media img {
  height: 100%;
  object-fit: cover;
  object-position: center center;
  width: 100%;
  background-color: white;
}
.quick-add.no-js-hidden {
  position: absolute;
  bottom: 21%;
  left: 4%;
  width: 92%;
}

.collection .card-wrapper {
  padding: 0rem 2rem;
}

.button,
.shopify-challenge__button,
.customer button,
button.shopify-payment-button__button--unbranded {
  min-height: 40px;
}

.quick-add__submit {
  background-color: #9d8a6f;
  color: #fff;
  border-radius: 5px;
  font-size: 20px;
  font-family: pt serif;
  text-transform: capitalize;
}

.card:hover .quick-add__submit {
  background-color: #21332b;
  transition: 0.5s linear;
}

.collection .card__heading {
  font-size: 16px;
  font-family: pt serif;
  letter-spacing: 0px;
  color: #21332b;
  text-transform: capitalize;
  opacity: 1;
}

.card__badge {
  align-self: flex-start !important;
  grid-row-start: 1 !important;
  justify-self: flex-end !important;
  padding-top: 47px;
}
span.new-badge {
  font-size: 16px;
  padding: 3px 13px;
}
.new_badge {
  text-align: left;
  padding-right: 10px;
}

.collection .price {
  font-size: 21px;
  font-family: pt serif;
}
.hover_linkscontent {
  position: absolute;
  top: 10%;
  z-index: 9999;
}
.collection .card--standard > .card__content .card__information {
  padding-left: 16px;
  padding-right: 0;
}

.collection s.price-item.price-item--regular {
  font-size: 13px;
  color: #21332b;
  text-transform: uppercase;
  opacity: 0.5;
  font-family: pt serif;
}
.collection span.discount-percentage {
  font-size: 16px;
  font-family: pt serif;
  width: 120px;
  height: 40px;
  padding: 2px 5px;
}
.collection .percent_badge {
  position: absolute;
  top: 5%;
  right: 9%;
}
.hover_linkscontent {
  position: absolute;
  top: 2%;
  right: 6%;
  z-index: 9999;
}
.hover_linkscontent .share-button__fallback {
  display: flex;
  align-items: center;
  position: absolute;
  top: -3rem;
  left: -6.9rem;
  z-index: 3;
  width: 70px;
  height: 100px;
  border-radius: 50%;
}
.hover_linkscontent li a svg {
  color: white;
  width: 25px;
  height: 25px;
  background-color: #9d8a6f;
  border-radius: 50%;
  padding: 3px;
}
.hover_linkscontent .share_social_icons {
  width: 210px;
  height: 80px;
  border-radius: 50%;
  background-color: transparent !important;
  padding: 4px;
  top: 17px;
  position: absolute;
  right: 0;
}

.hover_linkscontent .share-button__fallback:before {
  background: transparent !important;
  pointer-events: none;
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 50%;
  z-index: -1;
}

.hover_linkscontent .share-button__fallback:after {
  pointer-events: none;
  content: "";
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  border: 0.1rem solid transparent;
  border-radius: 50%;
  box-shadow: unset !important;
  transition: box-shadow var(--duration-short) ease;
  z-index: 1;
}
.hover_linkscontent .social-sharing {
  display: flex;
  gap: 0px;
  justify-content: center;
  align-items: center;
  background-color: #ffffff4f;
  border: 2px solid #21332b;
  border-radius: 15px;
}
.hover_linkscontent .list-social__link {
  padding: 0.5rem;
}
.magnified_image {
  width: 50%;
  height: auto;
  margin: 0 auto;
}

.magnified_image img {
  width: 100%;
  height: 100%;
  object-fit: inherit;
}

/* -------quick view icon----------- */
.hover_linkscontent .no-js-hidden {
  position: relative;
}
.hover_linkscontent .no-js-hidden .tooltiptext {
  visibility: hidden;
  width: 55px;
  background-color: #9d8a6f;
  color: #fff;
  text-align: center;
  border-radius: 2px;
  padding: 4px 0;
  position: absolute;
  z-index: 1;
  left: -65px;
  font-size: 8px;
  font-family: poppins;
  top: 2px;
}
.hover_linkscontent .no-js-hidden .tooltiptext:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 100%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent transparent #9d8a6f;
}
.hover_linkscontent .no-js-hidden:hover .tooltiptext {
  visibility: visible;
}

.quick-add-modal__content-info .wishlist_product {
  margin-bottom: 1rem;
  position: absolute;
  top: 44.5%;
  right: 0%;
}

/* ------share icon------------ */
.hover_linkscontent .share-button__button {
  position: relative;
  display: inline-block;
}

.hover_linkscontent .share-button__button .tooltiptext {
  visibility: hidden;
  width: 55px;
  background-color: #9d8a6f;
  color: #fff;
  text-align: center;
  border-radius: 3px;
  padding: 4px 0;
  position: absolute;
  z-index: 1;
  top: 2px;
  right: 136%;
  font-size: 8px;
  font-family: poppins;
}

.hover_linkscontent .share-button__button .tooltiptext:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 100%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent transparent #9d8a6f;
}
.hover_linkscontent .share-button__button:hover .tooltiptext {
  visibility: visible;
}

.share_social_icons ul.social-sharing li a i {
  color: #fff;
  background-color: #9d8a6f;
  border-radius: 50%;
  padding: 3px;
  height: 2.2rem;
  width: 2.2rem;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.share_social_icons ul.social-sharing {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 13px;
  padding: 5px 4px 5px 4px;
}
.share_social_icons ul.social-sharing li {
  list-style: none;
}
.share_social_icons ul.social-sharing li a {
  text-decoration: none;
}
/* -----------wishlist icon----------- */
.wishlist_tooltip {
  position: relative;
  display: inline-block;
}

.wishlist_tooltip .tooltiptext {
  visibility: hidden;
  width: 55px;
  background-color: #9d8a6f;
  color: #fff;
  text-align: center;
  border-radius: 3px;
  padding: 3px 0;
  position: absolute;
  z-index: 1;
  top: 10px;
  right: 136%;
  font-size: 8px;
  font-family: poppins;
}

.wishlist_tooltip .tooltiptext:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 100%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent transparent #9d8a6f;
}
.wishlist_tooltip:hover .tooltiptext {
  visibility: visible;
}
.wishlist_tooltip {
  transition: all 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.like-button.liked {
  color: #ff6e6f;
  border-color: currentColor;
  filter: grayscale(0);
}
.like-button:hover {
  border-color: currentColor;
}

.like-icon {
  width: 18px;
  height: 16px;
  display: inline-block;
  position: relative;
  margin-right: 0.25em;
  font-size: 1.5rem;
  background-size: 100%;
  -webkit-animation: heartUnlike 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    both;
  animation: heartUnlike 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
}

.liked .like-icon {
  -webkit-animation: heartPulse 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    both;
  animation: heartPulse 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
}
.liked .like-icon [class^="heart-animation-"] {
  background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAyMSAxOCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTAuMTAxIDQuNDE3UzguODk1LjIwNyA1LjExMS4yMDdjLTQuNDY1IDAtMTAuOTY3IDYuODQ2IDUuMDgyIDE3LjU5MkMyNS4yMzcgNy4wMyAxOS42NjUuMjAyIDE1LjUwMS4yMDJjLTQuMTYyIDAtNS40IDQuMjE1LTUuNCA0LjIxNXoiIGZpbGw9IiNGRjZFNkYiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvc3ZnPg==")
    no-repeat center;
  background-size: 100%;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 16px;
  height: 14px;
  opacity: 0;
}
.liked .like-icon [class^="heart-animation-"]::before,
.liked .like-icon [class^="heart-animation-"]::after {
  content: "";
  background: inherit;
  background-size: 100%;
  width: inherit;
  height: inherit;
  display: inherit;
  position: relative;
  top: inherit;
  left: inherit;
  opacity: 0;
}
.liked .like-icon .heart-animation-1 {
  -webkit-animation: heartFloatMain-1 1s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    both;
  animation: heartFloatMain-1 1s cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
}
.liked .like-icon .heart-animation-1::before,
.liked .like-icon .heart-animation-1::after {
  width: 12px;
  height: 10px;
  visibility: hidden;
}
.liked .like-icon .heart-animation-1::before {
  opacity: 0.6;
  -webkit-animation: heartFloatSub-1 1s 0.25s
    cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
  animation: heartFloatSub-1 1s 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    both;
}
.liked .like-icon .heart-animation-1::after {
  -webkit-animation: heartFloatSub-2 1s 0.15s
    cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
  animation: heartFloatSub-2 1s 0.15s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    both;
  opacity: 0.75;
}
.liked .like-icon .heart-animation-2 {
  -webkit-animation: heartFloatMain-2 1s 0.1s
    cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
  animation: heartFloatMain-2 1s 0.1s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    both;
}
.liked .like-icon .heart-animation-2::before,
.liked .like-icon .heart-animation-2::after {
  width: 10px;
  height: 8px;
  visibility: hidden;
}
.liked .like-icon .heart-animation-2::before {
  -webkit-animation: heartFloatSub-3 1s 0.25s
    cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
  animation: heartFloatSub-3 1s 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    both;
  opacity: 0.25;
}
.liked .like-icon .heart-animation-2::after {
  -webkit-animation: heartFloatSub-4 1s 0.15s
    cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
  animation: heartFloatSub-4 1s 0.15s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    both;
  opacity: 0.4;
}

@-webkit-keyframes heartPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
}

@keyframes heartPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
}
@-webkit-keyframes heartUnlike {
  50% {
    transform: scale(0.75);
  }
}
@keyframes heartUnlike {
  50% {
    transform: scale(0.75);
  }
}
@-webkit-keyframes heartFloatMain-1 {
  0% {
    opacity: 0;
    transform: translate(0) rotate(0);
  }
  50% {
    opacity: 1;
    transform: translate(0, -25px) rotate(-20deg);
  }
}
@keyframes heartFloatMain-1 {
  0% {
    opacity: 0;
    transform: translate(0) rotate(0);
  }
  50% {
    opacity: 1;
    transform: translate(0, -25px) rotate(-20deg);
  }
}
@-webkit-keyframes heartFloatMain-2 {
  0% {
    opacity: 0;
    transform: translate(0) rotate(0) scale(0);
  }
  50% {
    opacity: 0.9;
    transform: translate(-10px, -38px) rotate(25deg) scale(1);
  }
}
@keyframes heartFloatMain-2 {
  0% {
    opacity: 0;
    transform: translate(0) rotate(0) scale(0);
  }
  50% {
    opacity: 0.9;
    transform: translate(-10px, -38px) rotate(25deg) scale(1);
  }
}
@-webkit-keyframes heartFloatSub-1 {
  0% {
    visibility: hidden;
    transform: translate(0) rotate(0);
  }
  50% {
    visibility: visible;
    transform: translate(13px, -13px) rotate(30deg);
  }
}
@keyframes heartFloatSub-1 {
  0% {
    visibility: hidden;
    transform: translate(0) rotate(0);
  }
  50% {
    visibility: visible;
    transform: translate(13px, -13px) rotate(30deg);
  }
}
@-webkit-keyframes heartFloatSub-2 {
  0% {
    visibility: hidden;
    transform: translate(0) rotate(0);
  }
  50% {
    visibility: visible;
    transform: translate(18px, -10px) rotate(55deg);
  }
}
@keyframes heartFloatSub-2 {
  0% {
    visibility: hidden;
    transform: translate(0) rotate(0);
  }
  50% {
    visibility: visible;
    transform: translate(18px, -10px) rotate(55deg);
  }
}
@-webkit-keyframes heartFloatSub-3 {
  0% {
    visibility: hidden;
    transform: translate(0) rotate(0);
  }
  50% {
    visibility: visible;
    transform: translate(-10px, -10px) rotate(-40deg);
  }
  100% {
    transform: translate(-50px, 0);
  }
}
@keyframes heartFloatSub-3 {
  0% {
    visibility: hidden;
    transform: translate(0) rotate(0);
  }
  50% {
    visibility: visible;
    transform: translate(-10px, -10px) rotate(-40deg);
  }
  100% {
    transform: translate(-50px, 0);
  }
}
@-webkit-keyframes heartFloatSub-4 {
  0% {
    visibility: hidden;
    transform: translate(0) rotate(0);
  }
  50% {
    visibility: visible;
    transform: translate(2px, -18px) rotate(-25deg);
  }
}
@keyframes heartFloatSub-4 {
  0% {
    visibility: hidden;
    transform: translate(0) rotate(0);
  }
  50% {
    visibility: visible;
    transform: translate(2px, -18px) rotate(-25deg);
  }
}

@media screen and (min-width: 1025px) and (max-width: 1440px) {
  .quick-add.no-js-hidden {
    bottom: 21%;
  }
  .hover_linkscontent {
    top: 1%;
    right: 3%;
  }
}
@media screen and (min-width: 1001px) and (max-width: 1024px) {
  .collection .collection__title.title-wrapper {
    margin-bottom: 3.5rem;
  }
  .grid--peek.slider .grid__item:first-of-type {
    margin-left: 0rem;
  }
  .hover_linkscontent .share_social_icons {
    width: 159px;
  }
  .quick-add-modal__content-info .wishlist_product {
    top: 50%;
  }
  .quick-add-modal__content-info span.quantity_button_rules {
    width: 85%;
  }
  .collection .card-wrapper {
    padding: 0rem 0rem;
  }
  .collection .grid--4-col-desktop .grid__item {
    width: calc(40% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
    max-width: calc(33% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
  }
}

@media screen and (min-width: 901px) and (max-width: 1000px) {
  .grid--peek.slider .grid__item:first-of-type {
    margin-left: 0rem;
  }
  .hover_linkscontent .share_social_icons {
    width: 170px;
  }
  .collection .collection__title.title-wrapper {
    margin-bottom: 4rem;
  }
  .collection .card__inner {
    height: unset;
  }
  .quick-add.no-js-hidden {
    bottom: 30%;
  }
  .hover_linkscontent {
    top: 25%;
  }
}

@media screen and (min-width: 769px) and (max-width: 900px) {
  .quick-add.no-js-hidden {
    bottom: 26%;
  }
  .hover_linkscontent {
    top: 41%;
  }
}

@media screen and (min-width: 431px) and (max-width: 768px) {
  .quick-add.no-js-hidden {
    bottom: 23%;
  }
  .card__inner {
    height: -webkit-fill-available;
  }
  .hover_linkscontent .share_social_icons {
    width: 150px;
  }
  .hover-link-product {
    display: none;
  }
  .quick-add__submit {
    font-size: 16px;
  }
  .collection .card-wrapper {
    padding: 0rem 0rem;
  }
}

@media screen and (min-width: 426px) and (max-width: 430px) {
  .collection .collection__title.title-wrapper {
    margin-bottom: 0.5rem;
  }
  .collection h2.title.inline-richtext.h2.scroll-trigger.animate--slide-in {
    font-size: 30px !important;
  }
  .viewall_button a.button {
    gap: 5px;
    font-size: 12px;
  }
  .collection .grid--peek.slider .grid__item {
    margin-left: 0.1rem;
  }
  .button,
  .shopify-challenge__button,
  .customer button {
    padding: 0 0rem;
  }
  .collection .price {
    font-size: 16px;
  }
  .quick-add.no-js-hidden {
    bottom: 18%;
  }
  .hover_linkscontent {
    top: 1%;
    right: 4%;
  }
  .quick-add__submit {
    font-size: 15px;
  }
  .button,
  .shopify-challenge__button,
  .customer button,
  button.shopify-payment-button__button--unbranded {
    min-height: 40px;
    max-height: 100%;
  }
  .magnified_image {
    width: 100%;
  }
  .collection .card.card--standard.card--media {
    width: 90%;
    margin: 0 auto;
  }
}

@media screen and (min-width: 394px) and (max-width: 425px) {
  .collection h2.title.inline-richtext.h2.scroll-trigger.animate--slide-in {
    font-size: 30px !important;
  }
  .viewall_button a.button {
    gap: 5px;
    font-size: 12px;
  }
  .button,
  .shopify-challenge__button,
  .customer button {
    padding: 0 0rem;
  }
  .collection .collection__title.title-wrapper {
    margin-bottom: 2.5rem;
  }
  .collection .price {
    font-size: 16px;
  }
  .quick-add.no-js-hidden {
    bottom: 18%;
  }
  .hover_linkscontent {
    top: 1%;
    right: 4%;
  }
  .quick-add__submit {
    font-size: 15px;
  }
  .button,
  .shopify-challenge__button,
  .customer button,
  button.shopify-payment-button__button--unbranded {
    min-height: 40px;
    max-height: 100%;
  }
  .magnified_image {
    width: 100%;
  }
  .collection .card.card--standard.card--media {
    width: 80%;
    margin: 0 auto;
  }
  .collection .grid--peek.slider .grid__item {
    margin-left: 0.1rem;
  }
}

@media screen and (min-width: 391px) and (max-width: 393px) {
  .collection h2.title.inline-richtext.h2.scroll-trigger.animate--slide-in {
    font-size: 30px !important;
  }
  .viewall_button a.button {
    gap: 5px;
    font-size: 12px;
  }
  .button,
  .shopify-challenge__button,
  .customer button {
    padding: 0 0rem;
  }
  .collection .collection__title.title-wrapper {
    margin-bottom: 2.5rem;
  }
  .collection .price {
    font-size: 16px;
  }
  .quick-add.no-js-hidden {
    bottom: 18%;
  }
  .hover_linkscontent {
    top: 1%;
    right: 4%;
  }
  .quick-add__submit {
    font-size: 15px;
  }
  .button,
  .shopify-challenge__button,
  .customer button,
  button.shopify-payment-button__button--unbranded {
    min-height: 40px;
    max-height: 100%;
  }
  .magnified_image {
    width: 100%;
  }
  .collection .card.card--standard.card--media {
    width: 80%;
    margin: 0 auto;
  }
  .collection .grid--peek.slider .grid__item {
    margin-left: 0.1rem;
  }
}

@media screen and (min-width: 376px) and (max-width: 390px) {
  .collection .grid--peek.slider .grid__item {
    margin-left: 0.1rem;
  }
  .collection h2.title.inline-richtext.h2.scroll-trigger.animate--slide-in {
    font-size: 30px !important;
  }
  .viewall_button a.button {
    gap: 5px;
    font-size: 12px;
  }
  .button,
  .shopify-challenge__button,
  .customer button {
    padding: 0 0rem;
  }
  .collection .collection__title.title-wrapper {
    margin-bottom: 2.5rem;
  }
  .collection .price {
    font-size: 16px;
  }
  .quick-add.no-js-hidden {
    bottom: 18%;
  }
  .hover_linkscontent {
    top: 1%;
    right: 4%;
  }
  .quick-add__submit {
    font-size: 15px;
  }
  .button,
  .shopify-challenge__button,
  .customer button,
  button.shopify-payment-button__button--unbranded {
    min-height: 40px;
    max-height: 100%;
  }
  .magnified_image {
    width: 100%;
  }
  .collection .card.card--standard.card--media {
    width: 80%;
    margin: 0 auto;
  }
}

@media screen and (min-width: 321px) and (max-width: 375px) {
  .collection h2.title.inline-richtext.h2.scroll-trigger.animate--slide-in {
    font-size: 25px !important;
  }
  .viewall_button a.button {
    gap: 3px;
    font-size: 12px;
  }
  .collection .grid--peek.slider .grid__item {
    margin-left: 0.1rem;
  }
  .button,
  .shopify-challenge__button,
  .customer button {
    padding: 0 1rem;
  }
  .collection .collection__title.title-wrapper {
    margin-bottom: 2rem;
  }
  .magnified_image {
    width: 100%;
  }
  .collection .card.card--standard.card--media {
    width: 90%;
    margin: 0 auto;
  }
  .hover_linkscontent {
    right: 8%;
  }
  .new_badge {
    padding-right: 20px;
  }
}

@media screen and (max-width: 320px) {
  #rich_text_section .left_text_image {
    width: 41px;
    height: 37px;
    top: 25px;
    left: 13%;
    box-shadow: 8px -9px #f6f6f6;
  }
  #rich_text_section .right_text_image {
    width: 41px;
    height: 25px;
    top: 26px;
    right: 13%;
    box-shadow: -9px -6px #f6f6f6;
  }
  .feature_collection_title {
    display: block;
  }
  .center.collection__view-all {
    text-align: center;
  }
  .collection h2.title.inline-richtext.h2.scroll-trigger.animate--slide-in {
    font-size: 31px !important;
    text-align: center;
  }
  .collection .grid--peek.slider .grid__item {
    margin-left: 0.1rem;
  }
  .button,
  .shopify-challenge__button,
  .customer button {
    padding: 0 1rem;
  }
  .collection .collection__title.title-wrapper {
    margin-bottom: 1.5rem;
  }
  .magnified_image {
    width: 100%;
  }
}
