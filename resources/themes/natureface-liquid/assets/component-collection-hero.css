.collection-hero__inner {
  display: flex;
  flex-direction: column;
}

.collection-hero--with-image .collection-hero__inner {
  margin-bottom: 0;
  padding-bottom: 2rem;
}

@media screen and (min-width: 750px) {
  .collection-hero.collection-hero--with-image {
    padding: calc(4rem + var(--page-width-margin)) 0 calc(4rem + var(--page-width-margin));
    overflow: hidden;
  }

  .collection-hero--with-image .collection-hero__inner {
    padding-bottom: 0;
  }
}

.collection-hero__text-wrapper {
  flex-basis: 100%;
}

@media screen and (min-width: 750px) {
  .collection-hero {
    padding: 0;
  }

  .collection-hero__inner {
    align-items: center;
    flex-direction: row;
    padding-bottom: 0;
  }
}

.collection-hero__title {
  margin: 2.5rem 0;
}

.collection-hero__title + .collection-hero__description {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  font-size: 1.6rem;
  line-height: calc(1 + 0.5 / var(--font-body-scale));
}

@media screen and (min-width: 750px) {
  .collection-hero__title + .collection-hero__description {
    font-size: 1.8rem;
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .collection-hero__description {
    max-width: 66.67%;
  }

  .collection-hero--with-image .collection-hero__description {
    max-width: 100%;
  }
}

.collection-hero--with-image .collection-hero__title {
  margin: 0;
}

.collection-hero--with-image .collection-hero__text-wrapper {
  padding: 5rem 0 4rem;
}

.collection-hero__image-container {
  border: var(--media-border-width) solid rgba(var(--color-foreground), var(--media-border-opacity));
  border-radius: var(--media-radius);
  box-shadow: var(--media-shadow-horizontal-offset) var(--media-shadow-vertical-offset) var(--media-shadow-blur-radius)
    rgba(var(--color-shadow), var(--media-shadow-opacity));
}

@media screen and (max-width: 749px) {
  .collection-hero__image-container {
    height: 20rem;
  }
}

@media screen and (min-width: 750px) {
  .collection-hero--with-image .collection-hero__text-wrapper {
    padding: 4rem 2rem 4rem 0;
    flex-basis: 50%;
  }

  .collection-hero__image-container {
    align-self: stretch;
    flex: 1 0 50%;
    margin-left: 3rem;
    min-height: 20rem;
  }
}
