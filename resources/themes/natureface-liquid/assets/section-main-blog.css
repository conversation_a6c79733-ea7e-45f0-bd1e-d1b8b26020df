.blog-articles {
  display: grid;
  grid-gap: 1rem;
  column-gap: var(--grid-mobile-horizontal-spacing);
  row-gap: var(--grid-mobile-vertical-spacing);
}

.blog-articles .card-wrapper {
  width: 100%;
}

@media screen and (min-width: 750px) {
  /* .blog-articles {
    grid-template-columns: 1fr 1fr;
    column-gap: var(--grid-desktop-horizontal-spacing);
    row-gap: var(--grid-desktop-vertical-spacing);
  }
 */

  .blog-articles {
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: var(--grid-desktop-horizontal-spacing);
    row-gap: var(--grid-desktop-vertical-spacing);
}
  
  .blog-articles--collage > *:nth-child(3n + 1),
  .blog-articles--collage > *:nth-child(3n + 2):last-child {
    grid-column: span 2;
    text-align: center;
  }

  .blog-articles--collage > *:nth-child(3n + 1) .card,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .card {
    text-align: center;
  }

  .blog-articles--collage > *:nth-child(3n + 1) .article-card__image--small .ratio::before,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .article-card__image--small .ratio::before {
    padding-bottom: 22rem;
  }

  .blog-articles--collage > *:nth-child(3n + 1) .article-card__image--medium .ratio::before,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .article-card__image--medium .ratio::before {
    padding-bottom: 44rem;
  }

  .blog-articles--collage > *:nth-child(3n + 1) .article-card__image--large .ratio::before,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .article-card__image--large .ratio::before {
    padding-bottom: 66rem;
  }
}

@media screen and (min-width: 990px) {
  .blog-articles--collage > *:nth-child(3n + 1) .article-card__image--small .ratio .ratio::before,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .article-card__image--small .ratio .ratio::before {
    padding-bottom: 27.5rem;
  }

  .blog-articles--collage > *:nth-child(3n + 1) .article-card__image--medium .ratio::before,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .article-card__image--medium .ratio::before {
    padding-bottom: 55rem;
  }

  .blog-articles--collage > *:nth-child(3n + 1) .article-card__image--large .ratio::before,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .article-card__image--large .ratio::before {
    padding-bottom: 82.5rem;
  }
}




/* ------------------extra css------------------- */

#main_blog_page .article-card .card__heading {
    margin-bottom: .6rem;
    font-size: 25px;
    font-family: 'Poppins';
}

#main_blog_page .article-card__excerpt {
    width: 100%;
    margin-top: 1.2rem;
    font-size: 13px;
    font-family: 'Poppins';
}

#main_blog_page .card__information {
    padding: 10px;
}

#main_blog_page .blog_btn {
    background-color: #9D8A6F;
    padding: 10px 20px;
    color: white;
    width: fit-content;
    font-size: 17px;
    font-family: 'Poppins';
    border-radius: 5px;
   transition: 0.3s infinite;
}

#main_blog_page .blog_btn a {
    color: white;
    text-decoration: none;
 
}

/* #main_blog_page .blog_btn:hover {
    background-color: #21332B !important;
    transition: 0.3s infinite;
} */




@media screen and  (max-width: 1024px) {
#main_blog_page .article-card .card__heading {
    margin-bottom: .6rem;
    font-size: 19px;
    font-family: Poppins;
}
h3.blog_category_name {
    font-size: 20px;
}
#main_blog_page .blog_btn {
    padding: 5px 20px;
}
}



@media screen and  (max-width: 768px) {
.blog-articles {
    grid-template-columns: 1fr 1fr;
}

}


@media screen and  (max-width: 430px) {
.blog-articles {
    grid-template-columns: 1fr;
}

}
