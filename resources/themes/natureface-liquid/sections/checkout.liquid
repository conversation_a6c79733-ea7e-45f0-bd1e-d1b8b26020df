<div class="bg-white">
  <main class="mx-auto max-w-7xl px-4 pt-4 pb-16 sm:px-6 sm:pt-8 sm:pb-24 lg:px-8 xl:px-2 xl:pt-14">
    <h1 class="sr-only">{{ section.settings.text_checkout_title | default: 'Checkout' }}</h1>

    <div class="w-full flex justify-center mb-8">
      <a href="/" class="block">
        <img src="{{ section.settings.logo_main.src | img_url: '200x' }}" alt="{{ section.settings.logo_alt | default: 'Logo' }}" class="h-12 w-auto mx-auto">
      </a>
    </div>

    <div
      class="mx-auto grid max-w-lg grid-cols-1 gap-x-8 lg:max-w-none lg:grid-cols-3 lg:grid-flow-col-dense"
      x-data="{
        cartProducts: [],
        cart: {},
        summaryOpen: false,
        contactsOpen: true,
        addressOpen: false,
        deliveryOpen: false,
        paymentOpen: false,
        completedSteps: {
          contacts: false,
          address: false,
          delivery: false,
          payment: false
        },
        fetchCart() {
          fetch('/cart.json')
            .then(res => res.json())
            .then(data => {
              this.cartProducts = data.products || [];
              this.cart = data;
            });
        }
      }"
      x-init="fetchCart()"
      @contacts-complete.window="
        contactsOpen = false;
        addressOpen = true;
        deliveryOpen = false;
        paymentOpen = false;
        completedSteps.contacts = true;
      "
      @address-complete.window="
        contactsOpen = false;
        addressOpen = false;
        deliveryOpen = true;
        paymentOpen = false;
        completedSteps.address = true;
      "
      @delivery-complete.window="
        contactsOpen = false;
        addressOpen = false;
        deliveryOpen = false;
        paymentOpen = true;
        completedSteps.delivery = true;
      "
      @payment-complete.window="
        completedSteps.payment = true;
      "
    >
      <!-- Order Summary (Cart) -->
      <div class="mx-auto w-full max-w-lg lg:col-span-1 lg:sticky lg:top-8 lg:self-start lg:order-1">
        <!-- Mobile summary toggle -->
        <div class="block lg:hidden mb-4">
          <button type="button" class="w-full flex items-center justify-between rounded-md border border-gray-200 bg-white px-4 py-3 text-base font-medium text-gray-900 shadow-sm hover:bg-gray-50" @click="summaryOpen = !summaryOpen" aria-expanded="summaryOpen" aria-controls="order-summary-panel">
            <span>Order summary</span>
            <svg :class="summaryOpen ? 'rotate-180' : ''" class="h-5 w-5 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
          </button>
        </div>
        <div id="order-summary-panel" x-show="summaryOpen || window.innerWidth >= 1024" class="transition-all duration-300" :class="{'hidden': !summaryOpen && window.innerWidth < 1024}">
          {% render 'snippets.checkout-cart' %}
          <dl class="mt-10 space-y-6 text-sm font-medium text-gray-500">
            <div class="flex justify-between">
              <dt>{{ section.settings.text_subtotal | default: 'Subtotal' }}</dt>
              <dd class="text-gray-900"
                x-text="`$${cartProducts.reduce((sum, item) => sum + (item.final_price * item.quantity), 0).toFixed(2)}`"
              ></dd>
            </div>
            <div class="flex justify-between">
              <dt>{{ section.settings.text_taxes | default: 'Taxes' }}</dt>
              <dd class="text-gray-900"
                x-text="cart.tax_price !== undefined ? `$${Number(cart.tax_price).toFixed(2)}` : '$0.00'"
              ></dd>
            </div>
            <div class="flex justify-between">
              <dt>{{ section.settings.text_shipping | default: 'Shipping' }}</dt>
              <dd class="text-gray-900"
                x-text="cart.shipping_price !== undefined ? `$${Number(cart.shipping_price).toFixed(2)}` : '$0.00'"
              ></dd>
            </div>
            <div class="flex justify-between border-t border-gray-200 pt-6 text-gray-900">
              <dt class="text-base">{{ section.settings.text_total | default: 'Total' }}</dt>
              <dd class="text-base"
                x-text="`$${(
                  cartProducts.reduce((sum, item) => sum + (item.final_price * item.quantity), 0)
                  + (cart.tax_price || 0)
                  + (cart.shipping_price || 0)
                ).toFixed(2)}`"
              ></dd>
            </div>
          </dl>
        </div>
      </div>
      <!-- Steps/Forms -->
      <div class="mx-auto w-full max-w-lg lg:col-span-2 lg:pt-12 lg:order-2">
        <!-- Stepper -->
        <nav aria-label="Checkout Progress" class="mb-8">
          <ol class="flex items-center space-x-4 text-sm font-medium">
            <li :class="completedSteps.contacts ? 'text-[var(--accent)]' : 'text-gray-400'">
              <span class="inline-flex items-center"><span class="step-circle">1</span> Contact</span>
            </li>
            <li><span class="text-gray-300">/</span></li>
            <li :class="completedSteps.address ? 'text-[var(--accent)]' : 'text-gray-400'">
              <span class="inline-flex items-center"><span class="step-circle">2</span> Shipping</span>
            </li>
            <li><span class="text-gray-300">/</span></li>
            <li :class="completedSteps.delivery ? 'text-[var(--accent)]' : 'text-gray-400'">
              <span class="inline-flex items-center"><span class="step-circle">3</span> Delivery</span>
            </li>
            <li><span class="text-gray-300">/</span></li>
            <li :class="completedSteps.payment ? 'text-[var(--accent)]' : 'text-gray-400'">
              <span class="inline-flex items-center"><span class="step-circle">4</span> Payment</span>
            </li>
          </ol>
        </nav>
        <!-- Accordion Steps -->
        <div class="mt-10 divide-y divide-gray-200 border-t border-b border-gray-200">
          <div class="_checkout-contacts">
            <button type="button" class="accordion-btn w-full cursor-pointer py-6 text-left text-lg font-medium transition-colors duration-150 enabled:hover:text-[color:var(--accent)] disabled:text-gray-300 disabled:cursor-not-allowed disabled:pointer-events-none disabled:opacity-70"
              :class="contactsOpen ? 'font-bold text-black' : ''"
              @click="contactsOpen = true; addressOpen = false; deliveryOpen = false; paymentOpen = false"
              :disabled="contactsOpen"
              aria-expanded="contactsOpen"
              aria-controls="contacts-panel"
            ><span class="step-circle">1</span> Contact information</button>
            <div x-show="contactsOpen" id="contacts-panel" class="pb-4"
              x-transition:enter="transition ease-out duration-300"
              x-transition:enter-start="opacity-0 -translate-y-2"
              x-transition:enter-end="opacity-100 translate-y-0"
              x-transition:leave="transition ease-in duration-200"
              x-transition:leave-start="opacity-100 translate-y-0"
              x-transition:leave-end="opacity-0 -translate-y-2"
            >
              {% render 'snippets.checkout-contacts' %}
            </div>
          </div>
          <div class="_checkout-address">
            <button type="button" class="accordion-btn w-full cursor-pointer py-6 text-left text-lg font-medium transition-colors duration-150 enabled:hover:text-[color:var(--accent)] disabled:text-gray-300 disabled:cursor-not-allowed disabled:pointer-events-none disabled:opacity-70"
              :class="addressOpen ? 'font-bold text-black' : ''"
              @click="contactsOpen = false; addressOpen = true; deliveryOpen = false; paymentOpen = false"
              :disabled="!completedSteps.contacts || addressOpen"
              aria-expanded="addressOpen"
              aria-controls="address-panel"
            ><span class="step-circle">2</span> Shipping address</button>
            <div x-show="addressOpen" id="address-panel" class="pb-4"
              x-transition:enter="transition ease-out duration-300"
              x-transition:enter-start="opacity-0 -translate-y-2"
              x-transition:enter-end="opacity-100 translate-y-0"
              x-transition:leave="transition ease-in duration-200"
              x-transition:leave-start="opacity-100 translate-y-0"
              x-transition:leave-end="opacity-0 -translate-y-2"
            >
              {% render 'snippets.checkout-address' %}
            </div>
          </div>
          <div class="_checkout-delivery">
            <button type="button" class="accordion-btn w-full cursor-pointer py-6 text-left text-lg font-medium transition-colors duration-150 enabled:hover:text-[color:var(--accent)] disabled:text-gray-300 disabled:cursor-not-allowed disabled:pointer-events-none disabled:opacity-70"
              :class="deliveryOpen ? 'font-bold text-black' : ''"
              @click="contactsOpen = false; addressOpen = false; deliveryOpen = true; paymentOpen = false"
              :disabled="!completedSteps.address || deliveryOpen"
              aria-expanded="deliveryOpen"
              aria-controls="delivery-panel"
            ><span class="step-circle">3</span> Delivery method</button>
            <div x-show="deliveryOpen" id="delivery-panel"
              x-transition:enter="transition ease-out duration-300"
              x-transition:enter-start="opacity-0 -translate-y-2"
              x-transition:enter-end="opacity-100 translate-y-0"
              x-transition:leave="transition ease-in duration-200"
              x-transition:leave-start="opacity-100 translate-y-0"
              x-transition:leave-end="opacity-0 -translate-y-2"
            >
              {% render 'snippets.checkout-delivery' %}
            </div>
          </div>
          <div class="_checkout-payment">
            <button type="button" class="accordion-btn w-full cursor-pointer py-6 text-left text-lg font-medium transition-colors duration-150 enabled:hover:text-[color:var(--accent)] disabled:text-gray-300 disabled:cursor-not-allowed disabled:pointer-events-none disabled:opacity-70"
              :class="paymentOpen ? 'font-bold text-black' : ''"
              @click="contactsOpen = false; addressOpen = false; deliveryOpen = false; paymentOpen = true"
              :disabled="!completedSteps.delivery || paymentOpen"
              aria-expanded="paymentOpen"
              aria-controls="payment-panel"
            ><span class="step-circle">4</span> Payment method</button>
            <div x-show="paymentOpen" id="payment-panel"
              x-transition:enter="transition ease-out duration-300"
              x-transition:enter-start="opacity-0 -translate-y-2"
              x-transition:enter-end="opacity-100 translate-y-0"
              x-transition:leave="transition ease-in duration-200"
              x-transition:leave-start="opacity-100 translate-y-0"
              x-transition:leave-end="opacity-0 -translate-y-2"
            >
              {% render 'snippets.checkout-payment' %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>

<style>
.step-circle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.5em;
  height: 1.5em;
  border-radius: 9999px;
  background: var(--accent, #6366f1);
  color: #fff;
  font-weight: bold;
  margin-right: 0.5em;
  font-size: 1em;
}
</style>
