{% assign company_name = section.settings.company_name | default: "Nature Face Ltd." %}
{% assign company_address = section.settings.company_address | default: "София, ул. Примерна 123" %}
{% assign company_phone = section.settings.company_phone | default: "+*********** 456" %}
{% assign company_email = section.settings.company_email | default: "<EMAIL>" %}
{% assign company_context = section.settings.company_context | default: "For any questions, partnership inquiries, or support, please use the contact details provided.<br>Our team is available during business hours to assist you." %}

<div class="container mx-auto px-4 w-full flex justify-center my-8" style="scroll-margin-top: var(--header-height, 64px); padding-top: var(--header-height, 64px);">
  <div class="max-w-3xl w-full flex md:flex-row flex-col gap-8 text-[var(--dark)] items-start">
    <div class="flex flex-col gap-6 flex-1">
      <div class="flex items-center gap-4">
        <i class="fa-solid fa-building text-2xl text-[var(--accent)]"></i>
        <span class="font-semibold text-lg">{{ company_name }}</span>
      </div>
      <div class="flex items-center gap-4">
        <i class="fa-solid fa-location-dot text-xl text-[var(--accent)]"></i>
        <span>{{ company_address }}</span>
      </div>
      <div class="flex items-center gap-4">
        <i class="fa-solid fa-phone text-xl text-[var(--accent)]"></i>
        <a href="tel:{{ company_phone | remove: ' ' | remove: '+' }}" class="hover:underline">{{ company_phone }}</a>
      </div>
      <div class="flex items-center gap-4">
        <i class="fa-solid fa-envelope text-xl text-[var(--accent)]"></i>
        <a href="mailto:{{ company_email }}" class="hover:underline">{{ company_email }}</a>
      </div>
    </div>
    <div class="flex-1 flex items-center">
      <div class="text-base text-gray-600">
        {{ company_context }}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Company Info",
  "settings": [
    {
      "type": "text",
      "id": "company_name",
      "label": "Company Name",
      "default": "Nature Face Ltd."
    },
    {
      "type": "text",
      "id": "company_address",
      "label": "Address",
      "default": "София, ул. Примерна 123"
    },
    {
      "type": "text",
      "id": "company_phone",
      "label": "Phone",
      "default": "+*********** 456"
    },
    {
      "type": "text",
      "id": "company_email",
      "label": "Email",
      "default": "<EMAIL>"
    },
    {
      "type": "textarea",
      "id": "company_context",
      "label": "Context Text",
      "default": "For any questions, partnership inquiries, or support, please use the contact details provided.<br>Our team is available during business hours to assist you."
    }
  ]
}
{% endschema %}
