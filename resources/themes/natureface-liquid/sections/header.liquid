<header
    x-data="{
    navOpen: false,
    cartOpen: false,
    toggleNav() {
      this.navOpen = !this.navOpen;
    },
    closeNav() {
      this.navOpen = false;
    }
  }"
  x-init="
    $watch('navOpen', value => {
      if (value) {
        const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
        document.body.classList.add('overflow-hidden');
        document.body.style.paddingRight = scrollbarWidth > 0 ? `${scrollbarWidth}px` : '';
        document.querySelector('.js-header').style.paddingRight = scrollbarWidth > 0 ? `${scrollbarWidth}px` : '';
      } else {
        document.body.classList.remove('overflow-hidden');
        document.body.style.paddingRight = '';
        document.querySelector('.js-header').style.paddingRight = '';
      }
    });
    window.addEventListener('click', (event) => {
      if (!event.target.closest('.js-header-cart')) {
        cartOpen = false;
      }
    });
  "
>
  <nav
    class="js-header fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md shadow-md">
    <div class="container mx-auto px-4">
      <div class="relative flex items-center justify-between h-16">
        <button
          class="focus:outline-none cursor-pointer hover:text-[var(--accent)] text-[var(--dark)]"
          aria-label="Open Menu"
          @click="toggleNav()"
          @keydown.escape="closeNav()"
        >
            <span class="relative block w-7 h-7">
              <span
                class="absolute left-0 w-7 h-0.5 bg-current rounded transition-all duration-300"
                :class="navOpen
                  ? 'rotate-45 top-3.5'
                  : 'top-1.5'"
              ></span>
              <span
                class="absolute left-0 w-7 h-0.5 bg-current rounded transition-all duration-300"
                :class="navOpen
                  ? 'opacity-0 top-3.5'
                  : 'opacity-100 top-3.5'"
              ></span>
              <span
                class="absolute left-0 w-7 h-0.5 bg-current rounded transition-all duration-300"
                :class="navOpen
                  ? '-rotate-45 top-3.5'
                  : 'top-5.5'"
              ></span>
            </span>
        </button>

        <!-- Logo -->
        <a href="/"
          class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-300">
          {% if section.settings.logo_main %}
            <img src="{{ section.settings.logo_main.src | img_url: '200x' }}" alt="{{ section.settings.logo_alt }}" class="h-10 w-auto" />
          {% endif %}
        </a>
        <div class="flex items-center space-x-4">
          <!-- User, Wishlist, Cart placeholders -->
          <div class="flex items-center space-x-4" x-data="{ userMenuOpen: false, cartOpen: false }">
            <div class="relative leading-none">
              <button
                class="_user-controls transition cursor-pointer hover:text-[var(--accent)]"
                @click="
                  if (!(window.cc_customer_data && window.cc_customer_data.id)) {
                    window.location.href = '/auth/login';
                  } else {
                    window.location.href = '/account';
                  }
                "
                @mouseenter="
                  if (window.cc_customer_data && window.cc_customer_data.id && window.innerWidth >= 768) {
                    userMenuOpen = true;
                    cartOpen = false;
                  }
                "
                @mouseleave="if (window.innerWidth >= 768) userMenuOpen = false"
                @keydown.escape="userMenuOpen = false"
                aria-label="User Menu"
                type="button"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user h-6 w-6"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
              </button>
              <!-- User Dropdown Start -->
              <div
                x-show="userMenuOpen && window.cc_customer_data && window.cc_customer_data.id"
                x-transition:enter="transition ease-out duration-200"
                x-transition:enter-start="opacity-0 -translate-y-2"
                x-transition:enter-end="opacity-100 translate-y-0"
                x-transition:leave="transition ease-in duration-150"
                x-transition:leave-start="opacity-100 translate-y-0"
                x-transition:leave-end="opacity-0 -translate-y-2"
                @mouseenter="
                  if (window.innerWidth >= 768) {
                    userMenuOpen = true;
                    cartOpen = false;
                  }
                "
                @mouseleave="if (window.innerWidth >= 768) userMenuOpen = false"
                class="_user-menu-dropdown absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black/5 z-50 mt-3"
                style="display: none;"
              >
                <ul class="py-2 text-gray-700">
                  <li>
                    <a href="/account/orders" class="block px-4 py-2 hover:bg-gray-100 transition">
                      {{ 'customer.account.menu.orders' | t | default: 'Orders' }}
                    </a>
                  </li>
                  <li>
                    <a href="/account/payments" class="block px-4 py-2 hover:bg-gray-100 transition">
                      {{ 'customer.account.menu.payments' | t | default: 'Payments' }}
                    </a>
                  </li>
                  <li>
                    <a href="/account/wishlist" class="block px-4 py-2 hover:bg-gray-100 transition">
                      {{ 'customer.account.menu.favorites' | t | default: 'Favorite products' }}
                    </a>
                  </li>
                  <li>
                    <a href="/account/files" class="block px-4 py-2 hover:bg-gray-100 transition">
                      {{ 'customer.account.menu.files' | t | default: 'Files' }}
                    </a>
                  </li>
                  <li>
                    <a href="/account" class="block px-4 py-2 hover:bg-gray-100 transition">
                      {{ 'customer.account.menu.details' | t | default: 'Details' }}
                    </a>
                  </li>
                  <li>
                    <a href="/auth/logout" class="block px-4 py-2 hover:bg-gray-100 transition">
                      {{ 'customer.account.menu.logout' | t | default: 'Logout' }}
                    </a>
                  </li>
                </ul>
              </div>
              <!-- User Dropdown End -->
            </div>
            <div
              @mouseenter="
                cartOpen = true;
                if (window.innerWidth >= 768) userMenuOpen = false;
              "
              class="relative js-header-cart"
              x-data
            >
              <a
                href="{% if section.settings.cart_behavior == 'sidebar' %}#{% else %}/cart{% endif %}"
                class="relative transition cursor-pointer hover:text-[var(--accent)] text-[var(--dark)]"
                {% if section.settings.cart_behavior == 'sidebar' %} @click.prevent="window.openCartSidebar && window.openCartSidebar()" {% endif %}
              >
                {% if cart != empty %}
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shopping-cart h-6 w-6"><circle cx="8" cy="21" r="1"></circle><circle cx="19" cy="21" r="1"></circle><path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path></svg>
                {% endif %}
                <span class="absolute -top-2 -right-2 bg-[var(--accent)] text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span>
              </a>
              <div
                @mouseover="cartOpen = true; if (window.innerWidth >= 768) userMenuOpen = false;"
                @mouseleave="cartOpen = false"
                class="absolute inset-x-0 top-16 mt-px bg-white pb-6 shadow-lg sm:px-2 lg:top-full lg:right-0 lg:left-auto lg:mt-3 lg:-mr-1.5 lg:w-80 lg:rounded-lg lg:ring-1 lg:ring-black/5"
                x-show="cartOpen"
                x-transition:enter="transition ease-out duration-200"
                x-transition:enter-start="opacity-0 translate-y-1"
                x-transition:enter-end="opacity-100 translate-y-0"
              >
                <h2 class="sr-only">Shopping Cart</h2>
                <form class="mx-auto max-w-2xl px-4">
                  {% if section.settings.cart.items and section.settings.cart.items.size > 0 %}
                  <ul role="list" class="divide-y divide-gray-200">
                    {% for item in section.settings.cart.items %}
                    <li class="flex items-center py-6">
                      <img src="{{ item.image }}" alt="{{ item.product.title }}" class="size-16 flex-none rounded-md border border-gray-200">
                      <div class="ml-4 flex-auto">
                        <h3 class="font-medium text-gray-900">
                          <a href="{{ item.url }}">{{ item.product.title }}</a>
                        </h3>
                        {% for option in item.options_with_values %}
                          <p class="text-gray-500">{{ option.name }}: {{ option.value }}</p>
                        {% endfor %}
                        <p class="text-gray-500">Quantity: {{ item.quantity }}</p>
                        <p class="text-gray-900 font-semibold">{{ item.final_price }}</p>
                      </div>
                    </li>
                    {% endfor %}
                    <!-- More products... -->
                  </ul>
                  <a href="/test-liquid-theme/checkout-template" class="w-full block text-center rounded-full border border-transparent bg-[var(--accent)] hover:bg-[var(--accent)]/90 px-4 py-2 text-sm font-medium text-white shadow-xs focus:outline-hidden cursor-pointer">
                    Checkout
                  </a>
                  {% else %}
                  <div class="py-8 text-center text-gray-500">
                    {{ 'cart.general.empty' | t | default: 'Your cart is empty.' }}
                  </div>
                  {% endif %}
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nav>
  <div class="_header-navigation fixed -translate-y-full z-40 left-auto top-0 w-full h-full bg-white/80 backdrop-blur-md shadow-md transition-transform duration-500"
    :class="navOpen ? 'translate-y-0' : '-translate-y-full'"
  >
  <ul class="_header-navigation flex flex-col items-center justify-center h-full space-y-8 overflow-y-auto max-h-full">
    {% for link in section.settings.menu.links %}
      <li>
        <a href="{{ link.page_url }}" class="text-[var(--dark)] hover:text-[var(--accent)] cursor-pointer text-2xl font-semibold">
          {{ link.title }}
        </a>
      </li>
    {% endfor  %}
  </ul>
  </div>
</header>
{% render 'cart-sidebar' %}

{% schema %}
{
  "name": "t:sections.header.name",
  "class": "section-header",
  "max_blocks": 3,
  "settings": [
    {
      "type": "select",
      "id": "logo_position",
      "options": [
        {
          "value": "top-left",
          "label": "t:sections.header.settings.logo_position.options__2.label"
        },
        {
          "value": "top-center",
          "label": "t:sections.header.settings.logo_position.options__3.label"
        },
        {
          "value": "middle-left",
          "label": "t:sections.header.settings.logo_position.options__1.label"
        },
        {
          "value": "middle-center",
          "label": "t:sections.header.settings.logo_position.options__4.label"
        }
      ],
      "default": "middle-left",
      "label": "t:sections.header.settings.logo_position.label",
      "info": "t:sections.header.settings.logo_help.content"
    },
    {
      "type": "link_list",
      "id": "menu",
      "default": "main-menu",
      "label": "t:sections.header.settings.menu.label"
    },
    {
      "type": "select",
      "id": "menu_type_desktop",
      "options": [
        {
          "value": "dropdown",
          "label": "t:sections.header.settings.menu_type_desktop.options__1.label"
        },
        {
          "value": "mega",
          "label": "t:sections.header.settings.menu_type_desktop.options__2.label"
        },
        {
          "value": "drawer",
          "label": "t:sections.header.settings.menu_type_desktop.options__3.label"
        }
      ],
      "default": "dropdown",
      "label": "t:sections.header.settings.menu_type_desktop.label",
      "info": "t:sections.header.settings.menu_type_desktop.info"
    },
    {
        "type": "color",
        "id": "background-color",
        "label": "background color",
        "default": "#21332B"
      },
     {
        "type": "color",
        "id": "text-color",
        "label": "menu text color",
        "default": "#fff"
      },
    {
      "type": "select",
      "id": "sticky_header_type",
      "options": [
        {
          "value": "none",
          "label": "t:sections.header.settings.sticky_header_type.options__1.label"
        },
        {
          "value": "on-scroll-up",
          "label": "t:sections.header.settings.sticky_header_type.options__2.label"
        },
        {
          "value": "always",
          "label": "t:sections.header.settings.sticky_header_type.options__3.label"
        },
        {
          "value": "reduce-logo-size",
          "label": "t:sections.header.settings.sticky_header_type.options__4.label"
        }
      ],
      "default": "on-scroll-up",
      "label": "t:sections.header.settings.sticky_header_type.label"
    },
    {
      "type": "checkbox",
      "id": "show_line_separator",
      "default": true,
      "label": "t:sections.header.settings.show_line_separator.label"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.header__1.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
      {
      "type": "color_scheme",
      "id": "menu_color_scheme",
      "label": "t:sections.header.settings.menu_color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.header__3.content",
      "info": "t:sections.header.settings.header__4.info"
    },
    {
      "type": "checkbox",
      "id": "enable_country_selector",
      "default": false,
      "label": "t:sections.header.settings.enable_country_selector.label"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.header__5.content",
      "info": "t:sections.header.settings.header__6.info"
    },
    {
      "type": "checkbox",
      "id": "enable_language_selector",
      "default": false,
      "label": "t:sections.header.settings.enable_language_selector.label"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.mobile_layout.content"
    },
    {
      "type": "select",
      "id": "mobile_logo_position",
      "options": [
        {
          "value": "center",
          "label": "t:sections.header.settings.mobile_logo_position.options__1.label"
        },
        {
          "value": "left",
          "label": "t:sections.header.settings.mobile_logo_position.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.header.settings.mobile_logo_position.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.spacing"
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.header.settings.margin_bottom.label",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 36,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 20
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 36,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 20
    },
    {
      "type": "select",
      "id": "cart_behavior",
      "label": "Cart Behavior",
      "options": [
        { "value": "sidebar", "label": "Sidebar" },
        { "value": "page", "label": "Cart Page" }
      ],
      "default": "sidebar"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ]
}
{% endschema %}
