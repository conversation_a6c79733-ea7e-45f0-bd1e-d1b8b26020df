<section class="_section-benefits lg:py-12 py-4 bg-white">
    <div class="container mx-auto px-4">
        <div class="mb-6">
            <h2 class="text-3xl md:text-4xl mb-2 text-center">{{ section.settings.title | raw }}</h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            {% for benefit in section.settings.benefits %}
            <div class="rounded-lg md:p-6 p-3 text-center max-w-[360px] md:max-w-none mx-auto {{ benefit.class }}">
                <div class="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-2">
                    <img src="{{ benefit.icon }}" alt="{{ benefit.title }}">
                </div>
                <h3 class="text-xl mb-2">{{ benefit.title }}</h3>
                <p class="text-[var(--dark)] text-sm/5">{{ benefit.description }}</p>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

{% schema %}
{
  "name": "Benefits",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Benefits"
    },
    {
      "type": "list",
      "id": "benefits",
      "label": "Benefits",
      "default": [],
      "item": {
        "type": "object",
        "properties": [
          {
            "type": "image_picker",
            "id": "icon",
            "label": "Icon"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Title"
          },
          {
            "type": "textarea",
            "id": "description",
            "label": "Description"
          },
          {
            "type": "text",
            "id": "class",
            "label": "Custom CSS Class",
            "default": ""
          }
        ]
      }
    }
  ]
}
{% endschema %}
