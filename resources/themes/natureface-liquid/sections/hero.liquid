<header class="relative h-[514px] md:h-[600px]"
  x-data="{
    scrolled: false,
    handleScroll() {
      this.scrolled = window.scrollY > 0;
    },
    navOpen: false,
    toggleNav() {
      this.navOpen = !this.navOpen;
    },
    closeNav() {
      this.navOpen = false;
    }
  }"
  x-init="
    handleScroll();
    window.addEventListener('scroll', () => handleScroll());
    $watch('navOpen', value => {
      if (value) {
        const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
        document.body.classList.add('overflow-hidden');
        document.body.style.paddingRight = scrollbarWidth > 0 ? `${scrollbarWidth}px` : '';
        document.querySelector('.js-header').style.paddingRight = scrollbarWidth > 0 ? `${scrollbarWidth}px` : '';
      } else {
        document.body.classList.remove('overflow-hidden');
        document.body.style.paddingRight = '';
        document.querySelector('.js-header').style.paddingRight = '';
      }
    });
  "
>
  <nav
    class="js-header fixed top-0 left-0 right-0 z-50"
    :class="scrolled ? 'bg-white/80 backdrop-blur-md shadow-md' : 'bg-transparent'"
  >
    <div class="container mx-auto px-4">
      <div class="relative flex items-center justify-between h-16">
        <button
          class="focus:outline-none cursor-pointer hover:text-[var(--accent)]"
          :class="scrolled || navOpen ? 'text-[var(--dark)]' : 'text-white'"
          aria-label="Open Menu"
          @click="toggleNav()"
          @keydown.escape="closeNav()"
        >
            <span class="relative block w-7 h-7">
              <span
                class="absolute left-0 w-7 h-0.5 bg-current rounded transition-all duration-300"
                :class="navOpen
                  ? 'rotate-45 top-3.5'
                  : 'top-1.5'"
              ></span>
              <span
                class="absolute left-0 w-7 h-0.5 bg-current rounded transition-all duration-300"
                :class="navOpen
                  ? 'opacity-0 top-3.5'
                  : 'opacity-100 top-3.5'"
              ></span>
              <span
                class="absolute left-0 w-7 h-0.5 bg-current rounded transition-all duration-300"
                :class="navOpen
                  ? '-rotate-45 top-3.5'
                  : 'top-5.5'"
              ></span>
            </span>
        </button>

        <!-- Logo -->
        <a href="/"
          class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-300"
          :class="scrolled ? '' : 'opacity-0 invisible'"
        >
          {% if section.settings.logo_main %}
            <img src="{{ section.settings.logo_main.src | img_url: '200x' }}" alt="{{ section.settings.logo_alt }}" class="h-10 w-auto" />
          {% endif %}
        </a>
        <div class="flex items-center space-x-4">
          <!-- User, Wishlist, Cart placeholders -->
            <div class="_user-controls-wrap flex items-center space-x-4" x-data="{ userMenuOpen: false, cartOpen: false }">
              <div class="relative leading-none">
                <button
                  class="_user-controls transition cursor-pointer hover:text-[var(--accent)]"
                  :class="scrolled || navOpen ? 'text-[var(--dark)]' : 'text-white'"
                  @click="
                    if (!(window.cc_customer_data && window.cc_customer_data.id)) {
                      window.location.href = '/auth/login';
                    } else {
                      window.location.href = '/account';
                    }
                  "
                  @mouseenter="
                    if (window.cc_customer_data && window.cc_customer_data.id && window.innerWidth >= 768) {
                      userMenuOpen = true;
                      cartOpen = false;
                    }
                  "
                  @mouseleave="if (window.innerWidth >= 768) userMenuOpen = false"
                  @keydown.escape="userMenuOpen = false"
                  aria-label="User Menu"
                  type="button"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user h-6 w-6"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                </button>
                <!-- User Dropdown Start -->
                <div
                  x-show="userMenuOpen && window.cc_customer_data && window.cc_customer_data.id"
                  x-transition:enter="transition ease-out duration-200"
                  x-transition:enter-start="opacity-0 -translate-y-2"
                  x-transition:enter-end="opacity-100 translate-y-0"
                  x-transition:leave="transition ease-in duration-150"
                  x-transition:leave-start="opacity-100 translate-y-0"
                  x-transition:leave-end="opacity-0 -translate-y-2"
                  @mouseenter="
                    if (window.innerWidth >= 768) {
                      userMenuOpen = true;
                      cartOpen = false;
                    }
                  "
                  @mouseleave="if (window.innerWidth >= 768) userMenuOpen = false"
                  class="_user-menu-dropdown absolute lg:top-full right-0 w-56 rounded-md shadow-lg bg-white ring-1 ring-black/5 z-50 mt-3"
                  style="display: none;"
                >
                  <ul class="py-2 text-gray-700">
                    <li>
                      <a href="/account/orders" class="block px-4 py-2 hover:bg-gray-100 transition">
                        {{ 'customer.account.menu.orders' | t | default: 'Orders' }}
                      </a>
                    </li>
                    <li>
                      <a href="/account/payments" class="block px-4 py-2 hover:bg-gray-100 transition">
                        {{ 'customer.account.menu.payments' | t | default: 'Payments' }}
                      </a>
                    </li>
                    <li>
                      <a href="/account/wishlist" class="block px-4 py-2 hover:bg-gray-100 transition">
                        {{ 'customer.account.menu.favorites' | t | default: 'Favorite products' }}
                      </a>
                    </li>
                    <li>
                      <a href="/account/files" class="block px-4 py-2 hover:bg-gray-100 transition">
                        {{ 'customer.account.menu.files' | t | default: 'Files' }}
                      </a>
                    </li>
                    <li>
                      <a href="/account" class="block px-4 py-2 hover:bg-gray-100 transition">
                        {{ 'customer.account.menu.details' | t | default: 'Details' }}
                      </a>
                    </li>
                    <li>
                      <a href="/auth/logout" class="block px-4 py-2 hover:bg-gray-100 transition">
                        {{ 'customer.account.menu.logout' | t | default: 'Logout' }}
                      </a>
                    </li>
                  </ul>
                </div>
                <!-- User Dropdown End -->
              </div>
              <!-- Cart Dropdown Start -->
              <div x-data x-init="cartOpen = false" @mouseenter="
    cartOpen = true;
    if (window.innerWidth >= 768) userMenuOpen = false;
  " class="relative js-header-cart">
                <a
                  href="{% if section.settings.cart_behavior == 'sidebar' %}#{% else %}/cart{% endif %}"
                  class="relative transition cursor-pointer hover:text-[var(--accent)]"
                  :class="scrolled || navOpen ? 'text-[var(--dark)]' : 'text-white'"
                  {% if section.settings.cart_behavior == 'sidebar' %} @click.prevent="window.openCartSidebar && window.openCartSidebar()" {% endif %}
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shopping-cart h-6 w-6"><circle cx="8" cy="21" r="1"></circle><circle cx="19" cy="21" r="1"></circle><path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path></svg>
                    <span class="absolute -top-2 -right-2 bg-[var(--accent)] text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span>
                </a>
                <div
                  @mouseover="cartOpen = true; if (window.innerWidth >= 768) userMenuOpen = false;"
                  @mouseleave="cartOpen = false"
                  x-show="cartOpen"
                  x-transition:enter="transition ease-out duration-200"
                  x-transition:enter-start="opacity-0 translate-y-1"
                  x-transition:enter-end="opacity-100 translate-y-0"
                  x-transition:leave="transition ease-in duration-150"
                  x-transition:leave-start="opacity-100 translate-y-0"
                  x-transition:leave-end="opacity-0 translate-y-1"
                  class="absolute inset-x-0 top-16 mt-px bg-white pb-6 shadow-lg sm:px-2 lg:top-full lg:right-0 lg:left-auto lg:mt-3 lg:-mr-1.5 lg:w-80 lg:rounded-lg lg:ring-1 lg:ring-black/5 hidden"
                  :class="{ 'hidden': !cartOpen, 'block': cartOpen }"
                  @click.away="cartOpen = false"
                >
                  <h2 class="sr-only">Shopping Cart</h2>
                  <form class="mx-auto max-w-2xl px-4">
                    {% if section.settings.cart.items and section.settings.cart.items.size > 0 %}
                    <ul role="list" class="divide-y divide-gray-200">
                      {% for item in section.settings.cart.items %}
                      <li class="flex items-center py-6">
                        <img src="{{ item.image }}" alt="{{ item.product.title }}" class="size-16 flex-none rounded-md border border-gray-200">
                        <div class="ml-4 flex-auto">
                          <h3 class="font-medium text-gray-900">
                            <a href="{{ item.url }}">{{ item.product.title }}</a>
                          </h3>
                          {% for option in item.options_with_values %}
                            <p class="text-gray-500">{{ option.name }}: {{ option.value }}</p>
                          {% endfor %}
                          <p class="text-gray-500">Quantity: {{ item.quantity }}</p>
                          <p class="text-gray-900 font-semibold">{{ item.final_price }}</p>
                        </div>
                      </li>
                      {% endfor %}
                      <!-- More products... -->
                    </ul>
                    <a href="/test-liquid-theme/checkout-template" class="w-full block text-center rounded-full border border-transparent bg-[var(--accent)] hover:bg-[var(--accent)]/90 px-4 py-2 text-sm font-medium text-white shadow-xs focus:outline-hidden cursor-pointer">
                      Checkout
                    </a>
                    {% else %}
                    <div class="py-8 text-center text-gray-500">
                      {{ 'cart.general.empty' | t | default: 'Your cart is empty.' }}
                    </div>
                    {% endif %}
                  </form>
                </div>
              </div>
              <!-- Cart Dropdown End -->
            </div>
        </div>
      </div>
    </div>
  </nav>
  <div class="absolute inset-0 w-full h-[514px] md:h-[600px] overflow-hidden">
    {% if section.settings.videos.desktop %}
      <video src="{{ section.settings.videos.desktop }}" class="hidden sm:block absolute inset-0 w-full h-full object-cover" autoplay loop muted playsinline></video>
    {% endif %}
    {% if section.settings.videos.mobile %}
      <video src="{{ section.settings.videos.mobile }}" class="block sm:hidden absolute inset-0 w-full h-full object-cover" autoplay loop muted playsinline></video>
    {% endif %}
    <div class="absolute inset-0 bg-[#3a0024]/30"></div>
  </div>
  <div class="relative z-10 container mx-auto px-4 flex flex-col items-center h-full justify-center">
    {% if section.settings.logo %}
      <div class="w-full md:w-1/3 mx-auto mb-2 mt-16">
        <img alt="{{ section.settings.logo.alt }}" class="w-full h-auto" src="{{ section.settings.logo.src | img_url: '400x' }}">
      </div>
    {% endif %}
    <div class="text-center">      
      {% if section.settings.title %}
        <h3 class="text-4xl md:text-5xl text-white mb-2">{{ section.settings.title }}</h3>
      {%  else %}
        <h1 class="text-xl! font-bold text-white mb-2">{{ section.settings.headline }}</h1>
      {% endif %}
      <p class="text-white mb-8 {{ section.settings.subheadline_class }}">{{ section.settings.subheadline }}</p>
    </div>
    {% if section.settings.cta_link and section.settings.cta_text %}
      <div class="absolute left-1/2 -translate-x-1/2 bottom-20 z-20">
        <a class="bg-white hover:bg-gray-100 text-gray-800 px-8 py-3 rounded-full inline-block shadow-lg text-center whitespace-nowrap"
           href="{{ section.settings.cta_link }}">{{ section.settings.cta_text }}</a>
      </div>
    {% endif %}
  </div>

  <div class="_header-navigation fixed -translate-y-full z-40 left-auto top-0 w-full h-full bg-white/80 backdrop-blur-md shadow-md transition-transform duration-500"
    :class="navOpen ? 'translate-y-0' : '-translate-y-full'"
  >
  {% comment %} <button
    class="absolute top-4 right-4 focus:outline-none hover:text-[var(--accent)] cursor-pointer"
    @click="closeNav()"
    aria-label="Close Menu"
  >
    <svg class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M6 18L18 6M6 6l12 12"></path>
    </svg>
  </button> {% endcomment %}
  <ul class="flex flex-col items-center justify-center h-full space-y-8 overflow-y-auto max-h-full">
    {% for link in section.settings.menu.links %}
      <li>
        <a href="{{ link.page_url }}" class="text-[var(--dark)] hover:text-[var(--accent)] cursor-pointer text-2xl font-semibold">
          {{ link.title }}
        </a>
      </li>
    {% endfor  %}
  </ul>
</div>
</header>

<div class="fixed bottom-8 right-8 z-50">
  <button
    type="button"
    class="px-4 py-2 bg-[var(--accent)] text-white rounded shadow cursor-pointer hover:bg-[var(--accent)]/90 focus:outline-none"
    onclick="window.openExampleModal && window.openExampleModal()"
  >
    Test Modal
  </button>
</div>
{% render 'modal',
  modal_header: section.settings.modal_header | default: 'Modal Title',
  modal_size: section.settings.modal_size | default: 'max-w-lg',
  show_header: section.settings.show_header | default: true,
  show_footer: section.settings.show_footer | default: true,
  modal_content: section.settings.modal_content | default: 'This is a placeholder for modal content. You can put any settings or content here.',
%}
{% render 'cart-sidebar' %}

{% schema %}
{
  "name": "Hero Section",
  "settings": [
    {
      "type": "image_picker",
      "id": "logo_main",
      "label": "Main Logo",
      "default": {
        "src": "https://cdncloudcart.com/59560/files/image/logo-682355eeab6fe.svg?1747146224",
        "alt": "Logo"
      }
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Logo",
      "default": {
        "src": "https://cdncloudcart.com/59560/files/image/logo.svg?1746793498",
        "alt": "Logo"
      }
    },
    {
      "type": "text",
      "id": "headline",
      "label": "Headline",
      "default": "Вдъхновение от природата, създадено с любов... за всеки тип кожа"
    },
    {
      "type": "text",
      "id": "subheadline",
      "label": "Subheadline",
      "default": "Специално създадени да бъдат част от вашите Face Yoga Ритуали"
    },
    {
      "type": "text",
      "id": "cta_text",
      "label": "CTA Text",
      "default": "ОТКРИЙ СЕГА"
    },
    {
      "type": "url",
      "id": "cta_link",
      "label": "CTA Link",
      "default": "#products"
    },
    {
      "type": "header",
      "content": "Videos"
    },
    {
      "type": "url",
      "id": "video_desktop",
      "label": "Desktop Video URL",
      "default": "https://cdncloudcart.com/59560/files/video/hero.mp4"
    },
    {
      "type": "url",
      "id": "video_mobile",
      "label": "Mobile Video URL",
      "default": "https://cdncloudcart.com/59560/files/video/hero-34.mp4?1745497572"
    },
    {
      "id": "navigation",
      "label": "Navigation Links",
      "default": [],
      "fields": [
        {
          "type": "text",
          "id": "name",
          "label": "Link Name"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Link URL"
        }
      ]
    },
    {
      "type": "select",
      "id": "cart_behavior",
      "label": "Cart Behavior",
      "options": [
        { "value": "sidebar", "label": "Sidebar" },
        { "value": "page", "label": "Cart Page" }
      ],
      "default": "sidebar"
    },
    {
      "type": "select",
      "id": "modal_size",
      "label": "Modal Size",
      "options": [
        { "value": "max-w-sm", "label": "Small" },
        { "value": "max-w-md", "label": "Medium" },
        { "value": "max-w-lg", "label": "Large" },
        { "value": "max-w-xl", "label": "X-Large" },
        { "value": "max-w-2xl", "label": "2X-Large" }
      ],
      "default": "max-w-lg"
    },
    {
      "type": "checkbox",
      "id": "show_header",
      "label": "Show Modal Header",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_footer",
      "label": "Show Modal Footer",
      "default": true
    }
  ]
}
{% endschema %}
