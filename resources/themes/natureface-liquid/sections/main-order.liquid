{% comment %} {{ 'customer.css' | asset_url | stylesheet_tag }} {% endcomment %}

{%- assign order = section.settings.order | default: customer_order -%}

{% if order %}
<div class="_customer-order pt-20 w-full max-w-[33.4rem] md:max-w-[47.8rem] lg:max-w-4xl mx-auto px-6 md:px-12 text-center md:text-left bg-white p-6 pt-20 rounded-lg shadow">
  <div class="mb-6 flex flex-col md:flex-row items-start md:items-center justify-between gap-2 md:gap-0">
    <h1 class="customer__title mt-0 mb-4 text-2xl md:text-3xl font-bold tracking-tight">{{ 'customer.account.title' | t }}</h1>
    <a href="{{ routes.account_url }}" class="text-[var(--accent,#6366f1)] underline text-base md:text-lg">{{ 'customer.account.return' | t }}</a>
  </div>

  <div>
    <div class="mb-8">
      <h2 class="text-xl md:text-2xl font-semibold mb-2">{{ 'customer.order.title' | t: name: order.name }}</h2>
      {%- assign order_date = order.created_at | time_tag: format: 'date_at_time' -%}
      <p class="text-gray-500 text-base md:text-lg mb-2">{{ 'customer.order.date_html' | t: date: order_date }}</p>
      {%- if order.cancelled -%}
        {%- assign cancelled_at = order.cancelled_at | time_tag: format: 'date_at_time' -%}
        <p class="text-red-500 text-base md:text-lg">{{ 'customer.order.cancelled_html' | t: date: cancelled_at }}</p>
        <p class="text-red-400 text-base md:text-lg">{{ 'customer.order.cancelled_reason' | t: reason: order.cancel_reason_label }}</p>
      {%- endif -%}

      <div class="overflow-x-auto">
        <table class="w-full table-auto border-collapse border-b border-gray-200 shadow-none text-base md:text-lg relative">
          <thead class="bg-gray-50 hidden sm:table-header-group">
            <tr>
              <th class="px-6 py-4 font-medium text-xs md:text-sm uppercase tracking-wider text-left">{{ 'customer.order.product' | t }}</th>
              <th class="px-6 py-4 font-medium text-xs md:text-sm uppercase tracking-wider text-left">{{ 'customer.order.sku' | t }}</th>
              <th class="px-6 py-4 font-medium text-xs md:text-sm uppercase tracking-wider text-left">{{ 'customer.order.price' | t }}</th>
              <th class="px-6 py-4 font-medium text-xs md:text-sm uppercase tracking-wider text-left">{{ 'customer.order.quantity' | t }}</th>
              <th class="px-6 py-4 font-medium text-xs md:text-sm uppercase tracking-wider text-left">{{ 'customer.order.total' | t }}</th>
            </tr>
          </thead>
          <tbody class="text-gray-900">
            {%- for line_item in section.settings.line_items -%}
              <tr class="border-t border-gray-200 flex flex-col sm:flex-row sm:table-row w-full">
                <td class="px-6 py-4 align-top text-left flex sm:block w-full" data-label="{{ 'customer.order.product' | t }}">
                  <div class="flex flex-col items-end md:items-start w-full">
                    {%- if line_item.url != blank -%}
                      <a href="{{ line_item.url }}" class="text-[var(--accent,#6366f1)] underline text-base md:text-lg">{{ line_item.title | escape }}</a>
                    {%- else -%}
                      <p class="text-base md:text-lg">{{ line_item.title | escape }}</p>
                    {%- endif -%}
                    {%- assign property_size = line_item.properties | size -%}
                    {%- unless line_item.selling_plan_allocation == null and property_size == 0 -%}
                      <div class="text-xs text-gray-500 mt-1">
                        {%- unless line_item.product.has_only_default_variant -%}
                          <span>{{ line_item.variant.title | escape }}</span>
                        {%- endunless -%}
                        {%- unless line_item.selling_plan_allocation == null -%}
                          <span>{{ line_item.selling_plan_allocation.selling_plan.name }}</span>
                        {%- endunless -%}
                        {%- if property_size != 0 -%}
                          {%- for property in line_item.properties -%}
                            {% assign property_first_char = property.first | slice: 0 %}
                            {%- if property.last != blank and property_first_char != '_' -%}
                              <span>{{ property.first }}:</span>
                              <span>
                                {%- if property.last contains '/uploads/' -%}
                                  <a href="{{ property.last }}">{{ property.last | split: '/' | last }}</a>
                                {%- else -%}
                                  {{ property.last }}
                                {%- endif -%}
                              </span>
                            {%- endif -%}
                          {%- endfor -%}
                        {%- endif -%}
                      </div>
                    {%- endunless -%}
                    {%- if line_item.line_level_discount_allocations != blank -%}
                      <ul class="text-xs text-green-600 mt-1">
                        {%- for discount_allocation in line_item.line_level_discount_allocations -%}
                          <li>
                            <span>🔖</span>
                            {{- discount_allocation.discount_application.title | escape }} (-
                            {{- discount_allocation.amount | money -}}
                            )
                          </li>
                        {%- endfor -%}
                      </ul>
                    {%- endif -%}
                    {%- if line_item.fulfillment -%}
                      <div class="text-xs text-blue-600 mt-1">
                        {%- assign created_at = line_item.fulfillment.created_at | time_tag: format: 'date' -%}
                        <span>{{ 'customer.order.fulfilled_at_html' | t: date: created_at }}</span>
                        {%- if line_item.fulfillment.tracking_url -%}
                          <a href="{{ line_item.fulfillment.tracking_url }}" class="ml-2 underline">{{ 'customer.order.track_shipment' | t }}</a>
                        {%- endif -%}
                        <span class="ml-2">
                          {{ line_item.fulfillment.tracking_company }}
                          {%- if line_item.fulfillment.tracking_number -%}
                            #{{ line_item.fulfillment.tracking_number }}
                          {%- endif -%}
                        </span>
                      </div>
                    {%- endif -%}
                  </div>
                </td>
                <td class="px-6 py-4 align-top text-left flex sm:block w-full" data-label="{{ 'customer.order.sku' | t }}">{{ line_item.sku }}</td>
                <td class="px-6 py-4 align-top text-left flex sm:block w-full" data-label="{{ 'customer.order.price' | t }}">
                  {%- if line_item.original_price != line_item.final_price or line_item.unit_price_measurement -%}
                    <dl>
                      {%- if line_item.original_price != line_item.final_price -%}
                        <dt class="sr-only">{{ 'products.product.price.regular_price' | t }}</dt>
                        <dd class="line-through text-gray-400">{{ line_item.original_price | money }}</dd>
                        <dt class="sr-only">{{ 'products.product.price.sale_price' | t }}</dt>
                        <dd class="text-green-600">{{ line_item.final_price | money }}</dd>
                      {%- else -%}
                        <dt class="sr-only">{{ 'products.product.price.regular_price' | t }}</dt>
                        <dd>{{ line_item.original_price | money }}</dd>
                      {%- endif -%}
                      {%- if line_item.unit_price_measurement -%}
                        <dt class="sr-only">{{ 'products.product.price.unit_price' | t }}</dt>
                        <dd class="text-xs">
                          <span data-unit-price>{{ line_item.unit_price | money }}</span>
                          /
                          {%- if line_item.unit_price_measurement.reference_value != 1 -%}
                            {{- line_item.unit_price_measurement.reference_value -}}
                          {%- endif -%}
                          {{ line_item.unit_price_measurement.reference_unit }}
                        </dd>
                      {%- endif -%}
                    </dl>
                  {%- else -%}
                    <span>{{ line_item.final_price | money }}</span>
                  {%- endif -%}
                </td>
                <td class="px-6 py-4 align-top text-left flex sm:block w-full" data-label="{{ 'customer.order.quantity' | t }}">{{ line_item.quantity }}</td>
                <td class="px-6 py-4 align-top text-left flex sm:block w-full" data-label="{{ 'customer.order.total' | t }}">
                  {%- if line_item.original_line_price != line_item.final_line_price -%}
                    <dl>
                      <dt class="sr-only">{{ 'products.product.price.regular_price' | t }}</dt>
                      <dd class="line-through text-gray-400">{{ line_item.original_line_price | money }}</dd>
                      <dt class="sr-only">{{ 'products.product.price.sale_price' | t }}</dt>
                      <dd class="text-green-600">{{ line_item.final_line_price | money }}</dd>
                    </dl>
                  {%- else -%}
                    {{ line_item.original_line_price | money }}
                  {%- endif -%}
                </td>
              </tr>
            {%- endfor -%}
          </tbody>
          <tfoot class="bg-gray-50 hidden sm:table-footer-group">
            <tr>
              <td colspan="4" class="px-6 py-4 font-semibold text-left">{{ 'customer.order.subtotal' | t }}</td>
              <td class="px-6 py-4 font-semibold text-left">{{ order.line_items_subtotal_price | money }}</td>
            </tr>
            {%- if order.cart_level_discount_applications != blank -%}
              {%- for discount_application in order.cart_level_discount_applications -%}
                <tr>
                  <td colspan="4" class="px-6 py-4 text-green-600 text-left">{{ 'customer.order.discount' | t }}: {{ discount_application.title | escape }}</td>
                  <td class="px-6 py-4 text-green-600 text-left">-{{ discount_application.total_allocated_amount | money }}</td>
                </tr>
              {%- endfor -%}
            {%- endif -%}
            {%- for shipping_method in order.shipping_methods -%}
              <tr>
                <td colspan="4" class="px-6 py-4 text-left">{{ 'customer.order.shipping' | t }} ({{ shipping_method.title | escape }})</td>
                <td class="px-6 py-4 text-left">{{ shipping_method.price | money }}</td>
              </tr>
            {%- endfor -%}
            {%- for tax_line in order.tax_lines -%}
              <tr>
                <td colspan="4" class="px-6 py-4 text-left">{{ 'customer.order.tax' | t }} ({{ tax_line.title | escape }} {{ tax_line.rate | times: 100 }}%)</td>
                <td class="px-6 py-4 text-left">{{ tax_line.price | money }}</td>
              </tr>
            {%- endfor -%}
            {%- if order.total_duties -%}
              <tr>
                <td colspan="4" class="px-6 py-4 text-left">{{ 'customer.order.total_duties' | t }}</td>
                <td class="px-6 py-4 text-left">{{ order.total_duties | money }}</td>
              </tr>
            {%- endif -%}
            {%- if order.total_refunded_amount > 0 -%}
              <tr>
                <td colspan="3" class="px-6 py-4 text-red-500 text-left">{{ 'customer.order.total_refunded' | t }}</td>
                <td colspan="2" class="px-6 py-4 text-red-500 text-left">-{{ order.total_refunded_amount | money_with_currency }}</td>
              </tr>
            {%- endif -%}
            <tr>
              <td colspan="3" class="px-6 py-4 font-bold text-left">{{ 'customer.order.total' | t }}</td>
              <td colspan="2" class="px-6 py-4 font-bold text-left">{{ order.total_net_amount | money_with_currency }}</td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
      <div>
        <h2 class="text-lg font-semibold mb-2 text-left">{{ 'customer.order.billing_address' | t }}</h2>
        <p class="mb-1 text-base md:text-lg text-left"><strong>{{ 'customer.order.payment_status' | t }}:</strong> {{ order.financial_status_label }}</p>
        <div class="text-gray-700 text-base md:text-lg text-left">{{ order.billing_address | format_address }}</div>
      </div>
      <div>
        <h2 class="text-lg font-semibold mb-2 text-left">{{ 'customer.order.shipping_address' | t }}</h2>
        <p class="mb-1 text-base md:text-lg text-left"><strong>{{ 'customer.order.fulfillment_status' | t }}:</strong> {{ order.fulfillment_status_label }}</p>
        <div class="text-gray-700 text-base md:text-lg text-left">{{ order.shipping_address | format_address }}</div>
      </div>
    </div>
  </div>
</div>
{% else %}
  <div class="bg-red-100 text-red-700 p-4 rounded max-w-2xl mx-auto my-8 mt-16">
    Order data is missing or invalid. Please check your backend or test data.
  </div>
{% endif %}

{% schema %}
{
  "name": "Customer Order",
  "settings": []
}
{% endschema %}
