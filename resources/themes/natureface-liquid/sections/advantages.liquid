<style>
    swiper-container {
      width: 100%;
      height: 100%;
    }
</style>

<section class="relative -mt-12" id="advantages">
    <div class="container mx-auto px-0 md:px-4">
        <swiper-container 
            slides-per-view="1.2" 
            space-between="5"
            breakpoints='{"1536":{"slidesPerView":4,"spaceBetween":24},
                        "1280":{"slidesPerView":3.5,"spaceBetween":24},
                        "768":{"slidesPerView":2.5,"spaceBetween":24}}'
            class="flex"
            style="--swiper-theme-color: var(--accent)"
        >
            {% for advantage in section.settings.advantages %}
                <swiper-slide class="flex items-center justify-center">
                    <div class="bg-[#FAE1E6D9] backdrop-blur-[2.5px] py-3 px-3 md:p-4 ml-4 md:ml-0 rounded-lg bg-gradient-to-t backdrop-blur flex items-center md:items-start gap-3 md:gap-0 min-h-[8.5rem] lg:min-h-[6.5rem]  md:min-h-[8rem] sm:min-h-[6rem] w-full">
                        <img src="{{ advantage.image }}" alt="Checked" class="h-12 w-12 mr-2 filter">
                        <div class="flex flex-col">
                            <p class="text-xl md:text-lg text-[var(--dark)] drop-shadow mb-1 lh-1">{{ advantage.title }}</p>
                            <p class="text-sm text-[var(--dark)] drop-shadow">{{ advantage.description }}</p>
                        </div>
                    </div>
                </swiper-slide>
            {% endfor %}
        </swiper-container>
    </div>
</section>

{% schema %}
{
  "name": "Advantages",
  "settings": [
    {
      "type": "list",
      "id": "advantages",
      "label": "Advantages",
      "default": [],
      "item": {
        "type": "object",
        "properties": [
          {
            "type": "image_picker",
            "id": "image",
            "label": "Image"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Title"
          },
          {
            "type": "textarea",
            "id": "description",
            "label": "Description"
          }
        ]
      }
    }
  ]
}
{% endschema %}