{{ 'breadcrumb-section.css' | asset_url | stylesheet_tag }}	

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}
<style>

nav.breadcrumb ul {
    display: flex !important;
    gap: 10px;
    padding-left: 0rem;
    margin-bottom: 0px;
}
  nav.breadcrumb {
    margin-bottom: 0px;
}
  nav.breadcrumb ul li {
     list-style: none;
    font-size: 15px;
    letter-spacing: 0px;
    color: #21332B;
    text-transform: capitalize;
    opacity: 1;
    font-family: 'Poppins';
}
  nav.breadcrumb ul li a {
    color: #21332B;
    text-decoration: none;
}
</style>
 <section id="breadcrumb-section">		
    <div class="section-{{ section.id }}-padding">
        <div class="page-width">
 {% if template != 'index' %}
  <nav class="breadcrumb" aria-label="breadcrumbs">
    <ul>
      <li><a href="{{ shop.url }}">Home</a></li>
      {% for link in linklists.main.links %}
        <li><a href="{{ link.url }}">{{ link.title }}</a></li>
      {% endfor %}
      <li><i class="fa-solid fa-angles-right"></i></li>
      <li class="is-active"><a href="#" aria-current="page">{{ page_title }}</a></li>
    </ul>
  </nav>
{% endif %} 

        </div>
    </div>
</section>



{% schema %}
{
  "name": "breadcrumb",		
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "breadcrumb"		
    }
  ]
}
{% endschema %}