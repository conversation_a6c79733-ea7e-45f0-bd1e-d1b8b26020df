{{ 'section-contact-form.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="_contacts container mx-auto px-4 color-{{ section.settings.color_scheme }} gradient" style="scroll-margin-top: var(--header-height, 64px); padding-top: var(--header-height, 64px);">
  <div class="contact max-w-2xl mx-auto py-8">
    {%- if section.settings.title != blank -%}
      <h1 class="inline-richtext {{ section.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %} md:mb-8 mb-4 md:text-3xl text-xl font-bold text-center">
        {{ section.settings.title }}
      </h1>
    {%- else -%}
      <h2 class="visually-hidden">{{ 'templates.contact.form.title' | t }}</h2>
    {%- endif -%}
    {%- liquid
      assign contact_form_class = 'isolate'
      if settings.animations_reveal_on_scroll
        assign contact_form_class = 'isolate scroll-trigger animate--slide-in'
      endif
    -%}
    {%- form 'contact', id: 'ContactForm', class: contact_form_class -%}
      {%- if form.posted_successfully? -%}
        <h2 class="form-status form-status-list form__message mb-4 text-green-600 flex items-center gap-2" tabindex="-1" autofocus>
          {% render 'icon-success' %}
          {{ 'templates.contact.form.post_success' | t }}
        </h2>
      {%- elsif form.errors -%}
        <div class="form__message">
          <h2 class="form-status caption-large text-body text-red-600 mb-2 flex items-center gap-2" role="alert" tabindex="-1" autofocus>
            {% render 'icon-error' %}
            {{ 'templates.contact.form.error_heading' | t }}
          </h2>
        </div>
        <ul class="form-status-list caption-large text-red-600 mb-4" role="list">
          <li>
            <a href="#ContactForm-email" class="underline">
              {{ form.errors.translated_fields.email | capitalize }}
              {{ form.errors.messages.email }}
            </a>
          </li>
        </ul>
      {%- endif -%}
      <div class="grid gap-4">
        <!-- Name Field -->
        <div class="relative">
          <input
            class="peer block w-full border border-gray-300 rounded px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[var(--accent)] placeholder-transparent"
            autocomplete="name"
            type="text"
            id="ContactForm-name"
            name="contact[{{ 'templates.contact.form.name' | t }}]"
            value="{% if form.name %}{{ form.name }}{% elsif customer %}{{ customer.name }}{% endif %}"
            placeholder="{{ 'templates.contact.form.name' | t }}"
          >
          <label for="ContactForm-name"
            class="absolute left-4 top-3 text-gray-500 text-base transition-all duration-200 pointer-events-none
              peer-placeholder-shown:top-3 peer-placeholder-shown:text-base
              peer-focus:-top-3 peer-focus:text-xs peer-focus:text-[var(--accent)]
              peer-not-placeholder-shown:-top-3 peer-not-placeholder-shown:text-xs bg-white px-1"
          >
            {{ 'templates.contact.form.name' | t }}
          </label>
        </div>
        <!-- Email Field -->
        <div class="relative">
          <input
            autocomplete="email"
            type="email"
            id="ContactForm-email"
            class="peer block w-full border border-gray-300 rounded px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[var(--accent)] placeholder-transparent"
            name="contact[email]"
            spellcheck="false"
            autocapitalize="off"
            value="{% if form.email %}{{ form.email }}{% elsif customer %}{{ customer.email }}{% endif %}"
            aria-required="true"
            {% if form.errors contains 'email' %}
              aria-invalid="true"
              aria-describedby="ContactForm-email-error"
            {% endif %}
            placeholder="{{ 'templates.contact.form.email' | t }}"
            required
          >
          <label for="ContactForm-email"
            class="absolute left-4 top-3 text-gray-500 text-base transition-all duration-200 pointer-events-none
              peer-placeholder-shown:top-3 peer-placeholder-shown:text-base
              peer-focus:-top-3 peer-focus:text-xs peer-focus:text-[var(--accent)]
              peer-not-placeholder-shown:-top-3 peer-not-placeholder-shown:text-xs bg-white px-1"
          >
            {{- 'templates.contact.form.email' | t }} <span aria-hidden="true">*</span>
          </label>
          {%- if form.errors contains 'email' -%}
            <small class="block text-red-600 mt-1" id="ContactForm-email-error">
              <span class="visually-hidden">{{ 'accessibility.error' | t }}</span>
              <span class="form__message flex items-center gap-1">
                {%- render 'icon-error' -%}
                {{- form.errors.translated_fields.email | capitalize }}
                {{ form.errors.messages.email -}}
              </span>
            </small>
          {%- endif -%}
        </div>
        <!-- Phone Field -->
        <div class="relative">
          <input
            type="tel"
            id="ContactForm-phone"
            class="peer block w-full border border-gray-300 rounded px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[var(--accent)] placeholder-transparent"
            autocomplete="tel"
            name="contact[{{ 'templates.contact.form.phone' | t }}]"
            pattern="^\+?[0-9\-]*"
            value="{% if form.phone %}{{ form.phone }}{% elsif customer %}{{ customer.phone }}{% endif %}"
            placeholder="{{ 'templates.contact.form.phone' | t }}"
          >
          <label for="ContactForm-phone"
            class="absolute left-4 top-3 text-gray-500 text-base transition-all duration-200 pointer-events-none
              peer-placeholder-shown:top-3 peer-placeholder-shown:text-base
              peer-focus:-top-3 peer-focus:text-xs peer-focus:text-[var(--accent)]
              peer-not-placeholder-shown:-top-3 peer-not-placeholder-shown:text-xs bg-white px-1"
          >
            {{ 'templates.contact.form.phone' | t }}
          </label>
        </div>
        <!-- Comment Field -->
        <div class="relative">
          <textarea
            rows="10"
            id="ContactForm-body"
            class="peer block w-full border border-gray-300 rounded px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[var(--accent)] placeholder-transparent resize-none"
            name="contact[{{ 'templates.contact.form.comment' | t }}]"
            placeholder="{{ 'templates.contact.form.comment' | t }}"
          >{{- form.body -}}</textarea>
          <label for="ContactForm-body"
            class="absolute left-4 top-3 text-gray-500 text-base transition-all duration-200 pointer-events-none
              peer-placeholder-shown:top-3 peer-placeholder-shown:text-base
              peer-focus:-top-3 peer-focus:text-xs peer-focus:text-[var(--accent)]
              peer-not-placeholder-shown:-top-3 peer-not-placeholder-shown:text-xs bg-white px-1"
          >
            {{- 'templates.contact.form.comment' | t -}}
          </label>
        </div>
        <!-- Checkbox Fields -->
        <div class="flex flex-col gap-2 my-2">
          <label class="flex items-baseline gap-2">
            <input type="checkbox" name="contact[terms_accepted]" required class="accent-[var(--accent)]">
            <span>{{ section.settings.terms_label | default: "Приемам ОБЩИ УСЛОВИЯ" }}</span>
          </label>
          <label class="flex items-baseline gap-2">
            <input type="checkbox" name="contact[privacy_policy]" required class="accent-[var(--accent)]">
            <span>{{ section.settings.privacy_label | default: "Приемам Политиката за поверителност" }}</span>
          </label>
          <label class="flex items-baseline gap-2">
            <input type="checkbox" name="contact[data_processing_consent]" required class="accent-[var(--accent)]">
            <span>{{ section.settings.data_processing_label | default: "Приемам Декларация за даване на съгласие за обработване на лични данни" }}</span>
          </label>
        </div>
        <!-- Submit Button -->
        <div class="text-end">
          <button type="submit" class="button bg-[var(--accent)] hover:bg-[var(--accent)]/90 text-white font-semibold px-6 py-2 rounded transition cursor-pointer">
            {{ 'templates.contact.form.send' | t }}
          </button>
        </div>
      </div>
    {%- endform -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.contact-form.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "heading",
      "default": "Contact form",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "text",
      "id": "terms_label",
      "label": "Label: Terms and Conditions",
      "default": "Приемам ОБЩИ УСЛОВИЯ"
    },
    {
      "type": "text",
      "id": "privacy_label",
      "label": "Label: Privacy Policy",
      "default": "Приемам Политиката за поверителност"
    },
    {
      "type": "text",
      "id": "data_processing_label",
      "label": "Label: Data Processing Consent",
      "default": "Приемам Декларация за даване на съгласие за обработване на лични данни"
    }
  ],
  "presets": [
    {
      "name": "t:sections.contact-form.presets.name"
    }
  ]
}
{% endschema %}
