{"settings_schema": {"colors": {"name": "<PERSON><PERSON>", "settings": {"background": {"label": "Hi<PERSON>grund"}, "background_gradient": {"label": "Hintergrundfarbverlauf", "info": "<PERSON><PERSON> m<PERSON><PERSON>, ersetzt der Hintergrundfarbverlauf den Hintergrund."}, "text": {"label": "Text"}, "button_background": {"label": "Hintergrund für durchgehende Schaltfläche"}, "button_label": {"label": "Beschriftung für durchgehende Schaltfläche"}, "secondary_button_label": {"label": "Umriss-Schaltfläche"}, "shadow": {"label": "<PERSON><PERSON><PERSON>"}}}, "typography": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"type_header_font": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Die Auswahl einer anderen Schriftart kann sich auf die Geschwindigkeit deines Shops auswirken. [Weitere Informationen zu Systemschriftarten.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "header__1": {"content": "Überschriften"}, "header__2": {"content": "Nachricht"}, "type_body_font": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Die Auswahl einer anderen Schriftart kann sich auf die Geschwindigkeit deines Shops auswirken. [Weitere Informationen zu Systemschriftarten.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "heading_scale": {"label": "Schriftgrößenmaßstab"}, "body_scale": {"label": "Schriftgrößenmaßstab"}}}, "social-media": {"name": "Social Media", "settings": {"social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://vimeo.com/shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "http://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Social-Media-Konten"}}}, "currency_format": {"name": "Währungsformat", "settings": {"content": "Währungscodes", "currency_code_enabled": {"label": "Währungscodes anzeigen"}, "paragraph": "Warenkorb- und Checkout-Preise zeigen immer Währungscodes an. Beispiel: 1,00 USD."}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "Seitenbreite"}, "spacing_sections": {"label": "Platz zwischen Vorlagenabschnitten"}, "header__grid": {"content": "<PERSON><PERSON>"}, "paragraph__grid": {"content": "Wirkt sich auf Bereiche mit mehreren Spalten oder Reihen aus."}, "spacing_grid_horizontal": {"label": "Horizontaler Abstand"}, "spacing_grid_vertical": {"label": "<PERSON><PERSON><PERSON><PERSON> Abstand"}}}, "search_input": {"name": "Suchverhalten", "settings": {"header": {"content": "Suchvorschläge"}, "predictive_search_enabled": {"label": "Suchvorschläge aktivieren"}, "predictive_search_show_vendor": {"label": "Produktanbieter anzeigen", "info": "<PERSON><PERSON><PERSON>, wenn Suchvorschläge aktiviert sind."}, "predictive_search_show_price": {"label": "Produktpreis anzeigen", "info": "<PERSON><PERSON><PERSON>, wenn Suchvorschläge aktiviert sind."}}}, "global": {"settings": {"header__border": {"content": "Rand"}, "header__shadow": {"content": "<PERSON><PERSON><PERSON>"}, "blur": {"label": "Weichzeichnen"}, "corner_radius": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "horizontal_offset": {"label": "Horizontaler Offset"}, "vertical_offset": {"label": "<PERSON><PERSON><PERSON><PERSON> Offset"}, "thickness": {"label": "<PERSON><PERSON>"}, "opacity": {"label": "Opazität"}, "image_padding": {"label": "Bild-Padding"}, "text_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Textausrichtung"}}}, "badges": {"name": "Badges", "settings": {"position": {"options__1": {"label": "Unten links"}, "options__2": {"label": "Unten rechts"}, "options__3": {"label": "Oben links"}, "options__4": {"label": "<PERSON><PERSON> rechts"}, "label": "Position auf Karten"}, "sale_badge_color_scheme": {"label": "Farbschema für Sale-Badges"}, "sold_out_badge_color_scheme": {"label": "Farbschema für Ausverkauft-Badges"}}}, "buttons": {"name": "Schaltflächen"}, "variant_pills": {"name": "Varianten-Kapseln", "paragraph": "Varianten-Kapseln sind eine Möglichkeit, deine Produktvarianten zu präsentieren. [Mehr Informationen](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"}, "inputs": {"name": "Eingaben"}, "content_containers": {"name": "Inhalts-Container"}, "popups": {"name": "Dropdown-Listen und Pop-ups", "paragraph": "Wirkt sich auf Bereiche wie das Dropdown-Menü für die Navigation, modale Pop-ups und Warenkorb-Pop-ups aus."}, "media": {"name": "Medien"}, "drawers": {"name": "Einschübe"}, "cart": {"name": "<PERSON><PERSON><PERSON>", "settings": {"cart_type": {"label": "Warenkorbstil", "drawer": {"label": "<PERSON><PERSON><PERSON>"}, "page": {"label": "Seite"}, "notification": {"label": "Pop-up-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "show_cart_note": {"label": "Warenkorbanmerkung aktivieren"}, "cart_drawer": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collection": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON>, wenn der Warenkorbeinschub leer ist."}}}}, "cards": {"name": "Produktkarten", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Optik"}}}, "collection_cards": {"name": "Kategoriekarten", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Optik"}}}, "blog_cards": {"name": "Blog-Karten", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Optik"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_width": {"label": "Desktop-Logobreite", "info": "Die Logobreite wird automatisch für Mobilgeräte optimiert."}, "favicon": {"label": "Favicon-Bild", "info": "Wird auf 32 x 32 Pixel verkleinert"}}}, "brand_information": {"name": "Markeninformationen", "settings": {"brand_headline": {"label": "Überschrift"}, "brand_description": {"label": "Beschreibung"}, "brand_image": {"label": "Bild"}, "brand_image_width": {"label": "Bildbreite"}, "paragraph": {"content": "Füge der Fußzeile deines Shops ein Beschreibung deiner Marke hinzu."}}}, "animations": {"name": "<PERSON><PERSON>", "settings": {"animations_reveal_on_scroll": {"label": "<PERSON><PERSON> Abschnitte e<PERSON>nden"}, "animations_hover_elements": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Vertikal-Lift"}, "label": "Hover-Effekt", "info": "Wirkt sich auf Karten und Schaltflächen aus.", "options__3": {"label": "3D-Lift"}}}}}, "sections": {"all": {"padding": {"section_padding_heading": "Abschnitts-Padding", "padding_top": "<PERSON><PERSON><PERSON>", "padding_bottom": "<PERSON><PERSON><PERSON> Padding"}, "spacing": "Abstand", "colors": {"label": "Farbschema", "has_cards_info": "Aktualisiere deine Theme-Einstellungen, um das Farbschema der Karte zu ändern."}, "heading_size": {"label": "Größe der Überschrift", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Extra groß"}}, "image_shape": {"options__1": {"label": "Standard"}, "options__2": {"label": "Bogen"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Chevron nach links"}, "options__5": {"label": "Chevron nach rechts"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "Parallelogramm"}, "options__8": {"label": "Rund"}, "label": "Bildform", "info": "Karten mit Standarddesign haben keinen <PERSON>, wenn eine Bildform aktiv ist."}, "animation": {"content": "<PERSON><PERSON>", "image_behavior": {"options__1": {"label": "<PERSON><PERSON>(r)"}, "options__2": {"label": "Atmosphärische Bewegung"}, "label": "Bildverhalten", "options__3": {"label": "Feste Hintergrundposition"}, "options__4": {"label": "<PERSON><PERSON> her<PERSON>zo<PERSON>n"}}}}, "announcement-bar": {"name": "Ankündigungsleiste", "blocks": {"announcement": {"name": "Ankündigung", "settings": {"text": {"label": "Text"}, "link": {"label": "Link"}, "text_alignment": {"label": "Textausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "settings": {"auto_rotate": {"label": "Autorotieren der Ankündigungen"}, "change_slides_speed": {"label": "Ändern alle"}, "header__1": {"content": "Social-Media-Symbole", "info": "Um deine Social-Media-Konten anzuzeigen, verlinke sie in deinen [Theme-Einstellungen](/editor?context=theme&category=social%20media)."}, "header__2": {"content": "Ankündigungen"}, "show_social": {"label": "Symbole auf dem Desktop anzeigen"}, "header__3": {"content": "Auswahl für Land/Region", "info": "Gehe zu den [Markteinstellungen](/admin/settings/markets), um ein Land / eine Region hinzuzufügen."}, "enable_country_selector": {"label": "Auswahl für Land/Region aktivieren"}, "header__4": {"content": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "Gehe zu den [Spracheinstellungen](/admin/settings/languages), um eine Sprache hinzuzufügen."}, "enable_language_selector": {"label": "Sprachauswahl aktivieren"}}, "presets": {"name": "Ankündigungsleiste"}}, "collage": {"name": "Collage", "settings": {"heading": {"label": "Überschrift"}, "desktop_layout": {"label": "Desktop-Layout", "options__1": {"label": "Großer Block links"}, "options__2": {"label": "Großer Block rechts"}}, "mobile_layout": {"label": "Mobiles Layout", "options__1": {"label": "Collage"}, "options__2": {"label": "<PERSON>lt<PERSON>"}}, "card_styles": {"label": "Kartendesign", "info": "Das Design von Produkten, Kategorien und Blogkarten kann in den Theme-Einstellungen aktualisiert werden.", "options__1": {"label": "Individuelle Kartendesigns verwenden"}, "options__2": {"label": "Alle als Produktkarten gestalten"}}}, "blocks": {"image": {"name": "Bild", "settings": {"image": {"label": "Bild"}}}, "product": {"name": "Produkt", "settings": {"product": {"label": "Produkt"}, "secondary_background": {"label": "Sekundären Hintergrund anzeigen"}, "second_image": {"label": "Hover-Effekt mit zweitem Bild"}}}, "collection": {"name": "<PERSON><PERSON><PERSON>", "settings": {"collection": {"label": "<PERSON><PERSON><PERSON>"}}}, "video": {"name": "Video", "settings": {"cover_image": {"label": "Titelbild"}, "video_url": {"label": "URL", "info": "Video wird in einem Pop-up abgespielt, wenn der Abschnitt andere Blöcke enthält.", "placeholder": "YouTube- oder Vimeo-URL verwenden"}, "description": {"label": "Video-Alt-Text", "info": "Beschreibe das Video für Kunden, die Bildschirmlesegeräte benutzen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"}}}}, "presets": {"name": "Collage"}}, "collection-list": {"name": "Kategorieliste", "settings": {"title": {"label": "Überschrift"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}, "info": "Bearbeite deine Kategorien, um Bilder hinzuzufügen. [Mehr Informationen](https://help.shopify.com/manual/products/collections)"}, "swipe_on_mobile": {"label": "Wischen auf Mobilgeräten aktivieren"}, "show_view_all": {"label": "Aktiviere die Schaltfläche \"Alle anzeigen\", wenn die Liste mehr Kategorien enthält, als angezeigt werden"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}}, "blocks": {"featured_collection": {"name": "<PERSON><PERSON><PERSON>", "settings": {"collection": {"label": "<PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "Kategorieliste"}}, "contact-form": {"name": "Kontaktformular", "presets": {"name": "Kontaktformular"}}, "custom-liquid": {"name": "Benutzerdefiniertes Liquid", "settings": {"custom_liquid": {"label": "Liquid-Code", "info": "Füge App-Snippets oder anderen Code hinzu, um fortgeschrittene Anpassungen zu erstellen. [Mehr Informationen](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Benutzerdefiniertes Liquid"}}, "featured-blog": {"name": "Blog-Beiträge", "settings": {"heading": {"label": "Überschrift"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Anzahl der anzuzeigenden Blog-Beiträge"}, "show_view_all": {"label": "Aktiviere die Schaltfläche \"Alle anzeigen\", wenn der Blog mehr Blog-Beiträge enthält, als angezeigt werden"}, "show_image": {"label": "Feature-<PERSON><PERSON><PERSON> anzeigen", "info": "Verwende für Bilder ein Seitenverhältnis von 3:2 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "show_date": {"label": "<PERSON><PERSON> anzeigen"}, "show_author": {"label": "<PERSON><PERSON> <PERSON>"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}}, "presets": {"name": "Blog-Beiträge"}}, "featured-collection": {"name": "Vorgestellte Kategorie", "settings": {"title": {"label": "Überschrift"}, "collection": {"label": "<PERSON><PERSON><PERSON>"}, "products_to_show": {"label": "Maximal anzuzeigende Produkte"}, "show_view_all": {"label": "\"Alles anzeigen\" aktivier<PERSON>, wenn die Kategorie mehr Produkte enthält, als angezeigt werden"}, "header": {"content": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, musst du eine Produktbewertungs-App hinzufügen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "enable_quick_buy": {"label": "Schaltfläche zum schnellen Hinzufügen aktivieren", "info": "Optimal bei Warenkörben des Typs Pop-up oder Einschub."}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "description": {"label": "Beschreibung"}, "show_description": {"label": "Kategoriebeschreibung im Adminbereich anzeigen"}, "description_style": {"label": "Beschreibungsstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}, "view_all_style": {"options__1": {"label": "Link"}, "options__2": {"label": "Umriss-Schaltfläche"}, "options__3": {"label": "Durchgehende Schaltfläche"}, "label": "Stil \"Alles anzeigen\""}, "enable_desktop_slider": {"label": "Ka<PERSON>ell auf Desktop aktivieren"}, "full_width": {"label": "Produkte auf gesamte Breite auslegen"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "swipe_on_mobile": {"label": "Wischen auf Mobilgeräten aktivieren"}}, "presets": {"name": "Vorgestellte Kategorie"}}, "footer": {"name": "Fußzeile", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Überschrift"}, "menu": {"label": "<PERSON><PERSON>", "info": "<PERSON>eigt nur Top-Level-Menüpunkte an."}}}, "text": {"name": "Text", "settings": {"heading": {"label": "Überschrift"}, "subtext": {"label": "Subtext"}}}, "brand_information": {"name": "Markeninformationen", "settings": {"paragraph": {"content": "In diesem Block werden deine Markeninformationen angezeigt. [Bearbeite deine Markeninformationen.](/editor?context=theme&category=brand%20information)"}, "header__1": {"content": "Social-Media-Symbole"}, "show_social": {"label": "Social-Media-Symbole anzeigen", "info": "Um deine Social-Media-Konten anzuzeigen, verlinke sie in deinen [Theme-Einstellungen](/editor?context=theme&category=social%20media)."}}}}, "settings": {"newsletter_enable": {"label": "E-Mail-Anmeldung anzeigen"}, "newsletter_heading": {"label": "Überschrift"}, "header__1": {"content": "E-Mail-Anmeldung", "info": "<PERSON><PERSON><PERSON><PERSON>, die automatisch zu deiner Kundenliste \"Akzeptiert Marketing\" hinzugefügt wurden. [Mehr Informationen](https://help.shopify.com/manual/customers/manage-customers)"}, "header__2": {"content": "Social Media-Symbole", "info": "Um deine Social-Media-Konten anzuzeigen, verlinke sie in deinen [Theme-Einstellungen](/editor?context=theme&category=social%20media)."}, "show_social": {"label": "Social-Media-Symbole anzeigen"}, "header__3": {"content": "Auswahl für Land/Region"}, "header__4": {"info": "Gehe zu den [Markteinstellungen](/admin/settings/markets), um ein Land / eine Region hinzuzufügen."}, "enable_country_selector": {"label": "Auswahl für Land/Region aktivieren"}, "header__5": {"content": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "header__6": {"info": "Gehe zu den [Spracheinstellungen](/admin/settings/languages), um eine Sprache hinzuzufügen."}, "enable_language_selector": {"label": "Sprachauswahl aktivieren"}, "header__7": {"content": "Zahlungsmethoden"}, "payment_enable": {"label": "Zahlungssymbole anzeigen"}, "margin_top": {"label": "<PERSON>berer Rand"}, "header__8": {"content": "<PERSON><PERSON> <PERSON> Richtlinien", "info": "Um Richtlinien für den Shop hinzuzufügen, gehe zu deinen [Richtlinien-Einstellungen](/admin/settings/legal)."}, "show_policy": {"label": "<PERSON><PERSON> zu Richtlinien anzeigen"}, "header__9": {"content": "In Shop folgen", "info": "Damit Kunden deinem Shop in der Shop-App über deine Storefront folgen können, muss Shop Pay aktiviert sein. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "\"In Shop folgen\" aktivieren"}}}, "header": {"name": "Header", "settings": {"logo_position": {"label": "Desktop-Logoposition", "options__1": {"label": "Mitte links"}, "options__2": {"label": "Oben links"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON>"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Trennlinie anzeigen"}, "margin_bottom": {"label": "Un<PERSON>er Rand"}, "menu_type_desktop": {"label": "Desktop-Menütyp", "info": "Der Menütyp wird automatisch für die mobile Nutzung optimiert.", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Mega-Menü"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "mobile_layout": {"content": "Mobiles Layout"}, "mobile_logo_position": {"label": "Logo-Position für mobile Darstellung", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Links"}}, "logo_help": {"content": "Bearbeite dein Logo in den [theme settings](/editor?context=theme&category=logo)."}, "sticky_header_type": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Immer"}, "options__4": {"label": "Immer, Größe des Logos reduzieren"}}, "header__3": {"content": "Auswahl für Land/Region"}, "header__4": {"info": "Gehe zu den [Markteinstellungen](/admin/settings/markets), um ein Land / eine Region hinzuzufügen."}, "enable_country_selector": {"label": "Auswahl für Land/Region aktivieren"}, "header__5": {"content": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "header__6": {"info": "Gehe zu den [Spracheinstellungen](/admin/settings/languages), um eine Sprache hinzuzufügen."}, "enable_language_selector": {"label": "Sprachauswahl aktivieren"}, "header__1": {"content": "Farbe"}, "menu_color_scheme": {"label": "Menü-Farbschema"}}}, "image-banner": {"name": "Bild-Banner", "settings": {"image": {"label": "<PERSON><PERSON><PERSON> Bild"}, "image_2": {"label": "Zweites Bild"}, "stack_images_on_mobile": {"label": "Gestapelte Bilder auf Mobilgeräten"}, "show_text_box": {"label": "Container auf dem Desktop anzeigen"}, "image_overlay_opacity": {"label": "Deckkraft der Bildüberlagerung"}, "show_text_below": {"label": "Container auf Mobilgerät anzeigen"}, "image_height": {"label": "Bannerhöhe", "options__1": {"label": "An erstes Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Verwende für Bilder ein Seitenverhältnis von 3:2 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "desktop_content_position": {"options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON> rechts"}, "options__4": {"label": "Mitte links"}, "options__5": {"label": "<PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON> rechts"}, "options__7": {"label": "Unten links"}, "options__8": {"label": "<PERSON>ten zent<PERSON>t"}, "options__9": {"label": "Unten rechts"}, "label": "Desktopinhaltsposition"}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktopinhaltsausrichtung"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Mobile Inhaltsausrichtung"}, "mobile": {"content": "Mobiles Layout"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Beschreibung"}, "text_style": {"options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}, "label": "Textstil"}}}, "buttons": {"name": "Schaltflächen", "settings": {"button_label_1": {"label": "Erste Beschriftung der Schaltfläche", "info": "Lasse die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link_1": {"label": "Erster Link der Schaltfläche"}, "button_style_secondary_1": {"label": "Umriss-Stil für Schaltfläche verwenden"}, "button_label_2": {"label": "Zweite Beschriftung der Schaltfläche", "info": "Lasse die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link_2": {"label": "Zweiter Link der Schaltfläche"}, "button_style_secondary_2": {"label": "Umriss-Stil für Schaltfläche verwenden"}}}}, "presets": {"name": "Bild-Banner"}}, "image-with-text": {"name": "Bild mit Text", "settings": {"image": {"label": "Bild"}, "height": {"options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Bildhöhe", "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "layout": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Zweites Bild"}, "label": "Desktop-Bildplatzierung", "info": "Das Standardlayout für Mobilgeräte ist \"Bild zuerst\"."}, "desktop_image_width": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktop-Bildbreite", "info": "Bild wird automatisch für die mobile Nutzung optimiert."}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktop-Inhaltsausrichtung"}, "desktop_content_position": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Unten"}, "label": "Desktop-Inhaltsposition"}, "content_layout": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Überlappung"}, "label": "Layout des Inhalts"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Mobile Inhaltsausrichtung"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Inhalt"}, "text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}}}}, "button": {"name": "Schaltfläche", "settings": {"button_label": {"label": "Schaltflächenbeschriftung", "info": "Lasse die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link": {"label": "Schaltflächenlink"}, "outline_button": {"label": "Umriss-Stil für Schaltfläche verwenden"}}}, "caption": {"name": "Bildtext", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Untertitel"}, "options__2": {"label": "Großbuchstaben"}}, "caption_size": {"label": "Textgröße", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Bild mit Text"}}, "main-article": {"name": "Blog-Beitrag", "blocks": {"featured_image": {"name": "Feature-Bild", "settings": {"image_height": {"label": "<PERSON><PERSON><PERSON> des Feature-Bildes", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Verwende für Bilder ein Seitenverhältnis von 16:9 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "Titel", "settings": {"blog_show_date": {"label": "<PERSON><PERSON> anzeigen"}, "blog_show_author": {"label": "<PERSON><PERSON> <PERSON>"}}}, "content": {"name": "Inhalt"}, "share": {"name": "Teilen", "settings": {"featured_image_info": {"content": "<PERSON><PERSON> <PERSON> e<PERSON> Link in Social-Media-Posts einfügst, wird das Feature-Bild der Seite als Vorschaubild angezeigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Ein Titel und eine Beschreibung des Shops sind im Vorschaubild enthalten. [Mehr Informationen](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Text"}}}}}, "main-blog": {"name": "Blog-Beiträge", "settings": {"header": {"content": "Blog-Beitrags-Karte"}, "show_image": {"label": "Feature-<PERSON><PERSON><PERSON> anzeigen"}, "paragraph": {"content": "Bearbeite deine Blog-Beiträge, um Auszüge zu ändern. [Mehr Informationen](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "show_date": {"label": "<PERSON><PERSON> anzeigen"}, "show_author": {"label": "<PERSON><PERSON> <PERSON>"}, "layout": {"label": "Desktop-Layout", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Collage"}, "info": "Beiträge werden bei der mobilen Nutzung gestapelt."}, "image_height": {"label": "<PERSON><PERSON><PERSON> des Feature-Bildes", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Verwende für Bilder ein Seitenverhältnis von 3:2 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-cart-footer": {"name": "Zwischensumme", "blocks": {"subtotal": {"name": "Zwischensumme"}, "buttons": {"name": "Checkout-Schaltfläche"}}}, "main-cart-items": {"name": "Artikel"}, "main-collection-banner": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"paragraph": {"content": "Bearbeite deine Kategorien, um eine Beschreibung oder ein Bild hinzuzufügen. [Mehr Informationen](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Kategoriebeschreibung anzeigen"}, "show_collection_image": {"label": "Kategoriebild anzeigen", "info": "Verwende für Bilder ein Seitenverhältnis von 16:9 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"products_per_page": {"label": "Produkte pro Seite"}, "enable_filtering": {"label": "Filtern aktivieren", "info": "Passe Filter mit der Search & Discovery-App an. [Mehr Informationen](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_sorting": {"label": "Sortieren aktivieren"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "header__1": {"content": "Filtern und Sortieren"}, "header__3": {"content": "Produktkarte"}, "enable_tags": {"label": "Filtern aktivieren", "info": "Passe Filter mit der Search & Discovery-App an. [Mehr Informationen](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, musst du eine Produktbewertungs-App hinzufügen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"}, "enable_quick_buy": {"label": "Schaltfläche zum schnellen Hinzufügen aktivieren", "info": "Optimal bei Warenkörben des Typs Pop-up oder Einschub."}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "filter_type": {"label": "Desktopfilterlayout", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertikal"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Das Standardlayout für Mobilgeräte ist \"Einschub\"."}}}, "main-list-collections": {"name": "Listenseite für Kategorien", "settings": {"title": {"label": "Überschrift"}, "sort": {"label": "<PERSON><PERSON><PERSON> sortieren nach:", "options__1": {"label": "Alphabetisch, A-Z"}, "options__2": {"label": "Alphabetisch, Z-A"}, "options__3": {"label": "<PERSON><PERSON>, neu zu alt"}, "options__4": {"label": "<PERSON><PERSON>, alt zu neu"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hoch zu niedrig"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ni<PERSON><PERSON> zu hoch"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}, "info": "Bearbeite deine Kategorien, um Bilder hinzuzufügen. [Mehr Informationen](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}}}, "main-page": {"name": "Seite"}, "main-password-footer": {"name": "Passwort-Fußzeile"}, "main-password-header": {"name": "Passwort-Header", "settings": {"logo_header": {"content": "Logo"}, "logo_help": {"content": "Bearbeite dein Logo in den Theme-Einstellungen."}}}, "main-product": {"blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "title": {"name": "Titel"}, "price": {"name": "Pre<PERSON>"}, "quantity_selector": {"name": "Men<PERSON>aus<PERSON><PERSON>"}, "variant_picker": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"picker_type": {"label": "Optik", "options__1": {"label": "Dropdown"}, "options__2": {"label": "<PERSON><PERSON>ln"}}, "swatch_shape": {"label": "<PERSON><PERSON><PERSON>", "info": "Aktiviere [<PERSON><PERSON><PERSON><PERSON>](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) für Produktoptionen.", "options__1": {"label": "Kreis"}, "options__2": {"label": "Quadrat"}, "options__3": {"label": "<PERSON><PERSON>"}}}}, "buy_buttons": {"name": "Buy Buttons", "settings": {"show_dynamic_checkout": {"label": "Dynamischer Checkout-<PERSON><PERSON> anzeigen", "info": "Wenn sie die Zahlungsmethoden verwenden, die in deinem Shop verfügbar sind, sehen <PERSON> ihre bevorzugte Option, z. B. PayPal oder Apple Pay. [Mehr Informationen](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Formular für Empfängerinformationen für Geschenkgutscheine anzeigen", "info": "So können Käufer Geschenkgutscheine an einem bestimmten Datum mit einer persönlichen Nachricht versenden. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "pickup_availability": {"name": "Verfügbarkeit von Abholungen"}, "description": {"name": "Beschreibung"}, "share": {"name": "Teilen", "settings": {"featured_image_info": {"content": "<PERSON><PERSON> <PERSON> e<PERSON> Link in Social-Media-Posts einfügst, wird das Feature-Bild der Seite als Vorschaubild angezeigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Ein Titel und eine Beschreibung des Shops sind im Vorschaubild enthalten. [Mehr Informationen](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "Text"}}}, "collapsible_tab": {"name": "Einklappbare Reihe", "settings": {"heading": {"info": "Füge eine Überschrift ein, die den Inhalt erklärt.", "label": "Überschrift"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "Reiheninhalt der Seite"}, "icon": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Flasche"}, "options__5": {"label": "Box"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__7": {"label": "Chat-Blase"}, "options__8": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__9": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON>"}, "options__11": {"label": "Laktosef<PERSON>i"}, "options__12": {"label": "<PERSON><PERSON><PERSON>"}, "options__13": {"label": "<PERSON><PERSON>"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Glutenfrei"}, "options__16": {"label": "<PERSON><PERSON>"}, "options__17": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__18": {"label": "<PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Blitz"}, "options__21": {"label": "Lippenstift"}, "options__22": {"label": "<PERSON><PERSON><PERSON>"}, "options__23": {"label": "Pinnnadel"}, "options__24": {"label": "<PERSON><PERSON>"}, "label": "Symbol", "options__25": {"label": "<PERSON><PERSON>"}, "options__26": {"label": "Pfotenabdruck"}, "options__27": {"label": "<PERSON><PERSON><PERSON>"}, "options__28": {"label": "Parfüm"}, "options__29": {"label": "Flugzeug"}, "options__30": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__31": {"label": "<PERSON><PERSON><PERSON>"}, "options__32": {"label": "Fragezeichen"}, "options__33": {"label": "Recyclen"}, "options__34": {"label": "Rückgabe"}, "options__35": {"label": "Lineal"}, "options__36": {"label": "Servierteller"}, "options__37": {"label": "<PERSON><PERSON><PERSON>"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__41": {"label": "Stern"}, "options__42": {"label": "Stoppuhr"}, "options__43": {"label": "Lieferwagen"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}, "popup": {"name": "Pop-up", "settings": {"link_label": {"label": "Link-Label"}, "page": {"label": "Seite"}}}, "rating": {"name": "Produktbewertung", "settings": {"paragraph": {"content": "Um eine Bewertung anzuzeigen, musst du eine Produktbewertungs-App hinzufügen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"}}}, "complementary_products": {"name": "Ergänzende Produkte", "settings": {"paragraph": {"content": "Um ergänzende Produkte auszuwählen, füge die Search & Discovery-App hinzu. [Mehr Informationen](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "Überschrift"}, "make_collapsible_row": {"label": "Als einklappbare Reihe anzeigen"}, "icon": {"info": "<PERSON><PERSON><PERSON>, wenn einklappbare Reihe angezeigt wird."}, "product_list_limit": {"label": "Maximal anzuzeigende Produkte"}, "products_per_page": {"label": "Anzahl der Produkte pro Seite"}, "pagination_style": {"label": "Seitennummerierungsstil", "options": {"option_1": "Punkte", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "<PERSON><PERSON><PERSON>"}}, "product_card": {"heading": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options": {"option_1": "Hochformat", "option_2": "<PERSON>uadratisch"}}, "enable_quick_add": {"label": "Schaltfläche zum schnellen Hinzufügen aktivieren"}}}, "icon_with_text": {"name": "Symbol mit Text", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertikal"}}, "content": {"label": "Inhalt", "info": "W<PERSON>hle ein Symbol oder füge ein Bild für jede Spalte oder Zeile hinzu."}, "heading": {"info": "Lasse die Überschrift leer, um die Symbolspalte auszublenden."}, "icon_1": {"label": "Erstes Symbol"}, "image_1": {"label": "<PERSON><PERSON><PERSON> Bild"}, "heading_1": {"label": "Erste Überschrift"}, "icon_2": {"label": "Zweites Symbol"}, "image_2": {"label": "Zweites Bild"}, "heading_2": {"label": "Zweite Überschrift"}, "icon_3": {"label": "Drittes Sym<PERSON>"}, "image_3": {"label": "<PERSON><PERSON><PERSON>"}, "heading_3": {"label": "Dritte Überschrift"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "inventory": {"name": "Inventarstatus", "settings": {"text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}, "inventory_threshold": {"label": "Geringer Inventarschwellenwert", "info": "Wähle 0 aus, um immer den Lagerbestand anzuzeigen, falls verfügbar."}, "show_inventory_quantity": {"label": "Inventar anzeigen"}}}}, "settings": {"header": {"content": "Medien", "info": "Mehr Informationen über [Medienarten](https://help.shopify.com/manual/products/product-media)."}, "enable_video_looping": {"label": "Videoschleife aktivieren"}, "enable_sticky_info": {"label": "Fixierte Inhalte für Desktop aktivieren"}, "hide_variants": {"label": "Medien anderer <PERSON> aus<PERSON>, sobald eine Variante ausgewählt wurde"}, "gallery_layout": {"label": "Desktop-Layout", "options__1": {"label": "Gestapelt"}, "options__2": {"label": "2 Spalten"}, "options__3": {"label": "Vorschaubilder"}, "options__4": {"label": "Karussell mit Vorschaubildern"}}, "media_size": {"label": "Medienbreite für Desktop", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Medien werden automatisch für die mobile Nutzung optimiert."}, "mobile_thumbnails": {"label": "Mobiles Layout", "options__1": {"label": "2 Spalten"}, "options__2": {"label": "Vorschaubilder anzeigen"}, "options__3": {"label": "Vorschaubilder ausblenden"}}, "media_position": {"label": "Desktop-Medienposition", "info": "Positionen werden automatisch für die mobile Nutzung optimiert.", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_zoom": {"label": "Bildzoom", "info": "<PERSON>licke auf und fahre mit der Maus über \"Standards\", um Lightbox auf deinem Mobilgerät zu öffnen.", "options__1": {"label": "Lightbox öffnen"}, "options__2": {"label": "<PERSON><PERSON><PERSON> und mit der Maus darüber fahren"}, "options__3": {"label": "Nicht zoomen"}}, "constrain_to_viewport": {"label": "Medien auf Bildschirmhöhe beschränken"}, "media_fit": {"label": "Medienanpassung", "options__1": {"label": "Original"}, "options__2": {"label": "Füllung"}}}, "name": "Produktinformationen"}, "main-search": {"name": "Suchergebnisse", "settings": {"image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "header__1": {"content": "Produktkarte"}, "header__2": {"content": "Blog-Karte", "info": "Die Blog-Karten-Optik gilt auch für Seiten-Karten in den Suchergebnissen. Aktualisiere deine Theme-Einstellungen, um die Karten-Optik zu ändern."}, "article_show_date": {"label": "<PERSON><PERSON> anzeigen"}, "article_show_author": {"label": "<PERSON><PERSON> <PERSON>"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, musst du eine Produktbewertungs-App hinzufügen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}}}, "multicolumn": {"name": "<PERSON>t mehreren <PERSON>", "settings": {"title": {"label": "Überschrift"}, "image_width": {"label": "Bildbreite", "options__1": {"label": "Drittelbreite der Spalte"}, "options__2": {"label": "Halbe Breite der Spalte"}, "options__3": {"label": "Ganze Breite der Spalte"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}, "options__4": {"label": "Kreis"}}, "column_alignment": {"label": "Spaltenausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "background_style": {"label": "Sekundärer Hintergrund", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Als Spaltenhintergrund anzeigen"}}, "button_label": {"label": "Schaltflächenbeschriftung"}, "button_link": {"label": "Schaltflächenlink"}, "swipe_on_mobile": {"label": "Wischen auf Mobilgeräten aktivieren"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}}, "blocks": {"column": {"name": "<PERSON>lt<PERSON>", "settings": {"image": {"label": "Bild"}, "title": {"label": "Überschrift"}, "text": {"label": "Beschreibung"}, "link_label": {"label": "Link-Label"}, "link": {"label": "Link"}}}}, "presets": {"name": "<PERSON>t mehreren <PERSON>"}}, "newsletter": {"name": "E-Mail-Anmeldung", "settings": {"full_width": {"label": "Abschnitt über die gesamte Breite"}, "paragraph": {"content": "Durch jedes E-Mail-Abonnement wird ein Kundenkonto erstellt. [Mehr Informationen](https://help.shopify.com/manual/customers)"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift"}}}, "paragraph": {"name": "Unter-Überschrift", "settings": {"paragraph": {"label": "Beschreibung"}}}, "email_form": {"name": "E-Mail-Formular"}}, "presets": {"name": "E-Mail-Anmeldung"}}, "page": {"name": "Seite", "settings": {"page": {"label": "Seite"}}, "presets": {"name": "Seite"}}, "rich-text": {"name": "Rich Text", "settings": {"full_width": {"label": "Abschnitt über die gesamte Breite"}, "desktop_content_position": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktop-Inhaltsposition", "info": "Positionen werden automatisch für die mobile Nutzung optimiert."}, "content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Inhaltsausrichtung"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Beschreibung"}}}, "buttons": {"name": "Schaltflächen", "settings": {"button_label_1": {"label": "<PERSON><PERSON><PERSON>flächenbeschriftung", "info": "Lasse die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link_1": {"label": "<PERSON><PERSON><PERSON>"}, "button_style_secondary_1": {"label": "Umriss-Stil für Schaltfläche verwenden"}, "button_label_2": {"label": "Zweite Schaltflächenbeschriftung", "info": "Lasse die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link_2": {"label": "Zweiter Schaltflächenlink"}, "button_style_secondary_2": {"label": "Umriss-Stil für Schaltfläche verwenden"}}}, "caption": {"name": "Bildtext", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Untertitel"}, "options__2": {"label": "Großbuchstaben"}}, "caption_size": {"label": "Textgröße", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Rich Text"}}, "apps": {"name": "Apps", "settings": {"include_margins": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> so gestalten wie das Theme"}}, "presets": {"name": "Apps"}}, "video": {"name": "Video", "settings": {"heading": {"label": "Titel"}, "cover_image": {"label": "Titelbild"}, "video_url": {"label": "URL", "info": "YouTube- oder Vimeo-URL verwenden"}, "description": {"label": "Video-Alt-Text", "info": "Beschreibe das Video für Kunden, die Bildschirmlesegeräte benutzen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "Bild-Padding hinzufügen", "info": "<PERSON><PERSON><PERSON>e Bild-Padding aus, wenn du nicht möchtest, dass dein Titelbild abgeschnitten wird."}, "full_width": {"label": "Abschnitt über die gesamte Breite"}, "video": {"label": "Video"}, "enable_video_looping": {"label": "Videoschleife abspielen"}, "header__1": {"content": "Von Shopify gehostetes Video"}, "header__2": {"content": "<PERSON>der Video von URL einbetten"}, "header__3": {"content": "Stil"}, "paragraph": {"content": "<PERSON>ird an<PERSON>, wenn kein von Shopify gehostetes Video ausgewählt wurde."}}, "presets": {"name": "Video"}}, "featured-product": {"name": "Vorgestelltes Produkt", "blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "title": {"name": "Titel"}, "price": {"name": "Pre<PERSON>"}, "quantity_selector": {"name": "Men<PERSON>aus<PERSON><PERSON>"}, "variant_picker": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"picker_type": {"label": "Optik", "options__1": {"label": "Dropdown"}, "options__2": {"label": "<PERSON><PERSON>ln"}}, "swatch_shape": {"label": "<PERSON><PERSON><PERSON>", "info": "Aktiviere [<PERSON><PERSON><PERSON><PERSON>](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) für Produktoptionen.", "options__1": {"label": "Kreis"}, "options__2": {"label": "Quadrat"}, "options__3": {"label": "<PERSON><PERSON>"}}}}, "buy_buttons": {"name": "Buy Buttons", "settings": {"show_dynamic_checkout": {"label": "Dynamischen Checkout-<PERSON><PERSON> anzeigen", "info": "Wenn sie die Zahlungsmethoden verwenden, die in deinem Shop verfügbar sind, sehen <PERSON> ihre bevorzugte Option, z. B. PayPal oder Apple Pay. [Mehr Informationen](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Beschreibung"}, "share": {"name": "Teilen", "settings": {"featured_image_info": {"content": "<PERSON><PERSON> <PERSON> e<PERSON> Link in Social-Media-Posts einfügst, wird das Feature-Bild der Seite als Vorschaubild angezeigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Ein Titel und eine Beschreibung des Shops sind im Vorschaubild enthalten. [Mehr Informationen](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Text"}}}, "rating": {"name": "Produktbewertung", "settings": {"paragraph": {"content": "Um eine Bewertung anzuzeigen, musst du eine Produktbewertungs-App hinzufügen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}}, "settings": {"product": {"label": "Produkt"}, "secondary_background": {"label": "Sekundären Hintergrund anzeigen"}, "header": {"content": "Medien", "info": "Mehr Informationen zu [Medienarten](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Videoschleife aktivieren"}, "hide_variants": {"label": "Medien von nicht ausgewählten Varianten auf dem Desktop ausblenden"}, "media_position": {"label": "Desktop-Medienposition", "info": "Positionen werden automatisch für die mobile Nutzung optimiert.", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}, "presets": {"name": "Vorgestelltes Produkt"}}, "email-signup-banner": {"name": "Banner für E-Mail-Anmeldung", "settings": {"paragraph": {"content": "Durch jedes E-Mail-Abonnement wird ein Kundenkonto erstellt. [Mehr Informationen](https://help.shopify.com/manual/customers)"}, "image": {"label": "Hintergrundbild"}, "show_background_image": {"label": "Hintergrundbild anzeigen"}, "show_text_box": {"label": "Container auf dem Desktop anzeigen"}, "image_overlay_opacity": {"label": "Deckkraft der Bildüberlagerung"}, "show_text_below": {"label": "Auf Mobilgeräten Inhalt unterhalb der Bilder anzeigen", "info": "Verwende Bilder mit einem Seitenverhältnis von 16:9 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "image_height": {"label": "Bannerhöhe", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Verwende Bilder mit einem Seitenverhältnis von 16:9 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_position": {"options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON> rechts"}, "options__4": {"label": "Mitte links"}, "options__5": {"label": "<PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON> rechts"}, "options__7": {"label": "Unten links"}, "options__8": {"label": "<PERSON>ten zent<PERSON>t"}, "options__9": {"label": "Unten rechts"}, "label": "Desktopinhaltsposition"}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktopinhaltsausrichtung"}, "header": {"content": "Mobiles Layout"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Mobile Inhaltsausrichtung"}, "color_scheme": {"info": "<PERSON><PERSON><PERSON>, wenn Container angezeigt wird."}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Titel"}}}, "paragraph": {"name": "Absatz", "settings": {"paragraph": {"label": "Beschreibung"}, "text_style": {"options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "label": "Textstil"}}}, "email_form": {"name": "E-Mail-Formular"}}, "presets": {"name": "Banner für E-Mail-Anmeldung"}}, "slideshow": {"name": "Slideshow", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Volle Breite"}, "options__2": {"label": "<PERSON><PERSON>"}}, "slide_height": {"label": "Diahö<PERSON>", "options__1": {"label": "An erstes Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "slider_visual": {"label": "Seitennummerierungsstil", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Punkte"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "auto_rotate": {"label": "Autorotieren der Slides"}, "change_slides_speed": {"label": "Anzeige der nächsten Folie alle"}, "show_text_below": {"label": "Auf Mobilgeräten Inhalt unterhalb der Bilder anzeigen"}, "mobile": {"content": "Mobiles Layout"}, "accessibility": {"content": "Barrierefreiheit", "label": "Slideshow-Beschreibung", "info": "Beschreibe die Slideshow für Kunden, die Bildschirmlesegeräte benutzen."}}, "blocks": {"slide": {"name": "Folie", "settings": {"image": {"label": "Bild"}, "heading": {"label": "Titel"}, "subheading": {"label": "Unter-Überschrift"}, "button_label": {"label": "Schaltflächenbeschriftung", "info": "Lasse die Beschriftung leer, um die Schaltfläche auszublenden."}, "link": {"label": "Schaltflächenlink"}, "secondary_style": {"label": "Umriss-Stil für Schaltfläche verwenden"}, "box_align": {"label": "Desktop-Inhaltsposition", "options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON> rechts"}, "options__4": {"label": "Mitte links"}, "options__5": {"label": "<PERSON><PERSON> mittig"}, "options__6": {"label": "<PERSON><PERSON> rechts"}, "options__7": {"label": "Unten links"}, "options__8": {"label": "Unten mittig"}, "options__9": {"label": "Unten rechts"}, "info": "Positionen werden automatisch für die mobile Nutzung optimiert."}, "show_text_box": {"label": "Container auf dem Desktop anzeigen"}, "text_alignment": {"label": "Desktop-Inhaltsausrichtung", "option_1": {"label": "Links"}, "option_2": {"label": "<PERSON><PERSON><PERSON>"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Deckkraft der Bildüberlagerung"}, "text_alignment_mobile": {"label": "Mobile Inhaltsausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Slideshow"}}, "collapsible_content": {"name": "Einklappbarer Inhalt", "settings": {"caption": {"label": "Bildtext"}, "heading": {"label": "Überschrift"}, "heading_alignment": {"label": "Ausrichtung der Überschrift", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "layout": {"label": "Layout", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Reihencontainer"}, "options__3": {"label": "Abschnittscontainer"}}, "open_first_collapsible_row": {"label": "<PERSON><PERSON><PERSON> e<PERSON>bare Reihe ö<PERSON>"}, "header": {"content": "Bildlayout"}, "image": {"label": "Bild"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "desktop_layout": {"label": "Desktop-Layout", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Bild an zweiter Stelle"}, "info": "Das Bild wird auf mobilen Geräte immer zuerst angezeigt."}, "container_color_scheme": {"label": "Farbschema für Container", "info": "Wird ange<PERSON>, wenn für das Layout Zeilen- oder Abschnitts-Container festgelegt wird."}}, "blocks": {"collapsible_row": {"name": "Einklappbare Reihe", "settings": {"heading": {"info": "Füge eine Überschrift ein, die den Inhalt erklärt.", "label": "Überschrift"}, "row_content": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "Reiheninhalt der Seite"}, "icon": {"label": "Symbol", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Flasche"}, "options__5": {"label": "Box"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__7": {"label": "Chat-Blase"}, "options__8": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__9": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON>"}, "options__11": {"label": "Laktosef<PERSON>i"}, "options__12": {"label": "<PERSON><PERSON><PERSON>"}, "options__13": {"label": "<PERSON><PERSON>"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Glutenfrei"}, "options__16": {"label": "<PERSON><PERSON>"}, "options__17": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__18": {"label": "<PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Blitz"}, "options__21": {"label": "Lippenstift"}, "options__22": {"label": "<PERSON><PERSON><PERSON>"}, "options__23": {"label": "Pinnnadel"}, "options__24": {"label": "<PERSON><PERSON>"}, "options__25": {"label": "<PERSON><PERSON>"}, "options__26": {"label": "Pfotenabdruck"}, "options__27": {"label": "<PERSON><PERSON><PERSON>"}, "options__28": {"label": "Parfüm"}, "options__29": {"label": "Flugzeug"}, "options__30": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__31": {"label": "<PERSON><PERSON><PERSON>"}, "options__32": {"label": "Fragezeichen"}, "options__33": {"label": "Recyclen"}, "options__34": {"label": "Rückgabe"}, "options__35": {"label": "Lineal"}, "options__36": {"label": "Servierteller"}, "options__37": {"label": "<PERSON><PERSON><PERSON>"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__41": {"label": "Stern"}, "options__42": {"label": "Stoppuhr"}, "options__43": {"label": "Lieferwagen"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Einklappbarer Inhalt"}}, "main-account": {"name": "Ko<PERSON>"}, "main-activate-account": {"name": "Kontoaktivierung"}, "main-addresses": {"name": "<PERSON><PERSON><PERSON>"}, "main-login": {"name": "<PERSON><PERSON>"}, "main-order": {"name": "Bestellung"}, "main-register": {"name": "Registrierung"}, "main-reset-password": {"name": "Passwort zurücksetzen"}, "related-products": {"name": "Ähnliche Produkte", "settings": {"heading": {"label": "Überschrift"}, "products_to_show": {"label": "Maximal anzuzeigende Produkte"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "paragraph__1": {"content": "Dynamische Empfehlungen nutzen Bestell- und Produktinformationen, um sich mit der Zeit zu verändern und zu verbessern. [Mehr Informationen](https://help.shopify.com/themes/development/recommended-products)"}, "header__2": {"content": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Hochformat"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, musst du eine Produktbewertungs-App hinzufügen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}}}, "multirow": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"image": {"label": "Bild"}, "image_height": {"options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Bildhöhe"}, "desktop_image_width": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktop-Bildbreite", "info": "Bild wird automatisch für die mobile Nutzung optimiert."}, "heading_size": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Größe der Überschrift"}, "text_style": {"options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "label": "Textstil"}, "button_style": {"options__1": {"label": "Durchgehende Schaltfläche"}, "options__2": {"label": "Umriss-Schaltfläche"}, "label": "Schaltflächenstil"}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktop-Inhaltsausrichtung"}, "desktop_content_position": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Unten"}, "label": "Desktop-Inhaltsposition", "info": "Positionen werden automatisch für die mobile Nutzung optimiert."}, "image_layout": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>s"}, "options__3": {"label": "Linksbündig"}, "options__4": {"label": "Rechtsbündig"}, "label": "Desktop-Bildplatzierung", "info": "Platzierung wird automatisch für die mobile Nutzung optimiert."}, "container_color_scheme": {"label": "Farbschema für Container"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Mobile Inhaltsausrichtung"}, "header_mobile": {"content": "Mobiles Layout"}}, "blocks": {"row": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "Bild"}, "caption": {"label": "Bildtext"}, "heading": {"label": "Überschrift"}, "text": {"label": "Text"}, "button_label": {"label": "Schaltflächenbeschriftung"}, "button_link": {"label": "Schaltflächenlink"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "quick-order-list": {"name": "<PERSON><PERSON><PERSON>", "settings": {"show_image": {"label": "Bilder anzeigen"}, "show_sku": {"label": "SKUs anzeigen"}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}}}