{"settings_schema": {"colors": {"name": "Ren<PERSON>r", "settings": {"background": {"label": "Arka plan"}, "background_gradient": {"label": "Arka plan gradyanı", "info": "Arka plan gradyanı, mümkün olduğunda arka planın yerine geçer."}, "text": {"label": "<PERSON><PERSON>"}, "button_background": {"label": "Sabit düğme arka planı"}, "button_label": {"label": "<PERSON><PERSON> dü<PERSON> et<PERSON>"}, "secondary_button_label": {"label": "<PERSON><PERSON>ş çizgi düğmes<PERSON>"}, "shadow": {"label": "<PERSON><PERSON><PERSON>"}}}, "typography": {"name": "Tipografi", "settings": {"type_header_font": {"label": "Yazı tipi", "info": "Farklı bir yazı tipi seçmeniz mağazanızın hızını etkileyebilir. [Sistem yazı tipleri hakkında daha fazla bilgi edinin.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "header__1": {"content": "Başlıklar"}, "header__2": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "type_body_font": {"label": "Yazı tipi", "info": "Farklı bir yazı tipi seçmeniz mağazanızın hızını etkileyebilir. [Sistem yazı tipleri hakkında daha fazla bilgi edinin.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "heading_scale": {"label": "Ya<PERSON><PERSON> boyutu <PERSON>"}, "body_scale": {"label": "Ya<PERSON><PERSON> boyutu <PERSON>"}}}, "social-media": {"name": "So<PERSON>al medya", "settings": {"social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "https://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "So<PERSON>al medya he<PERSON>ı"}}}, "currency_format": {"name": "Para birimi biçimi", "settings": {"content": "Para birimi kodları", "currency_code_enabled": {"label": "Para birimi kodlarını göster"}, "paragraph": "Sepet ve ödeme ücretleri her zaman para birimi kodlarını gösterir. Örnek: 1,00 USD."}}, "layout": {"name": "D<PERSON>zen", "settings": {"page_width": {"label": "Say<PERSON> genişliği"}, "spacing_sections": {"label": "Şablon bölümleri arasındaki alan"}, "header__grid": {"content": "Izgara"}, "paragraph__grid": {"content": "Birden fazla sütun veya satır içeren alanları etkiler."}, "spacing_grid_horizontal": {"label": "<PERSON><PERSON><PERSON>"}, "spacing_grid_vertical": {"label": "<PERSON><PERSON> b<PERSON>"}}}, "search_input": {"name": "<PERSON><PERSON>", "settings": {"header": {"content": "<PERSON><PERSON>"}, "predictive_search_enabled": {"label": "<PERSON><PERSON>"}, "predictive_search_show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON>ı<PERSON>ı gö<PERSON>", "info": "<PERSON>ma <PERSON>i etkinleştirildiğinde görünür."}, "predictive_search_show_price": {"label": "<PERSON><PERSON>ün fiyatını göster", "info": "<PERSON>ma <PERSON>i etkinleştirildiğinde görünür."}}}, "global": {"settings": {"header__border": {"content": "Kenarlık"}, "header__shadow": {"content": "<PERSON><PERSON><PERSON>"}, "blur": {"label": "Bulanıklık"}, "corner_radius": {"label": "Köşe yarıçapı"}, "horizontal_offset": {"label": "<PERSON><PERSON><PERSON>"}, "vertical_offset": {"label": "<PERSON><PERSON> den<PERSON>e"}, "thickness": {"label": "Kalınlık"}, "opacity": {"label": "Opaklık"}, "image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>gus<PERSON>"}, "text_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "<PERSON><PERSON>"}}}, "badges": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"position": {"options__1": {"label": "Alt sol"}, "options__2": {"label": "Alt sağ"}, "options__3": {"label": "Üst sol"}, "options__4": {"label": "Üst sağ"}, "label": "<PERSON><PERSON><PERSON><PERSON> konum"}, "sale_badge_color_scheme": {"label": "İndirim rozeti renk şeması"}, "sold_out_badge_color_scheme": {"label": "Tükendi rozeti renk şeması"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "variant_pills": {"name": "<PERSON><PERSON><PERSON><PERSON>", "paragraph": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> vary<PERSON>larınızı göstermenin bir yoludur. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"}, "inputs": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "content_containers": {"name": "İçerik kapsayıcıları"}, "popups": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve pencereler", "paragraph": "Gezinme açı<PERSON>ı<PERSON>, açılır pencere modları ve sepet açılır pencereleri gibi alanları etkiler."}, "media": {"name": "<PERSON><PERSON><PERSON>"}, "drawers": {"name": "Çekmeceler"}, "cart": {"name": "Sepet", "settings": {"cart_type": {"label": "<PERSON><PERSON> türü", "drawer": {"label": "Çekmece"}, "page": {"label": "Say<PERSON>"}, "notification": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere bildirimi"}}, "show_vendor": {"label": "Satıcıyı göster"}, "show_cart_note": {"label": "<PERSON><PERSON> notunu <PERSON>"}, "cart_drawer": {"header": "Sepet çekmecesi", "collection": {"label": "Koleksiyon", "info": "Sepet çekmecesi boşken görünür."}}}}, "cards": {"name": "<PERSON>rün kartları", "settings": {"style": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Kart"}, "label": "Stil"}}}, "collection_cards": {"name": "Koleksiyon kartları", "settings": {"style": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Kart"}, "label": "Stil"}}}, "blog_cards": {"name": "Blog kartları", "settings": {"style": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Kart"}, "label": "Stil"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_width": {"label": "Masaüstü logo genişliği", "info": "<PERSON><PERSON>, mobil i<PERSON>in otomatik olarak optimize edilir."}, "favicon": {"label": "<PERSON><PERSON><PERSON>", "info": "Ölçeği 32 x 32 piksele düşürülür"}}}, "brand_information": {"name": "<PERSON><PERSON>i", "settings": {"brand_headline": {"label": "Başlık"}, "brand_description": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "brand_image": {"label": "G<PERSON><PERSON><PERSON>"}, "brand_image_width": {"label": "G<PERSON><PERSON><PERSON> genişliği"}, "paragraph": {"content": "Mağazanızın altbilgisine bir marka açıklaması ekleyin."}}}, "animations": {"name": "Animasyonlar", "settings": {"animations_reveal_on_scroll": {"label": "Kaydır<PERSON><PERSON><PERSON> bölümleri göster"}, "animations_hover_elements": {"options__1": {"label": "Yok"}, "options__2": {"label": "Dikey lift"}, "label": "Üzerine gelme efekti", "info": "Kartları ve düğmeleri etkiler.", "options__3": {"label": "3D lift"}}}}}, "sections": {"all": {"padding": {"section_padding_heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "padding_top": "<PERSON>st dolgu", "padding_bottom": "Alt dolgu"}, "spacing": "Boşluk", "colors": {"label": "Renk şeması", "has_cards_info": "Kart renk şemasını değiştirmek için tema ayarlarınızı güncelleyin."}, "heading_size": {"label": "Başlık boyutu", "options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}, "options__4": {"label": "Çok büyük"}}, "image_shape": {"options__1": {"label": "Varsayılan"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Sola ok"}, "options__5": {"label": "Sağa ok"}, "options__6": {"label": "Baklava"}, "options__7": {"label": "Paralelkenar"}, "options__8": {"label": "<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Bir görsel şeklinin etkin olduğu standart stile sahip kartlarda sınır bulunmaz"}, "animation": {"content": "Animasyonlar", "image_behavior": {"options__1": {"label": "Yok"}, "options__2": {"label": "Ortam iç<PERSON> ha<PERSON>et"}, "label": "<PERSON><PERSON><PERSON><PERSON>", "options__3": {"label": "Sabit arka plan konumu"}, "options__4": {"label": "Kaydırarak yakınlaştır"}}}}, "announcement-bar": {"name": "<PERSON><PERSON><PERSON>", "blocks": {"announcement": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_alignment": {"label": "<PERSON><PERSON>", "options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}}, "link": {"label": "Bağlantı"}}}}, "settings": {"auto_rotate": {"label": "Duyuruları otomatik olarak döndür"}, "change_slides_speed": {"label": "<PERSON><PERSON> zaman aralığında değiştir:"}, "header__1": {"content": "So<PERSON><PERSON> medya sim<PERSON>eri", "info": "Sosyal medya hesaplarınızın görüntülenmesi için bu hesapları [tema ayarların<PERSON><PERSON>](/editor?context=theme&category=social%20media) bağlayın."}, "header__2": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "show_social": {"label": "Simgeleri masaüstünde göster"}, "header__3": {"content": "<PERSON><PERSON><PERSON>/bö<PERSON> se<PERSON>", "info": "<PERSON><PERSON><PERSON>/bö<PERSON> e<PERSON> [pazar ayar<PERSON>n<PERSON>](/admin/settings/markets) gidin."}, "enable_country_selector": {"label": "<PERSON><PERSON><PERSON>/bö<PERSON>kinleştir"}, "header__4": {"content": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "info": "Dil eklemek için [dil ayarların<PERSON>za](/admin/settings/languages) gidin."}, "enable_language_selector": {"label": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> etkin<PERSON>ştir"}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "collage": {"name": "<PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "Başlık"}, "desktop_layout": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "Sol geniş blok"}, "options__2": {"label": "Sağ geniş blok"}}, "mobile_layout": {"label": "<PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "card_styles": {"label": "Kart stili", "info": "<PERSON><PERSON><PERSON><PERSON>, koleksiyon ve blog kartı stilleri tema ayarlarınızdan güncellenebilir.", "options__1": {"label": "Bireysel kart stilleri kullanın"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON> stilini ürün kartı şeklinde ayarla"}}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}}}, "product": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"product": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "secondary_background": {"label": "İkincil arka planı göster"}, "second_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}}}, "collection": {"name": "Koleksiyon", "settings": {"collection": {"label": "Koleksiyon"}}}, "video": {"name": "Video", "settings": {"cover_image": {"label": "Kapak gö<PERSON>"}, "video_url": {"label": "URL", "info": "Bölümde başka bloklar varsa video açılır pencerede oynatılır.", "placeholder": "YouTube veya Vimeo URL'si kullanın"}, "description": {"label": "Video alternatif metni", "info": "Ekran okuyucu kullanan müşteriler için videoyu açıklayın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "collection-list": {"name": "Koleks<PERSON><PERSON>esi", "settings": {"title": {"label": "Başlık"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}, "info": "Koleksiyonlarınızı düzenleyerek görsel ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/products/collections)"}, "swipe_on_mobile": {"label": "Mobil cihazda kaydırmayı etkinleştir"}, "show_view_all": {"label": "Liste gösterilenden daha fazla koleksiyon içeriyorsa \"Tümünü gö<PERSON><PERSON><PERSON><PERSON><PERSON>\" <PERSON><PERSON><PERSON><PERSON><PERSON> etkinleştir"}, "columns_desktop": {"label": "Masaüstündeki sütun sayısı"}, "header_mobile": {"content": "<PERSON><PERSON>"}, "columns_mobile": {"label": "Mobildeki sütun say<PERSON>ı", "options__1": {"label": "1 sütun"}, "options__2": {"label": "2 sütun"}}}, "blocks": {"featured_collection": {"name": "Koleksiyon", "settings": {"collection": {"label": "Koleksiyon"}}}}, "presets": {"name": "Koleks<PERSON><PERSON>esi"}}, "contact-form": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "presets": {"name": "İletişim formu"}}, "custom-liquid": {"name": "Özel Liquid", "settings": {"custom_liquid": {"label": "Liquid kodu", "info": "Gelişmiş özelleştirmeler oluşturmak için uygulama parçacıkları veya başka bir kod ekleyin. [Daha fazla bilgi edinin](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Özel Liquid"}}, "featured-blog": {"name": "Blog gönderileri", "settings": {"heading": {"label": "Başlık"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Gösterilecek blog gönderisi sayısı"}, "show_view_all": {"label": "Blog gösterilenden daha fazla blog gönderisi içeriyorsa \"Tümünü görüntüle\" düğ<PERSON>ini etkinleştir"}, "show_image": {"label": "<PERSON>ne çıkan görseli göster", "info": "En iyi sonuçlar için 3:2 en-boy oran<PERSON>na sahip bir gö<PERSON>l kullanın. [<PERSON>ha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "show_date": {"label": "<PERSON><PERSON><PERSON>"}, "show_author": {"label": "Yazarı göster"}, "columns_desktop": {"label": "Masaüstündeki sütun sayısı"}}, "presets": {"name": "Blog gönderileri"}}, "featured-collection": {"name": "Öne çıkan koleksiyon", "settings": {"title": {"label": "Başlık"}, "collection": {"label": "Koleksiyon"}, "products_to_show": {"label": "Gösterilecek maksimum ürün sayısı"}, "show_view_all": {"label": "Koleksiyon gösterilenden daha fazla ürün içeriyorsa \"Tümünü gö<PERSON><PERSON><PERSON><PERSON><PERSON>\"yi et<PERSON>"}, "header": {"content": "Ürün kartı"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "show_secondary_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}, "show_vendor": {"label": "Satıcıyı göster"}, "show_rating": {"label": "<PERSON>rün <PERSON>larını göster", "info": "Puan göstermek için bir ürün puanlandırma uygulaması ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "columns_desktop": {"label": "Masaüstündeki sütun sayısı"}, "description": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "show_description": {"label": "Yöneticiden koleksiyon açıklamasını göster"}, "description_style": {"label": "Açıklama stili", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}, "view_all_style": {"label": "\"<PERSON>ümü<PERSON>ü görü<PERSON><PERSON><PERSON>\" stili", "options__1": {"label": "Bağlantı"}, "options__2": {"label": "<PERSON><PERSON>ş çizgi düğmes<PERSON>"}, "options__3": {"label": "<PERSON>bit dü<PERSON>"}}, "enable_desktop_slider": {"label": "Carousel'i masa üstünde etkinleştir"}, "full_width": {"label": "Ürünleri tam genişlikli yap"}, "header_mobile": {"content": "<PERSON><PERSON>"}, "columns_mobile": {"label": "Mobildeki sütun say<PERSON>ı", "options__1": {"label": "1 sütun"}, "options__2": {"label": "2 sütun"}}, "swipe_on_mobile": {"label": "Mobil cihazda kaydırmayı etkinleştir"}, "enable_quick_buy": {"label": "Hızlı ekleme düğmesini etkinleştir", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere ve çekmece sepet türü için optimumdur."}}, "presets": {"name": "Öne çıkan koleksiyon"}}, "footer": {"name": "Altbilgi", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Başlık"}, "menu": {"label": "<PERSON><PERSON>", "info": "Yalnızca üst taraftaki menü öğeleri gösterilir."}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Başlık"}, "subtext": {"label": "Alt metin"}}}, "brand_information": {"name": "<PERSON><PERSON>i", "settings": {"paragraph": {"content": "Marka bilgileriniz bu blokta görünür. [Marka bilgilerini düzenleyin.](/editor?context=theme&category=brand%20information)"}, "header__1": {"content": "So<PERSON><PERSON> medya sim<PERSON>eri"}, "show_social": {"label": "Sosyal medya simgeleri<PERSON>", "info": "Sosyal medya hesaplarınızın görüntülenmesi için bu hesapları [tema ayarların<PERSON><PERSON>](/editor?context=theme&category=social%20media) bağlayın."}}}}, "settings": {"newsletter_enable": {"label": "E-posta kaydını göster"}, "newsletter_heading": {"label": "Başlık"}, "header__1": {"content": "E-posta Kaydı", "info": "\"Kabul edilen pazarlama\" mü<PERSON><PERSON><PERSON> listenize otomatik olarak eklenen aboneler. [Daha fazla bilgi edinin](https://help.shopify.com/manual/customers/manage-customers)"}, "header__2": {"content": "So<PERSON><PERSON> medya sim<PERSON>eri", "info": "Sosyal medya hesaplarınızın görüntülenmesi için bu hesapları [tema ayarların<PERSON><PERSON>](/editor?context=theme&category=social%20media) bağlayın."}, "show_social": {"label": "Sosyal medya simgeleri<PERSON>"}, "header__3": {"content": "<PERSON><PERSON><PERSON>/bö<PERSON> se<PERSON>"}, "header__4": {"info": "<PERSON><PERSON><PERSON>/bö<PERSON> e<PERSON> [pazar ayar<PERSON>n<PERSON> ](/admin/settings/markets) gidin."}, "enable_country_selector": {"label": "<PERSON><PERSON><PERSON>/bö<PERSON>kinleştir"}, "header__5": {"content": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "header__6": {"info": "Dil eklemek için [dil ayarların<PERSON>za](/admin/settings/languages) gidin."}, "enable_language_selector": {"label": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> etkin<PERSON>ştir"}, "header__7": {"content": "<PERSON><PERSON><PERSON>"}, "payment_enable": {"label": "<PERSON>deme simgeleri<PERSON>"}, "margin_top": {"label": "Üst kenar b<PERSON>luğ<PERSON>"}, "header__8": {"content": "Politika bağlantıları", "info": "Mağaza politikası eklemek için [politika ayarlarınıza](/admin/settings/legal) gidin."}, "show_policy": {"label": "Politika bağlantılarını göster"}, "header__9": {"content": "Follow on Shop", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Shop uygulamasındaki mağazanızı vitrininizden takip edebilmesi için Shop Pay'i etkinleştirmeniz gerekir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "Follow on Shop'u etkinleştir"}}}, "header": {"name": "Üstbilgi", "settings": {"logo_position": {"label": "Masaüstü logo konumu", "options__1": {"label": "Orta sol"}, "options__2": {"label": "Üst sol"}, "options__3": {"label": "Üst orta"}, "options__4": {"label": "<PERSON><PERSON> kısmın ortası"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Ayırıcı satırı göster"}, "margin_bottom": {"label": "Alt kenar boşluğu"}, "menu_type_desktop": {"label": "Ma<PERSON>ü<PERSON><PERSON> menü türü", "info": "<PERSON><PERSON>, mobil cihazlar için otomatik olarak optimize edilir.", "options__1": {"label": "Açılır <PERSON>ü"}, "options__2": {"label": "Mega menü"}, "options__3": {"label": "Çekmece"}}, "mobile_layout": {"content": "<PERSON><PERSON>"}, "mobile_logo_position": {"label": "Mobil logo konumu", "options__1": {"label": "Orta"}, "options__2": {"label": "Sol"}}, "logo_help": {"content": "Logonuzu şurada düzenleyin: [tema ayar<PERSON>](/editor?context=theme&category=logo)."}, "sticky_header_type": {"label": "Sabit üstbilgi", "options__1": {"label": "Yok"}, "options__2": {"label": "Yukarı kaydırıldığında"}, "options__3": {"label": "Her zaman"}, "options__4": {"label": "Her zaman, logo boy<PERSON><PERSON><PERSON>"}}, "header__3": {"content": "<PERSON><PERSON><PERSON>/bö<PERSON> se<PERSON>"}, "header__4": {"info": "<PERSON><PERSON><PERSON>/bö<PERSON> e<PERSON> [pazar ayar<PERSON>n<PERSON>](/admin/settings/markets) gidin."}, "enable_country_selector": {"label": "<PERSON><PERSON><PERSON>/bö<PERSON> etkinleştirin"}, "header__5": {"content": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "header__6": {"info": "Dil eklemek için [dil ayarların<PERSON>za](/admin/settings/languages) gidin."}, "enable_language_selector": {"label": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> etkin<PERSON>ştir"}, "header__1": {"content": "Renk"}, "menu_color_scheme": {"label": "<PERSON><PERSON> renk <PERSON>"}}}, "image-banner": {"name": "Görsel banner'ı", "settings": {"image": {"label": "İlk görsel"}, "image_2": {"label": "<PERSON><PERSON><PERSON>"}, "stack_images_on_mobile": {"label": "Mobilde görselleri üst üste ekle"}, "show_text_box": {"label": "Masaüstünde kapsayıcıyı göster"}, "image_overlay_opacity": {"label": "Görsel yer paylaşımı opaklığı"}, "show_text_below": {"label": "Mobil cihaz üzerinde kapsayıcıyı göster"}, "image_height": {"label": "Banner yüksekliği", "options__1": {"label": "İlk görsele uyarla"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "info": "En iyi sonuçlar için 3:2 en-boy oran<PERSON>na sahip bir gö<PERSON>l kullanın. [<PERSON>ha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "Büyük"}}, "desktop_content_position": {"options__1": {"label": "Üst Sol"}, "options__2": {"label": "Üst Orta"}, "options__3": {"label": "Üst Sağ"}, "options__4": {"label": "Orta Sol"}, "options__5": {"label": "Orta Kısmın <PERSON>"}, "options__6": {"label": "Orta Sağ"}, "options__7": {"label": "Alt Sol"}, "options__8": {"label": "Alt Orta"}, "options__9": {"label": "Alt Sağ"}, "label": "Masaüstü içerik konumu"}, "desktop_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "Masaüstü içerik hizalaması"}, "mobile_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "Mobil içerik hizalaması"}, "mobile": {"content": "<PERSON><PERSON>"}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık"}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}, "label": "<PERSON><PERSON> stili"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label_1": {"label": "<PERSON><PERSON> düğme et<PERSON>", "info": "Düğmeyi gizlemek için etiketi boş bırakın."}, "button_link_1": {"label": "İlk düğme bağlantısı"}, "button_style_secondary_1": {"label": "<PERSON><PERSON><PERSON> ç<PERSON> düğ<PERSON> stil<PERSON> kullan"}, "button_label_2": {"label": "<PERSON><PERSON><PERSON> düğ<PERSON>", "info": "Düğmeyi gizlemek için etiketi boş bırakın."}, "button_link_2": {"label": "İkinci düğme bağlantısı"}, "button_style_secondary_2": {"label": "<PERSON><PERSON><PERSON> ç<PERSON> düğ<PERSON> stil<PERSON> kullan"}}}}, "presets": {"name": "Görsel banner'ı"}}, "image-with-text": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "height": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "label": "Görsel yüksekliği", "options__4": {"label": "Büyük"}}, "layout": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Görsel 2"}, "label": "Masaüst<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON>, varsayılan mobil düzendir."}, "desktop_image_width": {"options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}, "label": "Masaüstü görsel genişliği", "info": "<PERSON><PERSON><PERSON><PERSON>, mobil cihazlar için otomatik olarak optimize edilir."}, "desktop_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "Masaüstü içerik hizalaması"}, "desktop_content_position": {"options__1": {"label": "Üst"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Alt"}, "label": "Masaüstü içerik konumu"}, "content_layout": {"options__1": {"label": "Çakışma yok"}, "options__2": {"label": "Çakışma"}, "label": "İçerik düzeni"}, "mobile_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "Mobil içerik hizalaması"}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık"}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "İçerik"}, "text_style": {"label": "<PERSON><PERSON> stili", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}}}}, "button": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Düğmeyi gizlemek için etiketi boş bırakın."}, "button_link": {"label": "<PERSON><PERSON><PERSON><PERSON> bağlantısı"}, "outline_button": {"label": "<PERSON><PERSON><PERSON> ç<PERSON> düğ<PERSON> stil<PERSON> kullan"}}}, "caption": {"name": "Alt yazı", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_style": {"label": "<PERSON><PERSON> stili", "options__1": {"label": "Alt yazı"}, "options__2": {"label": "Büyük harf"}}, "caption_size": {"label": "<PERSON><PERSON>", "options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}}}}}, "presets": {"name": "<PERSON><PERSON>"}}, "main-article": {"name": "Blog gönderisi", "blocks": {"featured_image": {"name": "<PERSON>ne <PERSON>", "settings": {"image_height": {"label": "Öne çıkan görsel yüksekliği", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "info": "En iyi sonuçlar için 16:9 en-boy oran<PERSON>na sahip bir gö<PERSON>l kullanın. [<PERSON>ha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "Büyük"}}}}, "title": {"name": "Başlık", "settings": {"blog_show_date": {"label": "<PERSON><PERSON><PERSON>"}, "blog_show_author": {"label": "Yazarı göster"}}}, "content": {"name": "İçerik"}, "share": {"name": "Paylaş", "settings": {"featured_image_info": {"content": "Sosyal medya gönderilerine bağlantı eklerseniz sayfanın öne çıkan görseli, ö<PERSON>zleme görseli olarak gösterilir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON> ve açıklaması, önizleme görseline dahildir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "<PERSON><PERSON>"}}}}}, "main-blog": {"name": "Blog gönderileri", "settings": {"header": {"content": "Blog gönderisi kartı"}, "show_image": {"label": "<PERSON>ne çıkan görseli göster"}, "paragraph": {"content": "Blog gönderilerinizi düzenleyerek alıntılarınızı değiştirin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "show_date": {"label": "<PERSON><PERSON><PERSON>"}, "show_author": {"label": "Yazarı göster"}, "layout": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "Izgara"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "info": "<PERSON><PERSON><PERSON><PERSON>, mobil cihazda üst üste eklenir."}, "image_height": {"label": "Öne çıkan görsel yüksekliği", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "options__4": {"label": "Büyük"}, "info": "En iyi sonuçlar için 3:2 en-boy oran<PERSON>na sahip bir gö<PERSON>l kullanın. [<PERSON>ha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-cart-footer": {"name": "Alt toplam", "blocks": {"subtotal": {"name": "Alt toplam fiyatı"}, "buttons": {"name": "<PERSON><PERSON><PERSON>"}}}, "main-cart-items": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "main-collection-banner": {"name": "Koleksiyon banner'ı", "settings": {"paragraph": {"content": "Koleksiyonunuzu düzenleyerek açıklama veya görsel ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Koleksiyon açıklamasını görüntüle"}, "show_collection_image": {"label": "Koleksiyon görselini görüntüle", "info": "En iyi sonuçlar için 16:9 en-boy oran<PERSON>na sahip bir gö<PERSON>l kullanın. [<PERSON>ha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "Ürün ızgarası", "settings": {"products_per_page": {"label": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>"}, "enable_filtering": {"label": "<PERSON>lt<PERSON><PERSON><PERSON><PERSON>", "info": "Search & Discovery uygulamasıyla filtreleri özelleştirin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_sorting": {"label": "Sıralamayı etkinleştir"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "show_secondary_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}, "show_vendor": {"label": "Satıcıyı göster"}, "header__1": {"content": "Filtreleme ve sıralama"}, "header__3": {"content": "Ürün kartı"}, "enable_tags": {"label": "<PERSON>lt<PERSON><PERSON><PERSON><PERSON>", "info": "Search & Discovery uygulamasıyla filtreleri özelleştirin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "show_rating": {"label": "<PERSON>rün <PERSON>larını göster", "info": "Puan göstermek için bir ürün puanlandırma uygulaması ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"}, "columns_desktop": {"label": "Masaüstündeki sütun sayısı"}, "header_mobile": {"content": "<PERSON><PERSON>"}, "columns_mobile": {"label": "Mobildeki sütun say<PERSON>ı", "options__1": {"label": "1 sütun"}, "options__2": {"label": "2 sütun"}}, "enable_quick_buy": {"label": "Hızlı ekleme düğmesini etkinleştir", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere ve çekmece sepet türü için optimumdur."}, "filter_type": {"label": "Masaüstü filtre düzeni", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Çekmece"}, "info": "<PERSON><PERSON><PERSON><PERSON>, varsayılan mobil düzendir."}}}, "main-list-collections": {"name": "Koleksiyonlar listesi sayfası", "settings": {"title": {"label": "Başlık"}, "sort": {"label": "Koleksiyonları sıralama ölçütü:", "options__1": {"label": "Alfabetik olarak, A-Z"}, "options__2": {"label": "Alfabetik olarak, Z-A"}, "options__3": {"label": "<PERSON><PERSON><PERSON>, yeniden eskiye"}, "options__4": {"label": "<PERSON><PERSON><PERSON>, eskiden yeniye"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON>, yüksekten düşüğe"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON>, dü<PERSON><PERSON>kten yükseğe"}}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}, "info": "Koleksiyonlarınızı düzenleyerek görsel ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Masaüstündeki sütun sayısı"}, "header_mobile": {"content": "<PERSON><PERSON>"}, "columns_mobile": {"label": "Mobildeki sütun say<PERSON>ı", "options__1": {"label": "1 sütun"}, "options__2": {"label": "2 sütun"}}}}, "main-page": {"name": "Say<PERSON>"}, "main-password-footer": {"name": "<PERSON><PERSON><PERSON>"}, "main-password-header": {"name": "<PERSON><PERSON><PERSON> ü<PERSON>bilgisi", "settings": {"logo_header": {"content": "Logo"}, "logo_help": {"content": "Logonuzu tema ayarlarında d<PERSON>."}}}, "main-product": {"blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_style": {"label": "Text style", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}}}, "title": {"name": "Başlık"}, "price": {"name": "<PERSON><PERSON><PERSON>"}, "quantity_selector": {"name": "<PERSON><PERSON>"}, "variant_picker": {"name": "Varyasyon <PERSON>ç<PERSON>", "settings": {"picker_type": {"label": "Stil", "options__1": {"label": "Açılır liste"}, "options__2": {"label": "Seçenekler"}}, "swatch_shape": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON> [numune parçaları](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) etkinleştirin.", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Yok"}}}}, "buy_buttons": {"name": "Satın al düğmeleri", "settings": {"show_dynamic_checkout": {"label": "Dinamik ödeme düğmelerini göster", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mağazanızda bulunan ödeme yöntemlerini kullanarak PayPal veya Apple Pay gibi tercih ettikleri seçeneği görür. [Daha fazla bilgi edinin](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Hediye kartları için alıcı bilgi formunu göster", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, plan<PERSON>m<PERSON>ş bir tarihte kişisel bir mesajla birlikte hediye kartları göndermesine olanak sağlar. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "pickup_availability": {"name": "<PERSON><PERSON><PERSON> alım stok durumu"}, "description": {"name": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "share": {"name": "Paylaş", "settings": {"featured_image_info": {"content": "Sosyal medya gönderilerine bağlantı eklerseniz sayfanın öne çıkan görseli, ö<PERSON>zleme görseli olarak gösterilir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON> ve açıklaması, önizleme görseline dahildir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "<PERSON><PERSON>"}}}, "collapsible_tab": {"name": "Daraltılabilir <PERSON>", "settings": {"heading": {"info": "İçeriği açıklayan bir başlık ekleyin.", "label": "Başlık"}, "content": {"label": "<PERSON><PERSON><PERSON>r içeriği"}, "page": {"label": "<PERSON><PERSON><PERSON>ı<PERSON> içeriği"}, "icon": {"options__1": {"label": "Hiç<PERSON>i"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Muz"}, "options__4": {"label": "Şişe"}, "options__5": {"label": "<PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON><PERSON> balonu"}, "options__8": {"label": "<PERSON><PERSON>"}, "options__9": {"label": "Pan<PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON> ürü<PERSON>"}, "options__11": {"label": "Süt ürünü içermez"}, "options__12": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__13": {"label": "Göz"}, "options__14": {"label": "Ateş"}, "options__15": {"label": "Glütensiz"}, "options__16": {"label": "<PERSON><PERSON><PERSON>"}, "options__17": {"label": "Ütü"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Ş<PERSON>şek"}, "options__21": {"label": "<PERSON><PERSON><PERSON>"}, "options__22": {"label": "<PERSON><PERSON>"}, "options__23": {"label": "<PERSON><PERSON> pini"}, "options__24": {"label": "Kabuklu yemişsiz"}, "label": "<PERSON>m<PERSON>", "options__25": {"label": "Pantolon"}, "options__26": {"label": "<PERSON><PERSON>"}, "options__27": {"label": "<PERSON><PERSON>"}, "options__28": {"label": "Parfüm"}, "options__29": {"label": "Uçak"}, "options__30": {"label": "<PERSON><PERSON>"}, "options__31": {"label": "<PERSON><PERSON><PERSON>"}, "options__32": {"label": "<PERSON><PERSON>"}, "options__33": {"label": "<PERSON><PERSON>"}, "options__34": {"label": "İade"}, "options__35": {"label": "Cetvel"}, "options__36": {"label": "<PERSON><PERSON>"}, "options__37": {"label": "Gömlek"}, "options__38": {"label": "Ayakkabı"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON>"}, "options__41": {"label": "Yıld<PERSON>z"}, "options__42": {"label": "Kronometre"}, "options__43": {"label": "<PERSON><PERSON><PERSON>"}, "options__44": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "popup": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere", "settings": {"link_label": {"label": "Bağlantı etiketi"}, "page": {"label": "Say<PERSON>"}}}, "rating": {"name": "<PERSON>rün puanı", "settings": {"paragraph": {"content": "Puan göstermek için bir ürün puanlandırma uygulaması ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"}}}, "complementary_products": {"name": "Tamamlayıcı ürünler", "settings": {"paragraph": {"content": "Tamamlayıcı ürünleri seçmek için Search & Discovery uygulamasını ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "Başlık"}, "make_collapsible_row": {"label": "Daraltılabilir satı<PERSON> o<PERSON> gö<PERSON>"}, "icon": {"info": "Daraltılabilir satır gösterildiğinde görünür."}, "product_list_limit": {"label": "Gösterilecek maksimum ürün sayısı"}, "products_per_page": {"label": "<PERSON><PERSON> başına ürün say<PERSON>ı"}, "pagination_style": {"label": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ı<PERSON> stili", "options": {"option_1": "Noktalar", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "<PERSON><PERSON><PERSON><PERSON>"}}, "product_card": {"heading": "Ürün kartı"}, "image_ratio": {"label": "Görsel oranı", "options": {"option_1": "Portre", "option_2": "<PERSON><PERSON>"}}, "enable_quick_add": {"label": "Hızlı ekleme düğmesini etkinleştir"}}}, "icon_with_text": {"name": "<PERSON><PERSON> i<PERSON>n simge", "settings": {"layout": {"label": "D<PERSON>zen", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}}, "content": {"label": "İçerik", "info": "Her sütun veya satır için bir simge seçin ya da görsel e<PERSON>in."}, "heading": {"info": "<PERSON><PERSON><PERSON> sütununu gizlemek için başlık etiketini boş bırakın."}, "icon_1": {"label": "İlk simge"}, "image_1": {"label": "İlk görsel"}, "heading_1": {"label": "İlk başlık"}, "icon_2": {"label": "İ<PERSON><PERSON> simge"}, "image_2": {"label": "<PERSON><PERSON><PERSON>"}, "heading_2": {"label": "İkinci başlık"}, "icon_3": {"label": "Üçüncü simge"}, "image_3": {"label": "Üçüncü görsel"}, "heading_3": {"label": "Üçüncü başlık"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "<PERSON><PERSON> stili", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}}}, "inventory": {"name": "<PERSON><PERSON><PERSON> du<PERSON>", "settings": {"text_style": {"label": "<PERSON><PERSON> stili", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}, "inventory_threshold": {"label": "Düşük envanter eşiği", "info": "Mevcutsa her zaman stokta göstermek için 0 değerini seçin"}, "show_inventory_quantity": {"label": "<PERSON><PERSON><PERSON> gö<PERSON>"}}}}, "settings": {"header": {"content": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON> hakkında daha fazla bilgi edinin: [medya tü<PERSON>.](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Video döngüs<PERSON><PERSON><PERSON>ş<PERSON>r"}, "enable_sticky_info": {"label": "Masaüstünde sabit içeriği etkinleştir"}, "hide_variants": {"label": "Bir varyasyon seçtikten sonra diğer varyasyonların medyasını gizleyin"}, "gallery_layout": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "Üst üste"}, "options__2": {"label": "2 sütun"}, "options__3": {"label": "Küçük resimler"}, "options__4": {"label": "Küçük resim döngüsü"}}, "media_size": {"label": "Masaüstü medya g<PERSON>liği", "info": "<PERSON><PERSON><PERSON>, mobil cihazlar için otomatik olarak optimize edilir.", "options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}}, "mobile_thumbnails": {"label": "<PERSON><PERSON>", "options__1": {"label": "2 sütun"}, "options__2": {"label": "Küçük resimleri göster"}, "options__3": {"label": "Küçük resimleri gizle"}}, "media_position": {"label": "Ma<PERSON>üst<PERSON> medya konumu", "info": "<PERSON><PERSON>, mobil cihazlar için otomatik olarak optimize edilir.", "options__1": {"label": "Sol"}, "options__2": {"label": "Sağ"}}, "image_zoom": {"label": "Görsel yakınlaştırma", "info": "Ligtbox'ı mobilde açmak için tıklayın ve imleci varsayılan seçeneklerin üzerine getirin.", "options__1": {"label": "Lightbox'ı aç"}, "options__2": {"label": "Tıkla ve imleci üzerine getir"}, "options__3": {"label": "Yakınlaştırma yok"}}, "constrain_to_viewport": {"label": "Medyayı ekran yüksekliğiyle sınırla"}, "media_fit": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Orijinal"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}, "name": "<PERSON><PERSON><PERSON>n bilgi<PERSON>i"}, "main-search": {"name": "<PERSON><PERSON>", "settings": {"image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "show_secondary_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}, "show_vendor": {"label": "Satıcıyı göster"}, "header__1": {"content": "Ürün kartı"}, "header__2": {"content": "Blog kartı", "info": "Blog kartı stilleri, arama sonuçlarındaki sayfa kartlarına da uygulanır. Kart stillerini değiştirmek için tema ayarlarınızı güncelleyin."}, "article_show_date": {"label": "<PERSON><PERSON><PERSON>"}, "article_show_author": {"label": "Yazarı göster"}, "show_rating": {"label": "<PERSON>rün <PERSON>larını göster", "info": "Puan göstermek için bir ürün puanlandırma uygulaması ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"}, "columns_desktop": {"label": "Masaüstündeki sütun sayısı"}, "header_mobile": {"content": "<PERSON><PERSON>"}, "columns_mobile": {"label": "Mobildeki sütun say<PERSON>ı", "options__1": {"label": "1 sütun"}, "options__2": {"label": "2 sütun"}}}}, "multicolumn": {"name": "Çoklu sütun", "settings": {"title": {"label": "Başlık"}, "image_width": {"label": "G<PERSON><PERSON><PERSON> genişliği", "options__1": {"label": "Sütun genişliğinin üçte biri"}, "options__2": {"label": "Sütun genişliğinin yarısı"}, "options__3": {"label": "Sütun genişliğinin tama<PERSON>ı"}}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "column_alignment": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}}, "background_style": {"label": "İkincil arka plan", "options__1": {"label": "Hiç<PERSON>i"}, "options__2": {"label": "Sütun arka planı olarak göster"}}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "button_link": {"label": "<PERSON><PERSON><PERSON><PERSON> bağlantısı"}, "swipe_on_mobile": {"label": "Mobil cihazda kaydırmayı etkinleştir"}, "columns_desktop": {"label": "Masaüstündeki sütun sayısı"}, "header_mobile": {"content": "<PERSON><PERSON>"}, "columns_mobile": {"label": "Mobildeki sütun say<PERSON>ı", "options__1": {"label": "1 sütun"}, "options__2": {"label": "2 sütun"}}}, "blocks": {"column": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "title": {"label": "Başlık"}, "text": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "link_label": {"label": "Bağlantı etiketi"}, "link": {"label": "Bağlantı"}}}}, "presets": {"name": "Çoklu sütun"}}, "newsletter": {"name": "E-posta kaydı", "settings": {"full_width": {"label": "Bölümü tam genişlikli yap"}, "paragraph": {"content": "Her e-posta aboneliği bir müşteri hesabı oluşturur. [Daha fazla bilgi edinin](https://help.shopify.com/manual/customers)"}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık"}}}, "paragraph": {"name": "Alt başlık", "settings": {"paragraph": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}}}, "email_form": {"name": "E-posta formu"}}, "presets": {"name": "E-posta kaydı"}}, "page": {"name": "Say<PERSON>", "settings": {"page": {"label": "Say<PERSON>"}}, "presets": {"name": "Say<PERSON>"}}, "rich-text": {"name": "<PERSON><PERSON> metin", "settings": {"full_width": {"label": "Bölümü tam genişlikli yap"}, "desktop_content_position": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "Masaüstü içerik konumu", "info": "<PERSON><PERSON>, mobil cihazlar için otomatik olarak optimize edilir."}, "content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "İçerik hizalaması"}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık"}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label_1": {"label": "<PERSON><PERSON> düğme et<PERSON>", "info": "Düğmeyi gizlemek için etiketi boş bırakın."}, "button_link_1": {"label": "İlk düğme bağlantısı"}, "button_style_secondary_1": {"label": "<PERSON><PERSON><PERSON> ç<PERSON> düğ<PERSON> stil<PERSON> kullan"}, "button_label_2": {"label": "<PERSON><PERSON><PERSON> düğ<PERSON>", "info": "Düğmeyi gizlemek için etiketi boş bırakın."}, "button_link_2": {"label": "İkinci düğme bağlantısı"}, "button_style_secondary_2": {"label": "<PERSON><PERSON><PERSON> ç<PERSON> düğ<PERSON> stil<PERSON> kullan"}}}, "caption": {"name": "Alt yazı", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_style": {"label": "<PERSON><PERSON> stili", "options__1": {"label": "Alt yazı"}, "options__2": {"label": "Büyük harf"}}, "caption_size": {"label": "<PERSON><PERSON>", "options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}}}}}, "presets": {"name": "<PERSON><PERSON> metin"}}, "apps": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"include_margins": {"label": "B<PERSON>lüm kenar boşluklarını temayla aynı yap"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "video": {"name": "Video", "settings": {"heading": {"label": "Başlık"}, "cover_image": {"label": "Kapak gö<PERSON>"}, "video_url": {"label": "URL", "info": "YouTube veya Vimeo URL'si kullan"}, "description": {"label": "Video alternatif metni", "info": "Ekran okuyucu kullanan müşteriler için videoyu açıklayın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON> e<PERSON>", "info": "Kapak görselinizin kırpılmasını istemiyorsanız görsel dolgusunu seçin."}, "full_width": {"label": "Bölümü tam genişlikli yap"}, "video": {"label": "Video"}, "enable_video_looping": {"label": "<PERSON><PERSON> döngü halinde o<PERSON>t"}, "header__1": {"content": "Shopify'da barındırılan videolar"}, "header__2": {"content": "URL'den video ekle"}, "header__3": {"content": "Stil"}, "paragraph": {"content": "Shopify'da barındırılan bir video seçilmediğinde görünür."}}, "presets": {"name": "Video"}}, "featured-product": {"name": "<PERSON><PERSON>ü<PERSON>", "blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_style": {"label": "<PERSON><PERSON> stili", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}}}, "title": {"name": "Başlık"}, "price": {"name": "<PERSON><PERSON><PERSON>"}, "quantity_selector": {"name": "<PERSON><PERSON>"}, "variant_picker": {"name": "Varyasyon <PERSON>ç<PERSON>", "settings": {"picker_type": {"label": "Stil", "options__1": {"label": "Açılır <PERSON>ü"}, "options__2": {"label": "Seçenekler"}}, "swatch_shape": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON> [numune parçaları](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) etkinleştirin.", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Yok"}}}}, "buy_buttons": {"name": "Satın al düğmeleri", "settings": {"show_dynamic_checkout": {"label": "Dinamik ödeme düğmelerini göster", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mağazanızda bulunan ödeme yöntemlerini kullanarak PayPal veya Apple Pay gibi tercih ettikleri seçeneği görür. [Daha fazla bilgi edinin](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "share": {"name": "Paylaş", "settings": {"featured_image_info": {"content": "Sosyal medya gönderilerine bağlantı eklerseniz sayfanın öne çıkan görseli, önizleme görseli olarak gösterilir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON> ve açıklaması, önizleme görseline dahildir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "<PERSON><PERSON>"}}}, "rating": {"name": "<PERSON>rün puanı", "settings": {"paragraph": {"content": "Puan göstermek için bir ürün puanlandırma uygulaması ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "<PERSON><PERSON> stili", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}}}}, "settings": {"product": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "secondary_background": {"label": "İkincil arka planı göster"}, "header": {"content": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON> hakkında daha fazla bilgi edinin: [medya tü<PERSON>](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Video döngüs<PERSON><PERSON><PERSON>ş<PERSON>r"}, "hide_variants": {"label": "Masaüstünde seçimi kaldırılmış varyasyonların medyasını gizle"}, "media_position": {"label": "Ma<PERSON>üst<PERSON> medya konumu", "info": "<PERSON><PERSON>, mobil cihazlar için otomatik olarak optimize edilir.", "options__1": {"label": "Sol"}, "options__2": {"label": "Sağ"}}}, "presets": {"name": "<PERSON><PERSON>ü<PERSON>"}}, "email-signup-banner": {"name": "E-posta kaydı banner'ı", "settings": {"paragraph": {"content": "Her e-posta aboneliği bir müşteri hesabı oluşturur. [Daha fazla bilgi edinin](https://help.shopify.com/manual/customers)"}, "image": {"label": "Arka plan resmi"}, "show_background_image": {"label": "Arka plan resmini <PERSON>"}, "show_text_box": {"label": "Masaüstünde kapsayıcıyı göster"}, "image_overlay_opacity": {"label": "Görsel yer paylaşımı opaklığı"}, "show_text_below": {"label": "Mobil cihaz üzerinde görselin altındaki içeriği göster", "info": "En iyi sonuçlar için 16:9 en-boy oran<PERSON>na sahip bir gö<PERSON>l kullanın. [<PERSON>ha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "image_height": {"label": "Banner yüksekliği", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "options__4": {"label": "Büyük"}, "info": "En iyi sonuçlar için 16:9 en-boy oran<PERSON>na sahip bir gö<PERSON>l kullanın. [<PERSON>ha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_position": {"options__1": {"label": "Üst Sol"}, "options__2": {"label": "Üst Orta"}, "options__3": {"label": "Üst Sağ"}, "options__4": {"label": "Orta Sol"}, "options__5": {"label": "Orta Kısmın <PERSON>"}, "options__6": {"label": "Orta Sağ"}, "options__7": {"label": "Alt Sol"}, "options__8": {"label": "Alt Orta"}, "options__9": {"label": "Alt Sağ"}, "label": "Masaüstü içerik konumu"}, "desktop_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "Masaüstü içerik hizalaması"}, "header": {"content": "<PERSON><PERSON>"}, "mobile_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "Mobil içerik hizalaması"}, "color_scheme": {"info": "Kapsayıcı gösterildiğinde görünür."}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık"}}}, "paragraph": {"name": "Paragra<PERSON>", "settings": {"paragraph": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "label": "<PERSON><PERSON> stili"}}}, "email_form": {"name": "E-posta formu"}}, "presets": {"name": "E-posta kaydı banner'ı"}}, "slideshow": {"name": "<PERSON><PERSON><PERSON> gö<PERSON>", "settings": {"layout": {"label": "D<PERSON>zen", "options__1": {"label": "<PERSON> g<PERSON>"}, "options__2": {"label": "Izgara"}}, "slide_height": {"label": "Slayt yüksekliği", "options__1": {"label": "İlk görsele uyarla"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "options__4": {"label": "Büyük"}}, "slider_visual": {"label": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ı<PERSON> stili", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Noktalar"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "auto_rotate": {"label": "Slaytları otomatik olarak döndür"}, "change_slides_speed": {"label": "Slaytları şu zaman aralığında değiştir:"}, "show_text_below": {"label": "Mobil cihaz üzerinde görsellerin altındaki içeriği göster"}, "mobile": {"content": "<PERSON><PERSON>"}, "accessibility": {"content": "Erişilebilirlik", "label": "Slayt gösterisi açıklaması", "info": "Ekran koruyucu kullanan müşteriler için slayt gösterisini açıklayın."}}, "blocks": {"slide": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "heading": {"label": "Başlık"}, "subheading": {"label": "Alt başlık"}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Düğmeyi gizlemek için etiketi boş bırakın."}, "link": {"label": "<PERSON><PERSON><PERSON><PERSON> bağlantısı"}, "secondary_style": {"label": "<PERSON><PERSON><PERSON> ç<PERSON> düğ<PERSON> stil<PERSON> kullan"}, "box_align": {"label": "Masaüstü içerik konumu", "options__1": {"label": "Üst sol"}, "options__2": {"label": "Üst orta"}, "options__3": {"label": "Üst sağ"}, "options__4": {"label": "Orta sol"}, "options__5": {"label": "<PERSON><PERSON> kısmın ortası"}, "options__6": {"label": "<PERSON><PERSON>"}, "options__7": {"label": "Alt sol"}, "options__8": {"label": "Alt orta"}, "options__9": {"label": "Alt sağ"}, "info": "<PERSON><PERSON>, mobil cihazlar için otomatik olarak optimize edilir."}, "show_text_box": {"label": "Masaüstünde kapsayıcıyı göster"}, "text_alignment": {"label": "Masaüstü içerik hizalaması", "option_1": {"label": "Sol"}, "option_2": {"label": "Orta"}, "option_3": {"label": "Sağ"}}, "image_overlay_opacity": {"label": "Görsel yer paylaşımı opaklığı"}, "text_alignment_mobile": {"label": "Mobil içerik hizalaması", "options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}}}}}, "presets": {"name": "<PERSON><PERSON><PERSON> gö<PERSON>"}}, "collapsible_content": {"name": "Daraltılabilir içerik", "settings": {"caption": {"label": "Alt yazı"}, "heading": {"label": "Başlık"}, "heading_alignment": {"label": "Başlık hizalaması", "options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}}, "layout": {"label": "D<PERSON>zen", "options__1": {"label": "Kapsayıcı yok"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "B<PERSON>lüm ka<PERSON>ı"}}, "container_color_scheme": {"label": "Kapsayıcı renk şeması", "info": "<PERSON><PERSON><PERSON>; satır veya böl<PERSON><PERSON> ka<PERSON>ıcısına ayarlandığında görünür."}, "open_first_collapsible_row": {"label": "İlk daraltılabilir satırı aç"}, "header": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "image": {"label": "G<PERSON><PERSON><PERSON>"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Büyük"}}, "desktop_layout": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "info": "<PERSON><PERSON><PERSON> her zaman önce görsel görünür."}}, "blocks": {"collapsible_row": {"name": "Daraltılabilir <PERSON>", "settings": {"heading": {"info": "İçeriği açıklayan bir başlık ekleyin.", "label": "Başlık"}, "row_content": {"label": "<PERSON><PERSON><PERSON>r içeriği"}, "page": {"label": "<PERSON><PERSON><PERSON>ı<PERSON> içeriği"}, "icon": {"label": "<PERSON>m<PERSON>", "options__1": {"label": "Hiç<PERSON>i"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Muz"}, "options__4": {"label": "Şişe"}, "options__5": {"label": "<PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON><PERSON> balonu"}, "options__8": {"label": "<PERSON><PERSON>"}, "options__9": {"label": "Pan<PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON> ürü<PERSON>"}, "options__11": {"label": "Süt ürünü içermez"}, "options__12": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__13": {"label": "Göz"}, "options__14": {"label": "Ateş"}, "options__15": {"label": "Glütensiz"}, "options__16": {"label": "<PERSON><PERSON><PERSON>"}, "options__17": {"label": "Ütü"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Ş<PERSON>şek"}, "options__21": {"label": "<PERSON><PERSON><PERSON>"}, "options__22": {"label": "<PERSON><PERSON>"}, "options__23": {"label": "<PERSON><PERSON> pini"}, "options__24": {"label": "Kabuklu yemişsiz"}, "options__25": {"label": "Pantolon"}, "options__26": {"label": "<PERSON><PERSON>"}, "options__27": {"label": "<PERSON><PERSON>"}, "options__28": {"label": "Parfüm"}, "options__29": {"label": "Uçak"}, "options__30": {"label": "<PERSON><PERSON>"}, "options__31": {"label": "<PERSON><PERSON><PERSON>"}, "options__32": {"label": "<PERSON><PERSON>"}, "options__33": {"label": "<PERSON><PERSON>"}, "options__34": {"label": "İade"}, "options__35": {"label": "Cetvel"}, "options__36": {"label": "<PERSON><PERSON>"}, "options__37": {"label": "Gömlek"}, "options__38": {"label": "Ayakkabı"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON>"}, "options__41": {"label": "Yıld<PERSON>z"}, "options__42": {"label": "Kronometre"}, "options__43": {"label": "<PERSON><PERSON><PERSON>"}, "options__44": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Daraltılabilir içerik"}}, "main-account": {"name": "<PERSON><PERSON><PERSON>"}, "main-activate-account": {"name": "<PERSON><PERSON><PERSON>"}, "main-addresses": {"name": "<PERSON><PERSON><PERSON>"}, "main-login": {"name": "<PERSON><PERSON><PERSON>"}, "main-order": {"name": "Sipariş"}, "main-register": {"name": "<PERSON><PERSON><PERSON>"}, "main-reset-password": {"name": "<PERSON><PERSON><PERSON>"}, "related-products": {"name": "Alakalı ürünler", "settings": {"heading": {"label": "Başlık"}, "products_to_show": {"label": "Gösterilecek maksimum ürün sayısı"}, "columns_desktop": {"label": "Masaüstündeki sütun sayısı"}, "paragraph__1": {"content": "Dinamik önerilerin zamanla değişmesi ve gelişmesi için sipariş ve ürün bilgileri kullanılır. [Daha fazla bilgi edinin](https://help.shopify.com/themes/development/recommended-products)"}, "header__2": {"content": "Ürün kartı"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "show_secondary_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}, "show_vendor": {"label": "Satıcıyı göster"}, "show_rating": {"label": "<PERSON>rün <PERSON>larını göster", "info": "Puan göstermek için bir ürün puanlandırma uygulaması ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"}, "header_mobile": {"content": "<PERSON><PERSON>"}, "columns_mobile": {"label": "Mobildeki sütun say<PERSON>ı", "options__1": {"label": "1 sütun"}, "options__2": {"label": "2 sütun"}}}}, "multirow": {"name": "Çok satırlı", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "image_height": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "options__4": {"label": "Büyük"}, "label": "Görsel yüksekliği"}, "desktop_image_width": {"options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}, "label": "Masaüstü görsel genişliği", "info": "<PERSON><PERSON><PERSON><PERSON>, mobil cihazlar için otomatik olarak optimize edilir."}, "heading_size": {"options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}, "label": "Başlık boyutu"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "label": "<PERSON><PERSON> stili"}, "button_style": {"options__1": {"label": "<PERSON>bit dü<PERSON>"}, "options__2": {"label": "<PERSON><PERSON>ş çizgi düğmes<PERSON>"}, "label": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>i"}, "desktop_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "Masaüstü içerik hizalaması"}, "desktop_content_position": {"options__1": {"label": "Üst"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Alt"}, "label": "Masaüstü içerik konumu", "info": "<PERSON><PERSON>, mobil cihazlar için otomatik olarak optimize edilir."}, "image_layout": {"options__1": {"label": "Alternatif (soldan)"}, "options__2": {"label": "Alternatif (sağdan)"}, "options__3": {"label": "Sola hizalanmış"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Masaüst<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON>, mobil cihazlar için otomatik olarak optimize edilir."}, "container_color_scheme": {"label": "Kapsayıcı renk şeması"}, "mobile_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "Mobil içerik hizalaması"}, "header_mobile": {"content": "<PERSON><PERSON>"}}, "blocks": {"row": {"name": "Satır", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "caption": {"label": "Alt yazı"}, "heading": {"label": "Başlık"}, "text": {"label": "<PERSON><PERSON>"}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "button_link": {"label": "<PERSON><PERSON><PERSON><PERSON> bağlantısı"}}}}, "presets": {"name": "Çok satırlı"}}, "quick-order-list": {"name": "Hızlı sipariş listesi", "settings": {"show_image": {"label": "Görselleri göster"}, "show_sku": {"label": "SKU'ları göster"}}, "presets": {"name": "Hızlı sipariş listesi"}}}}