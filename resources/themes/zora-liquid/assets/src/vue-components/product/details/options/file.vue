<template>
    <div>
        <label :for="labelId" class="d-block">
            {{ option.name_amount }}:
        </label>

        <div :class="'js-error-field-option-' + option.id" data-fill-on-error></div>

        <div class="custom-file">
            <input :name="'option[' + option.id + ']'" type="file" class="custom-file-input" :id="labelId + '-file'" :lang="locale" :data-amount="option.amount_input">
            <label class="custom-file-label" :for="labelId + '-file'" :data-browse="lang('global.text.browse')" :data-default="lang('global.text.chose.file')">{{ "global.text.chose.file" | t }}</label>
        </div>
    </div>
</template>

<script>
    import I18n from '../../../../js/helpers/i18n';

    export default {
        props: [
            'product_id', 'option'
        ],
        computed: {
            labelId: function() {
                return 'product-' + this.product_id + '-option-' + this.option.id;
            },
            locale: function() {
                return window.ccSettings.locale;
            },
            lang: function() {
                let trans = I18n();
                return (key, def) => trans.__(key, def);
            }
        }
    }
</script>