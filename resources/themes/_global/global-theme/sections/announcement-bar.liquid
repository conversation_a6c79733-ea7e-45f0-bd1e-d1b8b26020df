{{ 'component-slideshow.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}

{%- liquid
  assign social_icons = true
  if settings.social_facebook_link == blank and settings.social_instagram_link == blank and settings.social_youtube_link == blank and settings.social_tiktok_link == blank and settings.social_twitter_link == blank and settings.social_pinterest_link == blank and settings.social_snapchat_link == blank and settings.social_tumblr_link == blank and settings.social_vimeo_link == blank
    assign social_icons = false
  endif
  if section.settings.enable_country_selector or section.settings.enable_language_selector
    assign language_country_selector = true
  endif
  if section.blocks.size > 0
    assign announcement_bar = true
  endif
-%}

{% if social_icons %}
  {{ 'component-list-social.css' | asset_url | stylesheet_tag }}
{% endif %}

<div
  class="utility-bar color-{{ section.settings.color_scheme }} gradient{% if section.settings.show_line_separator and section.blocks.size > 0 %} utility-bar--bottom-border{% elsif section.settings.show_line_separator and section.settings.show_social and social_icons%} utility-bar--bottom-border-social-only{% endif %}{% if section.settings.enable_country_selector or section.settings.enable_language_selector %} header-localization{% endif %}"
>
  <div class="page-width utility-bar__grid{% if announcement_bar and language_country_selector or section.settings.show_social and social_icons %} utility-bar__grid--3-col{% elsif language_country_selector or section.settings.show_social and social_icons %} utility-bar__grid--2-col{% endif %}">
    {%- if section.settings.show_social and social_icons -%}
      {%- render 'social-icons' -%}
    {%- endif -%}
    {%- if section.blocks.size == 1 -%}
      <div
        class="announcement-bar{% if section.settings.show_social %} announcement-bar--one-announcement{% endif %}"
        role="region"
        aria-label="{{ 'sections.header.announcement' | t }}"
        {{ section.blocks.first.shopify_attributes }}
      >
        {%- if section.blocks.first.settings.text != blank -%}
          {%- if section.blocks.first.settings.link != blank -%}
            <a
              href="{{ section.blocks.first.settings.link }}"
              class="announcement-bar__link link link--text focus-inset animate-arrow"
            >
          {%- endif -%}
          <p class="announcement-bar__message h5">
            <span>{{ section.blocks.first.settings.text | escape }}</span>
            {%- if section.blocks.first.settings.link != blank -%}
              {{- 'icon-arrow.svg' | inline_asset_content -}}
            {%- endif -%}
          </p>
          {%- if section.blocks.first.settings.link != blank -%}
            </a>
          {%- endif -%}
        {%- endif -%}
      </div>
    {%- elsif section.blocks.size > 1 -%}
      <slideshow-component
        class="announcement-bar"
        role="region"
        aria-roledescription="{{ 'sections.announcements.carousel' | t }}"
        aria-label="{{ 'sections.announcements.announcement_bar' | t }}"
      >
        <div class="announcement-bar-slider slider-buttons">
          <button
            type="button"
            class="slider-button slider-button--prev"
            name="previous"
            aria-label="{{ 'sections.announcements.previous_announcement' | t }}"
            aria-controls="Slider-{{ section.id }}"
          >
            <span class="svg-wrapper">
              {{- 'icon-caret.svg' | inline_asset_content -}}
            </span>
          </button>
          <div
            class="grid grid--1-col slider slider--everywhere"
            id="Slider-{{ section.id }}"
            aria-live="polite"
            aria-atomic="true"
            data-autoplay="{{ section.settings.auto_rotate }}"
            data-speed="{{ section.settings.change_slides_speed }}"
          >
            {%- for block in section.blocks -%}
              <div
                class="slideshow__slide slider__slide grid__item grid--1-col"
                id="Slide-{{ section.id }}-{{ forloop.index }}"
                {{ block.shopify_attributes }}
                role="group"
                aria-roledescription="{{ 'sections.announcements.announcement' | t }}"
                aria-label="{{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
                tabindex="-1"
              >
                <div
                  class="announcement-bar__announcement"
                  role="region"
                  aria-label="{{ 'sections.header.announcement' | t }}"
                >
                  {%- if block.settings.text != blank -%}
                    {%- if block.settings.link != blank -%}
                      <a
                        href="{{ block.settings.link }}"
                        class="announcement-bar__link link link--text focus-inset animate-arrow"
                      >
                    {%- endif -%}
                    <p class="announcement-bar__message h5">
                      <span>{{ block.settings.text | escape }}</span>
                      {%- if block.settings.link != blank -%}
                        {{- 'icon-arrow.svg' | inline_asset_content -}}
                      {%- endif -%}
                    </p>
                    {%- if block.settings.link != blank -%}
                      </a>
                    {%- endif -%}
                  {%- endif -%}
                </div>
              </div>
            {%- endfor -%}
          </div>
          <button
            type="button"
            class="slider-button slider-button--next"
            name="next"
            aria-label="{{ 'sections.announcements.next_announcement' | t }}"
            aria-controls="Slider-{{ section.id }}"
          >
            <span class="svg-wrapper">
              {{- 'icon-caret.svg' | inline_asset_content -}}
            </span>
          </button>
        </div>
      </slideshow-component>
      {%- if request.design_mode -%}
        <script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
        <style>
          /* theme editor power preview fix */
          .announcement-bar-slider .slider__slide[aria-hidden='true'] {
            visibility: hidden;
          }
        </style>
      {%- endif -%}
    {%- endif -%}
    <div class="localization-wrapper">
      {%- if section.settings.enable_country_selector and localization.available_countries.size > 1 -%}
        <localization-form class="small-hide medium-hide">
          {%- form 'localization', id: 'AnnouncementCountryForm', class: 'localization-form' -%}
            <div>
              <h2 class="visually-hidden" id="AnnouncementCountryLabel">{{ 'localization.country_label' | t }}</h2>
              {%- render 'country-localization', localPosition: 'AnnouncementCountry' -%}
            </div>
          {%- endform -%}
        </localization-form>
      {% endif %}

      {%- if section.settings.enable_language_selector and localization.available_languages.size > 1 -%}
        <localization-form class="small-hide medium-hide">
          {%- form 'localization', id: 'AnnouncementLanguageForm', class: 'localization-form' -%}
            <div>
              <h2 class="visually-hidden" id="AnnouncementLanguageLabel">{{ 'localization.language_label' | t }}</h2>
              {%- render 'language-localization', localPosition: 'AnnouncementLanguage' -%}
            </div>
          {%- endform -%}
        </localization-form>
      {%- endif -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.announcement-bar.name",
  "max_blocks": 12,
  "class": "announcement-bar-section",
  "enabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "auto_rotate",
      "label": "t:sections.announcement-bar.settings.auto_rotate.label",
      "default": false
    },
    {
      "type": "range",
      "id": "change_slides_speed",
      "min": 3,
      "max": 10,
      "step": 1,
      "unit": "s",
      "label": "t:sections.announcement-bar.settings.change_slides_speed.label",
      "default": 5
    },    
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-4"
    },  
    {
      "type": "checkbox",
      "id": "show_line_separator",
      "default": true,
      "label": "t:sections.header.settings.show_line_separator.label"
    },
    {
      "type": "header",
      "content": "t:sections.announcement-bar.settings.heading_utilities.content"
    },
    {
      "type": "paragraph",
         "content": "t:sections.announcement-bar.settings.paragraph.content"
    },             
    {
      "type": "checkbox",
      "id": "show_social",
      "default": false,
      "label": "t:sections.announcement-bar.settings.show_social.label",
      "info": "t:sections.announcement-bar.settings.show_social.info"
    },
    {
      "type": "checkbox",
      "id": "enable_country_selector",
      "default": false,
      "label": "t:sections.announcement-bar.settings.enable_country_selector.label",
      "info": "t:sections.announcement-bar.settings.enable_country_selector.info"
    },
    {
      "type": "checkbox",
      "id": "enable_language_selector",
      "default": false,
      "label": "t:sections.announcement-bar.settings.enable_language_selector.label",
      "info": "t:sections.announcement-bar.settings.enable_language_selector.info"
    }
  ],
  "blocks": [
    {
      "type": "announcement",
      "name": "t:sections.announcement-bar.blocks.announcement.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "default": "t:sections.announcement-bar.blocks.announcement.settings.text.default",
          "label": "t:sections.announcement-bar.blocks.announcement.settings.text.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.announcement-bar.blocks.announcement.settings.link.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.announcement-bar.presets.name",
      "blocks": [
        {
          "type": "announcement"
        }
      ]
    }
  ]
}
{% endschema %}
