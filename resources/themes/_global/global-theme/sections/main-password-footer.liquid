{% assign scheme1 = settings.color_schemes | first %}
{%- if section.settings.color_scheme == scheme1 -%}<hr>{%- endif -%}
<div class="password__footer color-{{ section.settings.color_scheme }} gradient">
  <ul class="list-social list-unstyled" role="list">
    {%- if settings.social_twitter_link != blank -%}
      <li class="list-social__item">
        <a href="{{ settings.social_twitter_link }}" class="link list-social__link">
          <span class="svg-wrapper">
            {{- 'icon-twitter.svg' | inline_asset_content -}}
          </span>
          <span class="visually-hidden">{{ 'general.social.links.twitter' | t }}</span>
        </a>
      </li>
    {%- endif -%}
    {%- if settings.social_facebook_link != blank -%}
      <li class="list-social__item">
        <a href="{{ settings.social_facebook_link }}" class="link list-social__link">
          <span class="svg-wrapper">
            {{- 'icon-facebook.svg' | inline_asset_content -}}
          </span>
          <span class="visually-hidden">{{ 'general.social.links.facebook' | t }}</span>
        </a>
      </li>
    {%- endif -%}
    {%- if settings.social_pinterest_link != blank -%}
      <li class="list-social__item">
        <a href="{{ settings.social_pinterest_link }}" class="link list-social__link">
          <span class="svg-wrapper">
            {{- 'icon-pinterest.svg' | inline_asset_content -}}
          </span>
          <span class="visually-hidden">{{ 'general.social.links.pinterest' | t }}</span>
        </a>
      </li>
    {%- endif -%}
    {%- if settings.social_instagram_link != blank -%}
      <li class="list-social__item">
        <a href="{{ settings.social_instagram_link }}" class="link list-social__link">
          <span class="svg-wrapper">
            {{- 'icon-instagram.svg' | inline_asset_content -}}
          </span>
          <span class="visually-hidden">{{ 'general.social.links.instagram' | t }}</span>
        </a>
      </li>
    {%- endif -%}
    {%- if settings.social_tiktok_link != blank -%}
      <li class="list-social__item">
        <a href="{{ settings.social_tiktok_link }}" class="link list-social__link">
          <span class="svg-wrapper">
            {{- 'icon-tiktok.svg' | inline_asset_content -}}
          </span>
          <span class="visually-hidden">{{ 'general.social.links.tiktok' | t }}</span>
        </a>
      </li>
    {%- endif -%}
    {%- if settings.social_tumblr_link != blank -%}
      <li class="list-social__item">
        <a href="{{ settings.social_tumblr_link }}" class="link list-social__link">
          <span class="svg-wrapper">
            {{- 'icon-tumblr.svg' | inline_asset_content -}}
          </span>
          <span class="visually-hidden">{{ 'general.social.links.tumblr' | t }}</span>
        </a>
      </li>
    {%- endif -%}
    {%- if settings.social_snapchat_link != blank -%}
      <li class="list-social__item">
        <a href="{{ settings.social_snapchat_link }}" class="link list-social__link">
          <span class="svg-wrapper">
            {{- 'icon-snapchat.svg' | inline_asset_content -}}
          </span>
          <span class="visually-hidden">{{ 'general.social.links.snapchat' | t }}</span>
        </a>
      </li>
    {%- endif -%}
    {%- if settings.social_youtube_link != blank -%}
      <li class="list-social__item">
        <a href="{{ settings.social_youtube_link }}" class="link list-social__link">
          <span class="svg-wrapper">
            {{- 'icon-youtube.svg' | inline_asset_content -}}
          </span>
          <span class="visually-hidden">{{ 'general.social.links.youtube' | t }}</span>
        </a>
      </li>
    {%- endif -%}
    {%- if settings.social_vimeo_link != blank -%}
      <li class="list-social__item">
        <a href="{{ settings.social_vimeo_link }}" class="link list-social__link">
          <span class="svg-wrapper">
            {{- 'icon-vimeo.svg' | inline_asset_content -}}
          </span>
          <span class="visually-hidden">{{ 'general.social.links.vimeo' | t }}</span>
        </a>
      </li>
    {%- endif -%}
  </ul>
  {% capture shopify %}
    <a
      class="shopify-link"
      href="//www.shopify.com"
      rel="nofollow"
      target="_blank"
      aria-describedby="a11y-new-window-message"
      aria-label="Shopify"
    >
      {{ 'icon-shopify.svg' | inline_asset_content }}
    </a>
  {% endcapture %}
  <small class="password__footer-caption password__footer-text">
    {{- 'general.password_page.powered_by_shopify_html' | t: shopify: shopify -}}
  </small>
  <small class="password__footer-login password__footer-text">{{ 'general.password_page.admin_link_html' | t }}</small>
</div>

{% schema %}
{
  "name": "t:sections.main-password-footer.name",
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    }
  ]
}
{% endschema %}
