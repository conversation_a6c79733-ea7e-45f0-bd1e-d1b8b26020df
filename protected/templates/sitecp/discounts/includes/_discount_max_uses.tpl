<div class="box js-box-max-usage-holder box-discount-max-uses">
    <div class="box-title">
        <div class="box-title-text">
            <h5>{t}discount.label.max_uses{/t}</h5>
            <p>{__('discount.help.max_uses', ['statuses' => implode(', ', App\Models\Discount\Discount::getUsedStatuses())])}</p>
        </div>
    </div>

    <div class="box-section">
        <div class="input-group">
            <span class="input-group-addon input-group-addon-left">
                <label class="checkbox-inline">
                    <input type="checkbox" value="on" class="checked js-status-box-triggered" name="{inputPrefix('unlimited', $inputPrefix|default)}"{if !isset($discount->max_uses)} checked="checked"{/if} />
                    {t}discount.addon.unlimited{/t}
                </label>
            </span>

            <input type="text" name="{inputPrefix('max_uses', $inputPrefix|default)}" class="form-control"{if !isset($discount->max_uses)} disabled="disabled"{else} value="{$discount->max_uses}"{/if} placeholder="{t}discount.ph.max_uses{/t}"/>
        </div>
    </div>
</div>

{capture append="js"}
<script type="text/javascript">
    (function() {
        let noCheck = {if $noCheck|default}true{else}false{/if};
        let holder = $('.js-box-max-usage-holder');

        setTimeout(() => {
            if (holder.find('input[name="{inputPrefix('unlimited', $inputPrefix|default)}"]').is(':checked')) {
                holder.find('input[name="{inputPrefix('max_uses', $inputPrefix|default)}"]').val('').prop('disabled', true);
            } else {
                holder.find('input[name="{inputPrefix('max_uses', $inputPrefix|default)}"]').prop('disabled', false);
            }
        }, 10);

        holder.on('change', 'input[name="{inputPrefix('unlimited', $inputPrefix|default)}"]', function (e) {
            if (!noCheck) e.preventDefault();
            let that = $(this);
            if (that.is(':checked')) {
                holder.find('input[name="{inputPrefix('max_uses', $inputPrefix|default)}"]').val('').prop('disabled', true);
            } else {
                holder.find('input[name="{inputPrefix('max_uses', $inputPrefix|default)}"]').prop('disabled', false);
            }
        });
    })();
</script>
{/capture}