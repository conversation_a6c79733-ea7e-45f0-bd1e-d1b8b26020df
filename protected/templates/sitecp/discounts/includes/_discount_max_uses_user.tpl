<div class="box js-box-max-usage-user-holder box-discount-max-uses-user">
    <div class="box-title">
        <div class="box-title-text">
            <h5>{t}discount.setting.user_limit{/t}</h5>
            <p>{__('discount.setting.help.user_limit', ['statuses' => implode(', ', App\Models\Discount\Discount::getUsedStatuses())])}</p>
        </div>
    </div>

    <div class="box-section">
        <div class="input-group">
            <span class="input-group-addon input-group-addon-left">
                <label class="checkbox-inline">
                    <input type="checkbox" class="checked" value="on"
                           name="unlimited_user"{if !isset($discount->maxused_user)} checked="checked"{/if} />
                    {t}discount.addon.unlimited{/t}
                </label>
            </span>

            <input type="text" name="maxused_user"
                   class="form-control"{if !isset($discount->maxused_user)} disabled="disabled"{else} value="{$discount->maxused_user}"{/if}
                   placeholder="{t}discount.ph.max_uses{/t}"/>
        </div>
    </div>
</div>

{capture append="js"}
    <script type="text/javascript">
        let noCheck = {if $noCheck|default}true{else}false{/if};

        var type = $('select.js-target-settings').val();
        if(!['all','order_over'].includes(type)) {
            $('.box-discount-max-uses, .box-discount-max-uses-user').hide();
        }
        $('select.js-target-settings').on('change', function() {
            var type = $(this).val();
            if(noCheck) {
                $('.box-discount-max-uses, .box-discount-max-uses-user').show();
            }

            if(['all','order_over'].includes(type) || noCheck) {
                $('.box-discount-max-uses, .box-discount-max-uses-user').show();
            } else {
                $('.box-discount-max-uses, .box-discount-max-uses-user').hide();
            }
        });
        (function () {
            let holder = $('.js-box-max-usage-user-holder');

            setTimeout(() => {
                if (holder.find('input[name="unlimited_user"]').is(':checked')) {
                    holder.find('input[name="maxused_user"]').val('').prop('disabled', true);
                } else {
                    holder.find('input[name="maxused_user"]').prop('disabled', false);
                }
            }, 10);

            holder.on('change', 'input[name="unlimited_user"]', function (e) {
                if (!noCheck) e.preventDefault();
                let that = $(this);
                if (that.is(':checked')) {
                    holder.find('input[name="maxused_user"]').val('').prop('disabled', true);
                } else {
                    holder.find('input[name="maxused_user"]').prop('disabled', false);
                }
            });
            
            setTimeout(() => {
                if(noCheck) {
                    $('.box-discount-max-uses, .box-discount-max-uses-user').show();
                }
            }, 60);
        })();
    </script>
{/capture}
