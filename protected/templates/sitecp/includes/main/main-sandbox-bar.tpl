<div id="onboarding-progress-bar" class="hidden">
    <div class="left-side">
        <i onclick="handleToggle()" class="fa-light fa-rocket-launch launch"></i>

        <div class="content-left">
            <div class="title">
                <span class="label-400-white">
                    {t}plan.setup_guide{/t}
                </span>
                <span class="label-400-white" id="steps-count">
                </span>
            </div>
            <div class="progress-bar" id="progress-bar-fill">
            </div>
        </div>
    </div>
    <div class="right-side">
        <a href="/admin" class="btn btn-ghost btn-small">
            {t}plan.go_to_setup_guide{/t}
        </a>
    </div>
</div>
<script>
    window.addEventListener('load', handleMobileVersion);
    window.addEventListener('resize', handleMobileVersion);

    function handleMobileVersion() {
        if (window.outerWidth <= 768) {
            document.getElementById('onboarding-progress-bar').classList.add('mobile');
            document.querySelector('.content-left').classList.add('hidden');
            document.querySelector('.right-side').classList.add('hidden');
        } else {
            document.getElementById('onboarding-progress-bar').classList.remove('mobile');
            document.querySelector('.content-left').classList.remove('hidden');
            document.querySelector('.right-side').classList.remove('hidden');
        }
    }

    function handleToggle() {
        if (window.outerWidth <= 768) {
            document.querySelector('.content-left').classList.toggle('hidden');
            document.querySelector('.right-side').classList.toggle('hidden');
        }
    };

    async function getProgress() {
        let onboarding_progress_complete = localStorage.getItem('onboarding_progress_complete');
        if (onboarding_progress_complete) {
            return;
        }
        try {
            const req = await fetch('/admin/api/core/dashboard/onboarding-progress', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const response = await req.json();
            let totalSteps = Object.keys(response).length;
            let completedSteps = 0;
            if (!response?.onboarding_progress_complete) {
                Object.keys(response).forEach((key) => {
                    if (response[key] == true) {
                        completedSteps++;
                    } else if (typeof response[key] == 'object') {
                        let totalSubStepsCompleted = Object.values(response[key])
                            .filter((value) => value == true).length;
                        if (totalSubStepsCompleted == Object.values(response[key]).length) {
                            completedSteps++;
                        }
                    }
                });
                const stepsCount = document.getElementById('steps-count');

                if (stepsCount) {
                    stepsCount.textContent = completedSteps.toString() + '/' + totalSteps.toString();
                }
                const progressBarFill = document.getElementById('progress-bar-fill');
                if (progressBarFill) {
                    const stepsPercentage = (completedSteps / totalSteps) * 100;
                    progressBarFill.style.setProperty('--progress-width', stepsPercentage + '%');
                }
                if (completedSteps < totalSteps) {
                    document.getElementById('onboarding-progress-bar').classList.remove('hidden');
                }
            } else {
                localStorage.setItem('onboarding_progress_complete', '1');
            }
        } catch (error) {
            console.log(error)
        }

    }

    getProgress();
</script>
<style type="text/css">
    /* NEW STYLES */
    #onboarding-progress-bar {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        padding: var(--Spacing---cc-space-sm, 12px);
        border-radius: var(--Border-radius---cc-border-radius-md, 8px);
        box-shadow: 0px 4px 14px 0px rgba(52, 60, 90, 0.25),
            0px 0px 1px 0px rgba(52, 60, 90, 0.15);

        display: flex;
        align-items: center;
        justify-content: space-between;

        gap: 16px;
        min-width: 300px;
        z-index: 1000;

        transition: all 0.2s ease-in-out;
    }

    #onboarding-progress-bar::before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(61, 65, 77, 0.75);
        border-radius: var(--Border-radius---cc-border-radius-md, 8px);
        backdrop-filter: blur(4px);
        z-index: -1;
        border-radius: var(--Border-radius---cc-border-radius-md, 8px);
    }

    #onboarding-progress-bar.hidden {
        display: none;
    }

    .left-side {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .launch {
        color: #fff;
        text-align: center;
        font-size: 20px;
        font-weight: 300;
        animation: launch 0.5s infinite;
    }

    .content-left {
        min-width: 135px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
    }

    .title {
        display: flex;
        justify-content: space-between;
        gap: 16px;
        align-items: center;

    }

    .title .label-400-white {
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        color: #fff;
    }

    .content-left .progress-bar {
        width: 100%;
        height: 6px;
        border-radius: 6px;
        position: relative;
        background: #FFF;
        position: relative;
        overflow: hidden;
        transition: all 0.2s ease-in-out;
    }

    .content-left .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: var(--progress-width, 0%);
        height: 100%;
        background: #25CF8B;
        transition: all 0.2s ease-in-out;
    }

    .steps {
        transition: all 0.2s ease-in-out;
    }

    .right-side {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .right-side .btn.btn-ghost.btn-small {
        color: #3D414D;
        font-size: 14px;
        padding: 8px;
        line-height: 14px;
        background: #FFF;
        border: 1px solid #D6D9E9;
        font-weight: 500;
        font-family: 'Roboto';
        transition: all 0.2s ease-in-out;
    }

    .right-side .btn.btn-ghost.btn-small:hover {
        background: #e7e7e7;
    }

    #onboarding-progress-bar.mobile {
        right: 0;
        left: auto;
        transform: translateX(0);
        border-radius: 8px 0px 0px 8px;
        min-width: fit-content;
        min-height: 56px;
        transition: all 0.2s ease-in-out;
    }

    #onboarding-progress-bar.mobile::before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(61, 65, 77, 0.75);
        backdrop-filter: blur(4px);
        z-index: -1;
        border-radius: 8px 0px 0px 8px;
    }

    #onboarding-progress-bar.mobile .content-left.hidden,
    #onboarding-progress-bar.mobile .right-side.hidden {
        max-width: 0px;
        overflow: hidden;
    }

    #onboarding-progress-bar.mobile .content-left:not(.hidden),
    #onboarding-progress-bar.mobile .right-side:not(.hidden) {
        min-width: fit-content;
        overflow: visible;
    }

    @keyframes launch {
        0% {
            transform: rotate(3deg);
        }

        50% {
            transform: rotate(-3deg);
        }

        100% {
            transform: rotate(3deg);
        }
    }
</style>


{if false}
    {* {if site('sand_box')} *}
        <div class="promo-bar">
            <div class="container">
                <div class="promo-bar-inner">
                    <div class="promo-bar-text">
                        <p>{t}plan.sandbox_bar{/t}</p>
                    </div>

                    <div class="promo-bar-button">
                        {*<a href="#" class="btn btn-default"*}
                        {*data-ajax="{route('admin.setting.go-live')}"*}
                        {*data-confirm="{t}plan.confirm.go_live{/t}"*}
                        {*>*}
                        {*{t}plan.go_live{/t}*}
                        {*</a>*}

                        {* <a href="javascript:;" class="btn btn-default" data-modal-ajax="{route('admin.setting.confirm-live')}"
                        data-modal-size="medium">
                        {t}plan.go_live{/t}
                    </a> *}
                    </div>
                </div>
            </div>
        </div>

        <style type="text/css">
            .promo-bar {
                background-color: #f65f5e;
                padding: 7px 0;
                position: fixed;
                bottom: 0;
                /*left: 220px;*/
                left: 0;
                width: 100%;
                z-index: 9999;
                color: #fff;
                font-size: 16px;
                text-align: center;
            }

            .promo-bar-inner {
                display: inline-table;
                vertical-align: middle;
            }

            .promo-bar-text,
            .promo-bar-button {
                display: table-cell;
                vertical-align: middle;
            }

            .promo-bar-text {
                padding-right: 20px;
            }

            .promo-bar-text p {
                margin: 0;
            }

            .promo-bar-text a {
                color: #fff;
                text-decoration: underline !important;
            }

            .promo-bar-text a:hover {
                text-decoration: none !important;
            }

            .promo-bar-button {
                width: 1px;
                white-space: nowrap;
            }

            .promo-bar-button .btn {
                line-height: 26px;
                background-color: #432f74;
                border-color: #432f74;
            }

            .promo-bar-button .btn:hover {
                line-height: 26px;
                background-color: #62499c;
                border-color: #62499c;
            }

            .sitecp .page-container,
            .page-sidebar,
            .side-panel-stretch,
            .side-panel-right {
                padding-bottom: 43px;
            }

            .side-panel-open .promo-bar,
            .modal-open .promo-bar {
                z-index: 999;
            }

            @media screen and (max-width: 1200px) {
                .promo-bar {
                    /*left: 101px;*/
                }

                .page-sidebar {
                    padding-bottom: 43px;
                }
            }
        </style>
    {/if}