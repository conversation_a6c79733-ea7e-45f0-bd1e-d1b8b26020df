<template>
    <div v-if="providerData.provider">
        <b-row v-if="!withoutCredentials">
            <SettingDescription v-model="boxes.Credentials" :box-key="providerKey" />
            <b-col class="col-12 col-md-7">
                <b-card>
                    <div class="d-flex align-items-center justify-content-between">
                        <span class="label-500" v-html="boxes.Credentials.title"></span>
                        <a
                            href="#"
                            @click.prevent="credentialsSlide = !credentialsSlide"
                            class="edit-shipping-btn"
                        >
                            <i v-if="credentialsSlide" class="fal fa-times"></i>
                            <i v-else class="fal fa-pen"></i>
                        </a>
                    </div>
                    <Vue3SlideUpDown v-model="credentialsSlide" :duration="200">
                        <slot name="credentials">
                            <UsernamePasswordCredentials
                                v-model:credentials="providerData.settings"
                                v-model:isCredentialsChanged="isCredentialsChanged"
                                :error="credentialError"
                                :provider-key="providerKey"
                            ></UsernamePasswordCredentials>
                        </slot>

                        <div v-if="$slots.credentialsAction" class="d-flex justify-content-end">
                            <slot name="credentialsAction"></slot>
                        </div>
                        <b-row
                            class="justify-content-center mt-3"
                            v-if="!isValidCredentials"
                        >
                            <b-col class="col-12 d-flex justify-content-end">
                                <button
                                    @click.prevent="submitCredentials()"
                                    :disabled="submitLoader"
                                    class="btn btn-primary"
                                >
                                    <b-spinner small v-if="submitLoader"></b-spinner>
                                    {{ translations["Connect"] }}
                                </button>
                            </b-col>
                        </b-row>
                    </Vue3SlideUpDown>
                </b-card>
            </b-col>
        </b-row>
        <Vue3SlideUpDown :model-value="isValidCredentials" :duration="200">
            <div>
                <hr v-if="!withoutCredentials" class="hr-separator" />
                <b-row>
                    <SettingDescription
                        v-model="boxes.Visualization"
                        :box-key="providerKey"
                    />

                    <b-col class="col-12 col-md-7">
                        <!-- NAME AND LOGO -->
                        <slot name="courierLogoSection">
                            <CourierLogoSection
                                v-model:name="providerData.provider.name"
                                v-model:image="providerData.provider.image"
                                v-model:has_image="providerData.provider.has_image"
                                :default-image="app.icon"
                                :provider-key="providerKey"
                                :provider-id="providerData.provider?.id"
                                :no-image="providerData.info.image"
                                :response-errors="responseErrors"
                            />
                        </slot>
                    </b-col>
                </b-row>
            </div>

            <SettingRow
                v-if="$slots.senderData"
                v-model="boxes.pickup"
                :box-key="providerKey"
                :response-errors="responseErrors"
                :settings-keys="pickupKeys.settingsKeys"
                :app="app"
                v-model:status-open="boxOpen"
                v-model:settings="providerData"
            >
                <template #preview_settings>
                    <PickupData
                        v-model:provider="providerData.settings"
                        :keys="pickupKeys"
                        :meta="config.meta"
                        :inputs="config.inputs"
                    />
                </template>
                <template #settings_content="{ slotData }">
                    <slot
                        name="senderData"
                        v-if="slotData"
                        :data="slotData"
                        @update:providerData="val => providerData = val"
                    ></slot>
                </template>
            </SettingRow>

            <!-- SЕRVICES -->
            <b-container
                v-if="providerData.supports.services && providerKey !== 'sameday'"
                class="container-medium"
            >
                <hr style="margin: 24px 0" />
                <b-row>
                    <SettingDescription v-model="boxes.Service" :box-key="providerKey" />
                    <b-col class="col-12 col-md-7">
                        <b-card>
                            <h5
                                class="label-500 d-block mb-2"
                                v-html="boxes.Service.title"
                            ></h5>
                            <SelectWithAjax
                                v-model:val="providerData.settings.allowed_methods"
                                :options="providerData.inputs.services"
                                mode="tags"
                                select-classes="mt-2"
                                :cols-width="12"
                                :error="responseErrors[`${providerKey}.allowed_methods`]"
                            />
                        </b-card>
                    </b-col>
                </b-row>
            </b-container>

            <div v-if="providerData.deliveryType[0]">
                <hr class="hr-separator" />
                <b-row>
                    <SettingDescription v-model="boxes.Services" :box-key="providerKey" />

                    <b-col
                        style="gap: 16px"
                        class="col-12 col-md-7 d-flex flex-column gap-3"
                    >
                        <b-card
                            v-for="(type, index) in providerData.deliveryType || []"
                            :key="index"
                        >
                            <div
                                class="d-flex flex-row flex-nowrap align-items-center justify-content-between"
                            >
                                <div class="d-flex gap-2 align-items-baseline">
                                    <span
                                        class="label-500"
                                        v-html="
                                            $trp(translations['Service type to {type}'], {
                                                type: translations[type] || type,
                                            })
                                        "
                                    ></span>
                                    <StatusBadge
                                        v-model:currentStatus="
                                            providerData.settings[`to_${type}`]
                                        "
                                    />
                                </div>
                                <a
                                    href="#"
                                    @click.prevent="() => openCalculator(type)"
                                    class="edit-shipping-btn"
                                >
                                    <i class="fal fa-pen"></i>
                                </a>
                            </div>
                        </b-card>
                    </b-col>

                    <SettingsModal
                        size="xll"
                        :sectionTitle="
                            $trp(translations['Service type to {type}'], {
                                type: translations[calcType] || calcType,
                            })
                        "
                        :value="'calculator'"
                        v-model:modal="modals"
                        :hide-card="true"
                        :settings-keys="calcKeys"
                        :response-errors="responseErrors"
                        @toggle-modal="toggleOpen"
                    >
                        <template #setting>
                            <AddressAndOfficeCalculatorSection
                                v-if="savedData"
                                v-model:provider-data="savedData"
                                :provider-key="providerKey"
                                :pricing-options="pricing_options"
                                :key="`services_${calcType}`"
                                :delivery-type="calcType"
                                :delivery-options="providerData.deliveryType"
                                :services="providerData.inputs.services || []"
                                :availableCountries="
                                    providerData.available_countries || {}
                                "
                                :response-errors="responseErrors"
                                :categories="providerData.inputs.categories"
                            />
                        </template>
                    </SettingsModal>
                </b-row>
            </div>

            <!-- GEO ZONES -->
            <b-container class="container-medium" v-if="providerKey !== 'glovo'">
                <hr style="margin: 24px 0" />
                <b-row>
                    <SettingDescription
                        v-model="boxes.geo_zones"
                        :box-key="providerKey"
                    />
                    <b-col class="col-12 col-md-7">
                        <b-card>
                            <h5
                                class="label-500 d-block mb-2"
                                v-html="boxes.geo_zones.title"
                            ></h5>

                            <GeoZonesSection
                                v-model:provider="providerData.provider"
                                v-model:zones="zones"
                                :response-errors="responseErrors"
                                :provider-key="providerKey"
                            />
                        </b-card>
                    </b-col>
                </b-row>
            </b-container>

            <!-- PAYMENT PROVIDERS -->
            <b-container class="container-medium">
                <hr style="margin: 24px 0" />
                <b-row>
                    <SettingDescription v-model="boxes.payments" :box-key="providerKey" />
                    <b-col class="col-12 col-md-7">
                        <b-card>
                            <h5
                                class="label-500 d-block mb-2"
                                v-html="boxes.payments.title"
                            ></h5>
                            <PaymentProvidersSection
                                :settings="providerData"
                                v-model:payments="providerData.selectedPayments"
                                :providers="providerData.inputs?.payments || []"
                                :response-errors="responseErrors"
                                :provider-key="providerKey"
                            />
                        </b-card>
                    </b-col>
                </b-row>
            </b-container>

            <!-- SHIPPING AND RECEIVING SETTINGS -->
            <SettingsBox
                v-model:boxes="providerData.additionalSettings"
                v-model:settings="providerData.settings"
                :box-key="providerKey"
                :selects="providerData.inputs || []"
                :response-errors="responseErrors"
                v-model:setting-open="boxOpen"
                :app="app"
            />

            <!-- PALLET -->
            <SettingRow
                v-if="$slots.pallet"
                v-model="boxes.pallet"
                :box-key="providerKey"
                :settings-keys="[
                    'pallet_lenght',
                    'pallet_height',
                    'pallet_width',
                    'pallet_categories',
                ]"
                :response-errors="responseErrors"
                @toggle-open="toggleOpen"
                v-model:status-open="boxOpen"
                v-model:settings="providerData"
            >
                <template #settings_content="{ slotData }">
                    <slot v-if="slotData" name="pallet" :pallet="slotData"></slot>
                </template>
            </SettingRow>
            <slot name="others"></slot>
        </Vue3SlideUpDown>
    </div>
</template>

<script>
import axios from "axios";
import { Vue3SlideUpDown } from "vue3-slide-up-down";

import SettingsModal from "@components/Modals/SettingsModal";
import StatusBadge from "@components/Row/Helpers/StatusBadge";
import SettingDescription from "@components/SettingDescription";
import SettingModalRow from "@components/Row/SettingModalRow";
import SettingsSlideRow from "@components/Row/SettingsSlideRow";
import SettingRow from "@components/Row/SettingRow";

import UsernamePasswordCredentials from "./Helpers/UsernamePasswordCredentials";
import CourierLogoSection from "./Helpers/CourierLogoSection";
import PaymentProvidersSection from "./Helpers/PaymentProvidersSection";
import GeoZonesSection from "./Helpers/GeoZonesSection";
import AddressAndOfficeCalculatorSection from "./Helpers/AddressAndOfficeCalculatorSection";
import PickupData from "./Helpers/PickupData";

import SelectWithAjax from "@components/Form/SelectWithAjax";
import SettingsBox from "@components/SettingsBox/SettingsBox";

export default {
    name: "SettingsFormShippings",
    components: {
        Vue3SlideUpDown,
        GeoZonesSection,
        PaymentProvidersSection,
        UsernamePasswordCredentials,
        CourierLogoSection,
        AddressAndOfficeCalculatorSection,
        SettingsModal,
        StatusBadge,
        SettingDescription,
        PickupData,
        SettingModalRow,
        SettingsSlideRow,
        SettingRow,
        SelectWithAjax,
        SettingsBox,
    },
    props: {
        app: {
            required: true,
            default: {},
            type: Object,
        },
        providerKey: {
            type: String,
            required: true,
        },
        config: {
            type: Object,
            default: {},
            required: true,
        },
        pickupKeys: {
            type: Object,
            requierd: false,
            default: {
                //street num,street name, code, city
                firstLine: [],
                // firm, sender name
                secondLine: [],
                // phone and ect
                thirdLine: [],
                settigsKeys: [],
            },
        },
        credentialsKeys: {
            required: false,
            type: Array,
            default: ['username', 'password'],
        },
        // credFields: {
        //     required: false,
        //     type: Array,
        //     default: [],
        // },
        error: {
            required: false,
            default: false,
            type: Boolean,
        },
        shops: {
            required: false,
            default: {
                isSet: 1,
                shops: [],
            },
        },
        isAllContentLoaded: {
            required: false,
            default: false,
            type: Boolean,
        },
        isValid: {
            requierd: true,
            default: false,
            type: Boolean,
        },
        isCredentialsChangedCourier: {
            default: false,
            type: Boolean,
            required: false,
        },
        responseErrors: {
            required: true,
            default: {},
            type: Object,
        },
        withoutCredentials: {
            default: false,
            type: Boolean,
            required: false,
        },
        credentialsError: {
            required: false,
            default: {},
        },
        preventSave: {
            required: true,
            type: Boolean,
        },
    },
    data() {
        return {
            providerData: this.config,
            calcType: "",
            submitLoader: false,
            credentialError: false,
            savedData: {},
            prevData: {},

            boxOpen: false,

            isSetBoxes: false,
            zones: [],
            modals: {
                sender_data: false,
                services: false,
                geo_zones: false,
                payments: false,
                settings: false,
                additional_settings: false,
                address: false,
                office: false,
                locker: false,
                pallet: false,
                calculator: false,
            },
            isCredentialsChanged: false,
            credentialsSlide: false,
            // credentialsFields: this.credentialsKeys,
            isValidAfterSubmit: false,
            translations: {
                Apps: this.$t("Apps"),
                "User credentials": this.$t("User credentials"),
                "Username {username}": this.$t("Username {username}"),
                Connect: this.$t("Connect"),
                "Ships to ": this.$t("Ships to "),
                service: this.$t("service"),
                Services: this.$t("Services"),
                "{length} selected": this.$t("{length} selected"),
                "Service type to {type}": this.$t("Service type to {type}"),
                "All regions": this.$t("All regions"),
                "All payment's": this.$t("All payment's"),
                methods: this.$t("methods"),
                method: this.$t("method"),
                "Payment providers": this.$t("Payment providers"),
                "Pallet delivery": this.$t("Pallet delivery"),
                "Service types": this.$t("Service types"),
                "Here you can select several of the services you would like to use in your store.": this.$t(
                    "Here you can select several of the services you would like to use in your store."
                ),
                "Selected providers": this.$t("Selected providers"),
                "Selected regions": this.$t("Selected regions"),
                "Selected services": this.$t("Selected services"),
                "You have not entered a shipping method name": this.$t(
                    "You have not entered a shipping method name"
                ),
                "The maximum name length can be 191 characters": this.$t(
                    "The maximum name length can be 191 characters"
                ),
                "You have not selected a regions": this.$t(
                    "You have not selected a regions"
                ),
                "Weight is required": this.$t("Weight is required"),
                "Field is required.": this.$t("Field is required."),
                "You have not selected a geographic area": this.$t(
                    "You have not selected a geographic area"
                ),
                "You have not chosen to generate a price for delivery to an address": this.$t(
                    "You have not chosen to generate a price for delivery to an address"
                ),
                "You have not chosen to generate a price for delivery to a locker": this.$t(
                    "You have not chosen to generate a price for delivery to a locker"
                ),
                "You have not selected pricing for office delivery": this.$t(
                    "You have not selected pricing for office delivery"
                ),
                "The name is required": this.$t("The name is required"),
                "Name can not be longer than 191 symbols": this.$t(
                    "Name can not be longer than 191 symbols"
                ),
                "Price from must be less that price to for each shipping method rate!": this.$t(
                    "Price from must be less that price to for each shipping method rate!"
                ),
                "Shipping method rate weight from must be less than weight to": this.$t(
                    "Shipping method rate weight from must be less than weight to"
                ),
                "Shipping method range overlaps existing": this.$t(
                    "Shipping method range overlaps existing"
                ),
                "Shipping method rate weight from must start from largest previous weight to": this.$t(
                    "Shipping method rate weight from must start from largest previous weight to"
                ),
                "Shipping method rate weight to must end at smallest previous weight from": this.$t(
                    "Shipping method rate weight to must end at smallest previous weight from"
                ),
                "Username is required": this.$t("Username is required"),
                "Password is required": this.$t("Password is required"),
                "Client ID is required": this.$t("Client ID is required"),
                "API key is required": this.$t("API key is required"),
                "You have not entered a username.": this.$t(
                    "You have not entered a username."
                ),
                "You have not entered a password": this.$t(
                    "You have not entered a password"
                ),
                "You have not entered an API key": this.$t(
                    "You have not entered an API key"
                ),
                'Speedy v2 calculator + free shipping': this.$t('Speedy v2 calculator + free shipping')
            },
            boxes: {
                Credentials: {
                    key: "credentials",
                    group: this.providerKey,
                    title: "Credentials",
                    infoTitle: "Credentials",
                    infoDescription:
                        "__A eu laoreet consequat convallis ut. Dui velit eu sociis vitae lobortis Malesuada massa maecenas consectetur est. Praesent ut nec at nisl iaculis pretium morbi. Interdum nec",
                    editMethod: "inline",
                },
                Visualization: {
                    key: "visualization",
                    group: this.providerKey,
                    title: "Visualization",
                    infoTitle: "Visualization",
                    infoDescription:
                        "A eu laoreet consequat convallis ut. Dui velit eu sociis vitae lobortis Malesuada massa maecenas consectetur est. Praesent ut nec at nisl iaculis pretium morbi. Interdum nec",
                    editMethod: "inline",
                },
                Service: {
                    key: "service",
                    group: this.providerKey,
                    title: "Services",
                    infoTitle: "Services",
                    infoDescription:
                        "___A eu laoreet consequat convallis ut. Dui velit eu sociis vitae lobortis Malesuada massa maecenas consectetur est. Praesent ut nec at nisl iaculis pretium morbi. Interdum nec",
                    editMethod: "inline",
                },
                Services: {
                    key: "services",
                    group: this.providerKey,
                    title: "Service types",
                    infoTitle: "Service types",
                    infoDescription:
                        "___A eu laoreet consequat convallis ut. Dui velit eu sociis vitae lobortis Malesuada massa maecenas consectetur est. Praesent ut nec at nisl iaculis pretium morbi. Interdum nec",
                    editMethod: "inline",
                },
                geo_zones: {
                    key: "geo_zones",
                    group: this.providerKey,
                    title: "Ships to",
                    infoTitle: "Shipping regions",
                    infoDescription:
                        "___A eu laoreet consequat convallis ut. Dui velit eu sociis vitae lobortis Malesuada massa maecenas consectetur est. Praesent ut nec at nisl iaculis pretium morbi. Interdum nec",
                    editMethod: "inline",
                },
                payments: {
                    key: "payments",
                    group: this.providerKey,
                    title: "Payment providers",
                    infoTitle: "Payment providers",
                    infoDescription:
                        "___A eu laoreet consequat convallis ut. Dui velit eu sociis vitae lobortis Malesuada massa maecenas consectetur est. Praesent ut nec at nisl iaculis pretium morbi. Interdum nec",
                    editMethod: "inline",
                },
                pickup: {
                    key: "pickup",
                    group: this.providerKey,
                    title: "Sender Data",
                    infoTitle: "Sender Data",
                    infoDescription: "Sender data description",
                    editMethod: "inline",
                    hideTitle: true
                },
                pallet: {
                    key: "pallet",
                    group: this.providerKey,
                    title: "Pallet delivery",
                    infoTitle: "Pallet delivery configuration",
                    infoDescription: "Pallet delivery configuration",
                    editMethod: "panel",
                    hideInlineLabel: true,
                },
            },
        };
    },
    computed: {
        pricing_options(){
            return Object.entries(this.config?.pricings || {}).map(([id, name]) => {
                return {
                    id,
                    name: this.providerKey === 'dpdbulgaria' && id === 'free' ? this.translations['Speedy v2 calculator + free shipping'] : this.$trp(this.translations[name], { app: this.app.name }),
                }
            });
        },
        calcKeys() {
            let arr = [];
            let types = this.providerData?.deliveryType;
            let examples = [
                `${this.providerKey}.pricing_`,
                `${this.providerKey}.fixed_price_`,
                `${this.providerKey}.price_calculator_`,
                `${this.providerKey}.free_method_city_`,
                `${this.providerKey}.free_shipping_total_`,
                `${this.providerKey}.free_method_intercity_`,
                `${this.providerKey}.free_method_international_`,
            ];
            if (types && Array.isArray(types)) {
                examples.forEach((example) => {
                    types.forEach((type) => {
                        arr.push(example + type);
                    });
                });
            }

            if (types && Array.isArray(types)) {
                types.forEach((type) => {
                    arr.push(`${type}.[d].from`)
                    arr.push(`${type}.[d].to`)
                    arr.push(`${type}.[d].amount`)
                })
            }

            return arr;
        },
        generateGeoZoneValue() {
            if (this.zones.length !== 0) {
                let obj = this.zones.find(
                    (x) => x.id === this.providerData.provider.geo_zone_id
                );
                return obj ? obj.name : "";
            }
            return "";
        },
        isValidCredentials() {
            if (this.withoutCredentials) {
                this.$emit("update:credentialsError", false);
                this.$emit("update:isValid", true);
                return true;
            }
            if (
                !this.providerData.session ||
                this.isCredentialsChanged ||
                !this.isValidAfterSubmit
            ) {
                this.$emit("update:isValid", false);
                this.credentialsSlide = true;
                return false;
            }
            this.credentialsSlide = false;
            this.$emit("update:credentialsError", false);
            this.$emit("update:isValid", true);
            return true;
        },
    },
    methods: {
        openCalculator(type) {
            this.calcType = type;
            this.toggleOpen(true);
            this.modals.calculator = true;
        },
        toggleSlide(status, key) {
            if (status === true) {
                this.prevData[key] ??= {};
                this.prevData[key] = JSON.parse(JSON.stringify(this.providerData));
            } else if (status === "save") {
                this.providerData = JSON.parse(JSON.stringify(this.prevData[key]));
            }
        },
        toggleOpen(status) {
            if (status === true) {
                this.savedData = JSON.parse(JSON.stringify(this.providerData));
            } else if (status === "save") {
                this.providerData = {...this.savedData, provider:{
                    ...this.savedData.provider,
                    image: this.providerData.provider.image,
                }},
                this.savedData = null;
            }
        },
        submitCredentials() {
            if (!this.providerData.supports.validate_credentials) {
                return null;
            }

            this.submitLoader = true;
            this.$emit("update:credentialsError", false);
            this.credentialError = false;
            let body = {
                [this.providerKey]: Object.fromEntries(
                    Object.entries(this.providerData?.settings).filter(([key]) =>
                        this.credentialsKeys.includes(key)
                    )
                ),
            };
            axios
                .post(`/admin/api/${this.providerKey}/validate`, body)
                .then((res) => {
                    this.submitLoader = false;
                    this.isValidAfterSubmit = res.data === true;
                    if (this.isValidAfterSubmit) {
                        this.isCredentialsChanged = false;
                        this.credentialsSlide = false;
                        //fix issue with changing credentials
                        this.$parent.actions.getSettings(this.providerKey === 'econt');
                    }
                })
                .catch((err) => {
                    this.credentialsSlide = true;
                    this.isValidAfterSubmit = false;

                    this.$emit("update:credentialsError", true);
                    this.$emit("update:isValid", false);
                    this.credentialError = true;
                })
                .finally(() => {
                    this.submitLoader = false;
                });
        },
    },
    watch: {
        "prevData.geo_zones": {
            deep: true,
            handler(val) {
                let keys = Object.keys(this.prevData).filter((x) => x !== "geo_zones");
                if (keys.length !== 0) {
                    keys.forEach((x) => {
                        this.prevData[x].provider.geo_zone_id = val.provider.geo_zone_id;
                        this.prevData[x].provider.target = val.provider.target;
                    });
                }
            },
        },
        "prevData.payments": {
            deep: true,
            handler(val) {
                let keys = Object.keys(this.prevData).filter((x) => x !== "payments");
                if (keys.length !== 0) {
                    keys.forEach((x) => {
                        this.prevData[x].selectedPayments = val.selectedPayments;
                        this.prevData[x].target = val.target;
                        if (this.prevData.payments.payments_all) {
                            this.prevData[x].payments_all = val.payments_all;
                        } else {
                            delete this.prevData[x].payments_all;
                        }
                    });
                }
            },
        },
        "prevData.pickup": {
            deep: true,
            handler(val) {
                let keys = Object.keys(this.prevData).filter((x) => x !== "pickup");
                if (keys.length !== 0) {
                    keys.forEach((x) => {
                        this.pickupKeys.settingsKeys.forEach((key) => {
                            this.prevData[x].settings[key] = val.settings[key];
                        });
                    });
                }
            },
        },
        "prevData.services": {
            deep: true,
            handler(val) {
                let keys = Object.keys(this.prevData).filter((x) => x !== "services");
                if (keys.length !== 0) {
                    keys.forEach((x) => {
                        this.prevData[x].settings.allowed_methods =
                            val.settings.allowed_methods;
                    });
                }
            },
        },
        boxOpen(val) {
            this.$emit("update:preventSave", val);
        },
        providerData: {
            deep: true,
            handler(value) {
                this.$emit("update:config", value);
                this.isValidAfterSubmit = value.session;
            },
        },
        config: {
            deep: true,
            handler(val) {
                this.providerData = val;
            },
        },
        // credentialsKeys: {
        //     deep: true,
        //     handler(val) {
        //         if (val.length !== 0) {
        //             this.credentialsFields = val;
        //         }
        //     },
        // },
        // credentialsFields: {
        //     deep: true,
        //     handler(val) {
        //         this.$emit('update:credFields', val)
        //     },
        // },
        responseErrors: {
            deep: true,
            handler(val) {
                let keys = Object.keys(val);

                if (keys.length !== 0) {
                    keys.forEach((key) => {
                        if (key.includes("address")) {
                            this.calcType = "address";
                        } else if (key.includes("office")) {
                            this.calcType = "office";
                        } else if (key.includes("locker")) {
                            this.calcType = "locker";
                        }
                    });
                }
            },
        },
        isCredentialsChangedCourier(val) {
            this.isCredentialsChanged = val;
        },
    },
    created() {
        this.isValidAfterSubmit = this.config.session;
    },
    mounted() {
        if (this.config.pricings) {
            if (
                !Array.isArray(this.config.pricings) &&
                typeof this.config.pricings === "object"
            ) {
                Object.entries(this.config.pricings).forEach(([id, name]) => {
                    this.translations[name] = this.$t(name);
                    // this.pricing_options.push({
                    //     id,
                    //     name: this.$trp(this.translations[name], { app: this.app.name }),
                    // });
                });
            } else if (
                Array.isArray(this.config.pricings) &&
                this.config.pricings.length !== 0
            ) {
                this.config.pricings.forEach((option) => {
                    this.translations[option.name] = this.$t(option.name);
                    // this.pricing_options.push({
                    //     id: option.id,
                    //     name: this.$trp(this.translations[option.name], {
                    //         app: this.app.name,
                    //     }),
                    // });
                });
            }
        }

        if (!this.isSetBoxes) {
            this.helpBoxes(this.boxes);
            this.isSetBoxes = true;
            this.zones = this.providerData?.inputs?.geo_zones || [];
        }

        if (this.providerData && this.providerData.deliveryType) {
            this.providerData.deliveryType.forEach((type) => {
                this.translations[type] = this.$t(type);
            });
        }

        this.$populateTranslations(this);
    },
    emits: [
        "update:config",
        "update:isValid",
        "update:credentialsError",
        "update:preventSave",
        // "update:credFields",
    ],
};
</script>
<style>
@import url("./../scss/style.scss");

.settings-value-short {
    color: #7a7d84;
    text-align: left;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
}
</style>
