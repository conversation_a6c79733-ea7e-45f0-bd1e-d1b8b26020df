<template>
    <SettingsFormShippings
        provider-key="dpdbulgaria"
        v-model:config="config"
        v-model:isAllContentLoaded="contentLoaded"
        v-model:isCredentialsChangedCourier="isCredentialsChanged"
        v-model:credentialsError="error"
        v-model:isValid="isValidCredentials"
        :response-errors="responseErrors"
        :pickup-keys="{
            firstLine: ['client_name'],
            secondLine: ['address_id', 'office_id'],
            thirdLine: ['client_phone'],
            settingsKeys: [
                'client_name',
                'address_id',
                'office_id',
                'client_phone',
            ],
            selects: ['address_id', 'office_id'],
        }"
        :app="app"
        v-model:prevent-save="preventSave"
    >
        <template
            #credentialsAction
            v-if="config?.session && isValidCredentials"
        >
            <button
                class="btn btn-white d-flex align-items-center gap-1"
                @click="renewClientInformation"
                :disabled="renewLoader"
            >
                <b-spinner v-if="renewLoader" small></b-spinner>
                {{ translations["Renew client information"] }}
            </button>
        </template>
        <template #senderData="slotProps">
            <SenderDataSection
                :sender="slotProps.data?.settings"
                :clients="config.meta?.clients || []"
                :response-errors="responseErrors"
                :provider-key="providerKey"
                :offices="config.inputs?.offices || []"
                :meta="config.meta"
            />
        </template>
    </SettingsFormShippings>
</template>

<script>
import axios from "axios";
import SettingsFormShippings from "./../../components/SettingsFormShippings";
import SenderDataSection from "./section-components/SenderDataSection";
import mixin from "./../../js/mixin.js";

export default {
    name: "Settings",
    mixins: [mixin],
    components: {
        SettingsFormShippings,
        SenderDataSection,
    },
    mounted() {
        this.config.meta.address_id = Array.isArray(this.config.meta?.clients)
            ? this.config.meta?.clients.map((x) => ({
                  id: x?.clientId,
                  name: x?.name,
              }))
            : [];
        this.config.meta.office_id = Array.isArray(
            this.config.settings.office_input
        )
            ? this.config.settings.office_input
            : [];
    },
    data() {
        return {
            renewLoader: false,
            translations: {
                "You have not selected a shipping method": this.$t(
                    "You have not selected a shipping method"
                ),
                "You have not selected an office to send to": this.$t(
                    "You have not selected an office to send to"
                ),
                "You have not entered a name": this.$t(
                    "You have not entered a name"
                ),
                "You have not entered a phone": this.$t(
                    "You have not entered a phone"
                ),
                "You have not selected a shipping address": this.$t(
                    "You have not selected a shipping address"
                ),
                "Renew client information": this.$t("Renew client information"),
            },
        };
    },
    methods: {
        async renewClientInformation() {
            this.renewLoader = true;
            try {
                const response = await axios.get(
                    "/admin/api/dpdbulgaria/refresh-client"
                );
                await this.actions.getSettings();
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.renewLoader = false;
            }
        },
    },
};
</script>
