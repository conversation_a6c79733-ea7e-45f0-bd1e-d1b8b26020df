<template>
    <b-button variant="primary" class="text-nowrap" @click="modal = true">
        <i class="far fa-plus"></i>
        {{ translations["Add domain"] }}
    </b-button>
    <b-modal
        v-model="modal"
        :no-footer="true"
        :no-header="true"
        size="md"
        id="create-modal"
    >
        <div id="create-new-domain" class="p-1">
            <div class="mb-4">
                <div class="d-flex justify-content-between">
                    <p class="label-500-18px" v-html="translations['Add New Domain']"></p>
                    <a
                        href="javascript:void(0);"
                        @click.prevent="modal = false"
                        class="remove-row-action"
                    >
                        <i class="far fa-times"></i>
                    </a>
                </div>
                <p
                    class="text-400-secondary-16px"
                    v-html="
                        translations['Choose one of the options for adding a new domain']
                    "
                ></p>
            </div>

            <div id="dropdowns-domain-add-wrapper">
                <a
                    @click="dropdownFirst = !dropdownFirst"
                    href="javascript:void(0);"
                    class="dropdown-wrapper-item"
                    :class="{ active: dropdownFirst }"
                >
                    <div class="left-side">
                        <figure class="m-0 icon-holder">
                            <i class="far fa-browser"></i>
                        </figure>
                        <div class="d-flex flex-column">
                            <p
                                class="d-block label-500-16px mb-1"
                                v-html="translations['Add existing domain']"
                            ></p>
                            <span
                                class="value-400"
                                v-html="
                                    translations[
                                        'Lorem ipsum dolor sit amet consectetur.'
                                    ]
                                "
                            ></span>
                        </div>
                    </div>
                    <div class="right-side">
                        <i v-if="!dropdownFirst" class="far fa-angle-down"></i>
                        <i v-else class="far fa-angle-up"></i>
                    </div>
                </a>
                <Vue3SlideUpDown v-model="dropdownFirst" :duration="200">
                    <p
                        class="label-500-16px d-block w-100 text-center my-4"
                        v-html="translations['Enter domain name here']"
                    ></p>
                    <div
                        class="domain-input"
                        :class="{
                            focused: focusedInput,
                            'error-domain': responseErrors.domain,
                        }"
                    >
                        <div class="http-holder">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="80"
                                height="48"
                                viewBox="0 0 80 48"
                                fill="none"
                            >
                                <path
                                    d="M0 42V6C0 2.68629 2.68629 0 6 0H67.5077L80 23.5L66.0308 48H6C2.68629 48 0 45.3137 0 42Z"
                                    fill="#F7F2FF"
                                />
                            </svg>
                            <span> https:// </span>
                        </div>
                        <input
                            ref="addDomainInput"
                            type="text"
                            autocomplete="off"
                            v-model="domain"
                            @focus="focusedInput = true"
                            @keypress.enter="()=>{ if(domain) addExistingDomain() }"
                        />
                        <div
                            class="help-block-error transition-show-error"
                            :class="
                                responseErrors.domain
                                    ? 'show-error-text'
                                    : 'hide-error-text'
                            "
                            v-if="responseErrors.domain"
                        >
                            <span
                                v-html="
                                    translations[responseErrors.domain] ||
                                    responseErrors.domain
                                "
                            ></span>
                            <i
                                class="fal fa-times close-error-message"
                                @click="delete responseErrors.domain"
                            ></i>
                        </div>
                    </div>
                    <div class="d-flex justify-content-end gap-3 mt-3">
                        <button
                            @click="
                                domain = '';
                                dropdownFirst = false;
                            "
                            class="btn btn-white btn-small"
                            v-html="translations.Cancel"
                        ></button>
                        <button
                            class="btn btn-primary btn-small"
                            :disabled="!domain || createExistingLoader"
                            @click="addExistingDomain"
                        >
                            <b-spinner small v-if="createExistingLoader"></b-spinner>
                            <i class="far fa-check" v-else></i>
                            {{ translations.Save }}
                        </button>
                    </div>
                    <div class="d-flex align-items-center gap-2 mt-3">
                        <div class="question-wrapper">
                            <i class="fas fa-question"></i>
                        </div>
                        <span>
                            {{ translations["Need help adding a domain?"] }}
                            <a
                                @click="modal = false"
                                :href="`https://help.cloudcart.com/${serverSettings('language_cp')}/support/solutions/articles/77000172734-how-can-i-add-an-existing-domain-on-my-online-store-`"
                                target="_blank"
                                v-html="translations['Contact us']"
                            >
                            </a>
                        </span>
                    </div>
                </Vue3SlideUpDown>

                <a
                    @click="dropdownSecond = !dropdownSecond"
                    href="javascript:void(0);"
                    class="dropdown-wrapper-item mt-4"
                    :class="{ active: dropdownSecond }"
                >
                    <div class="left-side">
                        <figure class="m-0 icon-holder">
                            <i class="far fa-globe"></i>
                        </figure>
                        <div class="d-flex flex-column">
                            <p
                                class="d-block label-500-16px mb-1"
                                v-html="translations['Purchase new domain']"
                            ></p>
                            <span
                                class="value-400"
                                v-html="translations['Purchase new domain description']"
                            ></span>
                        </div>
                    </div>
                    <div class="right-side">
                        <i v-if="!dropdownSecond" class="far fa-angle-down"></i>
                        <i v-else class="far fa-angle-up"></i>
                    </div>
                </a>
                <Vue3SlideUpDown v-model="dropdownSecond" :duration="200">
                    <p
                        class="label-500-16px d-block w-100 text-center my-4"
                        v-html="translations['Search for your new domain here']"
                    ></p>
                    <div class="d-flex align-items-center gap-2 w-100">
                        <div class="domain-input" :class="{ focused: focusedInputSecond }">
                            <a
                                href="javascript:void(0);"
                                @click.prevent="searchDomainName"
                                class="search-domain-icon"
                            >
                                <b-spinner small v-if="searchNameLoader"></b-spinner>
                                <i v-else class="far fa-search"></i>
                            </a>
                            <input
                                class="padding"
                                type="text"
                                ref="addDomainInputSecond"
                                autocomplete="off"
                                :readonly="searchNameLoader"
                                v-model="domain"

                                @focus="focusedInputSecond = true"
                                @keypress.enter="searchDomainName"
                            />

                            <div
                                class="help-block-error transition-show-error"
                                :class="
                                    responseErrors.domain
                                        ? 'show-error-text'
                                        : 'hide-error-text'
                                "
                                v-if="responseErrors.domain"
                            >
                                <span
                                    v-html="
                                        translations[responseErrors.domain] ||
                                        responseErrors.domain
                                    "
                                ></span>
                                <i
                                    class="fal fa-times close-error-message"
                                    @click="delete responseErrors.domain"
                                ></i>
                            </div>
                        </div>
                        <button :disabled="domain === '' || searchName" class="btn btn-primary" @click="searchDomainName">{{ translations["Search"] }}</button>
                    </div>
                    <Vue3SlideUpDown
                        :model-value="list.length > 0 || loadingList"
                        :duration="200"
                    >
                        <Loading
                            :loading="loadingList"
                            v-if="loadingList"
                            style="margin: 30px 45%"
                        />
                        <ul v-else id="list-of-domains">
                            <li v-for="(item, index) in list" :key="index">
                                <div class="d-flex align-items-center gap-2">
                                    <i v-if="item.purchasable" class="fas fa-check-circle"></i>
                                    <i v-else  class="fas fa-ban"></i>
                                    <span
                                        class="text-400-secondary-16px"
                                        v-html="item.domainName"
                                    >
                                    </span>
                                </div>
                                <div
                                    v-if="item.purchasable"
                                    class="d-flex align-items-center justify-content-end gap-3 flex-wrap w-100"
                                >
                                    <div class="d-flex flex-column gap-1 align-items-end">
                                        <p class="label-500-16px m-0 text-nowrap">
                                            <span
                                                class="cc-badge-status cc-tag-status--enabled"
                                            >
                                                {{ translations["Promo price 1st year"] }}
                                            </span>
                                            {{ item.purchasePrice }}
                                        </p>
                                        <span class="text-400-secondary-12px">
                                            {{
                                                $trp(
                                                    this.translations[
                                                        "Price for next year: {price}"
                                                    ],
                                                    { price: item.renewalPrice }
                                                )
                                            }}
                                        </span>
                                    </div>
                                    <button
                                        class="btn btn-white"
                                        @click="$emit('purchaseDomain', item, false)"
                                        :disabled="item.loader"
                                    >
                                        <b-spinner small v-if="item.loader"></b-spinner>
                                        {{ translations.Buy }}
                                    </button>
                                </div>
                                <div v-else>
                                    <p
                                        class="text-400-secondary-16px m-0"
                                        v-html="
                                            translations[
                                                'This domain is not available for purchase.'
                                            ]
                                        "
                                    ></p>
                                </div>
                            </li>
                        </ul>
                    </Vue3SlideUpDown>
                </Vue3SlideUpDown>
            </div>
        </div>
    </b-modal>
</template>

<script>
import { Vue3SlideUpDown } from "vue3-slide-up-down";
import Loading from "@components/Loading";
export default {
    components: {
        Vue3SlideUpDown,
        Loading,
    },
    props: {
        modelValue: {
            type: Boolean,
        },
        model: {
            type: Object,
        },
    },
    data() {
        return {
            modal: false,
            action: {},
            dropdownFirst: false,
            dropdownSecond: false,
            focusedInput: true,
            focusedInputSecond: true,
            url: "",
            domain: "",
            responseErrors: {},
            createExistingLoader: false,
            searchNameLoader: false,
            list: [],
            loadingList: false,
            translations: {
                Cancel: this.$t("Cancel"),
                Save: this.$t("Save"),
                Buy: this.$t("Buy"),
                "Add domain": this.$t("Add domain"),
                "Purchase new domain": this.$t("Purchase new domain"),
                "Purchase new domain description": this.$t(
                    "Purchase new domain description"
                ),
                "Search for your new domain here": this.$t(
                    "Search for your new domain here"
                ),
                "Add New Domain": this.$t("Add New Domain"),
                "Add existing domain": this.$t("Add existing domain"),
                "Choose one of the options for adding a new domain": this.$t(
                    "Choose one of the options for adding a new domain"
                ),
                "Lorem ipsum dolor sit amet consectetur.": this.$t(
                    "Lorem ipsum dolor sit amet consectetur."
                ),
                "Enter domain name here": this.$t("Enter domain name here"),
                "Need help adding a domain?": this.$t("Need help adding a domain?"),
                "Contact us": this.$t("Contact us"),
                "Domain is required": this.$t("Domain is required"),
                "Invalid domain name": this.$t("Invalid domain name"),
                "The domain already exists in the system": this.$t(
                    "The domain already exists in the system"
                ),
                "Promo price 1st year": this.$t("Promo price 1st year"),
                "Price for next year: {price}": this.$t("Price for next year: {price}"),
                "This domain is not available for purchase.": this.$t(
                    "This domain is not available for purchase."
                ),
                "Search": this.$t("Search")
            },
        };
    },
    methods: {
        async addExistingDomain() {
            this.responseErrors = {};

            const domainPattern = /^[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/;
            const latinPattern = /^[a-zA-Z0-9-\.]+$/;

            // if (!domainPattern.test(this.domain) || !latinPattern.test(this.domain)) {
            //     this.responseErrors.domain = this.translations["Invalid domain name"];
            //     return;
            // }
            this.createExistingLoader = true;

            try {
                const response = await this.model.post("save-external", {
                    domain: this.domain,
                });
                this.$emit("addRecord", response);
                this.modal = false;
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.createExistingLoader = false;
            }
        },
        async searchDomainName() {
            if(this.domain === "") return;

            this.responseErrors = {};

            this.searchNameLoader = true;
            this.loadingList = true;
            try {
                const response = await this.model.post("search", {
                    keyword: this.domain,
                });
                this.list = Object.values(response.results || {});
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.searchNameLoader = false;
                this.loadingList = false;
            }
        },
    },
    watch: {
        modelValue(val) {
            this.modal = val;
        },
        modal(val) {
            this.$emit("update:modelValue", val);
            if (!val) {
                this.dropdownFirst = false;
                this.dropdownSecond = false;
                this.url = "";
                this.domain = "";
                this.list = [];
            }
        },
        dropdownFirst(val) {
            if (val) {
                this.dropdownSecond = false;
                this.responseErrors = {};
                setTimeout(() => {
                    this.$refs.addDomainInput ? this.$refs.addDomainInput.focus() : null;
                }, 280);
            }
        },
        dropdownSecond(val) {
            if (val) {
                this.dropdownFirst = false;
                this.responseErrors = {};
                setTimeout(() => {
                    this.$refs.addDomainInputSecond
                        ? this.$refs.addDomainInputSecond.focus()
                        : null;
                }, 280);
            }
        },
    },
    emits: ["update:modelValue", "addRecord", "purchaseDomain"],
};
</script>

<style lang="scss">
@import url('./../scss/style.scss');
</style>
