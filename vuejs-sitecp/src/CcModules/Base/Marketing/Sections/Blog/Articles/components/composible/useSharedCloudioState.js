import { inject, ref } from 'vue'
import { createSharedComposable } from '@vueuse/core'
import axios from 'axios';

const useSharedCloudioState = createSharedComposable(() => {
    const errorResponse = inject('errorResponse');

    const cloudioModal = ref(false);
    const type = ref('description');

    const openCloudioModal = (mode) => {
        type.value = mode
        cloudioModal.value = true;
    }
    const isCloudioActivated = ref(false);

    const getCloudioStatus = async () => {
        try {
            const response = await axios.get("/admin/api/cloudio/skills");
            let shopper_pen = response.data.find(
                (skill) => skill.key === "shopper_pen"
            );
            isCloudioActivated.value = shopper_pen?.isActive;
        } catch (error) {
            errorResponse(error);
        }
    }

    return {
        cloudioModal,
        type,
        openCloudioModal,
        isCloudioActivated,
        getCloudioStatus
    };
});

export default useSharedCloudioState;