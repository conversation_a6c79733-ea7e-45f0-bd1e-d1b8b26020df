<template>
    <a href="javascript:void(0);" class="name-wrapper">
        <ActiveSwitch v-model:isActive="active"> </ActiveSwitch>
    </a>
</template>

<script>
import ActiveSwitch from "@components/ConfirmModal";

export default {
    name: "Published",
    components: {
        ActiveSwitch,
    },
    props: {
        data: {
            required: true,
            default: {},
            type: Object,
        },
        column: {
            required: true,
            default: {},
            type: Object,
        },
    },
    data() {
        return {
            translations: {
                Comments: this.$t("Comments"),
            },
            active: true,
        };
    },
};
</script>

<style lang="scss" scoped>
.name-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    gap: var(--Spacing---cc-space-md, 16px);
}
</style>
