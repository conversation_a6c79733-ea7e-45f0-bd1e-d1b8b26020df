<template>
    <a href="javascript:void(0);" class="name-wrapper">
        <span
            :disabled="data.comments_count === 4"
            class="btn btn-ghost btn-small d-flex align-items-center justify-content-center"
            ><i style="color: #8d58e0" class="far fa-comment-alt"></i>Comments
            ({{ data.comments_count }})</span
        >
    </a>
</template>

<script>
export default {
    name: "Comment",
    props: {
        data: {
            required: true,
            default: {},
            type: Object,
        },
        column: {
            required: true,
            default: {},
            type: Object,
        },
    },
    data() {
        return {
            translations: {
                Comments: this.$t("Comments"),
            },
        };
    },
};
</script>

<style lang="scss" scoped>
.name-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    gap: var(--Spacing---cc-space-md, 16px);
}

</style>
