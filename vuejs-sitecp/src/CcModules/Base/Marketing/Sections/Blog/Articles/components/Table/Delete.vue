<template>
    <div class="d-flex align-items-center justify-content-end w-100">
        <DeleteComponent
            v-model:loader="data.loader"
            :deleteFunction="() => data.deleteRow(data)"
        />
    </div>
</template>
<script>
import DeleteComponent from "@components/DeleteComponent";

export default {
    name: "Delete",
    components: {
        DeleteComponent,
    },
    props: {
        data: {
            required: true,
            default: {},
        },
        column: {
            required: true,
            default: {},
        },
    },

};
</script>
