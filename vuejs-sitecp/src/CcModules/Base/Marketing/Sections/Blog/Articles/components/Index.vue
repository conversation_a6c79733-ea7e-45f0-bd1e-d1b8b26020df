<template>
    <SettingsWrapper
        setting-key="blog-articles"
        :loading="false"
        :prevent-save="true"
        icon="far fa-typewriter"
        :title="translations['Blog articles']"
        :header-button-classes="'col-sm-3'"
        :breadcrumb="breadcrumb"
        :container-classes="'container-large'"
    >
        <template #headerButton>
            <div
                class="d-flex flex-row justify-content-center align-items-center gap-3"
            >
                <b-button
                    v-if="
                        !['articles-add', 'articles-edit'].includes($route.name)
                    "
                    class="text-nowrap"
                    variant="secondary"
                    @click="modal = true"
                >
                    <i class="far fa-sliders-v fs-6" style="color: #8d58e0"></i>
                    {{ translations["Blog Settings"] }}
                </b-button>

                <b-button
                    v-if="
                        !['articles-add', 'articles-edit'].includes($route.name)
                    "
                    class="text-nowrap"
                    variant="primary"
                    @click="handleCreate"
                >
                    <i class="far fa-plus"></i>
                    {{ translations["Add article"] }} ({{ data.total }})
                </b-button>
            </div>
        </template>

        <template #settings>
            <template
                v-if="['articles-add', 'articles-edit'].includes($route.name)"
            >
                <router-view />
            </template>

            <template v-else>
                <div id="blog-articles-table">
                    <data-table
                        v-bind="table"
                        v-model="ids"
                        @pagination-change-page="paginate"
                        :enable-mobile="true"
                        :filters="true"
                        app-name="blog-articles"
                        :get-data="getData"
                        :filter-options="filterOptions"
                        v-model:query="query"
                    >
                        <TableActions
                            v-model="ids"
                            app-key="blog-articles"
                            @success="handleActions"
                            delete-type="delete"
                            delete-url="/admin/api/core/blog/articles"
                            :enable-mobile="true"
                        />
                    </data-table>
                </div>
            </template>
        </template>
    </SettingsWrapper>
</template>

<script>
import { markRaw } from "vue";
import { toast } from "@js/toast";
import Articles from "../js/Articles";

import DataTable from "@components/Table";
import TableActions from "@components/TableActions";

import SettingsWrapper from "@components/SettingsWrapper/SettingsWrapper";

import CreateOrEdit from "./CreateOrEdit.vue";

import Name from "./Table/Name.vue";
import Delete from "./Table/Delete.vue";
import Published from "./Table/Published.vue";
import Comment from "./Table/Comment.vue";

export default {
    components: {
        DataTable,
        TableActions,
        SettingsWrapper,
        Name,
        Delete,
        Published,
        Comment,
        CreateOrEdit,
    },
    props: ["app"],
    data() {
        return {
            modal: false,
            isLoading: true,
            data: {
                data: [],
            },
            ids: [],
            dataRow: null,
            page: this.$route.query?.page || 1,
            perPage: this.$route.query?.perpage || 25,
            query: this.$route.query,
            model: new Articles(),
            translations: {
                Name: this.$t("Name"),
                Published: this.$t("Published"),
                Comment: this.$t("Comment"),
                "Add article": this.$t("Add article"),
                "Blog Settings": this.$t("Blog Settings"),
                "Blog articles": this.$t("Blog articles"),
                "Deleted successfully": this.$t("Deleted successfully"),
                "Error while deleting": this.$t("Error while deleting"),
                Article: this.$t("Article"),
                Marketing: this.$t("Marketing"),
                Blog: this.$t("Blog"),
                "Edit article": this.$t("Add article"),
            },
        };
    },
    computed: {
        table() {
            return {
                data: this.data,
                isLoading: this.isLoading,
                defaultSorting: [{ key: "id", sortingMode: "desc" }],
                columns: [
                    {
                        column: "name",
                        key: "name",
                        sortable: false,
                        title: this.translations["Name"],
                        component: markRaw(Name),
                        isMain: true,
                    },
                    {
                        column: "comment",
                        key: "comment",
                        sortable: false,
                        title: this.translations["Comment"],
                        component: markRaw(Comment),
                    },
                    {
                        column: "status",
                        key: "status",
                        sortable: true,
                        title: this.translations["Published"],
                        component: markRaw(Published),
                    },
                    {
                        column: "actions",
                        key: "actions",
                        title: " ",
                        sortable: false,
                        component: markRaw(Delete),
                    },
                ],
            };
        },
        breadcrumb() {
            const base = [
                {
                    text: this.translations["Marketing"],
                    to: { name: "admin.index" },
                },
                { text: this.translations["Blog"] },
            ];

            if (this.$route.name === "articles-add") {
                base.push({ text: this.translations["Add article"] });
            }

            if (this.$route.name === "articles-edit") {
                base.push({ text: this.translations["Edit article"] });
            }

            return base;
        },
    },
    methods: {
        handleCreate() {
            this.$router.push({ name: "articles-add" });
        },
        async paginate(page) {
            this.page = page;
            await this.getData(true);
        },
        openModal(item) {
            this.dataRow = _.clone(item);
            this.modal = true;
        },
        async getData(isLoading = true) {
            try {
                this.isLoading = isLoading;

                this.model.where(this.query);

                const tableData = await this.model.paginate(
                    this.query?.page || this.page,
                    this.query?.perpage || this.perPage
                );

                tableData.data = tableData.data.map((x) => {
                    return {
                        ...x,
                        deleteRow: this.deleteRow,
                        openModal: this.openModal,
                    };
                });

                this.data = tableData;

                this.$router.push({
                    query: {
                        ...this.$route.query,
                        page: this.data.current_page,
                    },
                });
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.isLoading = false;
            }
        },
        async deleteRow(item) {
            try {
                item.loading = true;

                const response = await this.model.deleteBulk([item.id]);
                if (response && response?.status === "error") {
                    throw new Error(response?.msg);
                }
                this.data.data = this.data.data.filter((x) => x.id !== item.id);

                document.querySelectorAll(".tooltip").forEach((tooltip) => {
                    tooltip.parentNode.removeChild(tooltip);
                });

                toast.success(this.translations["Deleted successfully"]);
            } catch (error) {
                console.log(error);
                this.$errorResponse(error);
                toast.error(this.translations["Error while deleting"]);
            } finally {
                delete item.loading;
            }
        },
        handleActions(value) {
            if (value.action === "delete") {
                this.data.data = this.data.data.filter(
                    (item) => !this.ids.includes(item.id)
                );
                if (this.data.data.length === 0 && this.page > 1) {
                    this.page -= 1;
                    this.getData();
                }
            }
        },
    },
};
</script>
