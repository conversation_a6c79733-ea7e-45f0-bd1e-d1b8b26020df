<template>
    <a
        href="javascript:void(0);"
        class="name-wrapper"
        @click.prevent="() => data.openModal(data)"
    >
        <LogoThumbnail :image="data.image || serverSettings('noImages.150x150')" width="48px" height="48px" padding="4px" />
        <div class="cc-tooltip-dotted">{{ data.name }}</div>
  </a>
</template>

<script>
import LogoThumbnail from "@components/LogoThumbnail";

export default {
    name: "Name",
    components: {
        LogoThumbnail,
    },
    props: {
        data: {
            required: true,
            default: {},
            type: Object,
        },
        column: {
            required: true,
            default: {},
            type: Object,
        },
    },
};
</script>

<style lang="scss" scoped>
.name-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    gap: var(--Spacing---cc-space-md, 16px);
}
</style>
