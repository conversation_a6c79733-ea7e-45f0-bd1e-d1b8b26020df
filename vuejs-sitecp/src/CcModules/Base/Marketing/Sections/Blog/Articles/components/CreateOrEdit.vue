<template>
    <!-- <div v-if="loading" class="w-100vh h-100 relative">
        <Loading :loading="loading" class="app-loader-center" />
    </div> -->
    <!-- <div v-else> -->
    <div>
        <SettingsForm>
            <DefaultLayout>
                <template #title>
                    <b-row>
                        <b-col cols="12" md="8">
                            <SettingsCard
                                :title="translations['Article title']"
                            >
                                <InputComponent
                                    v-model="item.name"
                                    :labelText="translations['Title']"
                                    :column-style="true"
                                    :error="
                                        translations[responseErrors['name']] ||
                                        responseErrors['name']
                                    "
                                    :no-margin="true"
                                    :placeholder="translations['Title']"
                                />
                            </SettingsCard>
                        </b-col>

                        <b-col cols="12" md="4">
                            <SettingsCard :title="translations['Author']">
                                <SelectWithAjax
                                    :column-style="true"
                                    :colsWidth="12"
                                    :noMargin="true"
                                    :label="
                                        translations[
                                            'Choose an author for this article'
                                        ]
                                    "
                                />
                            </SettingsCard>
                        </b-col>
                    </b-row>

                    <b-row>
                        <!-- Left side - TextEditor -->
                        <b-col cols="8">
                            <SettingsCard :title="translations['Content']">
                                <TextEditor
                                    v-model="item.content"
                                    :height="450"
                                />

                                <CloudioBar
                                    :description="translations['Write article']"
                                    @open="(value) => openCloudioModal(value)"
                                />
                            </SettingsCard>
                        </b-col>

                        <!-- Right side -->
                        <b-col cols="4">
                            <div class="d-flex flex-column gap-3">
                                <SettingsCard
                                    :title="translations['Blog category']"
                                >
                                    <SelectWithAjax
                                        :column-style="true"
                                        :noMargin="true"
                                        :colsWidth="12"
                                        :label="translations['Select']"
                                    />
                                    <a class="add-row-action p-0">
                                        <i class="fal fa-plus"></i>
                                        {{ translations["Create category"] }}
                                    </a>
                                </SettingsCard>

                                <SettingsCard>
                                    <LogoSection
                                        v-model:image="item.image"
                                        v-model:response-errors="responseErrors"
                                        :error="
                                            translations[
                                                responseErrors['image']
                                            ] || responseErrors['image']
                                        "
                                        :has_image="!!item.image"
                                        :label="
                                            translations['Blog category image']
                                        "
                                        :url="deleteLogoImageUrl"
                                        @delete:image="
                                            $emit('delete:image', item.id)
                                        "
                                    />
                                </SettingsCard>

                                <SettingsCard :title="translations['Tags']">
                                    <InputComponent
                                        v-model="item.name"
                                        :labelText="
                                            translations[
                                                'Tags helps people finding similar content'
                                            ]
                                        "
                                        :column-style="true"
                                        :column-size="12"
                                        :error="
                                            translations[
                                                responseErrors['name']
                                            ] || responseErrors['name']
                                        "
                                        :no-margin="true"
                                        :placeholder="
                                            translations['Enter tags']
                                        "
                                    />
                                </SettingsCard>
                            </div>
                        </b-col>
                    </b-row>
                </template>

                <template #advanced>
                    <b-row>
                        <b-col cols="8">
                            <SettingsCard
                                :collapsible="true"
                                :open="true"
                                :title="translations['Advanced settings']"
                            >
                                <InputComponent
                                    v-model="item.seo_title"
                                    :column-size="12"
                                    :column-style="true"
                                    :error="
                                        translations[
                                            responseErrors['seo_title']
                                        ] || responseErrors['seo_title']
                                    "
                                    :label-text="translations['SEO page title']"
                                    :no-margin="true"
                                    :placeholder="
                                        translations[
                                            'Enter unique SEO page title'
                                        ]
                                    "
                                />
                                <InputComponent
                                    v-model="item.url_handle"
                                    :colored="true"
                                    :column-size="12"
                                    :column-style="true"
                                    :error="
                                        translations[responseErrors['url']] ||
                                        responseErrors['url']
                                    "
                                    :label-text="translations['URL']"
                                    :no-margin="true"
                                    :placeholder="
                                        translations['Enter SEO friendly URL']
                                    "
                                    :unit="'/article/'"
                                    unit_position="left"
                                    @blur="handleSlugify"
                                />
                                <TextareaComponent
                                    v-model="item.seo_description"
                                    :column-size="12"
                                    :column-style="true"
                                    :error="
                                        translations[
                                            responseErrors['seo_description']
                                        ] || responseErrors['seo_description']
                                    "
                                    :label-text="
                                        translations['SEO meta description']
                                    "
                                    :no-margin="true"
                                    :placeholder="
                                        translations[
                                            'Enter unique SEO meta description'
                                        ]
                                    "
                                />
                                <CloudioBar
                                    :description="
                                        translations['Genereate text']
                                    "
                                    @open="(value) => openCloudioModal(value)"
                                />
                                <SeoGooglePreview
                                    :description="item.seo_description"
                                    :label-text="translations['Google preview']"
                                    :no-margin="true"
                                    :title="item.seo_title"
                                    :url="item.url"
                                />
                            </SettingsCard>
                        </b-col>
                    </b-row>
                </template>
            </DefaultLayout>
        </SettingsForm>
    </div>
</template>

<script>
import axios from "axios";
import slugify from "@js/slugify";

import { toast } from "@js/toast";
import { buildFormData } from "@js/shippingHelpers";
import useSharedCloudioState from "../components/composible/useSharedCloudioState";

import { useWindowSize } from "@vueuse/core";
import Loading from "@components/Loading";
import SettingsBox from "@components/SettingsBox/SettingsBox";
import SettingsForm from "@components/SettingsForm/SettingsForm";
import SettingsCard from "@components/SettingsForm/Helpers/SettingsCard";
import DefaultLayout from "@components/SettingsForm/Layouts/DefaultLayout";
import InputComponent from "@components/Form/InputComponent";
import TextareaComponent from "@components/Form/TextareaComponent";
import TextEditor from "@components/Form/TextEditor";
import LogoSection from "@components/Form/LogoSection";
import SeoGooglePreview from "@components/SeoGooglePreview";
import ActiveSwitch from "@components/Form/ActiveSwitch";
import RadioComponent from "@components/Form/RadioComponent";
import SelectWithAjax from "@components/Form/SelectWithAjax";
import CloudioBar from "./Helpers/CloudioBar.vue";

const { width } = useWindowSize();

export default {
    components: {
        Loading,
        SettingsBox,
        SettingsForm,
        SettingsCard,
        DefaultLayout,
        InputComponent,
        TextareaComponent,
        TextEditor,
        LogoSection,
        SeoGooglePreview,
        ActiveSwitch,
        RadioComponent,
        SelectWithAjax,
        CloudioBar,
    },
    props: {
        modelValue: {
            type: Boolean,
            required: false,
        },
        model: {
            type: Object,
            default: {},
        },
        data: {
            default: null,
        },
    },
    setup() {
        const { openCloudioModal, type, cloudioModal } =
            useSharedCloudioState();

        return {
            openCloudioModal,
            type,
            cloudioModal,
        };
    },
    data() {
        return {
            isVisible: false,
            modal: false,
            submitLoader: false,
            loading: false,
            item: {
                name: null,
                comments: "automatic",
                image: null,
                seo_title: "",
                seo_description: "",
                type: "select",
            },
            imageFile: null,
            responseErrors: {},
            translations: {
                Cancel: this.$t("Cancel"),
                Tags: this.$t("Tags"),
                Save: this.$t("Save"),
                Title: this.$t("Title"),
                Author: this.$t("Author"),
                Name: this.$t("Name"),
                Content: this.$t("Content"),
                Description: this.$t("Description"),
                URL: this.$t("URL"),
                "Write article": this.$t("Write article"),
                "Genereate text": this.$t("Genereate text"),
                "Enter tags": this.$t("Enter tags"),
                Select: this.$t("Select"),
                "Tags helps people finding similar content": this.$t(
                    "Tags helps people finding similar content"
                ),
                "Create category": this.$t("Create category"),
                "Choose an author for this article": this.$t(
                    "Choose an author for this article"
                ),
                "Article title": this.$t("Article title"),
                "Saved successfully": this.$t("Saved successfully"),
                "General settings": this.$t("General settings"),
                "Advanced settings": this.$t("Advanced settings"),
                "Edit blog category": this.$t("Edit blog category"),
                "Blog category image": this.$t("Blog category image"),
                //
                "SEO page title": this.$t("SEO page title"),
                "Enter unique SEO page title": this.$t(
                    "Enter unique SEO page title"
                ),
                "SEO meta description": this.$t("SEO meta description"),
                "Enter unique SEO meta description": this.$t(
                    "Enter unique SEO meta description"
                ),
                "Google preview": this.$t("Google preview"),
                "If checked, all comments on articles within this category will be automatically posted.":
                    this.$t(
                        "If checked, all comments on articles within this category will be automatically posted."
                    ),
            },
        };
    },

    emits: ["update:modelValue", "delete:image", "success"],
};
</script>
<style>
.tox-dialog__disable-scroll .modal {
    display: none !important;
}
</style>
