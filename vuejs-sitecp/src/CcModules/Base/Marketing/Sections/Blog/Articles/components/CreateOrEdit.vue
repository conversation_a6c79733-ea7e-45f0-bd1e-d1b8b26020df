<template>
    <div v-if="loading" class="w-100vh h-100 relative">
        <Loading :loading="loading" class="app-loader-center" />
    </div>
    <div v-else>
        <SettingsForm>
            <DefaultLayout>
                <template #title>
                    <b-row>
                        <b-col cols="12" md="8">
                            <SettingsCard
                                :title="translations['Article title']"
                            >
                                <InputComponent
                                    v-model="item.name"
                                    :labelText="translations['Title']"
                                    :column-style="true"
                                    :column-size="12"
                                    :no-margin="true"
                                    :error="
                                        translations[responseErrors['Title']] ||
                                        responseErrors['Title']
                                    "
                                    :placeholder="translations['Title']"
                                />
                            </SettingsCard>
                        </b-col>

                        <b-col cols="12" md="4">
                            <SettingsCard :title="translations['Author']">
                                <SelectWithAjax
                                    v-model:val="item.author_id"
                                    :api-url="'/admin/api/core/settings/account/admins'"
                                    value-indicator="id"
                                    :column-style="true"
                                    :noMargin="true"
                                    :colsWidth="12"
                                    label-indicator="name"
                                    :label="
                                        translations[
                                            'Choose an author for this article'
                                        ]
                                    "
                                />
                            </SettingsCard>
                        </b-col>
                    </b-row>

                    <b-row>
                        <!-- Left side - TextEditor -->
                        <b-col cols="8">
                            <div>
                                <SettingsCard
                                :classes="'gap-0'"
                                    :title="translations['Content']"
                                >
                                    <TextEditor
                                        v-model:value="item.content"
                                        :height="450"
                                    />

                                    <CloudioBar
                                        :description="
                                            translations['Write article']
                                        "
                                        @open="
                                            (value) => openCloudioModal(value)
                                        "
                                    />
                                </SettingsCard>
                            </div>
                        </b-col>

                        <!-- Right side -->
                        <b-col cols="4">
                            <div class="d-flex flex-column gap-3">
                                <SettingsCard
                                    :title="translations['Blog category']"
                                >
                                    <SelectWithAjax
                                        label-indicator="name"
                                        v-model:val="item.blog_id"
                                        :column-style="true"
                                        value-indicator="id"
                                        :noMargin="true"
                                        :api-url="'/admin/api/core/blog/categories'"
                                        :colsWidth="12"
                                        :label="translations['Select']"
                                    />
                                    <a
                                        class="add-row-action p-0"
                                        @click="openCategorySidebar"
                                    >
                                        <i class="fal fa-plus"></i>
                                        {{ translations["Create category"] }}
                                    </a>
                                </SettingsCard>

                                <SettingsCard>
                                    <LogoSection
                                        v-model:image="item.logoImage"
                                        v-model:response-errors="responseErrors"
                                        :error="
                                            translations[
                                                responseErrors['image']
                                            ] || responseErrors['image']
                                        "
                                        :has_image="!!item.image"
                                        :label="
                                            translations['Blog category image']
                                        "
                                        :url="deleteLogoImageUrl"
                                        @delete:image="
                                            $emit('delete:image', item.id)
                                        "
                                    />
                                </SettingsCard>

                                <SettingsCard :title="translations['Tags']">
                                    <SelectWithAjax
                                        v-model:val="modelValue.tags_array"
                                        :cols-width="12"
                                        :column-style="true"
                                        :createOption="true"
                                        :placeholder="
                                            translations['Enter tags']
                                        "
                                        :error="
                                            translations[
                                                responseErrors['Tags']
                                            ] || responseErrors['Tags']
                                        "
                                        :externalOptiosMod="modifyTagsOptions"
                                        :labelText="
                                            translations[
                                                'Tags helps people finding similar content'
                                            ]
                                        "
                                        :no-margin="true"
                                        :options="modelValue.tags_options"
                                        :request-on-search="true"
                                        :searchable="true"
                                        :watch-options="true"
                                        api-url="/admin/api/core/customers/tags"
                                        mode="tags"
                                    />
                                </SettingsCard>
                            </div>
                        </b-col>
                    </b-row>
                </template>

                <template #advanced>
                    <b-row>
                        <b-col cols="8">
                            <SettingsCard
                                :collapsible="true"
                                :open="true"
                                :title="translations['Advanced settings']"
                            >
                                <InputComponent
                                    v-model="item.seo_title"
                                    :column-size="12"
                                    :column-style="true"
                                    :error="
                                        translations[
                                            responseErrors['seo_title']
                                        ] || responseErrors['seo_title']
                                    "
                                    :label-text="translations['SEO page title']"
                                    :no-margin="true"
                                    :placeholder="
                                        translations[
                                            'Enter unique SEO page title'
                                        ]
                                    "
                                />
                                <InputComponent
                                    v-model="item.url_handle"
                                    :colored="true"
                                    :column-size="12"
                                    :column-style="true"
                                    :error="
                                        translations[responseErrors['url']] ||
                                        responseErrors['url']
                                    "
                                    :label-text="translations['URL']"
                                    :no-margin="true"
                                    :placeholder="
                                        translations['Enter SEO friendly URL']
                                    "
                                    :unit="'/article/'"
                                    unit_position="left"
                                    @blur="handleSlugify"
                                />
                                <div class="settings-card-cloudio">
                                    <TextareaComponent
                                        v-model="item.seo_description"
                                        :column-size="12"
                                        :column-style="true"
                                        :error="
                                            translations[
                                                responseErrors[
                                                    'seo_description'
                                                ]
                                            ] ||
                                            responseErrors['seo_description']
                                        "
                                        :label-text="
                                            translations['SEO meta description']
                                        "
                                        :no-margin="true"
                                        :placeholder="
                                            translations[
                                                'Enter unique SEO meta description'
                                            ]
                                        "
                                    />
                                    <CloudioBar
                                        type="description"
                                        :description="
                                            translations['Genereate text']
                                        "
                                        @open="
                                            (value) => openCloudioModal(value)
                                        "
                                    />
                                </div>

                                <SeoGooglePreview
                                    :label-text="translations['Google preview']"
                                    :no-margin="true"
                                    :description="item.seo_description"
                                    :title="item.seo_title"
                                    :url="articleUrl"
                                />
                            </SettingsCard>
                        </b-col>
                    </b-row>
                </template>
            </DefaultLayout>
        </SettingsForm>
        <div class="d-flex justify-content-end mt-4">
            <b-button
                variant="primary"
                :disabled="submitLoader"
                @click="createOrEdit"
            >
                <b-spinner v-if="submitLoader" small></b-spinner>
                {{ translations["Save"] }}
            </b-button>
        </div>
    </div>

    <CreateCategory
        v-model="modal"
        v-model:data="dataRow"
        :model="modelCategories"
        @success="handleRecords"
        @delete:image="handleDeleteImage"
    />
</template>

<script>
import axios from "axios";

import { toast } from "@js/toast";
import { buildFormData } from "@js/shippingHelpers";
import useSharedCloudioState from "../components/composible/useSharedCloudioState";
import modelCategories from "../../Categories/js/Categories";

import CreateCategory from "../../Categories/components/CreateOrEdit.vue";
import Multiselect from "@vueform/multiselect";
import Loading from "@components/Loading";
import SettingsBox from "@components/SettingsBox/SettingsBox";
import SettingsForm from "@components/SettingsForm/SettingsForm";
import SettingsCard from "@components/SettingsForm/Helpers/SettingsCard";
import DefaultLayout from "@components/SettingsForm/Layouts/DefaultLayout";
import InputComponent from "@components/Form/InputComponent";
import TextareaComponent from "@components/Form/TextareaComponent";
import TextEditor from "@components/Form/TextEditor";
import LogoSection from "@components/Form/LogoSection";
import SeoGooglePreview from "@components/SeoGooglePreview";
import ActiveSwitch from "@components/Form/ActiveSwitch";
import RadioComponent from "@components/Form/RadioComponent";
import SelectWithAjax from "@components/Form/SelectWithAjax";
import CloudioBar from "./Helpers/CloudioBar.vue";

export default {
    components: {
        Loading,
        SettingsBox,
        SettingsForm,
        SettingsCard,
        DefaultLayout,
        InputComponent,
        TextareaComponent,
        TextEditor,
        LogoSection,
        SeoGooglePreview,
        ActiveSwitch,
        RadioComponent,
        SelectWithAjax,
        CloudioBar,
        Multiselect,
        CreateCategory,
    },
    props: {
        modelValue: {
            type: Boolean,
            required: false,
        },
        model: {
            type: Object,
            default: {},
        },
        data: {
            default: null,
        },
    },
    setup() {
        const { openCloudioModal, type, cloudioModal } =
            useSharedCloudioState();

        return {
            openCloudioModal,
            type,
            cloudioModal,
        };
    },
    data() {
        return {
            isVisible: false,
            modal: false,
            submitLoader: false,
            loading: false,
            dataRow: null,
            modelCategories: new modelCategories(),
            item: {
                name: null,
                url_handle: "",
                content: "",
                author_id: null,
                blog_id: null,
                category_id: null,
                url: "",
                seo_title: "",
                seo_description: "",
                active: "no",
                image: null,
                category_id: null,
                tags_array: [],
            },
            responseErrors: {},
            translations: {
                Cancel: this.$t("Cancel"),
                Tags: this.$t("Tags"),
                Save: this.$t("Save"),
                Title: this.$t("Title"),
                Author: this.$t("Author"),
                Name: this.$t("Name"),
                Content: this.$t("Content"),
                Description: this.$t("Description"),
                URL: this.$t("URL"),
                "Write article": this.$t("Write article"),
                "Genereate text": this.$t("Genereate text"),
                "Enter tags": this.$t("Enter tags"),
                Select: this.$t("Select"),
                "Tags helps people finding similar content": this.$t(
                    "Tags helps people finding similar content"
                ),
                "Create category": this.$t("Create category"),
                "Choose an author for this article": this.$t(
                    "Choose an author for this article"
                ),
                "Article title": this.$t("Article title"),
                "Enter SEO friendly URL": this.$t("Enter SEO friendly URL"),
                "Saved successfully": this.$t("Saved successfully"),
                "General settings": this.$t("General settings"),
                "Advanced settings": this.$t("Advanced settings"),
                "Edit blog category": this.$t("Edit blog category"),
                "Blog category": this.$t("Blog category"),
                //
                "SEO page title": this.$t("SEO page title"),
                "Enter unique SEO page title": this.$t(
                    "Enter unique SEO page title"
                ),
                "SEO meta description": this.$t("SEO meta description"),
                "Enter unique SEO meta description": this.$t(
                    "Enter unique SEO meta description"
                ),
                "Google preview": this.$t("Google preview"),
            },
        };
    },
    methods: {
        openCategorySidebar() {
            this.modal = true;
            this.dataRow = null;
        },
        handleDeleteImage(id) {
            this.data.data.forEach((item) => {
                if (item.id === id) {
                    item.image = null;
                    item.img = null;
                }
            });
        },
        handleRecords(data, isEdit) {
            if (isEdit) {
                tableData.data = tableData.data.map((x) => {
                    if (x.id === data.id) {
                        return {
                            ...data,
                            deleteRow: this.deleteRow,
                            openModal: this.openModal,
                        };
                    }
                    return {
                        ...x,
                        deleteRow: this.deleteRow,
                        openModal: this.openModal,
                    };
                });
            } else {
                this.tableData?.data?.unshift({
                    ...data,
                    deleteRow: this.deleteRow,
                    openModal: this.openModal,
                });
            }
        },
        async getArticle() {
            this.loading = true;
            try {
                const data = await this.model.find(this.data.id);
                this.item = { ...data };
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.loading = false;
            }
        },
        async createOrEdit() {
            try {
                this.submitLoader = true;
                this.responseErrors = {};

                const body = {
                    name: this.item.name,
                    content: this.item.content,
                    author_id: this.item.author_id,
                    blog_id: this.item.blog_id,
                    category_id: this.item.category_id,
                    tags_array: this.item.tags_array,
                    image: this.item.image,
                    url_handle: this.item.url_handle,
                    seo_title: this.item.seo_title,
                    seo_description: this.item.seo_description,
                    active: this.item.active,
                };

                const formData = new FormData();
                buildFormData(formData, body);

                const headers = {
                    headers: {
                        "Content-Type": "multipart/form-data",
                    },
                };

                let response;
                if (!this.data) {
                    response = await axios.post(
                        "/admin/api/core/blog/articles",
                        formData,
                        headers
                    );
                    this.$emit("success", { ...response.data }, true);
                } else {
                    response = await axios.post(
                        `/admin/api/core/blog/articles/${this.item.id}`,
                        formData,
                        headers
                    );
                    this.$emit("success", response.data, false);
                }

                if (this.reloadData) {
                    await this.reloadData(false);
                }

                toast.success(this.translations["Saved successfully"]);
                this.modal = false;
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.submitLoader = false;
            }
        },
    },
    computed: {
        logoImage: {
            get() {
                return this.item.image;
            },
            set(val) {
                this.item.image = val;
            },
        },
        articleUrl() {
            return this.item.url_handle
                ? `${this.articleBaseUrl}${this.item.url_handle}`
                : "";
        },
        articleBaseUrl() {
            const { width } = useWindowSize();
            return width.value < 768
                ? "/article/"
                : `${this.serverSettings("host")}/article/`;
        },
        deleteLogoImageUrl() {
            return this.item.id && this.item.image
                ? `/admin/api/core/blog/articles/${this.item.id}/delete-image`
                : null;
        },
    },
    watch: {
        item: {
            deep: true,
            handler(newVal) {
                this.item.url = `${this.articleBaseUrl}${newVal.url_handle}`;
            },
        },
        modelValue(val) {
            this.modal = val;
            if (val) {
                if (this.data) {
                    this.getArticle();
                } else {
                    this.loading = false;
                }
            }
        },
        modal(val) {
            if (!val) {
                this.responseErrors = {};
                this.item = {
                    name: null,
                    url_handle: "",
                    content: "",
                    author_id: null,
                    blog_id: null,
                    category_id: null,
                    tags: [],
                    image: null,
                    seo_title: "",
                    seo_description: "",
                    active: "no",
                    url: "",
                };
                this.loading = false;
                this.$emit("update:data", null);
            }
            this.$emit("update:modelValue", val);
        },
    },
    emits: ["update:modelValue", "update:data", "delete:image", "success"],
};
</script>
<style scoped>
.settings-card-cloudio {
    background: #fff;

    &.bordered-card {
        border: var(--space-size-1, 1px) solid
            var(--Color-Border-Neutral---cc-color-border-disabled, #e6e7eb);
        border-radius: 8px;
    }
    .card-body {
        display: flex;
        flex-direction: column;
    }
    .card-body hr {
        margin: 0 !important;
    }
}
</style>
