<template>
    <div class="cloudio-bottom-bar" >
        <div class="text">
            <p class="label-500 m-0">
                {{ translations["Write with ShopperPen by"] }}

                <a
                    href="/admin/apps/cloudio/shopper_pen_advanced"
                    target="_blank"
                >
                    {{ translations["CloudIO app"] }}
                </a>
            </p>
            
            <span class="text-400-secondary">
                {{
                    translations[
                        "Boost your sales with ShopperPen: Precision-crafted product descriptions, summaries, meta titles & descriptions!"
                    ]
                }}
            </span>
        </div>
        <a
            class="btn btn-primary gradient-border d-flex align-items-center gap-1 flex-norap text-nowrap"
            ref="triggerPanel"
            @click.prevent="handleOpenCloudio"
        >
            <svg
                width="22"
                height="18"
                viewBox="0 0 22 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M0 4C0 1.79086 1.79086 0 4 0H18C20.2091 0 22 1.79086 22 4V14C22 16.2091 20.2091 18 18 18H4C1.79086 18 0 16.2091 0 14V4Z"
                    fill="white"
                />
                <path
                    d="M9.4707 5.76367L7.03906 13H5.33398L8.52148 4.46875H9.61133L9.4707 5.76367ZM11.5039 13L9.06055 5.76367L8.91406 4.46875H10.0098L13.2148 13H11.5039ZM11.3926 9.83008V11.1016H6.81641V9.83008H11.3926ZM15.8105 4.46875V13H14.1992V4.46875H15.8105Z"
                    fill="#8D58E0"
                />
            </svg>
            {{ description }}
        </a>
    </div>
</template>
<script>
import useSharedCloudioState from "../composible/useSharedCloudioState";

export default {
    name: "CloudioBar",
    props: {
        type: {
            type: String,
        },
        description: {
            type: String,
        }
    },
    setup() {
        const { isCloudioActivated } = useSharedCloudioState();

        return {
            isCloudioActivated,
        };
    },
    data() {
        return {
            modal: this.modelValue,
            translations: {
                "Write with ShopperPen by": this.$t("Write with ShopperPen by"),
                "CloudIO app": this.$t("CloudIO app"),
                "Boost your sales with ShopperPen: Precision-crafted product descriptions, summaries, meta titles & descriptions!":
                    this.$t(
                        "Boost your sales with ShopperPen: Precision-crafted product descriptions, summaries, meta titles & descriptions!"
                    ),
                "Generate text": this.$t("Generate text"),
            },
        };
    },
    methods: {
        handleOpenCloudio() {
            this.$emit("open", this.type);
        },
    },
    emits: ["open"],
};
</script>
<style lang="scss">
.cloudio-bottom-bar {
    position: relative;
    z-index: 5;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    gap: 64px;
    align-items: center;
    width: calc(100% + 1px);
    padding: 16px;
    border-radius: 0px 0px 8px 8px;
    background: var(--color-purple-100, #f7f2ff);
    // margin-top: -1px;

    @media (max-width: 767px) {
        gap: 32px;
    }

    @media (max-width: 575px) {
        gap: 12px;
        flex-wrap: wrap;
        a.btn {
            align-self: self-end;
            margin-left: auto;
            width: 100%;
            justify-content: center;
        }
    }

    .text {
        display: flex;
        flex-direction: column;
    }
}
</style>
