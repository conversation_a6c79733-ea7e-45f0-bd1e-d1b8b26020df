<template>
    <b-modal
        v-model="openModal"
        :no-close-on-esc="true"
        :no-close-on-backdrop="true"
        :no-footer="true"
        :no-header="true"
        centered
        size="md"
    >
        <b-row v-if="titleX">
            <b-col
                class="col-12 icon-title d-flex align-items-center justify-content-between gap-2 text-nowrap mb-3"
            >
                <slot name="icon"></slot>
                <p class="label-500-16px m-0 text-wrap" v-html="titleX"></p>
                <i
                    style="cursor: pointer"
                    v-if="iconX"
                    class="modal-icon"
                    @click.prevent="cancelAction"
                    :class="iconX"
                ></i>
            </b-col>
        </b-row>
        <b-row>
            <b-col>
                <slot>
                    <p
                        v-if="subtitle"
                        class="label-500-14px m-0 text-wrap"
                        v-html="subtitle"
                    ></p>

                    <span
                        class="value-400"
                        v-html="
                            message ||
                            translations[
                                'Are you are sure you want to delete? Caution: This action cannot be undone.'
                            ]
                        "
                    ></span>
                </slot>
            </b-col>
            <b-col class="col-12" v-if="showHr">
                <hr style="margin: 16px 0" />
            </b-col>
        </b-row>
        <b-row>
            <b-col
                class="col-12 d-flex gap-2 align-items-center justify-content-end"
            >
                <button
                    v-if="showCancelButton"
                    @click.prevent="cancelAction"
                    class="btn btn-ghost"
                    :disabled="rejectLoader || submitLoader"
                >
                    <b-spinner small v-if="rejectLoader"></b-spinner>
                    {{ cancelLabel || translations["Cancel"] }}
                </button>

                <button
                    v-if="showSpamButton"
                    @click.prevent="pendingAction"
                    class="btn btn-danger"
                    :disabled="submitLoader || rejectLoader"
                >
                    <b-spinner small v-if="submitLoader"></b-spinner>
                    {{ spamLabel || translations["Mark as spam"] }}
                </button>

                <button
                    v-if="showApproveButton"
                    @click.prevent="okAction"
                    class="btn btn-primary"
                    :disabled="submitLoader || rejectLoader"
                >
                    <b-spinner small v-if="submitLoader"></b-spinner>
                    {{ approveLabel || translations["Approve comment"] }}
                </button>
            </b-col>
        </b-row>
    </b-modal>
</template>

<script>
export default {
    name: "CommentsModal",
    props: {
        showApproveButton: { type: Boolean, default: false },
        showSpamButton: { type: Boolean, default: false },
        showCancelButton: { type: Boolean, default: true },
        approveLabel: { type: String, default: null },
        spamLabel: { type: String, default: null },
        cancelLabel: { type: String, default: null },
        titleX: {
            type: String,
            default: null,
        },
        iconX: {
            type: String,
            default: null,
        },
        confirmButtonClass: {
            default: "btn-danger",
            type: [String, null],
            required: false,
        },
        subtitle: {
            required: false,
            type: [String, null],
        },
        message: {
            required: false,
            type: [String, null],
        },
        pendingComments: {
            required: false,
            type: [String, null],
        },
        approved: {
            required: false,
            type: [String, null],
        },
        spam: {
            required: false,
            type: [String, null],
        },
        modelValue: {
            required: true,
            type: Boolean,
            default: false,
        },
        submitLoader: {
            type: Boolean,
            default: false,
        },
        rejectLoader: {
            type: Boolean,
            default: false,
        },
        showHr: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            translations: {
                "Mark as spam": this.$t("Mark as spam"),
                "Approve comment": this.$t("Approve comment"),
                Cancel: this.$t("Cancel"),
            },
            openModal: this.open,
            action: {},
        };
    },
    methods: {
        okAction() {
            this.$emit("ok");
        },
        pendingAction() {
            this.$emit("pending");
        },
        cancelAction() {
            this.openModal = false;
            this.$emit("cancel");
        },
    },
    watch: {
        openModal(value) {
            this.$emit("update:modelValue", value);
        },
        modelValue(value) {
            this.openModal = value;
        },
    },
    emits: ["ok", "cancel", "update:modelValue"],
};
</script>

<style>
.modal-confirm .modal-footer {
    display: flex;
    justify-content: center;
}

.modal-confirm .modal-footer button:nth-child(1) {
    order: 2;
}

.modal-confirm .modal-footer button:nth-child(2) {
    order: 1;
}

.modal-confirm .icon-title {
    display: flex;
    align-items: center;
}

.modal-confirm .icon-title .modal-icon {
    margin-right: 8px;
}

.modal-confirm .icon-title .modal-title {
    flex-grow: 1;
}
</style>
