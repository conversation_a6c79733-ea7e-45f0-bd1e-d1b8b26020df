import axios from "axios";
import { toast } from "@js/toast";

export default {
    data() {
        return {
            openModal: false,
            targetStatusOnOk: null,
            targetStatusOnCancel: null,
            translations: {
                approved: this.$t("Approved"),
                spam: this.$t("Spam"),
                manage: this.$t("Manage"),
                pending: this.$t("Manage"),
                Cancel: this.$t("Cancel"),
                "Manage comment": this.$t("Manage comment"),
                "added a comment:": this.$t("added a comment:"),
                "Approve comment": this.$t("Approve comment"),
                "Saved successfully": this.$t("Saved successfully"),
                "Mark as spam": this.$t("Mark as spam"),
            },
        };
    },
    computed: {
        shortComment() {
            const comment = this.data.comment || "";
            return comment.length > 40 ? comment.slice(0, 40) + "..." : comment;
        },
        showSpamButton() {
            return (
                this.data.status === "approved" ||
                this.data.status === "pending"
            );
        },
        showApproveButton() {
            return (
                this.data.status === "spam" || this.data.status === "pending"
            );
        },
        showCancelButton() {
            return true;
        },
        translatedStatus() {
            const status = this.data.status;
            return this.translations[status] || this.capitalize(status);
        },
        statusClasses() {
            switch (this.data.status) {
                case "approved":
                    return "cc-badge-status cc-tag-status--enabled";
                case "spam":
                    return "cc-badge-status cc-tag-status--required";
                default:
                    return "cc-badge-status";
            }
        },
    },
    methods: {
        capitalize(word) {
            if (!word) return "";
            return word.charAt(0).toUpperCase() + word.slice(1);
        },
        handleStatusClick() {
            switch (this.data.status) {
                case "spam":
                    this.targetStatusOnOk = "approved";
                    this.targetStatusOnCancel = null;
                    break;
                case "approved":
                    this.targetStatusOnOk = "spam";
                    this.targetStatusOnCancel = null;
                    break;
                case "pending":
                    this.targetStatusOnOk = "approved";
                    this.targetStatusOnCancel = "spam";
                    break;
            }
            this.openModal = true;
        },
        async onModalOk() {
            try {
                if (!this.targetStatusOnOk) return;
                await axios.post("/admin/api/core/blog/comments/status", {
                    ids: [this.data.id],
                    comment: this.data.comment,
                    status: this.targetStatusOnOk,
                });
                this.data.status = this.targetStatusOnOk;
                toast.success(this.translations["Saved successfully"]);
            } catch (error) {
                console.error("Error updating comment status:", error);
            } finally {
                this.openModal = false;
                this.targetStatusOnOk = null;
                this.targetStatusOnCancel = null;
            }
        },
        async onModalPending() {
            try {
                await axios.post("/admin/api/core/blog/comments/status", {
                    ids: [this.data.id],
                    comment: this.data.comment,
                    status: "spam",
                });
                this.data.status = "spam";
                toast.success(this.translations["Saved successfully"]);
            } catch (error) {
                console.error("Error updating comment status:", error);
            } finally {
                this.openModal = false;
            }
        },
    },
};
