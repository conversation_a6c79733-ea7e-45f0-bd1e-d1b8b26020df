<template>
    <a href="javascript:void(0);" class="name-wrapper">
        <i
            style="color: #8d58e0"
            class="far fa-comment-alt rounded-2 gap-2 p-2 align-items-center justify-content-center d-flex icon"
        ></i>
        <div class="d-flex flex-column">
            <span>{{ data.name }} ({{ data.email }})</span>
            <a
                :href="URL"
                target="_blank"
                class="text-secondary cc-tooltip-dotted"
            >
                {{ data.article_name }}</a
            >
        </div>
    </a>
</template>

<script>

export default {
    name: "User",
    props: {
        data: {
            required: true,
            default: {},
            type: Object,
        },
        column: {
            required: true,
            default: {},
            type: Object,
        },
    },
    computed: {
        URL() {
            return `${this.serverSettings("host")}/admin/blog/article/edit/${
                this.data.item_id
            }`;
        },
    },
};
</script>

<style lang="scss" scoped>
.name-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    gap: var(--Spacing---cc-space-md, 16px);
}

.icon {
    background: #f6f7fb;
    width: 36px;
    height: 36px;
}
</style>
