<template>
    <SettingsWrapper
        setting-key="blog-comments"
        :loading="false"
        :prevent-save="true"
        icon="far fa-comments-alt"
        :title="translations['Blog comments']"
        :header-button-classes="'col-sm-3'"
        :breadcrumb="[
            { text: translations['Marketing'], to: { name: 'admin.index' } },
            { text: translations['Blog'] },
        ]"
        :container-classes="'container-large'"
    >
        <template #settings>
            <div id="blog-comments-table">
                <data-table
                    v-bind="table"
                    v-model="ids"
                    @pagination-change-page="paginate"
                    :enable-mobile="true"
                    :filters="true"
                    app-name="blog-comments"
                    :get-data="getData"
                    :filter-options="filterOptions"
                    v-model:query="query"
                >
                    <TableActions
                        v-model="ids"
                        app-key="blog-comments"
                        @success="handleActions"
                        delete-type="delete"
                        delete-url="/admin/api/core/blog/comments"
                        :enable-mobile="true"
                    />
                </data-table>
            </div>
        </template>
    </SettingsWrapper>
</template>

<script>
import { markRaw } from "vue";
import { toast } from "@js/toast";
import Comments from "../js/Comments";

import DataTable from "@components/Table";
import TableActions from "@components/TableActions";

import SettingsWrapper from "@components/SettingsWrapper/SettingsWrapper";
import ConfirmModal from "@components/ConfirmModal";

import User from "./Table/User.vue";
import Date from "./Table/Date.vue";
import Delete from "./Table/Delete.vue";
import Comment from "./Table/Comment.vue";
import Status from "./Table/Status.vue";

export default {
    components: {
        DataTable,
        TableActions,
        SettingsWrapper,
        ConfirmModal,
        User,
        Date,
        Delete,
    },
    props: ["app"],
    data() {
        return {
            modal: false,
            isLoading: true,
            data: {
                data: [],
            },
            ids: [],
            dataRow: null,
            page: this.$route.query?.page || 1,
            perPage: this.$route.query?.perpage || 25,
            query: this.$route.query,
            model: new Comments(),
            translations: {
                Name: this.$t("Name"),
                Date: this.$t("Date"),
                Comment: this.$t("Comment"),
                "Blog comments": this.$t("Blog comments"),
                Marketing: this.$t("Marketing"),
                Blog: this.$t("Blog"),
                "Deleted successfully": this.$t("Deleted successfully"),
                "Error while deleting": this.$t("Error while deleting"),
                Date: this.$t("Date"),
                Status: this.$t("Status"),
                Article: this.$t("Article"),
            },
        };
    },
    computed: {
        table() {
            return {
                data: this.data,
                isLoading: this.isLoading,
                defaultSorting: [{ key: "id", sortingMode: "desc" }],
                // sortingMode: 'multiple',
                columns: [
                    {
                        column: "name",
                        key: "name",
                        sortable: false,
                        title: this.translations["User"],
                        component: markRaw(User),
                        isMain: true,
                    },
                    {
                        column: "comment",
                        key: "comment",
                        sortable: false,
                        title: this.translations["Comment"],
                        component: markRaw(Comment),
                    },
                    {
                        column: "date_added",
                        key: "date_added",
                        sortable: true,
                        title: this.translations["Date"],
                        component: markRaw(Date),
                    },
                    {
                        column: "status",
                        key: "status",
                        sortable: true,
                        title: this.translations["Status"],
                        component: markRaw(Status),
                    },
                    {
                        column: "actions",
                        key: "actions",
                        title: " ",
                        sortable: false,
                        component: markRaw(Delete),
                    },
                ],
            };
        },
        filterOptions() {
            return [
                {
                    key: "article_id",
                    label: this.translations["Article"],
                    type: "select",
                    url: "/admin/api/core/blog/articles",
                },
                {
                    key: "author_id",
                    label: this.translations["Blog"],
                    type: "select",
                    url: "/admin/api/core/blog/comments",
                },
            ];
        },
    },
    methods: {
        async paginate(page) {
            this.page = page;
            await this.getData(true);
        },
        openModal(item) {
            this.dataRow = _.clone(item);
            this.modal = true;
        },
        async getData(isLoading = true) {
            try {
                this.isLoading = isLoading;

                this.model.where(this.query);

                const tableData = await this.model.paginate(
                    this.query?.page || this.page,
                    this.query?.perpage || this.perPage
                );

                tableData.data = tableData.data.map((x) => {
                    return {
                        ...x,
                        deleteRow: this.deleteRow,
                        openModal: this.openModal,
                    };
                });

                this.data = tableData;

                this.$router.push({
                    query: {
                        ...this.$route.query,
                        page: this.data.current_page,
                    },
                });
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.isLoading = false;
            }
        },
        async deleteRow(item) {
            try {
                item.loading = true;

                const response = await this.model.deleteBulk([item.id]);
                if (response && response?.status === "error") {
                    throw new Error(response?.msg);
                }
                this.data.data = this.data.data.filter((x) => x.id !== item.id);

                document.querySelectorAll(".tooltip").forEach((tooltip) => {
                    tooltip.parentNode.removeChild(tooltip);
                });

                toast.success(this.translations["Deleted successfully"]);
            } catch (error) {
                console.log(error);
                this.$errorResponse(error);
                toast.error(this.translations["Error while deleting"]);
            } finally {
                delete item.loading;
            }
        },
        handleActions(value) {
            if (value.action === "delete") {
                this.data.data = this.data.data.filter(
                    (item) => !this.ids.includes(item.id)
                );
                if (this.data.data.length === 0 && this.page > 1) {
                    this.page -= 1;
                    this.getData();
                }
            }
        },
    },
};
</script>
