<template>
    <a href="javascript:void(0);" class="name-wrapper">
        <div class="d-flex flex-column">
            <template
                v-if="data.status === 'manage' || data.status === 'pending'"
            >
                <a @click="handleStatusClick" class="btn btn-ghost btn-xs">
                    <i class="far fa-tasks"></i>
                    {{ translations.manage }}
                </a>
            </template>
            <template v-else>
                <span
                    :class="statusClasses"
                    @click="handleStatusClick"
                    style="cursor: pointer"
                >
                    {{ translatedStatus }}
                </span>
            </template>
        </div>
    </a>

    <CommentsModal
        v-model="openModal"
        :titleX="translations['Manage comment']"
        :subtitle="`${data.name} ${translations['added a comment:']}`"
        :message="data.comment"
        :iconX="'far fa-times'"
        :showApproveButton="showApproveButton"
        :showSpamButton="showSpamButton"
        :showCancelButton="showCancelButton"
        :approveLabel="translations['Approve comment']"
        :spamLabel="translations['Mark as spam']"
        :cancelLabel="translations['Cancel']"
        @ok="onModalOk"
        @pending="onModalPending"
    />
</template>

<script>
import CommentsModal from "./Helpers/CommentsModal.vue";
import commentStatusMixin from "./mixin/commentStatusMixin";

export default {
    name: "Status",
    components: {
        CommentsModal,
    },
    mixins: [commentStatusMixin],
    props: {
        data: {
            required: true,
            default: () => ({}),
            type: Object,
        },
        column: {
            required: true,
            default: () => ({}),
            type: Object,
        },
    },
};
</script>

<style lang="scss" scoped>
.name-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    gap: var(--Spacing---cc-space-md, 16px);

    &:hover {
        color: #8d58e0;
    }
}
</style>
