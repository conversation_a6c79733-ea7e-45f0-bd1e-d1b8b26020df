let routes = [
    {
        path: "/admin/marketing-new/blog/categories",
        name: "blog-categories",
        component: () => import("../Categories/components/Index.vue"),
    },
    {
        path: "/admin/marketing-new/blog/articles",
        name: "blog-articles",
        component: () => import("../Articles/components/Index.vue"),
         children: [
            {
                path: "add", 
                name: "articles-add",
                component: () => import("../Articles/components/CreateOrEdit.vue"),
            },
            {
                path: "edit/:id", 
                name: "articles-edit",
                component: () => import("../Articles/components/CreateOrEdit.vue"),
                // props: true, 
            }
        ]
    },
    {
        path: "/admin/marketing-new/blog/comments",
        name: "blog-comments",
        component: () => import("../Comments/components/Index.vue"),
    }
]

export {
    routes
}
