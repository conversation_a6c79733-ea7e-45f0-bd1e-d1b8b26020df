<template>
    <b-modal
        v-model="modal"
        :no-close-on-backdrop="true"
        :no-footer="true"
        body-class="edit-settings-modal-content"
        class="modal-right"
        header-class="edit-settings-modal-header"
        size="xl"
    >
        <template #header>
            <div class="col-8">
                <h5
                    class="settings-modal-title"
                    v-html="
                        translations[
                            data ? 'Edit blog category' : 'Add blog category'
                        ]
                    "
                ></h5>
            </div>
            <div
                class="col-4 text-right d-flex justify-content-end align-items-center gap-2"
            >
                <button
                    class="btn btn-white"
                    type="button"
                    @click="modal = false"
                    v-html="translations['Cancel']"
                ></button>

                <button
                    :disabled="submitLoader"
                    class="btn btn-primary"
                    type="button"
                    @click="createOrEdit"
                >
                    <b-spinner v-if="submitLoader" small></b-spinner>
                    {{ translations["Save"] }}
                </button>
            </div>
        </template>
        <Loading v-if="loading" :loading="loading" class="app-loader-center" />
        <SettingsForm v-else-if="!loading && modal">
            <DefaultLayout>
                <template #title>
                    <SettingsCard :title="translations['Blog category title']">
                        <InputComponent
                            v-model="item.name"
                            :column-size="12"
                            :column-style="true"
                            :error="
                                translations[responseErrors['name']] ||
                                responseErrors['name']
                            "
                            :no-margin="true"
                            :placeholder="translations['Title']"
                        />
                    </SettingsCard>
                </template>

                <template #comments>
                    <SettingsCard :title="translations['Comments settings']">
                        <ActiveSwitch
                            v-model:is-active="item.comments"
                            :error="
                                translations[responseErrors['comments']] ||
                                responseErrors['comments']
                            "
                            :labelText="
                                translations['Automatic comments approval']
                            "
                            :reverse="true"
                            :tooltip="true"
                            :tooltip-text="
                                translations[
                                    'If checked, all comments on articles within this category will be automatically posted.'
                                ]
                            "
                            false-value="no"
                            true-value="automatic"
                        />
                        <hr />
                        <RadioComponent
                            v-model="item.comments"
                            :disabled="item.comments === 'automatic'"
                            :is-disabled="item.comments === 'automatic'"
                            :options="radioOptions"
                            :stacked="false"
                            class="radio"
                            name="comments"
                        />
                    </SettingsCard>
                </template>

                <template #logo>
                    <SettingsCard>
                        <LogoSection
                            v-model:image="item.image"
                            v-model:response-errors="responseErrors"
                            :error="
                                translations[responseErrors['image']] ||
                                responseErrors['image']
                            "
                            :has_image="!!item.image"
                            :label="translations['Blog category image']"
                            :url="deleteLogoImageUrl"
                            @delete:image="$emit('delete:image', item.id)"
                        />
                    </SettingsCard>
                </template>

                <template #advanced>
                    <SettingsCard
                        :collapsible="true"
                        :title="translations['Advanced settings']"
                    >
                        <InputComponent
                            v-model="item.seo_title"
                            :column-size="12"
                            :column-style="true"
                            :error="
                                translations[responseErrors['seo_title']] ||
                                responseErrors['seo_title']
                            "
                            :label-text="translations['SEO page title']"
                            :no-margin="true"
                            :placeholder="
                                translations['Enter unique SEO page title']
                            "
                        />
                        <InputComponent
                            v-model="item.url_handle"
                            :colored="true"
                            :column-size="12"
                            :column-style="true"
                            :error="
                                translations[responseErrors['url']] ||
                                responseErrors['url']
                            "
                            :label-text="translations['URL']"
                            :no-margin="true"
                            :placeholder="
                                translations['Enter SEO friendly URL']
                            "
                            :unit="blogCategoriesBaseUrl"
                            unit_position="left"
                            @blur="handleSlugify"
                        />
                        <TextareaComponent
                            v-model="item.seo_description"
                            :column-size="12"
                            :column-style="true"
                            :error="
                                translations[
                                    responseErrors['seo_description']
                                ] || responseErrors['seo_description']
                            "
                            :label-text="translations['SEO meta description']"
                            :no-margin="true"
                            :placeholder="
                                translations[
                                    'Enter unique SEO meta description'
                                ]
                            "
                        />
                        <SeoGooglePreview
                            :description="item.seo_description"
                            :label-text="translations['Google preview']"
                            :no-margin="true"
                            :title="item.seo_title"
                            :url="item.url"
                        />
                    </SettingsCard>
                </template>
            </DefaultLayout>
        </SettingsForm>
    </b-modal>
</template>

<script>
import axios from "axios";
import slugify from "@js/slugify";

import { toast } from "@js/toast";
import { buildFormData } from "@js/shippingHelpers";
import { useWindowSize } from "@vueuse/core";
import Loading from "@components/Loading";
import SettingsBox from "@components/SettingsBox/SettingsBox";
import SettingsForm from "@components/SettingsForm/SettingsForm";
import SettingsCard from "@components/SettingsForm/Helpers/SettingsCard";
import DefaultLayout from "@components/SettingsForm/Layouts/DefaultLayout";
import InputComponent from "@components/Form/InputComponent";
import TextareaComponent from "@components/Form/TextareaComponent";
import TextEditor from "@components/Form/TextEditor";
import LogoSection from "@components/Form/LogoSection";
import SeoGooglePreview from "@components/SeoGooglePreview";
import ActiveSwitch from "@components/Form/ActiveSwitch";
import RadioComponent from "@components/Form/RadioComponent";

const { width } = useWindowSize();

export default {
    components: {
        Loading,
        SettingsBox,
        SettingsForm,
        SettingsCard,
        DefaultLayout,
        InputComponent,
        TextareaComponent,
        TextEditor,
        LogoSection,
        SeoGooglePreview,
        ActiveSwitch,
        RadioComponent,
    },
    props: {
        modelValue: {
            type: Boolean,
            required: false,
        },
        model: {
            type: Object,
            default: {},
        },
        data: {
            default: null,
        },
    },
    data() {
        return {
            isVisible: false,
            modal: false,
            submitLoader: false,
            loading: false,
            item: {
                name: null,
                comments: "automatic",
                image: null,
                seo_title: "",
                seo_description: "",
                type: "select",
            },
            imageFile: null,
            responseErrors: {},
            translations: {
                Cancel: this.$t("Cancel"),
                Save: this.$t("Save"),
                "Saved successfully": this.$t("Saved successfully"),
                "Blog category title": this.$t("Blog category title"),
                Title: this.$t("Title"),
                "Comments settings": this.$t("Comments settings"),
                "Enter SEO friendly URL": this.$t("Enter SEO friendly URL"),
                "Automatic comments approval": this.$t(
                    "Automatic comments approval"
                ),
                "Turn off comments": this.$t("Turn off comments"),
                "Comments need approval": this.$t("Comments need approval"),
                "Add blog category": this.$t("Add blog category"),
                "General settings": this.$t("General settings"),
                "Advanced settings": this.$t("Advanced settings"),
                Name: this.$t("Name"),
                Description: this.$t("Description"),
                "Edit blog category": this.$t("Edit blog category"),
                "Blog category image": this.$t("Blog category image"),
                URL: this.$t("URL"),
                "SEO page title": this.$t("SEO page title"),
                "Enter unique SEO page title": this.$t(
                    "Enter unique SEO page title"
                ),
                "SEO meta description": this.$t("SEO meta description"),
                "Enter unique SEO meta description": this.$t(
                    "Enter unique SEO meta description"
                ),
                "Google preview": this.$t("Google preview"),
                "If checked, all comments on articles within this category will be automatically posted.":
                    this.$t(
                        "If checked, all comments on articles within this category will be automatically posted."
                    ),
            },
        };
    },
    methods: {
        handleSlugify() {
            this.item.url_handle = slugify(this.item.url_handle);
        },
        async getBlogCategory() {
            this.loading = true;
            try {
                let data = await this.model.find(this.data.id);
                this.item = {
                    ...data,
                    comments: data.comments ?? "automatic",
                };
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.loading = false;
            }
        },
        async createOrEdit() {
            try {
                this.submitLoader = true;
                this.responseErrors = {};

                let body = {
                    name: this.item.name,
                    comments: this.item.comments,
                    image: this.item.image,
                    url_handle: this.item.url_handle,
                    seo_title: this.item.seo_title || this.item.name,
                    seo_description:
                        this.item.seo_description || this.item.name,
                };

                const formData = new FormData();
                buildFormData(formData, body);

                const headers = {
                    headers: {
                        "Content-Type": "multipart/form-data",
                    },
                };

                let response;
                if (!this.item.id) {
                    response = await this.model.create(formData, headers);
                } else {
                    response = await this.model.post(
                        this.item.id,
                        formData,
                        headers
                    );
                }

                this.$emit("success", response, !!this.item.id);
                toast.success(this.translations["Saved successfully"]);
                this.modal = false;
            } catch (error) {
                this.$errorResponse(error);
                console.log(error)
            } finally {
                this.submitLoader = false;
            }
        },
    },
    computed: {
        radioOptions() {
            return [
                { value: "no", text: this.translations["Turn off comments"] },
                {
                    value: "moderator",
                    text: this.translations["Comments need approval"],
                },
            ];
        },
        blogCategoriesBaseUrl() {
            if (width.value < 768) {
                return "/blog/categories/";
            } else {
                return `${this.serverSettings("host")}/blog/categories/`;
            }
        },
        deleteLogoImageUrl() {
            return this.item.id && !!this.item.image
                ? `/admin/api/core/blog/categories/${this.item.id}/delete-image`
                : null;
        },
    },
    watch: {
        item: {
            deep: true,
            handler(newVal) {
                this.item.url = `${this.blogCategoriesBaseUrl}${newVal.url_handle}`;
            },
        },
        modelValue(val) {
            this.modal = val;
            if (val) {
                if (this.data) {
                    this.getBlogCategory();
                } else {
                    this.loading = false;
                }
            }
        },
        modal(val) {
            if (!val) {
                this.responseErrors = {};
                this.item = {
                    name: null,
                    comments: "automatic",
                    image: null,
                    url_handle: "",
                    seo_title: "",
                    seo_description: "",
                };
                this.loading = true;
                this.$emit("update:data", null);
            }
            this.$emit("update:modelValue", val);
        },
    },
    emits: ["update:modelValue", "delete:image", "success"],
};
</script>
<style>
.tox-dialog__disable-scroll .modal {
    display: none !important;
}
</style>
