<template>
    <SettingsWrapper
        setting-key="blog-categories"
        :loading="false"
        :prevent-save="true"
        icon="far fa-typewriter"
        :title="translations['Blog categories']"
        :header-button-classes="'col-sm-3'"
        :breadcrumb="[
            { text: translations['Marketing'], to: { name: 'admin.index' } },
            { text: translations['Blog'] },
        ]"
        :container-classes="'container-large'"
    >
        <template #headerButton>
            <div class="d-flex flex-row align-items-center gap-3">
                <b-button
                    class="text-nowrap"
                    variant="primary"
                    @click="modal = true"
                >
                    <i class="far fa-plus"></i>
                    {{ translations["Add blog category"] }}
                </b-button>
            </div>
        </template>

        <template #settings>
            <div id="blog-categories-table">
                <data-table
                    v-model="ids"
                    v-model:query="query"
                    :enable-mobile="true"
                    :filters="true"
                    :get-data="getData"
                    app-name="blog-categories"
                    v-bind="table"
                    :filter-options="filterOptions"
                    @pagination-change-page="paginate"
                >
                    <TableActions
                        v-model="ids"
                        :enable-mobile="true"
                        app-key="blog-categories"
                        delete-type="delete"
                        delete-url="/admin/api/core/blog/categories"
                        @success="handleActions"
                    />
                </data-table>
            </div>

            <CreateOrEdit
                v-model="modal"
                v-model:data="dataRow"
                :model="model"
                @success="handleRecords"
                @delete:image="handleDeleteImage"
            />
        </template>
    </SettingsWrapper>
</template>

<script>
import { markRaw } from "vue";
import { toast } from "@js/toast";
import Categories from "../js/Categories";

import DataTable from "@components/Table";
import TableActions from "@components/TableActions";

import SettingsWrapper from "@components/SettingsWrapper/SettingsWrapper";
import CreateOrEdit from "./CreateOrEdit";

import Name from "./Table/Name.vue";
import Date from "./Table/Date.vue";
import Delete from "./Table/Delete.vue";
import Comment from "./Table/Comment.vue";

export default {
    components: {
        DataTable,
        TableActions,
        SettingsWrapper,
        CreateOrEdit,
        Name,
        Date,
        Comment,
    },
    props: ["app"],
    data() {
        return {
            modal: false,
            isLoading: true,
            data: {
                data: [],
            },
            ids: [],
            dataRow: null,
            page: this.$route.query?.page || 1,
            perPage: this.$route.query?.perpage || 25,
            query: this.$route.query,
            model: new Categories(),
            translations: {
                Name: this.$t("Name"),
                "Blog categories": this.$t("Blog categories"),
                "Add blog category": this.$t("Add blog category"),
                Products: this.$t("Products"),
                "Deleted successfully": this.$t("Deleted successfully"),
                "Error while deleting": this.$t("Error while deleting"),
                Yes: this.$t("Yes"),
                No: this.$t("No"),
                "ID #": this.$t("ID #"),
                "Created at": this.$t("Created at"),
                "Has products": this.$t("Has products"),
                "Updated at": this.$t("Updated at"),
                Marketing: this.$t("Marketing"),
                Categories: this.$t("Categories"),
                Blog: this.$t("Blog"),
                "Created at": this.$t("Created at"),
                "Updated at": this.$t("Updated at"),
                Comments: this.$t("Comments"),
                Automatic: this.$t("Automatic"),
                Modified: this.$t("Modified"),
                "Not modified": this.$t("Not modified"),
            },
        };
    },
    computed: {
        table() {
            return {
                data: this.data,
                isLoading: this.isLoading,
                defaultSorting: [{ key: "id", sortingMode: "desc" }],
                // sortingMode: 'multiple',
                columns: [
                    {
                        column: "name",
                        key: "name",
                        sortable: true,
                        title: this.translations["Name"],
                        component: markRaw(Name),
                        isMain: true,
                    },
                    {
                        column: "created_at",
                        key: "created_at",
                        sortable: true,
                        title: this.translations["Created at"],
                        component: markRaw(Date),
                    },
                    {
                        column: "updated_at",
                        key: "updated_at",
                        sortable: true,
                        title: this.translations["Updated at"],
                        component: markRaw(Date),
                    },
                    {
                        column: "comments",
                        key: "comments",
                        sortable: true,
                        title: this.translations["Comments"],
                        component: markRaw(Comment),
                    },
                    {
                        column: "actions",
                        key: "actions",
                        title: " ",
                        sortable: false,
                        component: markRaw(Delete),
                    },
                ],
            };
        },
        filterOptions() {
            return [
                {
                    label: this.translations["Comments"],
                    key: "comments",
                    options: [
                        {
                            value: "moderator",
                            label: this.translations["Modified"],
                        },
                        {
                            value: "no",
                            label: this.translations["Not modified"],
                        },
                        {
                            value: "automatic",
                            label: this.translations["Automatic"],
                        },
                    ],
                },
            ];
        },
    },
    methods: {
        handleRecords(data, isEdit) {
            if (isEdit) {
                tableData.data = tableData.data.map((x) => {
                    if (x.id === data.id) {
                        return {
                            ...data,
                            deleteRow: this.deleteRow,
                            openModal: this.openModal,
                        };
                    }
                    return {
                        ...x,
                        deleteRow: this.deleteRow,
                        openModal: this.openModal,
                    };
                });
            } else {
                this.tableData?.data?.unshift({
                    ...data,
                    deleteRow: this.deleteRow,
                    openModal: this.openModal,
                });
                this.getData();
            }
        },
        async paginate(page) {
            this.page = page;
            await this.getData(true);
        },
        openModal(item) {
            this.dataRow = _.clone(item);
            this.modal = true;
        },
        async getData(isLoading = true) {
            try {
                this.isLoading = isLoading;

                this.model.where(this.query);

                const tableData = await this.model.paginate(
                    this.query?.page || this.page,
                    this.query?.perpage || this.perPage
                );

                tableData.data = tableData.data.map((x) => {
                    return {
                        ...x,
                        deleteRow: this.deleteRow,
                        openModal: this.openModal,
                    };
                });

                this.data = tableData;

                this.$router.push({
                    query: {
                        ...this.$route.query,
                        page: this.data.current_page,
                    },
                });
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.isLoading = false;
            }
        },
        async deleteRow(item) {
            try {
                item.loading = true;

                this.data.data = this.data.data.filter((x) => x.id !== item.id);

                toast.success(this.translations["Deleted successfully"]);
            } catch (error) {
                console.log(error);
                this.$errorResponse(error);
                toast.error(this.translations["Error while deleting"]);
            } finally {
                delete item.loading;
            }
        },
        handleActions(value) {
            if (value.action === "delete") {
                this.data.data = this.data.data.filter(
                    (item) => !this.ids.includes(item.id)
                );
                if (this.data.data.length === 0 && this.page > 1) {
                    this.page -= 1;
                    this.getData();
                }
            }
        },
        handleDeleteImage(id) {
            this.data.data.forEach((item) => {
                if (item.id === id) {
                    item.image = null;
                    item.img = null;
                }
            });
        },
    },
};
</script>
