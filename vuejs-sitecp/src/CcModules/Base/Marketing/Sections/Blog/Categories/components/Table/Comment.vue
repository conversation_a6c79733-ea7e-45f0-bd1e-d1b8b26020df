<template>
    <a href="javascript:void(0);" class="name-wrapper">
        <div class="d-flex flex-column">
            <span :class="commentsClass">
                {{ translations[data.comments] || capitalize(data.comments) }}
            </span>
        </div>
    </a>
</template>

<script>
export default {
    name: "Comment",
    props: {
        data: {
            required: true,
            default: () => ({}),
            type: Object,
        },
        column: {
            required: true,
            default: () => ({}),
            type: Object,
        },
    },
    data() {
        return {
            translations: {
                automatic: this.$t("Automatic"),
                moderator: this.$t("Moderator"),
                no: this.$t("Off"),
            },
        };
    },
    computed: {
        commentsClass() {
            switch (this.data.comments) {
                case "automatic":
                    return "cc-badge-status cc-tag-status--enabled";
                case "moderator":
                    return "cc-badge-status cc-tag-status--disabled";
                case "no":
                    return "cc-badge-status cc-tag-status--yellow";
                default:
                    return "cc-badge-status";
            }
        },
    },
    methods: {
        capitalize(word) {
            if (!word) return "";
            return word.charAt(0).toUpperCase() + word.slice(1);
        },
    },
};
</script>

<style lang="scss" scoped>
.name-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    gap: var(--Spacing---cc-space-md, 16px);

    &:hover {
        color: #8d58e0;
    }
}
</style>
