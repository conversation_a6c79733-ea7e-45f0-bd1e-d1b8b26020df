<template>
    <button class="btn btn-white" @click="modal = true">
        <i class="fa-light fa-cloud-arrow-up"></i>
        {{ translations['Import redirects'] }}
    </button>
    <b-modal
        v-model="modal"
        :no-close-on-backdrop="true"
        :no-close-on-esc="true"
        :no-footer="true"
        body-class="bg-white px-4 pt-2 pb-5"
        class="modal-right"
        header-class="edit-settings-modal-header sticky"
        size="xl"
    >
        <template #header>
            <div
                class="d-flex justify-content-between align-items-center flex-wrap gap-2 w-100"
            >
                <div class="d-flex align-items-center gap-3">
                    <a
                        href="javascript:void(0);"
                        :class="{ 'pe-none': submitLoader }"
                        @click="modal = false"
                    >
                        <svg
                            width="12"
                            height="12"
                            viewBox="0 0 12 12"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M7.16016 6.25L11.5195 10.6094C11.625 10.7148 11.625 10.8906 11.5195 11.0312L10.7461 11.8047C10.6055 11.9102 10.4297 11.9102 10.3242 11.8047L9.58594 11.0664L6 7.44531L1.64062 11.8047C1.53516 11.9102 1.35938 11.9102 1.21875 11.8047L0.445312 11.0312C0.339844 10.8906 0.339844 10.7148 0.445312 10.6094L4.80469 6.25L0.445312 1.92578C0.339844 1.82031 0.339844 1.64453 0.445312 1.50391L1.21875 0.730469C1.35938 0.625 1.53516 0.625 1.64062 0.730469L6 5.08984L10.3242 0.730469C10.4297 0.625 10.6055 0.625 10.7461 0.730469L11.5195 1.50391C11.625 1.64453 11.625 1.82031 11.5195 1.92578L10.7812 2.66406L7.16016 6.25Z"
                                fill="#7A7D84"
                            />
                        </svg>
                    </a>
                    <h5
                        class="settings-modal-title"
                        v-html="translations['Import with CSV file']"
                    ></h5>
                </div>
                <div
                    class="d-flex gap-2 flex-grow-1 justify-content-end"
                >
                    <button
                        :disabled="submitLoader"
                        class="btn btn-ghost"
                        type="button"
                        @click="handleCancel"
                    >
                        <i v-if="step > 1 && step < 3" class="fa-light fa-chevron-left"></i>
                        {{ translations[step === 1 ? "Cancel" : step === 3 ? 'Close' : "Back"] }}
                    </button>
                    <button
                        v-if="step < 3"
                        :disabled="submitLoader || loading"
                        class="btn btn-primary"
                        type="button"
                        @click="handleNextStep"
                    >
                        <b-spinner v-if="submitLoader" small></b-spinner>
                        {{ translations[step === 2 ? "Submit" : "Next"] }}
                        <i class="fa-light fa-chevron-right me-0 ms-1"></i>
                    </button>
                </div>
            </div>
        </template>

        <Loading v-if="loading" :loading="loading" class="app-loader-center"/>
        <template v-else>
            <StepsTimeline v-if="step < 3"/>
            <Step1Csv v-if="step === 1"/>
            <Step2Csv v-if="step === 2"/>
            <SuccessRedirectsImport v-if="step === 3"/>
        </template>
    </b-modal>
</template>
<script>
import Csv from './../../js/Csv.js'
import useSharedRedirectsState from './../../js/useSharedRedirectsState.js';

import Loading from '@components/Loading';

import StepsTimeline from './Helpers/StepsTimeline.vue';
import Step1Csv from './Helpers/Step1Csv.vue';
import Step2Csv from './Helpers/Step2Csv.vue';
import SuccessRedirectsImport from "./Helpers/SuccessRedirectsImport.vue";

export default {
    name: "ImportRedirectsModal",
    components: {
        SuccessRedirectsImport,
        Loading,
        StepsTimeline,
        Step1Csv,
        Step2Csv
    },
    props: {},
    setup() {
        const {
            file,
            step,
            meta,
            submitLoader,
            modal,
            has_header_line,
            errorStep1,
            errorStep2,
            mappingId,
            map_options,
            import_binds,
            resetData
        } = useSharedRedirectsState()

        return {
            file,
            step,
            meta,
            submitLoader,
            modal,
            has_header_line,
            errorStep1,
            errorStep2,
            mappingId,
            map_options,
            import_binds,
            resetData
        }
    },
    data() {
        return {
            model: new Csv(),
            loading: true,
            responseErrors: {},
            translations: {
                'Import redirects': this.$t('Import redirects'),
                "Back": this.$t('Back'),
                "Submit": this.$t("Submit"),
                "Next": this.$t("Next"),
                "Cancel": this.$t("Cancel"),
                "Close": this.$t("Close"),
                'Import with CSV file': this.$t('Import with CSV file'),
                "Please select a file to import": this.$t("Please select a file to import"),
                "Please select a valid file type (csv, txt)": this.$t("Please select a valid file type (csv, txt)"),
                "The import binds.redirect.new url field is required.": this.$t("The import binds.redirect.new url field is required."),
                "The import binds.redirect.old url field is required.": this.$t("The import binds.redirect.old url field is required.")
            }
        }
    },
    methods: {
        handleCancel() {
            if ([1, 3].includes(this.step)) {
                this.modal = false
            } else {
                console.log(this.step)
                this.step -= 1
            }
        },
        async handleNextStep() {
            if (this.step === 1 && this.mappingId && this.file) {
                this.step = 2;
                return
            }
            this.submitLoader = true
            this.responseErrors = {}
            this.errorStep1 = null
            this.errorStep2 = {}

            const formData = new FormData()

            try {
                if (this.step === 1) {
                    if (!this.file) {
                        this.errorStep1 = this.translations["Please select a file to import"]
                        this.submitLoader = false
                        return
                    }
                    formData.append('import_file', this.file)
                    formData.append('has_header_line', this.has_header_line)
                    const response = await this.model.post('save/redirects', formData, {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    })
                    this.mappingId = response.id
                    await this.getMapOptions()
                    this.step = 2
                } else {
                    const response = await this.model.post(`mapping/${this.mappingId}`, {import_binds: this.import_binds})
                    this.step = 3
                }
            } catch (err) {
                this.$errorResponse(err)
            } finally {
                this.submitLoader = false
            }
        },
        async getMapOptions() {
            try {
                const response = await this.model.find(
                    `mapping/${this.mappingId}`
                );
                this.map_options = Object.entries(response).map(
                    ([id, name]) => {
                        this.translations[name] = this.$t(name);
                        return {id, name: this.translations[name]};
                    }
                );
                if (
                    this.meta.import_fields &&
                    Object.keys(this.meta.import_fields).length > 0
                ) {
                    Object.values(this.meta.import_fields).forEach((field) => {
                        Object.keys(field || []).forEach((key) => {
                            this.import_binds[key] = "";
                        });
                    });
                }
            } catch (error) {
                this.$errorResponse(error);
            }
        },
        async getMeta() {
            this.loading = true
            try {
                this.meta = await this.model.find('meta/redirects')
                this.has_header_line = this.meta?.default_settings?.has_header_line || 0

            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.loading = false;
            }
        }
    },
    watch: {
        modal: async function (value) {
            if (value) {
                await this.getMeta()
            } else {
                this.resetData()
                this.responseErrors = {}
                this.loading = true
                this.submitLoader = false
            }
        },
        responseErrors: {
            deep: true,
            handler(value) {
                if (value.import_file) {
                    if (!this.translations[value.import_file]) {
                        this.translations[value.import_file] = this.$t(value.import_file)
                    }
                    this.errorStep1 = this.translations[value.import_file] || value.import_file
                }
                let keys = Object.keys(value)
                let isMappingKeys = keys.some(x => x.includes('import_binds'))
                if (keys.length > 0 && isMappingKeys) {
                    keys.forEach(key => {
                        if (!this.translations[value[key]]) {
                            this.translations[value[key]] = this.$t(value[key])
                        }
                        this.errorStep2[key] = this.translations[value[key]] || value[key]
                    })
                }
            }
        }
    }

}
</script>
