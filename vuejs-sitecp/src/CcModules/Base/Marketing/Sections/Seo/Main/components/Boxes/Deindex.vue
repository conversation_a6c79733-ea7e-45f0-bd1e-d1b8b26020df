<template>
    <WrapperBox :loader="submitLoader" :open="isSettingsChanged" @revert="revertChanges" @save="saveChanges">
        <template #default>
            <ActiveSwitch
                v-model:is-active="allow_noindex_query_limit"
                :error="responseErrors.allow_noindex_query_limit"
                :is-disabled="submitLoader"
                :label-text="translations['Deindex Filtered and Sorted pages']"
            />
            <InputComponent
                v-model="noindex_query_limit"
                :column-style="true"
                :error="responseErrors.noindex_query_limit"
                :is-disabled="!allow_noindex_query_limit || submitLoader"
                :label-text="translations['Parameter value']"
                :max="5"
                :min="0"
                :no-margin="true"
                :step="1"
                type="number"
                :digits="0"
            />
        </template>
    </WrapperBox>
</template>
<script>
import {toast} from "@js/toast"

import ActiveSwitch from '@components/Form/ActiveSwitch'
import InputComponent from '@components/Form/InputComponent'
import WrapperBox from './../WrapperBox.vue'

import {SeoModel} from './../../js/Seo.js'

export default {
    name: 'deindex',
    components: {
        WrapperBox,
        ActiveSwitch,
        InputComponent
    },
    props: {
        modelValue: {
            default: {}
        }
    },
    data() {
        return {
            submitLoader: false,
            enable: false,
            limit: 0,
            model: new SeoModel(),
            responseErrors: {},
            translations: {
                'Deindex Filtered and Sorted pages': this.$t('Deindex Filtered and Sorted pages'),
                'Saved Successfully': this.$t('Saved Successfully'),
                'Parameter value': this.$t('Parameter value')
            }
        }
    },
    computed: {
        allow_noindex_query_limit: {
            get() {
                return this.modelValue.allow_noindex_query_limit
            },
            set(value) {
                this.$emit('update:modelValue', {
                        ...this.modelValue, allow_noindex_query_limit:
                        value
                    }
                )
            }
        },
        noindex_query_limit: {
            get() {
                return this.modelValue.noindex_query_limit
            },
            set(value) {
                this.$emit('update:modelValue', {
                        ...this.modelValue, noindex_query_limit:
                        value
                    }
                )
            }
        },
        isSettingsChanged() {
            return this.enable !== this.modelValue.allow_noindex_query_limit || this.limit !== this.modelValue.noindex_query_limit
        }
    },
    methods: {
        revertChanges() {
            this.responseErrors = {}
            this.$emit('update:modelValue', {
                ...this.modelValue,
                allow_noindex_query_limit: this.enable,
                noindex_query_limit: this.limit
            })
        },
        async saveChanges() {
            this.submitLoader = true
            this.responseErrors = {}

            try {
                await this.model.post('no-index-limit', {
                    allow_noindex_query_limit: this.allow_noindex_query_limit,
                    noindex_query_limit: this.noindex_query_limit
                })
                this.limit = this.noindex_query_limit
                this.enable = this.allow_noindex_query_limit
                toast.success(this.translations['Saved Successfully'])
            } catch (err) {
                this.$errorResponse(err)
            } finally {
                this.submitLoader = false
            }

        }
    },
    mounted() {
        this.enable = this.modelValue.allow_noindex_query_limit
        this.limit = this.modelValue.noindex_query_limit
    },
    emits: ['update:modelValue']
}
</script>
