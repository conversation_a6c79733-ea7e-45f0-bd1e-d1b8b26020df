<template>
    <SettingsWrapper
        v-model:responseErrors="responseErrors"
        v-model:settings="settings"
        :breadcrumb="breadcrumbs "
        :loading="loading"
        :prevent-save="true"
        :submitLoader="submitLoader"
        :title="translations['Main SEO settings']"
        :translation-labels="translations"
        icon="fa-brands fa-searchengin"
        setting-key="seo-main"
    >
        <template #settings>
            <b-row class="gy-3 mt-3">
                <template v-for="(box, index) in boxes" :key="index">
                    <SettingDescription
                        v-model="boxes[index]"
                        :box-key="box.key"
                    />
                    <b-col class="col-12 col-md-7">
                        <component :is="box.key" v-model="settings"/>
                    </b-col>
                    <b-col v-if="index < boxes.length -1" class="col-12">
                        <hr class="m-0"/>
                    </b-col>
                </template>
            </b-row>
        </template>
    </SettingsWrapper>
</template>
<script>
import {SeoModel} from './js/Seo.js'

import SettingsWrapper from "@components/SettingsWrapper/SettingsWrapper";
import SettingDescription from "@components/SettingDescription"

import canonical from './components/Boxes/Canonical.vue'
import deindex from './components/Boxes/Deindex.vue'
import metaPage from './components/Boxes/MetaPage.vue'
import sitemap from './components/Boxes/Sitemap.vue'
import robots from './components/Boxes/Robots.vue'
import sharing from './components/Boxes/Sharing.vue'
import rss from './components/Boxes/Rss.vue'


export default {
    name: "Merchant",
    components: {
        SettingsWrapper,
        SettingDescription,
        canonical,
        deindex,
        metaPage,
        sitemap,
        robots,
        sharing,
        rss
    },
    data() {
        return {
            loading: true,
            responseErrors: {},
            model: new SeoModel(),
            submitLoader: false,
            settings: {
                "canonical_is_active": 1,
                "allow_noindex_query_limit": 1,
                "noindex_query_limit": 1,
                "meta_page": "",
                "sitemap": "",
                "robots": [],
                "widget": {
                    "enabled": true,
                    "show_counter": "yes",
                    "show_compact": "yes",
                    "show_top_services": "yes",
                    "ui_click": "yes",
                    "layout": "small",
                    "ui_hover_direction": -1,
                    "custom_toolbar": ""
                },
                "og_image_url": null,
                "rss_feed_count": 20,
                "rss_url": ""
            },
            translations: {
                "Marketing": this.$t("Marketing"),
                "SEO settings": this.$t("SEO settings"),
                "Main SEO settings": this.$t("Main SEO settings")
            },
            boxes: [
                {
                    key: "canonical",
                    group: "seo-main",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Canonical tag",
                    infoTitleHelp: null,
                    infoDescription:
                        "\n" +
                        "The canonical tag (also called \"rel canonical\") is a way to tell search engines that a specific URL is the master copy of a page. Using the canonical tag prevents problems caused by identical or \"duplicate\" content appearing on multiple URLs.",
                    editMethod: "inline",
                    hideEditMethod: true
                },
                {
                    key: "deindex",
                    group: "seo-main",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Deindex Filtered and Sorted pages",
                    infoTitleHelp: null,
                    infoDescription:
                        "Set a noindex directive on any parameterized page (such as a category after a filter sort by price or other criteria) that does not add SEO value. This tag will prevent search engines from indexing the page. URLs with a noindex tag are also likely to be crawled less frequently and, if present for a long time, will eventually cause Google to not follow links on the page.",
                    editMethod: "inline",
                    hideEditMethod: true
                },
                {
                    key: "metaPage",
                    group: "seo-main",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Text to add pagination to meta title and description",
                    infoTitleHelp: null,
                    infoDescription:
                        "For the correct crawling of product pages, we use the so-called Self-canonical tag, which allows us to read all product pages from one category or manufacturer. To avoid duplication of meta information, we add text such as \"Page 2 -\" to it, which makes the content unique for each page. You can change the word \"Page\" to your liking.",
                    editMethod: "inline",
                    hideEditMethod: true
                },
                {
                    key: "sitemap",
                    group: "seo-main",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Sitemap.xml file",
                    infoTitleHelp: null,
                    infoDescription:
                        "A good XML sitemap acts as a roadmap of your site, guiding Google to all the important pages. XML sitemaps are a good SEO tool because they allow Google to quickly find the important pages on a website.",
                    editMethod: "inline",
                    hideEditMethod: true
                },
                {
                    key: "robots",
                    group: "seo-main",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Robots.xml file",
                    infoTitleHelp: null,
                    infoDescription:
                        "Robots.txt is a text file that is created to instruct web crawlers (usually search engines) how to crawl a website's pages. The robots.txt file is part of the Robot Exclusion Protocol (REP). The protocol is a group of web standards that governs how crawlers crawl a site, access and index content, and serve that content to users.",
                    editMethod: "inline",
                    hideEditMethod: true
                },
                {
                    key: "sharing",
                    group: "seo-main",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Share a product",
                    infoTitleHelp: null,
                    infoDescription:
                        "Share a product description",
                    editMethod: "inline",
                    hideEditMethod: true
                },
                {
                    key: "rss",
                    group: "seo-main",
                    title: null,
                    titleHelp: null,
                    infoTitle: "RSS file",
                    infoTitleHelp: null,
                    infoDescription:
                        "RSS stands for “really simple syndication,” also known as: “rich site feed.” At its core, RSS is a simple text file with basic up-to-date information—news, articles, and the like.",
                    editMethod: "inline",
                    hideEditMethod: true
                },
            ]
        }
    },
    computed: {
        breadcrumbs() {
            return [
                {
                    text: this.translations['Marketing'],
                    to: {name: 'marketing'}
                },
                {text: this.translations['SEO settings']}
            ]
        }
    },
    methods: {
        async getData() {
            this.loading = true
            try {
                let response = await this.model.all()
                if (response?.widget?.custom_toolbar === 'null' || !response?.widget?.custom_toolbar) {
                    response.widget.custom_toolbar = ''
                }
                this.settings = response
            } catch (err) {
                this.$errorResponse(err)
            } finally {
                this.loading = false
            }
        }
    },
    async created() {
        await this.getData()
        this.helpBoxes(this.boxes)
    }
};
</script>
