<template>
    <WrapperBox
        :loader="submitLoader"
        :open="isSettingsChanged"
        @revert="revertChanges"
        @save="saveChanges"
    >
        <template #default>
            <ActiveSwitch
                v-model:is-active="widget.enabled"
                :error="responseErrors.enabled"
                :is-disabled="submitLoader"
                :label-text="translations['Share product']"
            />
            <hr class="w-100 m-0" />
            <ActiveSwitch
                v-model:is-active="widget.show_counter"
                :error="responseErrors.show_counter"
                :is-disabled="submitLoader"
                :label-text="translations['Show share count']"
                false-value="no"
                true-value="yes"
            />
            <ActiveSwitch
                v-model:is-active="widget.show_compact"
                :error="responseErrors.show_counter"
                :is-disabled="submitLoader"
                :label-text="
                    translations['Show button for other social networks']
                "
                false-value="no"
                true-value="yes"
            />
            <ActiveSwitch
                v-model:is-active="widget.show_top_services"
                :error="responseErrors.show_top_services"
                :is-disabled="submitLoader"
                :label-text="translations['Show top networks']"
                false-value="no"
                true-value="yes"
            />
            <ActiveSwitch
                v-model:is-active="widget.ui_click"
                :error="responseErrors.ui_click"
                :is-disabled="submitLoader"
                :label-text="translations['UI click']"
                false-value="no"
                true-value="yes"
            />
            <hr class="w-100 m-0" />
            <SelectWithAjax
                v-model:val="widget.layout"
                :can-clear="false"
                :is-select-disabled="submitLoader"
                :label="translations['Format']"
                :no-margin="true"
                :options="layout_options"
            />
            <SelectWithAjax
                v-model:val="widget.ui_hover_direction"
                :can-clear="false"
                :is-select-disabled="submitLoader"
                :label="translations['Dropdown direction']"
                :no-margin="true"
                :options="direction_options"
            />
            <vue3-slide-up-down
                :duration="160"
                :model-value="widget.layout === 'custom'"
            >
                <TextareaComponent
                    v-model="widget.custom_toolbar"
                    :disabled="submitLoader"
                    :label-text="translations['Toolbar code']"
                    :no-margin="true"
                />
            </vue3-slide-up-down>
            <hr class="w-100 m-0" />

            <ImageUploadModal v-model="imageModal" v-model:chosen-image="image">
                <template #modalTrigger="{ handleOpen, disabled }">
                    <div
                        class="d-flex align-items-center gap-2 justify-content-between w-100 seo-image"
                    >
                        <span class="image" @click="handleOpen">
                            <img v-if="image" :src="image" alt="no-image" />
                            <div
                                v-else
                                class="d-flex flex-column gap-2 align-items-center no-image"
                            >
                                <i class="fa-light fa-cloud-arrow-up"></i>
                                <span
                                    class="text-400-secondary-13px d-none d-md-flex text-center"
                                >
                                    {{ translations["Click here to upload"] }}
                                </span>
                            </div>
                        </span>
                        <div
                            class="d-flex align-items-center justify-content-end justify-content-sm-between gap-3 flex-grow-1 pe-3"
                        >
                            <span class="label-500 m-0 d-none d-sm-flex">
                                {{ translations["Main sharing picture"] }}
                            </span>
                            <div
                                class="d-flex align-items-center flex-wrap gap-4"
                            >
                                <a
                                    :class="{ disabled: !image }"
                                    class="reselect-image"
                                    href="javascript:void(0);"
                                    @click="handleOpen"
                                >
                                    <i class="fa-light fa-rotate"></i>
                                </a>
                                <a
                                    :class="{ disabled: !image }"
                                    class="remove-seo-image"
                                    href="javascript:void(0);"
                                    @click="image = null"
                                >
                                    <i class="fa-light fa-trash-can"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </template>
            </ImageUploadModal>
        </template>
    </WrapperBox>
</template>
<script>
import _ from "lodash";
import { toast } from "@js/toast";

import ActiveSwitch from "@components/Form/ActiveSwitch";
import SelectWithAjax from "@components/Form/SelectWithAjax";
import TextareaComponent from "@components/Form/TextareaComponent";
import ImageUploadModal from "@components/Form/ImageUploadModal";
import WrapperBox from "./../WrapperBox.vue";
import { Vue3SlideUpDown } from "vue3-slide-up-down";

import { SeoModel } from "./../../js/Seo.js";

export default {
    name: "sharing",
    components: {
        Vue3SlideUpDown,
        WrapperBox,
        ActiveSwitch,
        SelectWithAjax,
        TextareaComponent,
        ImageUploadModal,
    },
    props: {
        modelValue: {
            default: {},
        },
    },
    data() {
        return {
            submitLoader: false,
            backup: {},
            backupImage: null,
            model: new SeoModel(),
            responseErrors: {},
            imageModal: false,
            translations: {
                "Show share count": this.$t("Show share count"),
                "UI click": this.$t("UI click"),
                "Show top networks": this.$t("Show top networks"),
                "Show button for other social networks": this.$t(
                    "Show button for other social networks"
                ),
                Large: this.$t("Large"),
                Small: this.$t("Small"),
                Custom: this.$t("Custom"),
                Format: this.$t("Format"),
                "Dropdown direction": this.$t("Dropdown direction"),
                Up: this.$t("Up"),
                Down: this.$t("Down"),
                "Toolbar code": this.$t("Toolbar code"),
                "Saved Successfully": this.$t("Saved Successfully"),
                "Main sharing picture": this.$t("Main sharing picture"),
                "Click here to upload": this.$t("Click here to upload"),
                "Share product": this.$t("Share product"),
            },
        };
    },
    computed: {
        image: {
            get() {
                return this.modelValue.og_image_url;
            },
            set(value) {
                this.modelValue.og_image_url = value?.url;
            },
        },
        layout_options() {
            return [
                {
                    id: "large",
                    name: this.translations["Large"],
                },
                {
                    id: "small",
                    name: this.translations["Small"],
                },
                {
                    id: "custom",
                    name: this.translations["Custom"],
                },
            ];
        },
        direction_options() {
            return [
                {
                    id: -1,
                    name: this.translations["Down"],
                },
                {
                    id: 1,
                    name: this.translations["Up"],
                },
            ];
        },
        widget: {
            get() {
                return this.modelValue.widget || {};
            },
            set(value) {
                this.$emit("update:modelValue", {
                    ...this.modelValue,
                    widget: value,
                });
            },
        },
        isSettingsChanged() {
            return (
                !_.isEqual(this.backup, this.widget) ||
                this.backupImage !== this.image
            );
        },
    },
    methods: {
        revertChanges() {
            this.responseErrors = {};
            this.$emit("update:modelValue", {
                ...this.modelValue,
                widget: _.cloneDeep(this.backup),
                og_image_url: this.backupImage,
            });
        },
        async saveChanges() {
            this.submitLoader = true;
            this.responseErrors = {};

            try {
                let payload = { ...this.widget, og_image_url: this.image };
                if (["no", 0, "0"].includes(payload.enabled)) {
                    delete payload.enabled;
                }
                await this.model.post("add-this", payload);

                this.backup = _.cloneDeep(this.modelValue.widget);
                this.backupImage = this.image;
                toast.success(this.translations["Saved Successfully"]);
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.submitLoader = false;
            }
        },
    },
    watch: {
        "widget.layout"(newValue, oldValue) {
            if (newValue !== "custom") {
                this.widget.custom_toolbar = this.backup.custom_toolbar;
            }
        },
    },
    mounted() {
        this.backup = _.cloneDeep(this.modelValue.widget);
        this.backupImage = this.modelValue.og_image_url;
    },
    emits: ["update:modelValue"],
};
</script>
<style lang="scss">
.seo-image {
    .image {
        height: 100px;
        width: 160px;
        padding: 6px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        border: 1px dashed #dedede;
        background: #fff;

        cursor: pointer;

        img {
            height: 100%;
        }

        .no-image i {
            color: var(--Color-Text-Body---cc-color-text-link, #8d58e0);
            font-size: 24px;
            font-style: normal;
            line-height: normal;
        }
    }

    .remove-seo-image {
        i {
            font-size: 18px;
            font-style: normal;
            font-weight: 300;
            line-height: 20px;
            color: #fc4f4e;
        }

        &.disabled {
            pointer-events: none;

            i {
                color: var(--Color-Text-Body---cc-color-text-disabled, #dedede);
            }
        }
    }

    .reselect-image {
        i {
            color: var(--Color-Text-Body---cc-color-text-secondary, #7a7d84);
            text-align: center;
            font-size: 18px;
            font-style: normal;
            font-weight: 300;
            line-height: 20px;
        }

        &.disabled {
            pointer-events: none;

            i {
                color: var(--Color-Text-Body---cc-color-text-disabled, #dedede);
            }
        }
    }
}
</style>
