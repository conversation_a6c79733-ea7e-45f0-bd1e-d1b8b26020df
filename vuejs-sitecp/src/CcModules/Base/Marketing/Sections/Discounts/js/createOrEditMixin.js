export default {
    data() {
        return {
            boxes: [],
        }
    },
    computed: {
        generalSettingsBlock() {
            return [
                {
                    key: "general",
                    group: "discounts",
                    title: "General settings",
                    titleHelp: null,
                    infoTitle: "General settings",
                    infoTitleHelp: null,
                    infoDescription: "General global settings",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            key: "active",
                            type: "switch",
                            label: "Discount status",
                            inputType: "switch",
                            trueValue: 'yes',
                            falseValue: 'no',
                        },
                        {
                            key: "name",
                            type: "string",
                            label: "Discount name",
                            inputType: "string",
                        },
                        {
                            key: "type",
                            type: "select",
                            label: "Discount type",
                            inputType: "select",
                            options: [
                                {id: 'flat', name: 'Fixed amount'},
                                {id: 'percent', name: 'Percentage'},
                                {id: 'shipping', name: 'Free shipping'},
                            ]
                        },
                        {
                            key: "type_value",
                            type: "number",
                            label: "Discount value",
                            inputType: "number",
                            dependField: "type",
                            dependValue: ["flat", 'percent'],
                            dependUnitField: "type",
                            unitDepends: {
                                flat: this.serverSettings("currency.sign"),
                                percent: "%",
                            },
                            inputSize: 4
                        },
                    ],
                }
            ]
        },
        customerGroupsBlock() {
            return [
                {
                    key: "customer_groups",
                    group: "discounts",
                    title: "Customer groups",
                    titleHelp: null,
                    infoTitle: "Customer groups",
                    infoTitleHelp: null,
                    infoDescription: "Choose all groups or pick a specific one, where the discount will be applied",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            key: "customer_groups_target",
                            type: "switch",
                            label: "All groups",
                            inputType: "switch",
                            trueValue: 'yes',
                            falseValue: 'no',
                        },
                        {
                            dependField: 'customer_groups_target',
                            dependValue: 'no',
                            type: 'line'
                        },
                        {
                            key: "customer_groups",
                            type: "select",
                            inputType: "select",
                            multiLine: true,
                            inputSize: 12,
                            searchable: true,
                            resolveOnLoad: true,
                            requestOnSearch: true,
                            multiple: true,
                            dependField: 'customer_groups_target',
                            dependValue: 'no',
                            url: '/admin/api/core/customers/groups',
                            querySearchKey: 'filters[query]',
                            urlParams: {
                                perpage: 99,
                            },
                        }
                    ],
                },
            ]
        },
        regionsBlock() {
            return [
                {
                    key: "regions",
                    group: "discounts",
                    title: "Regions",
                    titleHelp: null,
                    infoTitle: "Regions",
                    infoTitleHelp: null,
                    infoDescription: "Select a region where this discount will be applied or",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            key: "all_regions",
                            type: "switch",
                            label: "Make it Global",
                            inputType: "switch",
                            trueValue: 'yes',
                            falseValue: 'no',
                        },
                        {
                            dependField: 'all_regions',
                            dependValue: 'no',
                            type: 'line'
                        },
                        {
                            key: "geo_zone_id",
                            type: "select",
                            inputType: "select",
                            multiLine: true,
                            inputSize: 12,
                            searchable: true,
                            resolveOnLoad: true,
                            requestOnSearch: true,
                            multiple: true,
                            dependField: 'all_regions',
                            dependValue: 'no',
                            url: '/admin/api/core/settings/geo-zones/search',
                            querySearchKey: 'filters[query]',
                            urlParams: {
                                perpage: 99,
                            },
                        }
                    ],
                },
            ]
        },
        registeredCustomersBlock() {
            return [
                {
                    key: "registered_customers",
                    group: "discounts",
                    title: "Registered users only",
                    titleHelp: null,
                    infoTitle: "Registered users only",
                    infoTitleHelp: null,
                    infoDescription: "The discount will be available only to registered users",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            type: 'switch',
                            key: 'only_customer',
                            label: 'Discount available only to registered users',
                        },
                        {
                            key: "customers",
                            type: "select",
                            inputType: "select",
                            multiLine: true,
                            inputSize: 12,
                            searchable: true,
                            resolveOnLoad: true,
                            requestOnSearch: true,
                            multiple: true,
                            dependField: 'only_customer',
                            dependValue: '1',
                            placeholder: "Select customer/s",
                            url: '/admin/api/core/customers/autocomplete',
                        },
                    ],
                },
            ]
        },
        discountTargetBlock() {
            return [
                {
                    key: "target",
                    group: "discounts",
                    title: "Discount target",
                    titleHelp: null,
                    infoTitle: "Discount target",
                    infoTitleHelp: null,
                    infoDescription: "Define the target for the discount, based on orders, exact product or a product category",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        // {
                        //     key: "setting",
                        //     type: "select",
                        //     label: "Discount target",
                        //     inputType: "select",
                        //     options: [
                        //         {id: 'all', name: 'For every product in the cart'},
                        //         {id: 'order_over', name: 'Orders over'},
                        //         {id: 'product', name: 'Specific product/s'},
                        //         {id: 'product_category', name: 'Product category/categories'},
                        //         {id: 'product_vendor', name: 'Product vendor/s'},
                        //         {id: 'selection', name: 'Smart collection/s'},
                        //         {id: 'category_vendor', name: 'Product category/categories and vendor/s'},
                        //     ]
                        // },
                        {
                            type: "slot",
                            inputType: "slot",
                            slotName: 'discountTarget',
                        },
                    ],
                },
            ]
        },
        dateRangeBlock() {
            return [
                {
                    key: "date_range",
                    group: "discounts",
                    title: "Date range",
                    titleHelp: null,
                    infoTitle: "Start and end date",
                    infoTitleHelp: null,
                    infoDescription: "Specify the start date and the end date of the discount, or set no expiration.",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            type: "slot",
                            inputType: "slot",
                            slotName: 'dateRange',
                        },
                    ],
                },
            ]
        },
        dateRangeNoTimerBlock() {
            return [
                {
                    key: "date_range_no_timer",
                    group: "discounts",
                    title: "Date range",
                    titleHelp: null,
                    infoTitle: "Start and end date",
                    infoTitleHelp: null,
                    infoDescription: "Specify the start date and the end date of the discount, or set no expiration.",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            type: "slot",
                            inputType: "slot",
                            slotName: 'dateRangeNoTimer',
                        },
                    ],
                },
            ]
        },
        colorBlock() {
            return [
                {
                    key: "color_settings",
                    group: "discounts",
                    title: "Color settings",
                    titleHelp: null,
                    infoTitle: "Color settings",
                    infoTitleHelp: null,
                    infoDescription: "Customise discount label’s background and text color ",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            key: "color",
                            type: "color",
                            label: "Background color",
                            inputType: "color",
                        },
                        {
                            key: "text_color",
                            type: "color",
                            label: "Text color",
                            inputType: "color",
                        },
                    ],
                },
            ]
        },
        amountLabelBlock() {
            return [
                {
                    key: "amount_label",
                    group: "discounts",
                    title: "Discount’s amount in label",
                    titleHelp: null,
                    infoTitle: "Setting for showing discount’s amount in label",
                    infoTitleHelp: null,
                    infoDescription: "With this setting you can choose in what format the difference between the price and the discounted one to be displayed in the product’s detail page.",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            key: "discount_amount_type_in_label",
                            type: "radio",
                            label: "Show discount amount in label as:",
                            inputType: "radio",
                            layoutClasses: 'justify-content-start gap-4 flex-wrap',
                            options: [
                                {value: 'in_percent', text: 'As percent', img: `${this.serverSettings('img_url')}sitecp/img/img-percent-option.png?${this.serverSettings('last_build')}`, md: true},
                                {value: 'in_flat', text: 'As fixed amount', img: `${this.serverSettings('img_url')}sitecp/img/img-fixed-option.png?${this.serverSettings('last_build')}`, md: true},
                                // {id: 'dont_change', name: 'Don\'t change'},
                            ]
                        },
                    ],
                },
            ]
        },
        quantityBlock() {
            return [
                {
                    key: "quantity_settings",
                    group: "discounts",
                    title: "Discount conditions",
                    titleHelp: null,
                    infoTitle: "Discount conditions",
                    infoTitleHelp: null,
                    infoDescription: "Pizza ipsum dolor meat lovers buffalo.",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            key: "product_id",
                            type: "select",
                            inputType: "select",
                            multiLine: true,
                            label: "Customer buys",
                            inputSize: 12,
                            searchable: true,
                            resolveOnLoad: true,
                            requestOnSearch: true,
                            url: '/admin/api/core/products/search',
                            querySearchKey: 'filters[query]',
                        },
                        {
                            type: "slot",
                            slotName: 'quantityConfiguration',
                        }
                    ],
                },
            ]
        },
        discountLimitsBlock() {
            return [
                {
                    key: "discount_limits",
                    group: "discounts",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Discount limits",
                    infoTitleHelp: null,
                    infoDescription: "Global- The number of uses is counted only when the status of the order is: Paid, Completed, Fulfilled\n" +
                        "\n" +
                        "Discount limit for customer- The maximum number of times that the discount can be used by a single user. The number of uses is reported only when the status of the orders is: Paid, Completed, Fulfilled",
                    editMethod: "inline",
                    fields: [
                        {
                            type: "slot",
                            inputType: "slot",
                            slotName: 'discountLimits',
                        },
                    ],
                }
            ]
        },
        global() {
            return [
                ...this.generalSettingsBlock,
                ...this.discountTargetBlock,
                ...this.registeredCustomersBlock,
                ...this.discountLimitsBlock,
                ...this.customerGroupsBlock,
                ...this.colorBlock,
                ...this.amountLabelBlock,
                ...this.dateRangeBlock
            ]
        },
        code() {
            return [
                {
                    ...this.generalSettingsBlock[0],
                    key: "custom-code"
                },
                {
                    key: "date_range-code",
                    group: "discounts",
                    title: "Generate a discount code",
                    titleHelp: null,
                    infoTitle: "Discount code",
                    infoTitleHelp: null,
                    infoDescription: "Pizza ipsum dolor meat lovers buffalo. Thin melted mozzarella pie large deep extra melted mayo melted. Dolor ricotta pineapple garlic.",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            type: "slot",
                            inputType: "slot",
                            slotName: 'codeGenerator',
                        },
                        {
                            key: "code_apply",
                            type: "switch",
                            label: "The discount is applied even when the cart contains <strong>discounted</strong> products. *",
                            inputType: "switch",
                            helpBlock: 'Valid only for discounts of type "Discount" and "Fixed discount"'
                        },
                        {
                            key: "apply_regular_price",
                            type: "switch",
                            label: "Apply to the regular price of products, if this discount is greater",
                            inputType: "switch",
                            dependField: 'code_apply',
                            dependValue: '1',
                        },
                        {
                            key: "barcode_prefix",
                            type: "switch",
                            label: "The value from the field should be used as the barcode prefix.",
                            inputType: "switch",
                            dependField: 'code_format',
                            dependValue: ['ean13', 'ean8'],
                        },

                    ],
                },
                ...this.discountTargetBlock,
                ...this.registeredCustomersBlock,
                {
                    key: "discount_limits",
                    group: "discounts",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Discount limits",
                    infoTitleHelp: null,
                    infoDescription: "Global- The number of uses is counted only when the status of the order is: Paid, Completed, Fulfilled\n" +
                        "\n" +
                        "Discount limit for customer- The maximum number of times that the discount can be used by a single user. The number of uses is reported only when the status of the orders is: Paid, Completed, Fulfilled",
                    editMethod: "inline",
                    fields: [
                        {
                            type: "slot",
                            inputType: "slot",
                            slotName: 'discountLimits',
                        },
                    ],
                },
                ...this.customerGroupsBlock,
                ...this.regionsBlock,
                ...this.dateRangeNoTimerBlock
            ]
        },
        container() {
            return [
                {
                    ...this.generalSettingsBlock[0],
                    fields: [
                        {
                            key: "active",
                            type: "switch",
                            label: "Discount status",
                            inputType: "switch",
                            trueValue: 'yes',
                            falseValue: 'no',
                        },
                        {
                            key: "name",
                            type: "string",
                            label: "Discount name",
                            inputType: "string",
                        },
                        {
                            key: "type_value",
                            type: "percent",
                            label: "Discount value",
                            inputType: "percent",
                            unit: "%",
                            inputSize: 4,
                        },
                    ]
                },
                ...this.discountTargetBlock,
                ...this.customerGroupsBlock,
                ...this.regionsBlock,
                ...this.dateRangeNoTimerBlock
            ]
        },
        fixed() {
            return [
                {
                    ...this.generalSettingsBlock[0],
                    fields: [
                        {
                            key: "active",
                            type: "switch",
                            label: "Discount status",
                            inputType: "switch",
                            trueValue: 'yes',
                            falseValue: 'no',
                        },
                        {
                            key: "name",
                            type: "string",
                            label: "Discount name",
                            inputType: "string",
                        },
                    ]
                },
                ...this.customerGroupsBlock,
                ...this.colorBlock,
                ...this.amountLabelBlock,
                ...this.dateRangeBlock
            ]
        },
        quantity() {
            return [
                {
                    ...this.generalSettingsBlock[0],
                    fields: [
                        {
                            key: "active",
                            type: "switch",
                            label: "Discount status",
                            inputType: "switch",
                            trueValue: 'yes',
                            falseValue: 'no',
                        },
                        {
                            key: "name",
                            type: "string",
                            label: "Discount name",
                            inputType: "string",
                        },
                    ]
                },
                ...this.quantityBlock,
                ...this.customerGroupsBlock,
                ...this.dateRangeNoTimerBlock
            ]
        },
        countdown() {
            return [
                {
                    ...this.generalSettingsBlock[0],
                    fields: [
                        {
                            key: "active",
                            type: "switch",
                            label: "Discount status",
                            inputType: "switch",
                            trueValue: 'yes',
                            falseValue: 'no',
                        },
                        {
                            key: "name",
                            type: "string",
                            label: "Discount name",
                            inputType: "string",
                        },
                        {
                            key: "type",
                            type: "select",
                            label: "Discount type",
                            inputType: "select",
                            options: [
                                {id: 'flat', name: 'Fixed amount'},
                                {id: 'percent', name: 'Percentage'},
                            ]
                        },
                        {
                            key: "type_value",
                            type: "number",
                            label: "Discount value",
                            inputType: "number",
                            dependField: "type",
                            dependValue: ["flat", 'percent'],
                            dependUnitField: "type",
                            unitDepends: {
                                flat: this.serverSettings("currency.sign"),
                                percent: "%",
                            },
                            max: 100,
                            inputSize: 4
                        },
                    ],
                },
                {
                    ...this.discountTargetBlock[0],
                    key: 'target-countdown',
                    fields: [
                        {
                            key: "setting",
                            type: "select",
                            label: "Discount target",
                            inputType: "select",
                            options: [
                                {id: 'all', name: 'For every product in the cart'},
                                {id: 'order_over', name: 'Orders over'},
                            ]
                        },
                        {
                            type: "number",
                            key: "order_over",
                            inputType: "number",
                            unit: 'currency',
                            dependField: 'setting',
                            dependValue: 'order_over',
                            label: "Amount"
                        },
                    ]
                },
                {
                    key: "discount_conditions-countdown",
                    group: "discounts",
                    title: "Discount conditions",
                    titleHelp: null,
                    infoTitle: "Discount conditions",
                    infoTitleHelp: null,
                    infoDescription: "Conditions description",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            type: 'slot',
                            slotName: 'previewEffect'
                        },
                        {
                            type: "number",
                            key: "countdown_minutes",
                            inputType: "number",
                            digits: 0,
                            unit: 'minutes',
                            label: "Validity time"
                        },
                        {
                            type: "editor",
                            key: "countdown_description",
                            inputType: "editor",
                            digits: 0,
                            label: "Description",
                        },
                    ],
                },
                ...this.discountLimitsBlock,
                {
                    key: "registered_customers-countdown",
                    group: "discounts",
                    title: "Registered users only",
                    titleHelp: null,
                    infoTitle: "Registered users only",
                    infoTitleHelp: null,
                    infoDescription: "The discount will be available only to registered users",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            type: 'switch',
                            key: 'only_customer',
                            label: 'Discount available only to registered users',
                        },
                    ],
                },
                ...this.customerGroupsBlock,
                ...this.dateRangeNoTimerBlock
            ]
        },
        'code-pro'() {
            return [
                {
                    key: "code-pro",
                    group: "discounts",
                    title: "General settings",
                    titleHelp: null,
                    infoTitle: "General settings",
                    infoTitleHelp: null,
                    infoDescription: "Code pro description",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            key: "active",
                            type: "switch",
                            label: "Discount status",
                            inputType: "switch",
                            trueValue: 'yes',
                            falseValue: 'no',
                        },
                        {
                            key: "name",
                            type: "string",
                            label: "Discount name",
                            inputType: "string",
                        },
                        {
                            type: 'slot',
                            slotName: 'codeProSettings'
                        }
                    ]
                }
            ]
        }
    },
    methods: {
        setBoxes() {
            this.boxes = this[this.type];
            this.toggleBoxVisibility('discount_limits', ['all', 'order_over'].includes(this.settings.setting))
            this.toggleBoxVisibility('registered_customers', ['order_over'].includes(this.settings.setting));
        },
        toggleBoxVisibility(key, check) {
            this.boxes = this.boxes.map(box => {
                if (box.key === key && !check) {
                    return {
                        ...box,
                        lockEditMethod: true,
                        isVisible: false
                    }
                } else if (box.key === key && check) {
                    return {
                        ...box,
                        lockEditMethod: false,
                        isVisible: true
                    }
                }
                return box
            })
        }
    },
    watch: {
        'settings.setting'(value) {
            this.toggleBoxVisibility('discount_limits', ['all', 'order_over'].includes(value));
            this.toggleBoxVisibility('registered_customers', ['order_over'].includes(value));
        }
    }
}
