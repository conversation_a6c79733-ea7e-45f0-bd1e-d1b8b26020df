<template>
    <SettingsWrapper
        :breadcrumb="breadcrumbs"
        :container-classes="containerWidth"
        :loading="false"
        :prevent-save="true"
        :title="title"
        icon="fa-regular fa-calendar"
        setting-key="campaigns"
    >
        <template
            v-if="
                ($route.params.type && icons[$route.params.type]) ||
                customIconForSection
            "
            #icon
        >
            <figure
                class="m-0"
                v-if="icons[$route.params.type]"
                v-html="icons[$route.params.type]"
            ></figure>
            <figure
                class="m-0"
                v-if="customIconForSection"
                v-html="customIconForSection"
            ></figure>
        </template>
        <template #headerButton>
            <div
                v-if="$route.name === 'discounts-list'"
                class="d-flex gap-2 flex-wrap align-items-center justify-content-end"
            >
                <button
                    :disabled="!isAllowedCreate"
                    class="btn btn-white d-flex align-items-center gap-1"
                    @click="statusModal = true"
                >
                    <i class="fa-light fa-bars-staggered"></i>
                    {{ translations["Statuses"] }}
                </button>
                <button
                    :disabled="!isAllowedCreate"
                    class="btn btn-primary d-flex align-items-center gap-1"
                    @click="createModal = true"
                >
                    <i class="fa-light fa-plus"></i>
                    {{ translations["Add discount"] }}
                </button>
            </div>
            <button
                v-if="$route.name === 'discounts-products'"
                class="btn btn-primary d-flex align-items-center gap-1"
                @click="productsModal = true"
            >
                <i class="fa-light fa-plus"></i>
                {{ translations["Add product"] }}
            </button>
            <div
                v-if="this.$route.name === 'discounts-code_pro-list'"
                class="d-flex gap-2 flex-wrap align-items-center justify-content-end"
            >
                <button class="btn btn-white d-flex align-items-center gap-1">
                    <i class="fa-light fa-file-arrow-down"></i>
                    {{ translations["Export"] }}
                </button>
                <button class="btn btn-primary d-flex align-items-center gap-1">
                    <i class="fa-light fa-plus"></i>
                    {{ translations["Create Discount code"] }}
                </button>
                <router-link
                    :to="{
                        name: 'discounts-code_pro-generator',
                        params: { id: this.$route.params.id },
                    }"
                    class="btn btn-primary d-flex align-items-center gap-1"
                >
                    <i class="fa-light fa-list"></i>
                    {{ translations["Generate codes"] }}
                </router-link>
            </div>
        </template>
        <template #settings>
            <router-view></router-view>
        </template>
    </SettingsWrapper>
</template>
<script>
import SettingsWrapper from "@components/SettingsWrapper/SettingsWrapper";
import useSharedDiscounts from "./js/useSharedDiscounts.js";
import icons from "./js/icons.js";

export default {
    name: "DiscountsIndex",
    mixins: [icons],
    components: {
        SettingsWrapper,
    },
    setup() {
        const {
            createModal,
            isAllowedCreate,
            statusModal,
            discount,
            productsModal,
        } = useSharedDiscounts();

        return {
            createModal,
            isAllowedCreate,
            statusModal,
            discount,
            productsModal,
        };
    },
    data() {
        return {
            translations: {
                "Create {type} discount": this.$t("Create {type} discount"),
                "Add discount": this.$t("Add discount"),
                Discounts: this.$t("Discounts"),
                Marketing: this.$t("Marketing"),
                Edit: this.$t("Edit"),
                "Edit {name}": this.$t("Edit {name}"),
                Statuses: this.$t("Statuses"),
                Global: this.$t("Global"),
                Code: this.$t("Code"),
                Container: this.$t("Container"),
                Fixed: this.$t("Fixed"),
                Quantity: this.$t("Quantity"),
                Countdown: this.$t("Countdown"),
                "Multiple promo codes": this.$t("Multiple promo codes"),
                Create: this.$t("Create"),
                "Product management for {name}": this.$t(
                    "Product management for {name}"
                ),
                "Product management": this.$t("Product management"),
                "Add product": this.$t("Add product"),
                "Promo code management for {name}": this.$t(
                    "Promo code management for {name}"
                ),
                "Promo code management": this.$t("Promo code management"),
                Export: this.$t("Export"),
                "Create Discount code": this.$t("Create Discount code"),
                "Generate codes": this.$t("Generate codes"),
                "Generate discount codes": this.$t("Generate discount codes"),
                Generate: this.$t("Generate"),
                "Discounts list": this.$t("Discounts list"),
                "Discounts products": this.$t("Discounts products"),
                "Discounts code pro list": this.$t("Discounts code pro list"),
                "Discounts code pro generator": this.$t(
                    "Discounts code pro generator"
                ),
            },
        };
    },
    computed: {
        containerWidth() {
            switch (this.$route.name) {
                case "discounts-list":
                case "discounts-products":
                case "discounts-code_pro-list":
                    return "container-large";
                default:
                    return "container-medium";
            }
        },
        customIconForSection() {
            if (this.$route.name.match(/code[-_]pro/)) {
                return this.icons["code-pro"];
            } else if (this.$route.name.match(/products/)) {
                return this.icons["fixed"];
            }

            return null;
        },
        breadcrumbs() {
            let arr = [
                {
                    text: this.translations["Marketing"],
                    to: { name: "marketing" },
                },
            ];
            switch (this.$route.name) {
                case "discounts-list":
                    arr.push({ text: this.translations["Discounts"] });
                    break;
                case "discounts-products":
                    arr.push(
                        {
                            text: this.translations["Discounts"],
                            to: { name: "discounts-list" },
                        },
                        { text: this.translations["Products"] }
                    );
                    break;
                case "discounts-code_pro-list":
                    arr.push(
                        {
                            text: this.translations["Discounts"],
                            to: { name: "discounts-list" },
                        },
                        {
                            text: this.translations["Promo code management"],
                        }
                    );
                    break;
                case "discounts-code_pro-generator":
                    arr.push(
                        {
                            text: this.translations["Discounts"],
                            to: { name: "discounts-list" },
                        },
                        {
                            to: { name: "discounts-code_pro-list" },
                            text: this.translations["Promo code management"],
                        },
                        {
                            text: this.translations["Generate"],
                        }
                    );
                    break;
            }
            return arr;
        },
        type() {
            switch (this.$route.params.type) {
                case "global":
                    return this.translations["Global"];
                case "code":
                    return this.translations["Code"];
                case "container":
                    return this.translations["Container"];
                case "fixed":
                    return this.translations["Fixed"];
                case "quantity":
                    return this.translations["Quantity"];
                case "countdown":
                    return this.translations["Countdown"];
                case "code-pro":
                    return this.translations["Multiple promo codes"];
            }
        },
        title() {
            if (this.$route.name.includes("create")) {
                return this.$trp(this.translations["Create {type} discount"], {
                    type: this.type,
                });
            } else if (this.$route.name.includes("edit")) {
                return this.$trp(this.translations["Edit {name}"], {
                    name: this.discount?.name || "",
                });
            } else if (this.$route.name === "discounts-products") {
                return this.discount?.name
                    ? this.$trp(
                          this.translations["Product management for {name}"],
                          { name: this.discount?.name || "" }
                      )
                    : this.translations["Product management"];
            } else if (this.$route.name === "discounts-code_pro-list") {
                return this.discount?.name
                    ? this.$trp(
                          this.translations["Promo code management for {name}"],
                          { name: this.discount?.name || "" }
                      )
                    : this.translations["Promo code management"];
            } else if (this.$route.name === "discounts-code_pro-generator") {
                return this.translations["Generate discount codes"];
            }

            return this.translations["Discounts"];
        },
    },
    methods: {
        setTranslation(param) {
            let str = param.charAt(0).toUpperCase() + param.slice(1);
            if (!this.translations[str]) {
                this.translations[str] = this.$t(str);
            }
            return this.translations[str];
        },
        constructBreadcrumbName() {
            let paramCreate = this.$route.params.type;
            let paramEdit = this.$route.params.id;

            if (paramCreate) {
                return { text: this.setTranslation(paramCreate) };
            } else if (paramEdit) {
                return { text: this.translations["Edit"] };
            }

            return null;
        },
    },
};
</script>
