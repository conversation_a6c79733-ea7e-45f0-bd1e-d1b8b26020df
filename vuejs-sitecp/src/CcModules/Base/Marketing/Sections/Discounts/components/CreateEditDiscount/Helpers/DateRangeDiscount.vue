<template>
    <div class="d-flex flex-column gap-3">
        <TimePeriod
            v-model:end-date="settings.date_end"
            v-model:no-expire="settings.no_expire"
            v-model:start-date="settings.date_start"
            :error="translationLabels[responseErrors.date_start] || responseErrors.date_start"
            :errorSecond="translationLabels[responseErrors.date_end] || responseErrors.date_end"
            custom-class="correct-date-range-width"
            />
            <!-- format="YYYY-MM-DD" -->
        <template v-if="!hideTimer">
            <ActiveSwitch
                v-model:is-active="settings.timer_list"
                :error="translationLabels[responseErrors['timer_list']] || responseErrors['timer_list']"
                :label-text="translations['Show timer in product listing']"
                :reverse="true"
                :is-disabled="!settings.date_end"
            />
            <ActiveSwitch
                v-model:is-active="settings.timer_details"
                :error="translationLabels[responseErrors['timer_details']] || responseErrors['timer_details']"
                :label-text="translations['Show timer in product details page']"
                :reverse="true"
                :is-disabled="!settings.date_end"
            />
        </template>
    </div>
</template>
<script>
import boxMixin from './../../../js/boxMixin.js'

import TimePeriod from "@components/Form/TimePeriod";
import ActiveSwitch from "@components/Form/ActiveSwitch";

export default {
    name: 'DateRangeDiscount',
    mixins: [boxMixin],
    components: {
        TimePeriod,
        ActiveSwitch
    },
    data() {
        return {
            translations: {
                'Show timer in product listing': this.$t('Show timer in product listing'),
                'Show timer in product details page': this.$t('Show timer in product details page'),
            }
        };
    },
}
</script>
<style lang="scss">
.correct-date-range-width {
    .mx-datepicker {
        width: auto !important;
    }
}
</style>
