<template>
    <div class="d-flex flex-column gap-3">
        <SelectWithAjax
            v-model:val="settings.setting"
            :no-margin="true"
            :label="translations['Discount target']"
            :options="settingsOptions"
            :can-clear="false"
            :watch-options="true"
        />
        <Vue3SlideUpDown
            :model-value="
                settings.setting === 'all' && settings.type === 'shipping'
            "
            v-bind="slideBinds"
        >
            <ActiveSwitch
                v-model:is-active="settings.force_save"
                :error="
                    translations[responseErrors['force_save']] ||
                    responseErrors['force_save']
                "
                :label-text="translations['Save the discount on your order']"
            />
        </Vue3SlideUpDown>
        <Vue3SlideUpDown
            :model-value="settings.setting === 'order_over'"
            v-bind="slideBinds"
        >
            <CurrencyComponent
                v-model="settings.order_over"
                :error="
                    translationLabels[responseErrors['order_over']] ||
                    responseErrors['order_over']
                "
                :label-text="translations['Amount']"
                :min="0"
                :no-margin="true"
            />
            <ActiveSwitch
                v-model:is-active="settings.force_save"
                :error="
                    translationLabels[responseErrors['force_save']] ||
                    responseErrors['force_save']
                "
                :label-text="translations['Save the discount on your order']"
            />
        </Vue3SlideUpDown>
        <Vue3SlideUpDown
            :model-value="settings.setting === 'product'"
            v-bind="slideBinds"
        >
            <SelectWithAjax
                v-model:val="settings.products"
                :error="
                    translationLabels[responseErrors['products']] ||
                    responseErrors['products']
                "
                :options="productOptions"
                :placeholder="translations['Select products']"
                api-url="/admin/api/core/products/search"
                v-bind="selectBinds"
                @options="(val) => (productOptions = val)"
            />
        </Vue3SlideUpDown>
        <Vue3SlideUpDown
            :model-value="settings.setting === 'product_category'"
            v-bind="slideBinds"
        >
            <div class="info-box no-border info-box-warning">
                <p class="m-0" v-html="categoryMsg"></p>
            </div>
            <SelectWithAjax
                v-model:val="settings.product_categories"
                :error="
                    translationLabels[responseErrors['product_categories']] ||
                    responseErrors['product_categories']
                "
                :options="categoryOptions"
                :placeholder="translations['Select category/categories']"
                api-url="/admin/api/core/product-categories/search"
                v-bind="selectBinds"
                @options="(val) => (categoryOptions = val)"
            />
        </Vue3SlideUpDown>
        <Vue3SlideUpDown
            :model-value="settings.setting === 'product_vendor'"
            v-bind="slideBinds"
        >
            <SelectWithAjax
                v-model:val="settings.vendors"
                :error="
                    translationLabels[responseErrors['vendors']] ||
                    responseErrors['vendors']
                "
                :options="vendorOptions"
                :placeholder="translations['Select vendor/s']"
                api-url="/admin/api/core/vendors/search"
                v-bind="selectBinds"
                @options="(val) => (vendorOptions = val)"
            />
        </Vue3SlideUpDown>
        <Vue3SlideUpDown
            :model-value="settings.setting === 'selection'"
            v-bind="slideBinds"
        >
            <SelectWithAjax
                v-model:val="settings.selection"
                :error="
                    translationLabels[responseErrors['selection']] ||
                    responseErrors['selection']
                "
                :options="selectionOptions"
                :placeholder="translations['Select smart collection/s']"
                api-url="/admin/api/core/collections/search"
                v-bind="selectBinds"
                @options="(val) => (selectionOptions = val)"
            />
        </Vue3SlideUpDown>
        <Vue3SlideUpDown
            :model-value="settings.setting === 'category_vendor'"
            v-bind="slideBinds"
        >
            <div class="info-box no-border info-box-warning">
                <p class="m-0" v-html="categoryMsg"></p>
            </div>
            <SelectWithAjax
                v-model:val="settings.product_categories"
                :error="
                    translationLabels[responseErrors['product_categories']] ||
                    responseErrors['product_categories']
                "
                :label="translations['Categories']"
                :options="categoryOptions"
                :placeholder="translations['Select category/categories']"
                api-url="/admin/api/core/product-categories/search"
                v-bind="selectBinds"
                @options="(val) => (categoryOptions = val)"
            />
            <SelectWithAjax
                v-model:val="settings.vendors"
                :error="
                    translationLabels[responseErrors['vendors']] ||
                    responseErrors['vendors']
                "
                :label="translations['Vendors']"
                :options="vendorOptions"
                :placeholder="translations['Select vendor/s']"
                api-url="/admin/api/core/vendors/search"
                v-bind="selectBinds"
                @options="(val) => (vendorOptions = val)"
            />
        </Vue3SlideUpDown>
    </div>
</template>
<script>
import CurrencyComponent from "@components/Form/CurrencyComponent.vue";
import SelectWithAjax from "@components/Form/SelectWithAjax.vue";
import ActiveSwitch from "@components/Form/ActiveSwitch.vue";
import { Vue3SlideUpDown } from "vue3-slide-up-down";
import boxMixin from "../../../js/boxMixin.js";

export default {
    name: "DiscountTarget",
    mixins: [boxMixin],
    components: {
        Vue3SlideUpDown,
        CurrencyComponent,
        ActiveSwitch,
        SelectWithAjax,
    },
    data() {
        return {
            productOptions: [],
            categoryOptions: [],
            vendorOptions: [],
            selectionOptions: [],
            customerOptions: [],

            translations: {
                Amount: this.$t("Amount"),
                "Save the discount on your order": this.$t(
                    "Save the discount on your order"
                ),
                "Select products": this.$t("Select products"),
                "Select category/categories": this.$t(
                    "Select category/categories"
                ),
                "Select vendor/s": this.$t("Select vendor/s"),
                Categories: this.$t("Categories"),
                Vendors: this.$t("Vendors"),
                "Registered users only": this.$t("Registered users only"),
                "The discount will only be available to registered users.":
                    this.$t(
                        "The discount will only be available to registered users."
                    ),
                "Select smart collection/s": this.$t(
                    "Select smart collection/s"
                ),
                "If the option is enabled and when editing an order that includes this discount and the conditions are not met, the discount will not be removed.":
                    this.$t(
                        "If the option is enabled and when editing an order that includes this discount and the conditions are not met, the discount will not be removed."
                    ),
                "<strong>The discount will only be applied to the main product category.</strong><br/> For example: If a product is added to the Women > Shoes category and to the subcategory Season > Summer, the discount will only be applied to the main category (Women > Shoes) and will not be applied to (Season > Summer)":
                    this.$t(
                        "<strong>The discount will only be applied to the main product category.</strong><br/> For example: If a product is added to the Women > Shoes category and to the subcategory Season > Summer, the discount will only be applied to the main category (Women > Shoes) and will not be applied to (Season > Summer)"
                    ),
                "Discount target": this.$t("Discount target"),
                "For every product in the cart": this.$t(
                    "For every product in the cart"
                ),
                "Orders over": this.$t("Orders over"),
                "Specific product/s": this.$t("Specific product/s"),
                "Product category/categories": this.$t(
                    "Product category/categories"
                ),
                "Product vendor/s": this.$t("Product vendor/s"),
                "Smart collection/s": this.$t("Smart collection/s"),
                "Product category/categories and vendor/s": this.$t(
                    "Product category/categories and vendor/s"
                ),
            },
        };
    },
    computed: {
        settingsOptions() {
            return [
                {
                    id: "all",
                    name: this.translations["For every product in the cart"],
                },
                { id: "order_over", name: this.translations["Orders over"] },
                ...(this.settings.type !== "shipping"
                    ? [
                          {
                              id: "product",
                              name: this.translations["Specific product/s"],
                          },
                          {
                              id: "product_category",
                              name: this.translations[
                                  "Product category/categories"
                              ],
                          },
                          {
                              id: "product_vendor",
                              name: this.translations["Product vendor/s"],
                          },
                          {
                              id: "selection",
                              name: this.translations["Smart collection/s"],
                          },
                          {
                              id: "category_vendor",
                              name: this.translations[
                                  "Product category/categories and vendor/s"
                              ],
                          },
                      ]
                    : []),
            ];
        },
        categoryMsg() {
            return this.translations[
                "<strong>The discount will only be applied to the main product category.</strong><br/> For example: If a product is added to the Women > Shoes category and to the subcategory Season > Summer, the discount will only be applied to the main category (Women > Shoes) and will not be applied to (Season > Summer)"
            ];
        },
        slideBinds() {
            return {
                duration: 160,
                class: "d-flex flex-column gap-3",
            };
        },
        selectBinds() {
            return {
                colsWidth: 12,
                columnStyle: true,
                emitOnGet: true,
                noMargin: true,
                requestOnOpen: true,
                requestOnSearch: true,
                resolveOnLoad: !!this.$route.params.id,
                searchable: true,
                mode: "tags",
            };
        },
    },
    watch: {
        "settings.type"(value) {
            if (value === "shipping") {
                this.settings.setting = "all";
            }
        },
    },
};
</script>
