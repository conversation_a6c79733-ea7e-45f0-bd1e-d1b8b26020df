<template>
    <SettingsBox
        v-model:boxes="boxes"
        v-model:settings="settings"
        :response-errors="responseErrors"
        :translationLabels="translations"
        box-key="discounts-code-pro"
    >
        <template #codeConfig>
            <CodeConfig
                v-model="settings"
                :response-errors="responseErrors"
                :translation-labels="translations"
            />
        </template>
        <template #dateRangeNoTimer>
            <DateRangeDiscount
                v-model="settings"
                :hide-timer="true"
                :response-errors="responseErrors"
                :translation-labels="translations"
            />
        </template>
        <template #conditionsConfig>
            <ConditionsConfig
                v-model="settings"
                :response-errors="responseErrors"
                :translation-labels="translations"
            />
        </template>
        <template #discountLimits>
            <DiscountLimits
                v-model="settings"
                :response-errors="responseErrors"
                :translation-labels="translations"
            />
        </template>
    </SettingsBox>
    <SubmitChanges
        v-model="settings"
        v-model:response-errors="responseErrors"
        :save-func="submit"
        :submitLoader="submitLoader"
    />
    <pre>{{ settings }}</pre>
</template>
<script>
import { RequestModel } from "../../js/Discounts";
import createOrEditMixin from "../../js/createOrEditMixin";

import SettingsBox from "@components/SettingsBox/SettingsBox.vue";
import DateRangeDiscount from "../CreateEditDiscount/Helpers/DateRangeDiscount.vue";
import CodeConfig from "./Helpers/CodeConfig.vue";
import ConditionsConfig from "./Helpers/ConditionsConfig.vue";
import DiscountLimits from "../CreateEditDiscount/Helpers/DiscountLimits.vue";
import SubmitChanges from "@components/SubmitChanges.vue";

export default {
    name: "CodeGenerator",
    mixins: [createOrEditMixin],
    components: {
        SettingsBox,
        DateRangeDiscount,
        CodeConfig,
        ConditionsConfig,
        DiscountLimits,
        SubmitChanges,
    },
    data() {
        return {
            settings: {
                code: {
                    generator_type: "range",
                    from: null,
                    to: null,
                    limit: null,
                    length: null,
                    structure: [],
                },
                active: "no",
                code_apply: 0,
                apply_regular_price: 0,
                customer_groups_target: "yes",
                customer_groups: [],
                all_regions: "yes",
                geo_zone_id: [],
                date_start: null,
                date_end: null,
                no_expire: false,
                condition: [
                    {
                        type: "flat",
                        setting: "all",
                        type: null,
                        allow_price: 0,
                        order_over: null,
                        force_save: 0,
                        vendors: [],
                        product_categories: [],
                        selection: [],
                        products: [],
                    },
                ],
                max_uses: null,
                maxused_user: null,
            },
            translations: {},
            responseErrors: {},
            boxes: [],
            submitLoader: false,
            model: new RequestModel(
                `/admin/api/core/discounts/code-pro/${this.$route.params.id}`
            ),
        };
    },
    methods: {
        async submit() {
            this.submitLoader = true;
            this.responseErrors = {};

            try {
                let response = await this.model.post("generate", this.settings);
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.submitLoader = false;
            }
        },
        setBoxes() {
            this.boxes = [
                {
                    key: "general",
                    group: "code-pro",
                    title: "General settings",
                    titleHelp: null,
                    infoTitle: "General settings",
                    infoTitleHelp: null,
                    infoDescription: "General global settings",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            key: "active",
                            type: "switch",
                            label: "Discount status",
                            inputType: "switch",
                            trueValue: "yes",
                            falseValue: "no",
                        },
                    ],
                },
                {
                    key: "config",
                    group: "code-pro",
                    title: "Code settings",
                    titleHelp: null,
                    infoTitle: "Code settings",
                    infoTitleHelp: null,
                    infoDescription: "Code settings",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            type: "slot",
                            inputType: "slot",
                            slotName: "codeConfig",
                        },
                        {
                            key: "code_apply",
                            type: "switch",
                            label: "The discount is applied even when the cart contains <strong>discounted</strong> products. *",
                            inputType: "switch",
                            helpBlock:
                                'Valid only for discounts of type "Discount" and "Fixed discount"',
                        },
                        {
                            key: "apply_regular_price",
                            type: "switch",
                            label: "Apply to the regular price of products, if this discount is greater",
                            inputType: "switch",
                            dependField: "code_apply",
                            dependValue: "1",
                        },
                    ],
                },
                { ...this.customerGroupsBlock[0], group: "code-pro" },
                { ...this.regionsBlock[0], group: "code-pro" },
                { ...this.dateRangeNoTimerBlock[0], group: "code-pro" },
                ...this.discountLimitsBlock,
                {
                    key: "conditions-config",
                    group: "code-pro",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Conditions settings",
                    infoTitleHelp: null,
                    infoDescription: "Conditions settings",
                    editMethod: "inline",
                    isVisible: true,
                    cardClass: "bg-transparent",
                    cardBodyClass: "p-0",
                    fields: [
                        {
                            type: "slot",
                            inputType: "slot",
                            slotName: "conditionsConfig",
                        },
                    ],
                },
            ];
        },
    },
    mounted() {
        this.setBoxes();
    },
};
</script>