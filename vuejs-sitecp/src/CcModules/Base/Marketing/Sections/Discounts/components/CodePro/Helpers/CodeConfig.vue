<template>
    <b-row>
        <b-col class="col-12 col-md-6">
            <SelectWithAjax
                v-model:val="settings.code.generator_type"
                :label="translations['Generate type']"
                :column-style="true"
                :cols-width="12"
                :options="typeOptions"
                :no-margin="true"
                :can-clear="false"
            />
        </b-col>
        <b-col class="col-6 d-none d-md-block"></b-col>
    </b-row>
    <Vue3SlideUpDown
        :duration="130"
        :model-value="settings.code.generator_type === 'range'"
    >
        <b-row>
            <b-col class="col-12 col-md-6">
                <InputComponent
                    v-model="settings.code.from"
                    :column-style="true"
                    :no-margin="true"
                    :label-text="
                        translations['Code from the generator will start']
                    "
                />
            </b-col>
            <b-col class="col-12 col-md-6">
                <InputComponent
                    v-model="settings.code.to"
                    :column-style="true"
                    :no-margin="true"
                    :label-text="
                        translations['Code from the generator will end']
                    "
                />
            </b-col>
        </b-row>
    </Vue3SlideUpDown>
    <Vue3SlideUpDown
        :duration="130"
        :model-value="settings.code.generator_type === 'random'"
    >
        <b-row class="gy-3">
            <b-col class="col-12 col-md-6">
                <InputComponent
                    v-model="settings.code.limit"
                    :column-style="true"
                    :no-margin="true"
                    type="number"
                    :min="0"
                    :max="10000"
                    :digits="0"
                    :label-text="translations['Count of codes to generate']"
                />
            </b-col>
            <b-col class="col-12 col-md-6">
                <InputComponent
                    v-model="settings.code.length"
                    :column-style="true"
                    :no-margin="true"
                    :min="0"
                    :max="10000"
                    :digits="0"
                    :label-text="translations['Count of characters in code']"
                    :help-block="
                        translations[
                            'If blank, each code will be generated with a random length between 6 and 18 chars.'
                        ]
                    "
                />
            </b-col>
            <b-col class="col-12 col-md-6">
                <ActiveSwitch
                    v-model:is-active="alpha"
                    :true-value="true"
                    :false-value="false"
                    :label-text="translations['Letters']"
                    :reverse="true"
                />
            </b-col>
            <b-col class="col-12 col-md-6">
                <ActiveSwitch
                    v-model:is-active="numeric"
                    :true-value="true"
                    :false-value="false"
                    :label-text="translations['Numbers']"
                    :reverse="true"
                />
            </b-col>
        </b-row>
    </Vue3SlideUpDown>
</template>
<script>
import { indexOf } from "lodash";
import { Vue3SlideUpDown } from "vue3-slide-up-down";
import boxMixin from "../../../js/boxMixin";

import SelectWithAjax from "@components/Form/SelectWithAjax.vue";
import InputComponent from "@components/Form/InputComponent.vue";

export default {
    name: "CodeConfig",
    mixins: [boxMixin],
    components: {
        SelectWithAjax,
        Vue3SlideUpDown,
        InputComponent,
    },
    data() {
        return {
            translations: {
                "Generate type": this.$t("Generate type"),
                Range: this.$t("Range"),
                Random: this.$t("Random"),
                "Code from the generator will start": this.$t(
                    "Code from the generator will start"
                ),
                "Code from the generator will end": this.$t(
                    "Code from the generator will end"
                ),
                "Count of codes to generate": this.$t(
                    "Count of codes to generate"
                ),
                "Count of characters in code": this.$t(
                    "Count of characters in code"
                ),
                "If blank, each code will be generated with a random length between 6 and 18 chars.":
                    this.$t(
                        "If blank, each code will be generated with a random length between 6 and 18 chars."
                    ),
                Letters: this.$t("Letters"),
                Numbers: this.$t("Numbers"),
            },
        };
    },
    computed: {
        typeOptions() {
            return [
                { id: "range", name: this.translations["Range"] },
                { id: "random", name: this.translations["Random"] },
            ];
        },
        alpha: {
            get() {
                return this.settings.code.structure.includes("alpha");
            },
            set(value) {
                if (value) {
                    if (indexOf(this.settings.code.structure, "alpha") === -1) {
                        this.settings.code.structure.push("alpha");
                    }
                } else {
                    const index = indexOf(
                        this.settings.code.structure,
                        "alpha"
                    );
                    if (index !== -1) {
                        this.settings.code.structure.splice(index, 1);
                    }
                }
            },
        },
        numeric: {
            get() {
                return this.settings.code.structure.includes("numeric");
            },
            set(value) {
                if (value) {
                    if (
                        indexOf(this.settings.code.structure, "numeric") === -1
                    ) {
                        this.settings.code.structure.push("numeric");
                    }
                } else {
                    const index = indexOf(
                        this.settings.code.structure,
                        "numeric"
                    );
                    if (index !== -1) {
                        this.settings.code.structure.splice(index, 1);
                    }
                }
            },
        },
    },
};
</script>