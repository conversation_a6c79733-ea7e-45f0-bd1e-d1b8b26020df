<template>
    <data-table
        v-model="ids"
        v-model:query="query"
        :enable-mobile="true"
        :filter-options="filterOptions"
        :filters="true"
        :get-data="getData"
        app-name="discounts-list"
        v-bind="table"
        @pagination-change-page="paginate"
    >
        <TableActions
            v-model="ids"
            :actions="bulkActions"
            :delete-url="`/admin/api/core/discounts/code-pro/${this.$route.params.id}`"
            :enable-mobile="true"
            app-key="discounts"
            delete-type="delete"
            @success="handleBulkActions"
        />
    </data-table>
    <pre>{{ tableData }}</pre>
</template>
<script>
import { RequestModel } from "../../js/Discounts.js";
import useSharedDiscounts from "./../../js/useSharedDiscounts.js";
import { toast } from "@js/toast";
import { markRaw } from "vue";

import DataTable from "@components/Table";
import TableActions from "@components/TableActions.vue";
import StartToExpires from "../DistountsList/Helpers/StartToExpires.vue";
import ActiveDiscountToggle from "../DistountsList/Helpers/ActiveDiscountToggle.vue";
import Actions from "../DistountsList/Helpers/Actions.vue";
import RemoveDiscount from "../DistountsList/Helpers/RemoveDiscount.vue";
import OrderUses from "../DistountsList/Helpers/OrderUses.vue";
import CodeConditions from "./Helpers/CodeConditions.vue";
import CodeProCodeName from "./Helpers/CodeProCodeName.vue";

export default {
    name: "CodeProList",
    components: {
        DataTable,
        TableActions,
    },
    setup() {
        const { discount } = useSharedDiscounts();
        return {
            discount,
        };
    },
    data() {
        return {
            loading: true,
            model: new RequestModel(
                `/admin/api/core/discounts/code-pro/${this.$route.params.id}`
            ),
            tableData: {
                data: [],
            },
            page: this.$route.query?.page || 1,
            perPage: this.$route.query?.perpage || 25,
            query: this.$route.query || {},
            ids: [],

            translations: {
                Discount: this.$t("Discount"),
                Action: this.$t("Action"),
                "Starts/Expires": this.$t("Starts/Expires"),
                "Last updated": this.$t("Last updated"),
                "Last update of the product discount list": this.$t(
                    "Last update of the product discount list"
                ),
                Uses: this.$t("Uses"),
                Limit: this.$t("Limit"),
                Active: this.$t("Active"),
                Remove: this.$t("Remove"),
                "Discount deleted successfully.": this.$t(
                    "Discount deleted successfully."
                ),
                "Status changed successfully.": this.$t(
                    "Status changed successfully."
                ),
                Type: this.$t("Type"),
                "Has customer group": this.$t("Has customer group"),
                Yes: this.$t("Yes"),
                No: this.$t("No"),
                "Only customer": this.$t("Only customer"),
                "Free shipping": this.$t("Free shipping"),
                Flat: this.$t("Flat"),
                "Percent %": this.$t("Percent %"),
                "Fixed discount": this.$t("Fixed discount"),
                Volume: this.$t("Volume"),
                Countdown: this.$t("Countdown"),
                Code: this.$t("Code"),
                "Set status active": this.$t("Set status active"),
                "Set status unactive": this.$t("Set status unactive"),
                "Status set to active successfully": this.$t(
                    "Status set to active successfully"
                ),
                "Status set to unactive successfully": this.$t(
                    "Status set to unactive successfully"
                ),
                "Error while setting the status": this.$t(
                    "Error while setting the status"
                ),
                Conditions: this.$t("Conditions"),
                "Time used": this.$t("Time used"),
                "Uses left": this.$t("Uses left"),
                "Start date": this.$t("Start date"),
                "End date": this.$t("End date"),
                
            },
        };
    },
    computed: {
        filterOptions() {
            return [
                {
                    key: "active",
                    label: this.translations["Active"],
                    options: [
                        { value: "yes", label: this.translations["Yes"] },
                        { value: "no", label: this.translations["No"] },
                    ],
                },
                {
                    key: "time_used",
                    label: this.translations["Time used"],
                    type: "number",
                },
                {
                    key: "uses_left",
                    label: this.translations["Uses left"],
                    type: "number",
                },
                {
                    key: "start_date",
                    label: this.translations["Start date"],
                    type: "date",
                },
                {
                    key: "end_date",
                    label: this.translations["End date"],
                    type: "date",
                },
            ];
        },
        table() {
            return {
                data: this.tableData,
                isLoading: this.loading,
                columns: [
                    {
                        column: "name",
                        key: "name",
                        title: this.translations["Discount"],
                        sortable: true,
                        component: markRaw(CodeProCodeName),
                    },
                    {
                        column: "actions-discount",
                        key: "actions-discount",
                        title: this.translations["Action"],
                        codePro: true,
                        sortable: false,
                        component: markRaw(Actions),
                    },
                    {
                        column: "date_start",
                        key: "date_start",
                        title: this.translations["Starts/Expires"],
                        sortable: false,
                        component: markRaw(StartToExpires),
                    },
                    {
                        column: "uses",
                        key: "uses",
                        title: this.translations["Uses"],
                        sortable: false,
                        component: markRaw(OrderUses),
                    },
                    {
                        column: "max_uses",
                        key: "max_uses",
                        title: this.translations["Limit"],
                        type: "number",
                        sortable: false,
                        format: (value) => {
                            return value?.max_uses || "∞";
                        },
                    },
                    {
                        column: "conditions",
                        key: "conditions",
                        title: this.translations["Conditions"],
                        sortable: false,
                        component: markRaw(CodeConditions),
                    },
                    {
                        column: "active",
                        key: "active",
                        title: this.translations["Active"],
                        sortable: false,
                        trueValue: 1,
                        falseValue: 0,
                        changeActive: this.changeActive,
                        component: markRaw(ActiveDiscountToggle),
                    },
                    {
                        column: "actions",
                        key: "actions",
                        title: " ",
                        sortable: false,
                        removeDiscount: this.removeDiscount,
                        component: markRaw(RemoveDiscount),
                    },
                ],
            };
        },
        bulkActions() {
            return [
                {
                    label: this.translations["Set status active"],
                    icon: "far fa-arrow-circle-up",
                    action: "active",
                    type: "post",
                    confirm: false,
                    messageSuccess:
                        this.translations["Status set to active successfully"],
                    messageError:
                        this.translations["Error while setting the status"],
                    url: `/admin/api/core/discounts/${this.$route.params.id}/statuses`,
                    body: {
                        ids: this.ids,
                        status: "yes",
                    },
                },
                {
                    label: this.translations["Set status unactive"],
                    icon: "far fa-arrow-circle-down",
                    action: "unactive",
                    type: "post",
                    confirm: false,
                    messageSuccess:
                        this.translations[
                            "Status set to unactive successfully"
                        ],
                    messageError:
                        this.translations["Error while setting the status"],
                    url: `/admin/api/core/discounts/${this.$route.params.id}/statuses`,
                    body: {
                        ids: this.ids,
                        status: "no",
                    },
                },
            ];
        },
    },
    methods: {
        async handleBulkActions(value) {
            if (value.action === "delete") {
                await this.removeRecordFromList(this.ids);
                if (
                    this.tableData.data.length === 0 &&
                    (this.page == 1 || this.query.page == 1)
                ) {
                    await this.getData();
                } else if (
                    this.page > 1 &&
                    this.tableData.data.length === 0 &&
                    !this.tableData.next_page_url
                ) {
                    this.page = this.page - 1;
                    this.query.page = this.query.page - 1;
                    await this.getData();
                }
            } else if (value.action === "active") {
                this.tableData.data = this.tableData.data.map((item) => {
                    if (this.ids.includes(item.id)) {
                        item.active = "yes";
                    }
                    return item;
                });
            } else if (value.action === "unactive") {
                this.tableData.data = this.tableData.data.map((item) => {
                    if (this.ids.includes(item.id)) {
                        item.active = "no";
                    }
                    return item;
                });
            }
        },
        async removeRecordFromList(data) {
            this.tableData.data = this.tableData.data.filter((item) =>
                Array.isArray(data)
                    ? !this.ids.includes(item.id)
                    : item.id !== data
            );

            if (this.tableData.data.length === 0 && this.query.page > 1) {
                this.query.page = this.query.page - 1;
                this.page -= 1;
                await this.getData(false);
            } else if (
                this.tableData.data.length === 0 &&
                this.tableData?.data?.next_page_url
            ) {
                await this.getData(false);
            }
            if (Array.isArray(data)) {
                this.ids = [];
            }
        },
        async removeDiscount(data) {
            data.deleteLoader = true;
            try {
                await this.model.deleteBulk([data.id]);
                await this.removeRecordFromList(data.id);

                toast.success(
                    this.translations["Discount deleted successfully."]
                );
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                data.deleteLoader = false;
            }
        },
        async changeActive(data, value) {
            data.activeLoader = true;

            try {
                let payload = {
                    ids: [data.id],
                    status: value,
                };

                await this.model.post("status", payload);

                toast.success(
                    this.translations["Status changed successfully."]
                );
            } catch (err) {
                this.$errorResponse(err);
                data.active = !value === "yes" ? "no" : "yes";
            } finally {
                data.activeLoader = false;
            }
        },
        async paginate(page) {
            this.page = page;
            await this.getData();
        },
        async getData(loader = true) {
            this.loading = loader;

            this.model.where(this.query);

            try {
                const { discount, records } = await this.model.paginate(
                    this.query?.page || this.page,
                    this.query?.perpage || this.perPage
                );
                this.discount = discount;
                this.tableData = records;

                this.$router.push({
                    query: {
                        ...this.$route.query,
                        page: this.tableData.current_page,
                    },
                });
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.loading = false;
            }
        },
    },
};
</script>
