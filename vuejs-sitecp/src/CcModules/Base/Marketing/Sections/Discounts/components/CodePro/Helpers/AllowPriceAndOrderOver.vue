<template>
    <Vue3SlideUpDown
        :model-value="show"
        :duration="160"
        class="d-flex flex-column gap-3"
    >
        <ActiveSwitch
            v-model:is-active="allowPriceProxy"
            :error="
                translations[responseErrors['allow_price']] ||
                responseErrors['allow_price']
            "
            :label-text="
                translations['Where the price of the product is minimum']
            "
        />
        <Vue3SlideUpDown :model-value="!!allowPriceProxy" :duration="130">
            <CurrencyComponent
                v-model="orderOverProxy"
                :error="
                    translations[responseErrors['order_over']] ||
                    responseErrors['order_over']
                "
                :label-text="translations['Amount']"
                :min="0"
                :no-margin="true"
            />
        </Vue3SlideUpDown>
    </Vue3SlideUpDown>
</template>
<script>
import { Vue3SlideUpDown } from "vue3-slide-up-down";
import ActiveSwitch from "@components/Form/ActiveSwitch.vue";
import CurrencyComponent from "@components/Form/CurrencyComponent.vue";

export default {
    name: "AllowPriceAndOrderOver",
    components: {
        Vue3SlideUpDown,
        ActiveSwitch,
        CurrencyComponent,
    },
    props: {
        translations: {
            type: Object,
            default: {},
        },
        responseErrors: {
            type: Object,
            default: {},
        },
        show: {
            type: Boolean,
            default: false,
        },
        allowPrice: {
            type: [Boolean, Number, String],
            default: 0,
        },
        orderOver: {
            type: [Number, String],
            default: null,
        },
    },
    computed: {
        allowPriceProxy: {
            get() {
                return this.allowPrice;
            },
            set(value) {
                this.$emit("update:allowPrice", value);
            },
        },
        orderOverProxy: {
            get() {
                return this.orderOver;
            },
            set(value) {
                this.$emit("update:orderOver", value);
            },
        },
    },
    emits: ["update:allowPrice", "update:orderOver"],
};
</script>