<template>
    <div class="condition-discount-wrap">
        <div class="num">
            <span>{{ index + 1 }}</span>
        </div>
        <div class="content">
            <p class="label-500">
                {{ translations["Discount condition"] }}
            </p>
            <b-card>
                <div
                    class="d-flex align-items-center gap-3 justify-content-between mb-3"
                >
                    <p class="label-500 m-0">
                        {{ translations["Discount type"] }}
                    </p>
                    <a
                        class="pt-1"
                        href="javascript:void(0);"
                        @click.stop="() => removeRow(index)"
                    >
                        <i
                            :style="{ color: '#7a7d84', 'font-size': '18px' }"
                            class="fa-light fa-circle-xmark"
                        ></i>
                    </a>
                </div>
                <b-row>
                    <b-col class="col-12 d-flex flex-column gap-3">
                        <SelectWithAjax
                            v-model:val="settings.type"
                            :no-margin="true"
                            :label="translations['Discount type']"
                            :options="typeOptions"
                            :can-clear="false"
                        />
                        <Vue3SlideUpDown
                            :duration="130"
                            :model-value="
                                ['percent', 'flat'].includes(settings.type)
                            "
                        >
                            <InputComponent
                                v-model="settings.type_value"
                                type="number"
                                :digits="2"
                                :column-size="4"
                                :label-text="translations['Discount value']"
                                :unit="
                                    settings.type === 'flat'
                                        ? this.serverSettings('currency.sign')
                                        : '%'
                                "
                                :no-margin="true"
                            />
                        </Vue3SlideUpDown>
                        <hr class="m-0" />
                        <p class="label-500 m-0">
                            {{ translations["Discount target"] }}
                        </p>
                        <SelectWithAjax
                            v-model:val="settings.setting"
                            :no-margin="true"
                            :label="translations['Discount target']"
                            :options="settingsOptions"
                            :can-clear="false"
                            :column-style="true"
                            :cols-width="12"
                            :watch-options="true"
                        />
                        <AllowPriceAndOrderOver
                            :show="
                                settings.setting === 'all' &&
                                settings.type !== 'shipping'
                            "
                            v-model:allow-price="settings.allow_price"
                            v-model:order-over="settings.order_over"
                            :translations="translations"
                            :response-errors="responseErrors"
                        />
                        <Vue3SlideUpDown
                            :model-value="
                                settings.setting === 'all' &&
                                settings.type === 'shipping'
                            "
                            v-bind="slideBinds"
                        >
                            <ActiveSwitch
                                v-model:is-active="settings.force_save"
                                :error="
                                    translations[
                                        responseErrors['force_save']
                                    ] || responseErrors['force_save']
                                "
                                :label-text="
                                    translations[
                                        'Save the discount on your order'
                                    ]
                                "
                            />
                        </Vue3SlideUpDown>
                        <Vue3SlideUpDown
                            :model-value="settings.setting === 'order_over'"
                            v-bind="slideBinds"
                        >
                            <CurrencyComponent
                                v-model="settings.order_over"
                                :error="
                                    translations[
                                        responseErrors['order_over']
                                    ] || responseErrors['order_over']
                                "
                                :label-text="translations['Amount']"
                                :min="0"
                                :no-margin="true"
                            />
                            <ActiveSwitch
                                v-model:is-active="settings.force_save"
                                :error="
                                    translations[
                                        responseErrors['force_save']
                                    ] || responseErrors['force_save']
                                "
                                :label-text="
                                    translations[
                                        'Save the discount on your order'
                                    ]
                                "
                            />
                        </Vue3SlideUpDown>
                        <Vue3SlideUpDown
                            :model-value="settings.setting === 'product'"
                            v-bind="slideBinds"
                        >
                            <SelectWithAjax
                                v-model:val="settings.products"
                                :error="
                                    translations[responseErrors['products']] ||
                                    responseErrors['products']
                                "
                                :options="productOptions"
                                :placeholder="translations['Select products']"
                                api-url="/admin/api/core/products/search"
                                v-bind="selectBinds"
                                @options="(val) => (productOptions = val)"
                            />
                            <AllowPriceAndOrderOver
                                :show="true"
                                v-model:allow-price="settings.allow_price"
                                v-model:order-over="settings.order_over"
                                :translations="translations"
                                :response-errors="responseErrors"
                            />
                        </Vue3SlideUpDown>
                        <Vue3SlideUpDown
                            :model-value="
                                settings.setting === 'product_category'
                            "
                            v-bind="slideBinds"
                        >
                            <div class="info-box no-border info-box-warning">
                                <p class="m-0" v-html="categoryMsg"></p>
                            </div>
                            <SelectWithAjax
                                v-model:val="settings.product_categories"
                                :error="
                                    translations[
                                        responseErrors['product_categories']
                                    ] || responseErrors['product_categories']
                                "
                                :options="categoryOptions"
                                :placeholder="
                                    translations['Select category/categories']
                                "
                                api-url="/admin/api/core/product-categories/search"
                                v-bind="selectBinds"
                                @options="(val) => (categoryOptions = val)"
                            />
                            <AllowPriceAndOrderOver
                                :show="true"
                                v-model:allow-price="settings.allow_price"
                                v-model:order-over="settings.order_over"
                                :translations="translations"
                                :response-errors="responseErrors"
                            />
                        </Vue3SlideUpDown>
                        <Vue3SlideUpDown
                            :model-value="settings.setting === 'product_vendor'"
                            v-bind="slideBinds"
                        >
                            <SelectWithAjax
                                v-model:val="settings.vendors"
                                :error="
                                    translations[responseErrors['vendors']] ||
                                    responseErrors['vendors']
                                "
                                :options="vendorOptions"
                                :placeholder="translations['Select vendor/s']"
                                api-url="/admin/api/core/vendors/search"
                                v-bind="selectBinds"
                                @options="(val) => (vendorOptions = val)"
                            />
                            <AllowPriceAndOrderOver
                                :show="true"
                                v-model:allow-price="settings.allow_price"
                                v-model:order-over="settings.order_over"
                                :translations="translations"
                                :response-errors="responseErrors"
                            />
                        </Vue3SlideUpDown>
                        <Vue3SlideUpDown
                            :model-value="settings.setting === 'selection'"
                            v-bind="slideBinds"
                        >
                            <SelectWithAjax
                                v-model:val="settings.selection"
                                :error="
                                    translations[responseErrors['selection']] ||
                                    responseErrors['selection']
                                "
                                :options="selectionOptions"
                                :placeholder="
                                    translations['Select smart collection/s']
                                "
                                api-url="/admin/api/core/collections/search"
                                v-bind="selectBinds"
                                @options="(val) => (selectionOptions = val)"
                            />
                            <AllowPriceAndOrderOver
                                :show="true"
                                v-model:allow-price="settings.allow_price"
                                v-model:order-over="settings.order_over"
                                :translations="translations"
                                :response-errors="responseErrors"
                            />
                        </Vue3SlideUpDown>
                        <Vue3SlideUpDown
                            :model-value="
                                settings.setting === 'category_vendor'
                            "
                            v-bind="slideBinds"
                        >
                            <div class="info-box no-border info-box-warning">
                                <p class="m-0" v-html="categoryMsg"></p>
                            </div>
                            <SelectWithAjax
                                v-model:val="settings.product_categories"
                                :error="
                                    translations[
                                        responseErrors['product_categories']
                                    ] || responseErrors['product_categories']
                                "
                                :label="translations['Categories']"
                                :options="categoryOptions"
                                :placeholder="
                                    translations['Select category/categories']
                                "
                                api-url="/admin/api/core/product-categories/search"
                                v-bind="selectBinds"
                                @options="(val) => (categoryOptions = val)"
                            />
                            <SelectWithAjax
                                v-model:val="settings.vendors"
                                :error="
                                    translations[responseErrors['vendors']] ||
                                    responseErrors['vendors']
                                "
                                :label="translations['Vendors']"
                                :options="vendorOptions"
                                :placeholder="translations['Select vendor/s']"
                                api-url="/admin/api/core/vendors/search"
                                v-bind="selectBinds"
                                @options="(val) => (vendorOptions = val)"
                            />
                            <AllowPriceAndOrderOver
                                :show="true"
                                v-model:allow-price="settings.allow_price"
                                v-model:order-over="settings.order_over"
                                :translations="translations"
                                :response-errors="responseErrors"
                            />
                        </Vue3SlideUpDown>
                    </b-col>
                </b-row>
            </b-card>
            <slot name="add">
               
            </slot>
        </div>
    </div>
</template>
<script>
import SelectWithAjax from "@components/Form/SelectWithAjax.vue";
import InputComponent from "@components/Form/InputComponent.vue";
import { Vue3SlideUpDown } from "vue3-slide-up-down";
import CurrencyComponent from "@components/Form/CurrencyComponent.vue";
import ActiveSwitch from "@components/Form/ActiveSwitch.vue";
import AllowPriceAndOrderOver from "./AllowPriceAndOrderOver.vue";

export default {
    name: "ConditionWrapWithNum",
    components: {
        SelectWithAjax,
        InputComponent,
        Vue3SlideUpDown,
        CurrencyComponent,
        ActiveSwitch,
        AllowPriceAndOrderOver,
    },
    props: {
        settings: {
            type: Object,
            defailt: {},
        },
        index: {
            type: Number,
            default: 0,
        },
        responseErrors: {
            type: Object,
            default: {},
        },
        addNewRow: {
            type: Function,
            default: () => {},
        },
        removeRow: {
            type: Function,
            default: () => {},
        },
    },
    data() {
        return {
            productOptions: [],
            categoryOptions: [],
            vendorOptions: [],
            selectionOptions: [],
            translations: {
                "Discount condition": this.$t("Discount condition"),
                "Discount type": this.$t("Discount type"),
                "Fixed amount": this.$t("Fixed amount"),
                Percentage: this.$t("Percentage"),
                "Free shipping": this.$t("Free shipping"),
                "Discount value": this.$t("Discount value"),
                "Discount target": this.$t("Discount target"),
                Amount: this.$t("Amount"),
                "Save the discount on your order": this.$t(
                    "Save the discount on your order"
                ),
                "Select products": this.$t("Select products"),
                "Select category/categories": this.$t(
                    "Select category/categories"
                ),
                "Select vendor/s": this.$t("Select vendor/s"),
                "Select smart collection/s": this.$t(
                    "Select smart collection/s"
                ),
                Categories: this.$t("Categories"),
                Vendors: this.$t("Vendors"),
                "Registered users only": this.$t("Registered users only"),
                "The discount will only be available to registered users.":
                    this.$t(
                        "The discount will only be available to registered users."
                    ),
                "For every product in the cart": this.$t(
                    "For every product in the cart"
                ),
                "Orders over": this.$t("Orders over"),
                "Specific product/s": this.$t("Specific product/s"),
                "Product category/categories": this.$t(
                    "Product category/categories"
                ),
                "Product vendor/s": this.$t("Product vendor/s"),
                "Smart collection/s": this.$t("Smart collection/s"),
                "Product category/categories and vendor/s": this.$t(
                    "Product category/categories and vendor/s"
                ),
                "Where the price of the product is minimum": this.$t(
                    "Where the price of the product is minimum"
                ),
                "<strong>The discount will only be applied to the main product category.</strong><br/> For example: If a product is added to the Women > Shoes category and to the subcategory Season > Summer, the discount will only be applied to the main category (Women > Shoes) and will not be applied to (Season > Summer)":
                    this.$t(
                        "<strong>The discount will only be applied to the main product category.</strong><br/> For example: If a product is added to the Women > Shoes category and to the subcategory Season > Summer, the discount will only be applied to the main category (Women > Shoes) and will not be applied to (Season > Summer)"
                    ),
            },
        };
    },
    computed: {
        typeOptions() {
            return [
                { id: "flat", name: this.translations["Fixed amount"] },
                { id: "percent", name: this.translations["Percentage"] },
                { id: "shipping", name: this.translations["Free shipping"] },
            ];
        },
        settingsOptions() {
            return [
                {
                    id: "all",
                    name: this.translations["For every product in the cart"],
                },
                { id: "order_over", name: this.translations["Orders over"] },
                ...(this.settings.type !== "shipping"
                    ? [
                          {
                              id: "product",
                              name: this.translations["Specific product/s"],
                          },
                          {
                              id: "product_category",
                              name: this.translations[
                                  "Product category/categories"
                              ],
                          },
                          {
                              id: "product_vendor",
                              name: this.translations["Product vendor/s"],
                          },
                          {
                              id: "selection",
                              name: this.translations["Smart collection/s"],
                          },
                          {
                              id: "category_vendor",
                              name: this.translations[
                                  "Product category/categories and vendor/s"
                              ],
                          },
                      ]
                    : []),
            ];
        },
        slideBinds() {
            return {
                duration: 160,
                class: "d-flex flex-column gap-3",
            };
        },
        categoryMsg() {
            return this.translations[
                "<strong>The discount will only be applied to the main product category.</strong><br/> For example: If a product is added to the Women > Shoes category and to the subcategory Season > Summer, the discount will only be applied to the main category (Women > Shoes) and will not be applied to (Season > Summer)"
            ];
        },
        selectBinds() {
            return {
                colsWidth: 12,
                columnStyle: true,
                emitOnGet: true,
                noMargin: true,
                requestOnOpen: true,
                requestOnSearch: true,
                resolveOnLoad: !!this.$route.params.id,
                searchable: true,
                mode: "tags",
            };
        },
    },
    watch: {
        "settings.type"(value) {
            if (value === "shipping") {
                this.settings.setting = "all";
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.condition-discount-wrap {
    display: grid;
    grid-template-columns: 36px auto;
    position: relative;
    gap: 6px;
    padding-bottom: 4px;

    .num {
        display: flex;
        justify-content: start;
        align-items: start;
        position: relative;

        span {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: var(--Border-radius---cc-border-radius-full, 1000px);
            background: var(--Color-Surface-Neutral---cc-color-bg, #fff);
            aspect-ratio: 1/1;
            color: var(--Color-Text-Body---cc-color-text-link, #8d58e0);
            font-size: 24px;
            font-style: normal;
            font-weight: 900;
            height: 30px;
            width: 30px;

            &::after {
                content: "";
                position: absolute;
                bottom: 0px;
                top: 38px;
                width: 2px;
                background: var(
                    --Color-Border-Neutral---cc-color-border,
                    #d6d9e9
                );
                z-index: -1;
            }
        }
    }
    .content {
        margin-top: 6px;
    }
}
</style>