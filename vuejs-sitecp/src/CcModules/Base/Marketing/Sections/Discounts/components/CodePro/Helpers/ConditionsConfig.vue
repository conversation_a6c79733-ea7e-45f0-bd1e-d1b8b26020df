<template>
    <transition-group name="list" tag="ul" class="m-0 p-0">
        <li v-for="(_, index) in settings.condition" :key="index">
            <ConditionWrapWithNum
                v-model:settings="settings.condition[index]"
                :index="index"
                :response-errors="responseErrors"
                :addNewRow="addNewRow"
                :removeRow="removeRow"
            >
                <template #add>
                    <div
                        v-if="index <= 4 && index === settings.condition.length - 1"
                        class="d-flex align-items-center justify-content-center mt-3"
                    >
                        <a
                            class="add-action-btn"
                            href="javascript:void(0);"
                            @click="addNewRow"
                        >
                            <i class="far fa-plus"></i>
                            {{ translations["Add new condition"] }}
                        </a>
                    </div>
                </template>
            </ConditionWrapWithNum>
        </li>
    </transition-group>
</template>
<script>
import boxMixin from "../../../js/boxMixin";
import ConditionWrapWithNum from "./ConditionWrapWithNum.vue";

export default {
    name: "ConditionsConfig",
    components: { ConditionWrapWithNum },
    mixins: [boxMixin],
    data() {
        return {
            translations: {
                "Add new condition": this.$t("Add new condition"),
            },
        };
    },
    methods: {
        addNewRow() {
            this.settings.condition.push({
                type: "flat",
                setting: "all",
                type: null,
                allow_price: 0,
                order_over: null,
                force_save: 0,
                vendors: [],
                product_categories: [],
                selection: [],
                products: [],
            });
        },
        removeRow(index) {
            this.settings.condition.splice(index, 1);
            if (this.settings.condition.length === 0) {
                this.addNewRow();
            }
        },
    },
};
</script>
<style>
.list-enter-active,
.list-leave-active {
    transition: all 0.25s ease;
}
.list-enter-from,
.list-leave-to {
    opacity: 0;
    transform: translateY(-30px);
}
</style>