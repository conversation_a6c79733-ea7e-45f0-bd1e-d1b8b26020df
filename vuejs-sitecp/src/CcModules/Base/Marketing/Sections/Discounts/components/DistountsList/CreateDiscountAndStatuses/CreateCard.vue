<template>
    <a
        class="card-bordered d-flex flex-row align-items-center gap-3 p-3"
        href="javascript:void(0);"
        @click="()=>handleRedirect(type)"
    >
        <div class="icon-discount">
            <figure class="m-0" v-html="mapIcon(type)"></figure>
        </div>
        <div class="d-flex flex-column">
            <p class="label-500-16px m-0">
                {{ translations[type.title] }}
                <span v-if="type.key === 'cart_rules'" class="ai-badge d-inline-block">{{translations['AI powered']}}</span>
            </p>
            <span class="text-400-secondary text-wrap">
                {{ translations[type.text] }}
            </span>
        </div>
    </a>
</template>
<script>
export default {
    name: 'CreateCard',
    props: {
        handleRedirect: {
            type: Function,
            default: () => '',
        },
        mapIcon: {
            type: Function,
            default: () => '',
        },
        translations: {
            type: Object,
            default: {},
        },
        type: {
            type: Object,
            default: {},
        },
    }
}
</script>
