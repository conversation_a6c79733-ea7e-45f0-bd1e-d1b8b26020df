<template>
    {{ type }}
    <Loading
        v-if="loading"
        :loading="loading"
        class="app-loader-center"
    />
    <SettingsBox
        v-else
        v-model:boxes="boxes"
        v-model:settings="settings"
        :response-errors="responseErrors"
        :translationLabels="translations"
        box-key="discounts"
    >
        <template #discountTarget>
            <DiscountTarget
                v-model="settings"
                :response-errors="responseErrors"
                :translation-labels="translations"
            />
        </template>
        <template #dateRange>
            <DateRangeDiscount
                v-model="settings"
                :response-errors="responseErrors"
                :translation-labels="translations"
            />
        </template>
        <template #dateRangeNoTimer>
            <DateRangeDiscount
                v-model="settings"
                :hide-timer="true"
                :response-errors="responseErrors"
                :translation-labels="translations"
            />
        </template>
        <template #discountLimits>
            <DiscountLimits
                v-model="settings"
                :response-errors="responseErrors"
                :translation-labels="translations"
            />
        </template>
        <template #codeGenerator>
            <CodeGenerator
                v-model="settings"
                :response-errors="responseErrors"
                :translation-labels="translations"
            />
        </template>
        <template #quantityConfiguration>
            <QuantityConfig
                v-model="settings"
                :response-errors="responseErrors"
                :translation-labels="translations"
            />
        </template>
        <template #previewEffect>
            <PreviewEffect
                v-model="settings"
                :response-errors="responseErrors"
                :translation-labels="translations"
            />
        </template>
        <template #codeProSettings>
            <CodePro
                v-model="settings"
                :response-errors="responseErrors"
                :translation-labels="translations"
            />
        </template>
    </SettingsBox>

    <SubmitChanges
        v-if="isSettingsSet"
        v-model="settings"
        v-model:response-errors="responseErrors"
        :save-func="submitDiscount"
        :submitLoader="submitLoader"
    />

    <pre>{{ settings }}</pre>
</template>
<script>
import _ from 'lodash';

import Discounts from './../../js/Discounts.js'
import createOrEditMixin from './../../js/createOrEditMixin.js'
import useSharedDiscounts from './../../js/useSharedDiscounts.js'

import Loading from '@components/Loading.vue';
import SettingsBox from '@components/SettingsBox/SettingsBox.vue';
import SubmitChanges from '@components/SubmitChanges.vue';

import DiscountTarget from './Helpers/DiscountTarget.vue'
import DateRangeDiscount from './Helpers/DateRangeDiscount.vue'
import DiscountLimits from './Helpers/DiscountLimits.vue'
import CodeGenerator from "./Helpers/CodeGenerator.vue";
import QuantityConfig from "./Helpers/QuantityConfig.vue";
import PreviewEffect from "./Helpers/PreviewEffect.vue";
import CodePro from "./Helpers/CodePro.vue";

export default {
    name: 'CreateOrEditDiscount',
    mixins: [createOrEditMixin],
    components: {
        PreviewEffect,
        CodeGenerator,
        Loading,
        SettingsBox,
        DiscountTarget,
        DateRangeDiscount,
        DiscountLimits,
        SubmitChanges,
        QuantityConfig,
        CodePro
    },
    setup() {
        const {discount} = useSharedDiscounts();

        return {
            discount
        }
    },
    data() {
        return {
            model: new Discounts(),
            responseErrors: {},
            loading: false,
            submitLoader: false,
            isSettingsSet: false,

            settings: {
                active: "no",
                name: "",
                type: null,
                type_value: 0,
                only_customer: 0,
                timer_list: 0,
                timer_details: 0,
                discount_amount_type_in_label: null,
                color: "#FFF",
                text_color: "#000",
                customers: [],
                customer_groups: [],
                customer_groups_target: "no",
                setting: null,
                products: [],
                product_categories: [],
                vendors: [],
                date_start: null,
                date_end: null,
                no_expire: true,
                code_format: "",
                code: null,
                barcode_prefix: 0,
                code_apply: 0,
                apply_regular_price: 0,
                order_over: null,
                force_save: 0,
                selections: [],
                all_regions: 0,
                geo_zone_id: null,
                max_uses: null,
                maxused_user: null,
                end_date: null,
                product_id: null,
                conditions: [
                    {
                        quantity: null,
                        discount_value: null
                    },
                ],
                countdown_discount: 1,
                countdown_description: "",
                countdown_minutes: null,
                countdown_popup_effect: null
            },
            translations: {
                //ERRORS
                "Discount amount type in label is required": this.$t("Discount amount type in label is required"),
                "Name is required": this.$t("Name is required"),
                "Settings must be one of: all, order_over, product, product_category, product_vendor, selection, category_vendor": this.$t(
                    "Settings must be one of: all, order_over, product, product_category, product_vendor, selection, category_vendor"),
                "Text color must be a string": this.$t("Text color must be a string"),
                "Timer details is required": this.$t("Timer details is required"),
                "Timer list is required": this.$t("Timer list is required"),
                "Type must be one of: flat, percent, shipping, quantity": this.$t("Type must be one of: flat, percent, shipping, quantity"),
                "Type value must be a number": this.$t("Type value must be a number"),
                "Show timer in product listing": this.$t("Show timer in product listing"),
                "Show timer in product details page": this.$t("Show timer in product details page"),
                "Field is required": this.$t("Field is required"),
                "Code is required": this.$t("Code is required"),
                "Code discount is required": this.$t("Code discount is required"),
                "Code format must be one of: ean13, ean8": this.$t("Code format must be one of: ean13, ean8"),
                "Color must be a string": this.$t("Color must be a string"),
                "Discount amount type in label must be one of: in_percent, in_flat, dont_change": this.$t("Discount amount type in label must be one of: in_percent, in_flat, dont_change"),
            }
        }
    },
    computed: {
        isCreate() {
            return !!this.$route.name.includes('create');
        },
        isEdit() {
            return !!this.$route.params.id || !!this.settings.id;
        },
        type() {
            if (this.settings.type === 'quantity') {
                return 'quantity';
            } else if (this.settings.code) {
                return 'code';
            } else if (this.settings.is_container == 1) {
                return 'container';
            } else if (this.settings.type === 'code-pro') {
                return 'code-pro';
            } else if (this.settings.is_countdown) {
                return 'countdown';
            } else if (this.settings.type === 'fixed') {
                return 'fixed';
            }
            return this.$route.params.type || 'global';
        },
        id() {
            return this.$route.params.id || this.settings.id || null;
        },
        mainPartPayload() {
            return {
                active: this.settings.active,
                name: this.settings.name,
                date_start: this.settings.date_start || null,
                date_end: this.settings.date_end || null,
                no_expire: this.settings.no_expire,
            }
        },
        discountPartPayloadAll() {
            return {
                ...(this.settings.setting === 'order_over' ? {
                    order_over:this.amountToMinorUnits(this.settings.order_over) ,
                    force_save: this.settings.force_save
                } : {}),
                ...(this.settings.setting === 'product' ? {products: this.settings.products} : {}),
                ...(this.settings.setting === 'product_category' ? {product_categories: this.settings.product_categories} : {}),
                ...(this.settings.setting === 'product_vendor' ? {vendors: this.settings.vendors} : {}),
                ...(this.settings.setting === 'selections' ? {selections: this.settings.selections,} : {}),
                ...(this.settings.setting === 'category_vendor' ? {
                    product_categories: this.settings.product_categories,
                    "vendors": this.settings.vendors
                } : {}),
            }
        },
        customerGroupPartPayload() {
            return {
                customer_groups_target: this.settings.customer_groups_target,
                ...([0, '0', false, 'no'].includes(this.settings.customer_groups_target) ? {customer_groups: this.settings.customer_groups} : {}),
            }
        },
        regionsPartPayload() {
            return {
                all_regions: this.settings.all_regions,
                ...([0, '0', false, 'no'].includes(this.settings.all_regions) ? {geo_zone_id: this.settings.geo_zone_id} : {}),
            }
        },
        customersPartPayload() {
            return {
                only_customer: this.settings.only_customer,
                ...([0, '0', false, 'no'].includes(this.settings.only_customer) ? {customers: this.settings.customers} : {}),
            }
        },
        colorPartPayload() {
            return {
                color: this.settings.color || '#FFF',
                text_color: this.settings.text_color || '#000',
            }
        },
        limitPartPayload() {
            return {
                ...(['all', 'order_over'].includes(this.settings.setting) ? {
                    max_uses: this.settings.max_uses || null,
                    maxused_user: this.settings.max_uses || null,
                    unlimited: this.settings.unlimited || false,
                    unlimited_user: this.settings.unlimited_user || false,
                } : {}),
            }
        },
        payload() {
            return {
                global: {
                    ...this.mainPartPayload,
                    timer_list: this.settings.timer_list || 0,
                    timer_details: this.settings.timer_details || 0,
                    type: this.settings.type,
                    ...(['percent', 'flat'].includes(this.settings.type) ? {type_value: this.settings.type !== 'percent' ? this.amountToMinorUnits(this.settings.type_value) : parseFloat(this.settings.type_value)} : {}),
                    ...this.discountPartPayloadAll,
                    ...this.colorPartPayload,
                    ...this.customerGroupPartPayload,
                    ...this.customersPartPayload,
                    ...this.limitPartPayload,
                    discount_amount_type_in_label: this.settings.discount_amount_type_in_label,

                    "only_customer": 1
                },
                code: {
                    ...this.mainPartPayload,

                    code_discount: 1,
                    code_format: this.settings.code_format,
                    code: this.settings.code,
                    ...(['ean13', 'ean8'].includes(this.settings.code_format) ? {barcode_prefix: this.settings.barcode_prefix} : {}),
                    code_apply: this.settings.code_apply,
                    ...([1, '1', true, 'yes'].includes(this.settings.code_apply) ? {apply_regular_price: this.settings.apply_regular_price} : {}),
                    type: this.settings.type,
                    ...(['percent', 'flat'].includes(this.settings.type) ? {type_value: this.settings.type !== 'percent' ? this.amountToMinorUnits(this.settings.type_value) : parseFloat(this.settings.type_value)} : {}),
                    settings: this.settings.setting,

                    ...this.discountPartPayloadAll,
                    ...this.customerGroupPartPayload,
                    ...this.customersPartPayload,
                    ...this.regionsPartPayload,
                    ...this.limitPartPayload
                },
                container: {
                    ...this.mainPartPayload,

                    type: "percent",
                    is_container: 1,
                    type_value: this.amountToMinorUnits(this.settings.type_value),
                    settings: this.settings.setting,

                    ...this.discountPartPayloadAll,
                    ...this.customerGroupPartPayload,
                    ...this.regionsPartPayload,
                },
                fixed: {
                    ...this.mainPartPayload,
                    type: "fixed",
                    discount_amount_type_in_label: this.settings.discount_amount_type_in_label,
                    ...this.colorPartPayload,
                    ...this.customerGroupPartPayload,

                    timer_in_listing: this.settings.timer_list || 0,
                    timer_in_details: this.settings.timer_details || 0,
                },
                quantity: {
                    ...this.mainPartPayload,
                    type: "quantity",
                    ...this.customerGroupPartPayload,
                    product_id: this.settings.product_id || null,
                    conditions: (this.settings.conditions || []).map(x => {
                        return {
                            ...x,
                            discount_value: this.amountToMinorUnits(x.discount_value),
                        }
                    }),
                },
                countdown: {
                    ...this.mainPartPayload,
                    countdown_discount: 1,
                    ...this.limitPartPayload,
                    countdown_description: this.settings.countdown_description,
                    countdown_popup_effect: this.settings.countdown_popup_effect,
                    type: this.settings.type,
                    type_value: this.amountToMinorUnits(this.settings.type_value),
                    settings: this.settings.setting,
                    countdown_minutes: this.settings.countdown_minutes || 0,
                    only_customer: this.settings.only_customer,
                    ...this.customerGroupPartPayload,
                },
                'code-pro': {
                    active: this.settings.active,
                    name: this.settings.name
                }
            }
        }
    },
    methods: {
        setBoxes() {
            this.boxes = this[this.type];
        },
        amountToMinorUnits(amount) {
            return (parseFloat(amount) || 0) * 100;
        },
        amountFromMinorUnits(units) {
            return (parseFloat(units) || 0) / 100;
        },
        setDataPrices(data) {
            let temp = _.cloneDeep(data);

            temp.type_value = this.amountFromMinorUnits(temp.type_value);

            if (Array.isArray(temp.conditions)) {
                temp.conditions = temp.conditions.map(x => ({
                    ...x,
                    discount_value: this.amountFromMinorUnits(x.discount_value)
                }))
            }
            return temp;
        },
        async getData() {
            this.loading = true;

            try {
                let response = await this.model.find(this.id)

                this.settings = this.setDataPrices(response);

                // in case if it needs to be passed by composable
                this.discount = this.settings;
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.loading = false
                setTimeout(() => {
                    this.isSettingsSet = true;
                }, 350)
            }
        },
        async submitDiscount() {
            this.submitLoader = true;
            this.responseErrors = {};

            try {
                // if (!this.settings.date_start) {
                //     this.responseErrors.date_start = this.translations['Field is required'];
                // }
                let response;

                if (!this.id) {
                    if (this.type === 'global') {
                        response = await this.model.create(this.payload[this.type]);
                    } else {
                        response = await this.model.post(this.type, this.payload[this.type]);
                    }
                } else {
                    response = await this.model.patch(this.payload[this.type], this.type === 'global' ? this.id : `${this.id}/${this.type}`);
                }

                this.settings = this.setDataPrices(response);

                this.$router.push({
                    name: 'discounts-edit',
                    params: {
                        id: this.settings.id
                    },
                })
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.submitLoader = false;
            }
        }
    },
    async mounted() {
        if (this.isEdit) {
            await this.getData()
        } else {
            setTimeout(() => {
                this.isSettingsSet = true;
            }, 350)
        }
        this.setBoxes();
    }
}
</script>
