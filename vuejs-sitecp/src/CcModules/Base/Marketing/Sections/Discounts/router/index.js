let routes = [
    {
        path: "/admin/marketing-new/discounts",
        name: "discounts-index",
        component: () => import("./../Index.vue"),
        redirect: { name: 'discounts-list' },
        children: [
            {
                path: "",
                name: "discounts-list",
                component: () => import("./../components/DistountsList/DiscountsList.vue"),
            },
            {
                path: "create/:type",
                name: "discounts-create",
                component: () => import("./../components/CreateEditDiscount/CreateOrEditDiscount.vue"),
            },
            {
                path: "edit/:id",
                name: "discounts-edit",
                component: () => import("./../components/CreateEditDiscount/CreateOrEditDiscount.vue"),
            },
            {
                path: "products/:id",
                name: "discounts-products",
                component: () => import("./../components/DiscountsProductsList/DiscountsProductsList.vue"),
            },
            {
                path: "code-pro/:id",
                name: "discounts-code_pro-list",
                component: () => import("./../components/CodePro/CodeProList.vue"),
            },
            {
                path: "code-pro/:id/generator",
                name: "discounts-code_pro-generator",
                component: () => import("./../components/CodePro/CodeGenerator.vue"),
            },
            {
                path: "code-pro/:id/create",
                name: "discounts-code_pro-create",
                component: () => import("./../components/CodePro/CodeCreateOrEdit.vue"),
            },
            {
                path: "code-pro/:id/:codeId",
                name: "discounts-code_pro-edit",
                component: () => import("./../components/CodePro/CodeCreateOrEdit.vue"),
            },
        ]
    },
]

export {
    routes
}
