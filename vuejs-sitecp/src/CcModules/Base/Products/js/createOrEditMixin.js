import moment from "moment";

export default {
    methods: {
        handlePrepareDigitalProduct(data, isPages) {
            let temp = _.clone(data);

            temp.variant = temp.variants[0];
            !temp?.variant?.weight ? (temp.variant.weight = 0) : null;
            delete temp.variants;

            if (!isPages) {
                temp.file = temp.digital_file;

                if (temp?.file?.id || temp?.digital_file?.id) {
                    delete temp.file;
                    delete temp.digital_file;
                }
            }
            if (isPages && !temp?.days) {
                temp.days = 0
            }

            if (!parseFloat(temp.variant.price)) {
                temp.variant.price = '0.00';
            } else {
                temp.variant.price = parseFloat(temp.variant.price).toFixed(2);
            }


            if (!temp.variant.save || Number(temp.variant.save) == 0 || temp.variant.price === '0.00') {
                temp.variant.discount_price = null
                temp.variant.discount = null
            } else {
                temp.variant.discount_price = (Number(temp.variant.discount)).toFixed(2);
                // temp.variant.discount_price = (Number(temp.variant.price) - Number(temp.variant.save)).toFixed(2);
                delete temp.variant.discount;
            }

            delete temp?.variant?.save
            delete temp?.variant?.unit_id
            delete temp?.price_from
            delete temp?.price_to
            delete temp?.unit_id
            delete temp?.variant?.quantity
            delete temp?.variant?.weight


            temp.shipping = 'no';
            return temp;
        },
        handlePayloadClear(data) {
            let temp = _.clone(data);
            // Clear payload
            [
                "tags_options",
                "vendor",
                "unit",
                "supppliers",
                "category",
                "properties",
                "file_names",
                "linked_products",
                "pages_init",
                'date_added',
                'date_modified',
                'other_categories_options'
            ].forEach((key) => {
                delete temp[key];
            });

            // Clear empty arrays
            ["brand_model", "images", "tabs"].forEach((key) => {
                if (!temp[key] || (Array.isArray(temp[key]) && !temp[key].length)) {
                    delete temp[key];
                }
            });
            if (["no", 0].includes(temp.tracking)) {
                delete temp.continue_selling;
            }
            if (temp.type !== "digital") {
                delete temp.type_digital;
            }
            if (['yes', 1, true].includes(temp?.continue_selling) ||
                ['no', 0, false].includes(temp?.tracking)) {
                temp.out_of_stock_id = null;
            }

            if(!temp.vendor_id){
                delete temp.vendor_id;
            }

            if(Array.isArray(temp.other_categories)){
                temp.other_categories = temp.other_categories.filter(Boolean);
            }

            return temp;
        },
        handleFormatMultipleProcuctVariants(data) {
            let temp = data;
            let cleared = ['topUnitPrice', 'packagePrice', 'priceBaseUnit'];

            if (!temp.p1 || !temp.p1_id) {
                delete temp.p1;
                delete temp.p1_id;
                cleared.push("v1", "v1_id", "v1r");
            } else if (temp?.p1_id === 'custom') {
                temp.p1_id = null;
            }

            if (!temp.p2 || !temp.p2_id) {
                delete temp.p2;
                delete temp.p2_id;
                cleared.push("v2", "v2_id", "v2r");
            } else if (temp?.p2_id === 'custom') {
                temp.p2_id = null;
            }

            if (!temp.p3 || !temp.p3_id) {
                delete temp.p3;
                delete temp.p3_id;
                cleared.push("v3", "v3_id", "v3r");
            } else if (temp?.p3_id === 'custom') {
                temp.p3_id = null;
            }

            if (cleared.length) {
                temp.variants.forEach((variant) => {
                    Object.keys(variant).forEach((key) => {
                        if (cleared.includes(key)) {
                            delete variant[key];
                        }
                    });
                });
            }
            temp.variants = temp.variants.map((variant) => {
                if(typeof variant.id === 'string' && variant.id.includes('new-')){
                    delete variant.id;
                }
                delete variant?.recordIndex

                if (!parseFloat(variant.weight)) {
                    if (['no', 0, false].includes(temp.shipping)) {
                        variant.weight = null;
                    } else {
                        variant.weight = 0;
                    }
                } else {
                    variant.weight = parseFloat(variant.weight);
                }

                if (!parseFloat(variant.quantity)) {
                    variant.quantity = 0;
                } else {
                    variant.quantity = parseFloat(variant.quantity);
                }
                if (['no', 0].includes(temp.tracking)) {
                    delete variant.quantity;
                }
                if (['no', 0].includes(temp.shipping)) {
                    delete variant.weight;
                }
                if (!parseFloat(variant.price)) {
                    variant.price = '0.00';
                }

                if (!variant.v1 || !variant.v1_id) {
                    delete variant.v1;
                    delete variant.v1_id;
                }
                if (!variant.v2 || !variant.v2_id) {
                    delete variant.v2;
                    delete variant.v2_id;
                }
                if (!variant.v3 || !variant.v3_id) {
                    delete variant.v3;
                    delete variant.v3_id;
                }

                if (!variant.save || Number(variant.save) == 0 || variant.price === '0.00') {
                    delete variant.discount;
                    delete variant.save;
                    delete variant.discount_price;
                } else {
                    variant.discount_price = (Number(variant.discount)).toFixed(2);
                    delete variant.discount;
                }

                if (!variant.unit_text) {
                    variant.unit_text = ''
                }

                delete variant?.save;

                variant.image_ids = (variant?.images || []).join(',');
                delete variant.images

                delete variant?.v1r?.isCustom;
                delete variant?.v2r?.isCustom;
                delete variant?.v3r?.isCustom;

                return variant;
            });

            temp.unit_id = null;

            return temp;
        },
        handleFormatSimpleProductVariant(data) {
            let temp = data;

            temp.variant = temp.variants[0];
            delete temp.variants;

            if (!parseFloat(temp.variant.weight)) {
                temp.variant.weight = 0;
            } else {
                temp.variant.weight = parseFloat(temp.variant.weight);
            }

            if (!parseFloat(temp.variant.quantity)) {
                temp.variant.quantity = 0;
            } else {
                temp.variant.quantity = parseFloat(temp.variant.quantity);
            }

            if (!parseFloat(temp.variant.price)) {
                temp.variant.price = '0.00';
            } else {
                temp.variant.price = parseFloat(temp.variant.price).toFixed(2);
            }


            if (!temp.variant.save || Number(temp.variant.save) == 0 || temp.variant.price === '0.00') {
                temp.variant.discount_price = null
                temp.variant.discount = null
            } else {
                temp.variant.discount_price = (Number(temp.variant.discount)).toFixed(2);
                delete temp.variant.discount;
            }


            if (!temp.variant.unit_text) {
                temp.variant.unit_text = ''
            }

            if (!temp.variant.unit_id) {
                temp.unit_id = null
            }
            if(!temp.variant.minimum){
                temp.variant.minimum = 0;
            }
            delete temp?.variant?.save
            delete temp?.price_from
            delete temp?.price_to

            if (['no', 0].includes(temp.tracking)) {
                delete temp.variant.quantity;
            }
            if (['no', 0].includes(temp.shipping)) {
                delete temp.variant.weight;
            }

            return temp;
        },
        handleFinalCleanup(data) {
            let temp = _.clone(data);

            Object.keys(temp).forEach(key => {
                if(!temp[key] && (typeof temp[key] === 'object' || typeof temp[key] === 'string') ) {
                    delete temp[key];
                }
            })
            return temp;
        },
        handleSingleVariantCleanup(data){
            let temp = _.clone(data);
            Object.keys(temp.variant).forEach(key => {
                if(!temp.variant[key] && typeof temp.variant[key] === 'object' && ['quantity'].includes(key)) {
                    temp.variant[key] = 0;
                }
            })
            return temp;
        },
        handleMultipleVariantsCleanup(data) {

        },
        checkAndManipulateSwitchData(data) {
            let temp = _.clone(data);
            let yesNoEnum = {
                0: "no",
                1: "yes",
            };

            [
                "active",
                "hidden",
                // "featured",
                "new",
                "tracking",
                "shipping",
                "continue_selling",
                "draft"
            ].forEach((key) => {
                let value = temp[key];
                if (yesNoEnum.hasOwnProperty(value)) {
                    temp[key] = yesNoEnum[value];
                }
            });
            return temp;
        },
        async checkAndManipulateResponse(item) {
            let temp = _.clone(item);
            temp.tags_options = Array.isArray(temp.tags)
                ? temp.tags.map((x) => (x.tag))
                : [];
            temp.tags = (temp.tags || []).map((tag) => tag.tag);

            if (
                temp.vendor &&
                typeof temp.vendor === "object" &&
                !Array.isArray(temp.vendor) &&
                Object.keys(temp.vendor).length > 0
            ) {
                temp.vendor = [temp.vendor];
            } else if (!Array.isArray(temp.vendor)) {
                temp.vendor = [];
            }
            if (
                temp.category &&
                typeof temp.category === "object" &&
                !Array.isArray(temp.category) &&
                Object.keys(temp.category).length > 0
            ) {
                temp.category = [temp.category];
            } else if (!Array.isArray(temp.category)) {
                temp.category = [];
            }

            if (Array.isArray(temp.brand_model) && temp.brand_model.length > 0) {
                temp.brand_model = temp.brand_model.map((x) => x.id);
            }

            ["vendor_id", "category_id"].forEach((key) => {
                if (!temp[key]) {
                    temp[key] = "";
                }
            });

            if (temp.properties && Object.keys(temp.properties).length > 0) {
                let tempArr = [];

                Object.keys(temp.properties).forEach((property) => {
                    let row = {
                        id: +property,
                        name: temp.properties[property].name,
                        values: [],
                        values_init: [],
                    };
                    let values = temp.properties[property].values;
                    if (values && values?.length > 0) {
                        row.values_init = values;
                        row.values = values.map((x) => x.id);
                    }
                    tempArr.push(row);
                });
                temp.properties = tempArr;
            } else {
                temp.properties = [];
            }

            if (temp.files && temp.files.length > 0) {
                temp.file_names = temp.files.map((file) => file.name);
            } else {
                temp.file_names = [];
            }

            if (temp.type === "digital") {
                // temp.type_digital = temp?.digital_file ? "file" : "page";
                if (
                    temp?.digital_pages &&
                    Object.keys(temp?.digital_pages).length &&
                    temp?.digital_pages?.pages.length > 0
                ) {
                    temp.page_id = temp?.digital_pages?.pages.map((x) => x.id);
                    temp.days = temp?.digital_pages?.days;
                    temp.days = temp?.digital_pages?.days;
                    temp.pages_init = temp?.digital_pages?.pages;
                    delete temp.digital_pages;
                }
            }



            if (temp?.variants?.length === 0) {
                temp.variants = [
                    {
                        price: 0,
                        sku: "",
                        barcode: "",
                        minimum: 1,
                        quantity: 0,
                        weight: 0,
                    }
                ]
            } else {
                temp.variants.forEach((variant) => {

                    if (!variant.unit_value) {
                        variant.unit_value = 1;
                    } else {
                        variant.unit_value = Number(variant.unit_value);
                    }

                    if (!variant.price) {
                        variant.price = 0;
                    }
                    if (!variant.discount) {
                        variant.discount = 0;
                    } else {
                        variant.discount = Number(variant.discount);
                    }
                    if (!variant.save) {
                        variant.save = 0
                    } else {
                        variant.save = Number(variant.save)
                    }

                    variant.base_unit_value = Number(variant.base_unit_value);

                    variant.discount = Number(variant.discount);
                    variant.save = Number(variant.save);
                })
            }

            if(temp?.variants?.length > 1 && temp.type === 'simple') {
                temp.type = 'multiple';
            }

            if (temp.active_to) {
                temp.active_to = moment(temp.active_to).format(
                    this.serverSettings('format.dateTime').split('\\').join('')
                )
            }
            if (temp.publish_date) {
                temp.publish_date = moment(temp.publish_date).format(
                    this.serverSettings('format.dateTime').split('\\').join('')
                )
            }
            if (['yes', true, 1, '1'].includes(temp.draft)) {
                temp.active = 0
            }

            if (temp.other_categories && Array.isArray(temp.other_categories) && temp.other_categories.length) {
                temp.other_categories_options = _.cloneDeep(temp.other_categories);
                temp.other_categories = temp.other_categories.map((x) => x.id);
            }

            return temp;
        },
    },

    unmounted() {
        this.responseErrors = {};
    },
}
