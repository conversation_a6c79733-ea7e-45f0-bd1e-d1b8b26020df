<template>
    <Loading v-if="loading" :loading="loading" class="app-loader-center" />
    <div class="pb-4" v-else>
        <SettingsForm>
            <SidebarLayout sidebar-placement="right">
                <template #main>
                    <template v-for="(c, i) in mainComponents" :key="i">
                        <component
                            v-if="c"
                            :is="c"
                            v-model="item"
                            :model="model"
                            :meta="meta"
                            :response-errors="responseErrors"
                            v-bind="resolveProps(c)"
                        />
                    </template>
                </template>
                <template #sidebar>
                    <template v-for="(c, i) in sidebarComponents" :key="i">
                        <component
                            :is="c"
                            v-model="item"
                            :model="model"
                            :meta="meta"
                            :response-errors="responseErrors"
                            v-bind="resolveProps(c)"
                        />
                    </template>
                </template>
            </SidebarLayout>
        </SettingsForm>
        <SubmitChanges
            v-if="!dataLoading && isDataReady"
            :model-value="item"
            @update:model-value="handleDiscardChanges"
            v-model:response-errors="responseErrors"
            :submit-loader="submitLoader"
            :save-func="saveProduct"
            :ignored="[
                'images',
                'tags_options',
                'category',
                'vendor',
                'suppliers',
                'on_sale',
                'properties',
                'pages_init',
                'files',
                'file_names',
                'category',
                'other_categories_options',
            ]"
            :submitOptions="submitOptions"
            :save-btn-text="'Save and publish'"
        />
        <!-- 'file_names', -->

        <PublishModal
            v-model="publishModal"
            v-model:product="item"
            :save-action="saveProduct"
            :response-errors="responseErrors"
        />
        <PlanFeature
            v-model="planFeatureModal"
            v-model:record="currentPlanFeature"
            type="plan_feature"
            @success="
                (result) => {
                    handleAfterPay(result);
                }
            "
        >
        </PlanFeature>
         <CloudioSidePanel
            v-model="cloudioModal"
            :panel-type="type"
            v-model:description="item.description"
            v-model:short-description="item.short_description"
            v-model:seo-description="item.seo_description"
            :url-handle="item.url_handle"
        /> 
    </div> 
</template>

<script>
import _ from "lodash";
import Products from "../../js/Products";
import { buildFormData } from "@js/shippingHelpers";
import { ref, watch } from "vue";
import useSharedState from "../../composables/useSharedState";
import useSharedCloudioState from "../../composables/useSharedCloudioState";
import useSharedAppsInfo from "../../../../Apps/composables/useSharedAppsInfo";
import createOrEditMixin from "../../js/createOrEditMixin";
import moment from "moment";
import slugify from '@js/slugify';

import { PlanFeature } from "@components/Checkout";
import Loading from "@components/Loading";
import SidebarLayout from "@components/SettingsForm/Layouts/SidebarLayout";
import SettingsForm from "@components/SettingsForm/SettingsForm";
import SubmitChanges from "@components/SubmitChanges";
import PublishModal from "../Helpers/PublishModal";
import CloudioSidePanel from "./../../CloudioProduct/components/CloudioSidePanel";

import SectionDetails from "../Form/SectionDetails";
import SectionTitles from "../Form/SectionTitles";
// import SectionPricing from "../Form/SectionPricing";
import SectionVariants from "../Form/SectionVariants";
import SectionCategories from "../Form/SectionCategories";
import SectionSort from "../Form/SectionSort";
import SectionStatuses from "../Form/SectionStatuses";
import SectionMissingApps from "../Form/SectionMissingApps";
import SectionImos3d from "../Form/SectionImos3d";
// import SectionUnits from "../Form/SectionUnits";
// import SectionInventory from "../Form/SectionInventory";
import SectionSEOConfig from "../Form/SectionSEOConfig";
// import SectionDimentions from "../Form/SectionDimentions";
import SectionMedia from "../Form/SectionMedia";
import SectionTags from "../Form/SectionTags";
import SectionVendors from "../Form/SectionVendors";
import SectionLinkedProducts from "../Form/SectionLinkedProducts";
import SectionBrandAndModel from "../Form/SectionBrandAndModel";
import SectionSuppliers from "../Form/SectionSuppliers";
import SectionSmartCollections from "../Form/SectionSmartCollections";
import SectionDiscounts from "../Form/SectionDiscounts";
import SectionDigitalProduct from "../Form/SectionDigitalProduct";
import SectionDates from "../Form/SectionDates";

export default {
    name: "ProductEdit",
    mixins: [createOrEditMixin],
    components: {
        Loading,
        PlanFeature,
        SubmitChanges,
        PublishModal,
        CloudioSidePanel,
        SidebarLayout,
        SettingsForm,
        SectionDetails,
        SectionTitles,
        SectionVariants,
        // SectionPricing,
        SectionCategories,
        SectionSort,
        SectionStatuses,
        SectionMissingApps,
        // SectionUnits,
        // SectionInventory,
        SectionSEOConfig,
        // SectionDimentions,
        SectionMedia,
        SectionVendors,
        SectionTags,
        SectionLinkedProducts,
        SectionBrandAndModel,
        SectionSuppliers,
        SectionSmartCollections,
        SectionImos3d,
        SectionDiscounts,
        SectionDigitalProduct,
        SectionDates,
    },
    setup() {
        const {
            product: item,
            productType,
            createOrEditModal,
            meta,
            metaLoading,
            getProduct,
            responseErrors,
            translations: translationLabels,
            variantsCollection,
            isPublished,
            cookieBody,
            hidden_products,
            digital_products,
            multi_variants,
            variants_listing,
            initialVariantsData
        } = useSharedState();

        const { apps } = useSharedAppsInfo();

        const { cloudioModal, type, getCloudioStatus } = useSharedCloudioState();

        let isSetFeatures = false;
        const currentPlanFeature = ref({});

        // const hidden_products = ref({});
        // const digital_products = ref({});
        // const multi_variants = ref({});

        watch(
            meta,
            (value) => {
                if (
                    Array.isArray(value?.features) &&
                    value?.features?.length > 0 &&
                    !isSetFeatures
                ) {
                    value.features.forEach((feature) => {
                        if (feature.mapping === "hidden_products") {
                            hidden_products.value = feature;
                        } else if (feature.mapping === "digital_products") {
                            digital_products.value = feature;
                        } else if (feature.mapping === "multi_variants") {
                            multi_variants.value = feature;
                        } else if (feature.mapping === "variants.listing") {
                            variants_listing.value = feature;
                        }
                    });
                    isSetFeatures = true;
                }
            },
            { deep: true }
        );

        return {
            item,
            productType,
            createOrEditModal,
            meta,
            metaLoading,
            getProduct,
            responseErrors,
            translationLabels,
            currentPlanFeature,
            hidden_products,
            digital_products,
            multi_variants,
            cloudioModal,
            getCloudioStatus,
            type,
            variantsCollection,
            isPublished,
            cookieBody,
            variants_listing,
            apps,
            initialVariantsData
        };
    },
    data() {
        return {
            id: this.$route.params.id,
            planFeatureModal: false,
            model: new Products(),
            publishModal: false,
            dataLoading: false,
            submitLoader: false,
            isDataReady: false,
            translations: {
                "Show also in": this.$t("Show also in"),
                "You can select up to 5 additional categories where this product will be shown": this.$t("You can select up to 5 additional categories where this product will be shown"),
                "Add additional category": this.$t("Add additional category"),
                "Additional category {index}": this.$t("Additional category {index}"),
                "<strong>Note:</strong> Products added to \"Show Also In\" categories will appear in those categories on the storefront. However, they are not considered part of these categories for backend operations such as discounts, product widgets, or automated selections. Only the primary \"Category\" is used for such functionalities.":
                this.$t("<strong>Note:</strong> Products added to \"Show Also In\" categories will appear in those categories on the storefront. However, they are not considered part of these categories for backend operations such as discounts, product widgets, or automated selections. Only the primary \"Category\" is used for such functionalities."),
                "This section is enabled by installed applications that contribute to expanding the functionalities and improving the product.":
                    this.$t(
                        "This section is enabled by installed applications that contribute to expanding the functionalities and improving the product."
                    ),
                "Brand and model": this.$t("Brand and model"),
                Extended: this.$t("Extended"),
                "Select one or multiple models that are related to the product":
                    this.$t(
                        "Select one or multiple models that are related to the product"
                    ),
                "Add new brand": this.$t("Add new brand"),
                "Add new model": this.$t("Add new model"),
                Categories: this.$t("Categories"),
                "Create new category": this.$t("Create new category"),
                "Select a category for the product or create a new one":
                    this.$t(
                        "Select a category for the product or create a new one"
                    ),
                "Manage category properties": this.$t(
                    "Manage category properties"
                ),
                "You can assign different category properties to this product.":
                    this.$t(
                        "You can assign different category properties to this product."
                    ),
                "Create new property": this.$t("Create new property"),
                "Category properties": this.$t("Category properties"),
                "Pack Size": this.$t("Pack Size"),
                "Quantity Step": this.$t("Quantity Step"),
                "Enter the packaging size for quantity selection. This defines how the product is packaged and the increments in which it can be purchased (e.g., if set to 500g, buyers can select quantities like 500g, 1000g, etc.).":
                    this.$t(
                        "Enter the packaging size for quantity selection. This defines how the product is packaged and the increments in which it can be purchased (e.g., if set to 500g, buyers can select quantities like 500g, 1000g, etc.)."
                    ),
                "Enter the step increment for quantity selection. This defines how the product is packaged and the increments in which it can be purchased (e.g., if set to 500g, buyers can select quantities like 500g, 1000g, etc.).":
                    this.$t(
                        "Enter the step increment for quantity selection. This defines how the product is packaged and the increments in which it can be purchased (e.g., if set to 500g, buyers can select quantities like 500g, 1000g, etc.)."
                    ),
                Details: this.$t("Details"),
                "Product title": this.$t("Product title"),
                "Total quantity ({unit})": this.$t("Total quantity ({unit})"),
                "Price for ({unit})": this.$t("Price for ({unit})"),
                "Discount per ({unit})": this.$t("Discount per ({unit})"),
                "Price per {amount} {unit}": this.$t(
                    "Price per {amount} {unit}"
                ),
                Description: this.$t("Description"),
                "Add short description": this.$t("Add short description"),
                "Remove short description": this.$t("Remove short description"),
                "Short description": this.$t("Short description"),
                "Product setup": this.$t("Product setup"),
                "Select pages": this.$t("Select pages"),
                "Access days": this.$t("Access days"),
                "Select a file for your digital download. If you want to add multiple files, create an archive file first.":
                    this.$t(
                        "Select a file for your digital download. If you want to add multiple files, create an archive file first."
                    ),
                "This is the price for the defined pack size. The price is calculated based on the pack size and the price entered.":
                    this.$t(
                        "This is the price for the defined pack size. The price is calculated based on the pack size and the price entered."
                    ),
                "Your customers will be able to download this file when they purchase the product.":
                    this.$t(
                        "Your customers will be able to download this file when they purchase the product."
                    ),
                "Accepted file extensions for the digital download are - {types}":
                    this.$t(
                        "Accepted file extensions for the digital download are - {types}"
                    ),
                "The user will have temporary access to the pages you selected after finalizing the order":
                    this.$t(
                        "The user will have temporary access to the pages you selected after finalizing the order"
                    ),
                Dimensions: this.$t("Dimensions"),
                "Define product dimensions like width, height, length and weight":
                    this.$t(
                        "Define product dimensions like width, height, length and weight"
                    ),
                "Price per Pack Size": this.$t("Price per Pack Size"),
                "Price per pack": this.$t("Price per pack"),
                Height: this.$t("Height"),
                Weight: this.$t("Weight"),
                Length: this.$t("Length"),
                Width: this.$t("Width"),
                unit: this.$t("unit"),
                Save: this.$t("Save"),
                "Price per unit": this.$t("Price per unit"),
                "Price per 1 {unit}": this.$t("Price per 1 {unit}"),
                Cancel: this.$t("Cancel"),
                "Add unit price": this.$t("Add unit price"),
                'Edit "{category}" category': this.$t(
                    'Edit "{category}" category'
                ),
                "This is the initial quantity of the product that the customer must order.":
                    this.$t(
                        "This is the initial quantity of the product that the customer must order."
                    ),
                "Edit your product category": this.$t(
                    "Edit your product category"
                ),
                "Measurement unit": this.$t("Measurement unit"),
                "Base measurement unit": this.$t("Base measurement unit"),
                "Discount per package": this.$t("Discount per package"),
                "Sale price per package": this.$t("Sale price per package"),
                "Price for {size}": this.$t("Price for {size}"),
                "Show price per {unit}": this.$t("Show price per {unit}"),
                "Price for pack size": this.$t("Price for pack size"),
                "Manage your category properties": this.$t(
                    "Manage your category properties"
                ),
                "You need to save the products new category, before you can manage the properties.":
                    this.$t(
                        "You need to save the products new category, before you can manage the properties."
                    ),
                "Want to add a new manufacturer? Type the manufacturer name to create it.":
                    this.$t(
                        "Want to add a new manufacturer? Type the manufacturer name to create it."
                    ),
                "No manufacturers yet? Please type your manufacturer name to create it.":
                    this.$t(
                        "No manufacturers yet? Please type your manufacturer name to create it."
                    ),
                "This product has applied discounts": this.$t(
                    "This product has applied discounts"
                ),
                "You can make advanced settings for the product like tracking, shipping, etc...":
                    this.$t(
                        "You can make advanced settings for the product like tracking, shipping, etc..."
                    ),
                "If checked, the system will notify the administrator when this product's quantity has dropped below the specified number.":
                    this.$t(
                        "If checked, the system will notify the administrator when this product's quantity has dropped below the specified number."
                    ),
                "Hide settings": this.$t("Hide settings"),
                "Show settings": this.$t("Show settings"),
                "Enable Track Inventory or disable Continue Selling option":
                    this.$t(
                        "Enable Track Inventory or disable Continue Selling option"
                    ),
                "Product weight": this.$t("Product weight"),
                "Product quantity": this.$t("Product quantity"),
                "Applied discounts": this.$t("Applied discounts"),
                "If checked, the quantity of this product will be tracked by the system. When the product is sold, the necessary quantity will be subtracted from the total product quantity.":
                    this.$t(
                        "If checked, the quantity of this product will be tracked by the system. When the product is sold, the necessary quantity will be subtracted from the total product quantity."
                    ),
                "No expiration": this.$t("No expiration"),
                Hidden: this.$t("Hidden"),
                Percent: this.$t("Percent"),
                Amount: this.$t("Amount"),
                "Visible to": this.$t("Visible to"),
                "All customers": this.$t("All customers"),
                Inventory: this.$t("Inventory"),
                "Track inventory": this.$t("Track inventory"),
                "Total quantity": this.$t("Total quantity"),
                "Continue selling when out ot stock": this.$t(
                    "Continue selling when out ot stock"
                ),
                "Notify me when quantity is below": this.$t(
                    "Notify me when quantity is below"
                ),
                "Product has SKU/Barcode": this.$t("Product has SKU/Barcode"),
                SKU: this.$t("SKU"),
                Barcode: this.$t("Barcode"),
                "Add location": this.$t("Add location"),
                "Requires shipping": this.$t("Requires shipping"),
                "If enabled, you need to enter the weight of the variant.":
                    this.$t(
                        "If enabled, you need to enter the weight of the variant."
                    ),
                "If enabled, you need to enter the quantity of the variant.":
                    this.$t(
                        "If enabled, you need to enter the quantity of the variant."
                    ),
                "Linked products": this.$t("Linked products"),
                "Select which products to be linked with this product": this.$t(
                    "Select which products to be linked with this product"
                ),
                "Advanced inventory settings": this.$t(
                    "Advanced inventory settings"
                ),
                "You can assign different category properties to this product.":
                    this.$t(
                        "You can assign different category properties to this product."
                    ),
                Pricing: this.$t("Pricing"),
                Price: this.$t("Price"),
                "On Sale": this.$t("On Sale"),
                "Custom statuses": this.$t("Custom statuses"),
                Discount: this.$t("Discount"),
                "Sale price": this.$t("Sale price"),
                "Minimum order qty": this.$t("Minimum order qty"),
                Step: this.$t("Step"),
                Visualization: this.$t("Visualization"),
                "Step in {unit}": this.$t("Step in {unit}"),
                "If you enter a value, this value will be visible in a label in the product sheet and on the detail page":
                    this.$t(
                        "If you enter a value, this value will be visible in a label in the product sheet and on the detail page"
                    ),
                "SEO Configuration": this.$t("SEO Configuration"),
                "SEO page title": this.$t("SEO page title"),
                URL: this.$t("URL"),
                "SEO meta description": this.$t("SEO meta description"),
                "Google view": this.$t("Google view"),
                "Sort number": this.$t("Sort number"),
                "The smaller the number, the upper position will move the product. If there are two or more products with the same number to sort, then they will be sorted by date of addition as the last product added will be on upper position. If empty, product is display latest.":
                    this.$t(
                        "The smaller the number, the upper position will move the product. If there are two or more products with the same number to sort, then they will be sorted by date of addition as the last product added will be on upper position. If empty, product is display latest."
                    ),
                "Product statuses": this.$t("Product statuses"),
                "Product status in stock": this.$t("Product status in stock"),
                "Product status out of stock": this.$t(
                    "Product status out of stock"
                ),
                "Create new status": this.$t("Create new status"),
                Tags: this.$t("Tags"),
                "Tags will help the relation of your product with other similar products.":
                    this.$t(
                        "Tags will help the relation of your product with other similar products."
                    ),
                Units: this.$t("Units"),
                "Product unit measurement": this.$t("Product unit measurement"),
                Vendors: this.$t("Vendors"),
                "Select a vendor for the product": this.$t(
                    "Select a vendor for the product"
                ),
                "Add new vendor": this.$t("Add new vendor"),
                "Define product dimensions like width, height, length": this.$t(
                    "Define product dimensions like width, height, length"
                ),
                "To use this settings you need to enable tracking or disable continue selling":
                    this.$t(
                        "To use this settings you need to enable tracking or disable continue selling"
                    ),
                "Price per {unit}": this.$t("Price per {unit}"),
                "Define the price per {unit}": this.$t(
                    "Define the price per {unit}"
                ),
                "Price per {amount} {unit} is:": this.$t(
                    "Price per {amount} {unit} is:"
                ),
                pack: this.$t("pack"),
                // ERROR MESSAGES
                "Product name is required": this.$t("Product name is required"),
                "The maximum allowed characters for 'name' are {max}": this.$t(
                    "The maximum allowed characters for name are {max}"
                ),
                "The maximum allowed characters for 'description' are {max}":
                    this.$t(
                        "The maximum allowed characters for description are {max}"
                    ),
                "The publish date format is invalid. Format should be: {{siteDateTimeFormat}}":
                    this.$t(
                        "The publish date format is invalid. Format should be: {format}"
                    ),
                "The active to date must be a date after the publish date":
                    this.$t(
                        "The active to date must be a date after the publish date"
                    ),
                "The selected vendor is invalid. Please select a valid vendor":
                    this.$t(
                        "The selected vendor is invalid. Please select a valid vendor"
                    ),
                "You have not selected a main category for the product. Please select a main category for the product":
                    this.$t(
                        "You have not selected a main category for the product. Please select a main category for the product"
                    ),
                "Product type is required": this.$t("Product type is required"),
                "Product type must be a string": this.$t(
                    "Product type must be a string"
                ),
                "Invalid product type. Please select a valid product type. Valid product types are: {{Product.TYPES}}":
                    this.$t(
                        "Invalid product type. Please select a valid product type. Valid product types are: {types}"
                    ),
                "Invalid Require shipping value. Please select a valid Require shipping value. Valid shipping values are: yes, no":
                    this.$t(
                        "Invalid Require shipping value. Please select a valid Require shipping value. Valid shipping values are: yes, no"
                    ),
                "Invalid Track inventory value. Please select a valid Track inventory value. Valid tracking values are: yes, no":
                    this.$t(
                        "Invalid Track inventory value. Please select a valid Track inventory value. Valid tracking values are: yes, no"
                    ),
                "The threshold must be a number": this.$t(
                    "The threshold must be a number"
                ),
                "The variant field is required for simple products": this.$t(
                    "The variant field is required for simple products"
                ),
                "The variants field is required for multiple products": this.$t(
                    "The variants field is required for multiple products"
                ),
                "The variants field must be an array": this.$t(
                    "The variants field must be an array"
                ),
                "The type digital field is required for digital products":
                    this.$t(
                        "The type digital field is required for digital products"
                    ),
                "Invalid type digital value. Valid type digital values are: page, file":
                    this.$t(
                        "Invalid type digital value. Valid type digital values are: page, file"
                    ),
                "The variants field is required for type is not digital":
                    this.$t(
                        "The variants field is required for type is not digital"
                    ),
                "Invalid parameter value. Please select a valid parameter value":
                    this.$t(
                        "Invalid parameter value. Please select a valid parameter value"
                    ),
                "The maximum allowed characters for 'sku' are {max}": this.$t(
                    "The maximum allowed characters for sku are {max}"
                ),
                "The weight must be a number": this.$t(
                    "The weight must be a number"
                ),
                "The quantity field is required for tracked products": this.$t(
                    "The quantity field is required for tracked products"
                ),
                "The maximum allowed quantity for a product is {max}": this.$t(
                    "The maximum allowed quantity for a product is {max}"
                ),
                "The discount price must be a valid currency amount": this.$t(
                    "The discount price must be a valid currency amount"
                ),
                "The price must be a valid currency amount": this.$t(
                    "The price must be a valid currency amount"
                ),
                "Invalid price type. Please select a valid price type. Valid price types are: price, percent":
                    this.$t(
                        "Invalid price type. Please select a valid price type. Valid price types are: price, percent"
                    ),
                "The percent must be a valid percent": this.$t(
                    "The percent must be a valid percent"
                ),
                "The tab name field is required": this.$t(
                    "The tab name field is required"
                ),
                "The width must be a number": this.$t(
                    "The width must be a number"
                ),
                "The depth must be a number": this.$t(
                    "The depth must be a number"
                ),
                "The selected linked product is invalid. Please select a valid linked product":
                    this.$t(
                        "The selected linked product is invalid. Please select a valid linked product"
                    ),
                "The selected out of stock status is invalid. Please select a valid out of stock status":
                    this.$t(
                        "The selected out of stock status is invalid. Please select a valid out of stock status"
                    ),
                "The maximum allowed characters for 'v3' are {max}": this.$t(
                    "The maximum allowed characters for v3 are {max}"
                ),
                "The selected v3 is invalid. Please select a valid v3": this.$t(
                    "The selected v3 is invalid. Please select a valid v3"
                ),
                "To save a digital product, you need to upload a file": this.$t(
                    "To save a digital product, you need to upload a file"
                ),
                "Sale price must be greater than or equal to 0": this.$t(
                    "Sale price must be greater than or equal to 0"
                ),
                "Currency maximum characters are 10.": this.$t(
                    "Currency maximum characters are 10."
                ),
                "The discounted price is lower than 0": this.$t(
                    "The discounted price is lower than 0"
                ),
                "Save as draft": this.$t("Save as draft"),
                "Save and publish later": this.$t("Save and publish later"),
                "Other categories": this.$t("Other categories"),
            },
        };
    },
    computed: {
        sidebarComponents() {
            return [
                this.item?.active_to || this.item?.publish_date
                    ? SectionDates
                    : null,
                SectionTitles,
                SectionVendors,
                SectionTags,
                SectionSmartCollections,
                // SectionStatuses,
                SectionSort,
                SectionDiscounts,
            ];
        },
        mainComponents() {

            let isImosShowed = (this.apps || []).find(x=>x?.key === 'imos3d' && x?.is_installed && x?.is_paid && x?.is_active);

            return [
                SectionDetails,
                SectionMedia,
                ["file", "digital", "page"].includes(this.item?.type) &&
                ["file", "digital", "page"].includes(this.item?.type_digital)
                    ? SectionDigitalProduct
                    : null,
                ["simple", "multiple"].includes(this.item?.type)
                    ? SectionVariants
                    : null,
                SectionCategories,
                this.$allowedSections.apps && this.meta.brand_model ? SectionBrandAndModel : null,
                this.$allowedSections.apps && this.meta.suppliers ? SectionSuppliers : null,
                this.$allowedSections.apps && isImosShowed ? SectionImos3d : null,
                SectionSEOConfig,
                SectionLinkedProducts,
                this.$allowedSections.apps ? SectionMissingApps : null,
            ].filter(Boolean);
        },
        isEnabledHiddenProducts() {
            if ([null, true].includes(this.hidden_products?.current)) {
                return true;
            }

            return this.hidden_products?.current > this.hidden_products?.used;
        },
        loading() {
            return this.dataLoading || this.metaLoading;
        },
        submitOptions() {
            let options = [
                ...(!this.$route.params.id || !this.item.id
                    ? [
                          {
                              label: this.translations["Save as draft"],
                              action: () => this.saveProduct(true),
                          },
                      ]
                    : []),
                ...(this.item.active == 0
                    ? [
                          {
                              label: this.translations[
                                  "Save and publish later"
                              ],
                              action: this.openPublishModal,
                          },
                      ]
                    : []),
            ];
            return options;
        },
    },
    methods: {
        toggleHiddenProducts(value) {
            if (
                ["yes", 1, true].includes(value) &&
                !this.isEnabledHiddenProducts
            ) {
                this.currentPlanFeature = this.hidden_products;
                this.planFeatureModal = true;
                setTimeout(() => {
                    this.item.hidden = 0;
                }, 1000);
            } else {
                this.item.hidden = value;
            }
        },
        resolveProps(component) {
            if (["SectionCategories", "SectionSuppliers"].includes(component?.name)) {
                return {
                    saveProduct: this.saveProduct,
                };
            }
            if (component?.name === "SectionTitles") {
                return {
                    toggleHiddenProducts: this.toggleHiddenProducts,
                };
            }
            return {};
        },
        scrollTo(key) {
            let customScrollTo = document.querySelector(key);

            if (customScrollTo) {
                setTimeout(() => {
                    customScrollTo.scrollIntoView({
                        block: "center",
                        behavior: "smooth",
                    });
                }, 1000);
            }
        },
        handleDiscardChanges(value) {
            this.item = {
                ...value,
                images: this.item.images,
                tags_options: this.item.tags_options,
                category: this.item.category,
                vendor: this.item.vendor,
                suppliers: this.item.suppliers,
                files: this.item.files,
                file_names: this.item.file_names,
                on_sale: this.item.on_sale,
                // other_categories: this.item.other_categories,
                properties: this.item.properties,
            };
        },
        handleAfterPay(result) {
            Object.values(result?.status || []).forEach((item) => {
                if (this.currentPlanFeature.mapping === "hidden_products") {
                    this.hidden_products = {
                        ...this.hidden_products,
                        current:
                            Number(this.hidden_products.current) +
                            Number(item.item.value),
                    };
                }
            });
        },
        openPublishModal() {
            this.publishModal = true;
        },
        async saveProduct(draft = false) {
            this.responseErrors = {};
            this.submitLoader = true;

            if(!this.item.category_id) {
                this.responseErrors.category_id = this.translations[
                    "You have not selected a main category for the product. Please select a main category for the product"
                ];

                this.scrollTo(".category-error");

                setTimeout(()=>{
                    this.submitLoader = false;
                }, 1000);
                return;
            }

            let {
                imos3d,
                id,
                p1r,
                p2r,
                p3r,
                temporary,
                category,
                tags_options,
                variantsFull,
                ...rest
            } = _.cloneDeep(this.item);

            if(!rest.url_handle){
                rest.url_handle = slugify(rest.name);
            }

            // format variant/s
            if (this.item.type === "simple") {
                rest = this.handleFormatSimpleProductVariant(rest);
            } else if (this.item.type === "multiple") {
                rest = this.handleFormatMultipleProcuctVariants(rest);
            }

            if (rest?.tags && rest?.tags?.length > 0) {
                rest.tags = rest.tags.join(", ");
            } else {
                rest.tags = "";
            }

            //format smart_collections
            if (
                rest?.smart_collections &&
                rest?.smart_collections?.length > 0
            ) {
                rest.smart_collections = rest.smart_collections.map(
                    (x) => x.id
                );
            }

            // format brand model data
            if (rest.brand_model && rest.brand_model.length > 0) {
                let ids = _.clone(rest.brand_model);

                rest.brand_model = {
                    model_id: ids,
                };
            }
            if(this.meta.imos3d){
                rest = {...rest, imos3d};
            }

            let isCreate = !Boolean(this.item.id || this.$route.params.id);
            let path = !isCreate
                ? this.item.id || this.$route.params.id
                : `save`;

            if (draft) {
                path = `${path}?draft=1`;
            }

            if (["yes", 1, "1", true].includes(rest.active)) {
                rest.draft = 0;
            }

            // format switch data to yes/no
            rest = this.checkAndManipulateSwitchData(rest);

            // format digital product data
            if (
                (this.productType === "digital" ||
                    this.item.type === "digital") &&
                rest.type_digital === "file"
            ) {
                if(!rest.digital_file){
                    this.responseErrors.file = this.translations['The type digital field is required for digital products']
                    this.scrollTo(".err-empty-field");
                    this.submitLoader = false;
                    return
                }
                rest = this.handlePrepareDigitalProduct(rest, false);
            } else if (
                (this.productType === "membership" ||
                    this.item.type === "digital") &&
                rest.type_digital === "page"
            ) {
                rest = this.handlePrepareDigitalProduct(rest, true);
            }

            if (rest?.files?.length) {
                rest.files.forEach((file, index) => {
                    file.name = rest.file_names[index] || file.filename;
                    file.public = file.public == 1 ? "yes" : "no";
                });
            }

            if (rest?.linked_products && rest?.linked_products?.length) {
                rest.linked = rest.linked_products.map((product) => ({
                    linked_id: product.id,
                    two_way: product.two_way,
                }));
            }

            if (rest?.tabs && rest.tabs?.length) {
                rest.tabs.forEach((tab, index) => {
                    if (!tab.name || tab.name === "") {
                        tab.name = "empty";
                    }
                });
            }

            rest = this.handlePayloadClear(rest);

            // if (rest.active_to) {
            //     rest.active_to = moment(
            //         rest.active_to,
            //         "DD.MM.YYYY hh:mm a"
            //     ).format("DD.MM.YYYY HH:mm:ss");
            // }
            // if (rest.publish_date) {
            //     rest.publish_date = moment(
            //         rest.publish_date,
            //         "DD.MM.YYYY hh:mm a"
            //     ).format("DD.MM.YYYY HH:mm:ss");
            // }

            try {
                let response = null;

                if (
                    (this.productType === "digital" ||
                        this.item.type === "digital") &&
                    rest.type_digital === "file"
                ) {
                    const formData = new FormData();
                    buildFormData(formData, rest);
                    formData.append("type_digital", "file");

                    if (isCreate) {
                        formData.append("type", this.productType);
                    }

                    response = await this.model.post(path, formData, {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    });
                } else if (
                    (this.productType === "membership" ||
                        this.item.type === "digital") &&
                    rest.type_digital === "page"
                ) {
                    response = await this.model.post(path, {
                        type: "digital",
                        type_digital: "page",
                        ...rest,
                    });
                } else {
                    response = await this.model.post(path, {
                        type: this.productType,
                        ...rest,
                    });
                }

                this.item.id = response.id;
                this.isPublished = response.active == 1 ? true : false;

                if (isCreate) {
                    this.$router.push({
                        name: "products-edit",
                        params: { id: this.item.id },
                    });
                }
                if (this.$route.query.new) {
                    this.savePredefinedSettings(rest);
                }

                this.item.variants = response.variants;
                this.item.draft = response.draft;
                this.item.category = response.category;
                this.item.other_categories_options = response.other_categories;

                if(!this.item.url_handle){
                    this.item.url_handle = response.url_handle;
                }

                if (this.item?.variants?.length === 0) {
                    this.item.variants = [
                        {
                            price: 0,
                            sku: "",
                            barcode: "",
                            minimum: 1,
                            quantity: 0,
                            weight: 0,
                        },
                    ];
                } else {
                    this.item.variants.forEach((variant) => {
                        if (!variant.unit_value) {
                            variant.unit_value = 1;
                        } else {
                            variant.unit_value = Number(variant.unit_value);
                        }

                        if (!variant.price) {
                            variant.price = 0;
                        }
                        if (!variant.discount) {
                            variant.discount = 0;
                        } else {
                            variant.discount = Number(variant.discount);
                        }
                        if (!variant.save) {
                            variant.save = 0;
                        } else {
                            variant.save = Number(variant.save);
                        }

                        variant.base_unit_value = Number(
                            variant.base_unit_value
                        );

                        variant.discount = Number(variant.discount);
                        variant.save = Number(variant.save);
                    });
                }
                if (response.other_categories && Array.isArray(response.other_categories) && response.other_categories.length) {
                    this.item.other_categories = response.other_categories.map((x) => x.id);
                }
                this.initialVariantsData = {
                    type: this.item.type,
                    variants: Array.isArray(this.item.variants) ? _.cloneDeep(this.item.variants) :[{}]
                }
                return true;
            } catch (error) {
                this.$errorResponse(error);
                if (this.responseErrors?.variants) {
                    this.scrollTo(".variants-error-box");
                }
            } finally {
                this.submitLoader = false;
                setTimeout(() => {
                    this.isDataReady = false;
                }, 2000);
                setTimeout(() => {
                    this.isDataReady = true;
                }, 3000);
                this.$router.push({
                    query: {},
                });
            }
        },
        savePredefinedSettings(data) {
            if (data.out_of_stock_id || data.status_id) {
                this.cookieBody.custom_statuses = true;
            }
            if (data.meta.depth || data.meta.height || data.meta.width) {
                this.cookieBody.dimentions_enabled = true;
            }
            if (data.threshold) {
                this.cookieBody.threshold_enabled = true;
            }
            if (["simple", "digital"].includes(this.productType)) {
                if (data.variant.sku || data.variant.barcode) {
                    this.cookieBody.sku_barcode_enabled = true;
                }
                if (data.variant.minimum) {
                    this.cookieBody.min_quantity_enabled = true;
                }
            } else {
                data.variants.forEach((variant) => {
                    if (variant.sku || variant.barcode) {
                        this.cookieBody.sku_barcode_enabled = true;
                    }
                    if (variant.minimum) {
                        this.cookieBody.min_quantity_enabled = true;
                    }
                });
            }
            document.cookie = `productPredefinedSettingsCreate=${JSON.stringify(
                this.cookieBody
            )}; path=/; expires=Fri, 31 Dec 9999 23:59:59 GMT`;
        },
        async getData() {
            this.dataLoading = true;

            try {
                this.item = await this.model.find(this.id);
                this.item = await this.checkAndManipulateResponse(this.item);
                this.isPublished = this.item.active == 1 ? true : false;
                this.initialVariantsData = {
                    type: this.item.type,
                    variants: Array.isArray(this.item.variants) ? _.cloneDeep(this.item.variants) :[{}]
                }
                if (this.$route.query?.new) {
                    this.item.active = 1;
                }
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.dataLoading = false;
                setTimeout(() => {
                    this.isDataReady = true;
                }, 1000);
            }
        },
        resetData() {
            this.item = {
                name: "",
                url_handle: "",
                date_added: null,
                date_modified: null,
                category_id: null,
                category: [],
                vendor: [],
                active: 0,
                hidden: 0,
                featured: 0,
                draft: 0,
                new: 0,
                sort_order: "",
                quantity: null,
                // type: null,
                price_from: 0,
                price_to: 0,
                price: 0,
                p1r: {
                    id: null,
                    name: null,
                },
                status_id: null,
                out_of_stock_id: null,
                tracking: 0,
                shipping: 0,
                continue_selling: 0,
                threshold: null,
                meta: {
                    width: 0,
                    depth: 0,
                    height: 0,
                },
                import_info: null,
                temporary: {
                    temporary: null,
                    deleted_at: null,
                },
                unit_id: null,
                unit: null,
                imos3d: {
                    catalog: null,
                    article: null,
                    ind: null,
                },
                description_title: "",
                description: "",
                short_description: "",
                tabs: [],
                variants: [
                    {
                        price: 0,
                        sku: "",
                        barcode: "",
                        minimum: 1,
                        quantity: 0,
                        weight: 0,
                    },
                ],
                images: [],
                files: [],
                tags: [],
                tags_options: [],
                brand_model: [],
                digital_file: null,
            };
            this.variantsCollection = {
                p1r: {
                    value: "",
                    values_init: [],
                    options: [],
                    options_init: [],
                },
            };
        },
    },
    beforeMount() {
        this.resetData();
        if (
            this.$route?.params?.type &&
            ["digital", "membership"].includes(this.$route.params.type)
        ) {
            this.item.type_digital =
                this.$route.params.type === "digital" ? "file" : "page";
            if (this.item === "page") {
                this.item.page_id = [];
                this.item.days = null;
            }
        }
    },
    beforeUnmount() {
        this.resetData();
    },
    async created() {
        this.resetData();
        this.translationLabels = this.translations;
        this.createOrEditModal = false;

        if (this.id) {
            await this.getData();

            this.productType = this.item.type;
        } else {
            this.productType = this.$route.params.type;
            this.isDataReady = true;
        }
    },
    async mounted() {
        this.$populateTranslations(this);
        if (Object.keys(this.meta)?.length) {
            this.meta.features.forEach((feature) => {
                if (feature.mapping === "hidden_products") {
                    this.hidden_products = feature;
                } else if (feature.mapping === "digital_products") {
                    this.digital_products = feature;
                } else if (feature.mapping === "multi_variants") {
                    this.multi_variants = feature;
                } else if (feature.mapping === "variants.listing") {
                    this.variants_listing = feature;
                }
            });
            this.isSetFeatures = true;
        }
        await this.getCloudioStatus();
    },
};
</script>
