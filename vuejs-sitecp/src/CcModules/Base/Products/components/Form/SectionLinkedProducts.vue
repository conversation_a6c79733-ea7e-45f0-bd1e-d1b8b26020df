<template>
    <b-card
        :class="{
            'pb-0':
                modelValue.linked_products &&
                Array.isArray(modelValue.linked_products) &&
                modelValue.linked_products.length,
        }"
    >
        <p class="label-500">{{ translations["Linked products"] }}</p>
        <SelectWithAjax
            v-model:val="linkedProduct"
            @selected-item="addNewProductInList"
            :label="translations['Select which products to be linked with this product']"
            :cols-width="12"
            :column-style="true"
            :searchable="true"
            :request-on-search="true"
            :request-on-open="true"
            :apiUrl="'/admin/autocomplete/products'"
            :api-url-params="{
                'filter[!type]': 'bundle',
                'filter[draft]': 'no',
                'filter[active]': 'yes',
            }"
            :is-select-disabled="loading"
            :externalOptiosMod="uniqueLinkedProducts"
            :clear-on-select="true"
            :error="responseErrors.linkedProduct"
            :filterResults="true"
        />

        <LinkedProducts :products="modelValue.linked_products" @remove-row="removeRow" />
        <Vue3SlideUpDown
            v-model="loading"
            :duration="130"
            class="w-100 d-flex align-items-center justify-content-center"
        >
            <Loading :loading="loading" />
        </Vue3SlideUpDown>
    </b-card>
</template>

<script>
import useSharedState from "./../../composables/useSharedState";
import Loading from "@components/Loading";
import { Vue3SlideUpDown } from "vue3-slide-up-down";
import SectionMixin from "../../js/SectionMixin";
import SettingsCard from "@components/SettingsForm/Helpers/SettingsCard";
import SelectWithAjax from "@components/Form/SelectWithAjax";
import LinkedProducts from "../Helpers/LinkedProducts/LinkedProducts";

export default {
    name: "SectionLinkedProducts",
    mixins: [SectionMixin],
    components: {
        SelectWithAjax,
        SettingsCard,
        LinkedProducts,
        Loading,
        Vue3SlideUpDown,
    },
    setup() {
        const { translations } = useSharedState();

        return {
            translations,
        };
    },
    data() {
        return {
            linkedProduct: "",
            options: [],
            loading: false,
        };
    },
    methods: {
        removeRow(index) {
            this.modelValue.linked_products.splice(index, 1);
        },
        uniqueLinkedProducts(options) {
            let linkedIds = (this.modelValue.linked_products || []).map(
                (product) => product?.id
            );
            return options.filter((option) => {
                return !linkedIds.includes(option.id) && this.modelValue.id !== option.id;
            });
        },
        async addNewProductInList(product) {
            this.loading = true;

            this.modelValue.linked_products ??= [];
            try {
                const response = await this.model.find(product.id);
                this.modelValue.linked_products.push({ ...response, two_way: 1 });
                this.linkedProduct = "";
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.loading = false;
            }
        },
    },
};
</script>
