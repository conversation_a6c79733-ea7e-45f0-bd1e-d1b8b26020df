<template>
    <div id="variants-wrapper">
        <Vue3SlideUpDown :model-value="editMode" :duration="120">
            <b-row class="d-none d-md-flex">
                <b-col class="col-5">
                    <p class="label-500">{{ translations["Variant name"] }}</p>
                </b-col>
                <b-col class="col-7">
                    <p class="label-500">
                        {{ translations["Variant values"] }}
                    </p>
                </b-col>
            </b-row>
            <hr class="mx-0 my-auto d-none d-md-block" />
            <b-row v-for="(key, index) in Object.keys(results)" :key="key">
                <b-col class="col-12 col-md-5">
                    <SelectWithAjax
                        v-model:val="results[key].value"
                        :options="results[key].values_init"
                        @options="
                            (value) => {
                                results[key].values_init =
                                    this[`${key}UniqueOptions`](value);
                            }
                        "
                        :emit-on-get="true"
                        @get-search-query="
                            (value) => (searchQueryVariant[key] = value)
                        "
                        @selected-item="
                            (value) => {
                                if (value?.isCustom) {
                                    results[key].value = value.id;
                                }
                                results[key].options = [];
                                results[key].options_init = [];
                            }
                        "
                        v-model:is-loading="loadingOptionsVariant[key]"
                        :column-style="true"
                        :cols-width="12"
                        :searchable="true"
                        :request-on-search="true"
                        apiUrl="/admin/api/core/variant-parameters/autocomplete"
                        :selectClasses="`my-2 ${index === 0 ? 'mt-3' : ''}`"
                        :error="errors[`${key}.value`]"
                        :externalOptiosMod="this[`${key}UniqueOptions`]"
                        :watch-options="true"
                    >
                        <template #afterlist v-if="results[key].values_init">
                            <VariantCreateFromSelect
                                v-model:query="searchQueryVariant[key]"
                                v-model:options="results[key].values_init"
                                v-model:values="results[key].value"
                                :label="
                                    translations[
                                        'Want to add a new variant? Type the variant name to create it.'
                                    ]
                                "
                                :loading-options="loadingOptionsVariant[key]"
                            />
                        </template>
                        <template #nooptions v-if="!results[key].values_init">
                            <VariantCreateFromSelect
                                v-model:query="searchQueryVariant[key]"
                                v-model:options="results[key].values_init"
                                v-model:values="results[key].value"
                                :label="
                                    translations[
                                        'No variants yet? Please type your variant name to create it.'
                                    ]
                                "
                                :loading-options="loadingOptionsVariant[key]"
                            />
                        </template>
                    </SelectWithAjax>
                </b-col>
                <b-col class="col-11 col-md-6">
                    <SelectWithAjax
                        v-model:val="results[key].options"
                        :options="results[key].options_init"
                        @options="
                            (value) => {
                                setOptionsUnique(
                                    (results[key].options_init = [
                                        ...value,
                                        ...results[key].options_init,
                                    ]),
                                    key
                                );
                            }
                        "
                        :emit-on-get="true"
                        @get-search-query="
                            (value) => (searchQueryOptions[key] = value)
                        "
                        :is-select-disabled="!results[key].value"
                        v-model:is-loading="loadingOptionsOptions[key]"
                        :column-style="true"
                        :cols-width="12"
                        :searchable="true"
                        mode="tags"
                        :selectClasses="`my-2 ${index === 0 ? 'mt-3' : ''}`"
                        :request-on-search="true"
                        :apiUrl="
                            results[key].value
                                ? `/admin/api/core/variant-parameters/${results[key].value}/options/autocomplete`
                                : null
                        "
                        :api-url-params="{ perpage: 99 }"
                        :error="errors[`${key}.options`]"
                    >
                        <template #afterlist v-if="results[key].options_init">
                            <VariantOptionCreateFromSelect
                                v-model:query="searchQueryOptions[key]"
                                v-model:options="results[key].options_init"
                                v-model:values="results[key].options"
                                :variantId="results[key].value"
                                :label="
                                    translations[
                                        'Want to add a new variant option? Type the option name to create it.'
                                    ]
                                "
                                :loading-options="loadingOptionsOptions[key]"
                            />
                        </template>
                        <template #nooptions v-if="!results[key].options_init">
                            <VariantOptionCreateFromSelect
                                v-model:query="searchQueryOptions[key]"
                                v-model:options="results[key].options_init"
                                v-model:values="results[key].options"
                                :variantId="results[key].value"
                                :label="
                                    translations[
                                        'No options yet? Please type your option name to create it.'
                                    ]
                                "
                                :loading-options="loadingOptionsOptions[key]"
                            />
                        </template>
                    </SelectWithAjax>
                </b-col>
                <b-col
                    class="col-1 px-0 px-md-2 d-flex align-items-center justify-content-center"
                    :class="{ 'mt-2': index === 0 }"
                >
                    <a
                        href="javascript:void(0);"
                        class="pt-1"
                        @click="() => removeVariant(key)"
                    >
                        <i
                            style="
                                color: #7a7d84;
                                text-align: center;
                                font-size: 18px;
                                font-weight: 300;
                            "
                            class="fa-light fa-circle-xmark"
                        ></i>
                    </a>
                </b-col>
                <b-col
                    v-if="index < Object.keys(results).length - 1"
                    class="col-12 my-2"
                >
                    <hr />
                </b-col>
            </b-row>

            <b-row
                class="d-flex align-items-center justify-content-between mt-3"
            >
                <b-col
                    class="col-12 d-flex gap-2 align-items-center justify-content-between flex-wrap"
                >
                    <a
                        href="javascript:void(0);"
                        class="add-row-action p-0 d-flex align-items-baseline gap-2"
                        :class="{
                            'opacity-0 pe-none':
                                Object.keys(results).length >= 3,
                        }"
                        @click="addValue"
                    >
                        <i class="fa-regular fa-plus"></i>
                        {{ translations["Add value"] }}
                    </a>
                    <div
                        class="d-flex gap-2 align-items-center justify-content-end"
                    >
                        <button
                            class="btn btn-white btn-small"
                            @click="handleDiscardChanges"
                        >
                            {{ translations["Cancel"] }}
                        </button>
                        <button
                            class="btn btn-primary btn-small"
                            @click="updateVariants"
                        >
                            <i
                                class="fa-regular fa-check d-none d-md-inline-flex"
                            ></i>
                            {{ translations["Save"] }}
                        </button>
                    </div>
                </b-col>
            </b-row>
            <Vue3SlideUpDown
                :duration="160"
                :model-value="!!responseErrors.variants"
            >
                <div
                    class="d-flex flex-wrap w-100 info-box info-box-error align-items-center justify-content-between rounded-3 p-3 my-3 variants-error-box"
                >
                    <p
                        class="m-0 d-flex align-items-center justify-content-center w-100 gap-3"
                    >
                        <i class="far fa-exclamation-circle fs-5"></i>
                        {{
                            translations[responseErrors.variants] ||
                            responseErrors.variants
                        }}
                    </p>
                </div>
            </Vue3SlideUpDown>
        </Vue3SlideUpDown>
        <Vue3SlideUpDown
            :model-value="Boolean(!editMode && this.variants.length)"
            :duration="120"
        >
            <b-row class="options-preview-list gy-3">
                <b-col class="col-12">
                    <span class="label-500">
                        {{ translations["Product variants"] }}
                    </span>
                </b-col>
                <b-col
                    class="col-12"
                    v-for="(item, index) in ['p1', 'p2', 'p3'].filter(
                        (x) => product[x]
                    )"
                    :key="item"
                >
                    <div
                        v-if="product?.[item]"
                        class="option-item-preview d-flex flex-row gap-3 align-items-center justify-content-between"
                    >
                        <div class="d-flex flex-column gap-2">
                            <p class="label-600 m-0">{{ product?.[item] }}</p>
                            <div class="links-holder">
                                <span
                                    v-for="option in this[`${item}_options`]"
                                    :key="option"
                                    @click="() => toggleModal(true, [option])"
                                    class="link"
                                >
                                    {{ option }}
                                </span>
                            </div>
                        </div>
                        <a
                            href="javascript:void(0);"
                            class="edit-settings-btn-toggle"
                            @click="handleEditValues"
                        >
                            <i class="fa-light fa-pen text-secondary"></i>
                        </a>
                    </div>
                </b-col>
                <b-col
                    class="col-12 d-flex flex-column-reverse gap-2 flex-md-row justify-content-end"
                >
                    <button
                        class="btn btn-primary d-flex align-items-center gap-1 flex-grow-1 flex-md-grow-0 justify-content-center justify-content-md-start"
                        @click="() => toggleModal(true)"
                        :disabled="editMode"
                    >
                        <i class="fa-light fa-sliders"></i>
                        {{ translations["Manage variants"] }}
                    </button>
                    <!--                    <button-->
                    <!--                        class="btn btn-white d-flex align-items-center gap-1 flex-grow-1 flex-md-grow-0 justify-content-center justify-content-md-start"-->
                    <!--                        @click="handleEditValues"-->
                    <!--                    >-->
                    <!--                        <i class="fa-light fa-pen"></i>-->
                    <!--                        {{ translations["Edit values"] }}-->
                    <!--                    </button>-->
                </b-col>
            </b-row>
        </Vue3SlideUpDown>
    </div>
    <!-- <div class="mt-3 mx-1">
        <a
            href="javascript:void(0);"
            class="add-action-btn py-1"
            @click="variantWizard = true"
        >
            <i class="far fa-plus"></i>
            {{ translations["Create new variant"] }}
        </a>
    </div>
    <VariantsWizard
        v-model:feature="variants_listing"
        v-model="variantWizard"
        :final-redirect="false"
    /> -->
</template>
<script>
import _ from "lodash";
import useSharedState from "./../../../composables/useSharedState";
import useSharedVariantsModalState from "./../../../composables/useSharedVariantsModalState";

import SelectWithAjax from "@components/Form/SelectWithAjax";
import { Vue3SlideUpDown } from "vue3-slide-up-down";
import VariantCreateFromSelect from "./CreateVariantAndOption/VariantCreateFromSelect";
import VariantOptionCreateFromSelect from "./CreateVariantAndOption/VariantOptionCreateFromSelect";
// import VariantsWizard from "../../../../Variants/components/Wizard/VariantsWizard";

export default {
    name: "AddEditVariants",
    components: {
        SelectWithAjax,
        Vue3SlideUpDown,
        VariantCreateFromSelect,
        VariantOptionCreateFromSelect,
        // VariantsWizard,
    },
    props: {
        editMode: {
            type: Boolean,
            default: false,
        },
        product: {
            type: Object,
            default: () => {},
        },
        modelValue: {
            type: Array,
            default: () => [],
        },
    },
    setup() {
        const {
            variantsCollection: results,
            responseErrors,
            variants_listing,
        } = useSharedState();

        const { toggleModal } = useSharedVariantsModalState();

        return {
            results,
            responseErrors,
            toggleModal,
            variants_listing,
        };
    },
    data() {
        return {
            variants: this.modelValue,
            variantWizard: false,
            mode: this.editMode,
            errors: {},
            prevValuesCheck: false,
            searchQueryVariant: {},
            loadingOptionsVariant: {},
            searchQueryOptions: {},
            loadingOptionsOptions: {},
            backup: {},

            translations: {
                "Add value": this.$t("Add value"),
                "Edit values": this.$t("Edit values"),
                "Edit variant": this.$t("Edit variant"),
                Cancel: this.$t("Cancel"),
                "Create new variant": this.$t("Create new variant"),
                Save: this.$t("Save"),
                "Variant name": this.$t("Variant name"),
                "Variant values": this.$t("Variant values"),
                "This field is required": this.$t("This field is required"),
                "Values for the parameters are required": this.$t(
                    "Values for the parameters are required"
                ),
                "The variants field is required for multiple products": this.$t(
                    "The variants field is required for multiple products"
                ),
                "Product variants": this.$t("Product variants"),
                "Manage variants": this.$t("Manage variants"),
                "No variants yet? Please type your variant name to create it.":
                    this.$t(
                        "No variants yet? Please type your variant name to create it."
                    ),
                "Want to add a new variant? Type the variant name to create it.":
                    this.$t(
                        "Want to add a new variant? Type the variant name to create it."
                    ),
                "Want to add a new variant option? Type the option name to create it.":
                    this.$t(
                        "Want to add a new variant option? Type the option name to create it."
                    ),
                "No options yet? Please type your option name to create it.":
                    this.$t(
                        "No options yet? Please type your option name to create it."
                    ),
            },
        };
    },
    methods: {
        setOptionsUnique(options, key) {
            const seen = new Set();
            let uniqueOptions = options.filter((option) => {
                const id = option.id;
                if (seen.has(id)) {
                    return false;
                } else {
                    seen.add(id);
                    return true;
                }
            });
            this.results[key].options_init = uniqueOptions;
        },
        removeVariant(key) {
            try {
                const keys = Object.keys(this.results);

                if (keys.length === 1) {
                    this.results = {
                        p1r: {
                            value: "",
                            values_init: [],
                            options: [],
                            options_init: [],
                        },
                    };
                    return;
                } else if (key === "p3r") {
                    delete this.results[key];
                    // this.checkAndSetValuesAndOptionsToProduct(this.results);
                    return;
                } else if (key === "p2r") {
                    delete this.results[key];
                    // this.checkAndSetValuesAndOptionsToProduct(this.results);
                }

                let index = Number(key.match(/\d+/)[0]);

                for (let i = index; i < keys.length; i++) {
                    let currentKey = `p${i}r`;
                    let nextKey = `p${i + 1}r`;

                    if (this.results[nextKey]) {
                        this.results[currentKey] = _.cloneDeep(
                            this.results[nextKey]
                        );
                        delete this.results[nextKey];
                    } else {
                        delete this.results[currentKey];
                    }
                }
                // this.checkAndSetValuesAndOptionsToProduct(this.results);
            } catch (e) {
                console.log(e);
            }
        },
        p1rUniqueOptions(options) {
            return options.filter((option) => {
                return ![
                    this.results?.p2r?.value,
                    this.results?.p3r?.value,
                ].includes(option.id);
            });
        },
        p2rUniqueOptions(options) {
            return options.filter((option) => {
                return ![
                    this.results?.p1r?.value,
                    this.results?.p3r?.value,
                ].includes(option.id);
            });
        },
        p3rUniqueOptions(options) {
            return options.filter((option) => {
                return ![
                    this.results?.p2r?.value,
                    this.results?.p1r?.value,
                ].includes(option.id);
            });
        },
        handleEditValues() {
            this.backup = JSON.parse(JSON.stringify(this.results));
            this.errors = {};
            this.mode = true;
        },
        handleDiscardChanges() {
            this.results = JSON.parse(JSON.stringify(this.backup));
            this.mode = false;
        },
        addValue() {
            if (Object.keys(this.results).length >= 3) {
                return;
            }
            let lastIndex = Object.keys(this.results).length;
            this.results[`p${lastIndex + 1}r`] = {
                value: "",
                values_init: [],
                options: [],
                options_init: [],
            };
        },
        findExistingVariant(v1, v2, v3) {
            return this.variants.find(
                (variant) =>
                    variant.v1_id === v1 &&
                    variant.v2_id === v2 &&
                    variant.v3_id === v3
            );
        },
        updateVariants() {
            // this.checkAndSetValuesAndOptionsToProduct(this.results);
            let isValid = true;
            this.errors = {};

            let template = {
                v1: null,
                v2: null,
                v3: null,
                v1_id: null,
                v2_id: null,
                v3_id: null,
                quantity: 0,
                sku: "",
                barcode: "",
                price: 0,
                // delivery_price: null,
                weight: 0,
                v1r: null,
                v2r: null,
                v3r: null,
                images: [],
                unit_value: 0,
                unit_text: "",
                minimum: 1,
                discount: 0,
                save: 0,
                base_unit_id: null,
                base_unit_value: 1,
                unit_id: null,
                unit_type: null,
            };

            // /////// Set default values ///////
            // template.price = this.product?.variants?.[0]?.price || 0;
            // template.quantity = this.product?.variants?.[0]?.quantity || 0;
            // template.unit_value = this.product?.variants?.[0]?.unit_value || 0;
            // template.unit_text = this.product?.variants?.[0]?.unit_text || "";
            // template.minimum = this.product?.variants?.[0]?.minimum || 1;
            // template.discount = this.product?.variants?.[0]?.discount || 0;
            // template.sku = this.product?.variants?.[0]?.sku || "";
            // template.barcode = this.product?.variants?.[0]?.barcode || "";
            // template.weight = this.product?.variants?.[0]?.weight || 0;

            // ["p1r", "p2r", "p3r"].forEach((key) => {
            // if (this.results[key]) {
            //     if (!this.results[key].value) {
            //         this.errors[`${key}.value`] = this.translations[
            //             "This field is required"
            //         ];
            //         isValid = false;
            //         return;
            //     }
            //     if (this.results[key].options.length === 0) {
            //         this.errors[`${key}.options`] = this.translations[
            //             "Values for the parameters are required"
            //         ];
            //         isValid = false;
            //         return;
            //     }
            // }

            // if (!isValid) {
            //     return;
            // }
            // });

            // if (!isValid) {
            //     return;
            // }
            let validKeys = Object.keys(this.results).filter(
                (key) =>
                    !!this.results[key]?.value &&
                    !!this.results[key]?.options?.length
            );
            if (!validKeys.length) {
                ["p1r", "p2r", "p3r"].forEach((key) => {
                    if (this.results[key]) {
                        if (!this.results[key]?.value) {
                            this.errors[`${key}.value`] =
                                this.translations["This field is required"];
                        }
                        if (this.results[key]?.options?.length === 0) {
                            this.errors[`${key}.options`] =
                                this.translations[
                                    "Values for the parameters are required"
                                ];
                        }
                    }
                });
                return;
            }
            // let invalidKeys = Object.keys(this.results).filter(
            //     (key) =>
            //         !this.results[key]?.value ||
            //         !this.results[key]?.options?.length
            // );
            // if (!!invalidKeys.length) {
            //     invalidKeys.forEach((key) => {
            //         this.removeVariant(key);
            //     });
            // }

            let tempResults = {};

            validKeys.forEach((key, index) => {
                tempResults[`p${index + 1}r`] = _.cloneDeep(this.results[key]);
            });
            this.results = tempResults;

            const combinations = [];

            this.results.p1r.options.forEach((option, i1) => {
                let option1_name = this.results.p1r.options_init.find(
                    (element) => element.id === option
                );
                if (
                    !this.results.p2r ||
                    this.results.p2r.options.length === 0
                ) {
                    const existingVariant = this.findExistingVariant(
                        option,
                        null,
                        null
                    );
                    if (!existingVariant) {
                        combinations.push({
                            ...template,
                            id: `new-${i1}`,
                            v1_id: option,
                            v1: option1_name?.name,
                            v1r: {
                                id: option,
                                name: option1_name?.name,
                            },
                        });
                    } else {
                        combinations.push(existingVariant);
                    }
                    return;
                }
                this.results.p2r.options.forEach((option2, i2) => {
                    let option2_name = this.results.p2r.options_init.find(
                        (element) => element.id === option2
                    );
                    if (
                        !this.results.p3r ||
                        this.results.p3r.options.length === 0
                    ) {
                        const existingVariant = this.findExistingVariant(
                            option,
                            option2,
                            null
                        );
                        if (!existingVariant) {
                            combinations.push({
                                ...template,
                                id: `new-${i2}`,
                                v1_id: option,
                                v1: option1_name?.name,
                                v2_id: option2,
                                v2: option2_name?.name,
                                v1r: {
                                    id: option,
                                    name: option1_name?.name,
                                },
                                v2r: {
                                    id: option2,
                                    name: option2_name?.name,
                                },
                            });
                        } else {
                            combinations.push(existingVariant);
                        }
                        return;
                    }
                    this.results.p3r.options.forEach((option3, i3) => {
                        let option3_name = this.results.p3r.options_init.find(
                            (element) => element.id === option3
                        );
                        const existingVariant = this.findExistingVariant(
                            option,
                            option2,
                            option3
                        );
                        if (!existingVariant) {
                            combinations.push({
                                ...template,
                                id: `new-${i3}`,
                                v1_id: option,
                                v1: option1_name?.name,
                                v2_id: option2,
                                v2: option2_name?.name,
                                v3_id: option3,
                                v3: option3_name?.name,
                                v1r: {
                                    id: option,
                                    name: option1_name?.name,
                                },
                                v2r: {
                                    id: option2,
                                    name: option2_name?.name,
                                },
                                v3r: {
                                    id: option3,
                                    name: option3_name?.name,
                                },
                            });
                        } else {
                            combinations.push(existingVariant);
                        }
                    });
                });
            });

            this.checkAndSetValuesAndOptionsToProduct(this.results);

            this.variants = combinations;
            this.mode = !isValid;
            setTimeout(() => {
                this.toggleModal(true);
            }, 350);
        },
        checkAndSetValuesAndOptionsToProduct(results) {
            [
                { id: "p1_id", name: "p1", resultId: "p1r" },
                { id: "p2_id", name: "p2", resultId: "p2r" },
                { id: "p3_id", name: "p3", resultId: "p3r" },
            ].forEach((key) => {
                if (
                    results[key.resultId] &&
                    results[key.resultId].value !== this.product[key.id]
                ) {
                    let option = results[key.resultId].values_init.find(
                        (x) => x.id === results[key.resultId].value
                    );
                    let isCustom = Boolean(option?.isCustom);

                    this.product[key.id] = isCustom
                        ? "custom"
                        : results[key.resultId].value;

                    this.product[key.name] = option?.name;

                    this.product[key.resultId] = {
                        id: isCustom ? null : results[key.resultId].value,
                        name: results[key.resultId].values_init.find(
                            (x) => x.id === results[key.resultId].value
                        )?.name,
                    };
                } else if (!results[key.resultId]) {
                    this.product[key.id] = null;
                    this.product[key.name] = null;
                    this.product[key.resultId] = null;
                }
            });
            if (
                !this.results?.p1r?.value ||
                !this.results?.p1r?.options?.length
            ) {
                this.mode = true;
            }
        },
        collectValues() {
            ["p1r", "p2r", "p3r"].forEach((key) => {
                if (this.product?.[key]) {
                    this.results[key] ??= {
                        value: "",
                        values_init: [],
                        options: [],
                        options_init: [],
                    };
                    this.results[key].value = this.product[key].id;

                    if (this.results[key].value) {
                        this.results[key].values_init = [this.product[key]];
                    }
                }
            });

            const v1r_values_set = new Set();
            const v2r_values_set = new Set();
            const v3r_values_set = new Set();

            const v1r_values_options_set = new Set();
            const v2r_values_options_set = new Set();
            const v3r_values_options_set = new Set();

            this.product.variants.forEach((variantOption) => {
                if (variantOption.v1r) {
                    v1r_values_set.add(variantOption.v1r.id);
                    v1r_values_options_set.add(variantOption.v1r);
                }
                if (variantOption.v2r) {
                    v2r_values_set.add(variantOption.v2r.id);
                    v2r_values_options_set.add(variantOption.v2r);
                }
                if (variantOption.v3r) {
                    v3r_values_set.add(variantOption.v3r.id);
                    v3r_values_options_set.add(variantOption.v3r);
                }
            });

            if (this.results.p1r) {
                this.results.p1r.options = Array.from(v1r_values_set);
                this.results.p1r.options_init = Array.from(
                    v1r_values_options_set
                );
            }
            if (this.results.p2r) {
                this.results.p2r.options = Array.from(v2r_values_set);
                this.results.p2r.options_init = Array.from(
                    v2r_values_options_set
                );
            }
            if (this.results.p3r) {
                this.results.p3r.options = Array.from(v3r_values_set);
                this.results.p3r.options_init = Array.from(
                    v3r_values_options_set
                );
            }

            if (
                !this.results?.p1r?.value ||
                !this.results?.p1r?.options?.length
            ) {
                this.mode = true;
            }
        },
    },
    mounted() {
        this.variants = this.modelValue;
        this.mode = this.editMode;

        if ((this.product?.variants || []).length > 0) {
            this.collectValues();
            this.prevValuesCheck = true;
        } else {
            this.mode = true;
        }
    },
    computed: {
        p1_options() {
            let optionsSet = new Set();
            let tempArray = [];

            this.variants.forEach((variant) => {
                if (variant.v1r) {
                    tempArray.push(variant.v1r);
                }
            });
            if (tempArray.length > 0) {
                tempArray.forEach((element) => {
                    optionsSet.add(element.name);
                });
            }
            return Array.from(optionsSet);
        },
        p2_options() {
            let optionsSet = new Set();
            let tempArray = [];

            this.variants.forEach((variant) => {
                if (variant.v2r) {
                    tempArray.push(variant.v2r);
                }
            });
            if (tempArray.length > 0) {
                tempArray.forEach((element) => {
                    optionsSet.add(element.name);
                });
            }
            return Array.from(optionsSet);
        },
        p3_options() {
            let optionsSet = new Set();
            let tempArray = [];

            this.variants.forEach((variant) => {
                if (variant.v3r) {
                    tempArray.push(variant.v3r);
                }
            });
            if (tempArray.length > 0) {
                tempArray.forEach((element) => {
                    optionsSet.add(element.name);
                });
            }
            return Array.from(optionsSet);
        },
    },
    watch: {
        modelValue: {
            deep: true,
            handler(value) {
                this.variants = value;
                // this.collectValues();
                // this.checkAndSetValuesAndOptionsToProduct(this.results);
            },
        },
        variants: {
            deep: true,
            handler(value) {
                this.$emit("update:modelValue", value);
            },
        },
        editMode: {
            handler(value) {
                this.mode = value;
            },
        },
        mode: {
            handler(value) {
                this.$emit("update:editMode", value);
            },
        },
        results: {
            deep: true,
            handler(value) {
                if (!value?.p1r?.value || !value?.p1r?.options?.length) {
                    this.mode = true;
                }
            },
        },
    },
    emits: ["update:modelValue", "update:editMode"],
};
</script>
<style>
@use "./../../../scss/style.scss" as *;
</style>
