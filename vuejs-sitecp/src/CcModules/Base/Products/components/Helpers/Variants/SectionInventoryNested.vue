<template>
    <div class="nested-product-settings">
        <span class="label-500">
            {{ translations["Inventory"] }}
        </span>

        <div v-if="product?.type !== 'multiple'" class="advanced-variant-settings bg-white p-0">
            <template
                v-if="
                    product?.type === 'simple' ||
                    (product.type === 'digital' &&
                        ['file', 'page'].includes(product.type_digital))
                "
            >
                <ActiveSwitch
                    v-model:is-active="sku_barcode_enabled"
                    :false-value="false"
                    :label-text="translations['Product has SKU/Barcode']"
                    :reverse="true"
                    :true-value="true"
                />

                <Vue3SlideUpDown
                    :duration="140"
                    :model-value="sku_barcode_enabled"
                    class="nested"
                >
                    <b-row>
                        <b-col class="col-12 col-md-6">
                            <InputComponent
                                v-model="product.variants[0].sku"
                                :column-style="true"
                                :error="
                                    translations[responseErrors['sku']] ||
                                    responseErrors['sku'] ||
                                    translations[
                                        responseErrors['`variants.0.sku']
                                    ] ||
                                    responseErrors[`variants.0.sku`]
                                "
                                :label-text="translations['SKU']"
                                :no-margin="true"
                            />
                        </b-col>
                        <b-col class="col-12 col-md-6">
                            <InputComponent
                                v-model="product.variants[0].barcode"
                                :column-style="true"
                                :error="
                                    translations[responseErrors['barcode']] ||
                                    responseErrors['barcode'] ||
                                    translations[
                                        responseErrors['variants.0.barcode']
                                    ] ||
                                    responseErrors['variants.0.barcode']
                                "
                                :label-text="translations['Barcode']"
                                :no-margin="true"
                            />
                        </b-col>
                    </b-row>
                </Vue3SlideUpDown>
            </template>
            <ActiveSwitch
                v-model:is-active="product.tracking"
                :label-text="translations['Track inventory']"
                :reverse="true"
                :tooltip="true"
                :tooltip-text="
                    translations[
                        'If checked, the quantity of this product will be tracked by the system. When the product is sold, the necessary quantity will be subtracted from the total product quantity.'
                    ]
                "
            />
            <Vue3SlideUpDown
                v-if="
                    product?.type === 'simple' ||
                    (product.type === 'digital' &&
                        ['file', 'page'].includes(product.type_digital))
                "
                :duration="140"
                :model-value="['yes', 1].includes(product?.tracking)"
                class="nested"
            >
                <b-row>
                    <b-col class="col-12 col-md-4">
                        <InputComponent
                            v-model="product.variants[0].quantity"
                            :column-style="true"
                            :error="
                                translations[
                                    responseErrors['variants.0.quantity']
                                ] || responseErrors['variants.0.quantity']
                            "
                            :label-text="translations['Product quantity']"
                            :min="0"
                            :no-margin="true"
                            :showIncrementDecrement="false"
                            :unit="unit"
                            type="number"
                        />
                    </b-col>
                </b-row>
            </Vue3SlideUpDown>
            <ActiveSwitch
                v-if="
                    ['simple', 'digital'].includes(product.type) &&
                    !['page'].includes(product.type_digital)
                "
                v-model:is-active="product.shipping"
                :label-text="translations['Requires shipping']"
                :reverse="true"
            />
            <Vue3SlideUpDown
                v-if="
                    product?.type === 'simple' ||
                    (product.type === 'digital' &&
                        ['file'].includes(product.type_digital))
                "
                :duration="140"
                :model-value="['yes', 1].includes(product?.shipping)"
                class="nested"
            >
                <b-row>
                    <b-col class="col-12 col-md-4">
                        <WeightComponent
                            v-model="product.variants[0].weight"
                            :column-style="true"
                            :error="
                                translations[responseErrors['weight']] ||
                                responseErrors['weight'] ||
                                translations[
                                    responseErrors['variants.0.weight']
                                ] ||
                                responseErrors['variants.0.weight'] ||
                                translations[
                                    responseErrors['variant.weight']
                                ] ||
                                responseErrors['variant.weight']
                            "
                            :label-text="translations['Product weight']"
                            :min="0.01"
                            :no-margin="true"
                            placeholder="0"
                        />
                    </b-col>
                </b-row>
            </Vue3SlideUpDown>
        </div>

        <div class="advanced-variant-settings">
            <div class="head">
                <div
                    class="d-flex flex-row flex-wrap flex-md-nowrap justify-content-between align-items-center gap-2"
                >
                    <div class="d-flex flex-column gap-1">
                        <span class="label-500">
                            {{ translations["Advanced inventory settings"] }}
                        </span>
                        <span class="text-400-blue">
                            {{
                                translations[
                                    "You can make advanced settings for the product like tracking, shipping, etc..."
                                    ]
                            }}
                        </span>
                    </div>

                    <a
                        class="cc-purple text-nowrap"
                        href="javascript:void(0)"
                        @click="advanced = !advanced"
                    >
                        {{
                            advanced
                                ? translations["Hide settings"]
                                : translations["Show settings"]
                        }}
                        <i
                            :class="
                                advanced
                                    ? 'fal fa-chevron-up'
                                    : 'fal fa-chevron-down'
                            "
                            class="ms-1"
                        ></i>
                    </a>
                </div>

                <div v-if="advanced" class="separator"></div>
            </div>
            <Vue3SlideUpDown
                :duration="140"
                :model-value="advanced"
                class="body"
            >
                <div
                    v-if="
                        !product.variants[0].unit_id &&
                        product.type === 'simple'
                    "
                >
                    <ActiveSwitch
                        v-model:is-active="min_quantity_enabled"
                        :false-value="false"
                        :label-text="translations['Minimum order qty']"
                        :reverse="true"
                        :tooltip="true"
                        :tooltip-text="
                            translations[
                                'This is the initial quantity of the product that the customer must order.'
                            ]
                        "
                        :true-value="true"
                        labelClasses="text-400-blue"
                    />
                    <Vue3SlideUpDown
                        :duration="140"
                        :model-value="min_quantity_enabled"
                        class="nested"
                    >
                        <b-row class="pt-3">
                            <b-col class="col-12 col-md-4">
                                <InputNumberComponent
                                    v-model="product.variants[0].minimum"
                                    :column-style="true"
                                    :error="errorHandle"
                                    :no-margin="true"
                                    :showIncrementDecrement="false"
                                    :unit="unit"
                                    type="number"
                                    v-bind="propsMinimum"
                                />
                            </b-col>
                        </b-row>
                    </Vue3SlideUpDown>
                </div>

                <ActiveSwitch
                    v-model:is-active="product.continue_selling"
                    :label-text="
                        translations['Continue selling when out ot stock']
                    "
                    :reverse="true"
                    labelClasses="text-400-blue"
                />
                <Vue3SlideUpDown
                    :duration="140"
                    :model-value="Boolean(product.tracking)"
                >
                    <ActiveSwitch
                        v-model:is-active="threshold_enabled"
                        :false-value="false"
                        :label-text="
                            translations['Notify me when quantity is below']
                        "
                        :reverse="true"
                        :tooltip="true"
                        :tooltip-text="
                            translations[
                                'If checked, the system will notify the administrator when this product\'s quantity has dropped below the specified number.'
                            ]
                        "
                        :true-value="true"
                        labelClasses="text-400-blue"
                    />
                    <Vue3SlideUpDown
                        :duration="140"
                        :model-value="threshold_enabled"
                        class="nested"
                    >
                        <b-row>
                            <b-col class="col-12 col-md-4 pt-3">
                                <InputComponent
                                    v-model="product.threshold"
                                    :column-style="true"
                                    :error="
                                        translations[
                                            responseErrors['threshold']
                                        ] || responseErrors['threshold']
                                    "
                                    :label-text="
                                        translations['Product quantity']
                                    "
                                    :min="0"
                                    :no-margin="true"
                                    :showIncrementDecrement="false"
                                    :step="1"
                                    labelClasses="text-400-blue"
                                    type="number"
                                />
                            </b-col>
                        </b-row>
                    </Vue3SlideUpDown>
                </Vue3SlideUpDown>

                <hr class="mx-auto my-0"/>
                <ActiveSwitch
                    v-model:is-active="custom_statuses"
                    :false-value="false"
                    :label-text="translations['Custom statuses']"
                    :reverse="true"
                    :true-value="true"
                    labelClasses="text-400-blue"
                />
                <Vue3SlideUpDown
                    :duration="140"
                    :model-value="custom_statuses"
                    class="nested"
                >
                    <CreateOrEdit v-model="modal" :model="modelStatuses"/>
                    <b-row>
                        <b-col class="col-12 col-md-6">
                            <SelectWithAjax
                                v-model:val="product.status_id"
                                :apiUrl="'/admin/api/core/product-statuses'"
                                :cols-width="12"
                                :column-style="true"
                                :error="
                                    translations[responseErrors['status_id']] ||
                                    responseErrors['status_id']
                                "
                                :label="translations['Product status in stock']"
                                :no-margin="true"
                                :options="meta.statuses || options"
                                :request-on-open="true"
                                :request-on-search="true"
                                :searchable="true"
                                labelClasses="text-400-blue"
                            />
                        </b-col>
                        <b-col class="col-12 col-md-6">
                            <SelectWithAjax
                                v-model:val="product.out_of_stock_id"
                                :apiUrl="'/admin/api/core/product-statuses'"
                                :cols-width="12"
                                :column-style="true"
                                :error="
                                    translations[
                                        responseErrors['out_of_stock_id']
                                    ] || responseErrors['out_of_stock_id']
                                "
                                :is-select-disabled="
                                    ['yes', 1, true].includes(
                                        product.continue_selling
                                    ) ||
                                    ['no', 0, false].includes(product?.tracking)
                                "
                                :label="
                                    translations['Product status out of stock']
                                "
                                :options="meta.statuses || options"
                                :request-on-open="true"
                                :request-on-search="true"
                                :searchable="true"
                                :tooltip="
                                    ['yes', 1, true].includes(
                                        product.continue_selling
                                    ) ||
                                    ['no', 0, false].includes(product?.tracking)
                                "
                                :tooltip-text="
                                    translations[
                                        'Enable Track Inventory or disable Continue Selling option'
                                    ]
                                "
                                labelClasses="text-400-blue"
                                select-classes="mb-0 mt-3 mt-md-0"
                            />
                        </b-col>
                        <b-col class="col-12 col-md-6 mt-3">
                            <a
                                class="add-action-btn py-1"
                                href="javascript:void(0);"
                                @click="modal = true"
                            >
                                <i class="far fa-plus"></i>
                                {{ translations["Create new status"] }}
                            </a>
                        </b-col>
                    </b-row>
                </Vue3SlideUpDown>
                <template
                    v-if="
                        ['simple', 'multiple', 'digital'].includes(
                            product.type
                        ) && !['page'].includes(product.type_digital)
                    "
                >
                    <hr class="mx-auto my-0"/>
                    <ActiveSwitch
                        v-model:is-active="dimentions_enabled"
                        :false-value="false"
                        :help-block="
                            translations[
                                'Define product dimensions like width, height, length'
                            ]
                        "
                        :help-block-classes="'text-400-blue'"
                        :label-text="translations['Dimensions']"
                        :reverse="true"
                        :true-value="true"
                        labelClasses="text-400-blue"
                    />
                    <Vue3SlideUpDown
                        :duration="140"
                        :model-value="dimentions_enabled"
                        class="nested"
                    >
                        <b-row class="w-100 justify-content-center">
                            <b-col class="col-12 col-md-6 pb-3">
                                <figure
                                    class="m-0 d-flex align-items-center justify-content-center"
                                >
                                    <svg
                                        fill="none"
                                        height="182"
                                        viewBox="0 0 346 182"
                                        width="346"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M139.367 2.25781L48.1694 14.9988L207.074 30.7866L305.181 15.8298L139.367 2.25781Z"
                                            fill="#E1E1ED"
                                        />
                                        <path
                                            d="M311.813 123.019L206.883 156.048V30.8869L311.813 15.8283V123.019Z"
                                            fill="#D6D9E9"
                                        />
                                        <path
                                            d="M204.797 31.2312L43.777 16.2379C43.0198 16.1674 42.366 16.7631 42.366 17.5236V121.688C42.366 122.302 42.7984 122.831 43.4002 122.953L204.42 155.666C205.221 155.829 205.969 155.217 205.969 154.401V32.5169C205.969 31.8501 205.461 31.293 204.797 31.2312Z"
                                            fill="#F2F2FA"
                                        />
                                        <path
                                            d="M41.8132 15.5528L206.522 31.0636M41.8132 15.5528V121.964C41.8132 122.578 42.2465 123.108 42.8491 123.229L206.522 156.258M41.8132 15.5528L138.954 2.27648C139.045 2.26407 139.137 2.26137 139.228 2.26841L311.537 15.5528M206.522 31.0636V156.258M206.522 31.0636L311.537 15.5528M206.522 156.258L310.635 123.306C311.172 123.136 311.537 122.638 311.537 122.075V15.5528"
                                            stroke="#B6B9D2"
                                            stroke-linejoin="round"
                                            stroke-width="2.58255"
                                        />
                                        <path
                                            d="M95.3835 7.63281L258.299 22.9844"
                                            stroke="#B6B9D2"
                                            stroke-width="2"
                                        />
                                        <path
                                            d="M23.6492 13.9121C23.4539 13.7168 23.1374 13.7168 22.9421 13.9121L19.7601 17.0941C19.5649 17.2893 19.5649 17.6059 19.7601 17.8012C19.9554 17.9964 20.272 17.9964 20.4672 17.8012L23.2957 14.9727L26.1241 17.8012C26.3193 17.9964 26.6359 17.9964 26.8312 17.8012C27.0265 17.6059 27.0265 17.2893 26.8312 17.0941L23.6492 13.9121ZM22.9421 119.927C23.1374 120.122 23.4539 120.122 23.6492 119.927L26.8312 116.745C27.0265 116.55 27.0265 116.233 26.8312 116.038C26.6359 115.842 26.3193 115.842 26.1241 116.038L23.2957 118.866L20.4672 116.038C20.272 115.842 19.9554 115.842 19.7601 116.038C19.5649 116.233 19.5649 116.55 19.7601 116.745L22.9421 119.927ZM22.7957 14.2656V119.573H23.7957V14.2656H22.7957Z"
                                            fill="#7A7D84"
                                        />
                                        <path
                                            d="M53.6492 28.6464C53.4539 28.4512 53.1374 28.4512 52.9421 28.6464L49.7601 31.8284C49.5649 32.0237 49.5649 32.3403 49.7601 32.5355C49.9554 32.7308 50.272 32.7308 50.4672 32.5355L53.2957 29.7071L56.1241 32.5355C56.3193 32.7308 56.6359 32.7308 56.8312 32.5355C57.0265 32.3403 57.0265 32.0237 56.8312 31.8284L53.6492 28.6464ZM52.9421 107.854C53.1374 108.049 53.4539 108.049 53.6492 107.854L56.8312 104.672C57.0264 104.476 57.0264 104.16 56.8312 103.964C56.6359 103.769 56.3193 103.769 56.1241 103.964L53.2957 106.793L50.4672 103.964C50.272 103.769 49.9554 103.769 49.7601 103.964C49.5649 104.16 49.5649 104.476 49.7601 104.672L52.9421 107.854ZM52.7957 29L52.7957 107.5L53.7957 107.5L53.7957 29L52.7957 29Z"
                                            fill="#AEB1CD"
                                        />
                                        <path
                                            d="M316.455 141.975C316.581 141.729 316.484 141.428 316.238 141.301L312.236 139.245C311.99 139.119 311.689 139.216 311.563 139.461C311.436 139.707 311.533 140.008 311.779 140.134L315.337 141.962L313.509 145.52C313.383 145.766 313.48 146.067 313.725 146.194C313.971 146.32 314.272 146.223 314.398 145.977L316.455 141.975ZM215.304 173.725C215.177 173.97 215.274 174.272 215.52 174.398L219.523 176.454C219.768 176.58 220.07 176.484 220.196 176.238C220.322 175.992 220.225 175.691 219.98 175.565L216.422 173.737L218.249 170.179C218.376 169.933 218.279 169.632 218.033 169.506C217.788 169.38 217.486 169.476 217.36 169.722L215.304 173.725ZM315.857 141.27L215.595 173.477L215.901 174.429L316.163 142.222L315.857 141.27Z"
                                            fill="#7A7D84"
                                        />
                                        <path
                                            d="M305.617 116.244C305.752 116.003 305.666 115.698 305.425 115.563L301.496 113.369C301.255 113.234 300.95 113.32 300.816 113.561C300.681 113.803 300.767 114.107 301.008 114.242L304.5 116.193L302.549 119.685C302.415 119.926 302.501 120.23 302.742 120.365C302.983 120.5 303.288 120.413 303.422 120.172L305.617 116.244ZM215.312 141.08C215.177 141.321 215.263 141.626 215.504 141.76L219.433 143.955C219.674 144.09 219.979 144.003 220.113 143.762C220.248 143.521 220.162 143.217 219.921 143.082L216.429 141.131L218.38 137.639C218.514 137.398 218.428 137.093 218.187 136.959C217.946 136.824 217.641 136.91 217.507 137.151L215.312 141.08ZM305.044 115.519L215.612 140.843L215.885 141.805L305.317 116.481L305.044 115.519Z"
                                            fill="#AEB1CD"
                                        />
                                        <path
                                            d="M204.934 174.513C205.165 174.363 205.231 174.053 205.081 173.822L202.631 170.047C202.481 169.815 202.172 169.749 201.94 169.899C201.708 170.05 201.642 170.359 201.793 170.591L203.97 173.946L200.615 176.124C200.383 176.274 200.317 176.584 200.467 176.815C200.618 177.047 200.927 177.113 201.159 176.963L204.934 174.513ZM36.3915 137.904C36.1599 138.055 36.0939 138.364 36.2443 138.596L38.6938 142.371C38.8441 142.602 39.1538 142.668 39.3854 142.518C39.6171 142.368 39.683 142.058 39.5327 141.826L37.3553 138.471L40.7107 136.294C40.9424 136.143 41.0083 135.834 40.858 135.602C40.7077 135.37 40.398 135.304 40.1664 135.455L36.3915 137.904ZM204.766 173.605L36.7678 137.835L36.5596 138.813L204.557 174.583L204.766 173.605Z"
                                            fill="#7A7D84"
                                        />
                                        <path
                                            d="M197.778 141.739C198.008 141.585 198.069 141.275 197.915 141.045L195.409 137.308C195.256 137.078 194.945 137.017 194.716 137.171C194.486 137.324 194.425 137.635 194.579 137.864L196.806 141.187L193.484 143.414C193.255 143.568 193.193 143.879 193.347 144.108C193.501 144.337 193.811 144.399 194.041 144.245L197.778 141.739ZM58.7216 113.585C58.4922 113.738 58.4309 114.049 58.5847 114.278L61.0906 118.016C61.2444 118.245 61.555 118.307 61.7844 118.153C62.0137 117.999 62.075 117.689 61.9212 117.459L59.6937 114.137L63.0161 111.909C63.2455 111.756 63.3068 111.445 63.153 111.216C62.9992 110.986 62.6886 110.925 62.4593 111.079L58.7216 113.585ZM197.597 140.833L59.0968 113.509L58.9032 114.491L197.403 141.814L197.597 140.833Z"
                                            fill="#AEB1CD"
                                        />
                                        <path
                                            d="M7.10767 66.1162V67.5928H1.87134V66.1162H7.10767ZM2.38403 62.0078V71.9609H0.50415V62.0078H2.38403ZM8.5022 62.0078V71.9609H6.62231V62.0078H8.5022Z"
                                            fill="#3D414D"
                                        />
                                        <path
                                            d="M106.99 179.617V181.094H101.986V179.617H106.99ZM102.553 171.141V181.094H100.673V171.141H102.553Z"
                                            fill="#3D414D"
                                        />
                                        <path
                                            d="M277.762 179.187L279.744 171.141H280.818L280.886 172.836L278.767 181.094H277.632L277.762 179.187ZM276.511 171.141L278.138 179.159V181.094H276.901L274.645 171.141H276.511ZM282.944 179.125L284.543 171.141H286.416L284.16 181.094H282.923L282.944 179.125ZM281.33 171.141L283.313 179.214L283.429 181.094H282.294L280.182 172.829L280.264 171.141H281.33Z"
                                            fill="#3D414D"
                                        />
                                        <path
                                            d="M345.44 73.8906C345.721 75.0859 344.912 76.2109 343.787 76.2109H329.198C328.073 76.2109 327.264 75.0859 327.545 73.8906L330.112 63.625C330.252 63.0977 330.709 62.7461 331.201 62.7461H333.768C333.522 62.2891 333.416 61.832 333.416 61.3398C333.416 59.6172 334.787 58.2461 336.51 58.2461C338.198 58.2461 339.604 59.6172 339.604 61.3398C339.604 61.832 339.463 62.2891 339.217 62.7461H341.784C342.276 62.7461 342.733 63.0977 342.873 63.625L345.44 73.8906ZM335.104 61.3047C335.104 62.1133 335.701 62.7109 336.51 62.7109C337.284 62.7109 337.916 62.1133 337.916 61.3047C337.916 60.5312 337.284 59.8984 336.51 59.8984C335.701 59.8984 335.104 60.5312 335.104 61.3047ZM343.752 74.5234C343.787 74.5234 343.823 74.418 343.787 74.3125L341.326 64.3984H331.659L329.198 74.3125C329.162 74.418 329.198 74.5234 329.233 74.5234H343.752Z"
                                            fill="#3D414D"
                                        />
                                    </svg>
                                </figure>
                            </b-col>

                            <b-col
                                class="col-12 col-md-7 d-flex flex-column flex-md-row gap-md-3 w-100"
                            >
                                <MillimeterComponent
                                    v-model="product.meta.depth"
                                    :column-style="true"
                                    :digits="0"
                                    :error="
                                        translations[responseErrors['depth']] ||
                                        responseErrors['depth']
                                    "
                                    :label-text="translations['Length']"
                                    :min="1"
                                    :no-margin="true"
                                    :step="1"
                                    labelClasses="text-400-blue mb-1"
                                    placeholder="0"
                                />
                                <MillimeterComponent
                                    v-model="product.meta.width"
                                    :column-style="true"
                                    :error="
                                        translations[responseErrors['width']] ||
                                        responseErrors['width']
                                    "
                                    :label-text="translations['Width']"
                                    :min="1"
                                    :no-margin="true"
                                    :step="1"
                                    labelClasses="text-400-blue mb-1"
                                    placeholder="0"
                                />
                                <MillimeterComponent
                                    v-model="product.meta.height"
                                    :column-style="true"
                                    :error="
                                        translations[
                                            responseErrors['height']
                                        ] || responseErrors['height']
                                    "
                                    :label-text="translations['Height']"
                                    :min="1"
                                    :no-margin="true"
                                    :step="1"
                                    labelClasses="text-400-blue mb-1"
                                    placeholder="0"
                                />
                            </b-col>
                        </b-row>
                    </Vue3SlideUpDown>
                </template>
            </Vue3SlideUpDown>
        </div>
    </div>
</template>

<script>
import Statuses from "./../../../js/Statuses";
import useSharedState from "../../../composables/useSharedState";

import {Vue3SlideUpDown} from "vue3-slide-up-down";
import WeightComponent from "@components/Form/WeightComponent";
import ActiveSwitch from "@components/Form/ActiveSwitch";
import InputComponent from "@components/Form/InputComponent";
import SelectWithAjax from "@components/Form/SelectWithAjax";
import CreateOrEdit from "./../../../../ProductStatuses/components/Form/CreateOrEdit";
import MillimeterComponent from "@components/Form/MillimeterComponent";
import InputNumberComponent from "@components/Form/InputNumberComponent";

export default {
    name: "SectionInventoryNested",
    components: {
        ActiveSwitch,
        InputComponent,
        Vue3SlideUpDown,
        SelectWithAjax,
        WeightComponent,
        MillimeterComponent,
        CreateOrEdit,
        InputNumberComponent,
    },
    setup() {
        const {
            product,
            translations,
            meta,
            responseErrors,
            cookieData,
            cookieBody,
        } = useSharedState();

        return {
            translations,
            product,
            meta,
            responseErrors,
            cookieData,
            cookieBody,
        };
    },
    data() {
        return {
            modelStatuses: new Statuses(),
            options: [],
            modal: false,

            backup_dimentions: {},

            advanced: false,
            threshold_enabled: false,
            sku_barcode_enabled: false,
            custom_statuses: false,
            dimentions_enabled: false,
            min_quantity_enabled: true,
        };
    },
    computed: {
        unit() {
            if (
                this.product &&
                this.product?.variants[0]?.unit_id &&
                this.meta &&
                this.meta?.units
            ) {
                let option = this.meta.units.find(
                    (unit) => unit.id === this.product?.variants[0]?.unit_id
                );

                if (option) {
                    return option.short_name || option.name;
                }
            }
            return null;
        },
        propsMinimum() {
            if (this.unit && this.product?.variants[0]?.unit_id) {
                let option = this.meta.units.find(
                    (unit) => unit.id === this.product?.variants[0]?.unit_id
                );
                if (option) {
                    return {
                        step: option?.steps || null,
                        digits: option?.decimals || 2,
                        min: 0,
                    };
                }
            }
            return {
                min: 0,
            };
        },
        errorHandle() {
            const minimumValue = this.product?.variants?.[0]?.minimum;

            if (this.unit && minimumValue === 0) {
                this.responseErrors["minimum"] =
                    "Minimum order quantity must be greater than 0";
            } else if (!this.unit && minimumValue < 1) {
                // this.responseErrors["minimum"] =
                //     "Minimum order quantity must be at least 1";
            } else {
                delete this.responseErrors["minimum"];
            }

            return (
                this.translations[this.responseErrors["minimum"]] ||
                this.responseErrors["minimum"] ||
                this.translations[this.responseErrors["variants.0.minimum"]] ||
                this.responseErrors["variants.0.minimum"]
            );
        },
    },
    methods: {
        async setOpenAdvanced() {
            await new Promise((resolve) => {
                setTimeout(resolve, 350);
            });
            this.threshold_enabled = this.product?.threshold ? true : false;

            this.sku_barcode_enabled =
                this.product?.variants[0]?.sku ||
                this.product?.variants[0]?.barcode
                    ? true
                    : false;
            if (
                this.product.meta.height ||
                this.product.meta.depth ||
                this.product.meta.width
            ) {
                this.dimentions_enabled = true;
            }
            if (this.product.status_id || this.product.out_of_stock_id) {
                this.custom_statuses = true;
            }

            if (this.$route.query?.new) {
                if (this.cookieData?.min_quantity_enabled) {
                    this.min_quantity_enabled = true;
                }
                if (this.cookieData?.dimentions_enabled) {
                    this.dimentions_enabled = true;
                }
                if (this.cookieData?.custom_statuses) {
                    this.custom_statuses = true;
                }
                if (this.cookieData?.threshold_enabled) {
                    this.threshold_enabled = true;
                }
                if (this.cookieData?.sku_barcode_enabled) {
                    this.sku_barcode_enabled = true;
                }

                [
                    "min_quantity_enabled",
                    "dimentions_enabled",
                    "custom_statuses",
                    "threshold_enabled",
                ].forEach((key) => {
                    if (this.cookieData[key]) {
                        this.advanced = true;
                    }
                });
            }

            if (
                this.threshold_enabled ||
                this.custom_statuses ||
                this.dimentions_enabled ||
                this.product?.continue_selling ||
                (this.product.type === "simple" &&
                    this.product.variants[0].minimum)
            ) {
                this.advanced = true;
            }
        },
    },
    watch: {
        threshold_enabled(val) {
            if (!val) {
                this.product.threshold = null;
            }
            this.cookieBody.threshold_enabled = val;
        },
        min_quantity_enabled(value) {
            if (!value) {
                this.product.variants[0].minimum = 1;
            }
            this.cookieBody.min_quantity_enabled = value;
        },
        dimentions_enabled(value) {
            if (!value) {
                this.backup_dimentions = {
                    height: this.product.meta.height,
                    depth: this.product.meta.depth,
                    width: this.product.meta.width,
                };
                this.product.meta.height = 0;
                this.product.meta.depth = 0;
                this.product.meta.width = 0;
            }
            this.cookieBody.dimentions_enabled = value;
        },
        custom_statuses(value) {
            this.cookieBody.custom_statuses = value;
        },
        sku_barcode_enabled(value) {
            this.cookieBody.sku_barcode_enabled = value;
        },
        "product.variants.0.minimum"(value) {
            if (value) {
                this.min_quantity_enabled = true;
            }
        },
        "product.tracking"(value) {
            if (!value) {
                this.product.continue_selling = 0;
                this.product.threshold = null;
                this.threshold_enabled = false;
                this.product.threshold = null;
            }
        },
        responseErrors: {
            deep: true,
            handler(value) {
                let errorKeys = [
                    "variant.minimum",
                    "minimum",
                    "status_id",
                    "out_of_stock_id",
                    "height",
                    "depth",
                    "width",
                ];
                Object.keys(value).forEach((key) => {
                    if (errorKeys.includes(key)) {
                        this.advanced = true;
                    }
                });
                if (value?.height || value?.depth || value?.width) {
                    this.dimentions_enabled = true;
                }
                if (value?.threshold) {
                    this.threshold_enabled = true;
                }
                if (value?.status_id || value?.out_of_stock_id) {
                    this.custom_statuses = true;
                }
                if (value?.minimum || value?.["variant.minimum"]) {
                    this.min_quantity_enabled = true;
                }
            },
        },
    },
    async mounted() {
        await this.setOpenAdvanced();
    },
};
</script>
<style lang="scss" scoped>
@use "./../../../scss/style.scss" as *;
</style>
