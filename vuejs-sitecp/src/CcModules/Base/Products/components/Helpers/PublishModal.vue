<template>
    <b-modal
        v-model="modal"
        :no-header="true"
        :no-footer="true"
        centered
        @close="discardAndClose"
    >
        <template v-if="modal && product">
            <b-row>
                <b-col :cols="12" class="mb-3">
                    <span class="label-500-16px">
                        {{ translations["Save and publish later"] }}
                    </span>
                </b-col>
                <b-col :cols="6" class="d-flex align-items-center">
                    <ActiveSwitch
                        v-model:is-active="publish_date"
                        :label-text="translations['Publish on specific date']"
                        :true-value="true"
                        :false-value="false"
                        :reverse="true"
                    />
                </b-col>
                <b-col :cols="6">
                    <TimePeriod
                        v-model:start-date="publish_date_value"
                        :single="true"
                        :error="responseErrors['publish_date']"
                        :time="true"
                        :disabled="!publish_date"
                    />
                        <!-- :format="
                            this.serverSettings('format.dateTime').split('\\').join('')
                        " -->
                </b-col>
                <b-col :cols="6" class="d-flex align-items-center">
                    <ActiveSwitch
                        v-model:is-active="active_to"
                        :label-text="translations['Active till']"
                        :true-value="true"
                        :false-value="false"
                        :reverse="true"
                    />
                </b-col>
                <b-col :cols="6">
                    <TimePeriod
                        v-model:start-date="active_to_value"
                        :single="true"
                        :error="responseErrors['active_to']"
                        :time="true"
                        :disabled="!active_to"
                    />
                        <!-- :format="
                            this.serverSettings('format.dateTime').split('\\').join('')
                        " -->
                </b-col>
            </b-row>

            <b-row>
                <b-col>
                    <hr style="margin: 16px 0" />
                </b-col>
                <b-col class="col-12 d-flex align-items-center gap-2 justify-content-end">
                    <button class="btn btn-white" @click="discardAndClose">
                        {{ translations.Cancel }}
                    </button>
                    <button class="btn btn-primary" @click="submit">
                        {{ translations.Save }}
                    </button>
                </b-col>
            </b-row>
        </template>
    </b-modal>
</template>

<script>
import ActiveSwitch from "@components/Form/ActiveSwitch";
import TimePeriod from "@components/Form/TimePeriod";

export default {
    name: "PublishModal",
    components: {
        ActiveSwitch,
        TimePeriod,
    },
    props: {
        modelValue: {
            required: false,
            type: Boolean,
            default: false,
        },
        product: {
            required: false,
            type: Object,
            default: {},
        },
        saveAction: {
            required: true,
            type: Function,
            default: {},
        },
        responseErrors: {
            required: false,
            type: Object,
            default: {},
        },
    },
    data() {
        return {
            modal: this.modelValue,
            backup: {},
            publish_date: false,
            active_to: false,
            publish_date_value: null,
            active_to_value: null,

            translations: {
                Cancel: this.$t("Cancel"),
                Save: this.$t("Save"),
                "Save and publish later": this.$t("Save and publish later"),
                "Publish on specific date": this.$t("Publish on specific date"),
                "Active till": this.$t("Active till"),

                "Field is required when active till is enabled": this.$t(
                    "Field is required when active till is enabled"
                ),
                "Field is required when publish on specific date is enabled": this.$t(
                    "Field is required when publish on specific date is enabled"
                ),
            },
        };
    },
    methods: {
        discardAndClose() {
            // this.$emit("update:product", this.backup);
            this.active_to_value = null;
            this.publish_date_value = null;
            delete this.responseErrors.active_to;
            delete this.responseErrors.publish_date;

            this.modal = false;
        },
        submit() {
            if (this.active_to) {
                this.product.active_to = this.active_to_value;
            } else {
                this.product.active_to = null;
                this.active_to = false;
            }

            if (this.publish_date) {
                this.product.publish_date = this.publish_date_value;
            } else {
                this.product.publish_date = null;
                this.publish_date = false;
            }
            this.modal = false;
            this.saveAction();
        },
    },
    watch: {
        modelValue(val) {
            if (val) {
                // this.backup = JSON.parse(JSON.stringify(this.product));
                this.active_to_value = this.product.active_to || null;
                this.publish_date_value = this.product.publish_date || null;

                this.active_to = !!this.product.active_to;
                this.publish_date = !!this.product.publish_date;
            }
            this.modal = val;
        },
        modal(val) {
            this.$emit("update:modelValue", val);

            if (!val) {
                this.active_to_value = null;
                this.publish_date_value = null;
                delete this.responseErrors.active_to;
                delete this.responseErrors.publish_date;
            }
        },
        active_to(val) {
            if (!val) {
                this.active_to_value = null;
            }
        },
        publish_date(val) {
            if (!val) {
                this.publish_date_value = null;
            }
        },
        responseErrors: {
            deep: true,
            handler(val) {
                if (val?.active_to || val?.publish_date) {
                    console.log(val);
                    this.modal = true;
                }
            },
        },
    },
    emits: ["update:modelValue", "update:product"],
};
</script>
