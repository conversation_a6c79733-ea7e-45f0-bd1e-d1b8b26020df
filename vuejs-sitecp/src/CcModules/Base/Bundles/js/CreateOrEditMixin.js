import moment from "moment";
import { toast } from "@js/toast";

import Bundles from "./Bundles.js";

import { buildFormData } from "@js/shippingHelpers.js";

export default {
    data() {
        return {
            dataLoading: true,
            submitLoader: false,

            id: this.$route.params.id,
            model: new Bundles(),
            boxes: [],
            responseErrors: {},

            translations: {
                "Generate Invoice": this.$t("Generate Invoice"),
                "Automated by system": this.$t("Automated by system"),
                "Manual by admin": this.$t("Manual by admin"),
                "External system": this.$t("External system"),
                "Issue an invoice only if a billing address is selected": this.$t(
                    "Issue an invoice only if a billing address is selected"
                ),
                "Products per row": this.$t("Products per row"),
                "Show timer in products listing": this.$t(
                    "Show timer in products listing"
                ),
                "Show timer in product details page": this.$t(
                    "Show timer in product details page"
                ),
                "Mark as new": this.$t("Mark as new"),
                "Mark as featured": this.$t("Mark as featured"),
                "Successfully created": this.$t("Successfully created"),
                "Successfully updated": this.$t("Successfully updated"),

                "The maximum allowed characters for 'description' are 250000": this.$t(
                    "The maximum allowed characters for 'description' are 250000"
                ),
                "The maximum allowed characters for 'name' are 191": this.$t(
                    "The maximum allowed characters for 'name' are 191"
                ),
                "Product name is required": this.$t("Product name is required"),
                "Please choose at least two products to be added into the bundle": this.$t(
                    "Please choose at least two products to be added into the bundle"
                ),
                "Field is required": this.$t("Field is required"),
                "Category does not exist": this.$t("Category does not exist"),
                'Field may not be greater than {max} characters': this.$t('Field may not be greater than {max} characters'),
                'Products are required': this.$t('Products are required'),
                'Field is invalid': this.$t('Field is invalid'),
                'Field must be a integer': this.$t('Field must be a integer'),
                'Field must be at least {min}': this.$t('Field must be at least {min}'),
                'Field must be a string': this.$t('Field must be a string'),
                'Field must be at least {min} characters': this.$t('Field must be at least {min} characters'),
                'Field accepted only true, false, 1, 0, "1", and "0"': this.$t('Field accepted only true, false, 1, 0, "1", and "0"'),
                'Field must be a number': this.$t('Field must be a number'),
            },

            activeTill: true,
            logo: null,
            seo_description: "",
            payload: {},
        };
    },
    computed: {
        loading() {
            return this.dataLoading;
        },
    },
    created() {
        this.setupBoxes();
    },
    beforeMount() {
        this.settings = {
            name: "",
            description: "",
            publish_date: "",
            active_to: "",
            category_id: null,
            hidden: 0,
            new: 0,
            featured: 0,
            bundle_products: [],
            meta: {
                show_image: 0,
                timer_list: 0,
                timer_details: 0,
            },
            variant: {
                type: "price",
                price: 0,
                percent: 0,
            },
            per_row: 3,
            seo_description: "",
            url_handle: "",
            seo_title: "",
        };
    },
    async mounted() {
        if (this.id) {
            await this.getData();
        } else {
            this.dataLoading = false;
        }
    },
    emits: ["update:planFeatureModal"],
    methods: {
        handleDiscardChanges(original) {
            if(this.settings.image !== original.image) {
                this.settings = {...original, image: null, has_image: false};
            }else {
                this.settings = _.cloneDeep(original);
            }
        },
        async getData() {
            try {
                await this.getBundle(this.id);

                if (this.settings.publish_date) {
                    this.settings.publish_date = moment(
                        this.settings.publish_date
                    ).format(this.serverSettings("format.dateTime"));
                }
                if (this.settings.active_to) {
                    this.settings.active_to = moment(this.settings.active_to).format(
                        this.serverSettings("format.dateTime")
                    );
                    this.activeTill = true;
                }

                if (this.settings.has_image) {
                    this.logo = this.settings.image;
                }
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.dataLoading = false;
            }
        },

        async saveBundle() {
            this.submitLoader = true;
            this.responseErrors = {};
            
            if(!this.isAllowedCreateNewBundle) {
                this.$emit('update:planFeatureModal', true)
                this.submitLoader = false;
                return;
            }
            
            try {
                this.payload = _.cloneDeep(this.settings);

                if (!this.activeTill) {
                    this.payload.active_to = null;
                }

                ["hidden", "new", "featured", "active", "draft"].forEach((key) => {
                    this.payload[key] = this.payload[key] ? 1 : 0;
                });

                if (this.payload.bundle_products.length > 0) {
                    this.payload.bundle_products.forEach((element, index) => {
                        element.sort_order = index;
                        [
                            "optional",
                            "individual_price_enabled",
                            "visible_product_details",
                            "visible_cart",
                            "visible_order_details",
                            "price_visible_product_details",
                            "price_visible_cart",
                            "price_visible_order_details",
                            "hide_thumb",
                            "override_title",
                            "override_short_description",
                            "individual_qty_enabled",
                        ].forEach((key) => {
                            element[key] = element[key] ? 1 : 0;
                        });
                    });
                }

                if (!this.settings.seo_title) {
                    this.payload.seo_title = this.settings.name;
                }
                if (!this.settings.url_handle) {
                    this.payload.url_handle = this.settings.name;
                }
                if (!this.settings.seo_description) {
                    this.payload.seo_description = this.seo_description;
                }

                if (this.logo instanceof File) {
                    this.payload.image = this.logo;
                }
                if(this.settings?.variant?.type === 'percent') {
                    delete this.payload.variant.price;
                }
                if(this.settings?.variant?.type === 'price') {
                    delete this.payload.variant.percent;
                }

                const formData = new FormData();
                buildFormData(formData, this.payload);

                const headers = {
                    headers: {
                        "Content-Type": "multipart/form-data",
                    },
                };

                if (!this.id) {
                    await this.model.create(formData, headers);
                } else {
                    await this.model.post(this.id, formData, headers);
                }

                setTimeout(() => {
                    this.$router.push({ name: "bundles-list" });
                }, 2500);
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.submitLoader = false;
            }
        },
        setupBoxes() {
            this.boxes = [
                {
                    key: "details",
                    group: "bundles",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Bundle details",
                    infoTitleHelp: null,
                    infoDescription: `<strong>Allowed image extensions</strong> - jpeg, jpg, jpe, gif, png, svg, webp. The maximum allowed image size is - 10 MB.<br/><br/> <strong>Display photo in detailed page</strong> - if this option is on, the photo will appear as hero image in the Bundle detailed page.`,
                    editMethod: "inline",
                    isVisible: true,
                    hr: "hide",
                    fields: [
                        {
                            key: "name",
                            type: "string",
                            inputType: "string",
                            label: "Name",
                            multiLine: true,
                            inputSize: 12,
                            placeholder:
                                "E.g. Short sleeve polo shirt"
                            ,
                        },
                        {
                            type: "slot",
                            slotName: "shortDescriptionAndImage",
                        },
                        {
                            type: "slot",
                            slotName: "details",
                        },
                        {
                            key: "meta.show_image",
                            type: "switch",
                            label: "Display photo in detailed page",
                        },
                        {
                            type: "line",
                            style: "margin: 12px 0;",
                        },
                        {
                            key: "new",
                            type: "switch",
                            label: "Mark as new",
                        },
                        {
                            key: "featured",
                            type: "switch",
                            label: "Mark as featured",
                        },
                        {
                            key: "hidden",
                            type: "switch",
                            label: "Hide from store",
                        },
                    ],
                },
                {
                    key: "category",
                    group: "bundles",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Show in category",
                    infoTitleHelp: null,
                    infoDescription:
                        "You can choose a category where you want the bundle to be displayed. If you prefer not to assign the bundle to a category, simply leave the field blank. This way, the bundle will remain uncategorized but still accessible to those who need it.",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            key: "category_id",
                            type: "select",
                            label: "Category",
                            inputType: "select",
                            optionsFromSettings: null,
                            disableTranslatableOptions: true,
                            url: '/admin/api/core/product-categories/search',
                            searchable: true,
                            requestOnSearch: true,
                            resolveOnLoad: true,
                            canClear: true,
                        },
                    ],
                },
                {
                    key: "products",
                    group: "bundles",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Select products",
                    infoTitleHelp: null,
                    infoDescription:
                        "Nec faucibus volutpat urna senectus gravida ullamcorper augue. Donec ultricies id cursus nunc quis adipiscing mattis lorem. Tellus nunc urna elit imperdiet ut tempus a pulvinar sed",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            type: "slot",
                            slotName: "products",
                        },
                        {
                            type: "line",
                            style: "margin:12px 0",
                        },
                        {
                            key: "per_row",
                            type: "select",
                            label: "Products per row",
                            inputType: "select",
                            options: [
                                { id: 1, name: 1 },
                                { id: 2, name: 2 },
                                { id: 3, name: 3 },
                                { id: 4, name: 4 },
                                { id: 5, name: 5 },
                                { id: 6, name: 6 },
                            ],
                            optionsFromSettings: null,
                            url: null,
                        },
                    ],
                },
                {
                    key: "date_rage",
                    group: "bundles",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Date range",
                    infoTitleHelp: null,
                    infoDescription:
                        "Specify the start date and the end date of the discount, or set no expiration.",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            type: "slot",
                            slotName: "dateRange",
                        },
                        // {
                        //     type: "line",
                        //     style: "margin: 6px 0;",
                        // },
                        // {
                        //     key: "meta.timer_list",
                        //     type: "switch",
                        //     label: "Show timer in products listing",
                        //     reverse: true,
                        // },
                        // {
                        //     key: "meta.timer_details",
                        //     type: "switch",
                        //     label:
                        //         "Show timer in product details page"
                        //     ,
                        //     reverse: true,
                        // },
                    ],
                },
                {
                    key: "seo",
                    group: "bundles",
                    title: null,
                    titleHelp: null,
                    infoTitle: "Advanced SEO settings",
                    infoTitleHelp: null,
                    infoDescription: "Seo title and meta description",
                    editMethod: "inline",
                    isVisible: true,
                    fields: [
                        {
                            key: "seo_title",
                            type: "string",
                            inputType: "string",
                            label: "SEO page title",
                            multiLine: true,
                            inputSize: 12,
                        },
                        {
                            key: "url_handle",
                            type: "string",
                            inputType: "string",
                            label: "URL",
                            multiLine: true,
                            inputSize: 12,
                            colored: true,
                            unit: `${this.serverSettings("host")}/product/`,
                            unitPosition: "left",
                        },
                        {
                            key: "seo_description",
                            type: "text",
                            inputType: "text",
                            label: "SEO meta description",
                        },
                        {
                            type: "slot",
                            slotName: "seoPreview",
                        },
                    ],
                },
            ];
        },
    },
}
