import axios from 'axios'
import { computed, ref } from 'vue'
import { createSharedComposable } from '@vueuse/core'

const useSharedState = createSharedComposable(() => {
    const bundle = ref({});
    const meta = ref({});
    const feature = ref({});

    const metaLoading = computed(() => {
        return !Object.keys(meta.value).length;
    });

    const getMeta = async () => {
        try {
            const { data } = await axios.get(`/admin/api/bundles/meta`);

            let temp = (data?.features || []).filter((x) => x.mapping === "bundles");

            if (temp && temp[0]) {
                feature.value = temp[0];
            }
            
        } catch (error) {
            console.log(error);
        }
    };

    const getBundle = async (id) => {
        const { data } = await axios.get(`/admin/api/bundles/${id}`);
        bundle.value = data;
    };

    return {
        bundle,
        meta,
        metaLoading,
        getBundle,
        feature,
        getMeta
    };
});

export default useSharedState;