<template>
    <div class="mt-4" id="bundle-list">
        <data-table
            v-bind="table"
            v-model="ids"
            @pagination-change-page="paginate"
            :enable-mobile="true"
            @load-all="(val) => (loadAll = val)"
            :filters="true"
            app-name="bundle"
            :get-data="getData"
            :filter-options="filterOptions"
            v-model:query="query"
        >
            <template #noResult>
                <b-button
                    variant="primary"
                    class="text-nowrap"
                    @click="handleCreate"
                >
                    <i class="far fa-plus"></i>
                    {{ translations["Add bundle"] }}
                </b-button>
            </template>
            <TableActions
                v-model="ids"
                app-key="bundle"
                :actions="actions"
                @success="handleActions"
                :ignoreDefault="true"
                :enable-mobile="true"
            />
        </data-table>
    </div>
</template>

<script>
import Bundles from "../../js/Bundles";
import { markRaw } from "vue";
import axios from 'axios';
import DataTable from "@components/Table";
import TableActions from "@components/TableActions";
import RemoveProduct from "../Table/RemoveProduct.vue";
import Name from "./../Table/Name";
import Price from "./../Table/Price";
import RowActions from "./../Table/RowActions.vue";
import FormatDate from "./../Table/FormatDate";
import ExpandedRow from "./../Table/ExpandedRow";
import { toast } from "@js/toast";
import useSharedState from "../../composables/useSharedState";

export default {
    components: {
        DataTable,
        TableActions,
        RemoveProduct
    },
    setup() {
        const { meta, metaLoading } = useSharedState();

        return {
            meta,
            metaLoading,
        };
    },
    props: {
        handleCreate: {
            type: Function,
            default: () => {},
        },
        planFeatureModal: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            dataLoading: true,
            dataRow: null,
            ids: [],
            loadAll: false,
            paginationShowDisabled: false,
            total: 0,
            page: this.$route.query?.page || 1,
            perPage: this.$route.query?.perpage || 25,
            query: this.$route.query,
            model: new Bundles(),
            translations: {
                "Add bundle": this.$t("Add bundle"),
                "Bundle name": this.$t("Bundle name"),
                "Active till": this.$t("Active till"),
                "Active from": this.$t("Active from"),
                "Added": this.$t("Added"),
                "Delete": this.$t("Delete"),
                "Publish": this.$t("Publish"),
                "Unpublish": this.$t("Unpublish"),
                "Bundle price": this.$t("Bundle price"),
                Yes: this.$t("Yes"),
                No: this.$t("No"),
                "Deleted successully": this.$t("Deleted successully"),
                "Error while deleting": this.$t("Error while deleting"),
                "Are you are sure you want to delete? Caution: This action cannot be undone.": this.$t(
                    "Are you are sure you want to delete? Caution: This action cannot be undone."
                ),
                "Published successfully": this.$t("Published successfully"),
                "Unpublished successfully": this.$t("Unpublished successfully"),
                "Error occured while publishing": this.$t(
                    "Error occured while publishing"
                ),
                "Error occured while unpublishing": this.$t(
                    "Error occured while unpublishing"
                ),
                Duplicate: this.$t("Duplicate"),
                "Duplicated successfully": this.$t("Duplicated successfully"),
                "Error occured while duplicating": this.$t(
                    "Error occured while duplicating"
                ),

                // Filters
                Price: this.$t("Price"),
                Published: this.$t("Published"),
                Product: this.$t("Product"),
                New: this.$t("New"),
                Featured: this.$t("Featured"),
                Draft: this.$t("Draft"),
                Exactly: this.$t("Exactly"),
                "Not equal to": this.$t("Not equal to"),
                "More than": this.$t("More than"),
                "Less than": this.$t("Less than"),
                Includes: this.$t("Includes"),
                "Does not include": this.$t("Does not include"),
                Category: this.$t("Category"),
                "ID #": this.$t("ID #"),
                Date: this.$t("Date"),
                exactly: this.$t("exactly"),
                before: this.$t("before"),
                after: this.$t("after"),
                between: this.$t("between"),
                Actions: this.$t("Actions"),
            },
            data: {
                data: [],
            },
        };
    },
    computed: {
        loading() {
            return this.dataLoading;
        },
        filterOptions() {
            return [
                {
                    key: "price",
                    label: this.translations["Price"],
                    type: "number",
                    options: [
                        { value: "is", label: this.translations["Exactly"] },
                        { value: "is_not", label: this.translations["Not equal to"] },
                        { value: "gt", label: this.translations["More than"] },
                        { value: "lte", label: this.translations["Less than"] },
                    ],
                },
                {
                    key: "product",
                    label: this.translations["Product"],
                    url: "/admin/api/core/products/search",
                    options: [
                        { value: "in", label: this.translations["Includes"] },
                        { value: "not_in", label: this.translations["Does not include"] },
                    ],
                    type: "select",
                    multiple: true,
                },
                {
                    key: "category",
                    label: this.translations["Category"],
                    url: "/admin/api/core/product-categories/search",
                    options: [
                        { value: "in", label: this.translations["Includes"] },
                        { value: "not_in", label: this.translations["Does not include"] },
                    ],
                    type: "select",
                    multiple: true,
                },
                {
                    key: "publish_date",
                    label: this.translations["Active from"],
                    type: "date",
                    options: [
                        { value: 'is', label: this.translations["exactly"] },
                        { value: 'lt', label: this.translations["before"] },
                        { value: 'gt', label: this.translations["after"] },
                        { value: 'between', label: this.translations["between"] },
                    ],
                },
                {
                    key: "active_to",
                    label: this.translations["Active till"],
                    type: "date",
                    options: [
                        { value: "is", label: this.translations["exactly"] },
                        { value: "lt", label: this.translations["before"] },
                        { value: "gt", label: this.translations["after"] },
                        { value: "between", label: this.translations["between"] },
                    ],
                },
                {
                    key: "active",
                    label: this.translations["Published"],
                    input: false,
                    options: [
                        { value: "1", label: this.translations["Yes"] },
                        { value: "0", label: this.translations["No"] },
                    ],
                },
                {
                    key: "new",
                    label: this.translations["New"],
                    input: false,
                    options: [
                        { value: "1", label: this.translations["Yes"] },
                        { value: "0", label: this.translations["No"] },
                    ],
                },
                {
                    key: "featured",
                    label: this.translations["Featured"],
                    input: false,
                    options: [
                        { value: "1", label: this.translations["Yes"] },
                        { value: "0", label: this.translations["No"] },
                    ],
                },
                {
                    key: "draft",
                    label: this.translations["Draft"],
                    input: false,
                    options: [
                        { value: "1", label: this.translations["Yes"] },
                        { value: "0", label: this.translations["No"] },
                    ],
                },
            ];
        },
        table() {
            return {
                data: this.data,
                meta: this.meta,
                paginationShowDisabled: this.paginationShowDisabled,
                isLoading: this.loading,
                defaultSorting: [{ key: "id", sortingMode: "desc" }],
                expandedRowComponent: markRaw(ExpandedRow),
                // sortingMode: 'multiple',
                columns: [
                    {
                        column: "id",
                        key: "id",
                        sortable: true,
                        title: this.translations["ID #"],
                        showExpandToggle: true,
                    },
                    {
                        column: "name",
                        key: "name",
                        sortable: true,
                        title: this.translations["Bundle name"],
                        isMain: true,
                        component: markRaw(Name),
                    },
                    {
                        column: "date_added",
                        key: "date_added",
                        sortable: true,
                        title: this.translations["Added"],
                        component: markRaw(FormatDate),
                    },
                    {
                        column: "publish_date",
                        key: "publish_date",
                        sortable: true,
                        title: this.translations["Active from"],
                        component: markRaw(FormatDate),
                    },
                    {
                        column: "active_to",
                        key: "active_to",
                        sortable: true,
                        title: this.translations["Active till"],
                        component: markRaw(FormatDate),
                    },
                    {
                        column: "price",
                        key: "price",
                        sortable: true,
                        title: this.translations["Bundle price"],
                        component: markRaw(Price),
                    },
                    {
                        column: "actions",
                        key: "actions",
                        sortable: false,
                        type: 'number',
                        title: this.translations["Actions"],
                        deleteRow: this.deleteRow,
                        component: markRaw(RowActions),
                    },
                    // {
                    //     column: "actions",
                    //     key: "actions",
                    //     sortable: false,
                    //     title: " ",
                    //     component: markRaw(RemoveProduct),
                    //     deleteRow: this.deleteRow,
                    // },
                ],
            };
        },
        actions() {
            return [
                {
                    label: this.translations["Publish"],
                    icon: "far fa-arrow-circle-up",
                    action: "publish",
                    type: "patch",
                    confirm: false,
                    messageSuccess: this.translations["Published successfully"],
                    messageError: this.translations["Error occured while publishing"],
                    url: `/admin/api/bundles/active/1`,
                },
                {
                    label: this.translations["Unpublish"],
                    icon: "far fa-arrow-circle-down",
                    action: "unpublish",
                    type: "patch",
                    confirm: false,
                    messageSuccess: this.translations["Unpublished successfully"],
                    messageError: this.translations["Error occured while unpublishing"],
                    url: `/admin/api/bundles/active/0`,
                },
                {
                    label: this.translations["Duplicate"],
                    icon: "far fa-plus-circle",
                    action: "duplicate",
                    type: "post",
                    confirm: false,
                    messageSuccess: this.translations["Duplicated successfully"],
                    messageError: this.translations["Error occured while duplicating"],
                    url: `/admin/api/bundles/duplicate`,
                },
                {
                    label: this.translations["Delete"],
                    icon: "fal fa-trash-alt",
                    action: "delete",
                    type: "delete",
                    confirm: {
                        messageConfirm: this.translations[
                            "Are you are sure you want to delete? Caution: This action cannot be undone."
                        ],
                    },
                    messageSuccess: this.translations["Deleted successfully"],
                    messageError: this.translations["Error while deleting"],
                    url: `/admin/api/bundles`,
                },
            ];
        },
    },
    methods: {
        async deleteRow(data) {
            data.loader = true;
            try {
                await axios.delete("/admin/api/bundles", {
                    data: {
                        ids: [data.id],
                    },
                });
                toast.success("Product deleted successfully");
                this.onDeleteRow(data.id);
            } catch (err) {
                // toast.error("Error occured while deleting product");
                this.$errorResponse(err);
            } finally {
                data.loader = true;
            }
        },
        async paginate(page) {
            this.page = page;
            await this.getData("paginate");
        },
        async sorting(columns) {
            await this.sortingTable(columns, this.getData);
        },
        async getData(paginate) {
            try {
                this.dataLoading = true;

                this.model.where(this.query);

                if (Object.keys(this.query).toString().includes("filters") && !paginate) {
                    this.page = 1;
                }

                this.data = await this.model.paginate(
                    this.query?.page || this.page,
                    this.query?.perpage || this.perPage
                );

                this.total = this.data.total;

                this.data.data = this.data.data.map((x) => {
                    return {
                        ...x,
                        onDeleteRow: this.onDeleteRow,
                        onUpdateRow: this.onUpdateRow,
                    };
                });
                this.$router.push({
                    query: {
                        ...this.$route.query,
                        page: this.table.data.current_page,
                    },
                });
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.dataLoading = false;
            }
        },
        onUpdateRow(id, data) {
            const index = this.data.data.findIndex((x) => x.id === id);
            if (index !== -1) {
                this.data.data[index] = {
                    ...this.data.data[index],
                    ...data,
                };
            }
        },
        onDeleteRow(id) {
            this.data.data = this.data.data.filter((x) => x.id !== id);

            document.querySelectorAll(".tooltip").forEach((tooltip) => {
                tooltip.parentNode.removeChild(tooltip);
            });
        },
        handleActions(value) {
            if (value.action === "delete") {
                this.data.data = this.data.data.filter((x) => !this.ids.includes(x.id));
                if (this.data.data.length === 0 && this.page > 1) {
                    this.page--;
                    this.getData();
                } else if (
                    this.data.data.length === 0 &&
                    this.page === 1 &&
                    this.data.next_page_url
                ) {
                    this.getData();
                }
            } else if (value.action === "publish") {
                this.data.data.forEach((x) => {
                    if (this.ids.includes(x.id)) {
                        x.active = 1;
                    }
                });
            } else if (value.action === "unpublish") {
                this.data.data.forEach((x) => {
                    if (this.ids.includes(x.id)) {
                        x.active = 0;
                    }
                });
            } else if (value.action === "duplicate") {
                this.getData();
            }
            this.ids = [];
        },
    },
    watch: {
        loadAll(val) {
            if (val) {
                this.paginationShowDisabled = true;
                this.getData();
            } else {
                this.paginationShowDisabled = false;
                this.getData();
            }
        },
    },
};
</script>
<style lang="scss">
#bundle-list {
    // TODO
    // .col-options_count {
    //     .column-content {
    //         span {
    //             white-space: pre-wrap !important;
    //         }
    //     }
    // }
    // .col-products_count,
    // .col-categories_ids_count,
    // .col-options_count {
    //     padding: 16px 10px !important;
    // }
}
</style>
