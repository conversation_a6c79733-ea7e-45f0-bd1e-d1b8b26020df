<template>
    <div v-if="loading" class="w-100vh h-100 relative">
        <Loading :loading="loading"  class="app-loader-center" />
    </div>
    <template v-else>
        <SettingsBox
            v-model:settings="settings"
            v-model:boxes="boxes"
            box-key="bundles"
            :response-errors="responseErrors"
            :translation-labels="translations"
        >
            <template #details>
                <ShortDescriptionAndLogo
                    v-model="settings"
                    v-model:logo="logo"
                    v-model:seo_description="seo_description"
                    :responseErrors="responseErrors"
                />
            </template>
            <template #dateRange>
                <TimePeriod
                    v-model:start-date="settings.publish_date"
                    v-model:end-date="settings.active_to"
                    @update:end-date="handleClearTimerSwitches"
                    v-model:active-till="activeTill"
                    :format="this.serverSettings('format.dateTime').split('\\').join('')"
                    :reverse="true"
                    :time="true"
                    :error="responseErrors['active_to']"
                    custom-class="correct-width-size-edit"
                />
                <hr class="m-0"/>
                <ActiveSwitch
                    v-model:is-active="meta.timer_list"
                    :label-text="translations['Show timer in products listing']"
                    :reverse="true"
                    :is-disabled="!settings.active_to"
                />
                <ActiveSwitch
                    v-model:is-active="meta.timer_details"
                    :label-text="translations['Show timer in product details page']"
                    :reverse="true"
                    :is-disabled="!settings.active_to"
                />
            </template>
            <template #products>
                <Products v-model="settings" :responseErrors="responseErrors" />
            </template>
            <template #seoPreview>
                <SeoGooglePreview
                    :label-text="translations['Google preview']"
                    :title="settings.seo_title"
                    :description="settings.seo_description"
                    :url="`${serverSettings('host')}/product/${settings.url_handle}`"
                    :no-margin="true"
                />
            </template>
        </SettingsBox>

        <SubmitChanges
            v-if="!loading"
            :model-value="settings"
            @update:model-value="handleDiscardChanges"
            v-model:response-errors="responseErrors"
            :submit-loader="submitLoader"
            :disable-save="submitLoader"
            :save-func="saveBundle"
        />
    </template>
</template>

<script>
import _ from "lodash";

import CreateOrEditMixin from "../../js/CreateOrEditMixin";
import useSharedState from "../../composables/useSharedState";

import Loading from "@components/Loading";
import TimePeriod from "@components/Form/TimePeriod";
import SeoGooglePreview from "@components/SeoGooglePreview";

import SubmitChanges from "@components/SubmitChanges";
import SettingsBox from "@components/SettingsBox/SettingsBox";
import ActiveSwitch from "@components/Form/ActiveSwitch";

import ShortDescriptionAndLogo from "../SettingRows/ShortDescriptionAndLogo";
import Products from "../SettingRows/Products";

export default {
    mixins: [CreateOrEditMixin],
    components: {
        Loading,
        SettingsBox,
        ShortDescriptionAndLogo,
        TimePeriod,
        Products,
        SubmitChanges,
        SeoGooglePreview,
        ActiveSwitch,
    },
    props: {
        handleCreate: {
            type: Function,
            default: () => {},
        },
        planFeatureModal: {
            type: Boolean,
            default: false,
        },
    },
    setup() {
        const {
            bundle: settings,
            meta,
            metaLoading,
            getBundle,
            feature
        } = useSharedState();

        return {
            settings,
            meta,
            metaLoading,
            getBundle,
            feature
        };
    },
    methods:{
        handleClearTimerSwitches(value){
            if(!value){
                this.meta.timer_list = 0;
                this.meta.timer_details = 0;
            }
        }
    },
    computed: {
        isAllowedCreateNewBundle() {
            if (
                [null, true].includes(this.feature?.current) ||
                this.feature?.current > this?.feature?.used
            ) {
                return true;
            }
            return false;
        },
    },
};
</script>
