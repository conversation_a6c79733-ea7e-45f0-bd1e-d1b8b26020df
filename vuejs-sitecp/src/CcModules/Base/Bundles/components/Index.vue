<template>
    <Application
        v-model="app"
        app-key="bundles"
        :has-active-switch="false"
        :support-config="false"
        :card="false"
        :hasBreadcrumb="false"
        @on-install="handleAfterInstall"
        :top-padding="false"
    >
        <SettingsWrapper
            setting-key="bundles"
            :loading="false"
            :prevent-save="true"
            icon="fas fa-gifts"
            :title="translations['Product bundles']"
            :description="translations['Product bundles description']"
            :breadcrumb="[]"
            :tabs="tabs"
            :container-classes="
                $route.name === 'bundles-list' ? 'container-large' : 'container-medium'
            "
        >
            <template #headerButton v-if="$route.name === 'bundles-list'">
                <b-button variant="primary" class="text-nowrap" @click="handleCreate">
                    <i class="far fa-plus"></i>
                    {{ translations["Add bundle"] }}
                </b-button>
            </template>

            <template #settings>
                <router-view :handle-create="handleCreate" v-model:planFeatureModal="planFeatureModal"></router-view>
            </template>
        </SettingsWrapper>
        <PlanFeature
            v-model="planFeatureModal"
            v-model:record="feature"
            type="plan_feature"
            @success="
                (result) => {
                    handleAfterPay(result);
                }
            "
        >
            <template #message>
                <div
                    class="d-flex flex-wrap w-100 info-box info-box-error align-items-center justify-content-between rounded-3 p-3 my-3"
                >
                    <p
                        class="m-0 d-flex align-items-center justify-content-center w-100 gap-3"
                    >
                        <i class="far fa-exclamation-circle fs-5"></i>
                        {{
                            translations[
                                "You have reached the maximum number of bundles allowed, you need to purchase more to continue."
                            ]
                        }}
                    </p>
                </div>
            </template>
        </PlanFeature>
    </Application>
</template>

<script>
import MetaMixin from "@mixins/MetaMixin";
import Bundles from "../js/Bundles";
import useSharedState from "../composables/useSharedState";

import Application from "./../../../Apps/components/Application";
import SettingsWrapper from "@components/SettingsWrapper/SettingsWrapper";
import { onMounted } from "vue";

export default {
    mixins: [MetaMixin],
    components: {
        SettingsWrapper,
        Application,
    },
    setup() {
        const { bundle: editBundle, meta, feature, getMeta } = useSharedState();

        onMounted(() => {
            getMeta();
        });

        return {
            editBundle,
            meta,
            feature,
            getMeta,
        };
    },
    data() {
        return {
            app: {},
            settings: {},
            model: new Bundles(),
            modal: false,
            tabName: null,
            planFeatureModal: false,
            translations: {
                "Product bundles": this.$t("Product bundles"),
                "Product bundles description": this.$t("Product bundles description"),
                Bundles: this.$t("Bundles"),
                "Add bundle": this.$t("Add bundle"),
                "Edit bundle": this.$t("Edit bundle"),
                "Deleted successully": this.$t("Deleted successully"),
                "Error while deleting": this.$t("Error while deleting"),
                Products: this.$t("Products"),
                "You have reached the maximum number of bundles allowed, you need to purchase more to continue.": this.$t(
                    "You have reached the maximum number of bundles allowed, you need to purchase more to continue."
                ),
            },
        };
    },
    computed: {
        breadcrumbs() {
            return [
                {
                    text: this.translations["Products"],
                },
                {
                    text: this.translations["Product bundles"],
                    to: { name: "bundle-list" },
                },
            ];
        },
        tabs() {
            return [
                {
                    to: { name: "bundles-list" },
                    label: this.translations["Bundles"],
                },
                ...(["bundles-add", "bundles-edit"].includes(this.$route.name)
                    ? [
                          {
                              to: this.tabLink,
                              label: this.tabLabel,
                          },
                      ]
                    : []),
            ];
        },
        tabLink() {
            if (this.$route.name === "bundles-edit") {
                return { name: "bundles-edit", params: { id: this.$route.params.id } };
            }
            return { name: "bundles-add" };
        },
        tabLabel() {
            if (this.$route.name === "bundles-edit") {
                return this.translations["Edit bundle"];
            }
            return this.translations["Add bundle"];
        },
        isAllowedCreateNewBundle() {
            if (
                [null, true].includes(this.feature?.current) ||
                this.feature?.current > this?.feature?.used
            ) {
                return true;
            }
            return false;
        },
    },
    methods: {
        handleAfterInstall() {
            setTimeout(() => {
                this.$router.push({ name: "bundles-list" });
            }, 100);
        },
        handleAfterPay(result) {
            Object.values(result?.status || []).forEach((item) => {
                this.feature = {
                    ...this.feature,
                    current: Number(this.feature.current) + Number(item.item.value),
                };
            });
        },
        handleCreate() {
            if (this.isAllowedCreateNewBundle) {
                this.$router.push({ name: "bundles-add" });
            } else {
                this.planFeatureModal = true;
            }
        },
    },
    watch: {
        app: {
            handler(value) {
                if(value.paid && value.is_installed && !['bundles-add', 'bundles-edit'].includes(this.$route.name)){
                    this.$router.push({ name: "bundles-list" });
                }
            },
            deep: true,
            immediate: true,
        },
    },
};
</script>
