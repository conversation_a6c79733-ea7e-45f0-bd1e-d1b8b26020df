<template>
    <div class="d-flex gap-3 align-items-center">
        <ImgWrapper
            :img="data.image || serverSettings('noImages.150x150')"
            :url="data.web"
            :tooltip="translations['View in store']"
            :shadow="true"
        />
        <router-link :to="{ name: 'bundles-edit', params: { id: data.id } }">
            <div class="cc-tooltip-dotted">{{ data[column.key] }}</div>
            <div v-if="reviews" class="d-flex gap-1 align-items-center">
                <StarRating
                    :rating="data.review.average"
                    :read-only="true"
                    :star-size="15"
                    :show-rating="false"
                />
                <span class="review-count-text">({{ reviewCount }})</span>
            </div>
        </router-link>
    </div>
</template>
<script>
import { computed } from "vue";
import useSharedAppsInfo from '../../../../Apps/composables/useSharedAppsInfo';

import StarRating from "vue-star-rating";
import ImgWrapper from "@components/Apps/Erp/ImgWrapper";

export default {
    name: "Name",
    components: {
        StarRating,
        ImgWrapper,
    },
    props: {
        data: {
            type: Object,
            required: false,
            default: {},
        },
        column: {
            type: Object,
            required: false,
            default: {},
        },
        meta: {
            type: Object,
            required: false,
            default: {},
        },
    },
    setup(){
        const { apps } = useSharedAppsInfo();

        const reviews = computed(()=>{
            const app = apps.value.find(
                (app) => app.mapping === "product_review"
            );
            return (app?.is_installed) || false
        })

        return {
            reviews,
        }
    },
    data() {
        return {
            translations: {
                Review: this.$t("Review"),
                Reviews: this.$t("Reviews"),
                "No reviews": this.$t("No reviews"),
                "View in store": this.$t("View in store"),
            },
        };
    },
    computed: {
        reviewCount() {
            return this.data.review.total
                ? `${this.data.review.total} ${
                      this.data.review.total === 1
                          ? this.translations["Review"]
                          : this.translations["Reviews"]
                  }`
                : this.translations["No reviews"];
        },
    },
};
</script>

<style lang="scss" scoped>
.review-count-text {
    color: var(--Color-Text-Body---cc-color-text-secondary, #7a7d84);
    font-size: 13px;
    line-height: 13px;
}
</style>
