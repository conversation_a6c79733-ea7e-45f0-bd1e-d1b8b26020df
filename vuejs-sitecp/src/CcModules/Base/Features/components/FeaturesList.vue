<template>
    <div class="position-relative w-100" :class="{ 'h-100': loading }">
        <Loading v-if="loading" :loading="loading" class="app-loader-center" />
        <BContainer class="container-medium pb-5" v-else>
            <SettingsHeader
                icon="fa-light fa-calendar-star"
                :title="translations['Plan feature']"
            >
            </SettingsHeader>

            <!-- <div
                class="info-box info-box-warning rounded border-0 p-3 my-3 d-flex flex-row align-items-center gap-2"
            >
                <figure class="m-0">
                    <svg
                        width="24"
                        height="21"
                        viewBox="0 0 24 21"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M10.6875 8.50781C10.6875 8.23438 10.8828 8 11.1562 8H13.3047C13.5781 8 13.7734 8.23438 13.7734 8.50781L13.5 12.8828C13.5 13.1172 13.2656 13.3125 13.0312 13.3125H11.4297C11.1562 13.3125 10.9609 13.1562 10.9609 12.8828L10.6875 8.50781ZM13.8906 15.5C13.8906 16.4375 13.1484 17.1406 12.25 17.1406C11.3125 17.1406 10.6094 16.4375 10.6094 15.5C10.6094 14.6016 11.3125 13.8594 12.25 13.8594C13.1484 13.8594 13.8906 14.6016 13.8906 15.5ZM13.8516 1.4375L23.2266 17.7266C23.9297 18.9766 23.0312 20.5 21.5859 20.5H2.875C1.42969 20.5 0.53125 18.9375 1.23438 17.7266L10.6094 1.4375C11.3125 0.1875 13.1484 0.226562 13.8516 1.4375ZM3.07031 18.3125C2.95312 18.4688 3.07031 18.625 3.26562 18.625H21.1953C21.3906 18.625 21.5078 18.4688 21.3906 18.3125L12.4453 2.76562C12.3281 2.60938 12.1328 2.60938 12.0156 2.76562L3.07031 18.3125Z"
                            fill="#FCC400"
                        />
                    </svg>
                </figure>
                <div
                    v-html="
                        $trp(
                            translations[
                                'You reached the limit of feature <b>{feature} - {limit}</b><br>To continue you should purchase a feature pack or upgrade to a plan with higher limits!'
                            ],
                            {
                                feature: feature?.name,
                                limit: limit,
                            }
                        )
                    "
                ></div>
            </div> -->
            <data-table
                v-if="table.data && !feature?.purchaseRestrictMessage"
                class="cc-table w-100"
                v-bind="this.table"
                :enable-mobile="true"
            >
                <template #noResult>
                    <b-button
                        variant="primary"
                        class="text-nowrap"
                        @click="planModal = true"
                    >
                        {{ translations["Upgrade plan"] }}
                    </b-button>
                </template>
            </data-table>
            <b-card v-else>
                <div class="info-box info-box-warning">
                    <p
                        v-html="
                            translations[
                                'This feature is not enabled for your plan. To access it, please upgrade your plan.'
                            ]
                        "
                    ></p>
                    <p
                        v-html="
                            $trp(
                                translations[
                                    'Plans that support this functionality are: <strong>{plan}</strong>'
                                ],
                                {
                                    plan: (
                                        feature?.purchaseRestrictMessage || []
                                    ).join(', '),
                                }
                            )
                        "
                    ></p>
                    <p class="text-center">
                        <button class="btn btn-primary">
                            {{ translations["View prices"] }}
                        </button>
                    </p>
                </div>
            </b-card>

            <Checkout
                v-model="buyPanel"
                v-model:record="packRecord"
                v-model:type="packType"
                button-text="Back to overview"
                @success="
                    (result) => {
                        this.$emit('success', result);
                    }
                "
            ></Checkout>
        </BContainer>
    </div>
</template>

<script>
import PlanFeature from "../js/PlanFeature";
import useSharedPlanPanelState from "@components/Checkout/js/useSharedPlanPanelState";

import { markRaw } from "vue";
import { Checkout } from "@components/Checkout";
import SettingsHeader from "@components/SettingsBox/Helpers/SettingsHeader";
import DataTable from "@components/Table";
import Loading from "@components/Loading";

import BuyButton from "../components/Helpers/BuyButton";

export default {
    name: "FeaturesList",
    components: {
        SettingsHeader,
        DataTable,
        Checkout,
        Loading,
    },
    setup() {
        const {
            openModal: planModal,
            successState,
            message,
        } = useSharedPlanPanelState();

        return {
            planModal,
            successState,
            message,
        };
    },
    data() {
        return {
            translations: {
                Close: this.$t("Close"),
                Feature: this.$t("Feature"),
                Disabled: this.$t("Disabled"),
                Unlimited: this.$t("Unlimited"),
                "N/A": this.$t("N/A"),
                "View prices": this.$t("View prices"),
                "{featureValue} {featureSuffix}": this.$t(
                    "{featureValue} {featureSuffix}"
                ),
                "Buy feature": this.$t("Buy feature"),
                "You reached the limit of feature <b>{feature} - {limit}</b><br>To continue you should purchase a feature pack or upgrade to a plan with higher limits!":
                    this.$t(
                        "You reached the limit of feature <b>{feature} - {limit}</b><br>To continue you should purchase a feature pack or upgrade to a plan with higher limits!"
                    ),
                "This feature is not enabled for your plan. To access it, please upgrade your plan.":
                    this.$t(
                        "This feature is not enabled for your plan. To access it, please upgrade your plan."
                    ),
                "Plans that support this functionality are: <strong>{plan}</strong>":
                    this.$t(
                        "Plans that support this functionality are: <strong>{plan}</strong>"
                    ),
                "Plan feature": this.$t("Plan feature"),
                //suffix
                "Message|Messages": this.$t("Message|Messages"),
                "Customer|Customers": this.$t("Customer|Customers"),
                "Product option|Product options": this.$t(
                    "Product option|Product options"
                ),
                "Bundle|Bundles": this.$t("Bundle|Bundles"),
                "Smart Collection|Smart Collections": this.$t(
                    "Smart Collection|Smart Collections"
                ),
                "Administrator|Administrators": this.$t(
                    "Administrator|Administrators"
                ),
                "Page|Pages": this.$t("Page|Pages"),
                "Segment|Segments": this.$t("Segment|Segments"),
                "Synchronization|Synchronizations": this.$t(
                    "Synchronization|Synchronizations"
                ),
                "Hostname|Hostnames": this.$t("Hostname|Hostnames"),
                "meeting for 30 days|meetings for 30 days": this.$t(
                    "meeting for 30 days|meetings for 30 days"
                ),
                "Subscriber|Subscribers": this.$t("Subscriber|Subscribers"),
                "Task|Tasks": this.$t("Task|Tasks"),
                "Product|Products": this.$t("Product|Products"),
                "Discount|Discounts": this.$t("Discount|Discounts"),
                Name: this.$t("Name"),
                Price: this.$t("Price"),
                "No feature pack available, you can upgrade your plan.":
                    this.$t(
                        "No feature pack available, you can upgrade your plan."
                    ),
                "Upgrade plan": this.$t("Upgrade plan"),
            },
            featureModal: this.modelValue,
            submitLoader: false,
            loading: true,
            model: new PlanFeature(),
            feature: null,
            packs: [],
            responseErrors: {},
            pack: {},
            buyPanel: false,
        };
    },
    computed: {
        table() {
            return {
                data: {
                    data: this.packs.map((x) => ({
                        ...x,
                        buyFeature: () => this.buyFeature(x),
                    })),
                },
                isLoading: this.loading,
                paginationShowDisabled: true,
                columns: [
                    {
                        column: "name",
                        key: "name",
                        title: this.translations["Name"],
                        sortable: false,
                    },
                    {
                        column: "price_without_vat_formatted",
                        key: "price_without_vat_formatted",
                        title: this.translations["Price"],
                        sortable: false,
                    },
                    {
                        column: "actions",
                        key: "buy",
                        title: " ",
                        sortable: false,
                        component: markRaw(BuyButton),
                    },
                ],
            };
        },
        packRecord() {
            return this.pack || {};
        },
        packType() {
            return this.pack?.type || "";
        },
        limit() {
            if (!this.feature) {
                return this.translations["N/A"];
            }

            switch (this.feature.cast) {
                case "bool":
                    return this.translations["Disabled"];
                case "storage":
                    return "Disabled";
                case "int":
                    let value1 = parseFloat(this.feature.featureValue);
                    if (isNaN(value1)) {
                        return this.translations["Unlimited"];
                    }

                    return this.$trp(
                        this.translations["{featureValue} {featureSuffix}"],
                        {
                            featureValue: value1,
                            featureSuffix: this.featureSuffix,
                        }
                    );
                case "fee":
                    let value2 = parseFloat(this.feature.featureValue);
                    if (isNaN(value2) || this.feature.featureValue === true) {
                        value2 = 0;
                    }

                    return percentFormat(value2 / 100);
                default:
                    return this.feature.featureValue;
            }
        },
        featureSuffix() {
            let mapping = {
                viber_messages: this.$tc(
                    "Message|Messages",
                    this.feature.featureValue
                ),
                mailchimp: this.$tc(
                    "Customer|Customers",
                    this.feature.featureValue
                ),
                orders_amount: 'EUR',
                orders_revenue: 'EUR',
                product_options: this.$tc(
                    "Product option|Product options",
                    this.feature.featureValue
                ),
                bundles: this.$tc("Bundle|Bundles", this.feature.featureValue),
                product_collections: this.$tc(
                    "Smart Collection|Smart Collections",
                    this.feature.featureValue
                ),
                customers: this.$tc(
                    "Customer|Customers",
                    this.feature.featureValue
                ),
                discount_global: this.$tc(
                    "Discount|Discounts",
                    this.feature.featureValue
                ),
                discount_coupon: this.$tc(
                    "Discount|Discounts",
                    this.feature.featureValue
                ),
                discount_fixed: this.$tc(
                    "Discount|Discounts",
                    this.feature.featureValue
                ),
                discount_quantity: this.$tc(
                    "Discount|Discounts",
                    this.feature.featureValue
                ),
                discount_bogo: this.$tc(
                    "Discount|Discounts",
                    this.feature.featureValue
                ),
                discount_volume: this.$tc(
                    "Discount|Discounts",
                    this.feature.featureValue
                ),
                administrators: this.$tc(
                    "Administrator|Administrators",
                    this.feature.featureValue
                ),
                landing_page: this.$tc("Page|Pages", this.feature.featureValue),
                abandoned_notification: this.$tc(
                    "Message|Messages",
                    this.feature.featureValue
                ),
                segments: this.$tc("Segment|Segments"),
                shipping_payment_sync: this.$tc(
                    "Synchronization|Synchronizations",
                    this.feature.featureValue
                ),
                custom_hostname: this.$tc(
                    "Hostname|Hostnames",
                    this.feature.featureValue
                ),
                support_meetings: this.$tc(
                    "meeting for 30 days|meetings for 30 days",
                    this.feature.featureValue
                ),
                subscribers: this.$tc(
                    "Subscriber|Subscribers",
                    this.feature.featureValue
                ),
                viber_messages_subscription: this.$tc(
                    "Message|Messages",
                    this.feature.featureValue
                ),
                xml_import_limit: this.$tc(
                    "Task|Tasks",
                    this.feature.featureValue
                ),
                products: this.$tc(
                    "Product|Products",
                    this.feature.featureValue
                ),
            };

            return (
                mapping[this.feature.mapping] ||
                this.$tc("Product|Products", this.feature.featureValue)
            );
        },
    },
    methods: {
        async getData() {
            this.loading = true;

            try {
                let { feature, packs } = await this.model.find(
                    this.$route.params.id
                );
                if (Array.isArray(packs)) {
                    this.packs = packs;
                } else if (typeof packs === "object" && packs !== null) {
                    this.packs = Object.values(packs);
                } else {
                    this.packs = [];
                }

                if (this.packs?.length === 0) {
                    this.message =
                        this.translations[
                            "No feature pack available, you can upgrade your plan."
                        ];
                    this.planModal = true;
                }
                this.feature = feature;
            } catch (err) {
                this.$errorResponse(err);
                if (err?.response?.status == 404) {
                    this.$router.push({ name: "error404" });
                }
            } finally {
                this.loading = false;
            }
        },
        buyFeature(pack) {
            this.pack = {
                type: pack.model_type,
                mapping: pack.id,
            };

            setTimeout(() => {
                this.buyPanel = true;
            }, 50);
        },
    },
    async created() {
        await this.getData();
    },
};
</script>