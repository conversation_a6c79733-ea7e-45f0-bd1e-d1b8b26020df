import axios from 'axios';
import Steps from './Steps';

import {computed, inject, ref, watch} from 'vue'
import {createSharedComposable} from '@vueuse/core'

import useSharedPlanPanelState from "@components/Checkout/js/useSharedPlanPanelState";
import {useRoute, useRouter} from 'vue-router'


const useOnboardingProgress = createSharedComposable(() => {
    const errorResponse = inject('errorResponse');

    const router = useRouter();
    // const route = useRoute();

    const loading = ref(false);
    const submitLoader = ref(false);

    const onboardingProgressComplete = ref(false);
    const confirmGoLiveModal = ref(false);
    const goLiveError = ref(false);
    const error = ref(false);

    const {openModal: planModal, successState} = useSharedPlanPanelState();

    watch(successState, (value) => {
        if (value && Object.keys(value).length) {
            steps.value.plan.completed = true;
        }
    }, {deep: true})

    watch(confirmGoLiveModal, (value) => {
        if (!value) {
            goLiveError.value = false;
        }
    })

    // HELPERS
    async function handleSSL() {
        if (!steps.value.domain.completed) {
            await router.push({name: "domains.settings", hash: "#add-domain"});
        } else {
            await router.push({name: "domains.settings", hash: "#add-ssl"});
        }
    }

    async function handleGoLive() {
        if (!steps.value.plan.completed) {
            planModal.value = true;
            return;
        }
        confirmGoLiveModal.value = true;
    }

    // STEPS STATE
    const steps = ref({
        products: {
            key: "products",
            icon: "fal fa-truck-loading",
            completed: false,
            description: "Add at least one product and publish it",
            to: {name: "products-list", hash: "#add-product"},
            skippable: false
        },
        payment: {
            key: "payment",
            icon: "far fa-credit-card",
            description: "Select and activate at least two payment providers",
            completed: false,
            to: {name: "admin.payments", hash: "#add-payment"},
            skippable: false
        },
        shipping: {
            key: "shipping",
            icon: "fal fa-shipping-fast",
            description: "Select and activate at least two shipping provivers",
            completed: false,
            to: {name: "admin.shippingProviders", hash: "#add-shipping"},
            skippable: false
        },
        orders: {
            key: "orders",
            icon: "far fa-boxes",
            description: 'At least one order to be marked as "Completed".',
            completed: false,
            action: () => {
                try {
                    window.open(window.serverSettings.host)
                } catch (error) {
                    window.location.href = "/admin/orders";
                }
            },
            skippable: false
        },
        gdpr: {
            key: "gdpr",
            icon: "fa-solid fa-fingerprint",
            completed: false,
            description: "The GDPR app must be installed",
            to: {name: "apps.gdpr.settings"},
            skippable: true
        },
        domain: {
            key: "domain",
            icon: "fal fa-xl fa-globe",
            description: "Add your custom domain",
            completed: false,
            to: {name: "domains.settings", hash: "#add-domain"},
            skippable: true
        },
        ssl: {
            key: "ssl",
            icon: "fa-regular fa-shield-check",
            completed: false,
            description: "Install SSL certificate.",
            warningMessage: true,
            action: handleSSL,
            skippable: true
        },
        plan: {
            key: "plan",
            icon: "far fa-star",
            completed: false,
            description: "Check our plans",
            action: () => {
                planModal.value = true;
            },
            skippable: false
        },
        go_live: {
            key: "go_live",
            icon: "fal fa-rocket-launch",
            description: "Make sure all key settings are complete",
            completed: false,
            action: handleGoLive,
            skippable: false
        },
    });

    async function getStepsStatus(isLoading = true) {

        const isCompleted = localStorage.getItem('onboarding_progress_complete');
        
        if (isCompleted) {
            loading.value = false;
            onboardingProgressComplete.value = true;
            return;
        }

        loading.value = isLoading;

        try {
            let model = new Steps();
            const response = await model.all();

            // const response = {
            //     "domain": false,
            //     "ssl": false,
            //     "orders": false,
            //     "products": false,
            //     "gdpr": false,
            //     "payment": false,
            //     "shipping": false,
            //     "plan": false,
            //     "go_live": false,
            // }

            if (response.onboarding_progress_complete) {
                onboardingProgressComplete.value = true;
                localStorage.setItem('onboarding_progress_complete', '1');
            } else {
                Object.entries(response).forEach(([key, value]) => {
                    if (steps.value[key]) {
                        steps.value[key].completed = value;
                    }
                })
                if (Object.values(steps.value).length === stepsCompleted.value) {
                    onboardingProgressComplete.value = true;
                    localStorage.setItem('onboarding_progress_complete', '1');
                }
            }
        } catch (err) {
            error.value = true;
            errorResponse(err);
        } finally {
            loading.value = false;
        }
    }

    async function skipStep(step) {
        step.skipLoader = true;

        try {
            await axios.patch('/admin/api/core/dashboard/onboarding-progress', {[step.key]: true})

            step.completed = true;
            return true
        } catch (err) {
            error.value = true;
            return false
        } finally {
            step.skipLoader = false;
        }
    }

    async function onSubmit() {
        submitLoader.value = true;
        try {
            let modelSteps = new Steps();

            const response = await modelSteps.create({
                onboarding_progress_complete: 1
            });

            onboardingProgressComplete.value = true;
        } catch (error) {
            error.value = true;
        } finally {
            submitLoader.value = false;
        }
    }

    async function onSubmitGoLive() {
        steps.value.go_live.loading = true;
        goLiveError.value = false;

        try {
            const response = await axios.get('/admin/api/core/settings/go-live');
            if (response.data.status == false) {
                goLiveError.value = true;
            } else {
                steps.value.go_live.completed = true;
                confirmGoLiveModal.value = false;
                goLiveError.value = false;
            }
        } catch (error) {
            console.log(error);
        } finally {
            steps.value.go_live.loading = false;
        }
    }

    const stepsCompleted = computed(() => {
        return Object.values(steps.value).filter(step => step.completed).length;
    });

    const isCompletedConfiguration = computed(() => {
        return Object.values(steps.value).every(x => x.completed);
    })

    const sortedSteps = computed(() => {
        return Object.values(steps.value).sort((a, b) => a.completed - b.completed);
    });

    return {
        steps,
        loading,
        onboardingProgressComplete,
        confirmGoLiveModal,
        goLiveError,
        getStepsStatus,
        stepsCompleted,
        isCompletedConfiguration,
        onSubmit,
        submitLoader,
        onSubmitGoLive,
        sortedSteps,
        skipStep
    }
});

export default useOnboardingProgress
