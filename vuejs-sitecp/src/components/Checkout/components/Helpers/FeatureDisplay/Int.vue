<template>
    <i class="far fa-times" v-if="value === 0"></i>
    <div v-else v-html="getValue"></div>
</template>
<script>
export default {
    props: {
        feature: {
            type: Object,
            required: true,
            default: {}
        },
        value: {}
    },
    data() {
        return {
            translations: {
                'Unlimited': this.$t('Unlimited'),
                '{featureValue} {featureSuffix}': this.$t('{featureValue} {featureSuffix}'),
                //suffix
                'Message|Messages': this.$t('Message|Messages'),
                'Customer|Customers': this.$t('Customer|Customers'),
                'Product option|Product options': this.$t('Product option|Product options'),
                'Bundle|Bundles': this.$t('Bundle|Bundles'),
                'Smart Collection|Smart Collections': this.$t('Smart Collection|Smart Collections'),
                'Administrator|Administrators': this.$t('Administrator|Administrators'),
                'Page|Pages': this.$t('Page|Pages'),
                'Segment|Segments': this.$t('Segment|Segments'),
                'Synchronization|Synchronizations': this.$t('Synchronization|Synchronizations'),
                'Hostname|Hostnames': this.$t('Hostname|Hostnames'),
                'meeting for 30 days|meetings for 30 days': this.$t('meeting for 30 days|meetings for 30 days'),
                'Subscriber|Subscribers': this.$t('Subscriber|Subscribers'),
                'Task|Tasks': this.$t('Task|Tasks'),
                'Product|Products': this.$t('Product|Products'),
                'Discount|Discounts': this.$t('Discount|Discounts'),
            }
        }
    },
    computed: {
        getValue() {
            let value = parseFloat(this.value);
            if(isNaN(value)) {
                return '<span class="fs-6">∞</span>';
                return this.translations['Unlimited'];
            }

            return this.$trp(this.translations['{featureValue} {featureSuffix}'], {
                featureValue: value,
                featureSuffix: this.featureSuffix
            })
        },
        featureSuffix() {
            let mapping = {
                'viber_messages': this.$tc('Message|Messages', this.value),
                'mailchimp': this.$tc('Customer|Customers', this.value),
                'orders_amount': 'EUR',
                'orders_revenue': 'EUR',
                'product_options': this.$tc('Product option|Product options', this.value),
                'bundles': this.$tc('Bundle|Bundles', this.value),
                'product_collections': this.$tc('Smart Collection|Smart Collections', this.value),
                'customers': this.$tc('Customer|Customers', this.value),
                'discount_global': this.$tc('Discount|Discounts', this.value),
                'discount_coupon': this.$tc('Discount|Discounts', this.value),
                'discount_fixed': this.$tc('Discount|Discounts', this.value),
                'discount_quantity': this.$tc('Discount|Discounts', this.value),
                'discount_bogo': this.$tc('Discount|Discounts', this.value),
                'discount_volume': this.$tc('Discount|Discounts', this.value),
                'administrators': this.$tc('Administrator|Administrators', this.value),
                'landing_page': this.$tc('Page|Pages', this.value),
                'abandoned_notification': this.$tc('Message|Messages', this.value),
                'segments': this.$tc('Segment|Segments'),
                'shipping_payment_sync': this.$tc('Synchronization|Synchronizations', this.value),
                'custom_hostname': this.$tc('Hostname|Hostnames', this.value),
                'support_meetings': this.$tc('meeting for 30 days|meetings for 30 days', this.value),
                'subscribers': this.$tc('Subscriber|Subscribers', this.value),
                'viber_messages_subscription': this.$tc('Message|Messages', this.value),
                'xml_import_limit': this.$tc('Task|Tasks', this.value),
                'products': this.$tc('Product|Products', this.value),
            }

            return mapping[this.feature.mapping] || this.$tc('Product|Products', this.value);
        }
    }
}
</script>