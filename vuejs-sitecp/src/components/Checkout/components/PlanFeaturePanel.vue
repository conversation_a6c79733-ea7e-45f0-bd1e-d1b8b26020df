<template>
    <b-modal
        :size="!buyPanel ? 'xl' : 'xll'"
        class="modal-right"
        header-class="edit-settings-modal-header"
        body-class="edit-settings-modal-content"
        :no-footer="true"
        v-model="featureModal"
        :no-close-on-esc="true"
        :no-close-on-backdrop="true"
    >
        <template #header>
            <div class="col-10">
                <h5 class="modal-title-checkout mb-0">
                    {{ translations["Feature"] }}
                </h5>
            </div>
            <div class="col-2 text-right d-flex justify-content-end align-items-center">
                <button @click.prevent="featureModal = false" class="btn btn-ghost me-2">
                    {{ translations["Close"] }}
                </button>
            </div>
        </template>

        <Loading v-if="loading" :loading="loading" class="app-loader-center" />

        <template v-else>
            <slot name="message"></slot>
<!--            <b-card class="mb-3">-->
<!--                <b-row>-->
<!--                    <b-col>-->
<!--                        <div-->
<!--                            class="info-box info-box-warning"-->
<!--                            v-html="-->
<!--                                $trp(-->
<!--                                    translations[-->
<!--                                        'You reached the limit of feature <b>{feature} - {limit}</b><br>To continue you should purchase a feature pack or upgrade to a plan with higher limits!'-->
<!--                                    ],-->
<!--                                    {-->
<!--                                        feature: feature?.name,-->
<!--                                        limit: limit,-->
<!--                                    }-->
<!--                                )-->
<!--                            "-->
<!--                        ></div>-->
<!--                    </b-col>-->
<!--                </b-row>-->
<!--            </b-card>-->

            <b-row>
                <b-col>
                    <data-table
                        v-if="table.data && !feature?.purchaseRestrictMessage"
                        v-bind="this.table"
                        :enable-mobile="true"
                    />
                    <!-- <table class="table table-striped table-hover" v-if="!feature?.purchaseRestrictMessage">
                            <tbody>
                                <tr v-for="pack in packs">
                                    <td v-html="pack.name"></td>
                                    <td v-html="pack.price_without_vat_formatted"></td>
                                    <td class="text-end">
                                        <button class="btn btn-primary" @click.prevent="buyFeature(pack)">
                                            {{ translations['Buy feature'] }}
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table> -->

                    <b-card v-else>
                        <div class="info-box info-box-warning">
                            <p
                                v-html="
                                    translations[
                                        'This feature is not enabled for your plan. To access it, please upgrade your plan.'
                                    ]
                                "
                            ></p>
                            <p
                                v-html="
                                    $trp(
                                        translations[
                                            'Plans that support this functionality are: <strong>{plan}</strong>'
                                        ],
                                        {
                                            plan: (
                                                feature?.purchaseRestrictMessage || []
                                            ).join(', '),
                                        }
                                    )
                                "
                            ></p>
                            <p class="text-center">
                                <button class="btn btn-primary">
                                    {{ translations["View prices"] }}
                                </button>
                            </p>
                        </div>
                    </b-card>
                </b-col>
            </b-row>
        </template>
    </b-modal>

    <Checkout
        v-model="buyPanel"
        v-model:record="packRecord"
        v-model:type="packType"
        button-text="Back to overview"
        @success="
            (result) => {
                this.$emit('success', result);
            }
        "
    ></Checkout>
    <!-- this.buyPanel = false -->
</template>

<script>
import Loading from "./../../Loading";
import PlanFeature from "./../js/PlanFeatureModel";
import { Checkout } from "./../../Checkout";
import { percentFormat } from "@js/numberFormatters";
import DataTable from "@components/Table";
import BuyButton from "./Helpers/BuyButton";
import { markRaw } from "vue";
import useSharedPlanPanelState from "@components/Checkout/js/useSharedPlanPanelState";

export default {
    name: "PlanFeature",
    components: {
        Loading,
        Checkout,
        DataTable,
    },
    props: {
        modelValue: {
            required: true,
            type: Boolean,
            default: false,
        },
        record: {
            required: true,
            type: Object,
            default: {},
        },
        type: {
            required: true,
            type: String,
            default: null,
        },
        messagePlanPanel: {
            type: String,
            default: null,
        },
    },
    setup(){
        const { openModal: planModal, successState, message } = useSharedPlanPanelState();

        return {
            planModal,
            successState,
            message
        };
    },
    data() {
        return {
            translations: {
                Close: this.$t("Close"),
                Feature: this.$t("Feature"),
                Disabled: this.$t("Disabled"),
                Unlimited: this.$t("Unlimited"),
                "N/A": this.$t("N/A"),
                "View prices": this.$t("View prices"),
                "{featureValue} {featureSuffix}": this.$t(
                    "{featureValue} {featureSuffix}"
                ),
                "Buy feature": this.$t("Buy feature"),
                "You reached the limit of feature <b>{feature} - {limit}</b><br>To continue you should purchase a feature pack or upgrade to a plan with higher limits!": this.$t(
                    "You reached the limit of feature <b>{feature} - {limit}</b><br>To continue you should purchase a feature pack or upgrade to a plan with higher limits!"
                ),
                "This feature is not enabled for your plan. To access it, please upgrade your plan.": this.$t(
                    "This feature is not enabled for your plan. To access it, please upgrade your plan."
                ),
                "Plans that support this functionality are: <strong>{plan}</strong>": this.$t(
                    "Plans that support this functionality are: <strong>{plan}</strong>"
                ),
                //suffix
                "Message|Messages": this.$t("Message|Messages"),
                "Customer|Customers": this.$t("Customer|Customers"),
                "Product option|Product options": this.$t(
                    "Product option|Product options"
                ),
                "Bundle|Bundles": this.$t("Bundle|Bundles"),
                "Smart Collection|Smart Collections": this.$t(
                    "Smart Collection|Smart Collections"
                ),
                "Administrator|Administrators": this.$t("Administrator|Administrators"),
                "Page|Pages": this.$t("Page|Pages"),
                "Segment|Segments": this.$t("Segment|Segments"),
                "Synchronization|Synchronizations": this.$t(
                    "Synchronization|Synchronizations"
                ),
                "Hostname|Hostnames": this.$t("Hostname|Hostnames"),
                "meeting for 30 days|meetings for 30 days": this.$t(
                    "meeting for 30 days|meetings for 30 days"
                ),
                "Subscriber|Subscribers": this.$t("Subscriber|Subscribers"),
                "Task|Tasks": this.$t("Task|Tasks"),
                "Product|Products": this.$t("Product|Products"),
                "Discount|Discounts": this.$t("Discount|Discounts"),
                Name: this.$t("Name"),
                Price: this.$t("Price"),
            },
            featureModal: this.modelValue,
            submitLoader: false,
            loading: true,
            model: new PlanFeature(),
            feature: null,
            packs: [],
            responseErrors: {},
            pack: {},
            buyPanel: false,
        };
    },
    methods: {
        submit() {
            this.submitLoader = true;
        },
        async loadData() {
            this.loading = true;

            try {
                let { feature, packs } = await this.model.find(this.record.mapping);
                if(Array.isArray(packs)) {
                  this.packs = packs;
                } else if (typeof packs === 'object' && packs !== null) {
                  this.packs = Object.values(packs);
                } else {
                    this.packs = [];
                }

                if (this.packs.length === 0) {
                    if(this.messagePlanPanel) {
                        this.message = this.messagePlanPanel;
                    }
                    this.planModal = true;
                    this.featureModal = false;
                }
                this.feature = feature;
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.loading = false;
            }
        },
        buyFeature(pack) {
            this.pack = {
                type: pack.model_type,
                mapping: pack.id,
                value: pack.value
            };

            setTimeout(() => {
                this.buyPanel = true;
            }, 50);
        },
    },
    watch: {
        featureModal(value) {
            this.$emit("update:modelValue", value);
        },
        async modelValue(value) {
            this.featureModal = value;
            if (value) {
                await this.loadData();
            }
        },
    },
    computed: {
        table() {
            return {
                data: {
                    data: this.packs.map((x) => ({
                        ...x,
                        buyFeature: () => this.buyFeature(x),
                    })),
                },
                isLoading: this.loading,
                paginationShowDisabled: true,
                defaultSorting: [{
                    key: 'id',
                    sortingMode: 'desc'
                }],
                columns: [
                    {
                        column: "name",
                        key: "name",
                        title: this.translations["Name"],
                        sortable: false,
                    },
                    {
                        column: "price_without_vat_formatted",
                        key: "price_without_vat_formatted",
                        title: this.translations["Price"],
                        sortable: false,
                    },
                    {
                        column: "actions",
                        key: "buy",
                        title: " ",
                        sortable: false,
                        component: markRaw(BuyButton),
                    },
                ],
            };
        },
        packRecord() {
            return this.pack || {};
        },
        packType() {
            return this.pack?.type || "";
        },
        limit() {
            if (!this.feature) {
                return this.translations["N/A"];
            }

            switch (this.feature.cast) {
                case "bool":
                    return this.translations["Disabled"];
                case "storage":
                    return "Disabled";
                case "int":
                    let value1 = parseFloat(this.feature.featureValue);
                    if (isNaN(value1)) {
                        return this.translations["Unlimited"];
                    }

                    return this.$trp(
                        this.translations["{featureValue} {featureSuffix}"],
                        {
                            featureValue: value1,
                            featureSuffix: this.featureSuffix,
                        }
                    );
                case "fee":
                    let value2 = parseFloat(this.feature.featureValue);
                    if (isNaN(value2) || this.feature.featureValue === true) {
                        value2 = 0;
                    }

                    return percentFormat(value2 / 100);
                default:
                    return this.feature.featureValue;
            }
        },
        featureSuffix() {
            let mapping = {
                viber_messages: this.$tc("Message|Messages", this.feature.featureValue),
                mailchimp: this.$tc("Customer|Customers", this.feature.featureValue),
                orders_amount: 'EUR',
                orders_revenue: 'EUR',
                product_options: this.$tc(
                    "Product option|Product options",
                    this.feature.featureValue
                ),
                bundles: this.$tc("Bundle|Bundles", this.feature.featureValue),
                product_collections: this.$tc(
                    "Smart Collection|Smart Collections",
                    this.feature.featureValue
                ),
                customers: this.$tc("Customer|Customers", this.feature.featureValue),
                discount_global: this.$tc(
                    "Discount|Discounts",
                    this.feature.featureValue
                ),
                discount_coupon: this.$tc(
                    "Discount|Discounts",
                    this.feature.featureValue
                ),
                discount_fixed: this.$tc("Discount|Discounts", this.feature.featureValue),
                discount_quantity: this.$tc(
                    "Discount|Discounts",
                    this.feature.featureValue
                ),
                discount_bogo: this.$tc("Discount|Discounts", this.feature.featureValue),
                discount_volume: this.$tc(
                    "Discount|Discounts",
                    this.feature.featureValue
                ),
                administrators: this.$tc(
                    "Administrator|Administrators",
                    this.feature.featureValue
                ),
                landing_page: this.$tc("Page|Pages", this.feature.featureValue),
                abandoned_notification: this.$tc(
                    "Message|Messages",
                    this.feature.featureValue
                ),
                segments: this.$tc("Segment|Segments"),
                shipping_payment_sync: this.$tc(
                    "Synchronization|Synchronizations",
                    this.feature.featureValue
                ),
                custom_hostname: this.$tc(
                    "Hostname|Hostnames",
                    this.feature.featureValue
                ),
                support_meetings: this.$tc(
                    "meeting for 30 days|meetings for 30 days",
                    this.feature.featureValue
                ),
                subscribers: this.$tc(
                    "Subscriber|Subscribers",
                    this.feature.featureValue
                ),
                viber_messages_subscription: this.$tc(
                    "Message|Messages",
                    this.feature.featureValue
                ),
                xml_import_limit: this.$tc("Task|Tasks", this.feature.featureValue),
                products: this.$tc("Product|Products", this.feature.featureValue),
            };

            return (
                mapping[this.feature.mapping] ||
                this.$tc("Product|Products", this.feature.featureValue)
            );
        },
    },
    emits: ["update:modelValue", "success"],
};
</script>
