<template>
    <div class="container-fluid position-relative h-100"
         :class="{ 'p-0':isTerminalPage }"
    >
        <AccessControl/>
        <div v-if="ccEmployee.id !== null || isHungarianStoreForTranslations" :style="ccEmployeeStyle" ref="ccEmployeeTag">
            <div class="d-table">
                <span class="d-table-cell">
                    <img :src="ccEmployee.avatar" alt="avatar" style="max-width: 40px">
                </span>
                <span>
                </span>
                <span class="d-table-cell">
                    <a href="javascript:void(0)" @click.prevent="logoutCcEmployee" class="ps-1">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </span>
                <span class="d-table-cell">
                    <TranslatePage></TranslatePage>
                </span>
            </div>
        </div>

        <nav class="navbar navbar-mobile navbar-expand-xl d-block d-xl-none"
             v-if="!isTerminalPage"
        >
            <div class="container-fluid">
                <div class="d-flex align-items-center w-100">
                    <div class="navbar-mobile-toggler d-flex align-items-center cursor-pointer px-1"
                         @click="isNavMobileShown = !isNavMobileShown">
                        <i class="fal fa-times fa-2x" v-if="isNavMobileShown"></i>
                        <i class="fal fa-bars fa-2x" v-else></i>
                    </div>
                    <logo classes="logo-section-mobile" :is-nav-mobile-shown="isNavMobileShown"
                          :notifications="notifications" :lang-versions="languageVersions"></logo>
                    <profile v-if="windowWidth < 1200" class="mobile-profile" :position="'down'"
                             :notifications="notifications"
                             :hide-alerts="true"></profile>
                </div>
            </div>
        </nav>
        <transition name="fade">
            <div class="modal-backdrop modal-backdrop-custom" v-if="isNavMobileShown"
                 @click="isNavMobileShown = !isNavMobileShown"></div>
        </transition>

        <aside class="cc-aside"
               v-if="!isTerminalPage"
        >
            <transition name="slide">
                <div :class="{'cc-main-navigation-mobile-wrap navbar-collapse':windowWidth < 1200}"
                     v-show="isNavMobileShown || windowWidth >= 1200">
                    <logo classes="d-xl-block d-none" :notifications="notifications"
                          :lang-versions="languageVersions"></logo>
                    <navigation class="cc-main-navigation"
                                :class="{'lang-versions': !!languageVersions.length && windowWidth >= 1200 }"></navigation>
                    <profile v-if="width >= 1200" :notifications="notifications"
                             :window-width="windowWidth"></profile>
                    <!-- <GoogleCloudBadge v-else></GoogleCloudBadge> -->
                </div>
            </transition>
        </aside>
        <main class="cc-main h-100" v-if="!this.serverSettings('is_old_sitecp')"
              :class="isTerminalPage ? 'm-0' : 'pe-xl-4 pb-5'">
            <router-view :key="componentHash"/>
        </main>
    </div>

    <HelpCenterSidebar/>
    <div id="notify-toast">
        <notifications position="top center" class="cc-notification" animationName="bounce"/>
    </div>

    <OnboardingProgressBar v-if="!isTerminalPage"/>

    <SocketComponent v-if="websocketSupport"/>
    <SocketEmit v-if="websocketSupport"/>

    <BookMeeting/>
</template>

<script>
import {ref, onMounted, watch, getCurrentInstance, nextTick, inject, defineAsyncComponent} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import {useWindowSize} from '@vueuse/core';
import {useCcEmployee} from '@js/Layout/composables/useCcEmployee.js';
import {useNotifications} from '@js/Layout/composables/useNotifications.js';
import {useHelpCenter} from '@js/Layout/composables/useHelpCenter.js';
import {useLanguageVersions} from '@js/Layout/composables/useLanguageVersions.js';
import OnboardingProgressBar from "@/components/OnboardingProgressBar.vue";
//import SocketComponent from "@/components/SocketComponent.vue";
import SocketEmit from "@/components/SocketEmit.vue";
import BookMeeting from "@/components/BookMeeting/Index.vue";
import AccessControl from "@/components/AccessControl.vue";
import Logo from "@/components/SideBar/Logo.vue";
import Navigation from "@/components/SideBar/Navigation.vue";
import HelpCenterSidebar from "@/components/HelpCenterSidebar/HelpCenterSidebar.vue";
import Profile from "@/components/SideBar/Profile.vue";
import TranslatePage from "@/components/Core/Translate/TranslatePage.vue";
import {useCookies} from "vue3-cookies";
import axios from "axios";
import Loading from "@/components/Loading.vue";
import useSharedAppsInfo from "@/CcModules/Apps/composables/useSharedAppsInfo.js";
import useSharedBookMeeting from "@/components/BookMeeting/composables/useSharedBookMeeting.js";
import useLiveNotifications from "@js/useLiveNotifications";

const SocketComponentAsync = defineAsyncComponent(() =>
    import('./SocketComponent')
);

export default {
    name: 'SiteCp',
    components: {
        Loading,
        Profile,
        Navigation,
        Logo,
        AccessControl,
        BookMeeting,
        SocketEmit,
        SocketComponent: SocketComponentAsync,
        OnboardingProgressBar,
        TranslatePage,
        HelpCenterSidebar
    },
    data() {
        return {
            websocketSupport: !this.serverSettings('disabled.websocket'),
            cookies: useCookies(),
            mainNavigation: this.serverSettings('navigation', []),
            isNavMobileShown: false,
            notifications: 0,
        }
    },
    setup() {
        const route = useRoute();
        const router = useRouter();
        const {ccEmployee, getCcEmployee, logoutCcEmployee} = useCcEmployee();
        const {notifications, getNotificationsCount} = useNotifications();
        const {helpCenter} = useHelpCenter();
        const {languageVersions, getLanguageVersions} = useLanguageVersions();
        const {apps, getApps} = useSharedAppsInfo();
        const {getMeet} = useSharedBookMeeting();
        const {width} = useWindowSize();
        const { allowedNotifications } = useLiveNotifications();

        const permissions = inject('allowedSections')
        const errorResponse = inject('errorResponse')

        const _this = getCurrentInstance();
        const componentHash = ref(new Date().getTime());

        const handleHelpCenterLinkClick = async (event) => {
            if (event.target?.classList?.contains('customer-support') && route.name === 'dashboard') {
                return;
            }
            const targetUrl = event.target.href;
            if (!targetUrl || !targetUrl.startsWith('https://help.cloudcart.com/')) {
                return;
            }

            event.preventDefault();

            helpCenter.value.loading = true;
            helpCenter.value.modal = true;

            try {
                const response = await axios.get(`/admin/api/core/help/${targetUrl.replace('https://help.cloudcart.com/', '')}`);
                helpCenter.value.help = response.data;
            } catch (err){
                errorResponse(err);
            } finally {
                helpCenter.value.loading = false;
            }
        };

        const employeeData = computed({
            get() {
                return ccEmployee.value || {
                    id: null,
                    name: '',
                    avatar: '',
                    roles: []
                };
            },
            set(value) {
                ccEmployee.value = value;
            }
        });

        const handleUnauthorized = () => {
            axios.interceptors.response.use(null,
                function (error) {
                    if (error?.response?.status === 402 && error?.response?.data?.redirect) {
                        router.push({name: error.response.data.redirect});
                    }

                    if (error?.response?.status === 401) {
                        setTimeout(() => {
                        //    window.location.href = '/admin/login';
                        }, 200);
                    }
                    return Promise.reject(error);
                }
            );
        };

        onMounted(async () => {
            getCcEmployee();
            getNotificationsCount();
            handleUnauthorized();
            document.addEventListener('click', handleHelpCenterLinkClick);

            if (permissions.apps) {
                await getApps();
                const multilangApp = apps.value.find(app => app.mapping === 'multilang');
                getLanguageVersions(multilangApp);
            }
            if(route.name === 'dashboard' && !permissions.dashboard){
                router.push({ name: 'error403' })
            }

            watch(
                () => ccEmployee.value.id,
                async (newId) => {
                    if (newId !== null) {
                        setTimeout(async () => {
                            await nextTick();
                            _this.proxy.makeCcEmployeeMovable();
                        }, 150);
                    }
                }
            );
        });

        return {
            apps,
            getApps,
            getMeet,
            width,
            ccEmployee: employeeData,
            logoutCcEmployee,
            notifications,
            windowWidth: width,
            componentHash,
            languageVersions,
            handleHelpCenterLinkClick,
            helpCenter,
            allowedNotifications
        };
    },
    async created() {
        axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
    },
    methods: {
        makeCcEmployeeMovable() {
            if (!this.$refs.ccEmployeeTag) {
                return;
            }
            this.dragElement(this.$refs.ccEmployeeTag);
        },
        dragElement(elmnt) {
            let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

            elmnt.onmousedown = dragMouseDown;

            function dragMouseDown(e) {
                if (e.target?.tagName === 'SELECT') {
                    return;
                }

                e = e || window.event;
                e.preventDefault();

                pos3 = e.clientX;
                pos4 = e.clientY;

                document.onmousemove = elementDrag;
                document.onmouseup = closeDragElement;
            }

            function elementDrag(e) {
                e = e || window.event;
                e.preventDefault();

                pos1 = pos3 - e.clientX;
                pos2 = pos4 - e.clientY;
                pos3 = e.clientX;
                pos4 = e.clientY;

                elmnt.style.top = (elmnt.offsetTop - pos2) + "px";
                elmnt.style.left = (elmnt.offsetLeft - pos1) + "px";
            }

            function closeDragElement() {
                document.onmouseup = null;
                document.onmousemove = null;

                document.cookie = `boxPositionTop=${elmnt.style.top}; expires=Thu, 18 Dec 2033 12:00:00 UTC; path=/`;
                document.cookie = `boxPositionLeft=${elmnt.style.left}; expires=Thu, 18 Dec 2033 12:00:00 UTC; path=/`;
            }
        },
        getCookie(cookieName) {
            const name = `${cookieName}=`;
            const decodedCookie = decodeURIComponent(document.cookie);
            const cookieArray = decodedCookie.split(';');

            for (let i = 0; i < cookieArray.length; i++) {
                let cookie = cookieArray[i].trim();

                if (cookie.indexOf(name) === 0) {
                    return cookie.substring(name.length, cookie.length);
                }
            }

            return null;
        },
    },
    watch: {
        $route(to, from) {
            this.isNavMobileShown = false;

            document.querySelectorAll('.tooltip, .tooltip-dark').forEach((tooltip) => {
                tooltip.parentNode.removeChild(tooltip);
            })

            if (process.env.NODE_ENV === 'production') {
                if (this.cookies.cookies.get('last_compiled') != this.serverSettings('last_compiled')) {
                    window.location.reload();
                }
            }
        },
        isNavMobileShown: function (value) {
            if (value) {
                document.querySelector('body').style.overflowY = "hidden";
            } else {
                document.querySelector('body').style.overflowY = "auto";
            }
            window.parent.postMessage({'isNavMobileShown': value});
        },
    },
    computed: {
        isHungarianStoreForTranslations(){
            return this.serverSettings('site.id') == 59408
        },
        ccEmployeeStyle() {
            const savedTop = this.getCookie("boxPositionTop");
            const savedLeft = this.getCookie("boxPositionLeft");

            return {
                position: 'fixed',
                left: savedLeft || '286px',
                top: savedTop || 0,
                'background-color': '#ffffff',
                'border-bottom-left-radius': '5px',
                'padding': '5px',
                'z-index': 1100,
                'cursor': 'move',
            }
        },
        isTerminalPage() {
            return ['terminal.orders', 'terminal.products', 'terminal.productsList'].indexOf(this.$route.name) > -1;
        },
    },
};
</script>

<style lang="scss">
.slide-enter-active {
    transition: all 0.2s ease-out;
}

.slide-leave-active {
    transition: all 0.2s ease-out;
}

.slide-enter-from,
.slide-leave-to {
    transform: translateX(-100%);
}

.fade-enter-active,
.fade-leave-active {
    transition: all 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.bounce-enter-active,
.bounce-leave-active,
.bounce-move {
    transition: all .15s;
}

.bounce-enter-from,
.bounce-leave-to {
    transform: translateY(-100%);
}

#help-center-modal {
    z-index: 1058;
}
</style>
