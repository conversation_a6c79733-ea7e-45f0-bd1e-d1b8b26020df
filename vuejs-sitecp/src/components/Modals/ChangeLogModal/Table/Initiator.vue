<template>
    <div class="d-flex flex-column gap-1">
        <div>
            <!-- <span>{{ data.initiator.name }} </span> -->
            <span v-html="initiatorName" class="label-500"></span>
            <span v-if="data.initiator.ccConsole">
                ({{ data.initiator.ccConsole.name }})
            </span>
        </div>
        <!-- <div>{{ data.initiator.action }}</div> -->
        <div v-html="initiatorDataAction"></div>
        <!-- <pre>{{ data.initiator }}</pre> -->
    </div>
</template>
  
<script>
export default {
    name: "Initiator",
    props: {
        data: {
            type: Object,
            required: false,
            default: {},
        },
        column: {
            type: Object,
            required: false,
            default: {},
        },
    },
    data() {
        return {
            translations: {
                '<a href="/admin/orders/details/{id}" class="cc-tooltip-dotted" target="_blank">Edit from order #{id}</a>':
                    this.$t(
                        '<a href="/admin/orders/details/{id}" class="cc-tooltip-dotted" target="_blank">Edit from order #{id}</a>'
                    ),
            },
        };
    },
    computed: {
        initiatorDataAction() {
            let initiator = this.data.initiator;
            if (initiator.action === "order") {
                return this.$trp(
                    this.translations[
                        '<a href="/admin/orders/details/{id}" class="cc-tooltip-dotted" target="_blank">Edit from order #{id}</a>'
                    ],
                    { id: initiator.order_id }
                );
            }
            let strAction =
                typeof initiator.action === "string"
                    ? initiator.action.charAt(0).toUpperCase() +
                      initiator.action.slice(1)
                    : initiator.action;
            if (!this.translations[strAction]) {
                this.translations[strAction] = this.$t(strAction);
            }
            return this.translations[strAction]
                ? this.translations[strAction]
                : strAction;
        },
        initiatorName() {
            if (!this.translations[this.data.initiator.name]) {
                this.translations[this.data.initiator.name] = this.$t(
                    this.data.initiator.name
                );
            }
            return this.translations[this.data.initiator.name];
        },
    },
};
</script>