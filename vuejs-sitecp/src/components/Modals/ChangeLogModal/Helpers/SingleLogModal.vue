<template>
    <b-modal
        v-model="modal"
        class="modal-right"
        :no-footer="true"
        header-class="edit-settings-modal-header"
        size="xl"
    >
        <template #header>
            <div class="col-8">
                <h5
                    class="settings-modal-title"
                >
                {{ translations['Change log item'] }}: 
                <span v-html="initiatorDataAction"></span>
            </h5>
            </div>
            <div
                class="col-4 text-right d-flex justify-content-end align-items-center gap-2"
            >
                <button
                    type="button"
                    class="btn btn-primary"
                    @click="modal = false"
                    v-html="translations['Close']"
                ></button>
            </div>
        </template>

        <data-table
            v-bind="table"
            :enable-mobile="true"
            :paginationShowDisabled="true"
        />
    </b-modal>
</template>

<script>
import _ from "lodash";
import { markRaw } from "vue";

import { moneyFormat } from "@js/numberFormatters";
import DataTable from "@components/Table";

import Key from "../Table/Key";
import Action from "../Table/Action";
import Change from "../Table/Change";
import ExpandedRow from "./../Table/ExpandedRow";

export default {
    components: {
        DataTable
    },
    props: {
        modelValue: {
            type: Boolean,
            required: false,
        },
        type: {
            type: String,
            enum: ["product", "bundle"],
        },
        item: {
            type: Object,
            required: false,
        },
    },
    data() {
        return {
            modal: false,
            loading: true,
            data: {
                data: [],
            },
            paginationShowDisabled: false,
            total: 0,
            page: 1,
            perPage: 25,
            responseErrors: {},
            translations: {
                "Close": this.$t("Close"),
                "Change log item": this.$t("Change log item"),

                "Field / Subject": this.$t("Field / Subject"),
                "Action": this.$t("Action"),
                "Change": this.$t("Change"),
                '<a href="/admin/orders/details/{id}" class="cc-tooltip-dotted" target="_blank">Edit from order #{id}</a>':
                    this.$t(
                        '<a href="/admin/orders/details/{id}" class="cc-tooltip-dotted" target="_blank">Edit from order #{id}</a>'
                    ),
            },
        };
    },
    computed: {
        initiatorDataAction() {
            let initiator = this.item?.initiator;
            if(!initiator || !initiator?.action) {
                return '';
            }
            if (initiator.action === "order") {
                return this.$trp(
                    this.translations[
                        '<a href="/admin/orders/details/{id}" class="cc-tooltip-dotted" target="_blank">Edit from order #{id}</a>'
                    ],
                    { id: initiator.order_id }
                );
            }
            let strAction =
                typeof initiator.action === "string"
                    ? initiator.action.charAt(0).toUpperCase() +
                      initiator.action.slice(1)
                    : initiator.action;
            if (!this.translations[strAction]) {
                this.translations[strAction] = this.$t(strAction);
            }
            return this.translations[strAction]
                ? this.translations[strAction]
                : strAction;
        },
        table() {
            return {
                data: this.data,
                meta: {
                    type: this.type,
                },
                paginationShowDisabled: this.paginationShowDisabled,
                isLoading: this.loading,
		        expandedRowComponent: markRaw(ExpandedRow),
                showEntriesInfo: false,
                fixedColWidths: ["25%", "25%", "50%"],
                openByDefault: true,
                columns: [
                    {
                        title: this.translations["Field / Subject"],
                        component: markRaw(Key),
                        showExpandToggle: true,
                    },
                    {
                        title: this.translations["Action"],
                        component: markRaw(Action),
                    },
                    {
                        title: this.translations["Change"],
                        component: markRaw(Change),
                    },
                ],
            };
        },
    },
    methods: {
        translate(key) {
            this.translations[key] = this.$t(key);
            return this.translations[key];
        },
        formatValue(key, value) {
            const moneyKeys = [
                "price",
                "individual_price",
            ];
            if (moneyKeys.includes(key)) {
                return moneyFormat(value / 100);
            }
            return this.maybeTranslate(key, value);
        },
        maybeTranslate(key, value) {
            const blacklist = [
                'price',
                'individual_price',
                'qty',
                'sort_order',
            ];
            // Only translate if the key is not blacklisted or is a null value.
            if (!blacklist.includes(key) || value === null) {
                this.translations[value] = this.$t(value);
                return this.translations[value];
            }
            return value;
        },
        parseData(entry) {
            const formattedChanges = [];

            // Process attributes
            if (entry.attributes) {
                for (const [key, value] of Object.entries(entry.attributes)) {
                    formattedChanges.push({
                        type: 'attribute',
                        key: this.translate(key),
                        change: {
                            action: { key: 'updated', value: this.translate('updated') },
                            value: [{ old: this.formatValue(key, value.old), new: this.formatValue(key, value.new) }],
                        }
                    });
                }
            }

            // Process relations
            if (entry.relations) {
                for (const [relationKey, changes] of Object.entries(entry.relations)) {
                    const relationChange = {
                        type: 'relation',
                        key: this.translate(relationKey),
                        items: []
                    };
                    for (const [changeType, changeList] of Object.entries(changes)) {
                        changeList.forEach(change => {
                            relationChange.items.push({
                                key: this.mapChangeKey(relationKey, changeType, change),
                                change: {
                                    action: { key: changeType, value: this.translate(changeType) },
                                    value: this.mapChangeValue(relationKey, changeType, change),
                                }
                            });
                        });
                    }
                    formattedChanges.push(relationChange);
                }
            }

            return formattedChanges;
        },
        mapChangeKey(relationKey, changeType, change) {
            switch (relationKey) {
                case 'variants':
                    if (change.diff) {
                        return Object.entries(change)
                            .filter(([key]) => key !== 'diff')
                            .map(([key, value]) => {
                                return `${this.translate(key)}: ${this.formatValue(key, value)}`;
                            });
                    }
                    return null;
                case 'bundle_products':
                    if (changeType === 'detached' || changeType === 'attached')
                        return null;
                    else if (changeType === 'updated')
                        return change.new;
                default:
                    return null;
            }
        },
        mapChangeValue(relationKey, changeType, change) {
            if (change.diff) {
                return Object.values(change.diff).map(v => {
                    return { key: this.translate(v.key), old: this.formatValue(v.key, v.old), new: this.formatValue(v.key, v.new) };
                });
            }

            switch (relationKey) {
                case 'variants':
                    if (changeType === 'attached' || changeType === 'detached')
                        return Object.entries(change).map(([key, value]) => `${this.translate(key)}: ${this.formatValue(key, value)}`);
                case 'meta':
                    const metaChange = Object.values(change)[0];
                    if (changeType === 'attached' || changeType === 'detached')
                        return [this.translate(metaChange.key)]
                case 'bundle_products':
                    if (changeType === 'attached')
                        return [this.translate(change.new)]
                    if (changeType === 'detached')
                        return [this.translate(change.old)]
                case 'linked_products':
                    if (changeType === 'attached' || changeType === 'detached')
                        return [this.translate(change.name)]
                default:
                    return [change];
            }
        },
    },
    watch: {
        modelValue(val) {
            this.modal = val;
            this.data.data = this.parseData(this.item.dirty);
            this.loading = false;
        },
        modal(val) {
            this.$emit("update:modelValue", val);
        },
    },
    emits: ["update:modelValue"],
};
</script>
