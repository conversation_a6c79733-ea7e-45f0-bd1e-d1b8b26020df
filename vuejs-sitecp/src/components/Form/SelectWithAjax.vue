<template>
    <b-row :class="noMargin ? '' : selectClasses">
        <b-col cols="12" :class="colClasses">
            <div
                :class="
                    columnStyle || mode === 'tags'
                        ? 'column-style'
                        : '' || spaceBetween
                        ? 'justify-content-between'
                        : 'justify-content-start'
                "
                class="form-group padding-top-0 d-flex align-items-center flex-md-nowrap flex-wrap"
            >
                <label
                    v-if="tooltip"
                    class="col pe-3"
                    :class="!columnStyle || mode !== 'tags' ? 'mb-md-0' : '', labelClasses"
                >
                    <tooltip-label
                        :tooltip-text="tooltipText"
                        :label-classes="labelClasses"
                        :label-text="label"
                        :icon="icon"
                    ></tooltip-label>
                    <span v-if="required" class="ms-1 input-required required"
                    >*</span
                    >
                </label>
                <label
                    v-if="!tooltip && label"
                    class="pe-3"
                    v-html="labelValue"
                    :class="!columnStyle || mode !== 'tags' ? 'mb-md-0' : '', labelClasses"
                ></label>
                <slot name="helpBlock"></slot>
                <b-col
                    class="position-relative"
                    :md="mode === 'tags' ? '12' : colsWidth"
                    cols="12"
                >
                    <Multiselect
                        class="multi"
                        v-model="value"
                        :class="
                            (showError &&
                            (error || (errors && errors.length > 0))
                                ? 'err-empty-field'
                                : '') + (!label ? ' mt-0' : '')
                        "
                        v-bind="getMultiselectBinds"
                        @input="selectValue"
                        @search-change="emitSearch"
                        @open="handleOpen"
                        @clear="handleClear"
                        :onCreate="createOptionHandler"
                        :ref="multiselectRef"
                        @keyup="$emit('keyup')"
                        @deselect="
                            (deselectValue) => $emit('deselect', deselectValue)
                        "
                    >
                        <template
                            v-if="imagePreview"
                            v-slot:option="{ option }"
                        >
                            <img
                                style="max-width: 28px; margin-right: 10px"
                                class="character-option-icon"
                                :src="
                                    option?.img ||
                                    option?.image ||
                                    serverSettings('noImages.150x150')
                                "
                            />
                            {{ option[labelIndicator] }}
                        </template>
                        <template
                            v-if="showCountryFlag"
                            v-slot:option="{ option }"
                        >
                            <div class="mx-2">
                                <country-flag
                                    :country="option.id.toLowerCase()"
                                    size="small"
                                    class="country-flag"
                                />
                            </div>
                            {{ option[labelIndicator] }}
                        </template>
                        <template
                            v-if="imagePreview"
                            v-slot:singlelabel="{ value }"
                        >
                            <div class="multiselect-single-label">
                                <img
                                    class="character-label-icon"
                                    style="max-width: 20px; margin-right: 10px"
                                    :src="value?.img || value?.image"
                                />
                                <span>{{
                                        generateLabelText(
                                            value[this.labelIndicator]
                                        )
                                    }}</span>
                            </div>
                        </template>
                        <!-- new -->
                        <template
                            v-if="showCountryFlag"
                            v-slot:singlelabel="{ value }"
                        >
                            <div class="mx-2 multiselect-single-label">
                                <country-flag
                                    :country="(value?.id || '').toLowerCase()"
                                    size="small"
                                    class="country-flag"
                                />
                                <span>{{
                                        generateLabelText(
                                            value[this.labelIndicator]
                                        )
                                    }}</span>
                            </div>
                        </template>
                        <!-- new -->
                        <template v-if="$slots.afterlist" #afterlist>
                            <slot name="afterlist"></slot>
                        </template>
                        <template v-if="$slots.beforelist" #beforelist>
                            <slot name="beforelist"></slot>
                        </template>
                        <template v-if="$slots.nooptions" #nooptions>
                            <slot name="nooptions"></slot>
                        </template>
                        <template v-if="$slots.noresults" #noresults>
                            <slot name="noresults"></slot>
                        </template>
                    </Multiselect>

                    <div
                        v-show="errors && errors.length > 0 && showError"
                        class="input-errors"
                        v-for="error of errors"
                        :key="error.$uid"
                    >
                        <div class="help-block-error">
                            <span>{{ error.$message }}</span>
                            <i
                                class="fal fa-times close-error-message"
                                @click="showError = false"
                            ></i>
                        </div>
                    </div>
                    <div
                        v-show="error && showError"
                        class="help-block-error transition-show-error"
                        :class="error ? 'show-error-text' : 'hide-error-text'"
                    >
                        <span v-html="error"></span>
                        <i
                            class="fal fa-times close-error-message"
                            @click="showError = false"
                        ></i>
                    </div>
                    <div v-if="helpBlock" class="help-block d-block">
                        <span>{{ helpBlock }}</span>
                    </div>
                </b-col>
            </div>
            <slot></slot>
        </b-col>
    </b-row>
</template>

<script>
import TooltipLabel from "@components/Apps/Erp/TooltipLabel.vue";
import Multiselect from "@vueform/multiselect";
import axios from "axios";
import _ from "lodash";
import CountryFlag from "vue-country-flag-next";

export default {
    name: "SelectWithAjax",
    components: {
        TooltipLabel,
        Multiselect,
        CountryFlag,
    },
    props: {
        apiUrl: {
            type: String,
            required: false,
        },
        apiUrlParams: {
            required: false,
            default: {},
        },
        valueIndicator: {
            required: false,
            default: "id",
        },
        labelIndicator: {
            required: false,
            default: "name",
        },
        selectClasses: {
            default: "my-3",
            type: String,
            required: false,
        },
        colClasses: {
            default: "",
            type: String,
            required: false,
        },
        id: {
            type: String,
            required: false,
        },
        multiselectRef: {
            type: String,
            required: false,
            default: "multiselect",
        },
        label: {
            type: String,
            required: false,
            default: null,
        },
        placeholder: {
            type: [String, null],
            required: false,
        },
        options: {
            required: false,
        },
        activeOptions: {
            type: Array,
            required: false,
            default: [],
        },
        max: {
            required: false,
        },
        searchable: {
            type: Boolean,
            required: false,
            default: false,
        },
        isLoading: {
            type: Boolean,
            default: false,
        },
        tooltipText: {
            type: String,
            required: false,
        },
        tooltip: {
            type: Boolean,
            required: false,
        },
        val: {
            required: false,
        },
        icon: {
            type: String,
            required: false,
            default: "far fa-info-circle",
        },
        error: {
            required: false,
            default: null,
        },
        columnStyle: {
            type: Boolean,
            required: false,
        },
        canClear: {
            type: Boolean,
            required: false,
            default: true,
        },
        errors: {
            type: Array,
            required: false,
        },
        colsWidth: {
            type: Number,
            required: false,
            default: 6,
        },
        spaceBetween: {
            type: Boolean,
            required: false,
            default: true,
        },
        isSelectDisabled: {
            required: false,
        },
        groups: {
            type: Boolean,
            required: false,
            default: false,
        },
        mode: {
            required: false,
            // [tags]
        },
        createOption: {
            required: false,
            default: false,
        },
        requestOnSearch: {
            required: false,
            default: false,
        },
        requestOnOpen: {
            required: false,
            default: false,
        },
        minChars: {
            required: false,
            default: 0,
        },
        resolveOnLoad: {
            type: Boolean,
            required: false,
            default: false,
        },
        hideSelected: {
            type: Boolean,
            required: false,
            default: true,
        },
        noMargin: {
            required: false,
            default: false,
        },
        required: {
            type: Boolean,
            required: false,
        },
        showCountryFlag: {
            type: Boolean,
            required: false,
            default: false,
        },
        emitOnGet: {
            required: false,
            default: false,
            type: Boolean,
        },
        preventGetOnApiChange: {
            required: false,
            default: false,
            type: Boolean,
        },
        helpBlock: {
            required: false,
            default: null,
            type: String,
        },
        filterResults: {
            type: Boolean,
            default: true,
        },
        ignoreFilter: {
            type: Boolean,
            default: false,
        },
        externalOptiosMod: {
            type: Function,
            required: false,
            default: null,
        },
        clearOnSelect: {
            type: Boolean,
            required: false,
            default: false,
        },
        clearOnBlur: {
            type: Boolean,
            required: false,
            default: true,
        },
        noResultsText: {
            type: String,
            required: false,
            default: null,
        },
        labelClasses: {
            type: String,
            required: false,
            default: "label-text",
        },
        querySearchKey: {
            type: String,
            required: false,
            default: "query",
        },
        watchOptions:{
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            optionsData: [],
            loading: false,
            searchText: null,
            nextPage: null,
            nextPageNumber: null,
            hasNextPage: null,
            showError: true,
            translations: {
                "No matches found": this.$t("No matches found"),
                "Searching...": this.$t("Searching..."),
                Select: this.$t("Select"),
            },
        };
    },
    mounted() {
        this.loading = this.isLoading;

        if (Array.isArray(this.options)) {
            this.optionsData = this.options;
        } else {
            this.optionsData = [];
        }

        this.searchText = this.translations["No matches found"];
        if (this.noResultsText) {
            this.searchText = this.noResultsText;
        }

        if (this.apiUrl && this.resolveOnLoad) {
            this.getOptions();
        }

        this.$nextTick(() => {
            this.setupScrollListener();
        });
    },
    watch: {
        val(values) {
            this.value = values;
            this.showError = false;
        },
        options: {
            deep: true,
            handler(value) {
                if(!this.groups){
                    this.optionsData = _.uniqBy(
                        this.optionsData.concat(value),
                        (e) => String(e[this.valueIndicator])
                    );
                }
                if(this.watchOptions){
                    this.optionsData = value
                }
            },
        },
        isLoading(value) {
            this.loading = value;
        },
        loading(value) {
            this.$emit("update:isLoading", value);
        },
        apiUrlParams: {
            deep: true,
            handler(newValue, oldValue) {
                if (!_.isEqual(newValue, oldValue)) {
                    this.optionsData = [];
                    this.nextPage = null;
                    this.nextPageNumber = null;
                    this.hasNextPage = null;
                    this.getOptions();
                }
            },
        },
        apiUrl(newVal, oldVal) {
            if (oldVal && newVal != oldVal && !this.preventGetOnApiChange) {
                this.nextPage = null;
                this.nextPageNumber = null;
                this.hasNextPage = null;
                this.getOptions("", true);
            }
        },
        error(value) {
            if (value) {
                this.showError = true;
            }
        },
    },
    methods: {
        createOptionHandler(option, select) {
            this.optionsData.unshift({ ...option, isCustom: true });
            this.$emit("options", [
                { ...option, isCustom: true },
                ...this.optionsData,
            ]);

            if (this.mode !== "tags") {
                this.selectValue(option[this.valueIndicator]);
            }

            return option;
        },
        generateLabelText(label) {
            if (this.colsWidth <= 6 && label?.length > 13) {
                return label.slice(0, 14).concat("...");
            } else if (this.colsWidth >= 12 && label?.length > 69) {
                return label.slice(0, 69).concat("...");
            }
            return label;
        },
        selectValue(val) {
            try {
                this.$emit("options", this.optionsData);
                this.$emit("update:val", val);
                if (Array.isArray(this.optionsData)) {
                    this.$emit(
                        "selected-item",
                        this.mode === "tags"
                            ? this.uniqueSelectedOptions.filter((x) =>
                                val.includes(x[this.valueIndicator])
                            )
                            : this.optionsData.find(
                                (x) => x[this.valueIndicator] == val
                            )
                    );
                }
                //clears the search input after selecting an option
                this.$refs[this.multiselectRef].clearSearch();
            } catch (e) {
                console.log(e);
            }
        },
        emitSearch(query) {
            if (this.apiUrl && this.requestOnSearch) {
                this.search(query, this);
            }
            this.$emit("get-search-query", query);
        },
        search: _.debounce(async (query, vm) => {
            vm.loading = true;
            vm.searchText = vm.translations["Searching..."];

            let url = "";
            query
                ? (url = `${vm.apiUrl}?${vm.querySearchKey}=${query}`)
                : (url = `${vm.apiUrl}`);

            if (vm.apiUrlParams && Object.keys(vm.apiUrlParams).length) {
                Object.entries(vm.apiUrlParams).map(([key, value]) => {
                    if (url.indexOf("?") === -1) {
                        url += `?${key}=${value}`;
                    } else {
                        url += `&${key}=${value}`;
                    }
                });
            }

            try {
                const res = await axios.get(url);

                let handledFormatedData = vm.handleResponseData(res);

                vm.optionsData = handledFormatedData;

                vm.optionsData = _.uniqBy(vm.optionsData, (e) => {
                    return String(e[vm.valueIndicator]);
                });

                if (vm.externalOptiosMod) {
                    vm.optionsData = vm.externalOptiosMod(vm.optionsData);
                }

                if (res.data.next_page_url) {
                    vm.nextPage = res.data.next_page_url;
                } else {
                    vm.nextPage = null;
                }

                if (vm.optionsData.length === 0) {
                    vm.searchText = vm.translations["No matches found"];
                }
            } catch (err) {
                if (!vm.createOption) {
                    vm.$errorResponse(err);
                } else {
                    console.log(err);
                }
            } finally {
                vm.loading = false;
            }
        }, 350),
        async getOptions(query, refresh = false) {
            if (this.loading) {
                return;
            }
            if (refresh) {
                this.optionsData = [];
            }
            this.loading = true;
            this.searchText = this.translations["Searching..."];
            try {
                let url = query ? `${this.apiUrl}?${this.querySearchKey}=${query}` : this.apiUrl;
                if (
                    this.apiUrlParams &&
                    Object.keys(this.apiUrlParams).length
                ) {
                    Object.entries(this.apiUrlParams).forEach(
                        ([key, value]) => {
                            url += url.includes("?")
                                ? `&${key}=${value}`
                                : `?${key}=${value}`;
                        }
                    );
                }
                if (!url) {
                    this.loading = false;
                    return;
                }
                const res = await axios.get(url);

                let handledFormatedData = this.handleResponseData(res);

                this.optionsData = this.optionsData.concat(handledFormatedData);

                // if(!this.groups){
                //     this.optionsData = this.optionsData.concat(handledFormatedData);
                // } else {
                //     this.optionsData = handledFormatedData;
                // }

                this.optionsData = _.uniqBy(
                    this.optionsData,
                    (e) => String(e[this.valueIndicator])
                );

                if (this.externalOptiosMod) {
                    this.optionsData = _.uniqBy(
                        this.optionsData.concat(this.externalOptiosMod(this.optionsData)),
                        (e) => String(e[this.valueIndicator])
                    )
                    // this.optionsData = this.optionsData.concat(this.externalOptiosMod(this.optionsData));
                }

                if (res.data.next_page_url) {
                    this.nextPage = res.data.next_page_url;
                    this.hasNextPage = true;
                } else {
                    this.nextPage = null;
                    this.hasNextPage = false;
                }

                if (_.isEmpty(this.optionsData)) {
                    this.searchText = this.translations["No matches found"];
                }

                if (this.emitOnGet) {
                    this.$emit("options", this.optionsData);
                }
            } catch (err) {
                if (!this.createOption) {
                    this.$errorResponse(err);
                } else {
                    console.log(err);
                }
            } finally {
                this.loading = false;
            }
        },

        handleOpen() {
            if (this.apiUrl) {
                (!this.nextPage && !this.hasNextPage) ||
                this.requestOnOpen ||
                this.optionsData?.length === 0
                    ? this.getOptions()
                    : null;
            }
            this.$emit("open-options", true);
        },
        handleResponseData(res) {
            let keys = Object.keys(res.data);

            if (
                keys.length &&
                Number(keys[0]) &&
                typeof res.data[keys[0]] === "string"
            ) {
                return keys.map((x) => ({ id: x, name: res.data[x] }));
            }

            if (
                !res.data.results &&
                res.data.data &&
                typeof res.data.data === "object" &&
                !Array.isArray(res.data.data)
            ) {
                return Object.values(res.data.data);
            } else if (res.data.data && Array.isArray(res.data.data)) {
                return res.data.data;
            } else if (
                !res.data.results &&
                res.data &&
                typeof res.data === "object" &&
                !Array.isArray(res.data)
            ) {
                return Object.values(res.data);
            } else if (res.data.results) {
                return res.data.results;
            }

            return res.data;
        },
        async loadMoreItems(multiselectDropdown) {
            this.$emit("load-more-items");
            if (this.apiUrl && (this.nextPage || this.hasNextPage)) {
                let spinner = this.$refs[
                    this.multiselectRef
                    ]?.$el.querySelectorAll(".dropdown-spinner-wrapper");
                if (spinner.length === 0) {
                    multiselectDropdown.appendChild(this.createSpinner());
                }
                await this.getNextPage(this.nextPage, this.searchQuery);
            }
        },
        createSpinner() {
            const spinner = document.createElement("div");
            spinner.classList.add("text-center", "dropdown-spinner-wrapper");
            spinner.innerHTML = `
                <span class="spinner-border spinner-border-sm" aria-hidden="true"></span>
            `;
            return spinner;
        },
        removeSpinners() {
            const spinner = this.$refs[
                this.multiselectRef
                ]?.$el.querySelectorAll(".dropdown-spinner-wrapper");
            if (spinner && spinner.length > 0) {
                spinner.forEach((el) => el.parentNode.removeChild(el));
            }
        },
        setupScrollListener() {
            const multiselectDropdown = this.$refs[
                this.multiselectRef
                ]?.$el.querySelector(".multiselect-dropdown");
            if (multiselectDropdown) {
                multiselectDropdown.addEventListener(
                    "scroll",
                    async (event) => {
                        const { scrollTop, scrollHeight, clientHeight } =
                            event.target;
                        if (scrollHeight - scrollTop <= clientHeight) {
                            this.$emit("next-page", true);
                            await this.loadMoreItems(multiselectDropdown);
                        }
                    }
                );
            }
        },
        async getNextPage(nextPage, searchQuery = "") {
            if (!nextPage || this.loading) {
                return;
            }

            this.hasNextPage = false;

            if (searchQuery) {
                nextPage += `&${this.querySearchKey}=${searchQuery}`;
            }

            try {
                const res = await axios.get(nextPage);

                let handledFormatedData = this.handleResponseData(res);

                this.optionsData = this.optionsData.concat(handledFormatedData);

                // if(!this.groups){
                //     this.optionsData = this.optionsData.concat(handledFormatedData);
                // } else {
                //     this.optionsData = handledFormatedData;
                // }

                this.optionsData = _.uniqBy(
                    this.optionsData,
                    (e) => e[this.valueIndicator]
                );
                if (this.externalOptiosMod) {
                    this.optionsData = this.optionsData.concat(this.externalOptiosMod(this.optionsData));
                }
                // Handle pagination
                if (res.data.next_page_url) {
                    this.nextPage = res.data.next_page_url;
                    this.hasNextPage = true;
                } else {
                    this.nextPage = null;
                    this.hasNextPage = false;
                }
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.$nextTick(() => {
                    this.removeSpinners();
                });
            }
        },

        handleClear() {
            setTimeout(() => {
                if (this.$refs[this.multiselectRef]) {
                    this.$refs[this.multiselectRef].open();
                    document.addEventListener("click", this.handleClickOutside);
                }
            }, 100);
            this.$emit("update:val", this.mode === 'tags' ? [] : null);
        },
        handleClickOutside(event) {
            const isClickInsideComponent = this.$el.contains(event.target);
            if (!isClickInsideComponent) {
                this.$refs[this.multiselectRef]
                    ? this.$refs[this.multiselectRef].close()
                    : null;
                document.removeEventListener("click", this.handleClickOutside);
            }
        },
    },
    computed: {
        computeOptions() {
            if (this.apiUrl || this.groups) {
                return this.optionsData;
            }
            return this.uniqueSelectedOptions;
        },
        value: {
            get() {
                return this.mode === "tags" && !Array.isArray(this.val)
                    ? [this.val]
                    : this.val;
            },
            set(value) {
                this.$emit("update:val", value);
            },
        },
        uniqueSelectedOptions() {
            const idMap = new Map();
            (this.activeOptions || [])
                .concat(this.optionsData || [])
                .forEach((obj) => {
                    idMap.set(obj.id, obj);
                });
            return [...idMap.values()];
        },
        labelValue() {
            if (this.required) {
                return (
                    this.label +
                    '<span class="ms-1 input-required required">*</span>'
                );
            } else {
                return this.label;
            }
        },
        imagePreview() {
            if (
                Array.isArray(this.optionsData) &&
                this.optionsData.length !== 0 &&
                (this.optionsData.some((x) => x?.img) ||
                    this.optionsData.some((x) => x?.image))
            ) {
                return true;
            }
            return false;
        },
        isCustomFieldPasses() {
            return Boolean(
                this.$slots.afterlist ||
                this.$slots.beforelist ||
                this.$slots.noresults ||
                this.$slots.nooptions
            );
        },
        getMultiselectBinds() {
            const options = {
                "preselect-first": true,
                searchable: this.searchable,
                "allow-empty": false,
                canDeselect: false,
                placeholder: this.placeholder
                    ? this.placeholder
                    : this.translations["Select"],
                options: this.computeOptions,
                canClear: this.canClear,
                loading: this.loading,
                disabled: this.isSelectDisabled,
                groups: this.groups,
                mode: this.mode,
                createOption: this.createOption,
                "close-on-deselect": true,
                "resolve-on-load": this.resolveOnLoad,
                noResultsText: this.isCustomFieldPasses
                    ? null
                    : this.searchText,
                noOptionsText: this.isCustomFieldPasses
                    ? null
                    : this.searchText,
                minChars: this.minChars,
                valueProp: this.valueIndicator,
                label: this.labelIndicator,
                hideSelected: this.hideSelected,
                clearOnSelect: this.clearOnSelect,
                filterResults: this.apiUrl
                    ? this.ignoreFilter
                    : this.filterResults,
                max: this.max,
                clearOnBlur: this.clearOnBlur,
            };

            return options;
        },
    },
    emits: [
        "get-search-query",
        "update:val",
        "open-options",
        "load-more-items",
        "options",
        "selected-item",
        "next-page",
        "update:isLoading",
        "deselect",
        "keyup",
    ],
};
</script>

<style lang="scss" scoped>
.err-empty-field {
    border: 1px solid red;
}
.multiselect-single-label {
    span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.multiselect.is-disabled {
    color: #cbcbcb;
}
</style>
