<template>
    <b-row :class="single ? containerClasses : ''">
        <b-col v-if="label" class="d-flex align-items-center pe-1">
            <label class="label-text m-0" v-html="label"></label>
        </b-col>
        <b-col
            :class="[
                !single ? 'd-flex flex-md-row align-items-center gap-2' : 'correct-width',
                flexWrap ? 'flex-wrap' : '', customClass,
                error ? 'err-empty-field' : '',
            ]"
            class="position-relative"
        >
            <span>
                <!-- time-picker-inline -->
                <date-picker
                    v-model:open="openStart"
                    v-model:value="start"
                    :default-value="new Date()"
                    :disabled="disabled"
                    :format="computeFormat"
                    :lang="serverSettings('language_cp')"
                    :placeholder="placeholderStart || translations['Select date']"
                    :show-time-panel="showStart"
                    :type="type"
                    :value-type="valueType"
                    @change="handleClose"
                    @close="showStart = false"
                >
                    <template v-if="time" #footer>
                        <button
                            class="mx-btn mx-btn-text d-flex align-items-center justify-content-center w-100 fs-5"
                            @click="showStart = !showStart"
                        >
                            <i v-if="!showStart" class="far fa-clock"></i>
                            <i v-else class="far fa-calendar-edit"></i>
                        </button>
                    </template>
                </date-picker>
                <div
                    v-if="error && showError"
                    :class="error ? 'show-error-text' : 'hide-error-text'"
                    class="help-block-error transition-show-error"
                >
                    <span v-html="error"></span>
                    <i
                        class="fal fa-times close-error-message"
                        @click="showError = false"
                    ></i>
                </div>
            </span>

            <div v-if="!single" class="d-flex flex-column position-relative" 
                :class="{'err-empty-field': error}"
            >
                <div class="d-flex flex-row flex-nowrap gap-2">
                <span
                    class="d-flex align-items-center"
                    v-html="translations['to']"
                ></span>
                    <!-- time-picker-inline -->
                    <span class="">
                        <date-picker
                            v-model:open="openEnd"
                            v-model:value="end"
                            :disabled="computeDisabled"
                            :format="computeFormat"
                            :lang="serverSettings('language_cp')"
                            :placeholder="placeholderEnd || translations['Select date']"
                            :show-time-panel="showEnd"
                            :type="type"
                            :value-type="valueType"
                            @change="handleClose"
                            @close="showEnd = false"
                        >
                            <template v-if="time" #footer>
                                <button
                                    class="mx-btn mx-btn-text d-flex align-items-center justify-content-center w-100 fs-5"
                                    @click="showEnd = !showEnd"
                                >
                                    <i v-if="!showEnd" class="far fa-clock"></i>
                                    <i v-else class="far fa-calendar-edit"></i>
                                </button>
                            </template>
                        </date-picker>
                        <div
                             v-if="errorSecond && showError"
                             :class="errorSecond ? 'show-error-text' : 'hide-error-text'"
                             class="help-block-error transition-show-error"
                         >
                            <span v-html="errorSecond"></span>
                            <i
                                class="fal fa-times close-error-message"
                                @click="showError = false"
                            ></i>
                        </div>
                    </span>
                </div>
            </div>
            <div v-if="!single" class="d-flex align-items-top">
                <CheckboxComponent
                    v-if="!reverse"
                    v-model:value="expire"
                    :label="translations['No expiration']"
                    label-classes="text-nowrap"
                />
                <CheckboxComponent
                    v-else
                    v-model:value="active_to"
                    :label="translations['Active till']"
                    label-classes="d-flex align-items-center text-nowrap"
                    @update:value="(value) => !value ? end = null : null"
                />
            </div>
        </b-col>
        <b-col v-if="helpBlock" class="col-12 help-block mt-2" v-html="helpBlock"></b-col>
    </b-row>
</template>
<script>
import DatePicker from '@js/DatePicker';

import CheckboxComponent from "@components/Form/CheckboxComponent";

export default {
    name: "TimePeriod",
    components: {
        DatePicker,
        CheckboxComponent,
    },
    props: {
        startDate: {
            required: true,
            default: "",
        },
        endDate: {
            required: false,
            default: "",
        },
        noExpire: {
            required: false,
        },
        activeTill: {
            required: false,
        },
        reverse: {
            required: false,
            default: false,
        },
        format: {
            required: false,
            default: null,
        },
        valueType: {
            required: false,
            default: "format",
        },
        time: {
            type: Boolean,
            required: false,
            default: false,
        },
        error: {
            required: false,
            default: null,
        },
        errorSecond: {
            required: false,
            default: null,
        },
        single: {
            default: false,
            type: Boolean,
            required: false,
        },
        customClass: {
            type: String,
            default: null,
        },
        label: {
            required: false,
            default: null,
            type: String,
        },
        disabled: {
            required: false,
            type: Boolean,
            default: false,
        },
        type: {
            required: false,
            type: String,
            default: "datetime",
        },
        helpBlock: {
            required: false,
            default: null,
            type: String,
        },
        containerClasses: {
            required: false,
            default: "py-2",
            type: String,
        },
        placeholderStart: {
            required: false,
            default: null,
            type: String,
        },
        placeholderEnd: {
            required: false,
            default: null,
            type: String,
        },
        flexWrap: {
            required: false,
            default: true,
            type: Boolean,
        },
        openStartProp: {
            required: false,
            default: false,
            type: Boolean,
        },
        openEndProp: {
            required: false,
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            start: this.startDate,
            end: this.endDate,
            expire: this.noExpire,
            active_to: this.activeTill,

            showStart: false,
            showEnd: false,

            openStart: false,
            openEnd: false,
            showError: true,

            translations: {
                "No expiration": this.$t("No expiration"),
                to: this.$t("to"),
                "Select date": this.$t("Select date"),
                "Active till": this.$t("Active till"),
            },
        };
    },
    methods: {
        handleClose(val, type) {
            if (type === "date" || type === "minute" || type === "time") {
                this.openStart = false;
                this.openEnd = false;
            }
        },
    },
    computed: {
        computeFormat() {
            if (this.time) {
                return (
                    this.format ||
                    this.serverSettings("format.dateTime").split("\\").join("")
                );
            }
            return this.format || this.serverSettings("format.date");
        },
        computeDisabled() {
            if (!this.reverse) {
                return this.disabled || this.expire;
            } else {
                return !this.active_to;
            }
        },
    },
    watch: {
        startDate(newVal) {
            this.start = newVal;
        },
        endDate(newVal) {
            this.end = newVal;
        },
        start(newVal) {
            this.$emit("update:startDate", newVal);
        },
        end(newVal) {
            this.$emit("update:endDate", newVal);
        },
        expire(newVal) {
            this.$emit("update:noExpire", newVal);
        },
        noExpire(newVal) {
            this.expire = newVal;
        },
        activeTill(newVal) {
            this.active_to = newVal;
        },
        active_to(newVal) {
            this.$emit("update:activeTill", newVal);
        },
        error(value) {
            if (value) {
                this.showError = true;
            }
        },
        openStartProp(value) {
            this.openStart = value;
        },
        openEndProp(value) {
            this.openEnd = value;
        },
        openStart(value) {
            this.$emit("update:openStartProp", value);
        },
        openEnd(value) {
            this.$emit("update:openEndProp", value);
        },
    },
    emits: [
        "update:startDate",
        "update:endDate",
        "update:noExpire",
        "update:activeTill",
        "update:openStartProp",
        "update:openEndProp",
    ],
};
</script>
<style>
.correct-width {
    padding-left: 0px !important;
    padding-right: 0 !important;
    margin-right: calc(var(--bs-gutter-x) * 0.5);
}

.correct-width .mx-datepicker {
    width: 100% !important;
}

.correct-width-size-edit .mx-datepicker {
    width: 190px !important
}
</style>
