<template>
    <div class="form-group position-relative">
        <label class="label-text mb-2" v-html="labelValue" v-if="labelText"></label>

<!--        <div v-if="initLoader" class="loading-text-editor">-->
<!--            <Loading :loading="initLoader"/>-->
<!--        </div>-->
        <textarea v-model="content" :placeholder="placeholder" :id="uid" class="d-none">
            {{ content }}
        </textarea>

        <div class="input-errors" v-for="error of errors" :key="error.$uid">
            <div class="help-block-error" v-html="error.$message"></div>
        </div>
        <div v-if="id" :id="`${this.id}_error`" class="help-block-error d-none"></div>
        <div
            v-if="error && showError"
            class="help-block-error"
            :class="error ? 'show-error-text' : 'hide-error-text'"
        >
            <span v-html="error"></span>
            <i class="fal fa-times close-error-message" @click="showError = false"></i>
        </div>

        <!-- Storage images modal-->
        <b-modal class="modal-xl modal-images" v-model="showImagesModal">
            <template #header>
                <b-row class="justify-content-between align-items-center w-100">
                    <b-col md="4" cols="6">
                        <input
                            :accept="extensions"
                            type="file"
                            ref="uploadFilesAdmin"
                            @change="handleUpload"
                            hidden
                            multiple
                        />
                        <b-button variant="primary"
                                  @click.prevent="() => this.$refs.uploadFilesAdmin.click()"
                                  :disabled="uploading"
                                  class="white-space-nowrap"
                        >
                            <span v-if="uploading"><b-spinner small></b-spinner> {{
                                    translations["Uploading..."]
                                }}</span>
                            <span v-else><i class="far fa-arrow-to-top"></i> {{ translations["Upload Image"] }}</span>
                        </b-button>
                    </b-col>
                    <b-col md="4" class="text-center d-none d-lg-block">
                        <h5 class="modal-title">{{ translations["Choose Image"] }}</h5>
                    </b-col>
                    <b-col md="4" cols="6" class="text-right">
                        <i @click="showImagesModal = false" class="fal fa-times fa-lg c-pointer"></i>
                    </b-col>
                </b-row>
            </template>
            <div>
                <ul class="choose-image-list" v-if="isLoaded">
                    <li v-for="image in images" :key="image.id"
                        :class="{choosen: choosenImage.id == image.id}"
                        @click="chooseImage(image)"
                    >
                        <figure>
                            <img :src="image?.url" :alt="image.name"/>
                            <figcaption :title="image?.name">{{ image?.name }}</figcaption>
                        </figure>
                    </li>
                </ul>
                <div class="text-center py-10" v-if="!isLoaded">
                    <Loading :loading="!isLoaded" class="app-loader-center"></Loading>
                </div>
            </div>

            <div class="text-right"
                 v-if="meta && allImageData && allImageData.total > allImageData.per_page && isLoaded"
            >
                <vue-awesome-paginate
                    class="pagination"
                    :total-items="allImageData.total"
                    :items-per-page="allImageData.per_page"
                    :max-pages-shown="5"
                    v-model="allImageData.current_page"
                    @click="handlePagination(allImageData.current_page)"
                />
            </div>
            <template #footer>
                <b-button variant="ghost" @click="showImagesModal = false">
                    {{ translations["Close"] }}
                </b-button>
                <b-button variant="primary" @click="handleChooseImage">
                    {{ translations["Confirm"] }}
                </b-button>
            </template>
        </b-modal>
        <ImageResizer v-model="resizeModal" :img-node="selectedImg" :translations="translations"/>
        <ContextMenu ref="menu" :model="contextMenuItems" appendTo="self" />
        <transition name="fade-tooltip">
            <div 
                v-if="visibleTooltip"
                class="tiny-img-tooltip"
                :style="{ top: `${position.top}px`, left: `${position.left}px` }"
            >
            {{translations['Double click or right click to resize image']}}
            </div>
        </transition>
    </div>
</template>

<script>
import {ref} from 'vue'

import Loading from "@components/Loading.vue";
import axios from "axios";
import {toast} from "@js/toast";
import Files from "./js/Files";
import "vue-awesome-paginate/dist/style.css";
import tinymce from 'tinymce/tinymce';
import 'tinymce/themes/silver'
import 'tinymce/icons/default'
import 'tinymce/models/dom'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/table'
import 'tinymce/plugins/code'
import 'tinymce/plugins/image'
import 'tinymce/plugins/link'
import 'tinymce/plugins/fullscreen'
import 'tinymce/plugins/media'
import 'tinymce/plugins/advlist'
import 'tinymce/plugins/preview'
import 'tinymce/plugins/autolink'
import 'tinymce/plugins/charmap'
import 'tinymce/plugins/anchor'
// import 'tinymce/plugins/quickbars'
import 'tinymce/plugins/visualblocks'
import 'tinymce/plugins/insertdatetime'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/directionality'
import 'tinymce/plugins/codesample'
import 'tinymce/plugins/pagebreak'
import 'tinymce/plugins/save'
import 'tinymce/plugins/nonbreaking'
import 'tinymce/plugins/importcss'
import 'tinymce/plugins/accordion'
import 'tinymce/plugins/autosave'
import 'tinymce/plugins/visualchars'
import 'tinymce/plugins/help'
import 'tinymce/plugins/searchreplace'
import 'tinymce/plugins/image'
// import 'tinymce/plugins/editimage'
import "tinymce/skins/ui/tinymce-5/skin.min.css"
import "tinymce/plugins/link";

import './../../../tinymce/langs/bg_BG.js'
import './../../../tinymce/langs/de.js'
import './../../../tinymce/langs/el.js'
import './../../../tinymce/langs/es.js'
import './../../../tinymce/langs/ro.js'
import './../../../tinymce/langs/fr_FR.js'

import ImageResizer from './Helpers/ImageResizer/ImageResizer.vue'
import ContextMenu from 'primevue/contextmenu';

export default {
    name: "TextEditor",
    components: {
        Loading,
        ImageResizer,
        ContextMenu
    },
    setup(){
        const menu = ref()

        const showContextMenu = (event) => {
            menu.value.show(event);
        };

        const closeContextMenu = event =>{
            menu.value.hide(event)
        }

        return {
            menu,
            showContextMenu,
            closeContextMenu
        }
    },
    props: {
        // [true | false]
        enable: {
            type: Boolean,
            required: false,
            default: true,
        },
        // [true | false]
        readonly: {
            type: Boolean,
            required: false,
            default: false,
        },
        placeholder: {
            type: String,
            required: false,
            default: null,
        },
        // ["snow" | "bubble" | ""]
        theme: {
            required: false,
            default: "snow",
        },
        toolbar: {
            type: String,
            default: "undo redo blocks fontfamily fontsize fontsizeinput bold italic underline blockquote strikethrough align numlist bullist link customImageUpload resizeImage table media lineheight forecolor backcolor removeformat code pagebreak anchor fullscreen",
        },
        value: {
            required: false,
        },
        labelText: {
            required: false,
        },
        error: {
            required: false,
        },
        errors: {
            required: false,
        },
        id: {
            required: false,
        },
        required: {
            type: Boolean,
            required: false,
        },
        tabIndex: {
            type: Number,
            required: false,
            default: 0
        },
        height: {
            type: [Number, String],
            default: 300
        }
    },
    data() {
        return {
            content: this.value,
            initContent: "",
            model: new Files(),
            showError: true,
            initLoader: true,
            uid: "editorid" + Math.random().toString(16).slice(2),
            language: this.serverSettings('language_cp') || 'en',
            showImagesModal: false,
            allImageData: {},
            images: [],
            isLoaded: false,
            selectedImg: null,
            resizeModal: false,
            visibleTooltip: false,
            position: {
                top: 0,
                left: 0
            },
            translations: {
                "Choose Image": this.$t("Choose Image"),
                'Resize Image': this.$t('Resize Image'),
                "Upload Image": this.$t("Upload Image"),
                "Uploading...": this.$t("Uploading..."),
                'Please select an image first.': this.$t('Please select an image first.'),
                "Your file size is bigger than the allowed size.":
                    this.$t("Your file size is bigger than the allowed size."),
                "Confirm": this.$t("Confirm"),
                "Close": this.$t("Close"),
                "Please choose image": this.$t("Please choose image"),
                "Add image from storage": this.$t("Add image from storage"),
                "{file_name} successfully uploaded": this.$t("{file_name} successfully uploaded"),
                 'Confirm': this.$t('Confirm'),
                'Close': this.$t('Close'),
                'Resize Image': this.$t('Resize Image'),
                'Width': this.$t('Width'),
                'Height': this.$t('Height'),
                'Please enter valid dimensions.': this.$t('Please enter valid dimensions.'),
                'Double click or right click to resize image': this.$t('Double click or right click to resize image'),
            },
            uploading: false,
            choosenImage: {},
            meta: null,
            page: 1,
            fieldName: null,
            filePickerCallback: () => {
            },
            langMapping: [
                // Download tinymce translations from https://cdn.tiny.cloud/1/no-origin/tinymce/7.5.1-116/langs/bg_BG.js
                {lang: "en", value: "en"},
                {lang: "bg", value: "bg_BG"},
                {lang: "el", value: "el"},
                {lang: "de", value: "de"},
                {lang: "es", value: "es"},
                {lang: "fr", value: "fr_FR"},
            ],
        }
    },
    computed: {
        contextMenuItems(){
            return [
                { label: this.translations['Resize Image'],
                    command: () => {
                        this.selectedImg ? this.resizeModal = true : null
                    }
                },
            ]
        },
        editorHeight() {
            return this.height + 'px';
        },
        labelValue() {
            if (this.required) {
                return this.labelText + '<span class="ms-1 input-required required">*</span>';
            } else {
                return this.labelText;
            }
        },
        extensions() {
            if (this.meta && this.meta.config && this.meta.config.extensions) {
                return this.meta.config.extensions
                    .split(",")
                    .map((x) => `.${x}`)
                    .join(",");
            }
            return null;
        },
    },
    methods: {
        async initEditor() {
            this.initLoader = true;
            let self = this;

            const tinyMCEBaseURL = process.env.NODE_ENV === 'production'
                ? '/admin/vuejs/tinymce'
                : '/admin/vuejs-dev/tinymce';

            await tinymce.init({
                base_url: tinyMCEBaseURL,
                suffix: '.min',
                verify_html: false,
                selector: '#' + self.uid,
                license_key: 'gpl',
                branding: false,
                plugins: 'image preview importcss searchreplace autolink autosave save directionality code visualblocks visualchars fullscreen link media codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount help charmap accordion',
                editimage_cors_hosts: ['picsum.photos'],
                object_resizing: 'img',
                menubar: false,
                toolbar: self.toolbar,
                font_size_formats: '8pt 9pt 10pt 11pt 12pt 13pt 14pt 16pt 18pt 20pt 24pt 28pt 32pt 36pt 48pt 72pt',
                // Allow script tags with type, src, language, class, and JSON content
                extended_valid_elements: 'img[class|src|alt|title|width|height|style],script[type|src|language|class|id|async|defer],style,link[href|rel],div,span[title|*]',
                custom_elements: 'script,style,link,~link',
                valid_children: '+body[script|style|link]',
                // Prevent TinyMCE from removing script tags with application/ld+json
                protect: [
                    /\<script type="application\/ld\+json"\>[\s\S]*?\<\/script\>/g
                ],
                autosave_ask_before_unload: true,
                autosave_interval: '30s',
                autosave_prefix: '{path}{query}-{id}-',
                autosave_restore_when_empty: false,
                autosave_retention: '2m',
                image_advtab: true,
                image_dimensions: true,
                resize: true,
                inline_styles: true,
                link_list: [],
                image_list: [],
                image_class_list: [
                    {title: 'Responsive', value: 'img-fluid'}
                ],
                importcss_append: true,
                statusbar: false,
                file_picker_callback: (callback, value, meta) => {
                    /* Provide image and alt text for the image dialog */
                    if (meta.filetype === 'image') {
                        self.fieldName = meta.fieldname
                        self.filePickerCallback = callback;
                        self.getData();
                    }
                },
                height: this.height,
                image_caption: true,
                quickbars_selection_toolbar: false,
                noneditable_class: 'mceNonEditable',
                toolbar_mode: 'wrap',
                contextmenu: false,
                skin: false,
                content_css: '/assets/tinymce/tinymceTable.css',
                content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:16px }',
                language_url: `./../../../tinymce/langs/${self.language}.js`,
                language: self.language,
                language_fallback: 'en',
                setup: function (editor) {
                    editor.on('init', (e) => {
                        // setTimeout(() => {
                        //     if (self.value) {
                        //         editor.setContent(self?.value);
                        //         self.initContent = _.cloneDeep(self?.value)
                        //     }
                        // }, 1000);
                    });

                    editor.on('change blur keyup', function (e) {
                        // self.$emit('update:value', editor.getContent());
                        // self.$emit('update-text', self.removeHTMLTags(editor.getContent()));
                        self.content = editor.getContent();
                        // tinymce.triggerSave();
                    });

                    //add context menu
                    editor.on('contextmenu', function (event) {
                        const target = event.target;
                        if (target.tagName === 'IMG') {
                            event.preventDefault();
                            self.selectedImg = editor.selection.getNode();
                            self.showContextMenu(event)
                        } else {
                            self.selectedImg = null;
                            self.closeContextMenu(event)
                        }
                    });

                    //add double click event to open image resize modal
                       editor.on('dblclick', function (event) {
                        const target = event.target;
                        if (target.tagName === 'IMG') {
                            event.preventDefault();
                            self.selectedImg = editor.selection.getNode();
                            self.resizeModal = true;
                        } 
                    });
                    editor.on('click', function(event){
                        self.closeContextMenu(event)
                    })
                      
                    // show tooltip on mouseover
                    editor.on('mousemove', (e) => {
                       if (e.target.nodeName === 'IMG') {
                            const rect = e.target.getBoundingClientRect();
                            self.position = {
                              top: e.clientY + window.scrollY,
                              left: e.clientX + window.scrollX,
                            };
                            self.visibleTooltip = true;
                        }
                     });
                    // hide tooltip on mouseout
                     editor.on('mouseout', (e) => {
                       if (e.target.nodeName === 'IMG') {
                            self.visibleTooltip = false;
                         
                       }
                    });

                    // Add custom customImageUpload button
                    editor.ui.registry.addButton('customImageUpload', {
                        tooltip: self.translations["Add image from storage"],
                        icon: 'image',
                        onAction: function () {
                            self.fieldName = null;
                            self.getData();
                        }
                    });

                    // Image resizer
                    editor.ui.registry.addButton('resizeImage', {
                        tooltip: 'Resize Image',
                        icon: 'resize',
                        onAction: function () {
                            self.selectedImg = editor.selection.getNode();

                            if (self.selectedImg && self.selectedImg.nodeName === 'IMG') {
                                self.resizeModal = true;
                            } else {
                                toast.error(self.translations['Please select an image first.']);
                            }
                        }
                    })
                }
            });
            this.initLoader = false;
        },
        async handleUpload(e) {
            this.uploading = true;

            const files = e.target.files;
            const allowedFileSize = parseFloat(this.meta?.config?.max_size?.split?.("mb")[0]);
            let file = {}

            for (let i = 0; i < files.length; i++) {
                file = files[i];
                let size = file.size;
                let mbSize = size / (1024 * 1024);

                if (mbSize > allowedFileSize) {
                    toast.error(
                        this.translations[
                            "Your file size is bigger than the allowed size."
                            ]
                    );
                    continue;
                }

                try {
                    const formData = new FormData();
                    formData.append("name", file.name);
                    formData.append("file", file);

                    let data = await this.model.post("upload", formData, {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    });
                    if (data.status === "error") {
                        toast.error(this.translations[data.msg] || data.msg);
                    }
                } catch (error) {
                    this.$errorResponse(error);
                }
            }
            await this.getData(1);

            this.$refs.uploadFilesAdmin.value = "";
            this.uploading = false;
            toast.success(this.$trp(this.translations['{file_name} successfully uploaded'], {file_name: file.name}));
        },
        chooseImage(image) {
            this.choosenImage = image;
        },
        handleChooseImage() {
            if (this.choosenImage) {
                const editor = tinymce.get(this.uid)
                if (editor) {
                    this.showImagesModal = false;
                    if (this.filePickerCallback && this.fieldName) {
                        this.filePickerCallback(this.choosenImage.url, {alt: this.choosenImage.name});
                    } else {
                        // Fix: Insert image with width and height attributes for image_dimensions to work
                        const img = new window.Image();
                        img.src = this.choosenImage.url;
                        img.onload = () => {
                            editor.execCommand(
                                'mceInsertContent',
                                false,
                                `<img src="${this.choosenImage.url}" alt="${this.choosenImage.name}" width="${img.width}" height="${img.height}">`
                            );
                            editor.windowManager.close();
                        };
                        // If image fails to load, insert without dimensions
                        img.onerror = () => {
                            editor.execCommand(
                                'mceInsertContent',
                                false,
                                `<img src="${this.choosenImage.url}" alt="${this.choosenImage.name}">`
                            );
                            editor.windowManager.close();
                        };
                    }
                }
            } else {
                toast.error(this.translations["Please choose image"]);
            }
        },
        getData(page = null) {
            this.showImagesModal = true;
            this.isLoaded = false;
            axios.get(`/admin/api/core/settings/files?filters[dir][operator]=in&filters[dir][value]=image&perpage=16&page=${page ? page : '1'}`)
                .then((response) => {
                    this.images = response.data.data;
                    this.allImageData = response.data;
                    this.meta = response.data.meta;
                }).catch((err) => {
                this.$errorResponse(err)
            })
                .finally(() => {
                    this.isLoaded = true;
                });
        },
        handlePagination(page) {
            this.page = page;
            this.getData(this.page);
        },
        setLanguage() {
            this.langMapping.map((lang) => {
                if (lang.lang === this.serverSettings('language_cp')) {
                    this.language = lang.value;
                }
            })
        },
        removeHTMLTags(str) {
            return (str || '').replace(/<\/?[^>]+(>|$)/g, "");
        }
    },
    watch: {
        content(newValue) {
            const editor = tinymce.get(this.uid);
            if (editor && editor.getContent() !== newValue && newValue) {
                editor.setContent(newValue);
                this.$emit('update-text', this.removeHTMLTags(editor.getContent()));
            } else if (editor && !editor.getContent()) {
                editor.setContent('<p></p>');
                this.$emit('update-text', '');
            }
            this.$emit('update:value', newValue)
            tinymce.triggerSave();
        },
        value: {
            handler(newValue, oldValue) {
                this.content = newValue;
                if(!newValue){
                    const editor = tinymce.get(this.uid);
                    if (editor) {
                        editor.setContent('');
                    }
                }
            }
        },
        error(value) {
            if (value) {
                this.showError = true;
            }
        },
        tabIndex() {
            if (typeof this.value !== 'string') {
                return
            }
            let mce = tinymce.get(this.uid);
            if (mce && mce.getContent()) {
                mce.setContent(this.value);
            }
        }
    },
    created() {
        this.setLanguage();
    },
    async mounted() {
        await this.initEditor();
    },
    beforeUnmount() {
        tinymce.remove(`#${this.uid}`);
    },
    emits: ["update-text", "update:value"],
};
</script>

<style lang="scss">
/* images modal */
.modal.modal-images {
    --bs-modal-zindex: 3000;
    z-index: var(--bs-modal-zindex);

    .modal-body {
        min-height: 40rem;
    }
}

.choose-image-list {
    padding-left: 0;
    @media (max-width: 1023px) {
        display: flex;
        flex-flow: wrap;
        gap: 0.5rem;
    }

    & > li {
        background-color: var(--Color-Surface-Neutral---cc-color-bg-light, #fafaff);
        border: 0.25rem solid transparent;
        border-radius: 0.25rem;
        display: inline-block;
        width: 14.6%;
        padding-bottom: 14.6%;
        margin: 0 0 2% 2%;
        text-align: center;
        cursor: pointer;
        vertical-align: top;
        overflow: hidden;
        position: relative;
        transition: border-color .2s;

        @media (max-width: 1023px) {
            width: calc(33.33% - 0.5rem);
            margin: 0;
        }

        @media (max-width: 767px) {
            width: calc(50% - 0.5rem);
        }

        &.choosen {
            border-color: #8d58e0;
        }

        img {
            max-width: 100%;
            max-height: 100%;
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
        }

        figcaption {
            background-color: rgba(0, 0, 0, 0.7);
            color: #fff;
            line-height: 1.2;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 3px 5px;
            z-index: 1;
            word-break: break-word;
            -webkit-transition: .2s;
            transition: .2s;
            -webkit-transform: translate(0, calc(100% + 1px));
            transform: translate(0, calc(100% + 1px));

            @media (max-width: 767px) {
                display: none;
            }
        }

        figure {
            &:hover {
                figcaption {
                    -webkit-transform: translate(0, 0);
                    transform: translate(0, 0);
                }
            }
        }
    }
}

/* Pagination */
.pagination-container {
    display: flex;

    > li:not(:last-child) {
        .paginate-buttons {
            border-right: 0;
        }
    }
}

.paginate-buttons {
    font-size: var(--bs-pagination-font-size, 14px);
    height: 2.188rem;
    width: 2.188rem;
    cursor: pointer;
    background-color: var(--Color-Surface-Neutral---cc-color-bg-light, #fafaff);
    border: var(--bs-pagination-border-width) solid var(--bs-pagination-border-color);
    color: var(--bs-pagination-color, #949598);
}

.paginate-buttons:hover {
    z-index: 2;
    color: var(--bs-pagination-hover-color);
    background-color: var(--bs-pagination-hover-bg);
    border-color: var(--bs-pagination-hover-border-color);
}

.active-page {
    background-color: var(--bs-pagination-active-bg, #aeb1cd);
    border: var(--bs-pagination-border-width) solid var(--bs-pagination-border-color);
    color: white;
}

.active-page:hover {
    background-color: var(--bs-pagination-active-bg, #aeb1cd);
}

.pagination-container > li:first-child .paginate-buttons {
    border-top-left-radius: var(--bs-pagination-border-radius);
    border-bottom-left-radius: var(--bs-pagination-border-radius);
}

.pagination-container > li:last-child .paginate-buttons {
    border-top-right-radius: var(--bs-pagination-border-radius);
    border-bottom-right-radius: var(--bs-pagination-border-radius);
}

@media (max-width: 767px) {
    .paginate-buttons {
        &.number-buttons {
            display: none;
        }
    }
}
.loading-text-editor {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.tiny-img-tooltip{
    position: absolute;
    z-index: 9999;
    pointer-events: none;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    max-width: 150px;
}
.fade-tooltip-enter-active, .fade-tooltip-leave-active {
    transition: opacity 0.3s ease;
}
.fade-tooltip-enter, .fade-tooltip-leave-to {
    opacity: 0;
}
</style>
