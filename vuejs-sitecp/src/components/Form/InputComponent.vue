<template>
    <b-row :class="[noMargin ? '' : 'my-3', inputComponentClasses]">
        <b-col cols="12">
            <div
                :class="layoutClass"
                class="form-group d-md-flex justify-content-between"
            >
                <div v-if="labelText || tooltipText" :class="{ 'pe-md-3': !columnStyle }">
                    <label
                        v-if="tooltip"
                        class="col-md-8"
                        :class="[{ 'mb-md-0': !columnStyle }, labelClasses]"
                    >
                        <tooltip-label
                            :tooltip-text="tooltipText"
                            :label-text="labelText"
                            :label-classes="labelClasses"
                            :icon="icon"
                        ></tooltip-label>
                        <span v-if="required" class="ms-1 input-required required">*</span>
                    </label>
                    <label
                        v-else
                        :class="[{ 'mb-md-0': !columnStyle }, labelClasses]"
                    >
                        <span v-html="labelText"></span>
                        <span v-if="required" class="ms-1 input-required required">*</span>
                    </label>
                    <p class="help-block mb-0" v-if="helpText" v-html="helpText"></p>
                </div>
                <b-col
                    :class="!columnStyle ? `col-md-${columnSize}` : 'col-md-12'"
                    style="position: relative"
                >
                    <b-input-group
                        :prepend="unitPositionValue === 'left' ? unitValue : false"
                        :append="unitPositionValue === 'right' ? unitValue : false"
                        class="bs-input-group"
                        :class="[colored ? 'purple-unit' : '', {'flex-nowrap': $slots.button_addon_left || $slots.button_addon}, {'animate-input--trigger': animateInput}]"
                    >
                        <slot name="button_addon_left"></slot>
                        <input
                            v-model="value"
                            :id="inputId"
                            :type="inputType"
                            :step="type === 'number' && step ? step : ''"
                            :placeholder="placeholder"
                            :min="min"
                            :max="max"
                            :readonly="readonly"
                            :class="resolveClass"
                            :disabled="isDisabled"
                            :required="required"
                            :pattern="type === 'number' ? '^[0-9]*$' : null"
                            ref="inputElement"
                            class="form-control input-component-input transition-show-error"
                            :style="{
                                'border-radius': type === 'number' && !unitValue && !$slots.button_addon_left ? '6px' : '',
                                'border-radius': $slots.button_addon_left ? '0px 6px 6px 0px' : '',
                            }"
                            @keyup="(val) => checkForMinMax(val, 1500)"
                            @keydown="handleNumberIncrementAndDecrementWithArrows"
                            @blur="(val) => {checkForMinMax(val); checkIfNumberIsValidAndCorrect(val)}"
                            @keypress.enter="() => $emit('keypress-enter', value)"
                            autocomplete="off"
                        />

                        <div
                            v-if="type === 'number' && !unitValue && showIncrementDecrement"
                            class="decrement-increment-buttons"
                            :class="{
                                'unit-and-arrows': unitPositionValue === 'right' && unitValue,
                            }"
                            ref="inputUnit"
                        >
                            <button
                                class="decrement"
                                :disabled="isDisabled"
                                @click="decrement(value)"
                            >
                                -
                            </button>
                            <span>|</span>
                            <button
                                class="increment"
                                :disabled="isDisabled"
                                @click="increment(value)"
                            >
                                +
                            </button>
                        </div>
                        <slot name="button_addon"></slot>
                    </b-input-group>
                    <a
                        v-if="type === 'password'"
                        class="toggle-password"
                        href="javascript;;"
                        @click.prevent="
                            () =>
                                (inputType =
                                    inputType === 'password' ? 'text' : 'password')
                        "
                    >
                        <i
                            :class="
                                inputType === 'password'
                                    ? 'fal fa-eye-slash'
                                    : 'fal fa-eye'
                            "
                            :style="{
                                color: inputType === 'password' ? '#7a7d84' : '#3d414d',
                                fontWeight: '400',
                            }"
                        ></i>
                    </a>
                    <div class="input-errors" v-if="errors && errors.length > 0 && showError" v-for="error of errors" :key="error.$uid">
                        <div class="help-block-error">
                            {{ error.$message }}
                            <i class="fal fa-times close-error-message" @click="showError = false"></i>
                        </div>
                    </div>
                    <div
                        v-if="id"
                        :id="`${this.id}_error`"
                        class="help-block-error d-none"
                    ></div>
                    <!-- <div v-if="helpBlock" class="help-block" v-html="helpBlock"></div> -->
                    <div
                        class="help-block-error transition-show-error"
                        :class="error ? 'show-error-text' : 'hide-error-text'"
                        v-if="error && showError"
                    >
                        <span v-html="error"></span>
                        <i class="fal fa-times close-error-message" @click="showError = false"></i>
                    </div>
                    <div class="help-block-error" v-if="minMaxError && showError">
                        <span v-html="minMaxError"></span>
                        <i class="fal fa-times close-error-message" @click="showError = false"></i>
                    </div>
                    <div v-if="helpBlockInput || $slots.helpBlock" class="help-block">
                        <slot name="helpBlock"></slot>
                        {{ helpBlockInput }}
                    </div>
                </b-col>
            </div>
            <div v-if="helpBlock" class="help-block" :class="helpBlockClasses" v-html="helpBlock"></div>
        </b-col>
    </b-row>
</template>

<script>
import TooltipLabel from "@components/Apps/Erp/TooltipLabel";
import { disableAutocomplete } from "@js/disableAutocomplete";
import Inputmask from "inputmask";

export default {
    components: { TooltipLabel },
    data() {
        return {
            unitWidth: 0,
            value: this.modelValue,
            inputType: this.type,
            minMaxError: null,
            translations: {
                "The minimum value is {min}, it will be automatically convert to {min}": this.$t("The minimum value is {min}, it will be automatically convert to {min}"),
                "The maximum value is {max}, it will be automatically convert to {max}": this.$t("The maximum value is {max}, it will be automatically convert to {max}"),
            },
            unitValue: this.unit,
            unitPositionValue: this.unit_position,
            inputId: this.id || this.aggregateId(),
            showError: true,
            animateInput: false,
        };
    },
    props: {
        modelValue: {
            required: false,
        },
        isDisabled: {
            type: Boolean,
            required: false,
        },
        labelText: {
            type: String,
            required: false,
        },
        id: {
            type: String,
            required: false,
        },
        columnStyle: {
            type: Boolean,
            required: false,
        },
        columnSize: {
            type: Number,
            required: false,
            default: 6,
        },
        inputClasses:{
            default: '',
            type: String,
            required: false
        },
        placeholder: {
            type: String,
            required: false,
        },
        tooltip: {
            type: Boolean,
            required: false,
        },
        tooltipText: {
            type: String,
            required: false,
        },
        helpText: {
            type: String,
            required: false,
        },
        icon: {
            type: String,
            required: false,
            default: "far fa-info-circle",
        },
        type: {
            type: String,
            required: false,
        },
        min: {
            type: Number,
            required: false,
            default: 0,
        },
        max: {
            type: Number,
            required: false,
        },
        step: {
            type: Number,
            required: false,
        },
        error: {
            default: null,
            required: false,
        },
        errors: {
            type: Array,
            required: false,
        },
        readonly: {
            type: Boolean,
            required: false,
        },
        unit: {
            type: String,
            required: false,
            default: null,
        },
        unit_position: {
            type: String,
            required: false,
            default: "right",
        },
        unitStyle: {
            type: Object,
            required: false,
            default: {},
        },
        noMargin: {
            required: false,
            default: false,
        },
        colored: {
            type: Boolean,
            required: false,
            default: false,
        },
        helpBlock: {
            required: false,
            default: null,
            type: String,
        },
        helpBlockInput: {
            required: false,
            default: null,
            type: String,
        },
        showIncrementDecrement: {
            type: Boolean,
            required: false,
            default: true,
        },
        required: {
            type: Boolean,
            required: false,
        },
        focusOnMount: {
            type: Boolean,
            required: false,
            default: false,
        },
        animationTrigger: {
            required: false,
        },
        labelClasses: {
            type: String,
            required: false,
            default: "label-text",
        },
        helpBlockClasses: {
            type: String,
            required: false,
            default: "",
        },
        inputComponentClasses: {
            type: String,
            required: false,
            default: "",
        },
        digits:{
            required: false,
            type: Number,
            default: null,
        }
    },
    computed: {
        resolveClass() {
            let inputClass = this.inputClasses;
            this.unitValue && this.unitPositionValue !== "right"
                ? (inputClass += " border-6")
                : (inputClass += "");
            this.showError && (this.error || this.errors?.length > 0)
                ? (inputClass += " err-empty-field")
                : (inputClass += "");
            this.showIncrementDecrement && !this.unit ? (inputClass += " with-increment-decrement") : (inputClass += "");
            return inputClass;
        },
        layoutClass() {
            let arr = [];
            this.columnStyle ? arr.push("column-style") : null;
            arr.push(this.helpText ? "align-items-start" : "align-items-center");
            return arr;
        },
    },
    methods: {
        handleNumberIncrementAndDecrementWithArrows(event) {
            if (["ArrowUp", "ArrowDown"].includes(event.key) && this.type === "number") {
                event.preventDefault();
            }
        },
        aggregateId() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let result = 'input-';
            for (let i = 0; i < chars.length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        },
        calculateUnitWidth() {
            const unitElement = this.$refs.unit;
            if (unitElement) {
                this.unitWidth = unitElement.clientWidth + 10;

                const inputElement = this.$refs.inputElement;
                if (inputElement) {
                    inputElement.style.paddingLeft = `${this.unitWidth}px`;
                }
            }
        },
        increment(val) {
            let currentValue = val;
            if (isNaN(currentValue)) {
                currentValue = 0.0;
            }
            this.value = parseFloat(currentValue) + 1;
            this.checkForMinMax(this.value);
        },
        decrement(val) {
            let currentValue = val;
            if (parseFloat(currentValue) > 0) {
                this.value = parseFloat(currentValue) - 1;
                this.checkForMinMax(this.value);
            }
        },
        checkIfNumberIsValidAndCorrect(ev){
            if(this.type !== 'number') return
            let value = ev?.target?.value || '';
            if (this.digits !== null) {
                const parts = value.toString().split(/[.,]/);
                const fraction = parts[1] || '';
                if (this.digits === 0 && parts.length > 1) {
                    value = parseInt(value);
                    this.value = value;
                } else if (fraction.length > this.digits) {
                    value = `${parts[0]}.${fraction.slice(0, this.digits)}`;
                    this.value = parseFloat(value);
                }
            }
        },
        checkForMinMax(value, countdown = 1500) {
            this.minMaxError = null;
            setTimeout(() => {
                if (this.type === 'number' && this.min !== null) {
                    let newValue = parseFloat(value?.target?.value || value);
                    if (newValue < this.min) {
                        this.value = this.min
                        this.minMaxError = this.$trp(
                            this.translations['The minimum value is {min}, it will be automatically convert to {min}'],
                            {min: this.min}
                        )
                        return;
                    }
                }

                if (this.type === 'number' && this.max !== null) {
                    let newValue = parseFloat(value?.target?.value || value);
                    if (newValue > this.max) {
                        this.value = this.max

                        this.minMaxError = this.$trp(
                            this.translations['The maximum value is {max}, it will be automatically convert to {max}'],
                            {max: this.max}
                        )
                        return;
                    }
                }
            }, countdown);

            // value?.target?.value ? this.value = value.target.value : null;
            this.$emit('blur', this.value)
            this.$emit('inputKeyup', this.value)
        },
    },
    mounted() {
        this.inputType = this.type || "text";
        if (this.unitValue && this.unitPositionValue === "left") {
            this.calculateUnitWidth();
        }
        disableAutocomplete(this.$refs.inputElement);

        var selector = document.getElementById(this.inputId);
        if(selector && this.inputType === 'number') {
            new Inputmask("number", {
                prefix: "",
                groupSeparator: '',
                alias: "numeric",
                digits: this.digits !== null ? this.digits : this.serverSettings('unit_system') === 'metric' ? 3 : 0,
                digitsOptional: !1,
                min: this.min,
                max: this.max,
            }).mask(selector);
        }
        setTimeout(()=>{
            if(this.focusOnMount) {
                this.$refs.inputElement.focus();
            }
        }, 300)
    },
    watch: {
        // modelValue(value) {
        //     if (this.type == "number" && this.min != null) {
        //         let newValue = parseFloat(value),
        //             newMin = parseFloat(this.min);
        //         if (newValue < newMin) {
        //             value = newMin;
        //         }
        //     }
        //     if (this.type == "number" && this.max) {
        //         if (value > this.max) {
        //             value = this.max;
        //         }
        //     }
        //     this.value = value;
        // },
        animationTrigger: {
            deep: true,
            handler(value) {
                this.animateInput = true;
                setTimeout(() => {
                    this.animateInput = false;
                }, 1000);
            }
        },
        modelValue(value) {
            this.value = value;
            this.showError = false;
        },
        value(value) {
            this.$emit("update:modelValue", value);
        },
        unit(value) {
            this.unitValue = value;
        },
        unit_position(value) {
            this.unitPositionValue = value;
        },
        id(value) {
            this.inputId = value;
        },
        error(value) {
            this.showError = true;
            // if(value) {
            //     this.showError = true;
            // }
        },
        minMaxError(value) {
            if(value) {
                this.showError = true;
            }
        },
        focusOnMount(value) {
            if(value) {
                this.$refs.inputElement.focus();
            }
        }
    },
    emits: ["update:modelValue", 'blur', 'inputKeyup', 'keypress-enter'],
};
</script>
<style>
.toggle-password {
    position: absolute;
    top: 12px;
    right: 12px;
    display: flex;
    z-index: 5;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

.animate-input--trigger {
    border-radius: 6px;
    animation: animateInput 2s ease-in-out 0s 1 alternate-reverse forwards;
}
@keyframes animateInput {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    }
    100% {
        box-shadow: 0 0 20px 0px rgba(248, 237, 85, 0.65);
    }
}
</style>
