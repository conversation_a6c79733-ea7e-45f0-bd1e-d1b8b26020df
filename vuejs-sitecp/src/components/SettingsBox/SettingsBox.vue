<template>
    <div>
        <template v-for="(box, boxIndex) in dataBoxes">
            <Vue3SlideUpDown v-model="box.isVisible" :duration="duration">
                <b-container
                    class="mt-3 container-medium"
                    :class="settingsClasses"
                >
                    <SettingsHeader
                        v-if="box?.header"
                        :icon="box?.header?.icon || 'far fa-cog'"
                        :title="box?.header?.title"
                        :description="box?.header?.description"
                        :translations="translations"
                    />
                    <hr
                        v-if="box.hr !== 'hide' && !box?.header"
                        style="margin: 24px 0"
                    />
                    <b-row>
                        <SettingDescription
                            v-if="!box.hideHelp"
                            v-model="dataBoxes[boxIndex]"
                            :box-key="boxKey"
                        />
                        <b-col
                            :class="
                                box.settingClass
                                    ? box.settingClass
                                    : 'col-12 col-md-7'
                            "
                            v-if="box.editMethod === 'inline'"
                        >
                            <b-card
                                :style="box.cardStyle ? box.cardStyle : ''"
                                :class="box.cardClass"
                                :bodyClass="box.cardBodyClass"
                            >
                                <h5
                                    class="label-500 mb-3"
                                    v-if="!box.hideTitle && box.title"
                                    v-html="box.title"
                                ></h5>
                                    <div class="d-flex flex-column gap-3">
                                        <template v-for="(field, index) in box.fields">
                                                <component
                                                    v-if="field.type !== 'slot'"
                                                    :is="`${field.type}Input`"
                                                    v-model:field="box.fields[index]"
                                                    v-model:settings="data"
                                                    :data="selects"
                                                    :responseErrors="responseErrors"
                                                    :box-key="boxKey"
                                                    :app="app"
                                                    :translations="translations"
                                                    :innerBoxErrors="innerBoxErrors"
                                                    v-model:box="dataBoxes[boxIndex]"
                                                ></component>
                                            <slot v-else :name="field.slotName" :field="field"></slot>
                                        </template>
                                    </div>
                            </b-card>
                        </b-col>
                        <b-col
                            :class="
                                box.settingClass
                                    ? box.settingClass
                                    : 'col-12 col-md-7'
                            "
                            v-else-if="box.editMethod === 'panel'"
                        >
                            <b-card
                                :style="box.cardStyle ? box.cardStyle : ''"
                                :class="box.cardClass"
                            >
                                <b-row>
                                    <b-col
                                        class="col-11"
                                        style="margin-top: 6px"
                                        :class="
                                            box.maxRows &&
                                            box?.fields?.length > box.maxRows
                                                ? 'hide-partial-content'
                                                : 'd-flex flex-column gap-2 gap-sm-3'
                                        "
                                    >
                                        <span
                                            class="label-600 mb-0"
                                            v-if="box.showTitle"
                                            v-html="box.title"
                                        ></span>
                                        <DisplayValues
                                            :data="data"
                                            :box="box"
                                            :translations="translations"
                                            :max-rows="box.maxRows"
                                        />
                                        <span
                                            v-if="box.customValue"
                                            class="value-400 d-block mt-2"
                                            v-html="box.customValue"
                                        ></span>
                                    </b-col>
                                    <b-col class="col-1 px-0">
                                        <a
                                            href="javascript:void(0)"
                                            @click.prevent="openModal(box)"
                                            class="edit-settings-btn-toggle"
                                        >
                                            <i class="fal fa-pen"></i>
                                        </a>
                                    </b-col>
                                </b-row>
                            </b-card>
                        </b-col>

                        <b-col
                            :class="
                                box.settingClass
                                    ? box.settingClass
                                    : 'col-12 col-md-7'
                            "
                            v-if="box.editMethod === 'slide'"
                        >
                            <b-card
                                class="pulse-slide"
                                :style="box.cardStyle ? box.cardStyle : ''"
                                :class="
                                    slide[box.key]
                                        ? box.cardClass + ' pulse-slide-padding'
                                        : box.cardClass
                                "
                            >
                                <Vue3SlideUpDown
                                    :model-value="!slide[box.key]"
                                    :duration="duration"
                                >
                                    <div
                                        class="d-flex align-items-start justify-content-between gap-3"
                                    >
                                        <div
                                            :class="
                                                box.maxRows &&
                                                box?.fields?.length >
                                                    box.maxRows
                                                    ? 'hide-partial-content'
                                                    : 'd-flex flex-column gap-2 gap-sm-3'
                                            "
                                        >
                                            <span
                                                class="label-600 mb-0"
                                                v-if="
                                                    slide[box.key] ||
                                                    box.showTitle
                                                "
                                                v-html="box.title"
                                            ></span>

                                            <DisplayValues
                                                :data="data"
                                                :box="box"
                                                :translations="translations"
                                                :max-rows="box.maxRows"
                                            />
                                        </div>
                                        <a
                                            v-if="!slide[box.key]"
                                            href="javascript:void(0)"
                                            @click.prevent="
                                                openSlide(box, box.key)
                                            "
                                            class="edit-settings-btn-toggle"
                                        >
                                            <i class="fal fa-pen"></i>
                                        </a>
                                    </div>
                                    <span
                                        v-if="
                                            box.customValue && !slide[box.key]
                                        "
                                        class="value-400 d-block mt-2"
                                        v-html="box.customValue"
                                    ></span>
                                </Vue3SlideUpDown>

                                <Vue3SlideUpDown
                                    v-model="slide[box.key]"
                                    :duration="duration"
                                >
                                    <div class="d-flex flex-column gap-3">
                                        <template
                                            v-for="(field, index) in activeBoxSlide[
                                                box.key
                                            ].fields || []"
                                        >
                                            <component
                                                v-if="field.type !== 'slot'"
                                                :is="`${field.type}Input`"
                                                v-model:box="
                                                    activeBoxSlide[box.key]
                                                "
                                                v-model:field="
                                                    activeBoxSlide[box.key].fields[
                                                        index
                                                    ]
                                                "
                                                v-model:settings="
                                                    originalDataSlide[box.key]
                                                "
                                                :data="selects"
                                                :responseErrors="responseErrors"
                                                :translations="translations"
                                                :box-key="boxKey"
                                                :app="app"
                                                :innerBoxErrors="innerBoxErrors"
                                            >
                                            </component>
                                            <slot
                                                v-else
                                                :name="field.slotName"
                                                :field="field"
                                            ></slot>
                                        </template>
                                    </div>
                                </Vue3SlideUpDown>
                                <div
                                    v-if="slide[box.key]"
                                    class="bottom-expand-content d-flex gap-3 align-items-center justify-content-center pt-3"
                                >
                                    <a
                                        href="javascript:void(0)"
                                        class="btn-expand cancel"
                                        v-html="translations['Cancel']"
                                        :class="
                                            slide[box.key]
                                                ? 'slide-btn-close'
                                                : ''
                                        "
                                        @click.prevent="closeSlide(box.key)"
                                    ></a>
                                    <a
                                        v-if="!box.hideActions"
                                        href="javascript:void(0)"
                                        class="btn-expand save"
                                        :class="
                                            slide[box.key]
                                                ? 'slide-settings-btn'
                                                : ''
                                        "
                                        @click.prevent="saveSlide(box, box.key)"
                                    >
                                        <i class="far fa-check"></i>
                                        {{ translations["Confirm"] }}
                                    </a>
                                </div>
                            </b-card>
                        </b-col>
                    </b-row>
                </b-container>
            </Vue3SlideUpDown>
        </template>

        <b-modal
            v-model="modal"
            class="modal-right"
            :size="activeBox?.size || 'lg'"
            :no-footer="true"
            header-class="edit-settings-modal-header"
            body-class="edit-settings-modal-content"
            :no-close-on-backdrop="noCloseOnBackdrop"
            @hidden="closeModal"
        >
            <template #header>
                <div class="d-flex align-items-start gap-2 position-relative">
                    <h5
                        v-html="activeBox?.title"
                        class="settings-modal-title"
                    ></h5>
                </div>
                <div
                    class="d-flex justify-content-end align-items-center gap-2"
                >
                    <TooltipLabel
                        :hide-icon="true"
                        :tooltip-text="
                            translations[
                                'Share the currently open panel with another user'
                            ]
                        "
                    >
                        <a
                            href="javascript:void(0);"
                            @click.prevent="copyUrl"
                            class="link-icon me-1"
                        >
                            <i class="fal fa-link link-icon"></i>
                        </a>
                    </TooltipLabel>
                    <button
                        @click.prevent="closeModal"
                        type="button"
                        class="btn btn-white"
                        v-html="translations['Cancel']"
                    ></button>
                    <button
                        @click.prevent="saveModal"
                        type="button"
                        class="btn btn-primary"
                        v-html="translations['Confirm']"
                    ></button>
                </div>
            </template>
            <b-card class="mt-2 pulse-modal" v-if="activeBox">
                   <div class="d-flex flex-column gap-3">
                        <template v-for="(field, index) in activeBox.fields || []">
                                 <component
                                     v-if="field.type !== 'slot'"
                                     :is="`${field.type}Input`"
                                     v-model:field="activeBox.fields[index]"
                                     v-model:settings="originalData"
                                     :data="selects"
                                     :responseErrors="responseErrors"
                                     :box-key="boxKey"
                                     :translations="translations"
                                     :app="app"
                                     v-model:box="activeBox"
                                     :innerBoxErrors="innerBoxErrors"
                                 >
                                 </component>
                            <slot v-else :name="field.slotName" :field="field"></slot>
                        </template>
                   </div>
            </b-card>
        </b-modal>
    </div>
</template>

<script>
import { Vue3SlideUpDown } from "vue3-slide-up-down";

import SettingDescription from "@components/SettingDescription";
import TooltipLabel from "@components/Apps/Erp/TooltipLabel";

import DisplayValues from "@components/SettingsBox/Helpers/DisplayValues";
import SettingsHeader from "./Helpers/SettingsHeader";

import titleInput from "@components/SettingsBox/Fields/title";
import alertInput from "@components/SettingsBox/Fields/alert";
import htmlInput from "@components/SettingsBox/Fields/html";
import colorInput from "@components/SettingsBox/Fields/color";
import editorInput from "@components/SettingsBox/Fields/editor";
import textInput from "@components/SettingsBox/Fields/text";
import switchInput from "@components/SettingsBox/Fields/switch";
import selectInput from "@components/SettingsBox/Fields/select";
import radioInput from "@components/SettingsBox/Fields/radio";
import stringInput from "@components/SettingsBox/Fields/string";
import passwordInput from "@components/SettingsBox/Fields/password";
import numberInput from "@components/SettingsBox/Fields/number";
import percentInput from "@components/SettingsBox/Fields/percent";
import lineInput from "@components/SettingsBox/Fields/line";
import checkboxInput from "@components/SettingsBox/Fields/checkbox";
import fileInput from "@components/SettingsBox/Fields/file";
import helpBlockInput from "@components/SettingsBox/Fields/helpBlock";
import imageInput from "@components/SettingsBox/Fields/image"

import mixin from "./js/mixin";

export default {
    name: "SettingsBox",
    components: {
        colorInput,
        editorInput,
        htmlInput,
        textInput,
        fileInput,
        switchInput,
        selectInput,
        stringInput,
        passwordInput,
        numberInput,
        percentInput,
        lineInput,
        radioInput,
        checkboxInput,
        titleInput,
        alertInput,
        helpBlockInput,
        imageInput,

        TooltipLabel,
        Vue3SlideUpDown,
        SettingDescription,
        DisplayValues,
        SettingsHeader,
    },
    mixins: [mixin],
};
</script>
<style lang="scss">
.partly-hidden-content {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    align-self: stretch;
    font-weight: 400;
    overflow: hidden;
    opacity: var(--space-size-1, 1);
    background: linear-gradient(
        180deg,
        #7a7d84 35%,
        rgba(122, 125, 132, 0) 100%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    max-height: 200px;
    transition: all 200ms ease-in-out;
}
.partly-hidden-content:hover {
    -webkit-text-fill-color: unset;
    max-height: none;
}
</style>
