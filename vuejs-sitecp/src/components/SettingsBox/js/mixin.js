import dotGet from "@js/dotGet";
import _ from 'lodash';

import { toast } from "@js/toast";

export default {
    props: {
        boxes: {
            type: Array,
            default: [],
        },
        settings: {
            type: Object,
            required: true,
            default: {},
        },
        selects: {
            type: Object,
            required: false,
            default: {},
        },
        boxKey: {
            type: String,
            required: true,
        },
        calculatorWidth: {
            type: Boolean,
            required: false,
            default: false,
        },
        duration: {
            type: Number,
            // default: 200,
            default: 200,
        },
        responseErrors: {
            required: false,
            default: {},
            type: Object,
        },
        settingsClasses: {
            required: false,
            type: String,
            default: null,
        },
        app: {
            required: false,
            default: {},
            type: Object,
        },
        liveWatch: {
            required: false,
            default: [],
            type: Array,
        },
        settingOpen: {
            required: false,
            type: Boolean,
            default: false,
        },
        translationLabels: {
            type: Object,
            required: false,
            default: {},
        },
    },
    data() {
        return {
            dataBoxes: this.boxes.map((box) => {
                if (box.lockEditMethod) {
                    return box;
                }
                box.isVisible = this.hasVisibleFields(box);
                return box;
            }),
            data: this.settings,
            modal: false,
            slide: {},
            activeBox: null,

            activeBoxSlide: {},
            originalDataSlide: {},

            originalData: null,
            originalBoxes: null,

            innerBoxErrors: {},

            translations: {
                ...this.translationLabels,
                Save: this.$t("Save"),
                Confirm: this.$t("Confirm"),
                Cancel: this.$t("Cancel"),
                "{count} {option} selected": this.$t("{count} {option} selected"),
                options: this.$t("options"),
                option: this.$t("option"),
                "Invalid credentials": this.$t("Invalid credentials"),
                "The URL was copied and ready to share": this.$t(
                    "The URL was copied and ready to share"
                ),
                "Share the currently open panel with another user": this.$t(
                    "Share the currently open panel with another user"
                ),
                "This field is required when <b>{field}</b> is enabled!": this.$t(
                    "This field is required when <b>{field}</b> is enabled!"
                ),
                "This field is required!": this.$t("This field is required!"),
            },
        };
    },
    computed: {
        noCloseOnBackdrop() {
            if (this.activeBox) {
                return !_.isEqual(this.originalData, this.settings);
            }
            return false;
        },
    },
    methods: {
        // handleOpenCloseAnimation(box, setHeight) {
        //     let settingBox = document.querySelector(`.${box}`)
        //     if (settingBox) {
        //         let height = settingBox.clientHeight
        //         if (setHeight) {
        //             settingBox.style.height = `${height}px !important`
        //             settingBox.style.overflow = 'hidden'
        //         } else {
        //             settingBox.style.height = 'fit-content'
        //             settingBox.style.overflow = 'visible'
        //         }

        //     }
        // },
        copyUrl() {
            navigator.clipboard
                .writeText(`${this.serverSettings("host")}${this.$route.fullPath}`)
                .then(() => {
                    toast.success(
                        this.translations["The URL was copied and ready to share"]
                    );
                });
        },
        openSlide(box, key) {
            this.slide[key] = true;
            this.activeBoxSlide[key] ??= {};
            this.activeBoxSlide[key] = _.clone(box);

            this.originalDataSlide[key] ??= {};
            this.originalDataSlide[key] = _.clone(this.settings);

            this.originalBoxes = _.clone(this.dataBoxes);
        },
        saveSlide(box, key) {
            if (!this.checkRequiredAndDependFieldsSlide(box, key)) {
                this.slide[key] = false;
                this.saveSlideNoClose(box, key);

                this.activeBoxSlide[key] = null;
            }
        },
        saveSlideNoClose(box, key) {
            this.originalBoxes = _.clone(this.dataBoxes);

            let filteredOthersOriginalDataSlide = Object.keys(
                this.originalDataSlide
            ).filter((x) => x !== key);

            filteredOthersOriginalDataSlide.forEach((x) => {
                let valueKeys = box.fields
                    .filter((j) => !["line", "alert", "helpBlock"].includes(j.type))
                    .map((y) => y.key);

                valueKeys.forEach((z) => {
                    let value = dotGet(this.originalDataSlide[key]).get(z, "");
                    let keys = z.split(".");
                    let lastKey = keys.pop();
                    let nestedObject = dotGet(this.originalDataSlide[x]).get(keys, {});
                    this.originalDataSlide[x] = {
                        ...this.originalDataSlide[x],
                        [keys.join(".")]: { ...nestedObject, [lastKey]: value },
                    };
                });
            });

            this.data = _.clone(this.originalDataSlide[key]);
        },
        closeSlide(key) {
            this.slide[key] = false;
            this.innerBoxErrors = {};
            this.activeBoxSlide[key] = null;

            this.dataBoxes = _.clone(this.originalBoxes);
            this.$emit("update:boxes", this.dataBoxes);
        },
        openModal(box, animation, error) {
            this.$router.push({
                hash: `#${box.group}~${box.key}`,
            });

            this.activeBox = _.clone(box);
            this.modal = true;

            if (animation) {
                setTimeout(() => {
                    document.querySelectorAll(".pulse-modal").forEach((item) => {
                        item.classList.add(`pulse-animation${error ? "-error" : ""}`);
                    });
                }, 850);
                setTimeout(() => {
                    document.querySelectorAll(".pulse-modal").forEach((item) => {
                        item.classList.remove(`pulse-animation${error ? "-error" : ""}`);
                    });
                }, 7000);
            }
            this.originalData = _.clone(this.settings);

            this.originalBoxes = _.clone(this.dataBoxes);
        },
        closeModal() {
            this.$router.push({
                hash: "",
            });
            this.innerBoxErrors = {};
            this.activeBox = null;
            this.modal = false;
            this.dataBoxes = _.clone(this.originalBoxes);
            this.$emit("update:boxes", this.dataBoxes);
        },
        saveModal() {
            if (!this.checkRequiredAndDependFieldsModal()) {
                this.originalBoxes = _.clone(this.dataBoxes);

                this.data = _.clone(this.originalData);

                this.$router.push({
                    hash: "",
                });

                this.activeBox = null;
                this.modal = false;
            }
        },
        checkRequiredAndDependFieldsSlide(box, key) {
            let innerError = false;
            this.innerBoxErrors = {};
            let dependedFields = box.fields.filter(
                (x) =>
                    x.hasOwnProperty("dependField") &&
                    Array.isArray(x.dependValue) &&
                    x.dependValue.length !== 0
            );

            if (dependedFields.length !== 0) {
                dependedFields.forEach((field) => {
                    let dependFieldValue = dotGet(this.originalDataSlide[key]).get(
                        field.dependField,
                        ""
                    );

                    let fieldValue = dotGet(this.originalDataSlide[key]).get(
                        field.key,
                        ""
                    );

                    if (
                        this.$fieldsCompareValues(field.dependValue, dependFieldValue) &&
                        !fieldValue &&
                        field.dependRequired
                    ) {
                        field.required = true;
                        this.innerBoxErrors[field.key] = this.$trp(
                            this.translations[
                            "This field is required when <b>{field}</b> is enabled!"
                            ],
                            {
                                field: box.fields.find((x) => x.key === field.dependField)
                                    .label,
                            }
                        );
                        innerError = true;
                    } else {
                        delete field.required;
                    }
                });
            }

            let requiredFields = box.fields.filter((x) => x.required);

            if (requiredFields.length !== 0) {
                requiredFields.forEach((field) => {
                    let requiredValue = dotGet(this.originalDataSlide[key]).get(
                        field.key,
                        ""
                    );
                    if (typeof requiredValue !== 'number' && !requiredValue) {
                        this.innerBoxErrors[field.key] = this.translations[
                            "This field is required!"
                        ];
                        innerError = true;
                    }
                });
            }
            return innerError;
        },
        checkRequiredAndDependFieldsModal() {
            let innerError = false;
            this.innerBoxErrors = {};
            let dependedFields = this.activeBox.fields.filter(
                (x) =>
                    x.hasOwnProperty("dependField") &&
                    Array.isArray(x.dependValue) &&
                    x.dependValue.length !== 0
            );

            if (dependedFields.length !== 0) {
                dependedFields.forEach((field) => {
                    let dependFieldValue = dotGet(this.originalData).get(
                        field.dependField,
                        ""
                    );

                    let fieldValue = dotGet(this.originalData).get(field.key, "");

                    if (
                        this.$fieldsCompareValues(field.dependValue, dependFieldValue) &&
                        !fieldValue &&
                        field.dependRequired
                    ) {
                        field.required = true;
                        this.innerBoxErrors[field.key] = this.$trp(
                            this.translations[
                            "This field is required when <b>{field}</b> is enabled!"
                            ],
                            {
                                field: this.activeBox.fields.find(
                                    (x) => x.key === field.dependField
                                ).label,
                            }
                        );
                        innerError = true;
                    } else {
                        delete field.required;
                    }
                });
            }

            let requiredFields = this.activeBox.fields.filter((x) => x.required);

            if (requiredFields.length !== 0) {
                requiredFields.forEach((field) => {
                    let requiredValue = dotGet(this.originalData).get(field.key, "");
                    if (typeof requiredValue !== 'number' && !requiredValue) {
                        this.innerBoxErrors[field.key] = this.translations[
                            "This field is required!"
                        ];
                        innerError = true;
                    }
                });
            }
            return innerError;
        },
        checkConfigured() {
            if (Object.keys(this.app).length !== 0) {
                if (this.app.is_configured === false) {
                    if (this.dataBoxes[0]) {
                        let displayMethod = this.dataBoxes[0].editMethod;

                        if (displayMethod === "slide") {
                            this.openSlide(this.dataBoxes[0], this.dataBoxes[0].key);
                            setTimeout(() => {
                                document
                                    .querySelectorAll(".pulse-slide")
                                    .forEach((item) => {
                                        item.classList.add("pulse-animation-slide");
                                    });
                            }, 600);
                            setTimeout(() => {
                                document
                                    .querySelectorAll(".pulse-slide")
                                    .forEach((item) => {
                                        item.classList.remove("pulse-animation-slide");
                                    });
                            }, 7000);
                        } else if (displayMethod === "panel") {
                            this.openModal(this.dataBoxes[0], true);
                        }
                    }
                }
            }
        },
        checkBoxUrlData() {
            let hash = this.$route.hash;
            if (hash) {
                let [group, key] = hash.replace("#", "").split("~");
                this.dataBoxes.forEach((box) => {
                    if (box.group === group && box.key === key) {
                        this.openModal(box, true);
                    }
                });
            }
        },
        hasVisibleFields(box) {
            if (!box?.fields?.length) {
                return false;
            }

            return (
                box.fields.filter((field) => {
                    return this.isFieldVisible(field);
                }).length > 0
            );
        },
        isFieldVisible(field) {
            return this.$fieldIsVisible(field, this.settings);
        },
        filterLines(props) {
            return props && Array.isArray(props)
                ? props.filter((x) => !["line", "alert", "helpBlock"].includes(x.type))
                : [];
        },
        addTranslationsFromBoxes() {
            Object.entries(this.boxes).forEach(([key, box]) => {
                this.filterLines(box.fields || []).forEach((field) => {
                    if (
                        field.type === "select" &&
                        !field.optionsFromSettings &&
                        Array.isArray(field.options)
                    ) {
                        if (!field.disableTranslatableOptions) {
                            field.options.forEach((option) => {
                                this.translations[option.name] = this.$t(option.name);
                            });
                        }
                    }
                    if (
                        field.type === "radio" &&
                        Array.isArray(field.options)
                    ) {
                        if (!field.disableTranslatableOptions) {
                            field.options.forEach((option) => {
                                this.translations[option.text] = this.$t(option.text);
                            });
                        }
                    }
                    if (field.placeholder && !field.disableTranslatablePlaceholder) {
                        this.translations[field.placeholder] = this.$t(field.placeholder);
                    }

                    if (!field.disableTranslatable) {
                        this.translations[field?.label] = field?.label
                            ? this.$t(field.label)
                            : "";
                    }
                    if (field.required && field.label) {
                        this.translations[field.label + " is required"] = this.$t(
                            field.label + " is required"
                        );
                    }
                    if (field.errorMessages && field.errorMessages.length > 0) {
                        field.errorMessages.map((message) => {
                            this.translations[message] = this.$t(message);
                        });
                    }
                    if (field.help?.label && !field.help?.disableTranslatable) {
                        this.translations[field.help.label] = this.$t(
                            field.help.label,
                            field.help.parameters
                        );
                    }
                    if (field.helpBox?.label) {
                        this.translations[field.helpBox.label] = this.$t(
                            field.helpBox.label,
                            field.helpBox.parameters
                        );
                        if (field.helpBox?.plan_feature?.title) {
                            this.translations[field.helpBox.plan_feature.title] = this.$t(
                                field.helpBox.plan_feature.title,
                                {
                                    name: "{name}",
                                    key: "{key}",
                                }
                            );
                        }
                    }

                    if(field.type === 'title'){
                        this.translations[field.label] = this.$t(field.label);
                    }
                });
            });

            this.$populateTranslations(this);
        },
        htmlWatchFields(box) {
            if (!this.activeBoxSlide[box.key]) {
                return;
            }

            (box.fields || []).forEach((field, index) => {
                if (
                    field.type !== "html" ||
                    !field.props ||
                    !this.activeBoxSlide[box.key].fields ||
                    !this.activeBoxSlide[box.key].fields[index]
                ) {
                    return;
                }

                this.activeBoxSlide[box.key].fields[index].props = field.props;
            });
        },
    },
    created() {
        this.helpBoxes(this.dataBoxes);
    },
    mounted() {
        this.originalBoxes = _.clone(this.boxes);
        this.originalData = _.clone(this.settings);

        this.addTranslationsFromBoxes();

        this.checkBoxUrlData();
        // this.checkConfigured();
        this.liveWatch.forEach((property) => {
            this.$watch(`originalDataSlide.${property}`, () => {
                this.boxes.map((box) => {
                    if (property.indexOf(box.key) === 0) {
                        this.saveSlideNoClose(box, box.key);
                    }
                });
            });
        });
        setTimeout(()=>{
            this.helpBoxes(this.dataBoxes);
        },350)
    },
    watch: {
        slide: {
            deep: true,
            handler(val) {
                this.$emit(
                    "update:settingOpen",
                    Object.values(val).some((x) => x === true)
                );
            },
        },
        modal(value) {
            if (value) {
                // document.querySelector(".scroll-lock").style.overflowY = "hidden";
            } else {
                // document.querySelector(".scroll-lock").style.overflowY = "visible";
            }
            this.$emit("update:settingOpen", value);
        },
        responseErrors: {
            deep: true,
            handler(newVal) {
                if (Object.keys(newVal).length > 0) {
                    this.dataBoxes.forEach((box) => {
                        box?.fields?.forEach((field) => {
                            Object.keys(newVal).forEach((x) => {
                                if (x.includes("[")) {
                                    x = x.replace("[", ".").replace("]", "");
                                }
                                if (
                                    x.split(".").includes(field.key) ||
                                    x === field.key ||
                                    (field.errorKey && field.errorKey === x)
                                ) {
                                    if (box.editMethod === "slide") {
                                        this.openSlide(box, box.key);
                                    } else if (box.editMethod === "panel") {
                                        this.openModal(box, true, true);
                                    }
                                }
                            });
                        });
                    });
                }
            },
        },
        data: {
            deep: true,
            handler(value) {
                this.$emit("update:settings", value);
            },
        },
        boxes: {
            deep: true,
            handler(value) {
                this.dataBoxes = value.map((box) => {
                    this.htmlWatchFields(box);
                    if (box.lockEditMethod) {
                        return box;
                    }
                    box.isVisible = this.hasVisibleFields(box);
                    return box;
                });
            },
        },
        settings: {
            deep: true,
            handler(value) {
                this.data = value;

                this.dataBoxes = this.dataBoxes.map((box) => {
                    if (box.lockEditMethod) {
                        return box;
                    }

                    box.isVisible = this.hasVisibleFields(box);
                    return box;
                });
                this.$emit("update:boxes", this.dataBoxes);
            },
        },
        translationLabels: {
            deep: true,
            immediate: true,
            handler(value) {
                this.translations = {
                    ...this.translations,
                    ...value,
                };
            },
        }
    },
    emits: ["update:settingOpen", "update:boxes", "update:settings"],
};
