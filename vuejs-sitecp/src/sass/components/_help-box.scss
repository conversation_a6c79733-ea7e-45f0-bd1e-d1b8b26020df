.info-box {
  border-radius: 0px 8px 8px 0px;
  border-left: 4px solid var(--color-blue-400, #5FCCEE);
  background-color: var(--color-blue-100, #EBFAFF);
  color: var(--color-text-body-cc-color-text-secondary, #7A7D84);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 19px;
  padding: var(--spacing-cc-space-xs, 8px) var(--spacing-cc-space-md, 16px);
  transition: .3s;

  &.no-border {
    border-radius: 8px !important;
    border: none !important;
    padding: var(--spacing-cc-space-md, 16px);
  }

  &.info-box-warning {
    color: #A68C4B;
    border-radius: 0px 8px 8px 0px;
    border-left: 4px solid var(--color-yellow-400, #FCC400);
    background-color: var(--color-surface-status-caution-cc-color-bg-status-caution-subdued, #FFF7D9);

  }

  &.info-box-green {
    color: var(--Color-Text-Body---cc-color-text-primary, #3D414D);
    border-radius: 0px 8px 8px 0px;
    border-left: 4px solid #25CF8B;
    // border-left: 4px solid var(--color-green-400, #25CF8B);
    background-color: #E7FCF4;
    // background-color: var(--Color-Surface-Status-Success---cc-color-bg-status-success-light, #E7FCF4);
  }

  &.info-box-error {
    color: #d83f3f;
    background: #ffcdce;
    border: 1px solid #ffb8b8;
  }

  .fa-info-square {
    color: var(--color-blue-400, #5FCCEE);
    font-size: 16px;
    font-weight: 400;
    line-height: 16px;
  }

  .info {
    color: var(--Color-Border-Status---cc-color-border-status-info, #5FCCEE);
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}
