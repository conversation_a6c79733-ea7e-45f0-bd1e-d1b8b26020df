import {ref} from 'vue';
import axios from 'axios';

export function useLanguageVersions() {
    const languageVersions = ref([]);

    const getLanguageVersions = async (app) => {
        try {
            if (app?.is_installed) {
                const versions = await axios.get('/admin/api/multilang/versions');
                languageVersions.value = versions.data;
            }
        } catch (error) {
            console.error('Error fetching language versions:', error);
        }
    };

    return {
        languageVersions,
        getLanguageVersions,
    };
}
